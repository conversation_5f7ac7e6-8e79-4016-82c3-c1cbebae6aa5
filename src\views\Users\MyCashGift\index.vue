<template>
  <div class="my-cashgift">
    <nav-bar-2
      :title="'我的礼金'"
      v-if="navBgTransparent"
      :azShow="true"
      bgStyle="transparent-white"
      class="nav"
    >
      <template #right>
        <div @click="toPage('CashGiftExplain')" class="text btn">礼金说明</div>
      </template>
    </nav-bar-2>
    <nav-bar-2
      :title="'我的礼金'"
      :border="true"
      :azShow="true"
      v-else
      class="nav"
    >
      <template #right>
        <div @click="toPage('CashGiftExplain')" class="text black btn">
          礼金说明
        </div>
      </template>
    </nav-bar-2>
    <section class="section1">
      <div class="text">我的礼金</div>
      <div class="big-text">{{ myLijinInfo.balance }}</div>
      <div class="small-text">
        {{ myLijinInfo.expiring_date }}到期：<span>{{
          myLijinInfo.expiring_soon
        }}</span>
      </div>
    </section>
    <section class="section2">
      <div class="text">
        1:1兑换金币<span
          >每日最高可兑换1000金币<template
            v-if="myLijinInfo.expiring_quota < 1000"
            >(剩余{{ myLijinInfo.expiring_quota }})</template
          ></span
        >
      </div>
      <div class="bottom">
        <!-- <input
          v-model="gold_count"
          type="number"
          placeholder="请输入兑换金币数"
          class="left"
        /> -->
        <van-field
          v-model="gold_count"
          :formatter="formatter"
          placeholder="请输入兑换金币数"
          type="number"
          class="left"
        />
        <div
          :class="{ empty: gold_count === '' }"
          @click="gold_count !== '' ? (exchange_popup = true) : ''"
          class="right btn"
        >
          兑换
        </div>
      </div>
    </section>
    <section class="section3">
      <van-tabs v-model="tab_current" @click="onClickTab">
        <van-tab title="礼金获取记录"></van-tab>
        <van-tab title="礼金兑换记录"></van-tab>
      </van-tabs>
      <div class="tab-content">
        <load-more
          v-model="loading"
          :finished="finished"
          @loadMore="loadMore"
          :check="false"
          v-if="!empty"
        >
          <div class="list">
            <div class="item" v-for="(item, index) in log_list" :key="index">
              <div class="left">
                <div class="big-text">{{ item.title }}</div>
                <div class="small-text red" v-if="item.expiring">
                  有效期至{{ item.expiring | formatTime }}
                </div>
                <div class="small-text" v-else>
                  {{ item.update_time | formatTime }}
                </div>
              </div>
              <div class="right">
                <div class="big-text">
                  {{ item.type == 1 ? '+' : '-' }}{{ item.quantity }}
                </div>
                <div class="small-text" v-if="item.expiring">
                  {{ item.update_time | formatTime }}
                </div>
              </div>
            </div>
          </div>
        </load-more>
        <div v-else class="empty">
          <div class="bg"></div>
          <div class="text">没有任何记录哦~</div>
        </div>
      </div>
    </section>
    <van-popup v-model="exchange_popup" class="exchange-popup">
      <div class="title">兑换金币</div>
      <div class="text">
        你确认消耗<span>{{ gold_count }}</span
        >礼金兑换<span>{{ gold_count }}</span
        >金币？
      </div>
      <!-- <div v-if="!userInfo.is_svip" class="text2">
        开通SVIP可额外再获得 9999 金币哦~
      </div> -->
      <!-- <div v-if="!userInfo.is_svip" class="button-container">
        <div class="left button btn" @click="handleExchange">立即兑换</div>
        <div @click="toSvip" class="right button btn">开通SVIP</div>
      </div> -->
      <div class="button-container">
        <div @click="exchange_popup = false" class="left button btn">
          取消
        </div>
        <div class="right button btn" @click="handleExchange">立即兑换</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { BOX_openInNewNavWindow } from '@/utils/box.uni.js';
import {
  ApiUserWodelijin,
  ApiUserGetLiJinLog,
  ApiUserExpiringLiJin,
} from '@/api/views/users.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  data() {
    return {
      myLijinInfo: {}, // 我的礼金信息
      gold_count: '', //金币数
      tab_current: 0, //当前tab
      log_list: [], //获取记录
      exchange_popup: false, //兑换弹窗
      finished: false, //是否请求完，用于加载页面
      loading: false,
      navBgTransparent: true,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },

  filters: {
    formatTime(val) {
      let { year, month, day } = handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
  },
  async created() {
    await this.init();
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 输入框格式化
    formatter(value) {
      let result = Math.min(
        Number(value),
        Number(this.myLijinInfo?.balance ?? 0),
        Number(this.myLijinInfo?.expiring_quota ?? 0),
      );
      return result || '';
    },
    async init() {
      await this.getMyLijin();
      await this.getLijinLog();
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 去svip页面
    toSvip() {
      BOX_openInNewNavWindow({ name: 'Svip' }, { url: this.vipUrl });
    },
    // 获取我的礼金
    async getMyLijin() {
      const res = await ApiUserWodelijin();
      this.myLijinInfo = res.data.info;
    },
    // 礼金兑换金币
    async handleExchange() {
      try {
        const res = await ApiUserExpiringLiJin({ num: this.gold_count });
        if (res.code == 1) {
          this.$toast(res.msg);
        }
        await this.getMyLijin();
        await this.getLijinLog(2);
        this.exchange_popup = false;
        this.gold_count = '';
      } catch (e) {}
    },

    // 礼金日志
    async getLijinLog(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      // type: 0 => 兑换 1 => 增加
      let type = this.tab_current == 0 ? 1 : 0;
      this.loading = true;
      const res = await ApiUserGetLiJinLog({
        type,
        page: this.page,
        listRows: this.listRows,
      });
      const { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.log_list = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.log_list.push(...list);
      this.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getLijinLog(2);
    },
    // 点击tab
    async onClickTab() {
      this.page = 1;
      this.log_list = [];
      this.finished = false;
      this.empty = false;
      await this.getLijinLog();
    },
  },
};
</script>
<style lang="less" scoped>
.my-cashgift {
  min-height: 100vh;
  .image-bg('~@/assets/images/cash-gift/cashgift_bg1.png');
  padding-bottom: 20 * @rem;
  background-color: #fff0e9;
  .nav {
    .text {
      color: #fff;
    }
    .black {
      color: #000;
    }
  }
  .section1 {
    width: 339 * @rem;
    height: 110 * @rem;
    box-sizing: border-box;
    padding: 15 * @rem 20 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .image-bg('~@/assets/images/cash-gift/cashgift_bg2.png');
    margin: 38 * @rem auto 12 * @rem;
    .text {
      font-size: 14 * @rem;
      font-weight: 600;
      color: #7b3e46;
    }
    .big-text {
      font-size: 24 * @rem;
      font-family: Inter-Bold, Inter;
      font-weight: bold;
      color: #ff5040;
    }
    .small-text {
      font-size: 12 * @rem;
      font-weight: 600;
      color: #b8928b;
      span {
        color: #ff5040;
      }
    }
  }
  .section2 {
    width: 339 * @rem;
    height: 113 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    margin: 0 auto 12 * @rem;
    padding: 18 * @rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12 * @rem;
    .text {
      font-size: 14 * @rem;
      font-weight: 600;
      color: #7b3e46;
      span {
        margin-left: 5 * @rem;
        font-size: 12 * @rem;
        color: #ff5040;
      }
    }
    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        width: 200 * @rem;
        height: 40 * @rem;
        box-sizing: border-box;
        padding: 0 10 * @rem;
        line-height: 40 * @rem;
        border-radius: 8 * @rem;
        border: 1 * @rem solid #f8c7be;
        color: #b8928b;
        background-color: rgba(255, 255, 255, 0);
      }
      ::-webkit-input-placeholder {
        color: #b8928b;
      }
      .right {
        width: 80 * @rem;
        height: 32 * @rem;
        background: #ff7979;
        border-radius: 31 * @rem;
        color: #fff;
        text-align: center;
        line-height: 32 * @rem;
        font-size: 14 * @rem;
        &.empty {
          background: #dccbcb;
        }
      }
    }
  }
  .section3 {
    width: 339 * @rem;
    min-height: 395 * @rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12 * @rem;
    margin: 0 auto;
    overflow: hidden;
    /deep/ .van-tabs__nav {
      background-color: rgba(255, 255, 255, 0);
    }
    /deep/ .van-tabs__line {
      background: #ff7979;
      width: 20 * @rem;
      height: 5 * @rem;
      border-radius: 31 * @rem;
    }
    /deep/ .van-tab__text--ellipsis {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #cab0b3;
    }
    /deep/ .van-tab--active {
      .van-tab__text--ellipsis {
        color: #7b3e46;
      }
    }
    .tab-content {
      .list {
        overflow: hidden;
        margin-top: 10 * @rem;
        .item {
          display: flex;
          justify-content: space-between;
          margin-top: 20 * @rem;
          padding: 0 20 * @rem;
          .left {
            .big-text {
              font-size: 14 * @rem;
              color: #7b3e46;
            }
            .small-text {
              margin-top: 7 * @rem;
              font-size: 12 * @rem;
              color: #b8928b;
            }
            .red {
              color: #ff5040;
            }
          }
          .right {
            text-align: right;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .big-text {
              font-size: 14 * @rem;
              font-weight: 600;
              color: #a6676f;
            }
            .small-text {
              margin-top: 7 * @rem;
              font-size: 12 * @rem;
              color: #b8928b;
            }
          }
        }
      }
      .empty {
        display: flex;
        min-height: 300 * @rem;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .bg {
          width: 77 * @rem;
          height: 85 * @rem;
          .image-bg('~@/assets/images/cash-gift/cashgift_bg3.png');
        }
        .text {
          margin-top: 17 * @rem;
          font-size: 14 * @rem;
          color: #7b3e46;
        }
      }
    }
  }
  .exchange-popup {
    width: 300 * @rem;
    height: 182 * @rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 15 * @rem;
    padding: 20 * @rem;
    .title {
      font-size: 16 * @rem;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      text-align: center;
    }
    .text {
      text-align: center;
      font-size: 14 * @rem;
      line-height: 24 * @rem;
      color: #777777;
      /deep/ span {
        margin: 0 2 * @rem;
        color: #ff5040;
      }
    }
    .text2 {
      text-align: center;
      font-size: 14 * @rem;
      color: #ff5040;
    }
    .button-container {
      display: flex;
      justify-content: center;
      .button {
        width: 110 * @rem;
        height: 35 * @rem;
        border-radius: 18 * @rem;
        text-align: center;
        line-height: 35 * @rem;
        font-size: 13 * @rem;
      }
      .left {
        background: #f2f2f2;
        color: #7d7d7d;
      }
      .right {
        margin-left: 20 * @rem;
        background: @themeColor;
        color: #ffffff;
      }
    }
  }
}
</style>
