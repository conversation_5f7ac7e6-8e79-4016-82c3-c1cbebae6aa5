<template>
  <div class="turn-table-page page">
    <nav-bar-2 bgStyle="transparent-white" :placeholder="false">
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <!-- 活动规则 -->
    <div class="rule-box" @click="ruleDialogShow = true"></div>
    <div class="main">
      <div class="table-box">
        <div class="table-3">
          <!-- 幸运用户轮播 -->
          <div class="lucky-user-info">
            <div class="lucky-container">
              <div class="notice-icon"></div>
              <div class="su"></div>
              <van-swipe
                class="lucky-swipe"
                vertical
                :autoplay="2000"
                :show-indicators="false"
                :touchable="false"
              >
                <van-swipe-item v-for="(item, index) in maxReward" :key="index">
                  <div class="lucky-text">
                    <span>恭喜"{{ item.nickname }}"抽中</span>{{ item.num }}
                  </div>
                </van-swipe-item>
              </van-swipe>
            </div>
          </div>
          <!-- 转盘 -->
          <div class="table-container">
            <div class="table-content">
              <div class="table-list">
                <div
                  class="table-item"
                  v-for="(item, index) in dialList"
                  :key="index"
                  :class="{
                    active: current == index + 1,
                  }"
                >
                  <img class="reward-icon" :src="item.goods_icon_url" alt="" />
                  <div class="reward-text">{{ item.goods_name }}</div>
                </div>
                <div class="table-item turn-btn-1 btn" @click="handleRaffle(1)">
                  <div class="turn-btn-1-content">
                    <div
                      class="turn-btn-1-tip"
                      :class="{ free: Number(freeNum) }"
                    >
                      {{
                        Number(freeNum)
                          ? `${$t('免费抽')}${freeNum}${$t('次')}`
                          : `${oneGold}${$t('金币')}`
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 金币 -->
          <div class="current-gold">
            <div class="coins">
              {{ $t('我的金币') }}：<span>{{
                userInfo.token ? userInfo.gold + $t('个') : $t('请登录后查看')
              }}</span>
            </div>
          </div>
          <div class="turn-btn-reward">
            <div
              class="login"
              v-if="!userInfo.nickname"
              @click="toPage('PhoneLogin')"
            >
              <div class="login-text">
                {{ $t('登录') }}
              </div>
            </div>
            <div class="my-reward" v-else @click="myRewardDialogShow = true">
              <div class="reward-text">
                {{ $t('我的奖品') }}
              </div>
            </div>
          </div>
          <div class="turn-btn-box btn" @click="handleRaffle(2)">
            <div class="turn-btn-10">
              <span class="btn-tip"> {{ tenGold }}{{ $t('金币') }} </span>
              <span class="btn-text">10连抽</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 祝福值 -->
      <div class="wish-container">
        <div class="left">
          <div class="wish-top">
            <div class="current-wish">
              {{ $t('当前祝福值') }}：{{ allNum }}
            </div>
            <div
              class="wish-question"
              @click="wishIntroductionDialogShow = true"
            ></div>
          </div>
          <div class="wish-progress">
            <div
              class="current-progress"
              :style="{ width: `${currentProgress * 100}%` }"
            ></div>
          </div>
          <div class="wish-introduction">{{ boxContent }}</div>
        </div>
        <div class="right">
          <div class="wish-icon"></div>
          <div
            class="wish-btn default"
            v-if="boxStatus == 0"
            @click="handleGetWish"
          >
            {{ $t('未达成') }}
          </div>
          <div
            class="wish-btn on btn"
            v-else-if="boxStatus == 1"
            @click="handleGetWish"
          >
            {{ $t('领取') }}
          </div>
          <div
            class="wish-btn"
            v-else-if="boxStatus == 2"
            @click="handleGetWish"
          >
            {{ $t('已领取') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 活动规则 -->
    <van-dialog
      v-model="ruleDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="rule-content-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="wish-content" v-html="rules"></div>
        <div class="dialog-close-btn" @click="ruleDialogShow = false"> </div>
      </div>
    </van-dialog>

    <!-- 我的奖品 -->
    <van-dialog
      v-model="myRewardDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="reward-content-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="no-reward" v-if="!myWonList.length" @click="toLogin">
          <div
            class="no-reward-icon"
            v-if="!myWonList.length && userInfo.token"
          >
            <img
              src="@/assets/images/turn-table/turn-table-icon-jp.png"
              alt=""
            />
          </div>
          <div class="no-reward-text">
            {{ userInfo.token ? $t('暂无奖品') : $t('请先登录') }}
          </div>
        </div>
        <div class="reward-list" v-else>
          <div
            class="reward-item"
            v-for="(item, index) in myWonList"
            :key="index"
          >
            <div class="reward-item-img">
              <img :src="item.goods_icon_url" alt="" />
            </div>
            <div class="reward-item-info">
              <div class="gold">{{ item.goods_name }}</div>
              <div class="date">
                {{ handleTimestamp(item.create_time).date }}&nbsp;
                {{ handleTimestamp(item.create_time).time }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-close-btn" @click="myRewardDialogShow = false">
        </div>
      </div>
    </van-dialog>

    <!-- 祝福宝箱介绍 -->
    <van-dialog
      v-model="wishIntroductionDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="wish-introduction-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="wish-content" v-html="boxText"></div>
        <div
          class="dialog-close-btn"
          @click="wishIntroductionDialogShow = false"
        >
        </div>
      </div>
    </van-dialog>

    <!-- 祝福宝箱介绍 -->
    <van-dialog
      v-model="wishResultDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="false"
      class="result-dialog"
    >
      <div class="result-dialog-content">
        <div class="bg-xg-container">
          <div class="bg-xg"></div>
          <div class="dialog-title">
            <img
              src="@/assets/images/turn-table/turn-table-icon-huode.png"
              alt=""
            />
          </div>
          <div class="result-list">
            <div class="result-item one">
              <img class="result-icon" :src="wishResultIcon" alt="" />
            </div>
          </div>
        </div>

        <div class="prizes-info">
          <span>{{ wishResultMessage }}</span>
        </div>
        <div
          class="dialog-close-btn btn-bottom"
          @click="wishResultDialogShow = false"
        ></div>
      </div>
    </van-dialog>
    <!-- 抽奖结果弹窗 -->
    <van-dialog
      v-model="resultDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="false"
      class="result-dialog"
    >
      <div class="result-dialog-content">
        <div
          class="bg-xg-container"
          :class="{ 'bg-lc': resultList.length !== 1 }"
        >
          <div class="bg-xg" v-if="resultList.length == 1"></div>
          <div class="dialog-title">
            <img
              v-if="resultList.length == 1 && resultList[0].id == 19"
              src="@/assets/images/turn-table/turn-table-icon-canyu.png"
              alt=""
            />
            <img
              v-else
              src="@/assets/images/turn-table/turn-table-icon-huode.png"
              alt=""
            />
          </div>
          <div
            class="result-list"
            :class="{ 'bg-lc': resultList.length !== 1 }"
          >
            <div
              class="result-item"
              v-for="(item, index) in resultList"
              :key="index"
              :class="{
                one: resultList.length == 1,
                xxhg: item.id == 19 && resultList.length == 1,
              }"
            >
              <img class="result-icon" :src="item.goods_icon_url" alt="" />
              <div class="result-name" v-if="resultList.length !== 1">
                {{ item.goods_name }}
              </div>
            </div>
          </div>
        </div>

        <div
          class="prizes-info"
          v-if="resultList.length == 1 && resultList[0].id !== 19"
        >
          <span>{{ resultList[0].goods_name }}</span>
        </div>
        <div class="operate">
          <div class="operate-item">
            <div class="operate-btn btn" @click="handleRaffle(1)">
              <span>{{ oneGold }}金币&nbsp;{{ $t('抽一次') }}</span>
            </div>
          </div>
          <div class="operate-item">
            <div class="operate-btn lian-chou btn" @click="handleRaffle(2)">
              <span>{{ tenGold }}金币&nbsp;{{ $t('10连抽') }}</span>
            </div>
          </div>
        </div>
        <div class="dialog-close-btn" @click="resultDialogShow = false"></div>
      </div>
    </van-dialog>

    <!-- 实物奖品提示弹窗 -->
    <!-- <van-dialog
        v-model="tipDialogShow"
        :lock-scroll="false"
        :show-confirm-button="true"
        confirm-button-color="#FF8B02"
        @confirm="closeTipDialog"
      >
        <div class="real-reward-tips">
          <div class="tip-text">
            {{
              $t(
                "即2020-06-08日起，抽中实物奖励的玩家可凭“我的奖品”中奖记录截图联系客服领取奖励"
              )
            }}
          </div>
          <div class="checkbox" @click="remember = !remember">
            <div class="gou" :class="{ on: remember }"></div>
            <div class="remember-text">{{ $t("我知道了，不再提示") }}</div>
          </div>
        </div>
      </van-dialog> -->
  </div>
</template>

<script>
import rubberBand from '@/components/rubber-band';
import {
  ApiGoldDialList,
  ApiGoldDialMyWonList,
  ApiGoldDialRaffle,
} from '@/api/views/users.js';
export default {
  name: 'TurnTable',
  components: {
    rubberBand,
  },
  data() {
    return {
      current: 0,
      dialList: [], // 转盘内容
      rules: '', // 抽奖规则
      maxReward: [], // 幸运用户列表
      allNum: 0, //祝福总值
      blessNum: 0, //祝福达标值
      boxContent: '',
      boxStatus: 0, //祝福宝箱状态 0=未达成 1=未领取 2=已领取
      boxText: '',
      oneGold: 10,
      tenGold: 100,
      myWonList: [], // 我的奖品列表
      isWatch: false,
      freeNum: 0, // 剩余免费次数
      timer: null, // 定时器
      // tipDialogShow: true, // 实物奖品提示窗
      resultDialogShow: false, // 抽奖结果提示窗
      remember: false, // 实物奖品下次不提示？
      resultList: [],
      isStarting: false,
      ruleDialogShow: false, // 规则弹窗
      myRewardDialogShow: false, // 我的奖品弹窗
      wishIntroductionDialogShow: false, // 祝福值介绍
      wishResultDialogShow: false, // 祝福结果弹窗
      wishResultIcon: '', // 祝福结果图标
      wishResultMessage: '', //祝福结果消息
      turnIndex: 0, // 转盘旋转角度
      animationIndex: 0, // 动画滚动索引
    };
  },
  computed: {
    currentProgress() {
      if (this.allNum > this.blessNum) {
        return 1;
      }
      return this.allNum / this.blessNum;
    },
  },
  async created() {
    this.$sensorsTrack('lottery_page_view', {
      $title: this.$sensorsPageGet(),
      $url: this.$route.fullPath,
      $referrer: this.$sensorsChainGet(),
    })
    // 是否显示实物奖品提示窗
    // this.remember = localStorage.getItem("REAL_REWARD_SHOW");
    // if (!!this.remember) {
    //   this.tipDialogShow = false;
    // }

    await this.getGoldDialList();
    await this.getMyWonList();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    // 领取祝福奖励
    async handleGetWish() {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      try {
        const res = await ApiGoldDialRaffle({
          type: 4,
        });
        this.$toast.clear();
        let { message, goods_icon_url } = res.data;
        this.wishResultMessage = message;
        this.wishResultIcon = goods_icon_url;
        this.wishResultDialogShow = true;
        await this.getGoldDialList();
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    async getGoldDialList() {
      const res = await ApiGoldDialList();
      let {
        dial_list,
        max_reward,
        text,
        all_num,
        bless_num,
        box_content,
        box_status,
        box_text,
        one_gold,
        ten_gold,
      } = res.data;
      this.dialList = dial_list;
      this.rules = text.replace(/\n/g, '<br>');
      this.maxReward = max_reward;
      this.allNum = all_num;
      this.blessNum = bless_num;
      this.boxContent = box_content;
      this.boxStatus = box_status;
      this.boxText = box_text.replace(/\n\n/gi, '<br/>');
      if (one_gold) this.oneGold = one_gold;
      if (ten_gold) this.tenGold = ten_gold;
    },
    async getMyWonList() {
      const res = await ApiGoldDialMyWonList();
      let { free_num, is_watch, list } = res.data;
      this.myWonList = list;
      this.isWatch = is_watch;
      this.freeNum = free_num;
    },
    async handleRaffle(type) {
      if (this.isStarting) {
        return false;
      }

      // 神策埋点
      this.$sensorsTrack("lottery_button_click");

      this.isStarting = true;
      this.resultDialogShow = false;
      this.current = 0;
      this.turnIndex = 0;

      if (type == 1) {
        type = this.freeNum != 0 ? 3 : 1;
      }
      let is_success = false;
      let fail_reason = ''
      // @param type 1 1连抽  2 10连抽  3 免费
      try {
        const res = await ApiGoldDialRaffle({
          type: type,
        });
        let { free_num, list } = res.data;

        this.resultList = list;

        is_success = true;

        
        await this.turning(this.resultList[0].id);
        this.current = 0;
        this.turnIndex = 0;
        this.resultDialogShow = true;
        this.freeNum = free_num;
        await this.getGoldDialList();
        await this.getMyWonList();
        await this.SET_USER_INFO();
      } catch(e) {
        fail_reason = e.msg || '未知错误';
      } finally {
        this.isStarting = false;
      }
      
      // 神策埋点
      this.$sensorsTrack("lottery_result", {
          reward: this.resultList.map(item => item.goods_name).join(','),
          is_success,
          fail_reason,
        })
    },
    turning(id) {
      return new Promise((resolve, reject) => {
        this.turnIndex = this.dialList.findIndex(item => {
          return item.id == id;
        });
        this.turnIndex = this.turnIndex + 1 + 40;
        this.timer = setInterval(() => {
          if (this.turnIndex > 10) {
            this.currentChange();
            this.turnIndex--;
          } else {
            clearInterval(this.timer);
            this.timer = setInterval(() => {
              if (this.turnIndex > 0) {
                this.currentChange();
                this.turnIndex--;
              } else {
                clearInterval(this.timer);
                this.timer = null;
                resolve();
              }
            }, 300);
          }
        }, 100);
      });
    },
    currentChange() {
      if (this.current > 7) {
        this.current = 1;
      } else {
        this.current++;
      }
    },
    handleTimestamp(ts) {
      let temp = new Date(ts * 1000),
        m = temp.getMonth() + 1,
        d = temp.getDate(),
        h = temp.getHours(),
        min = temp.getMinutes();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      h = h < 10 ? '0' + h : h;
      min = min < 10 ? '0' + min : min;
      return {
        date: `${m}-${d}`,
        time: `${h}:${min}`,
      };
    },
    closeTipDialog() {
      if (this.remember) {
        localStorage.setItem('REAL_REWARD_SHOW', this.remember);
      }
    },
    toLogin() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.turn-table-page {
  background: #cddaff;
  .back {
    width: 36 * @rem;
    height: 36 * @rem;
    background: url(~@/assets/images/nav-bar-back-black.png) #fff center center
      no-repeat;
    background-size: 10 * @rem 18 * @rem;
    border-radius: 10 * @rem;
  }
  .rule-btn {
    font-size: 14 * @rem;
    color: #ffffff;
  }
  .rule-box {
    position: fixed;
    top: 240 * @rem;
    right: 0;
    width: 31 * @rem;
    height: 77 * @rem;
    background: url(~@/assets/images/turn-table/turn-table-btn-rule.png) center
      center no-repeat;
    background-size: 31 * @rem 77 * @rem;
    z-index: 9;
  }
  .main {
    background: url(~@/assets/images/turn-table/turn-table-bg-new.png) no-repeat
      top center;
    background-size: 100% 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .coin-banner {
    width: 100%;
    height: 294 * @rem;
    margin: 78 * @rem auto 0;
  }

  .table-box {
    margin-top: 24 * @rem;
    position: relative;
    .table-1 {
      width: 100%;
      height: 93 * @rem;
      background: url(~@/assets/images/turn-table/table-1.png) center top
        no-repeat;
      background-size: 100% 93 * @rem;

      .user-bar {
        box-sizing: border-box;
        height: 93 * @rem;
        border-radius: 12 * @rem;
        margin: 13 * @rem auto 0;
        display: flex;
        align-items: center;
        padding: 23 * @rem 22 * @rem 30 * @rem 29 * @rem;
        position: relative;
        .avatar {
          box-sizing: border-box;
          width: 40 * @rem;
          height: 40 * @rem;
          border-radius: 50%;
          overflow: hidden;
          border: 1 * @rem solid #fff;
        }
        .info {
          margin-left: 10 * @rem;
          flex: 1;
          min-width: 0;
          .nickname {
            font-size: 14 * @rem;
            color: #fff;
            font-weight: 600;
            line-height: 20 * @rem;
          }
          .coins {
            font-size: 12 * @rem;
            line-height: 17 * @rem;
            color: #fff;
            font-weight: 400;
            margin-top: 3 * @rem;
            span {
              color: #fff;
              font-weight: 600;
            }
          }
        }
      }
    }

    .table-3 {
      position: relative;
      width: 100%;
      height: 622 * @rem;
      background: url(~@/assets/images/turn-table/turn-table-bg1.png) center top
        no-repeat;
      background-size: 100% 622 * @rem;
      overflow: hidden;
      .lucky-user-info {
        position: absolute;
        top: 165 * @rem;
        left: 62 * @rem;
        width: 251 * @rem;
        height: 21 * @rem;
        border-radius: 4 * @rem;
        margin: 0 auto;
        background: #e2f7ff;
        .lucky-container {
          box-sizing: border-box;
          width: 100%;
          height: 21 * @rem;
          line-height: 21 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .notice-icon {
            width: 17 * @rem;
            height: 17 * @rem;
            background: url(~@/assets/images/turn-table/turn-table-icon1.png)
              center center no-repeat;
            background-size: 17 * @rem 17 * @rem;
            margin-left: 7 * @rem;
          }
          .su {
            width: 1 * @rem;
            height: 11 * @rem;
            border: 1px solid #000000;
            margin-left: 7 * @rem;
            margin-right: 7 * @rem;
          }
          .lucky-swipe {
            width: 250 * @rem;
            height: 21 * @rem;
            .van-swipe-item {
              color: #000000;
              font-size: 12 * @rem;
              height: 21 * @rem;
              line-height: 21 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              span {
                color: #000000;
              }
            }
          }
        }
      }

      .table-container {
        position: absolute;
        top: 195 * @rem;
        margin: 0 41 * @rem;
        .table-content {
          box-sizing: border-box;
          width: 293 * @rem;
          height: 295 * @rem;
          background: url(~@/assets/images/turn-table/turn-table-bg2.png) center
            center no-repeat;
          background-size: 293 * @rem 295 * @rem;
          padding: 21 * @rem;
          .table-list {
            // background-color: #fff;
            position: relative;
            .table-item {
              // width: 103 * @rem;
              // height: 103 * @rem;
              // background: url(~@/assets/images/turn-table/turn-item-new.png)
              //   center center no-repeat;
              // background-size: 103 * @rem 103 * @rem;
              width: 76 * @rem;
              height: 76 * @rem;
              background: #dff4ff;
              border-radius: 8 * @rem;
              border: 2 * @rem solid #000000;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              position: absolute;
              box-sizing: border-box;
              &.active {
                width: 76 * @rem;
                height: 76 * @rem;
                background: linear-gradient(180deg, #ffecc0 0%, #fbd787 100%);
                border-radius: 8 * @rem;
                border: 2 * @rem solid #ffffff;
              }
              .reward-icon {
                height: 42 * @rem;
                width: 42 * @rem;
              }
              .reward-text {
                font-size: 12 * @rem;
                line-height: 12 * @rem;
                color: #000000;
                margin-top: 2 * @rem;
                font-weight: 600;
              }
              &:nth-of-type(1) {
                left: 175 * @rem;
                top: 0;
              }
              &:nth-of-type(2) {
                left: 175 * @rem;
                top: 88 * @rem;
              }
              &:nth-of-type(3) {
                left: 175 * @rem;
                top: 176 * @rem;
              }
              &:nth-of-type(4) {
                left: 88 * @rem;
                top: 176 * @rem;
              }
              &:nth-of-type(5) {
                left: 0;
                top: 176 * @rem;
              }
              &:nth-of-type(6) {
                left: 0;
                top: 88 * @rem;
              }
              &:nth-of-type(7) {
                left: 0;
                top: 0;
              }
              &:nth-of-type(8) {
                left: 88 * @rem;
                top: 0;
              }
              &.turn-btn-1 {
                left: 81 * @rem;
                top: 81 * @rem;
                border: none;
                width: 89 * @rem;
                height: 90 * @rem;
                background: none;
                background-image: url(~@/assets/images/turn-table/turn-table-btn-icon.png);
                background-size: 89 * @rem 90 * @rem;
                .bg {
                  width: 89 * @rem;
                  height: 90 * @rem;
                  background-image: url(~@/assets/images/turn-table/turn-btn-bg.png);
                  background-size: 89 * @rem 90 * @rem;
                  position: absolute;
                  left: 0;
                  top: 0;
                  z-index: 1;
                  transform: rotate(0deg);
                  &.turn-result-0 {
                    transform: rotate(0deg);
                  }
                  &.turn-result-1 {
                    transform: rotate(3645deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-2 {
                    transform: rotate(3690deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-3 {
                    transform: rotate(3735deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-4 {
                    transform: rotate(3780deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-5 {
                    transform: rotate(3825deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-6 {
                    transform: rotate(3870deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-7 {
                    transform: rotate(3915deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-8 {
                    transform: rotate(3960deg);
                    transition: all 5s ease-in-out;
                  }
                }
                .turn-btn-1-content {
                  position: absolute;
                  width: 89 * @rem;
                  height: 90 * @rem;
                  left: 0;
                  top: 0;
                  z-index: 2;
                }
                .turn-btn-1-title {
                  font-size: 26 * @rem;
                  font-weight: bold;
                  color: #8c0002;
                }
                .turn-btn-1-tip {
                  color: #000000;
                  text-align: center;
                  font-size: 14 * @rem;
                  font-weight: 600;
                  margin-top: 53 * @rem;
                  white-space: pre-line;
                  &.free {
                    font-size: 12 * @rem;
                  }
                }
              }
            }
          }
        }
      }
    }
    .current-gold {
      position: absolute;
      top: 497 * @rem;
      left: 100 * @rem;
      width: 175 * @rem;
      overflow: hidden;
      height: 26 * @rem;
      background: #5477f3;
      border-radius: 4 * @rem;
      .coins {
        font-size: 14 * @rem;
        height: 26 * @rem;
        line-height: 26 * @rem;
        color: #fff;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        span {
          white-space: nowrap;
          color: #fff;
        }
      }
    }
    .turn-btn-reward {
      position: absolute;
      left: 42 * @rem;
      top: 546 * @rem;
      .login,
      .my-reward {
        position: relative;
        width: 127 * @rem;
        height: 42 * @rem;
        background: url(~@/assets/images/turn-table/turn-table-btn-jp.png)
          center center no-repeat;
        background-size: 127 * @rem 42 * @rem;
        line-height: 19 * @rem;

        .login-text,
        .reward-text {
          position: absolute;
          top: 9 * @rem;
          left: 50%;
          transform: translateX(-50%);
          white-space: nowrap;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #000000;
        }
      }
    }
    .turn-btn-box {
      position: absolute;
      right: 42 * @rem;
      top: 546 * @rem;
      width: 151 * @rem;
      height: 42 * @rem;
      background: url(~@/assets/images/turn-table/turn-table-btn-lian.png)
        center center no-repeat;
      background-size: 151 * @rem 42 * @rem;
      .turn-btn-10 {
        top: 9 * @rem;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        .btn-tip,
        .btn-text {
          white-space: nowrap;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #000000;
        }
        .btn-text {
          margin-left: 5 * @rem;
        }
      }
    }

    // .table-4 {
    //   width: 100%;
    //   height: 131 * @rem;
    //   background: url(~@/assets/images/turn-table/table-4.png) center top
    //     no-repeat;
    //   background-size: 100% 131 * @rem;
    //   margin-top: -1 * @rem;
    // }
  }
  .wish-container {
    box-sizing: border-box;
    border: 3 * @rem solid #6283f8;
    width: 355 * @rem;
    height: 113 * @rem;
    margin: 16 * @rem auto;
    border-radius: 12 * @rem;
    padding: 15 * @rem 20 * @rem 17 * @rem 16 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      width: 220 * @rem;
      .wish-top {
        display: flex;
        align-items: center;
        .current-wish {
          font-weight: 600;
          font-size: 16 * @rem;
          color: #000000;
        }
        .wish-question {
          width: 16 * @rem;
          height: 16 * @rem;
          .image-bg('~@/assets/images/turn-table/turn-table-icon.png');
          margin-left: 9 * @rem;
        }
      }
      .wish-progress {
        width: 220 * @rem;
        height: 6 * @rem;
        background: #b9c8ff;
        border-radius: 3 * @rem;
        margin-top: 10 * @rem;
        .current-progress {
          width: 10%;
          height: 6 * @rem;
          border-radius: 3 * @rem;
          background: #829cfb;
        }
      }
      .wish-introduction {
        font-size: 12 * @rem;
        color: #798cd0;
        line-height: 17 * @rem;
        margin-top: 10 * @rem;
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      .wish-icon {
        width: 46 * @rem;
        height: 46 * @rem;
        .image-bg('~@/assets/images/turn-table/turn-table-box.png');
      }
      .wish-btn {
        width: 52 * @rem;
        height: 22 * @rem;
        background: #94abfd;
        border-radius: 11 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #ffffff;
        font-weight: 400;
        margin-top: 3 * @rem;
        white-space: nowrap;
        &.default {
          background: #94abfd;
          color: #000000;
        }
        &.on {
          background: #ffd438;
          color: #000000;
        }
      }
    }
  }
  .section-title {
    font-size: 18 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20 * @rem 0 15 * @rem;
    .title-text {
      display: inline-block;
      margin: 0 auto;
      position: relative;
      &:before {
        content: '';
        width: 6 * @rem;
        height: 6 * @rem;
        border-radius: 3 * @rem;
        background-color: #fff;
        position: absolute;
        left: -15 * @rem;
        top: 50%;
        transform: translateY(-50%);
      }
      &:after {
        content: '';
        width: 6 * @rem;
        height: 6 * @rem;
        border-radius: 3 * @rem;
        background-color: #fff;
        position: absolute;
        right: -15 * @rem;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .reward-container {
    box-sizing: border-box;
    width: 345 * @rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3 * @rem;
    margin: 16 * @rem auto 0;
    padding-bottom: 20 * @rem;
    .no-reward {
      padding: 20 * @rem 0;
      text-align: center;
      font-size: 15 * @rem;
      color: #ffffff;
    }
    .reward-list {
      padding: 0 16 * @rem;
      font-size: 14 * @rem;
      color: #ffffff;
      height: 150 * @rem;
      overflow-y: auto;
      .reward-item {
        display: flex;
        justify-content: space-between;
        line-height: 30 * @rem;
        height: 30 * @rem;
      }
    }
  }

  .rule-container {
    box-sizing: border-box;
    width: 345 * @rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3 * @rem;
    margin: 23 * @rem auto 0;
    padding: 0 13 * @rem 15 * @rem 16 * @rem;
    .rule-content {
      font-size: 14 * @rem;
      color: #fff;
      line-height: 22 * @rem;
    }
  }
  /deep/ .van-dialog {
    border-radius: 10 * @rem;
    width: 320 * @rem;
  }
  .real-reward-tips {
    padding: 25 * @rem 23 * @rem 25 * @rem;
    .tip-text {
      font-size: 15 * @rem;
      color: #333333;
      line-height: 24 * @rem;
    }
    .checkbox {
      display: flex;
      align-items: center;
      margin-top: 15 * @rem;
      .gou {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/turn-table/gou.png) center center
          no-repeat;
        background-size: 15 * @rem 15 * @rem;
        &.on {
          background-image: url(~@/assets/images/turn-table/gou-on.png);
        }
      }
      .remember-text {
        font-size: 13 * @rem;
        color: #999999;
        margin-left: 6 * @rem;
      }
    }
  }

  .result-dialog {
    background: transparent;
    width: 100%;
    overflow: visible;
    .result-dialog-content {
      position: relative;
      .bg-xg-container {
        position: relative;
        height: 248 * @rem;
        &.bg-lc {
          height: 227 * @rem;
        }
        .bg-xg {
          width: 100%;
          height: 100%;
          background: url(~@/assets/images/turn-table/turn-table-bg-xg.png)
            no-repeat center center;
          background-size: 280 * @rem 248 * @rem;
          // animation: rotate-animation 10s linear infinite;
        }
        .dialog-title {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 141 * @rem;
          height: 39 * @rem;
          margin: 0 auto 0;
        }
        .result-list {
          position: absolute;
          height: 100%;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          &.bg-lc {
            top: 52 * @rem;
            height: 175 * @rem;
            padding: 20 * @rem 20 * @rem 21 * @rem 20 * @rem;
            background: url(~@/assets/images/turn-table/turn-table-bg-lianchou.png)
              no-repeat center center;
            background-size: 100% 175 * @rem;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            box-sizing: border-box;
            gap: 16 * @rem 10 * @rem;
            .result-item {
              width: 59 * @rem;
              height: 59 * @rem;
              background: linear-gradient(180deg, #ffecc0 0%, #fbd787 100%);
              border-radius: 10 * @rem;
              border: 2 * @rem solid #ffffff;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              img {
                width: 35 * @rem;
                height: 35 * @rem;
              }
              .result-name {
                margin-top: 3 * @rem;
                font-size: 11 * @rem;
              }
            }
          }
          .result-item {
            box-sizing: border-box;
            width: 59 * @rem;
            height: 59 * @rem;
            background: linear-gradient(180deg, #ffecc0 0%, #fbd787 100%);
            border-radius: 10 * @rem;
            border: 2 * @rem solid #ffffff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            // margin: 15 * @rem auto 0;
            &:nth-of-type(-n + 5) {
              margin-top: 0;
            }
            .result-icon {
              width: 66 * @rem;
              height: 66 * @rem;
            }
            .result-name {
              font-size: 16 * @rem;
              color: #000000;
              font-weight: 500;
              white-space: nowrap;
              width: 100%;
              overflow: hidden;
              text-align: center;
              margin-top: 1 * @rem;
            }
            &.one {
              width: 107 * @rem;
              height: 107 * @rem;
              border-radius: 20 * @rem;
              position: relative;
              &.xxhg {
                background: #ffffff;
              }
              .result-icon {
                width: 66 * @rem;
                height: 66 * @rem;
              }
              .result-name {
                font-size: 16 * @rem;
                color: #000000;
                margin-top: 1 * @rem;
                font-weight: 500;
              }
            }
          }
        }
      }

      .prizes-info {
        width: 282 * @rem;
        height: 48 * @rem;
        line-height: 48 * @rem;
        text-align: center;
        font-size: 18 * @rem;
        color: #fff;
        font-weight: 500;
        margin: 0 auto;
        background: url(~@/assets/images/turn-table/turn-table-bg-prizes.png)
          no-repeat center center;
        background-size: 282 * @rem 100%;
      }
      .operate {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 21 * @rem;
        .operate-item {
          &:not(:first-of-type) {
            margin-left: 15 * @rem;
          }
          .operate-btn {
            position: relative;
            width: 145 * @rem;
            height: 42 * @rem;
            background: url(~@/assets/images/turn-table/turn-table-btn-yici.png)
              no-repeat center center;
            background-size: 145 * @rem 42 * @rem;
            white-space: nowrap;
            display: flex;
            justify-content: center;
            align-items: center;
            &.lian-chou {
              background: url(~@/assets/images/turn-table/turn-table-btn-shici.png)
                no-repeat center center;
              background-size: 145 * @rem 42 * @rem;
            }
            span {
              position: absolute;
              top: 11 * @rem;
              font-size: 16 * @rem;
              color: #000000;
              font-weight: bold;
            }
          }
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -54 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/turn-table/turn-table-btn-close.png)
          no-repeat 0 0;
        background-size: 34 * @rem 34 * @rem;
        width: 34 * @rem;
        height: 34 * @rem;
        &.btn-bottom {
          bottom: -73 * @rem;
        }
      }
    }
  }
  .rule-dialog,
  .my-reward-dialog {
    box-sizing: border-box;
    width: 335 * @rem;
    border-radius: 16px;
    background-color: #fff;
    top: 50%;
    padding: 10 * @rem 18 * @rem;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      padding: 12 * @rem 0;
    }
    .close {
      width: 33 * @rem;
      height: 33 * @rem;
      background: url(~@/assets/images/turn-table/popup-close.png) center center
        no-repeat;
      background-size: 15 * @rem 15 * @rem;
      position: absolute;
      right: 0;
      top: 0;
    }
    .no-reward {
      text-align: center;
      padding: 30 * @rem 0 50 * @rem;
    }
    .rule-content {
      font-size: 13 * @rem;
      line-height: 20 * @rem;
      color: #000000;
      font-weight: 400;
      /deep/ p {
        margin-bottom: 5 * @rem;
      }
    }
  }
  .rule-content-dialog {
    width: 331 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      background: url('~@/assets/images/turn-table/turn-table-bg-rule.png')
        no-repeat center center;
      background-size: 331 * @rem 96 * @rem;
      width: 331 * @rem;
      height: 96 * @rem;
      margin: 0 auto;
      position: relative;
      z-index: 3;
      top: 1 * @rem;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 0 0 16 * @rem 16 * @rem;
      z-index: 2;
      padding: 0 19 * @rem 19 * @rem 19 * @rem;
      width: 331 * @rem;
      height: 433 * @rem;
      text-align: center;
      .wish-content {
        height: 433 * @rem;
        overflow-x: hidden;
        overflow-y: auto;
        font-size: 14 * @rem;
        color: #000000;
        line-height: 24 * @rem;
        font-weight: 400;
        text-align: justify;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -54 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/turn-table/turn-table-btn-close.png)
          no-repeat 0 0;
        background-size: 34 * @rem 34 * @rem;
        width: 34 * @rem;
        height: 34 * @rem;
      }
    }
  }
  .reward-content-dialog {
    width: 331 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      background: url('~@/assets/images/turn-table/turn-table-bg-jp.png')
        no-repeat center center;
      background-size: 331 * @rem 95 * @rem;
      width: 331 * @rem;
      height: 95 * @rem;
      margin: 0 auto;
      position: relative;
      z-index: 3;
      top: 1 * @rem;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 0 0 16 * @rem 16 * @rem;
      z-index: 2;
      padding: 0 20 * @rem 0 20 * @rem;
      width: 331 * @rem;
      height: 257 * @rem;
      text-align: center;
      .no-reward {
        padding: 57 * @rem 0 0 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        .no-reward-icon {
          width: 60 * @rem;
          height: 60 * @rem;
        }
        .no-reward-text {
          margin-top: 16 * @rem;
          font-size: 14 * @rem;
          color: #999999;
        }
      }
      .reward-list {
        font-size: 14 * @rem;
        height: 252 * @rem;
        box-sizing: border-box;
        color: #000000;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }
        .reward-item {
          display: flex;
          justify-content: space-between;
          height: 54 * @rem;
          margin-bottom: 12 * @rem;
          .reward-item-img {
            width: 54 * @rem;
            height: 54 * @rem;
            background: #dff4ff;
            border-radius: 8 * @rem;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 42 * @rem;
              height: 42 * @rem;
            }
          }
          .reward-item-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;
            .gold {
              font-weight: 500;
              font-size: 16 * @rem;
              color: #000000;
            }
            .date {
              margin-top: 8 * @rem;
              font-weight: 500;
              font-size: 12 * @rem;
              color: #999999;
            }
          }
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -54 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/turn-table/turn-table-btn-close.png)
          no-repeat 0 0;
        background-size: 34 * @rem 34 * @rem;
        width: 34 * @rem;
        height: 34 * @rem;
      }
    }
  }
  .my-reward-dialog {
    padding: 10 * @rem 0;
    .reward-list {
      height: 289 * @rem;
      overflow-y: auto;
      .reward-item {
        height: 40 * @rem;
        margin: 0 18 * @rem;
        border-bottom: 0.5px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        &:last-of-type {
          border-bottom: 0;
        }
      }
    }
  }
  .wish-introduction-dialog {
    width: 331 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      background: url('~@/assets/images/turn-table/turn-table-bg-bx.png')
        no-repeat center center;
      background-size: 331 * @rem 95 * @rem;
      width: 331 * @rem;
      height: 95 * @rem;
      margin: 0 auto;
      position: relative;
      z-index: 3;
      top: 1 * @rem;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 0 0 16 * @rem 16 * @rem;
      z-index: 2;
      padding: 0 19 * @rem 19 * @rem 19 * @rem;
      width: 331 * @rem;
      height: 207 * @rem;
      text-align: center;
      .wish-content {
        height: 207 * @rem;
        overflow-y: auto;
        font-size: 14 * @rem;
        color: #000000;
        line-height: 24 * @rem;
        font-weight: 400;
        text-align: justify;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -54 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/turn-table/turn-table-btn-close.png)
          no-repeat 0 0;
        background-size: 34 * @rem 34 * @rem;
        width: 34 * @rem;
        height: 34 * @rem;
      }
    }
  }
  .wish-result-dialog {
    width: 278 * @rem;
    padding: 10 * @rem 0;
    border-radius: 19 * @rem;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      padding: 20 * @rem 0 16 * @rem;
    }
    .wish-content {
      padding: 0 24 * @rem;
      font-size: 14 * @rem;
      color: #000000;
      line-height: 20 * @rem;
      font-weight: 400;
      text-align: justify;
    }
    .wish-btn {
      width: 208 * @rem;
      height: 40 * @rem;
      background: #ed4250;
      border-radius: 20 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      color: #ffffff;
      margin: 20 * @rem auto 10 * @rem;
    }
    .wish-result-content {
      font-size: 16 * @rem;
      color: #ed4250;
      text-align: center;
      padding: 12 * @rem 0;
    }
    .wish-result-btn {
      width: 172 * @rem;
      height: 48 * @rem;
      background: url(~@/assets/images/turn-table/wish-result-btn.png) center
        center no-repeat;
      background-size: 172 * @rem 48 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      line-height: 42 * @rem;
      text-align: center;
      margin: 20 * @rem auto 10 * @rem;
      color: #fff;
    }
  }
  @keyframes rotate-animation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
