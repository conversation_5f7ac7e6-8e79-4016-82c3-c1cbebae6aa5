<template>
  <div class="page">
    <nav-bar-2 :border="true" title="金币小贴士" :azShow="true"> </nav-bar-2>
    <div class="section">
      <div class="help-list">
        <div class="help-item">
          <div class="sub-title">{{ $t('特殊提醒') }}</div>
          <div class="desc">
            {{
              $t(
                '由于游戏厂商的要求，部分游戏内充值暂不支持使用金币抵扣，请先确定需要用金币充值的游戏在不在可使用名单内。',
              )
            }}<i @click="openGameDialog">{{ $t('查看名单') }}&gt;</i>
          </div>
        </div>
        <div class="help-item">
          <div class="sub-title">{{ $t('金币有什么用？') }}</div>
          <div class="desc">
            金币作用等同于平台币，可用于游戏内支付抵扣。折扣/网游/部分BT游戏暂不支持金币支付，具体以游戏支付页面显示为准。玩家还可以在<span
              @click="goToTurnTable"
              >金币转盘</span
            ><span>、</span><span @click="goToGoldGamble">金币夺宝</span
            ><span
              >中使用金币兑换代金券和其他奖励，或者在金币商城中兑换平台币（绑定），该平台币（绑定）可在绝大部分的折扣/网游/BT游戏中充值抵扣，具体以游戏支付页面显示为准。</span
            >
          </div>
        </div>
        <div class="help-item">
          <div class="sub-title">金币抵扣说明</div>
          <div class="desc">
            支持金币抵扣的游戏在游戏充值界面将展示金币抵扣开关。支付金额大于1元才能使用金币抵扣。每100金币抵扣1元，抵扣时需要使用100的整数倍的金币数量。
            <div class="jinbidikou-pic">
              <img src="@/assets/images/recharge/jinbidikou.png" alt="" />
            </div>
          </div>
        </div>
        <div class="help-item">
          <div class="sub-title">{{ $t('如何获取金币？') }}</div>
          <div class="desc">
            {{ $t('您可以通过')
            }}<span>{{
              $t('每日签到、连续签到宝箱、发布游戏评论、完成任务')
            }}</span
            >等途径获取金币！
          </div>
        </div>
        <div class="help-item">
          <div class="sub-title">金币退回说明</div>
          <div class="desc">
            若订单支付失败，在关闭订单后，抵扣的金币将自动返回到账号上。若有疑问，可联系客服。
          </div>
        </div>
        <div class="help-item">
          <div class="sub-title">金币兑换平台币说明</div>
          <div class="desc">
            1.金币兑换的平台币为平台币（绑定）；<br />
            2.金币兑换的平台币<span>在游戏内付费支付不计入实付统计</span>；<br />
            3.金币兑换的平台币<span>暂不支持GM版游戏/腾讯小游戏使用</span>。
          </div>
        </div>
      </div>
    </div>
    <!-- 查询可用游戏 -->
    <van-dialog
      v-model="gameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
    >
      <div class="search-container">
        <div class="close-search" @click="gameDialogShow = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="inputGame"
                :placeholder="$t('输入游戏名')"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">{{ $t('搜索') }}</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!gameList.length"
          :tips="$t('请输入您想搜索的游戏')"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in gameList"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : $t('不')
                }}{{ $t('支持使用金币抵扣') }}
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_goToGame,
} from '@/utils/box.uni.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
export default {
  data() {
    return {
      gameDialogShow: false, // 是否显示非金币抵扣游戏
      gameList: [], // 非金币抵扣的游戏列表
      inputGame: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
    };
  },
  methods: {
    goToGoldGamble() {
      BOX_openInNewWindow({ name: 'GoldGamble' }, { url: h5Page.jinbinduobao });
    },
    goToTurnTable() {
      BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
    },
    async handleShowNoGoldGame() {
      this.$toast.loading({
        message: this.$t('加载中'),
      });
      await this.getNoGoldGameList();
      this.$toast.clear();
      this.showNoGoldPopup = true;
    },
    async loadMore() {
      await this.getNoGoldGameList(2);
    },
    openGameDialog() {
      this.gameDialogShow = true;
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    async searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      this.gameList = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  padding: 15 * @rem 17 * @rem 15 * @rem;
  .close {
    position: absolute;
    right: 0;
    top: 0;
    width: 16 * @rem;
    height: 16 * @rem;
    padding: 13 * @rem;
    background: url(~@/assets/images/close-dialog.png) center center no-repeat;
    background-size: 16 * @rem 16 * @rem;
  }
  .title {
    font-size: 17 * @rem;
    color: #000000;
    text-align: center;
  }
  .content {
    font-size: 14 * @rem;
    color: #666;
    line-height: 20 * @rem;
    text-align: justify;
    margin-top: 15 * @rem;
    p {
      margin-top: 8 * @rem;
    }
  }
}

.section {
  box-sizing: border-box;
  padding: 0 20 * @rem 25 * @rem;
  background: #ffffff;
  border-radius: 12 * @rem;
  margin: 0 auto;
  position: relative;
  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title {
        font-size: 18 * @rem;
        color: #000000;
        font-weight: 600;
      }
      .icon {
        width: 20 * @rem;
        height: 20 * @rem;
        background-size: 20 * @rem 20 * @rem;
        margin-left: 5 * @rem;
        &.gold-coin-coupon {
          background-image: url(~@/assets/images/recharge/gold-coin-coupon.png);
        }
        &.gold-coin-welfare {
          background-image: url(~@/assets/images/recharge/gold-coin-welfare.png);
        }
        &.gold-coin-help {
          background-image: url(~@/assets/images/recharge/gold-coin-help.png);
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      .right-title {
        font-size: 13 * @rem;
        color: #000000;
      }
      .right-icon-white {
        width: 12 * @rem;
        height: 12 * @rem;
        background-size: 12 * @rem 12 * @rem;
        margin-left: 4 * @rem;
        &.gold-coin-rule {
          background-image: url(~@/assets/images/recharge/gold-coin-rule.png);
        }
      }
    }
  }
}
.help-list {
  .help-item {
    padding-top: 15 * @rem;
    margin-top: 12 * @rem;
    .jinbidikou-pic {
      width: 100%;
      background-color: #f5f5f5;
      margin-top: 10 * @rem;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    &:not(:first-of-type) {
      border-top: 0.5 * @rem solid #f3f3f8;
    }
    .sub-title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 600;
      padding-left: 20 * @rem;
      position: relative;
      &::before {
        content: '';
        width: 8 * @rem;
        height: 8 * @rem;
        background-color: #fe6600;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .desc {
      font-size: 14 * @rem;
      color: #444444;
      line-height: 22 * @rem;
      margin-top: 10 * @rem;
      i {
        font-size: 13 * @rem;
        color: #fe6600;
      }
      span {
        color: #fe6600;
      }
    }
  }
}
.no-gold-popup {
  width: 287 * @rem;
  background-color: transparent;
  .popup-content {
    box-sizing: border-box;
    width: 287 * @rem;
    height: 357 * @rem;
    background: #ffffff;
    border-radius: 16 * @rem;
    padding-bottom: 10 * @rem;
    .title {
      height: 50 * @rem;
      background-color: #f5f5f6;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
    }
    .game-list {
      box-sizing: border-box;
      width: 100%;
      height: 297 * @rem;
      overflow-y: auto;
      padding: 5 * @rem 18 * @rem;
      .game-item {
        width: 100%;
        padding: 15 * @rem 0;
        display: flex;
        align-items: center;
        border-bottom: 0.5 * @rem solid #f3f3f8;
        .game-icon {
          width: 36 * @rem;
          height: 36 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
        }
        .game-info {
          flex: 1;
          margin-left: 8 * @rem;
          .game-name {
            font-size: 14 * @rem;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .desc {
            font-size: 10 * @rem;
            color: #797979;
            margin-top: 4 * @rem;
          }
        }
      }
    }
  }
  .popup-close {
    width: 26 * @rem;
    height: 26 * @rem;
    .image-bg('~@/assets/images/recharge/popup-close.png');
    margin: 24 * @rem auto 0;
  }
}
/deep/ .van-dialog {
  overflow: unset;
  width: 320 * @rem;
}
.search-container {
  box-sizing: border-box;
  width: 320 * @rem;
  height: 450 * @rem;
  padding: 24 * @rem 19 * @rem 10 * @rem;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: unset;
  .close-search {
    width: 24 * @rem;
    height: 24 * @rem;
    background: url(~@/assets/images/recharge/close-search.png) center center
      no-repeat;
    background-size: 24 * @rem 24 * @rem;
    position: absolute;
    right: -10 * @rem;
    top: -10 * @rem;
  }
  .search-bar {
    display: flex;
    align-items: center;
    .input-text {
      width: 240 * @rem;
      height: 35 * @rem;
      border: 1px solid #e5e5e5;
      border-radius: 18 * @rem;
      flex: 1;
      overflow: hidden;
      form {
        display: block;
        width: 100%;
        height: 100%;
      }
      input {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 0 18 * @rem;
        font-size: 15 * @rem;
        color: #333333;
        background-color: #f6f6f6;
      }
    }
    .search-btn {
      font-size: 15 * @rem;
      color: #666666;
      padding-left: 13 * @rem;
      height: 35 * @rem;
      line-height: 35 * @rem;
    }
  }
  .game-list {
    flex: 1;
    overflow: auto;
    margin-top: 10 * @rem;
    .game-item {
      display: flex;
      align-items: center;
      padding: 21 * @rem 0;
      border-bottom: 1px solid #eeeeee;
      .game-icon {
        width: 50 * @rem;
        height: 50 * @rem;
        border-radius: 10 * @rem;
        background-color: #b5b5b5;
      }
      .right {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .game-name {
          font-size: 16 * @rem;
          font-weight: bold;
          color: #000000;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .use-card {
          font-size: 12 * @rem;
          color: #f72e2e;
          margin-top: 10 * @rem;
          &.can {
            color: #36b150;
          }
        }
      }
    }
  }
}
</style>
