<template>
  <div class="pay-help-detail-page page">
    <nav-bar-2 title="财富值明细" :border="true" :azShow="false"></nav-bar-2>
    <div class="main">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="record-list">
          <div
            class="record-item"
            v-for="(item, index) in detailList"
            :key="index"
          >
            <div class="info">
              <div class="title">{{ item.desc }}</div>
              <div class="date">{{ formatDate(item.create_time) }}</div>
            </div>
            <div class="num" :class="{ green: item.type == 4 }">
              {{ item.type == 4 ? '-' : '' }}{{ item.amount }}
            </div>
          </div>
        </div>
      </yy-list>
    </div>
    <bottom-safe-area></bottom-safe-area>
  </div>
</template>

<script>
import { ApiUserGetWealthPointsLog } from '@/api/views/users';
import { platform } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 30,
      empty: false,
      detailList: [],
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = '财富值明细';
    }
  },
  methods: {
    async getList() {
      const res = await ApiUserGetWealthPointsLog({
        page: this.page,
        listRows: this.listRows,
      });
      if (this.page === 1) {
        this.detailList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.detailList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
      this.page++;
    },
    formatDate(val) {
      let { year, date, time, second } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
};
</script>

<style lang="less" scoped>
.pay-help-detail-page {
  .main {
    display: flex;
    flex-direction: column;
    flex: 1;
    .record-list {
      .record-item {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #eeeeee;
        padding: 10 * @rem 14 * @rem;
        .info {
          .title {
            font-size: 15 * @rem;
            color: #333333;
          }
          .date {
            font-size: 12 * @rem;
            color: #999999;
            margin-top: 8 * @rem;
          }
        }
        .num {
          font-size: 15 * @rem;
          color: #f60000;
          &.green {
            color: #00902c;
          }
        }
      }
    }
  }
}
</style>
