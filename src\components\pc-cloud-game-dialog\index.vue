<template>
  <!-- PC云游戏非会员签到弹窗 -->
  <div>
    <van-dialog
      v-model="show"
      :closeOnClickOverlay="true"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-title">
        <img src="~@/assets/images/cloud-game/sign-guide-top-game.png" alt="" />
      </div>
      <div class="container">
        <div class="diagram-box">
          <img
            src="~@/assets/images/cloud-game/sign-guide-diagram.png"
            alt=""
          />
        </div>
        <div class="operation-bar">
          <div
            class="btn operation-btn cancel"
            @click="onCancel"
            v-if="showCancel"
          >
            {{ cancelText }}
          </div>
          <div
            class="btn operation-btn confirm"
            @click="onConfirm"
            v-if="showConfirm"
          >
            {{ confirmText }}
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true,
      cancelText: '取消',
      confirmText: '确定',
      showCancel: false,
      showConfirm: true,
    };
  },
  methods: {
    onCancel() {
      this.show = false;
    },
    onConfirm() {
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 300 * @rem;
  background: linear-gradient(119deg, #fdfbeb 0%, #f1ae30 100%);

  .popup-title {
    width: 300 * @rem;
    height: 101 * @rem;
  }
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 187 * @rem;
    padding: 19 * @rem 22 * @rem 16 * @rem 22 * @rem;
    background: linear-gradient(180deg, #fdfbeb 0%, #fffffd 100%);
    border-radius: 16 * @rem;
    overflow: hidden;
    box-sizing: border-box;
    .diagram-box {
      width: 256 * @rem;
      height: 100 * @rem;
    }
    .operation-bar {
      display: flex;
      align-items: center;
      margin: 16 * @rem 0 0;
      .operation-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 0;
        height: 36 * @rem;
        line-height: 36 * @rem;
        font-weight: 600;
        border-radius: 18 * @rem;
        font-size: 14 * @rem;
        &.confirm {
          margin-left: 10 * @rem;
          width: 150 * @rem;
          white-space: nowrap;
          background: linear-gradient(90deg, #ffe492 2%, #fcb458 100%);
          color: #823305;
        }
        &.cancel {
          width: 96 * @rem;
          white-space: nowrap;
          box-sizing: border-box;
          color: #cf9241;
          border: 1px solid #cf9241;
          background: linear-gradient(180deg, #fdfbeb 0%, #fffffd 100%);
        }
      }
    }
  }
}
</style>
