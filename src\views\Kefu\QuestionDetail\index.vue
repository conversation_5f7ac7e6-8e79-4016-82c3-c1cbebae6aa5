<template>
  <div class="question-detail">
    <nav-bar-2 :title="detail.title" :azShow="true" :border="true"></nav-bar-2>
    <div class="main">
      <div class="question-content" v-html="detail.content"></div>
    </div>
    <div class="bottom-container">
      <div class="bottom-fixed">
        <div class="help-container" v-if="!feedback">
          <div class="help-btn" @click="clickHelp(1)">
            <img src="@/assets/images/kefu/help.png" alt="" />
            {{ $t('有帮助') }}
          </div>
          <div class="help-btn" @click="clickHelp(2)">
            <img src="@/assets/images/kefu/help-no.png" alt="" />
            {{ $t('没帮助') }}
          </div>
        </div>
        <div class="bottom-btn-container">
          <div class="bottom-btn-title">{{ $t('不是你想要的问题') }}</div>
          <div class="bottom-btn" @click="connectQiYu()">
            {{ $t('联系客服') }}
          </div>
        </div>
        <bottom-safe-area></bottom-safe-area>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiFeedGetQuestionsInfo,
  ApiFeedQuestionsFeedback,
} from '@/api/views/feedback.js';
export default {
  name: 'QuestionDetail',
  data() {
    return {
      pageTitle: '',
      detail: {},
      id: 0,
      feedback: {},
    };
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getDetail();
  },
  methods: {
    async getDetail() {
      const res = await ApiFeedGetQuestionsInfo({ id: this.$route.params.id });
      this.detail = res.data.item;
      this.feedback = res.data.feedback;
    },
    async clickHelp(type) {
      const res = await ApiFeedQuestionsFeedback({ id: this.id, type });
      this.$toast(this.$t('提交成功'));
      await this.getDetail();
    },
    connectQiYu() {
      this.openKefu();
    },
  },
};
</script>

<style lang="less" scoped>
.question-detail {
  min-height: 100vh;
  .main {
    padding: 18 * @rem;
    .question-content {
      font-size: 15 * @rem;
      line-height: 22 * @rem;
      /deep/ img {
        max-width: 100% !important;
        margin: 10 * @rem 0;
        height: auto !important;
      }
      /deep/ p {
        margin-bottom: 10 * @rem;
      }
    }
  }
  .bottom-container {
    height: calc(224 * @rem + @safeAreaBottom);
    height: calc(224 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 2000;
      padding: 0 18 * @rem;
      .help-container {
        display: flex;
        justify-content: center;
        padding: 20 * @rem 0;
        border-bottom: 0.5 * @rem solid #c1c1c1;
        .help-btn {
          width: 93 * @rem;
          height: 42 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14 * @rem;
          &:nth-of-type(1) {
            color: #1283ff;
          }
          &:nth-of-type(2) {
            color: #ff3c06;
          }
          img {
            position: absolute;
            width: 93 * @rem;
            height: 42 * @rem;
            z-index: -1;
          }
        }
      }
      .bottom-btn-container {
        padding-bottom: 28 * @rem;
        .bottom-btn-title {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: bold;
          line-height: 20 * @rem;
          margin: 15 * @rem 0;
        }
        .bottom-btn {
          width: 339 * @rem;
          height: 42 * @rem;
          background: @themeColor;
          border-radius: 4 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16 * @rem;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
