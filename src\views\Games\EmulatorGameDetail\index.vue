<template>
  <div class="emulator-page page">
    <nav-bar-3 :placeholder="false" v-if="navBgTransparent">
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-3>
    <nav-bar-3
      :title="detail.main_title"
      :placeholder="false"
      :border="true"
      v-else
    >
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-3>
    <!-- 顶部信息 -->
    <div class="top-container">
      <div class="left-img">
        <img :src="detail.titlepic" alt="" />
      </div>
      <div class="right-info">
        <div class="title-box">
          <span class="title"> {{ detail.main_title }}</span>
        </div>
        <div class="game-version" v-if="!extra_info?.up_info && loadSuccess">
          <div class="version">
            {{ (isIos ? detail.version_i : detail.version) || '1.0.0' }}</div
          >
          <div class="volume">
            <template v-if="isIos && detail.size_i_str">{{
              detail.size_i_str
            }}</template>
            <template v-if="!isIos && detail.size_a">{{
              detail.size_a
            }}</template>
          </div>
        </div>
        <div class="tag-list" v-if="extra_info?.cate_info">
          <div class="tags">
            <div
              class="tag"
              v-for="tag in extra_info?.cate_info?.slice(0, 3)"
              :key="tag.id"
            >
              {{ tag.title }}
            </div>
          </div>
        </div>
        <div class="desc-box" v-if="detail.newstext && !extra_info?.cate_info">
          <span>{{ detail.newstext }}</span>
        </div>
        <div
          class="share-box"
          v-if="extra_info.up_info && Object.keys(extra_info.up_info).length"
        >
          <span class="text2">由</span>
          <span
            class="up-icon"
            @click="toPage('UpMine', { mem_id: extra_info.up_info.up.user_id })"
          >
            <UserAvatar :src="extra_info.up_info.avatar" :self="false" />
          </span>
          <span class="text3">分享</span>
        </div>
      </div>
    </div>
    <div class="activity-bar" v-if="!userInfo.is_svip" @click="toPage('Svip')">
      <div class="left-info">
        <div class="icon">
          <img src="~@/assets/images/games/activity-icon.png" alt="" />
        </div>
        <div class="title"
          >SVIP会员金币奖励翻倍，享金币兑换平台币等18+项特权</div
        >
      </div>
      <div class="right-arrow">
        <span> </span>
      </div>
    </div>
    <div class="h10-bg"> </div>
    <!-- 详情、评价 -->
    <div class="detail-content">
      <!-- tabs -->
      <van-sticky :offset-top="stickyOffsetTop">
        <div class="tabs">
          <div
            class="tab btn"
            v-for="(tab, index) in currentTabList"
            :key="index"
            :class="{
              active: current === tab.status,
              text3_active: tab.title.length === 3,
              text4_active: tab.title.length >= 4,
            }"
            @click="clickTab(tab.status)"
          >
            <span :class="{ active: current === tab.status }"
              >{{ tab.title }}
              <div
                class="comment_count"
                v-if="tab.status == 1 && Number(cmtSum)"
              >
                {{ cmtSum }}
              </div>
              <div
                class="comment_count"
                v-if="tab.status == 2 && Number(goldFingerList.length)"
              >
                {{ Number(goldFingerList.length) }}
              </div>
            </span>
          </div>
          <!-- <div
            class="tab-line"
            :style="{ left: `${current * 93.75 * remNumberLess}rem` }"
          ></div> -->
        </div>
      </van-sticky>
      <content-empty v-if="noGame" :tips="$t('没有该游戏')"></content-empty>
      <template v-else>
        <div class="content-container">
          <div class="tab-content tab-detail" v-if="current == 0">
            <van-loading v-if="!loadSuccess && !defaultPageShow" />
            <default-error-page
              @callback="parentCallback"
              v-else-if="!loadSuccess && defaultPageShow"
            />
            <template v-else>
              <!-- 独家福利 -->
              <template v-if="exclusive_benefits.length">
                <div class="benefits-container section">
                  <div class="section-title">
                    <div class="title-text" @click="platformBenefitsShow = true"
                      >独家福利
                      <img
                        class="section-arrow"
                        src="~@/assets/images/games/section-arrow.png"
                        alt=""
                      />
                    </div>
                  </div>
                  <div class="benefits-list">
                    <div
                      class="benefits-item"
                      @click="clickBenefitItem(item)"
                      v-for="(item, index) in exclusive_benefits"
                      :key="index"
                    >
                      <div class="icon">
                        <img :src="item.icon" alt="" />
                      </div>
                      <div class="center">
                        <div class="title-line">
                          <div class="title">{{ item.title }}</div>
                          <div class="subtitle" v-if="item.subtitle">
                            {{ item.subtitle }}
                          </div>
                        </div>
                        <div class="desc">{{ item.desc }}</div>
                      </div>
                      <div class="right-icon"></div>
                    </div>
                  </div>
                </div>
              </template>
              <!-- 温馨提示 -->
              <div class="welfare-info section" v-if="notice_list.length">
                <div class="section-title">
                  <div class="title-text" @click="welfareNoticeShow = true"
                    >温馨提示
                    <img
                      class="section-arrow"
                      src="~@/assets/images/games/section-arrow.png"
                      alt=""
                  /></div>
                </div>
                <div class="notice-list">
                  <div
                    v-for="(item, index) in notice_list"
                    :key="index"
                    class="notice-item"
                  >
                    <span class="notice-type">{{ item.tag_name }}</span>
                    <span @click="toNoticeDetail(item)" class="notice-title">
                      <span class="text">{{ item.infomation_title }}</span>
                      <i class="notice-icon"></i>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 截图 -->
              <div class="screens-hot section">
                <div class="section-title">
                  <div
                    class="title-text"
                    @click="toPage('GameDetailIntro', { detail: detail })"
                    >游戏介绍
                    <img
                      class="section-arrow"
                      src="~@/assets/images/games/section-arrow.png"
                      alt=""
                  /></div>
                </div>
                <div
                  class="game-picture-swiper"
                  v-if="detail.morepic && detail.morepic.small.length"
                >
                  <swiper :options="swiperOption">
                    <swiper-slide
                      v-for="(item, index) in detail.morepic
                        ? detail.morepic.small
                        : []"
                      :key="index"
                      class="swiper-slide"
                      :class="{ is_hp: isHp }"
                    >
                      <div v-if="!item" class="default_loading">
                        <div class="loading">
                          <img
                            src="~@/assets/images/games/default_loading_icon.png"
                            alt=""
                          />
                        </div>
                        <div class="text">正在加载...</div>
                      </div>
                      <img
                        v-else
                        :src="item"
                        class="slide-img btn"
                        @click="showBigImage(detail.morepic.big, index)"
                      />
                      <i class="player" v-if="index === 1"></i>
                    </swiper-slide>
                  </swiper>
                </div>
              </div>

              <div class="category-tags" v-if="extra_info?.cate_info?.length">
                <div
                  class="category-tag-item"
                  v-for="(item, itemIndex) in extra_info?.cate_info"
                  :key="itemIndex"
                  @click="handleCategoryTag(item)"
                >
                  {{ item.title }}
                </div>
              </div>
              <!-- 游戏介绍 -->
              <!-- <div class="game-introduction-text" v-if="detail.newstext">
                <div class="introduction-text">
                  {{ detail.newstext }}
                </div>
                <div
                  class="introduction-icon"
                  v-if="!isExpanded"
                  @click="toggleIntroduction"
                >
                  <img
                    src="@/assets/images/games/introduction-icon.png"
                    alt=""
                  />
                </div>
              </div> -->
              <div class="introduction">
                <div
                  v-html="detail.newstext"
                  class="introduction-text"
                  ref="content2"
                  :class="{ on: !isAll2 }"
                ></div>
                <div
                  class="more-text"
                  @click="isAll2 = true"
                  v-if="contentHeight2 > 60 && !isAll2"
                >
                  <div> {{ !isAll2 ? '&nbsp;&nbsp;...' : '' }}</div>
                  <span>{{ !isAll2 ? $t('展开') : '' }}</span>
                </div>
              </div>
              <!-- 温馨提示 -->
              <div class="reminder-text section" v-if="detail.features">
                <div class="section-title">
                  <div class="title-text">温馨提示 </div>
                </div>
                <div class="content">
                  <div
                    class="content-text"
                    ref="content1"
                    :class="{ on: !isAll1 }"
                    v-html="detail.features"
                  ></div>
                  <div
                    class="more-text"
                    @click="moreText"
                    v-if="contentHeight1 > 100 && !isAll1"
                  >
                    <div> {{ !isAll1 ? '&nbsp;...' : '' }}</div>
                    <span>{{ !isAll1 ? $t('展开') : '' }}</span>
                  </div>
                </div>
              </div>
              <!-- 游戏评价 -->
              <div class="hot-comments section" v-if="topCommentList?.length">
                <div class="section-title">
                  <div class="title-text" @click="clickTab(1)"
                    >{{ $t('游戏评价') }}
                    <span class="number" v-if="cmtSum">({{ cmtSum }})</span>
                    <img
                      class="section-arrow"
                      src="~@/assets/images/games/section-arrow.png"
                      alt=""
                    />
                  </div>
                  <div class="comments-btn" @click="clickComment">
                    <span class="icon"> </span>
                    <span class="text">去评价</span>
                  </div>
                </div>
                <div class="hot-comment-list">
                  <comment-item-2
                    class="item"
                    :comment="item"
                    :showScoreTime="true"
                    v-for="(item, index) in topCommentList.slice(0, 3)"
                    :key="index"
                  ></comment-item-2>
                </div>
                <div
                  class="comment-more"
                  v-if="shouldShow"
                  @click="current = 1"
                >
                  <div class="comment-more-text">{{
                    $t('查看全部游戏评价')
                  }}</div>
                  <i></i>
                </div>
              </div>
              <!-- 详细信息 -->
              <div class="section version-section">
                <div class="section-title">
                  <div class="title-text">详细信息 </div>
                  <div
                    class="feed-back"
                    @click="
                      toPage('NewOpinion', {
                        title: detail.main_title,
                        game_id: detail.id,
                      })
                    "
                  >
                    <span class="text">反馈</span>
                    <span class="icon"> </span>
                  </div>
                </div>
                <div class="version-container">
                  <template v-for="item in extra_info.information">
                    <div
                      class="content-item"
                      v-if="item.text || item.url"
                      :key="item.key"
                    >
                      <div class="label">{{ item.title }}</div>
                      <div class="value-box" v-if="item.text">
                        <div class="value1">{{ item.text }}</div>
                      </div>
                      <div class="value-box" v-if="item.url">
                        <template v-for="(val, index) in item.url">
                          <div
                            :key="index"
                            class="value2"
                            @click="handleUrl(val)"
                          >
                            {{ val.text }}
                          </div>
                        </template>
                      </div>
                    </div>
                  </template>

                  <div
                    class="see-more-info"
                    @click="
                      toPage('GameDetailIntro', {
                        detail: detail,
                        information: extra_info.information,
                      })
                    "
                  >
                    <div>查看全部信息</div>
                    <span> </span>
                  </div>
                </div>
              </div>
              <!-- 更多游戏推荐 -->
              <div class="related" v-if="formatRelatedList.list.length">
                <div class="section-title">
                  <div class="title-text">{{ formatRelatedList.title }}</div>
                </div>
                <div class="game-list">
                  <div
                    class="game-group"
                    v-for="(related, relatedIndex) in formatRelatedList.list"
                    :key="relatedIndex"
                  >
                    <div
                      class="game-item"
                      v-for="(item, index) in related"
                      :key="index"
                    >
                      <!-- <simulator-zone-tabs-item
                        :info="item"
                      ></simulator-zone-tabs-item> -->
                      <game-item-4
                        :gameInfo="item"
                        :iconSize="68"
                      ></game-item-4>
                    </div>
                  </div>
                </div>
                <div class="h28-bg"> </div>
              </div>
            </template>
          </div>
          <!-- <template v-if="current == 1">
            <evaluate-news :game_id="detail.id"></evaluate-news>
          </template> -->
          <div class="tab-content tab-comment" v-if="current === 1">
            <comment-tab></comment-tab>
          </div>
          <div class="tab-content gold-finger-content" v-if="current === 2">
            <div class="gold-finger-list">
              <div
                class="gold-finger-item"
                v-for="(item, index) in goldFingerList"
                :key="index"
                @click="toGoldFinger"
              >
                <div class="icon">
                  <img
                    src="~@/assets/images/games/gold_finger_icon.png"
                    alt=""
                  />
                </div>
                <div class="info">
                  {{ item.title }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <div class="fixed-review-box" v-if="current == 1 && !noGame">
      <div class="fixed-review-btn" @click="clickComment()">
        <img src="~@/assets/images/games/fixed-review-btn.png" alt="" />
      </div>
    </div>
    <!-- 底部fixed -->
    <div class="bottom-container" v-if="loadSuccess">
      <div class="bottom-fixed">
        <div class="comment btn" @click="clickComment">
          <div class="comment-icon"></div>
          <div class="comment-title">{{ $t('评价') }}</div>
        </div>
        <div class="download-bar">
          <div class="download-content">
            <div class="btn download-btn" @click="playDirectly(detail)">
              <span class="icon" v-if="!simulatorInitLoading[detail.id]">
                <img
                  src="~@/assets/images/games/download-content-icon.png"
                  alt=""
                />
              </span>
              <div
                class="text"
                :class="{ loading: simulatorInitLoading[detail.id] }"
                >直接玩</div
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 详细信息弹窗 -->
    <van-popup
      v-model="detailedInfoShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="detailedInfo-popup">
        <div class="permission-top">
          <div class="title">详细信息</div>
          <div class="close" @click="detailedInfoShow = false"></div>
        </div>

        <div class="permission-list">
          <template v-for="item in detail?.simulator_info?.info_internal">
            <div
              class="permission-item"
              :key="item.key"
              v-if="
                (item.key === 'icp_number' && detail.record_number) ||
                (item.key === 'source' && detail.company_name) ||
                item.val.length > 0 ||
                item.key == 'privacy'
              "
            >
              <div class="name">{{ item.title }}</div>
              <div class="value-box" v-if="item.key !== 'privacy'">
                <div class="value1" v-if="item.val">{{ item.val }}</div>
                <div class="value1" v-if="item.key === 'icp_number'">{{
                  detail.record_number
                }}</div>
                <div class="value1" v-if="item.key === 'source'">{{
                  detail.company_name
                }}</div>
              </div>
              <div class="value-box" v-else>
                <div class="value2" @click="handleApplicationPermissions"
                  >应用权限</div
                >
                <div class="value2" @click="handlePrivacyPermission"
                  >隐私权限</div
                >
              </div>
            </div>
          </template>
        </div>
      </div>
    </van-popup>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="permission-top">
          <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
          <div class="close" @click="permissionShow = false"></div>
        </div>

        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 温馨提示 -->
    <welfare-notice-popup
      title="温馨提示"
      :bgType="1"
      :show.sync="welfareNoticeShow"
      :notice_list="notice_list"
    ></welfare-notice-popup>

    <!-- 平台福利 -->
    <platform-benefits-popup
      :show.sync="platformBenefitsShow"
      :exclusive_benefits="exclusive_benefits"
      :features="detail.features"
    ></platform-benefits-popup>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { ImagePreview } from 'vant';
import {
  ApiGameDetail,
  ApiGameDetailRecommendGame,
  ApiGameGetPermissionInfo,
  ApiResourceCollect,
  ApiResourceCollectStatus,
} from '@/api/views/game.js';
import { ApiCommentClickComment } from '@/api/views/comment.js';
import TextOverflow from '@/components/text-overflow';
import EmulatorGame from './components/emulator-game/index.vue';
import EvaluateNews from './components/evaluate-news/index.vue';
import CommentTab from '../GameDetail2/components/comment-tab';
import CommentItem2 from '@/components/comment-item-2';
import WelfareNoticePopup from '@/components/welfare-notice-popup';
import PlatformBenefitsPopup from '@/components/platform-benefits-popup';
import SimulatorZoneTabsItem from '../../SimulatorZone/components/simulator-zone-tabs-item/index.vue';
import { mapActions, mapMutations, mapGetters } from 'vuex';
import { jumpAnnouncement } from '@/utils/function';
import { isIos } from '@/utils/userAgent';
import { PageName, handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'EmulatorGameDetail',
  components: {
    TextOverflow,
    EmulatorGame,
    EvaluateNews,
    CommentItem2,
    CommentTab,
    WelfareNoticePopup,
    SimulatorZoneTabsItem,
    PlatformBenefitsPopup,
  },
  data() {
    return {
      stickyOffsetTop: '0px', //顶部导航栏的高度
      navBgTransparent: true,
      collected: 0, //是否已收藏
      detail: {}, // 游戏详情信息
      extra_info: {}, // 游戏详情额外信息
      notice_list: [], //福利公告
      topCommentList: [], //热门评论
      welfareNoticeShow: false, //福利公告弹窗
      platformBenefitsShow: false, //平台福利弹窗
      isIos, //是ios
      current: 0, //tabs选中
      remNumberLess, //rem大小转换
      tabList: [
        {
          title: '详情',
          status: 0,
        },
        {
          title: '评价',
          status: 1,
        },
      ],
      loadSuccess: false, //加载完毕
      defaultPageShow: false, //网络错误占位符
      permissionList: [],
      detailedInfoShow: false, //游戏获取权限列表展示
      permissionShow: false,
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
      },
      isPlaying: false, //是否正在播放
      isExpanded: false,
      relatedList: [], // 相关游戏
      noGame: false, // 是否没有该游戏
      rows: 3,
      btnText: '展开',
      ellipsisText: '...',
      ellipsis: true,
      isAll1: true, // 展开 -> 是否显示全部
      isAll2: true, // 展开 -> 游戏介绍
      contentHeight1: 0, //展开收起内容高度1
      contentHeight2: 0, //展开收起内容高度2
      isHp: false, // 截图是否是横屏
      cmtSum: '', // 评论数量
      goldFingerList: [], //金手指列表
      exclusive_benefits: [], //独家福利
    };
  },
  async created() {
    this.detail = this.$route.params.id || {};
  },
  async mounted() {
    this.setSimulatorInitLoadingEmpty({});
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.detailedInfoShow = false;
    await this.getDetail();
    if (!this.noGame) {
      await this.getRecommendGame();
      await this.getCollectStatus();
    }
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    clickBenefitItem(item) {
      switch (item.action_code) {
        case 13: // web内页
          handleActionCode(item);
          break;
        case 23: // 游戏签到
          this.toPage('GameSignInDetail', { id: this.detail.id });
          break;
        case 21: // 开局号
          this.toPage('OpeningAccount', { id: this.detail.id });
          break;
      }
    },
    // 获取推荐游戏
    async getRecommendGame() {
      const res = await ApiGameDetailRecommendGame({
        id: this.detail.id,
      });
      this.relatedList = res.data.recommend_game;
    },
    toNoticeDetail(item) {
      jumpAnnouncement(item);
    },
    handleCategoryTag(item) {
      this.toPage('CateGameList', {
        cate_id: item.id,
        title: item.title,
      });
    },
    async parentCallback() {
      this.loadSuccess = false;
      this.defaultPageShow = false;
      this.detailedInfoShow = false;
      await this.getDetail();
      if (!this.noGame) {
        await this.getCollectStatus();
      }
    },
    getImageSize(filePath) {
      return new Promise((resolve, reject) => {
        try {
          let image = new Image();
          image.onload = function () {
            resolve({ width: this.width, height: this.height });
            this.removeAttribute('src');
            image = null;
          };
          image.onerror = error => {
            resolve({ width: this.width, height: this.height });
            image = null;
          };
          image.src = `${filePath}`;
        } catch (error) {
          resolve({ width: this.width, height: this.height });
        }
      });
    },
    // 直接玩
    async playDirectly(item) {
      if (
        this.simulatorInitLoading[item.id] ||
        Object.values(this.simulatorInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_SIMULATOR_GAME(item);
    },
    // 更多
    hideMore(ellipsis) {
      this.ellipsis = ellipsis;
    },
    toggleIntroduction() {
      this.isExpanded = !this.isExpanded;
      const gameIntroductionText = document.querySelector(
        '.game-introduction-text',
      );
      if (this.isExpanded) {
        gameIntroductionText.classList.add('expanded');
      } else {
        gameIntroductionText.classList.remove('expanded');
      }
    },
    // 获取收藏状态
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 3,
        sourceId: this.detail.id,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    // 设置收藏
    setCollectStatus() {
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;
      }
      ApiResourceCollect({
        classId: 3,
        sourceId: this.detail.id,
        status: status,
      }).then(res => {
        this.collected = res.data.status;
      });
    },
    // 获取详情页信息
    async getDetail() {
      try {
        const res = await ApiGameDetail({
          id: this.$route.params.id,
        });
        this.noGame = false;
        this.defaultPageShow = false;
        this.detail = res.data.detail;
        this.extra_info = res.data.extra_info;
        this.notice_list = res.data.extra_info.fuli_notice || [];
        this.topCommentList = res.data.extra_info.top_comment || [];
        this.goldFingerList = res.data.extra_info.user_cheats || [];
        this.exclusive_benefits = res.data.extra_info.exclusive_benefits || [];
        if (this.goldFingerList.length > 0) {
          this.tabList.push({
            title: '金手指',
            status: 2,
          });
        }
        this.cmtSum = res.data.extra_info.comment_count || '';
        const { width, height } = await this.getImageSize(
          res.data.detail.morepic.big[0],
        );
        // 截图是否横屏
        this.isHp =
          width === undefined || height === undefined || width >= height;
        this.loadSuccess = true;
      } catch (e) {
        if (!navigator.onLine) {
          this.loadSuccess = false;
          this.defaultPageShow = true;
        }
        if (e.code == 0) {
          this.$router.replace({
            name: 'GameNotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
          this.noGame = true;
        }
      } finally {
        this.$nextTick(() => {
          this.$toast.clear();
          if (!this.contentHeight1 && this.$refs.content1) {
            this.contentHeight1 = this.$refs.content1.clientHeight;
          }
          if (!this.contentHeight2 && this.$refs.content2) {
            this.contentHeight2 = this.$refs.content2.clientHeight;
          }
          if (this.contentHeight1 > 100) {
            this.isAll1 = false;
          }
          if (this.contentHeight2 > 60) {
            this.isAll2 = false;
          }
        });
      }
    },
    clickTab(index) {
      if (!this.loadSuccess || this.current === index) return false;
      window.scrollTo(0, 0);
      this.current = index;
    },
    // 处理tab底下高亮标记位移
    handleLeftDistance(current) {
      let width = document.body.clientWidth;
      let length = this.currentTabList.length;
      let left = width / length / 2 - 6;
      let distance = current * (width / length);
      return `${left + distance}px`;
    },
    moreText() {
      this.isAll1 = true;
    },
    // 点击评论时先判断有没有评论的权限
    async clickComment() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      if (![1, 2].includes(parseInt(this.userInfo.auth_status))) {
        this.$toast.clear();
        this.$dialog({
          title: '提示',
          message: '评论失败：您还未完善实名认证信息！',
          confirmButtonText: '我知道了',
        });
        return;
      }
      try {
        const res = await ApiCommentClickComment({
          classId: 103,
          sourceId: this.detail.id,
        });
        if (res.data && res.data.length) {
          this.setCommentTypeList(res.data);
        }
        this.$toast.clear();
        this.toPage('CommentEditor', {
          source_id: this.detail.id,
          class_id: 103,
        });
      } catch (e) {
        if (e.code == 0) {
          // 不能评论，弹窗提示
          this.$toast.clear();
          this.$dialog({
            title: '提示',
            message: e.msg,
            confirmButtonText: '我知道了',
          });
        }
      }
    },
    // updateCommentSum(sum) {
    //   this.cmtSum = sum;
    // },
    handlePlay() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
    handlePause() {
      this.$refs.videoPlayer.pause();
      this.isPlaying = false;
    },
    // 查看全部评价
    viewAllGameReviews() {
      window.scrollTo(0, 0);
      this.current = 1;
    },
    // 处理权限隐私说明
    handleUrl(val) {
      if (val.url && val.text == '隐私政策') {
        this.toPage('Iframe', {
          url: val.url,
          title: '隐私政策',
        });
      } else if (val.text == '应用权限') {
        this.handlePermission();
      } else {
        this.$toast('开发者正在努力完善中...');
      }
    },
    // 处理游戏权限详情
    async handlePermission() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    toGoldFinger() {
      this.$toast('请进入游戏使用金手指');
    },
    ...mapActions({
      OPEN_SIMULATOR_GAME: 'game/OPEN_SIMULATOR_GAME',
    }),
    ...mapMutations({
      setSimulatorInitLoadingEmpty: 'game/setSimulatorInitLoadingEmpty',
      setCommentTypeList: 'comment/setCommentTypeList',
    }),
  },
  computed: {
    currentTabList() {
      let tabList = [];
      tabList = [...this.tabList];

      // 把评价选项移到最后
      const commentTabIndex = tabList.findIndex(tab => tab.status === 1);
      if (commentTabIndex > -1) {
        const commentTab = tabList.splice(commentTabIndex, 1)[0];
        tabList.push(commentTab);
      }
      return tabList;
    },
    // 相关游戏
    formatRelatedList() {
      let title = this.relatedList?.title;
      let arr = [];
      for (let i = 0; i < this.relatedList?.list?.length; i += 3) {
        arr.push(this.relatedList?.list?.slice(i, i + 3));
      }
      return { title, list: arr };
    },
    shouldShow() {
      const num = Number(this?.cmtSum);
      return !Number.isInteger(num) || num > 3;
    },
    ...mapGetters({
      simulatorInitLoading: 'game/simulatorInitLoading',
    }),
  },
};
</script>

<style lang="less" scoped>
/deep/.van-sticky--fixed {
  left: 0;
  right: 0;
  margin: 0 auto;
  max-width: 450px;
  width: 100%;
}
.emulator-page {
  background: #ffffff;
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
  .collect-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    padding: 10 * @rem;
    background: url(~@/assets/images/games/collect-black.png) center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    &.collect-btn-white {
      background: url(~@/assets/images/games/collect.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.had {
      background-image: url(~@/assets/images/games/collect-success.png);
      background-size: 28 * @rem 28 * @rem;
    }
  }
  .top-container {
    margin-top: 50 * @rem;
    height: 116 * @rem;
    min-height: 116 * @rem;
    padding: 14 * @rem 12 * @rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-img {
      width: 88 * @rem;
      height: 88 * @rem;
      border-radius: 12 * @rem;
      background-color: #eeeeee;
      overflow: hidden;
      flex-shrink: 0;
      min-width: 0;
    }
    .right-info {
      flex: 1;
      min-width: 0;
      height: 88 * @rem;
      margin-left: 12 * @rem;
      .title-box {
        width: 232 * @rem;
        height: 20 * @rem;
        display: flex;
        align-items: center;
        .title {
          width: 232 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #111111;
          line-height: 20 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .sub-title {
          margin-left: 16 * @rem;
          width: 61 * @rem;
          white-space: nowrap;
          border-radius: 4 * @rem;
          border: 1px solid rgba(0, 0, 0, 0.23);
          font-weight: 400;
          font-size: 10 * @rem;
          color: #979797;
          padding: 2 * @rem 4 * @rem;
        }
      }
      .tag-list {
        display: flex;
        align-items: center;

        .tags {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 8 * @rem;
          margin-bottom: 11 * @rem;
          height: 22 * @rem;
          line-height: 22 * @rem;
          .tag {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            white-space: nowrap;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4 * @rem;
            padding: 2 * @rem 4 * @rem;
            color: #868686;
            height: 18 * @rem;
            line-height: 18 * @rem;
            &:not(:first-child) {
              margin-left: 8 * @rem;
            }
          }
        }
      }
      .desc-box {
        padding: 12 * @rem 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 239 * @rem;
        height: 15 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #9a9a9a;
        line-height: 15 * @rem;
      }
      .share-box {
        margin-top: 12 * @rem;
        display: flex;
        align-items: center;
        .text2,
        .text3 {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #121212;
        }
        .text2 {
          padding: 0 5 * @rem 0 0;
        }
        .text3 {
          padding: 0 0 0 5 * @rem;
        }
        .up-icon {
          width: 16 * @rem;
          height: 16 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
      }
      .game-version {
        margin: 8 * @rem 0;
        display: flex;
        align-items: center;
        .version {
          max-width: 100 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .version,
        .volume {
          font-weight: 400;
          font-size: 11 * @rem;
          color: #93999f;
          white-space: nowrap;
        }
        .volume {
          margin-left: 14 * @rem;
        }
      }
    }
  }
  .activity-bar {
    width: 351 * @rem;
    height: 36 * @rem;
    border-radius: 8 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 11 * @rem 0 8 * @rem;
    margin: -2 * @rem auto 14 * @rem;
    box-sizing: border-box;
    background: url('~@/assets/images/games/activity-bg.png') #fff5db no-repeat
      top center;
    background-size: 351 * @rem 36 * @rem;
    .left-info {
      display: flex;
      align-items: center;
      .icon {
        width: 28 * @rem;
        height: 28 * @rem;
      }
      .title {
        margin-left: 6 * @rem;
        white-space: nowrap;
        font-weight: 500;
        font-size: 11 * @rem;
        color: #6c4537;
      }
    }
    .right-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        width: 8 * @rem;
        height: 12 * @rem;
        background: url('~@/assets/images/games/activity-btn.png') no-repeat
          center center;
        background-size: 8 * @rem 12 * @rem;
      }
    }
  }
  .h10-bg {
    min-height: 10 * @rem;
    height: 10 * @rem;
    width: 100%;
    background: #f9f9f9;
  }
  .h28-bg {
    min-height: 28 * @rem;
    height: 28 * @rem;
    width: 100%;
    background: #f9f9f9;
  }
  .detail-content {
    flex: 1;
    .tabs {
      display: flex;
      position: relative;
      background-color: #fff;
      align-items: center;
      width: 100%;
      .tab {
        width: 93.75 * @rem;
        height: 38 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          display: block;
          font-size: 16 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          font-weight: 400;
          color: #999999;
          position: relative;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          .comment_count {
            position: absolute;
            right: -12 * @rem;
            flex-shrink: 0;
            width: 10 * @rem;
            height: 18 * @rem;
            line-height: 18 * @rem;
            font-weight: 400;
            font-size: 10 * @rem;
            color: #60666c;
          }
        }
        .notice {
          position: absolute;
          left: 50%;
          top: 2 * @rem;
          transform: translateX(12 * @rem);
          height: 14 * @rem;
          line-height: 14 * @rem;
          background: #ff504f;
          color: #ffffff;
          font-size: 10 * @rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 4 * @rem;
          border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
        }
        &.active {
          font-size: 16 * @rem;
          background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg.png)
            no-repeat center center;
          background-size: 48 * @rem 18 * @rem;
          &.text3_active {
            background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg1.png)
              no-repeat center center;
            background-size: 58 * @rem 18 * @rem;
          }
          &.text4_active {
            background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg2.png)
              no-repeat center center;
            background-size: 64 * @rem 18 * @rem;
          }
          span {
            font-size: 16 * @rem;
            text-align: center;
            font-weight: bold;
            color: #191b1f;
            &::before {
              content: '';
              display: block;
              width: 12 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/games/tabs-active-icon/tabs-active-dot.png)
                no-repeat;
              background-size: 12 * @rem 12 * @rem;
              position: absolute;
              top: -4 * @rem;
              right: -12 * @rem;
            }
          }
        }
      }
      .line {
        flex: 1;
        height: 0 * @rem;
        border: 1 * @rem solid #e8e8e8;
      }
      .tab-line {
        position: absolute;
        width: 10 * @rem;
        height: 4 * @rem;
        border-radius: 10 * @rem;
        background-color: @themeColor;
        left: 0;
        bottom: 10 * @rem;
        transform: translateX(39.875 * @rem);
        transition: 0.3s;
      }
      .active {
        font-size: 16 * @rem!important;
        font-weight: 600 !important;
        color: #111111 !important;
      }
    }
    .content-container {
      height: 100%;
      .tab-content {
        /deep/ .van-loading {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &.tab-detail {
          .benefits-container {
            .benefits-title {
              height: 20 * @rem;
              line-height: 20 * @rem;
              // background: url(~@/assets/images/games/dujia-title-icon.png) left
              //   center no-repeat;
              // background-size: 18 * @rem 18 * @rem;
              // padding-left: 24 * @rem;
              font-size: 16 * @rem;
              color: #191b1f;
              font-weight: 600;
              display: flex;
              align-items: center;
            }
            .benefits-list {
              border-radius: 8 * @rem;
              // background-color: #f9f9f9;
              // padding: 14 * @rem 10 * @rem;
              .benefits-item {
                display: flex;
                align-items: center;
                height: 54 * @rem;
                position: relative;
                &:not(:first-of-type) {
                  margin-top: 12 * @rem;
                }
                .icon {
                  width: 54 * @rem;
                  height: 54 * @rem;
                  border-radius: 12 * @rem;
                  overflow: hidden;
                }
                .center {
                  flex: 1;
                  min-width: 0;
                  margin-left: 8 * @rem;
                  .title-line {
                    display: flex;
                    align-items: center;
                    .title {
                      font-size: 14 * @rem;
                      color: #303236;
                      font-weight: 600;
                      line-height: 18 * @rem;
                      flex-shrink: 0;
                    }
                    .subtitle {
                      box-sizing: border-box;
                      border: 0.5 * @rem solid #ff7171;
                      border-radius: 4 * @rem;
                      margin-left: 4 * @rem;
                      height: 16 * @rem;
                      line-height: 16 * @rem;
                      padding: 0 2 * @rem;
                      background-color: #ffecec;
                      font-size: 11 * @rem;
                      color: #ff5050;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                    }
                  }
                  .desc {
                    font-size: 12 * @rem;
                    color: #93999f;
                    line-height: 14 * @rem;
                    margin-top: 8 * @rem;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                }
                .right-icon {
                  width: 9 * @rem;
                  height: 12 * @rem;
                  background: url(~@/assets/images/games/benefits-arrow.png)
                    left center no-repeat;
                  background-size: 9 * @rem 12 * @rem;
                }
              }
            }
          }
          .welfare-info {
            padding: 16 * @rem 12 * @rem 0;
            .section-title {
              margin-bottom: 8 * @rem;
            }
            .notice-list {
              // margin: 0 18 * @rem 18 * @rem;
              // background: #f9f9f9;
              // border-radius: 6 * @rem;
              .notice-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 32 * @rem;
                .notice-type {
                  margin-right: 7 * @rem;
                  font-size: 10 * @rem;
                  font-weight: 400;
                  background: linear-gradient(90deg, #faf2eb 0%, #ffffff 100%);
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid #191b1f;
                  color: #191b1f;
                  padding: 3 * @rem 5 * @rem;
                  box-sizing: border-box;
                  flex-shrink: 0;
                }
                .notice-title {
                  display: flex;
                  justify-content: space-between;
                  flex: 1;
                  color: #303236;
                  font-size: 13 * @rem;
                  .text {
                    width: 284 * @rem;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  .notice-icon {
                    display: block;
                    width: 14 * @rem;
                    height: 14 * @rem;
                    .image-bg('~@/assets/images/more-right-arrow.png');
                  }
                }
              }
            }
          }
          .screens-hot {
            padding: 16 * @rem 12 * @rem 0;
            .game-picture-swiper {
              box-sizing: border-box;
              width: 100%;
              overflow: hidden;
              .swiper-container {
                box-sizing: border-box;
                width: 100%;
                .swiper-slide {
                  width: auto;
                  height: 219 * @rem;
                  margin-left: 10 * @rem;
                  border-radius: 8 * @rem;
                  overflow: hidden;
                  min-width: 124 * @rem;
                  &:first-child {
                    margin-left: 0;
                  }
                  .slide-img {
                    width: auto;
                    height: 100%;
                    border-radius: 8 * @rem;
                    overflow: hidden;
                    background: #ccc;
                  }
                  &.video-container {
                    width: 267 * @rem;
                    height: 150 * @rem;
                    overflow: hidden;
                    #video {
                      display: block;
                      width: 267 * @rem;
                      height: 150 * @rem;
                      outline: none;
                      border: none;
                    }
                    .mask {
                      width: 100%;
                      height: 150 * @rem;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      position: absolute;
                      left: 0;
                      top: 0;
                      .fixed-center;
                      background-color: rgba(0, 0, 0, 0);
                      z-index: 10;
                      .play-btn {
                        width: 38 * @rem;
                        height: 38 * @rem;
                        background: url(~@/assets/images/video-play.png)
                          no-repeat;
                        background-size: 38 * @rem 38 * @rem;
                      }
                    }
                  }
                  &.is_hp {
                    height: 160 * @rem;
                  }
                  .default_loading {
                    background: #4f4f4f;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    .loading {
                      width: 28 * @rem;
                      height: 28 * @rem;
                      animation: loading_rotate 0.8s linear infinite;
                    }
                    .text {
                      margin-top: 24 * @rem;
                      font-weight: 400;
                      font-size: 11 * @rem;
                      color: #ffffff;
                    }
                  }
                }
              }
            }
          }
        }
        .category-tags {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          overflow: hidden;
          margin: 12 * @rem 12 * @rem 0 12 * @rem;
          height: 22 * @rem;
          .category-tag-item {
            flex-wrap: nowrap;
            white-space: nowrap;
            height: 22 * @rem;
            margin-right: 10 * @rem;
            font-size: 10 * @rem;
            color: #93999f;
            background: #f7f8fa;
            font-weight: 400;
            padding: 0 6 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4 * @rem;
            box-sizing: border-box;
          }
        }
        .game-introduction-text {
          display: flex;
          align-items: center;
          margin: 12 * @rem 18 * @rem 16 * @rem;
          .introduction-text {
            width: 322 * @rem;
            color: #666666;
            overflow: hidden;
            font-weight: 400;
            // white-space: pre-wrap;
            // transition: all 0.3s ease;
            flex: 1;
            font-size: 14px;
            height: 20px;
            line-height: 24px;
            // display: -webkit-box;
            max-height: unset;
            transition: 0.3s;
          }
          .introduction-icon {
            margin-top: 4 * @rem;
            margin-left: 9 * @rem;
            width: 12 * @rem;
            height: 11 * @rem;
          }
          &.expanded .introduction-text {
            overflow: visible;
            height: auto;
          }

          &.expanded .introduction-icon {
            opacity: 0;
          }
        }
        .introduction {
          width: 100%;
          padding: 0 12 * @rem;
          box-sizing: border-box;
          position: relative;
          .introduction-text {
            font-size: 14px;
            color: #797979;
            line-height: 20px;
            display: -webkit-box;
            margin-top: 10 * @rem;
            max-height: unset;
            transition: 0.3s;
            &.on {
              max-height: 60px;
              height: 60px;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              overflow: hidden;
            }
          }
          .more-text {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 42 * @rem;
            position: absolute;
            right: 10 * @rem;
            bottom: 0;
            background: #fff;
            span {
              font-size: 12 * @rem;
              color: #32b768;
              height: 17 * @rem;
              line-height: 17 * @rem;
            }
          }
        }

        .reminder-text {
          padding: 20 * @rem 12 * @rem 0;
          .title {
            white-space: nowrap;
            font-weight: 500;
            font-size: 15 * @rem;
            color: #222222;
            color: #000000;
            font-weight: 600;
            flex: 1;
            min-width: 0;
          }
          .content {
            margin-top: 12 * @rem;
            background: #f7f8fa;
            border-radius: 8 * @rem;
            padding: 12 * @rem 14 * @rem 14 * @rem 12 * @rem;
            width: 100%;
            box-sizing: border-box;
            position: relative;
            .content-text {
              font-size: 14px;
              color: #9a9a9a;
              line-height: 20px;
              display: -webkit-box;
              max-height: unset;
              height: auto;
              transition: 0.3s;
              display: flex;
              flex-wrap: wrap;
              &.on {
                max-height: 100px;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 5;
                overflow: hidden;
              }
            }
            .more-text {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 42 * @rem;
              position: absolute;
              right: 10 * @rem;
              bottom: 17 * @rem;
              background: #fff;
              span {
                font-size: 12 * @rem;
                color: #32b768;
                height: 17 * @rem;
                line-height: 17 * @rem;
              }
            }
            .ellipsis-content {
              font-weight: 400;
              font-size: 13px;
              color: #9a9a9a;
              position: relative;
              line-height: 1.5;
              text-align: justify;
              word-spacing: 2px;
              .btn {
                color: #32b768;
              }
            }
          }
        }
        .hot-comments {
          .section-title {
            .comments-btn {
              padding: 0 10 * @rem;
              box-sizing: border-box;
              height: 28 * @rem;
              border-radius: 26 * @rem;
              border: 1 * @rem solid #e3e5e8;
              flex-shrink: 0;
              display: flex;
              align-items: center;
              .icon {
                background: url(~@/assets/images/games/comments-btn-icon.png)
                  no-repeat top center;
                background-size: 12 * @rem 12 * @rem;
                width: 12 * @rem;
                height: 12 * @rem;
              }
              .text {
                margin-left: 4 * @rem;
                height: 15 * @rem;
                font-weight: 500;
                font-size: 12 * @rem;
                color: #93999f;
              }
            }
          }
          .hot-comment-list {
            .item {
              margin-top: 16 * @rem;
              &:not(:last-child) {
                padding-bottom: 16 * @rem;
                border-bottom: 1px solid #f9f9f9;
              }
            }
            width: 100%;
            margin-bottom: 32 * @rem;
          }
          .comment-more {
            width: 200 * @rem;
            height: 40 * @rem;
            margin: 0 auto;
            border: 1px solid #e3e5e8;
            border-radius: 80 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            .comment-more-text {
              font-size: 14 * @rem;
              color: #1cce94;
              font-weight: 400;
            }
            i {
              display: block;
              width: 8 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/games/welfare-right-icon.png)
                center center no-repeat;
              background-size: 8 * @rem 12 * @rem;
              margin-left: 3 * @rem;
            }
          }
        }
        .version-section {
          .section-title {
            .feed-back {
              padding: 0 8 * @rem 0 13 * @rem;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              height: 28 * @rem;
              border-radius: 26 * @rem;
              border: 1 * @rem solid #e3e5e8;
              .text {
                font-weight: 500;
                font-size: 12px;
                color: #93999f;
              }
              .icon {
                margin-left: 3 * @rem;
                background: url('~@/assets/images/games/version-arrow.png')
                  no-repeat top center;
                width: 8 * @rem;
                height: 8 * @rem;
                background-size: 8 * @rem 8 * @rem;
              }
            }
          }
          .version-container {
            margin: 12 * @rem 0;
            width: 351 * @rem;
            background: #f7f8fa;
            border-radius: 8 * @rem;
            box-sizing: border-box;
            padding: 14 * @rem 10 * @rem 13 * @rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            .content-item {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              .label {
                font-weight: 400;
                font-size: 14 * @rem;
                color: #777777;
                white-space: nowrap;
              }
              .value-box {
                width: 215 * @rem;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                .value1 {
                  font-weight: 400;
                  font-size: 14 * @rem;
                  color: #333333;
                }
                .value2 {
                  font-weight: 400;
                  font-size: 14 * @rem;
                  color: #32b768;
                  line-height: 14 * @rem;
                  // text-decoration: underline;
                  display: inline;
                  border-bottom: solid 1px #32b768;
                  &:not(:first-child) {
                    margin-left: 9 * @rem;
                  }
                }
              }
              &:not(:first-child) {
                margin-top: 13 * @rem;
              }
            }
            .see-more-info {
              display: flex;
              align-items: center;
              margin-top: 23 * @rem;
              > div {
                font-weight: 400;
                font-size: 14 * @rem;
                color: #60666c;
              }
              > span {
                width: 8 * @rem;
                height: 12 * @rem;
                margin-left: 3 * @rem;
                background: url(~@/assets/images/games/arrow-right-grey1.png)
                  right center no-repeat;
                background-size: 8 * @rem 12 * @rem;
              }
            }
          }
        }
        .related {
          margin-top: 16 * @rem;
          padding: 0;
          .section-title {
            padding-left: 12 * @rem;
          }
          .game-list {
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            margin-top: 12 * @rem;
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            padding-left: 12 * @rem;
            &::-webkit-scrollbar {
              display: none !important;
            }
            .game-group {
              &:not(:first-of-type) {
                margin-left: 10 * @rem;
              }
              &:last-of-type {
                padding-right: 18 * @rem;
              }
            }
            .game-item {
              box-sizing: border-box;
              width: 275 * @rem;
              height: 68 * @rem;
              border-radius: 12 * @rem;
              display: flex;
              align-items: center;
              &:not(:first-of-type) {
                margin-top: 20 * @rem;
              }
            }
          }
        }
      }
      // 金手指
      .gold-finger-content {
        background: #f9f9f9;
        height: 100%;
        padding: 16 * @rem 12 * @rem 0;
        .gold-finger-list {
          display: flex;
          flex-direction: column;
          margin-bottom: 14 * @rem;
          .gold-finger-item {
            width: 351 * @rem;
            height: 44 * @rem;
            background: #ffffff;
            border-radius: 8 * @rem;
            padding: 14 * @rem 15 * @rem 14 * @rem 12 * @rem;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .icon {
              width: 10 * @rem;
              height: 12 * @rem;
            }
            .info {
              font-weight: 400;
              font-size: 13 * @rem;
              color: #303236;
              margin-left: 6 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            &:not(:last-child) {
              margin-bottom: 12 * @rem;
            }
          }
        }
      }
    }
  }

  .content2 {
    width: 200px;
    line-height: 20px;
    /* 设置为行高的整倍数，此处显示两行: 2 * 20px */
    max-height: 40px;
  }
  .fixed-review-box {
    position: fixed;
    right: 14 * @rem;
    bottom: 0;
    z-index: 99;
    padding-bottom: calc(95 * 0.0267rem + env(safe-area-inset-bottom));
    .fixed-review-btn {
      background-size: 58 * @rem 56 * @rem;
      width: 58 * @rem;
      height: 56 * @rem;
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .download-bar {
        flex: 1;
        min-width: 0;
        height: 60 * @rem;
        .download-content {
          height: 60 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .download-btn {
            flex: 1;
            height: 44 * @rem;
            background: @themeBg;
            text-align: center;
            line-height: 44 * @rem;
            color: #fefefe;
            font-size: 16 * @rem;
            font-weight: 500;
            border-radius: 8 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 29 * @rem;
            .icon {
              background-origin: -1 * @rem -1 * @rem;
              background-size: 22 * @rem 24 * @rem;
              width: 21 * @rem;
              height: 23 * @rem;
            }
            .text {
              margin-left: 4 * @rem;
              font-weight: 600;
              font-size: 16 * @rem;
              color: #ffffff;
              &.loading {
                position: relative;
                font-size: 0;
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  -webkit-transform: translate(-50%, -50%);
                  display: block;
                  width: 16 * @rem;
                  height: 16 * @rem;
                  background-size: 16 * @rem 16 * @rem;
                  background-image: url(~@/assets/images/downloadLoading.png);
                  -webkit-animation: rotate 1s infinite linear;
                  animation: rotate 1s infinite linear;
                }
              }
            }
          }
        }
      }
      .comment {
        margin-right: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .comment-icon {
          width: 23 * @rem;
          height: 22 * @rem;
          background-size: 24 * @rem 24 * @rem;
          .image-bg('~@/assets/images/games/comment-icon-emn.png');
        }
        .comment-title {
          font-size: 12 * @rem;
          color: #222222;
          font-weight: 500;
          margin-top: 3 * @rem;
        }
      }
    }
  }
  .detailedInfo-popup {
    max-height: 400 * @rem;
    // min-height: 200 * @rem;
    display: flex;
    flex-direction: column;

    .permission-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20 * @rem 18 * @rem 0 18 * @rem;
      margin-bottom: 20 * @rem;
      .title {
        font-size: 16 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        flex-shrink: 0;
        font-weight: 600;
        color: #111111;
      }
      .close {
        background: url('~@/assets/images/games/permission-close.png') no-repeat
          0 0;
        background-size: 14 * @rem 14 * @rem;
        width: 13 * @rem;
        height: 13 * @rem;
      }
    }

    .permission-list {
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      padding: 0 18 * @rem 0 18 * @rem;
      .permission-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .name {
          font-weight: 400;
          font-size: 14 * @rem;
          color: #777777;
          white-space: nowrap;
        }
        .value-box {
          display: flex;
          align-items: center;
          width: 210 * @rem;
          justify-content: flex-end;
          .value1 {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #333333;
          }
          .value2 {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #32b768;
            line-height: 14 * @rem;
            display: inline;
            border-bottom: solid 1px #32b768;
            &:not(:first-child) {
              margin-left: 9 * @rem;
            }
          }
        }
        &:not(:first-child) {
          margin-top: 16 * @rem;
        }
        &:last-child {
          margin-bottom: 43 * @rem;
        }
      }
    }
  }

  .permission-popup {
    max-height: 400 * @rem;
    min-height: 200 * @rem;
    display: flex;
    flex-direction: column;
    .permission-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20 * @rem 18 * @rem 0 18 * @rem;
      margin-bottom: 20 * @rem;
      .title {
        font-size: 16 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        flex-shrink: 0;
        font-weight: 600;
        color: #111111;
      }
      .close {
        background: url('~@/assets/images/games/permission-close.png') no-repeat
          0 0;
        background-size: 14 * @rem 14 * @rem;
        width: 13 * @rem;
        height: 13 * @rem;
      }
    }

    .permission-list {
      padding: 0 14 * @rem 20 * @rem;
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      .permission-item {
        padding: 10 * @rem;
        border-bottom: 1px solid #ebebeb;
        .name {
          font-size: 14 * @rem;
          color: #666;
        }
        .desc {
          font-size: 12 * @rem;
          color: #999;
          line-height: 18 * @rem;
          margin-top: 10 * @rem;
        }
      }
    }
  }
  .section {
    padding: 20 * @rem 12 * @rem 0;
    .section-title {
      margin-bottom: 10 * @rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .title-text {
      font-size: 16 * @rem;
      color: #191b1f;
      font-weight: 600;
      display: flex;
      height: 20 * @rem;
      line-height: 20 * @rem;
      align-items: center;
      .section-arrow {
        margin-left: 4 * @rem;
        width: 8 * @rem;
        height: 12 * @rem;
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loading_rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
