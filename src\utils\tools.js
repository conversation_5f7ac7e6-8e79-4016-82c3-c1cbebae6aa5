import store from '@/store';
import router from '@/router';
import { platform, authInfo, BOX_consultService } from './box.uni';
import { getQueryVariable } from '@/utils/function.js';

export function modelName() {
  // 例子：小米10
  let str = '';
  if (platform == 'android') {
    if (authInfo?.useEwm == 1) {
      str = '模拟器';
    } else {
      let model = BOX.getModel();
      if (model) {
        str = model;
      } else {
        str = '';
      }
    }
  }

  return str;
}

export function systemVersion() {
  let str = '_系统：';
  // 例子：安卓10
  if (!authInfo.androidVersion) {
    str = '';
  } else {
    str += authInfo.androidVersion;
  }
  return str;
}

export function systemName() {
  switch (platform) {
    case 'iosBox':
      return 'iOS商店版';
    case 'android':
      return 'Android官包';
    case 'androidBox':
      return 'Android马甲包';
    default:
      return 'iOS书签版';
  }
}

export function appVersion() {
  switch (platform) {
    case 'android':
      return authInfo.versionName || authInfo.versionCode;
    default:
      return process.env.VUE_APP_versionCode;
  }
}

export function currentCps() {
  if (platform == 'android') {
    return authInfo.channel;
  } else {
    if (getQueryVariable('c')) {
      return `cps${getQueryVariable('c')}`;
    }
    return 'empty';
  }
}

export function toPage(name, params = {}, replace = 0) {
  replace
    ? router.replace({ name: name, params: params })
    : router.push({ name: name, params: params });
}

export function openKefu(params = {}) {
  if (platform == 'android') {
    try {
      let res = BOX_consultService(
        params.from,
        params.is_zx ? '尊享VIP客服' : '客服',
        Boolean(params.is_zx),
      );
      if (res) {
        return;
      }
    } catch (e) {
      console.log(e);
    }
  }
  if (!store.getters['user/userInfo'].username) {
    toPage('KefuChat', { is_zx: false });
  } else {
    let modelNameStr = modelName() ? '机型：' + modelName() : '';

    // 需传入的额外数据
    let extraData = [
      {
        key: 'laiyuan',
        label: '来源',
        value: `${systemName()}_${store.getters['system/appName']}_v${appVersion()}_注册：cps${store.getters['user/userInfo'].agent_id}(${store.getters['user/userInfo'].agent_name})_当前：${currentCps()}_客户端系统`,
      },
      {
        key: 'model',
        label: '设备信息',
        value: `${modelNameStr}${systemVersion()}`,
      },
    ];
    // 如果是返利申请的按钮
    if (params.from == 'fanli') {
      extraData.push({
        key: 'fanli',
        label: '返利申请',
        value: `${params.gameName}_${params.activityName}`,
      });
    }
    // 如果是云挂机的按钮
    if (params.from == 'cloudHangup') {
      extraData.push({
        key: 'cloudHangup',
        label: '云挂机',
        value: `${params.gameName}_${params.id}_${params.package_name}`,
      });
    }
    ysf('config', {
      uid: store.getters['user/userInfo'].username,
      name: `${store.getters['user/userInfo'].username}${store.getters['user/userInfo'].is_svip ? '(SVIP)' : ''
        }`,
      mobile: store.getters['user/userInfo'].mobile ?? '',
      data: JSON.stringify(extraData),
      groupid: store.getters['user/userInfo'].is_svip ? 482269080 : 482171132,
      robotShuntSwitch: 1,
      success: () => {
        toPage('KefuChat', { is_zx: params.is_zx });
      },
    });
  }
}

/**
 * 汇率转换
 * @param { number } amount 金额
 * @param { number } rate 汇率
 * @returns { number } 转换后的金额
 */
export function formatExchangeRate(amount, rate) {
  if (!rate) {
    return amount;
  }
  amount = amount * rate;
  const amount_1 = Number(amount.toFixed(2));
  const amount_2 = Number(amount.toFixed(3));
  if (amount_1 < amount_2) {
    return Number((Number(amount_1) + 0.01).toFixed(2));
  }
  return amount_1;
}

/**
 * url转成路由名
 * @param {*} url 
 * @returns routeName
 */
export function formatHashPathToRouteName(url) {
  const match = url.match(/#\/([^?]+)/); // 提取 #/ 后面到 ? 之前的部分
  const route = match ? match[1] : '';

  return route
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

export const sdkUrlList = [
  'https://static-hw.3733.com/wa/public/static/js/BgsSdk.min.1.28.0.js',
  '',
  'https://static-hw.3733.com/wa/public/static/js/BySDK.min.js'
]

// 动态加载云挂机SDK方法
export function loadSDK(cloudType) {
  // 先移除可能已经存在的SDK
  const existingScripts = document.querySelectorAll('script[data-sdk-type]');

  // 检查是否已经加载了相同类型的SDK
  for (const script of existingScripts) {
    if (Number(script.getAttribute('data-sdk-type')) == Number(cloudType) && script.src == sdkUrlList[cloudType]) {
      // console.log(`SDK已存在，无需重复加载: ${script.src}, cloudType: ${cloudType}`);
      return false; // 如果已存在相同类型SDK，直接返回
    }
    script.remove();
  }

  // 根据cloudType加载对应SDK
  const sdkUrl = sdkUrlList[Number(cloudType)];

  const script = document.createElement('script');
  script.src = sdkUrl;
  script.setAttribute('data-sdk-type', cloudType);
  document.head.appendChild(script);

  // console.log(`已加载SDK: ${sdkUrl}, cloudType: ${cloudType}`);
};

/**
 * 防抖函数：n 秒内只执行一次 若在 n 秒内再次触发 则重新计时
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 延迟执行的时间（毫秒）
 * @param {boolean} immediate - 是否立即执行一次
 * @returns {Function}
 */
export function debounce(func, wait = 1500, immediate = false) {
  let timeout;
  return function (...args) {
    const context = this;

    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(context, args);
  };
}

/**
 * 节流函数：每隔 n 秒只执行一次
 * @param {Function} func - 要执行的函数
 * @param {number} limit - 限制的时间间隔（毫秒）
 * @returns {Function}
 */
export function throttle(func, limit = 1500) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      func.apply(this, args);
    }
  };
}