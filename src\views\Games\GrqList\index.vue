<template>
  <div class="grq-list">
    <nav-bar-2 :border="true" :title="$t('游戏管理')"> </nav-bar-2>
    <pull-refresh @refresh="init()" v-model="isLoading">
      <main>
        <content-empty v-if="!list.length"></content-empty>
        <div class="list" v-else>
          <game-item
            v-for="(item, index) in list"
            :key="index"
            :gameInfo="item"
            :type="3"
            @refreshList="init()"
          >
          </game-item>
        </div>
      </main>
    </pull-refresh>
  </div>
</template>
<script>
import { ApigrqGameList } from '@/api/views/game.js';
import GameItem from '@/components/game-item';

export default {
  name: 'GrqList',
  data() {
    return {
      list: [],
      count: 0,
      isLoading: false,
    };
  },
  created() {
    this.$toast({
      type: 'loading',
      duration: 0,
      message: this.$t('拼命加载中...'),
    });
    try {
      this.init();
    } finally {
      this.$toast.clear();
    }
  },
  methods: {
    async init() {
      const res = await ApigrqGameList();
      this.count = res.data.grq_shengyu_count;
      this.list = res.data.list;
      this.$nextTick(() => {
        this.isLoading = false;
      });
    },
  },
  components: {
    GameItem,
  },
};
</script>
<style lang="less" scoped>
.list {
  height: calc(100vh - 50 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
  padding: 0 14 * @rem 14 * @rem;
}
.title {
  text-align: center;
  line-height: 40 * @rem;
  font-size: 15 * @rem;
  .color {
    color: #ff481e;
  }
}
</style>
