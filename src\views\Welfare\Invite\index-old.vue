<template>
  <rubber-band :topColor="'#ff7878'" :bottomColor="'#fa6a69'">
    <div class="invite-page">
      <nav-bar-2
        bgStyle="transparent-white"
        :border="false"
        :placeholder="false"
      ></nav-bar-2>
      <div class="main">
        <div class="top-bar"></div>
        <div class="rule-container">
          <div class="rule-title">
            <div class="title-icon title-icon-left"></div>
            <div class="title-text">{{ $t('奖励规则') }}</div>
            <div class="title-icon title-icon-right"></div>
          </div>
          <div class="rule-content" v-html="rules"></div>
        </div>
        <!-- 邀请统计 -->
        <div class="state-container">
          <div class="state-item">
            <div class="state-title">
              <div class="state-title-text">{{ $t('邀请人数') }}</div>
              <div class="right-icon"></div>
            </div>
            <div class="state-mount">{{ user_num }}</div>
            <div class="state-unit">{{ $t('人') }}</div>
          </div>
          <div class="state-item">
            <div class="state-title">
              <div class="state-title-text">{{ $t('累计收入') }}</div>
              <div class="right-icon"></div>
            </div>
            <div class="state-mount">{{ gold_num }}</div>
            <div class="state-unit">{{ $t('金币') }}</div>
          </div>
        </div>
        <div class="invite-btn" @click="handleInvite">
          {{ $t('立即邀请好友') }}
        </div>
      </div>
    </div>
  </rubber-band>
</template>
<script>
import rubberBand from '@/components/rubber-band';
import { ApiUserInviteCount } from '@/api/views/users.js';
export default {
  name: 'Invite',
  components: {
    rubberBand,
  },
  data() {
    return {
      gold_num: 0,
      user_num: 0,
      rules: '',
      inviteUrl: '',
    };
  },
  async created() {
    this.inviteUrl = `https://u.a3733.com/float.php/float/box/app_invite_register?referrer_user_id=${this.userInfo.user_id}&referrer_channel=${this.currentCps}`;
    await this.getInviteCount();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async getInviteCount() {
      const res = await ApiUserInviteCount();
      let { gold_num, rules, user_num } = res.data;
      this.gold_num = gold_num;
      this.rules = rules;
      this.user_num = user_num;
    },
    handleInvite() {
      this.$copyText(this.inviteUrl).then(
        res => {
          this.$toast(this.$t('链接已复制到剪贴板，快去邀请好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请联系客服'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.invite-page {
  background-color: #fa6a69;
  font-size: 14 * @rem;
  min-height: 100vh;
  .main {
    padding-bottom: 40 * @rem;
    background-color: #fa6a69;
  }
  .top-bar {
    width: 100%;
    height: 408 * @rem;
    .image-bg('~@/assets/images/invite/invite-bg.png');
  }
  .rule-container {
    width: 100%;
    .rule-title {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10 * @rem 0;
      .title-icon {
        width: 16 * @rem;
        height: 11 * @rem;
        &.title-icon-left {
          .image-bg('~@/assets/images/invite/rule-title-left-icon.png');
        }
        &.title-icon-right {
          .image-bg('~@/assets/images/invite/rule-title-right-icon.png');
        }
      }
      .title-text {
        font-size: 18 * @rem;
        color: #ffffff;
        font-weight: 600;
        margin: 0 6 * @rem;
      }
    }
    .rule-content {
      box-sizing: border-box;
      width: 347 * @rem;
      height: 123 * @rem;
      .image-bg('~@/assets/images/invite/rule-bg.png');
      margin: 5 * @rem auto 0;
      padding: 0 30 * @rem;
      font-size: 13 * @rem;
      color: #000000;
      line-height: 21 * @rem;
      text-align: justify;
      display: flex;
      align-items: center;
      padding-top: 20 * @rem;
    }
  }
  .state-container {
    width: 328 * @rem;
    height: 102 * @rem;
    background: #ffffff;
    border-radius: 12 * @rem;
    margin: 14 * @rem auto 0;
    display: flex;
    justify-content: space-around;
    position: relative;
    &::before {
      content: '';
      width: 1 * @rem;
      height: 15 * @rem;
      background-color: #ebebeb;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .state-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      .state-title {
        width: 110 * @rem;
        height: 28 * @rem;
        .image-bg('~@/assets/images/invite/total-subtitle-icon.png');
        display: flex;
        align-items: center;
        justify-content: center;
        .state-title-text {
          font-size: 12 * @rem;
          color: #ffffff;
          font-weight: 600;
        }
        .right-icon {
          width: 6 * @rem;
          height: 10 * @rem;
          .image-bg('~@/assets/images/invite/arrow-right-icon.png');
          margin-left: 5 * @rem;
        }
      }
      .state-mount {
        font-size: 22 * @rem;
        color: #000000;
        line-height: 24 * @rem;
        margin-top: 10 * @rem;
      }
      .state-unit {
        font-size: 12 * @rem;
        color: #9a9a9a;
        line-height: 24 * @rem;
        margin-bottom: 9 * @rem;
      }
    }
  }
  .invite-btn {
    overflow: hidden;
    width: 327 * @rem;
    height: 78 * @rem;
    margin: 12 * @rem auto 0;
    .image-bg('~@/assets/images/invite/invite-btn.png');
    text-align: center;
    line-height: 70 * @rem;
    font-size: 20 * @rem;
    color: #ffffff;
    font-weight: 500;
  }
}
</style>
