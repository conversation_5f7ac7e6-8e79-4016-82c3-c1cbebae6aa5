<template>
  <div class="cloud-play-box">
    <div
      class="game-item-components"
      @click.prevent="toPage('GameDetail', { id: cloudPlayInfo.id })"
    >
      <div class="game-bg">
        <img
          v-show="cloudPlayInfo.titleimg"
          :src="cloudPlayInfo.titleimg"
          alt=""
        />
      </div>
      <div class="game-bottom-item">
        <div class="left-item">
          <div class="game-img">
            <img
              v-show="cloudPlayInfo.titlepic"
              :src="cloudPlayInfo.titlepic"
              alt=""
            />
          </div>
          <div class="game-title">
            <div class="title1" v-if="cloudPlayInfo.title">{{
              cloudPlayInfo.title
            }}</div>
            <div class="title2" v-if="cloudPlayInfo.yxftitle">{{
              cloudPlayInfo.yxftitle
            }}</div>
            <div
              v-if="
                !cloudPlayInfo.yxftitle &&
                cloudPlayInfo.new_cate_list &&
                cloudPlayInfo.new_cate_list.length
              "
              class="tag-list"
            >
              <div class="tags">
                <div
                  class="tag"
                  v-for="tag in cloudPlayInfo.new_cate_list"
                  :key="tag.id"
                >
                  {{ tag.title }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="game-btn">
          <div
            v-if="cloudPlayInfo.detailid == 1"
            class="btn"
            :class="{ loading: pcCloudGameInitLoading[cloudPlayInfo.id] }"
            @click.stop="cloudPlayInit(cloudPlayInfo, cloudPlayInfo.id)"
            >云玩</div
          >
          <div
            v-else
            class="btn"
            :class="{ loading: pcCloudGameInitLoading[cloudPlayInfo.id] }"
            @click.stop="downJymyBtn(cloudPlayInfo)"
            >下载</div
          >
        </div>
      </div>
    </div>
    <div class="game-icon" v-if="cloudPlayInfo.state_tag"
      >{{ cloudPlayInfo.state_tag }}
    </div>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from 'vuex';
import { downJymyBtnCallback } from '@/utils/function';
export default {
  name: 'cloudPlayItem',
  data() {
    return {
      cloudPlayInfo: {},
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
    isShowBtn: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    info(val) {
      this.cloudPlayInfo = val;
    },
  },
  created() {
    this.cloudPlayInfo = this.info;
  },
  mounted() {
    this.setPcCloudGameInitLoadingEmpty({});
  },
  methods: {
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    // 云玩
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    ...mapActions({
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
    }),
    ...mapMutations({
      setPcCloudGameInitLoadingEmpty: 'game/setPcCloudGameInitLoadingEmpty',
    }),
  },
  computed: {
    ...mapGetters({
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
    }),
  },
};
</script>

<style lang="less" scoped>
.cloud-play-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  .game-item-components {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 236 * @rem;
    background: #f6f7f9;
    border-radius: 10 * @rem;
    overflow: hidden;
    .game-bg {
      min-height: 178 * @rem;
      height: 178 * @rem;
      background: #eee;
    }
    .game-bottom-item {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7 * @rem 12 * @rem;
      box-sizing: border-box;
      .left-item {
        display: flex;
        align-items: center;
        .game-img {
          width: 44 * @rem;
          height: 44 * @rem;
          border-radius: 10 * @rem;
          background: #eee;
          overflow: hidden;
        }
        .game-title {
          margin-left: 6 * @rem;
          display: flex;
          flex-direction: column;
          max-width: 160 * @rem;

          .title1 {
            font-weight: 600;
            font-size: 16 * @rem;
            color: #222222;
            height: 20 * @rem;
            line-height: 20 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .title2 {
            margin-top: 4 * @rem;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #868686;
            line-height: 14 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .tag-list {
            display: flex;
            align-items: center;
            margin-top: 4 * @rem;
            .tags {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              overflow: hidden;
              height: 16 * @rem;
              line-height: 16 * @rem;

              .tag {
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                border: 1px solid rgba(134, 134, 134, 0.73);
                border-radius: 4 * @rem;
                padding: 2 * @rem 4 * @rem;
                height: 16 * @rem;
                line-height: 16 * @rem;
                box-sizing: border-box;
                font-weight: 400;
                font-size: 11 * @rem;
                color: rgba(134, 134, 134, 0.73);
                &:not(:first-child) {
                  margin-left: 8 * @rem;
                }
              }
            }
          }
        }
      }

      .game-btn {
        position: relative;
        .btn {
          position: relative;
          width: 58 * @rem;
          height: 28 * @rem;
          line-height: 28 * @rem;
          text-align: center;
          background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
          border-radius: 24 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #ffffff;
          &.loading {
            position: relative;
            font-size: 0;
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              -webkit-transform: translate(-50%, -50%);
              display: block;
              width: 16 * @rem;
              height: 16 * @rem;
              background-size: 16 * @rem 16 * @rem;
              background-image: url(~@/assets/images/downloadLoading.png);
              animation: rotate 1s infinite linear;
              -webkit-animation: rotate 1s infinite linear;
            }
          }
        }
      }
    }
  }
  .game-icon {
    position: absolute;
    top: 8 * @rem;
    left: 8 * @rem;
    color: #fff;
    background: rgba(18, 18, 18, 0.6);
    box-sizing: border-box;
    padding: 3 * @rem 8 * @rem;
    border-radius: 4 * @rem;
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
