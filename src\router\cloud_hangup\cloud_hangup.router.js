export default [
  {
    path: '/cloud_hangup',
    name: 'CloudHangup',
    component: () =>
      import(/* webpackChunkName: "cloud_hangup" */ '@/views/CloudHangup'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      pageTitle: '云挂机主页',
    },
  },
  {
    path: '/cloud_hangup_interface',
    name: 'CloudHangupInterface',
    component: () =>
      import(
        /* webpackChunkName: "cloud_hangup" */ '@/views/CloudHangup/CloudHangupInterface'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      pageTitle: '云挂机游戏界面',
    },
  },
  {
    path: '/cloud_hangup_FAQ',
    name: 'CloudHangupFAQ',
    component: () =>
      import(
        /* webpackChunkName: "cloud_hangup" */ '@/views/CloudHangup/CloudHangupFAQ'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: false,
      pageTitle: '云挂机问答',
    },
  },
];
