<template>
  <div class="first-coupon">
    <div class="bg">
      <div class="back" @click="back()"></div>
      <div class="container">
        <div v-for="(item, index) in couponList" :key="index" class="coupon">
          <div class="top">
            <div class="left">{{ item.money }}<span>元</span></div>
            <div class="center">
              <div class="big-text">{{ item.tip }}</div>
              <div class="small-text">满{{ item.reach_money }}元可用</div>
              <div class="small-text">有效期{{ item.period }}天</div>
            </div>
            <div
              class="right"
              :class="{ already: item.is_take }"
              @click="receive(item)"
            >
              {{ item.is_take ? '已领取' : '点击领取' }}
            </div>
          </div>
          <div class="bottom">单笔充值满{{ item.reach_money }}元时可用</div>
        </div>
        <div class="title">快速了解</div>
        <div class="video-container">
          <video
            src="http://1252153290.vod2.myqcloud.com/da1e24bdvodgzp1252153290/a1ee9f275285890813016924461/9Lvb7yPHxAAA.mp4"
            poster="http://pic5.pic3733.com/snapshot/201904/b77b4aa17137ac4add4f5ec60efdab45_n.jpeg"
            ref="videoPlayer"
            :controls="isPlaying"
            :webkit-playsinline="false"
            :playsinline="false"
            :x5-playsinline="false"
            x-webkit-airplay="allow"
          ></video>
          <div class="modal" v-if="!isPlaying">
            <div @click="play()" class="play"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  ApiCouponGetFirstCouponAjax,
  ApiCouponTakeCouponAjax,
} from '@/api/views/coupon';

export default {
  name: 'FirstCoupon',
  data() {
    return {
      couponList: [], //优惠券列表
      isPlaying: false,
      status: [],
    };
  },
  async created() {
    const res = await ApiCouponGetFirstCouponAjax();
    this.couponList = res.data.coupon;
  },
  methods: {
    async receive(item) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.toPage('PhoneLogin');
      } else {
        if (item.is_take) {
          this.$toast('亲，当前账号已经领取过该券了~');
          return false;
        }
        const res = await ApiCouponTakeCouponAjax({ coupon_id: item.id });
        this.$toast(res.msg);
        // 修改状态
        const res2 = await ApiCouponGetFirstCouponAjax();
        this.couponList = res2.data.coupon;
      }
    },
    play() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
  },
};
</script>
<style lang="less" scoped>
.first-coupon {
  background: linear-gradient(to right, #f69145, #f68543);
}
.bg {
  position: relative;
  min-height: 100vh;
  background-image: url(~@/assets/images/active/first-coupon-image1.png);
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;
}
.back {
  position: absolute;
  top: calc(15 * @rem + @safeAreaTop);
  top: calc(15 * @rem + @safeAreaTopEnv);
  left: 18.5 * @rem;
  width: 19 * @rem;
  height: 15 * @rem;
  background-image: url(~@/assets/images/back-white.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
.container {
  margin-top: 270 * @rem;
  min-height: 636 * @rem;
}
.coupon {
  box-sizing: border-box;
  width: 364.5 * @rem;
  height: 125 * @rem;
  margin: 0 auto;
  padding: 0 20 * @rem;
  background-image: url(~@/assets/images/active/first-coupon-image2.png);
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 82 * @rem;
    margin-top: 6 * @rem;
    // background: blue;
    color: #fff;
    .left {
      flex: 0 0 80 * @rem;
      margin-left: 15 * @rem;
      line-height: 1;
      font-size: 39 * @rem;
      span {
        position: relative;
        top: -2 * @rem;
        left: 3 * @rem;
        font-size: 13 * @rem;
      }
    }
    .center {
      flex: 1;
      text-align: left;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      box-sizing: border-box;
      padding: 12 * @rem 0;
      margin-left: 28 * @rem;
      .big-text {
        font-size: 17 * @rem;
      }
      .small-text {
        font-size: 12 * @rem;
        color: #fff8ea;
      }
    }
    .right {
      width: 75 * @rem;
      height: 28 * @rem;
      background-color: #fff;
      border-radius: 14 * @rem;
      color: #fe9f00;
      text-align: center;
      line-height: 28 * @rem;
      &.already {
        color: #999;
      }
    }
  }
  .bottom {
    height: 25 * @rem;
    line-height: 25 * @rem;
    color: #666666;
  }
}
.title {
  margin: 24 * @rem 0 16 * @rem 14 * @rem;
  font-size: 18 * @rem;
  color: #fff;
}
.video-container {
  position: relative;
  margin: 15 * @rem;

  video {
    width: 345 * @rem;
    height: 197 * @rem;
  }
  .modal {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    .play {
      width: 64 * @rem;
      height: 64 * @rem;
      background-image: url(~@/assets/images/video-play.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}
</style>
