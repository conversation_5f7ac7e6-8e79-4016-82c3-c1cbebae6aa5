<template>
  <div class="page external-game-detail">
    <nav-bar-2
      bgStyle="transparent-white"
      :placeholder="false"
      v-if="navBgTransparent"
    >
      <template #right>
        <div
          class="collect-btn collect-btn-white btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-2>
    <nav-bar-2
      :title="detail.main_title"
      :placeholder="false"
      :border="true"
      v-else
    >
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-2>
    <div class="top-banner">
      <div class="game-cover">
        <img :src="detail.video_thumb" alt="" />
      </div>
    </div>
    <content-empty v-if="noGame" :tips="$t('没有该游戏')"></content-empty>
    <template v-else>
      <div class="game-bar">
        <div class="game-info">
          <div class="info-img">
            <img :src="detail.titlepic" alt="" />
          </div>
          <div class="info-box">
            <div class="title">{{ detail.title }}</div>
            <div class="center">
              <span v-if="detail.version || detail.version_i"
                >版本:
                {{
                  (isIos ? detail.version_i : detail.version) || '1.0.0'
                }}</span
              >
              <span>{{ detail.size_a }}</span>
            </div>
            <div class="types">
              <span
                class="type"
                v-for="(item, index) in detail.extra_tag"
                :key="index"
                >{{ item.name }}</span
              >
            </div>
          </div>
        </div>
        <div class="cp_txt" v-if="cp_txt">{{ cp_txt }}</div>
        <div class="game-tag" v-if="detail.game_label">
          <span
            class="type"
            v-for="(item, index) in detail.game_label"
            :key="index"
            >{{ item.title }}</span
          >
        </div>
      </div>
      <div class="bg-h8"> </div>
      <div class="detail-content">
        <!-- tabs -->
        <van-sticky :offset-top="stickyOffsetTop">
          <div class="tabs">
            <template>
              <div
                class="tab btn"
                v-for="(tab, index) in tabList"
                :key="index"
                :class="{ active: current === index }"
                @click="clickTab(index)"
              >
                <span>{{ tab.title }} </span>
                <div class="notice" v-if="index == 2">
                  {{ cmtSum }}
                </div>
              </div>
            </template>
            <div
              class="tab-line"
              :style="{ left: handleLeftDistance(current) }"
            ></div>
          </div>
        </van-sticky>
        <div class="content-container">
          <div class="tab-content tab-detail" v-if="current == 0">
            <van-loading v-if="!loadSuccess" />
            <template v-else>
              <!-- 截图 -->
              <div
                class="game-picture-swiper"
                v-if="detail.morepic && detail.morepic.small.length"
              >
                <swiper :options="swiperOption">
                  <!-- slides -->
                  <swiper-slide
                    class="swiper-slide video-container"
                    v-if="detail.video_url"
                  >
                    <video
                      ref="videoPlayer"
                      id="video"
                      :src="detail.video_url"
                      :poster="detail.video_thumb"
                      :controls="isPlaying"
                      x5-playsinline=""
                      :playsinline="true"
                      :webkit-playsinline="true"
                      :x-webkit-airplay="true"
                      x5-video-player-type="h5"
                      :x5-video-player-fullscreen="true"
                      x5-video-orientation="portraint"
                      muted
                    ></video>
                    <div class="mask" v-show="!isPlaying">
                      <div class="play-btn" @click="handlePlay"></div>
                    </div>
                  </swiper-slide>
                  <swiper-slide
                    v-for="(item, index) in detail.morepic
                      ? detail.morepic.small
                      : []"
                    :key="index"
                    class="swiper-slide"
                  >
                    <img
                      :src="item"
                      class="slide-img btn"
                      @click="showBigImage(detail.morepic.big, index)"
                    />
                    <i class="player" v-if="index === 1"></i>
                  </swiper-slide>
                </swiper>
              </div>
              <div class="category-tags">
                <div
                  class="category-tag-item"
                  v-for="(type, typeIndex) in detail.type"
                  :key="typeIndex"
                  @click="tapType(type)"
                >
                  {{ type }}
                </div>
              </div>
              <!-- 游戏介绍 -->
              <div class="introduction section" v-if="detail.newstext">
                <div class="section-title">
                  <div class="title-icon icon-jieshao"></div>
                  <div class="title-text">{{ $t('游戏介绍') }}</div>
                </div>

                <div
                  v-html="detail.newstext"
                  class="introduction-text"
                  ref="content3"
                  :class="{ on: !isAll3 }"
                ></div>
                <div
                  class="more-text"
                  @click="isAll3 = !isAll3"
                  v-if="contentHeight3 > 60"
                >
                  <span>{{ !isAll3 ? $t('展开') : $t('收起') }}</span>
                  <div class="more-text-icon" :class="{ on: isAll3 }"></div>
                </div>
              </div>
              <!-- 上线福利 -->
              <div class="welfare-container section" v-if="detail.features">
                <div class="section-title">
                  <div class="title-icon icon-fuli"></div>
                  <div class="title-text">{{ $t('上线福利') }}</div>
                </div>
                <div class="wel-section">
                  <div
                    class="content-text"
                    ref="content1"
                    :class="{ on: !isAll1 }"
                    v-html="detail.features"
                  ></div>
                  <div
                    class="more-text"
                    @click="isAll1 = !isAll1"
                    v-if="contentHeight1 > 100"
                  >
                    <span>{{ !isAll1 ? $t('展开') : $t('收起') }}</span>
                    <div class="more-text-icon" :class="{ on: isAll1 }"></div>
                  </div>
                </div>
              </div>
              <!-- 版本信息 -->
              <div class="section version-section">
                <div class="section-title">
                  <div class="title-icon icon-xinxi"></div>
                  <div class="title-text">版本信息</div>
                </div>
                <div class="version-container">
                  <div class="version-item">
                    <div class="version-title">版本号</div>
                    <div class="version-desc">
                      {{
                        (isIos ? detail.version_i : detail.version) || '1.0.0'
                      }}
                    </div>
                    <div
                      class="requestUpdates"
                      @click="please_update_popup = true"
                      >求更新</div
                    >
                  </div>
                  <div class="version-item">
                    <div class="version-title">更新时间</div>
                    <div class="version-desc">{{ formattedTime }}</div>
                  </div>
                  <div class="version-item">
                    <div class="version-title">权限说明</div>
                    <div class="version-desc">
                      <div class="btn" @click="handlePermission">查看</div>
                    </div>
                  </div>
                  <div class="version-item">
                    <div class="version-title">隐私政策</div>
                    <div class="version-desc">
                      <div class="btn" @click="handlePrivacyPermission">
                        查看
                      </div>
                    </div>
                  </div>
                  <div class="version-item" v-if="detail.networking_text">
                    <div class="version-title">联网APP</div>
                    <div class="version-desc">{{ detail.networking_text }}</div>
                  </div>
                  <div class="version-item" v-if="detail.age_appropriate">
                    <div class="version-title">适龄范围</div>
                    <div class="version-desc">{{ detail.age_appropriate }}</div>
                  </div>
                  <div class="version-item w100" v-if="detail.company_name">
                    <div class="version-title">厂商来源</div>
                    <div class="version-desc">{{ detail.company_name }}</div>
                  </div>
                  <div class="version-item w100" v-if="detail.record_number">
                    <div class="version-title">备案号</div>
                    <div class="version-desc">{{ detail.record_number }}</div>
                  </div>
                </div>
              </div>
              <!-- 游戏推荐 -->
              <div class="game-rec-container section" v-if="relatedList">
                <div class="section-title">
                  <div class="title-icon icon-xiangguan"></div>
                  <div class="title-text"> {{ $t('游戏推荐') }}</div>
                </div>
                <div class="deal-list">
                  <div class="deal-column" v-if="firstColumn.length">
                    <template v-for="item in firstColumn">
                      <div class="deal-item" :key="item.id">
                        <img
                          class="game-pic"
                          :src="item.titlepic"
                          alt=""
                          @click="toExternalGameDetail(item.id)"
                        />
                        <div class="game-name">{{ item.title }}</div>
                        <div
                          v-if="item.state == 1"
                          :class="{ loading: downloadLoading1[item.id] }"
                          class="game-btn"
                          @click="toExternalGameDetail(item.id)"
                          >云手机</div
                        >
                        <!-- <div
                          v-if="item.state == 1"
                          :class="{ loading: downloadLoading1[item.id] }"
                          class="game-btn"
                          @click="downloadBtn(item.down_a, item.id)"
                          >下载</div
                        > -->
                        <div
                          v-else-if="item.state == 2"
                          :class="{
                            'game-btn reserve': item.subscribed != 1,
                            'game-btn reserve-ok': item.subscribed == 1,
                          }"
                        >
                          <span
                            v-if="item.subscribed != 1"
                            @click="handleSubscribe(item)"
                            >预约</span
                          >
                          <span v-else @click="handleSubscribe(item)"
                            >取消预约</span
                          >
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="deal-column" v-if="secondColumn.length">
                    <template v-for="item in secondColumn">
                      <div class="deal-item" :key="item.id">
                        <img
                          class="game-pic"
                          :src="item.titlepic"
                          alt=""
                          @click="toExternalGameDetail(item.id)"
                        />
                        <div class="game-name">{{ item.title }}</div>
                        <div
                          v-if="item.state == 1"
                          :class="{ loading: downloadLoading1[item.id] }"
                          class="game-btn"
                          @click="toExternalGameDetail(item.id)"
                          >云手机</div
                        >
                        <!-- <div
                          v-if="item.state == 1"
                          :class="{ loading: downloadLoading1[item.id] }"
                          class="game-btn"
                          @click="downloadBtn(item.down_a, item.id)"
                          >下载</div
                        > -->
                        <div
                          v-else-if="item.state == 2"
                          :class="{
                            'game-btn reserve': item.subscribed != 1,
                            'game-btn reserve-ok': item.subscribed == 1,
                          }"
                        >
                          <span
                            v-if="item.subscribed != 1"
                            @click="handleSubscribe(item)"
                            >预约</span
                          >
                          <span v-else @click="handleSubscribe(item)"
                            >取消预约</span
                          >
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <!-- 开发商其他游戏 -->
              <div class="section">
                <div
                  class="section-title mb0"
                  @click="toDevelopersOtherGames()"
                >
                  <div class="title-icon icon-youxi"></div>
                  <div class="title-text"> {{ $t('开发商其他游戏') }}</div>
                  <div class="title-right">
                    <span class="title-right-icon"></span>
                  </div>
                </div>
              </div>
              <!-- 反馈按钮 -->
              <div class="section">
                <div
                  class="section-title"
                  @click="
                    toPage('FeedbackGame', {
                      id: detail.id,
                      gameName: detail.title,
                    })
                  "
                >
                  <div class="title-icon icon-fuli"></div>
                  <div class="title-text">
                    {{ $t('此游戏有问题？') }}{{ $t('点击反馈') }}</div
                  >
                  <div class="title-right">
                    <span class="title-right-icon"></span>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <gift-package-list
            v-if="current == 1"
            :xh_id="xh_id"
          ></gift-package-list>
          <comments-list
            @updateCommentSum="updateCommentSum"
            v-if="current == 2"
          ></comments-list>
          <collection-list
            v-if="current == 3"
            :cate="detail.cate"
            :title="detail.main_title"
          ></collection-list>
        </div>
      </div>
    </template>

    <!-- 底部fixed -->
    <div class="bottom-container" v-if="loadSuccess">
      <div class="bottom-fixed">
        <div class="comment btn" @click="clickComment">
          <div class="comment-icon"></div>
          <div class="comment-title">{{ $t('评论') }}</div>
        </div>
        <div class="download-bar">
          <div class="download-content">
            <!-- <div class="btn h5-btn">
              {{ $t('开始游戏') }}
              <i class="question"></i>
            </div>
            <div class="btn download-btn">
              {{ $t('添加到桌面') }}
              <i class="question"></i>
            </div> -->
            <div class="btn download-btn" @click="goToCloudDevices">
              云手机
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 求更新弹窗 -->
    <van-dialog
      v-model="please_update_popup"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="please-update-popup"
    >
      <div class="title">相对UP主说的话</div>
      <textarea
        class="please-update-input"
        v-model="please_update_input"
        placeholder="请输入您想对UP主说的内容"
      ></textarea>
      <div class="button-container">
        <div @click="please_update_popup = false" class="left">取消</div>
        <div @click="submitPleaseUpdate" class="right">确定</div>
      </div>
    </van-dialog>
    <!-- 续费/购买 设备弹窗 -->
    <van-dialog
      v-model="devicePayPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :overlay-style="{ 'z-index': '2996' }"
      class="onhook-popup"
    >
      <div class="title">{{ $t('温馨提示') }}</div>

      <div class="content"> 您当前无可使用的云手机，是否前往购买设置。 </div>
      <div class="btn-info">
        <div class="cancel" @click.stop="devicePayCancel()">
          {{ $t('取消') }}
        </div>
        <div class="confirm" @click.stop="devicePayConfirm()">
          {{ $t('前往购买') }}
        </div>
      </div>
    </van-dialog>
    <!-- 设备选择弹窗 -->
    <van-dialog
      v-model="deviceDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="device-dialog"
      :closeOnClickOverlay="true"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('温馨提示') }}</div>
        <div class="title-text">
          当前无闲置的云手机，可选择以下设备替换游戏，或者购买设备哦~
        </div>
        <div class="center">
          <div class="left">{{ $t('设备') }}</div>
          <div class="right">
            <div
              class="text"
              v-if="deviceListShowItem.title"
              @click="deviceListShow = !deviceListShow"
            >
              <span>{{ deviceListShowItem.title }}</span>
              <span
                class="more-text-icon"
                :class="{ on: deviceListShow }"
              ></span>
            </div>
            <div class="device-list" :class="{ on: deviceListShow }">
              <div
                class="device-item"
                v-for="(item, index) in onHookEquipmentList"
                :key="index"
                @click="deviceListClick(item)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
        </div>
        <div class="hangingUpName">
          <span>挂机游戏：</span>
          <span class="game-subtitle">
            <div
              :class="{
                'text-scroll': gameTitle.length > 8,
              }"
            >
              <span>{{ gameTitle }}</span>
            </div>
          </span>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="replacementGame()"> 替换游戏 </div>
          <div class="confirm btn" @click="purchaseEquipment()">购买设备</div>
        </div>
      </div>
    </van-dialog>
    <!-- 求更新弹窗 -->
    <van-dialog
      v-model="please_update_popup"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="please-update-popup"
    >
      <div class="title">相对UP主说的话</div>
      <textarea
        class="please-update-input"
        v-model="please_update_input"
        placeholder="请输入您想对UP主说的内容"
      ></textarea>
      <div class="button-container">
        <div @click="please_update_popup = false" class="left">取消</div>
        <div @click="submitPleaseUpdate" class="right">确定</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiGameRead,
  ApiGameGetPermissionInfo,
  ApiResourceCollect,
  ApiResourceCollectStatus,
  ApiGameSubmitCrackWish,
  ApiGameSubscribe,
} from '@/api/views/game.js';
import {
  ApiCommentComments,
  ApiCommentClickComment,
} from '@/api/views/comment.js';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import { platform } from '@/utils/box.uni.js';
import { isIos, isIosBox, isSafari, needGuide } from '@/utils/userAgent';
import { Sticky, ImagePreview } from 'vant';
import { handleTimestamp } from '@/utils/datetime';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { ApiCloudList, ApiCloudEquipPercent } from '@/api/views/upCloud.js';
export default {
  name: 'ExternalGameDetail',
  components: {
    giftPackageList: () => import('./GiftPackageList'),
    commentsList: () => import('./CommentsList'),
    collectionList: () => import('./CollectionList'),
  },
  data() {
    return {
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      stickyOffsetTop: '0px', //顶部导航栏的高度
      detail: {}, //游戏详情
      current: 0, //tabs选中
      tabList: [
        {
          title: this.$t('简介'),
          status: 0,
        },
        {
          title: this.$t('礼包'),
          status: 1,
        },
        {
          title: this.$t('评论'),
          status: 2,
        },
        // {
        //   title: this.$t('合集'),
        //   status: 3,
        // },
      ],
      platform,
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
      },
      navBgTransparent: true,
      isAll1: true, // 展开 -> 是否显示全部
      isAll2: true,
      isAll3: true,
      relatedList: [], // 相关游戏
      detail: {}, // 游戏详情信息
      openList: [], // 开服列表
      coupon: {}, // 代金券信息
      isPlaying: false, //是否正在播放
      contentHeight1: 0, //展开收起内容高度1
      contentHeight2: 0, //展开收起内容高度2
      contentHeight3: 0, //展开收起内容高度3（游戏介绍）
      empty: true, //是否支持下载
      loadSuccess: false, //加载完毕
      isIos, //是ios
      isIosBox, //是iosbox
      noGame: false,
      cmtSum: 0, // 评论数量
      permissionList: [], //游戏获取权限列表
      permissionShow: false, //游戏获取权限列表展示
      collected: 0, //是否已收藏
      downloadLoading1: {}, //下载当前loading id
      xh_id: null, // 默认第一个小号
      cp_txt: '', // 来源
      please_update_popup: false, //求更新弹窗
      please_update_input: '', //求更新input
      cloudDeviceList: [], // 云设备列表
      available_devices: 0, // 1 有设备 0 没有设备
      idleEquipmentList: [], // 闲置设备
      onHookEquipmentList: [], // 挂机设备
      obsoleteEquipmentList: [], // 过期设备
      devicePayPopupShow: false, // 购买设备跳转弹窗
      deviceDialogShow: false, // 显示设备选择弹窗
      deviceListShow: false, // 显示设备列表
      deviceListShowItem: {}, // 选中的设备
      count: 0, // 总量
      remain: 0, // 剩余总量
      percent: 0, // 设备百分比限制
      please_update_popup: false, //求更新弹窗
      please_update_input: '', //求更新input
    };
  },
  async created() {
    this.detail = this.$route.params.gameInfo || {};
  },
  async activated() {
    this.devicePayPopupShow = false;
    this.deviceDialogShow = false;
    await this.getDetail();
    this.devicePayPopupShow = false;
    this.deviceDialogShow = false;
    if (!this.noGame) {
      await this.getCollectStatus();
      await this.getCommentList();
    }
    await this.getCommentList();

    // await this.getXhId();
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    formatTime(val1, val2) {
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;

      if (datePattern.test(val1)) {
        return val1;
      } else if (!isNaN(Number(val1)) && !!val1) {
        let { year, date } = handleTimestamp(Number(val1));
        return `${year}-${date}`;
      }

      if (datePattern.test(val2)) {
        return val2;
      } else if (!isNaN(Number(val2)) && !!val2) {
        let { year, date } = handleTimestamp(Number(val2));
        return `${year}-${date}`;
      }

      // 如果不符合以上任何一种情况，返回 '未知时间'
      return '未知时间';
    },
    // 求更新提交
    async submitPleaseUpdate() {
      if (!this.please_update_input) {
        this.$toast('请填写内容');
        return false;
      }
      const res = await ApiGameSubmitCrackWish({
        remark: this.please_update_input,
        gameName: this.detail.title,
      });
      this.please_update_popup = false;
      this.please_update_input = '';
      this.$toast(res.msg);
    },
    // 获取云挂机设备
    async GetCloudMountedList() {
      this.available_devices = 0;
      this.cloudDeviceList = [];
      this.idleEquipmentList = [];
      this.onHookEquipmentList = [];
      this.obsoleteEquipmentList = [];
      this.deviceListShowItem = {};
      const res = await ApiCloudList();
      const { available_devices, list } = res.data;
      this.available_devices = available_devices;
      this.cloudDeviceList = list;
      // 用户无设备 && 无后台云设备
      if (!this.available_devices && !this.cloudDeviceList) {
        this.$toast('暂时没有可购买的设备了');
        return;
      }
      // 有后台云设备
      if (this.cloudDeviceList) {
        // 区分用户设备状态
        this.cloudDeviceList.forEach(device => {
          switch (device.expire_code) {
            case 0:
              this.idleEquipmentList.push(device);
              break;
            case 1:
              this.onHookEquipmentList.push(device);
              break;
            case 2:
              this.obsoleteEquipmentList.push(device);
              break;
            default:
              break;
          }
        });
      }

      this.setReceiveData(this.detail);
      // 设备容量大于设置百分比限制
      // const isBeyondLimit = (this.remain / this.count) * 100 > this.percent
      if (this.onHookEquipmentList.length) {
        const foundItem = this.onHookEquipmentList.find(
          item => item.game.id === this.detail.id,
        );
        // 如果挂机设备中存在当前游戏 直接进入
        if (foundItem) {
          this.deviceListShowItem = foundItem;
          this.replacementGame();
          return;
        }
      }
      if (this.idleEquipmentList.length) {
        this.setCheckCloudDeviceItem(this.idleEquipmentList[0]);
        this.toPage('CloudHangupInterface', {
          receiveData: this.detail,
          checkCloudDeviceItem: this.idleEquipmentList[0],
        });
      }
      // else if (this.onHookEquipmentList.length && isBeyondLimit) {
      //   // 有挂机设备 库存容量超出限制
      //   this.devicePayPopupShow = true
      // }
      else if (this.onHookEquipmentList.length) {
        // 有挂机设备 库存容量低于限制
        this.deviceListShowItem = this.onHookEquipmentList[0];

        this.deviceDialogShow = true;
      } else {
        this.devicePayPopupShow = true;
      }
    },
    formatTime(val1, val2) {
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;

      if (datePattern.test(val1)) {
        return val1;
      } else if (!isNaN(Number(val1)) && !!val1) {
        let { year, date } = handleTimestamp(Number(val1));
        return `${year}-${date}`;
      }

      if (datePattern.test(val2)) {
        return val2;
      } else if (!isNaN(Number(val2)) && !!val2) {
        let { year, date } = handleTimestamp(Number(val2));
        return `${year}-${date}`;
      }

      // 如果不符合以上任何一种情况，返回 '未知时间'
      return '未知时间';
    },
    // 求更新提交
    async submitPleaseUpdate() {
      if (!this.please_update_input) {
        this.$toast('请填写内容');
        return false;
      }
      const res = await ApiGameSubmitCrackWish({
        remark: this.please_update_input,
        gameName: this.detail.title,
      });
      this.please_update_popup = false;
      this.please_update_input = '';
      this.$toast(res.msg);
    },
    async getEquipmentInventoryCapacity() {
      const res = await ApiCloudEquipPercent();
      const { count, remain, percent } = res.data;
      this.count = count;
      this.remain = remain;
      this.percent = percent;
    },
    devicePayCancel() {
      this.devicePayPopupShow = false;
    },
    devicePayConfirm() {
      this.devicePayPopupShow = false;
      this.toPage('BuyCloudDevices');
    },
    deviceListClick(item) {
      this.deviceListShowItem = item;
      this.deviceListShow = false;
    },
    // 替换游戏
    replacementGame() {
      this.setReceiveData(this.detail);
      this.setCheckCloudDeviceItem(this.deviceListShowItem);
      this.toPage('CloudHangupInterface', {
        receiveData: this.detail,
        checkCloudDeviceItem: this.deviceListShowItem,
      });
    },
    // 购买设备
    purchaseEquipment() {
      this.deviceDialogShow = false;
      this.toPage('BuyCloudDevices');
    },
    // 获取小号[0]
    async getXhId() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.detail.id,
      });
      const { list } = res.data;
      if (list && list.length) {
        this.xh_id = list[0].id;
      }
    },
    // 跳转二级分类页面
    tapType(type) {
      console.log(type);
    },
    toDevelopersOtherGames() {
      this.$router.push({
        name: 'DevelopersOtherGames',
        params: {
          game_id: this.detail.id,
        },
      });
    },
    // 获取热门评论数量
    async getCommentList() {
      const res = await ApiCommentComments({
        page: 1,
        listRows: 1,
        classId: 103,
        sourceId: this.detail.id,
        order: 0,
      });
      let { cmt_sum } = res.data;
      this.cmtSum = cmt_sum;
    },
    updateCommentSum(sum) {
      this.cmtSum = sum;
    },
    clickTab(index) {
      window.scrollTo(0, 0);
      this.current = index;
    },
    // 处理tab底下高亮标记位移
    handleLeftDistance(current) {
      let width = document.body.clientWidth;
      let length = this.tabList.length;
      let left = width / length / 2 - 6;
      let distance = current * (width / length);
      return `${left + distance}px`;
    },
    // 点击评论时先判断有没有评论的权限
    async clickComment() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      if (![1, 2].includes(parseInt(this.userInfo.auth_status))) {
        this.$toast.clear();
        this.$dialog({
          title: '提示',
          message: '评论失败：您还未完善实名认证信息！',
          confirmButtonText: '我知道了',
        });
        return;
      }
      try {
        const res = await ApiCommentClickComment({
          classId: 103,
          sourceId: this.detail.id,
        });

        if (res.data && res.data.length) {
          this.setCommentTypeList(res.data);
        }

        this.$toast.clear();
        this.toPage('CommentEditor', {
          source_id: this.detail.id,
          class_id: 103,
        });
      } catch (e) {
        if (e.code == 0) {
          // 不能评论，弹窗提示
          this.$toast.clear();
          this.$dialog({
            title: '提示',
            message: e.msg,
            confirmButtonText: '我知道了',
          });
        }
      }
    },
    // 获取收藏状态
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 3,
        sourceId: this.detail.id,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    // 设置收藏
    setCollectStatus() {
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;
      }
      ApiResourceCollect({
        classId: 3,
        sourceId: this.detail.id,
        status: status,
      }).then(res => {
        this.collected = res.data.status;
      });
    },
    // 获取详情页信息
    async getDetail() {
      try {
        const res = await ApiGameRead({
          id: this.$route.params.id,
        });
        this.noGame = false;
        this.detail = res.data.detail;
        this.cp_txt = res.data.detail.cp_txt;
        this.relatedList = res.data.recommend_games;
        // this.coupon = res.data.coupon;
        // this.rebateMax = res.data.rebate_max;
        // this.notice_list = res.data.tag_information;
        // this.act_count = res.data.act_count;
        // this.exclusive_benefits = res.data.exclusive_benefits;
        // this.card648 = res.data.card648;
        this.loadSuccess = true;
        // 如果有视频就播放 ===> 暂时改成一进来不自动播放
        if (this.detail.video_url) {
          this.$nextTick(() => {
            this.handlePlay();
          });
        }
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'GameNotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
          this.noGame = true;
        }
      } finally {
        this.$nextTick(() => {
          this.$toast.clear();
          if (!this.contentHeight1 && this.$refs.content1) {
            this.contentHeight1 = this.$refs.content1.clientHeight;
          }
          if (!this.contentHeight2 && this.$refs.content2) {
            this.contentHeight2 = this.$refs.content2.clientHeight;
          }
          if (!this.contentHeight3 && this.$refs.content3) {
            this.contentHeight3 = this.$refs.content3.clientHeight;
          }

          if (this.contentHeight1 > 100) {
            this.isAll1 = false;
          }
          if (this.contentHeight2 > 100) {
            this.isAll2 = false;
          }
          if (this.contentHeight3 > 60) {
            this.isAll3 = false;
          }

          if (this.$refs.vipTable) {
            this.vipHeight = this.$refs.vipTable.clientHeight;
            if (this.vipHeight > 250) {
              this.vipOpen = false;
            }
          }
        });
      }
    },
    handlePlay() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
    handlePause() {
      this.$refs.videoPlayer.pause();
      this.isPlaying = false;
    },
    // 处理隐私权限
    handlePrivacyPermission() {
      if (this.detail.privacy_url) {
        this.toPage('Iframe', {
          url: this.detail.privacy_url,
          title: '隐私政策',
        });
      } else {
        this.$toast('开发者正在努力完善中...');
      }
    },
    // 处理游戏权限详情
    async handlePermission() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    toExternalGameDetail(id) {
      this.$router.push({
        name: 'ExternalGameDetail',
        params: {
          id,
        },
      });
    },
    // 预约
    async handleSubscribe(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        let status = Number(item.subscribed) == 0 ? 1 : -1;
        try {
          const res = await ApiGameSubscribe({ gameId: item.id, status });
          if (res.code > 0) {
            item.subscribed = status == 1 ? 1 : 0;
            if (res.data && res.data.wxstatus == 1) {
              this.$dialog({
                title: this.$t('预约成功'),
                message: res.data.wxinfo,
                showCancelButton: true,
                cancelButtonText: this.$t('关闭'),
                confirmButtonText: this.$t('设置微信提醒'),
                lockScroll: false,
              }).then(() => {
                this.toPage('BindWeChat');
              });
            } else {
              this.$toast(res.msg);
            }
          }
        } catch (e) {}
      }
    },
    downloadBtn(link, id) {
      // 先判断是否登录，未登录则跳转登录页
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (this.downloadLoading1[id]) {
          return;
        }
        if (this.interstitial_ad_id) {
          (window.slotbydup = window.slotbydup || []).push({
            id: this.interstitial_ad_id,
            container: 'up-detail',
            async: true,
          });
        }
        if (!link) {
          this.$toast('暂无下载噢~');
          return;
        }
        // loading动画
        this.$set(this.downloadLoading1, id, true);
        setTimeout(() => {
          this.$set(this.downloadLoading1, id, false);
        }, 2000);
        window.location.href = link;
      }
    },
    downloadBtn(link, id) {
      if (this.downloadLoading1[id]) {
        return;
      }
      if (this.interstitial_ad_id) {
        (window.slotbydup = window.slotbydup || []).push({
          id: this.interstitial_ad_id,
          container: 'up-detail',
          async: true,
        });
      }
      if (!link) {
        this.$toast('暂无下载噢~');
        return;
      }
      // loading动画
      this.$set(this.downloadLoading1, id, true);
      setTimeout(() => {
        this.$set(this.downloadLoading1, id, false);
      }, 2000);
      window.location.href = link;
    },
    // 云手机
    async goToCloudDevices() {
      // await this.getEquipmentInventoryCapacity()
      await this.GetCloudMountedList();
    },
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
      setReceiveData: 'cloud_hangup/setReceiveData',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
      setCommentTypeList: 'comment/setCommentTypeList',
    }),
  },
  computed: {
    firstColumn() {
      return (this.relatedList || []).slice(0, 5);
    },
    secondColumn() {
      return (this.relatedList || []).slice(5, 10);
    },
    formattedTime() {
      return this.formatTime(this.detail.online_time, this.detail.truetime);
    },
    gameTitle() {
      return this.deviceListShowItem.game && this.deviceListShowItem.game?.title
        ? this.deviceListShowItem.game?.title
        : '';
    },
  },
};
</script>

<style lang="less" scoped>
.external-game-detail {
  .top-banner {
    flex-shrink: 0;
    box-sizing: border-box;
    width: 100%;
    height: 211 * @rem;
    background-color: #c5c5c5;
    .game-cover {
      overflow: hidden;
      width: 100%;
      height: 211 * @rem;
    }
  }
  .game-bar {
    padding: 16 * @rem 18 * @rem;
    display: flex;
    flex-direction: column;
    .game-info {
      display: flex;
      align-items: center;
      margin-bottom: 14 * @rem;
      .info-img {
        height: 64 * @rem;
        width: 64 * @rem;
        flex-shrink: 0;
      }
      .info-box {
        margin-left: 8 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        width: 100%;
        overflow: hidden;

        .title {
          height: 18 * @rem;
          font-weight: 600;
          font-size: 14 * @rem;
          color: #111111;
          line-height: 18 * @rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .center {
          > span:first-of-type {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #888888;
            line-height: 15 * @rem;
          }
          > span:last-of-type {
            margin-left: 10 * @rem;
            width: 31 * @rem;
            height: 17 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            color: #666666;
            line-height: 14 * @rem;
          }
        }
        .types {
          display: flex;
          align-items: center;
          .type {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #888888;
            line-height: 15 * @rem;
            position: relative;

            display: flex;
            align-items: center;
            &:first-child {
              padding: 0 5 * @rem 0 0;
            }
            &:not(:first-child) {
              padding: 0 5 * @rem;
            }
            &:not(:first-child)::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1 * @rem;
              height: 12 * @rem;
              background-color: #929292;
            }
          }
        }
      }
    }
    .cp_txt {
      margin: 0 0 13 * @rem 0;
      height: 15 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #333333;
      line-height: 15 * @rem;
    }
    .game-tag {
      display: flex;
      align-items: center;

      > span {
        height: 17 * @rem;
        border-radius: 4 * @rem;
        white-space: nowrap;
        border: 1px solid rgba(204, 204, 204, 0.5);
        height: 13 * @rem;
        line-height: 13 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #999999;
        padding: 2 * @rem 4 * @rem;
        text-decoration: line-through;
        &:not(:first-child) {
          margin-left: 11 * @rem;
        }
      }
    }
  }
  .bg-h8 {
    width: 100%;
    min-height: 8 * @rem;
    background: #f8f8f8;
  }
  .detail-content {
    .tabs {
      display: flex;
      position: relative;
      background-color: #fff;
      .tab {
        width: 187 * @rem;
        height: 44 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          display: block;
          font-size: 16 * @rem;
          font-weight: 400;
          color: #000000;
          position: relative;
        }
        .notice {
          position: absolute;
          left: 50%;
          top: 2 * @rem;
          transform: translateX(12 * @rem);
          height: 14 * @rem;
          line-height: 14 * @rem;
          background: #ff504f;
          color: #ffffff;
          font-size: 10 * @rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 4 * @rem;
          border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
        }
        &.active {
          span {
            font-size: 16 * @rem;
            font-weight: 500;
            color: @themeColor;
          }
        }
      }
      .tab-line {
        position: absolute;
        width: 12 * @rem;
        height: 4 * @rem;
        border-radius: 2 * @rem;
        background-color: @themeColor;
        left: 0;
        bottom: 0;
        transition: 0.3s;
      }
    }
    .content-container {
      .tab-content {
        /deep/ .van-loading {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &.tab-up {
          .up-game-item {
            margin: 20 * @rem 18 * @rem 20 * @rem;
          }
        }
        &.tab-kaifu {
          padding: 18 * @rem 0;
          .kaifu-section {
            .kaifu-tips {
              text-align: center;
            }
            .kaifu-active {
              width: 96 * @rem;
              height: 34 * @rem;
              .image-bg('~@/assets/images/games/kaifu-active.png');
              margin: 15 * @rem auto;
            }
          }
        }
        &.tab-detail {
          padding: 15 * @rem 0 * @rem;
          .notice-list {
            margin: 0 18 * @rem 18 * @rem;
            background: #f9f9f9;
            border-radius: 6 * @rem;
            .notice-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 10 * @rem;
              height: 40 * @rem;
              .notice-type {
                margin-right: 10 * @rem;
                font-size: 13 * @rem;
                font-weight: 600;
              }
              .notice-title {
                display: flex;
                justify-content: space-between;
                flex: 1;
                margin-left: 10 * @rem;
                color: #666;
                .notice-icon {
                  display: block;
                  width: 14 * @rem;
                  height: 14 * @rem;
                  .image-bg('~@/assets/images/more-right-arrow.png');
                }
              }
            }
          }
          .game-picture-swiper {
            box-sizing: border-box;
            width: 100%;
            overflow: hidden;
            .swiper-container {
              box-sizing: border-box;
              padding: 0 18 * @rem;
              width: 100%;
              .swiper-slide {
                width: auto;
                height: 150 * @rem;
                margin-left: 10 * @rem;
                border-radius: 8 * @rem;
                overflow: hidden;
                &:first-child {
                  margin-left: 0;
                }
                .slide-img {
                  width: auto;
                  height: 100%;
                }
                &.video-container {
                  width: 267 * @rem;
                  height: 150 * @rem;
                  overflow: hidden;
                  #video {
                    display: block;
                    width: 267 * @rem;
                    height: 150 * @rem;
                    outline: none;
                    border: none;
                  }
                  .mask {
                    width: 100%;
                    height: 150 * @rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    left: 0;
                    top: 0;
                    .fixed-center;
                    background-color: rgba(0, 0, 0, 0);
                    z-index: 10;
                    .play-btn {
                      width: 38 * @rem;
                      height: 38 * @rem;
                      background: url(~@/assets/images/video-play.png) no-repeat;
                      background-size: 38 * @rem 38 * @rem;
                    }
                  }
                }
              }
            }
          }
        }
        .kaifu-tip {
          margin-top: 20 * @rem;
          font-size: 12 * @rem;
          color: #ffa800;
          text-align: center;
        }
        .kaifu-list {
          margin-top: 10 * @rem;
          .kaifu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 17 * @rem 14 * @rem;
            border-top: 1px solid #e5e5e5;
            &:nth-of-type(1) {
              border-top: 0;
            }
            &.today {
              .left {
                .time {
                  color: #47a83a;
                }
              }
            }
          }
          .left {
            .time {
              font-size: 15 * @rem;
              color: #000000;
              display: flex;
              align-items: center;
              .hour {
                margin-left: 2 * @rem;
              }
            }
            .quhao {
              font-size: 13 * @rem;
              color: #666666;
              margin-top: 10 * @rem;
            }
          }
          .right {
            font-size: 15 * @rem;
            color: #666666;
          }
        }
        .more-text {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-top: 8 * @rem;
          span {
            font-size: 12 * @rem;
            color: @themeColor;
          }
          .more-text-icon {
            width: 11 * @rem;
            height: 7 * @rem;
            background: url(~@/assets/images/games/bottom-arrow-green.png)
              center center no-repeat;
            background-size: 11 * @rem 7 * @rem;
            margin-left: 4 * @rem;
            transition: 0.3s;
            &.on {
              transform: rotateZ(180deg);
            }
          }
        }
        .welfare-container {
          .wel-section {
            box-sizing: border-box;
            padding: 16 * @rem 12 * @rem;
            background: linear-gradient(360deg, #ffffff 0%, #fff5f3 100%);
            border-radius: 12 * @rem;
          }
          .content-text {
            font-size: 14 * @rem;
            color: #7a473b;
            line-height: 20 * @rem;
            display: -webkit-box;
            max-height: unset;
            height: auto;
            transition: 0.3s;

            &.on {
              max-height: 100 * @rem;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 5;
              overflow: hidden;
            }
          }
        }
        .game-rec-container {
          padding: 20 * @rem 0 0 0;
          .section-title {
            padding: 0 0 0 18 * @rem;
          }
          .deal-list {
            padding: 0 0 0 18 * @rem;
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: space-between;
            overflow-x: auto;
            .deal-column {
              display: flex;

              .deal-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 64 * @rem;
                flex-shrink: 0;
                overflow: hidden;

                .game-pic {
                  width: 64 * @rem;
                  height: 64 * @rem;
                }

                .game-name {
                  text-align: center;
                  width: 100%;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  font-size: 12 * @rem;
                  color: #000000;
                  line-height: 15 * @rem;
                  margin-top: 7 * @rem;
                  width: 64 * @rem;
                  height: 30 * @rem;
                  font-weight: 400;
                  font-size: 12 * @rem;
                  color: #333333;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  justify-content: flex-start;
                }

                .game-btn {
                  margin-top: 12 * @rem;
                  width: 64 * @rem;
                  height: 24 * @rem;
                  background: @themeBg;
                  border-radius: 19 * @rem;
                  text-align: center;
                  line-height: 24 * @rem;
                  font-size: 13 * @rem;
                  color: #ffffff;

                  &.loading {
                    position: relative;
                    font-size: 0;

                    &::after {
                      content: '';
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);
                      display: block;
                      width: 16 * @rem;
                      height: 16 * @rem;
                      background-size: 16 * @rem 16 * @rem;
                      background-image: url(~@/assets/images/downloadLoading.png);
                      animation: rotate 1s infinite linear;
                    }
                  }
                  &.reserve {
                    background: linear-gradient(
                      75deg,
                      #ff7a00 0%,
                      #ffb03a 100%
                    );
                  }
                  &.reserve-ok {
                    background: #bbb;
                    color: #fefefe;
                  }
                  &.open {
                    background: linear-gradient(
                      62deg,
                      #46a6ff 0%,
                      #3ac4ff 100%
                    );
                  }
                }
                &:not(:first-child) {
                  margin-left: 20 * @rem;
                }
                &:last-child {
                  padding-right: 18 * @rem;
                }
              }
              &:nth-child(2) {
                margin-top: 20 * @rem;
              }
            }
          }
          /* 浏览器滚动条隐藏 */
          * {
            scrollbar-width: none;
          }
          *::-webkit-scrollbar {
            display: none;
          }
          * {
            -ms-overflow-style: none;
          }
          * {
            -ms-overflow-style: none;
          }
          * {
            overflow: -moz-scrollbars-none;
            scrollbar-width: none;
          }
          * {
            overflow: -webkit-scrollbar;
          }
        }
        .category-tags {
          display: flex;
          align-items: center;
          padding: 12 * @rem 18 * @rem 0;
          overflow: hidden;
          .category-tag-item {
            white-space: nowrap;
            height: 23 * @rem;
            margin-right: 10 * @rem;
            font-size: 12 * @rem;
            color: #21b98a;
            padding: 0 7 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5 * @rem;
            border: 1px solid #12b289;
          }
        }
        .introduction {
          .introduction-text {
            font-size: 14 * @rem;
            color: #797979;
            line-height: 20 * @rem;
            display: -webkit-box;
            margin-top: 10 * @rem;
            max-height: unset;
            transition: 0.3s;
            &.on {
              max-height: 60 * @rem;
              height: 60 * @rem;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              overflow: hidden;
            }
          }
        }
        .version-section {
          .version-container {
            box-sizing: border-box;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 339 * @rem;
            border-radius: 12 * @rem;
            background: #f9f9f9;
            padding: 4 * @rem 12 * @rem 16 * @rem;
            .version-item {
              display: flex;
              margin-top: 12 * @rem;
              width: 50%;
              &.w100 {
                width: 100%;
              }
              .version-title {
                font-size: 13 * @rem;
                color: #828282;
                line-height: 18 * @rem;
                width: 70 * @rem;
                &.nowrap {
                  white-space: nowrap;
                  width: auto;
                }
              }
              .version-desc {
                font-size: 13 * @rem;
                color: #828282;
                line-height: 18 * @rem;
                .btn {
                  color: #000000;
                  padding-right: 10 * @rem;
                  background: url(~@/assets/images/games/arrow-right.png) right
                    center no-repeat;
                  background-size: 6 * @rem 10 * @rem;
                }
              }
              .requestUpdates {
                display: none;
                white-space: nowrap;
                margin-left: 12 * @rem;
                height: 18 * @rem;
                font-weight: 400;
                font-size: 12 * @rem;
                color: #ff7a00;
                line-height: 18 * @rem;
              }
            }
          }
        }
        .vip-table {
          .vip-desc {
            font-size: 14 * @rem;
            color: #797979;
            line-height: 20 * @rem;
          }
          .table-container {
            height: 250 * @rem;
            overflow: hidden;
            position: relative;
            &.open {
              height: auto;
            }
            .vip-open {
              box-sizing: border-box;
              padding-top: 20 * @rem;
              width: 100%;
              position: absolute;
              bottom: -1 * @rem;
              left: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 40 * @rem;
              font-size: 12 * @rem;
              color: @themeColor;
              background: linear-gradient(
                to top,
                rgba(255, 255, 255, 1) 20 * @rem,
                rgba(255, 255, 255, 0)
              );
              .more-text-icon {
                width: 11 * @rem;
                height: 7 * @rem;
                background: url(~@/assets/images/games/bottom-arrow-green.png)
                  center center no-repeat;
                background-size: 11 * @rem 7 * @rem;
                margin-left: 4 * @rem;
                transition: 0.3s;
                &.on {
                  transform: rotateZ(180deg);
                }
              }
            }
          }
          .table {
            box-sizing: border-box;
            width: 100%;
            margin-top: 10 * @rem;
            border-radius: 12 * @rem;
            border: 0.5 * @rem solid #d3bba2;
            overflow: hidden;
            .row {
              display: flex;
              &:nth-of-type(n + 2) {
                border-top: 0.5 * @rem solid #d3bba2;
              }
              &:nth-of-type(2n + 1) {
                background-color: #f5f5f6;
              }
            }
            .th,
            .td {
              width: 50%;
              text-align: center;
              font-size: 12 * @rem;
              color: #000000;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 11 * @rem 5 * @rem;
              &:nth-of-type(n + 2) {
                border-left: 0.5 * @rem solid #d3bba2;
              }
            }
            .th {
              background-color: #f5e2ce;
              font-size: 14 * @rem;
              color: #7a532a;
              font-weight: normal;
            }
          }
        }
        .hot-comments {
          .hot-comment-list {
            margin-top: -10 * @rem;
          }
          .comment-more {
            width: 170 * @rem;
            height: 40 * @rem;
            margin: 0 auto;
            background-color: #f5f5f6;
            border-radius: 20 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            .comment-more-text {
              font-size: 14 * @rem;
              color: #9a9a9a;
              font-weight: 400;
            }
            i {
              display: block;
              width: 6 * @rem;
              height: 10 * @rem;
              background: url(~@/assets/images/games/arrow-right-grey.png)
                center center no-repeat;
              background-size: 6 * @rem 10 * @rem;
              margin-left: 6 * @rem;
            }
          }
        }
        .detail-info {
          margin-top: 5 * @rem;
          .info-content {
            .info-item {
              height: 22 * @rem;
              display: flex;
              align-items: center;
              color: rgb(153, 153, 153);
              font-size: 12 * @rem;
              .info-text {
                margin-right: 20 * @rem;
              }
              span {
                color: #51adff;
                font-size: 12 * @rem;
                margin-right: 20 * @rem;
              }
            }
          }
          .detail-info-content {
            margin-top: 15 * @rem;
            background-color: #f8f8f8;
            border-radius: 5 * @rem;
            .info-list {
              display: flex;
              flex-wrap: wrap;
              .info-item {
                box-sizing: border-box;
                padding: 15 * @rem 20 * @rem;
                width: 50%;
                .info-name {
                  font-size: 14 * @rem;
                  color: #666666;
                  font-weight: bold;
                }
                .info-value {
                  font-size: 12 * @rem;
                  color: #b2b2b2;
                  font-weight: bold;
                  margin-top: 8 * @rem;
                }
              }
            }
          }
        }

        .related {
          margin-top: 5 * @rem;
          padding-right: 0;
          padding-left: 0;
          .section-title {
            padding-left: 18 * @rem;
          }
          .game-list {
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            margin-top: 15 * @rem;
            width: 100%;
            overflow-x: auto;
            padding-left: 18 * @rem;
            &::-webkit-scrollbar {
              display: none !important;
            }
            .game-group {
              &:not(:first-of-type) {
                margin-left: 10 * @rem;
              }
              &:last-of-type {
                padding-right: 18 * @rem;
              }
            }
            .game-item {
              box-sizing: border-box;
              width: 275 * @rem;
              height: 98 * @rem;
              padding: 0 14 * @rem;
              background-color: #f5f5f6;
              border-radius: 12 * @rem;
              display: flex;
              align-items: center;
              &:not(:first-of-type) {
                margin-top: 8 * @rem;
              }
              /deep/ .game-item-components .tags .tag {
                background-color: #ebebeb;
              }
            }
          }
        }
      }
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      box-shadow: 0 -3 * @rem 4 * @rem 0 rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .download-bar {
        flex: 1;
        min-width: 0;
        height: 60 * @rem;
        .download-content {
          height: 60 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .download-btn {
            .unit {
              font-size: 12 * @rem;
            }
          }
          .download-btn,
          .subscribe-btn {
            flex: 1;
            height: 44 * @rem;
            background: @themeBg;
            margin: 0 7 * @rem;
            text-align: center;
            line-height: 44 * @rem;
            color: #fefefe;
            font-size: 16 * @rem;
            font-weight: 500;
            border-radius: 8 * @rem;
            .question {
              position: absolute;
              top: 0;
              right: 0;
              display: block;
              width: 25 * @rem;
              height: 25 * @rem;
              background-size: 100%;
              background-repeat: no-repeat;
              background-image: url(~@/assets/images/games/download-question.png);
            }
            &.loading {
              position: relative;
              font-size: 0;
              .unit {
                font-size: 0;
              }
              &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: block;
                width: 16 * @rem;
                height: 16 * @rem;
                background-size: 16 * @rem 16 * @rem;
                background-image: url(~@/assets/images/downloadLoading.png);
                animation: rotate 1s infinite linear;
              }
            }
          }
          .h5-btn {
            flex: 1;
            height: 44 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fefefe;
            font-size: 16 * @rem;
            font-weight: 500;
            border-radius: 6 * @rem;
            background-color: #fe4a55;
            margin: 0 7 * @rem;
            .question {
              position: absolute;
              top: 0;
              right: 0;
              display: block;
              width: 25 * @rem;
              height: 25 * @rem;
              background-size: 100%;
              background-repeat: no-repeat;
              background-image: url(~@/assets/images/games/download-question.png);
            }
          }
          .download-no,
          .subscribe-no {
            background-color: #bbb;
            flex: 1;
            height: 44 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fefefe;
            font-size: 16 * @rem;
            font-weight: bold;
            border-radius: 6 * @rem;
            margin: 0 7 * @rem;
          }
        }
      }
      .comment {
        margin-right: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .comment-icon {
          width: 24 * @rem;
          height: 24 * @rem;
          .image-bg('~@/assets/images/games/comment-icon.png');
        }
        .comment-title {
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 500;
          margin-top: 5 * @rem;
        }
      }
    }
  }
  .collect-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    padding: 10 * @rem;
    background: url(~@/assets/images/games/collect-black.png) center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    &.collect-btn-white {
      background: url(~@/assets/images/games/collect.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.had {
      background-image: url(~@/assets/images/games/collect-success.png);
      background-size: 28 * @rem 28 * @rem;
    }
  }
  .section {
    padding: 20 * @rem 18 * @rem 0;
    .section-title {
      margin-bottom: 12 * @rem;
      &.mb0 {
        margin-bottom: 0;
      }
    }
  }
  .section-title {
    display: flex;
    justify-content: space-between;
    height: 25 * @rem;
    align-items: center;
    .title-icon {
      width: 22 * @rem;
      height: 22 * @rem;
      .image-bg('~@/assets/images/games/sun-icon.png');
      margin-right: 5 * @rem;
      &.icon-jieshao {
        .image-bg('~@/assets/images/games/icon-jieshao.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-xinxi {
        .image-bg('~@/assets/images/games/icon-xinxi.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-jiagebiao {
        .image-bg('~@/assets/images/games/icon-jiagebiao.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-pinglun {
        .image-bg('~@/assets/images/games/icon-pinglun.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-fuli {
        .image-bg('~@/assets/images/games/icon-fuli.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-xiangguan {
        .image-bg('~@/assets/images/games/icon-xiangguan.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-youxi {
        .image-bg('~@/assets/images/games/icon-youxi.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
    }
    .title-text {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      flex: 1;
      min-width: 0;
    }
    .title-right {
      display: flex;
      align-items: center;
      span {
        font-size: 15 * @rem;
        color: @themeColor;
      }
      .title-right-icon {
        width: 8 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/games/arrow-right-grey.png) center
          center no-repeat;
        background-size: 8 * @rem 12 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
}
.onhook-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 20 * @rem 15 * @rem 25 * @rem 15 * @rem;
  background-size: 300 * @rem auto;
  border-radius: 20 * @rem;
  z-index: 2996 !important;
  .title {
    text-align: center;
    font-weight: bold;
  }
  .content {
    line-height: 17 * @rem;
    font-size: 14 * @rem;
    color: #777777;
    padding: 0 25 * @rem;
    margin-top: 22 * @rem;
  }
  .btn-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 12 * @rem 0 0;
    .cancel,
    .confirm {
      width: 126 * @rem;
      height: 40 * @rem;
      border-radius: 30 * @rem;

      font-size: 13 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cancel {
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .confirm {
      margin-left: 17 * @rem;
      color: #ffffff;
      background: @themeBg;
    }
  }

  .tips {
    display: flex;
    align-items: center;
    margin: 10 * @rem auto 0;
    padding: 0 25 * @rem;
    .gou {
      width: 12 * @rem;
      height: 12 * @rem;
      border-radius: 12 * @rem;
      border: 1px solid #7d7d7d;
      &.remember {
        background: url('~@/assets/images/cloudHangup/bzts-img.png') no-repeat 0
          0;
        background-size: 14 * @rem 14 * @rem;
        width: 14 * @rem;
        height: 14 * @rem;
        border: none;
      }
    }
    .tips-text {
      font-size: 12 * @rem;
      color: #999999;
      margin-left: 6 * @rem;
    }
  }
}
.device-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .title-text {
      font-size: 14 * @rem;
      padding: 5 * @rem 0;
      text-align: left;
      box-sizing: border-box;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .device-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .device-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }
    .hangingUpName {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 14 * @rem;
      margin: 10 * @rem 0;
      padding: 0 18 * @rem;
      white-space: nowrap;
      overflow: hidden;
      > span {
        max-width: 70 * @rem;
        background: #fff;
      }
      .game-subtitle {
        max-width: 120 * @rem;
        box-sizing: border-box;
        padding: 2 * @rem 4 * @rem;
        height: 17 * @rem;
        border-radius: 4 * @rem;
        line-height: 17 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        > div {
          &.text-scroll {
            flex-shrink: 0;
            flex-grow: 1;
            white-space: nowrap;
            animation: scroll-left 6s linear forwards infinite;
          }
          @keyframes scroll-left {
            0% {
              transform: translateX(100%);
            }
            100% {
              transform: translateX(-100%);
            }
          }
        }
      }
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.permission-popup {
  max-height: 400 * @rem;
  min-height: 200 * @rem;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 16 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 60 * @rem;
    background-color: #fff;
    flex-shrink: 0;
  }
  .permission-list {
    padding: 0 14 * @rem 20 * @rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    .permission-item {
      padding: 10 * @rem;
      border-bottom: 1px solid #ebebeb;
      .name {
        font-size: 14 * @rem;
        color: #666;
      }
      .desc {
        font-size: 12 * @rem;
        color: #999;
        line-height: 18 * @rem;
        margin-top: 10 * @rem;
      }
    }
  }
}
.please-update-popup {
  width: 300 * @rem;
  .title {
    margin: 20 * @rem auto 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .please-update-input {
    display: block;
    box-sizing: border-box;
    padding: 7 * @rem 9 * @rem;
    margin: 0 auto;
    font-size: 16 * @rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    resize: none;
    line-height: 21 * @rem;
    width: 264 * @rem;
    height: 85 * @rem;
    border-radius: 6 * @rem 6 * @rem 6 * @rem 6 * @rem;
    border: 1 * @rem solid #efefef;
  }
  .button-container {
    margin: 15 * @rem auto;
    display: flex;
    justify-content: space-between;
    padding: 0 30 * @rem;
    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 110 * @rem;
      height: 35 * @rem;
      border-radius: 18 * @rem 18 * @rem 18 * @rem 18 * @rem;
    }
    .left {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .right {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #ffffff;
      background: @themeColor;
    }
  }
}
</style>
