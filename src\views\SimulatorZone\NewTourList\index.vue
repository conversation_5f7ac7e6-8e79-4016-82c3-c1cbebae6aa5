<template>
  <div class="developer-games-container">
    <nav-bar-2 :title="header_title" :placeholder="false" :border="true">
    </nav-bar-2>

    <yy-list
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh()"
      @loadMore="loadMore()"
      :empty="empty"
      :tips="tips"
    >
      <div class="other-game-list">
        <div
          class="other-game-item"
          v-for="(item, index) in resultList"
          :key="index"
        >
          <simulator-zone-tabs-item
            :info="item"
            :isShowBtn="true"
          ></simulator-zone-tabs-item>
        </div>
      </div>
    </yy-list>
    <div
      class="return-top"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>
  </div>
</template>

<script>
import { ApiSimulatorGetNewGameList } from '@/api/views/game.js';
import SimulatorZoneTabsItem from '../components/simulator-zone-tabs-item/index.vue';
export default {
  name: 'DevelopersOtherGames',
  components: { SimulatorZoneTabsItem },
  data() {
    return {
      header_title: localStorage.getItem('header_title') || '新游上线，抢先玩',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无游戏',
      showReturnTop: false,
    };
  },
  async created() {
    this.addScrollEvent();
  },
  beforeDestroy() {
    this.removeScrollEvent();
  },
  async mounted() {},
  async activated() {
    await this.getSimulatorGetNewGameList();
  },
  methods: {
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    async getSimulatorGetNewGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiSimulatorGetNewGameList({
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getSimulatorGetNewGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getSimulatorGetNewGameList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.developer-games-container {
  padding: 0 18 * @rem;
  margin-top: 50 * @rem;
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
  .game-list-box {
    .other-game-list {
      .other-game-item {
        margin-bottom: 20 * @rem;
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .return-top {
    position: fixed;
    right: 5 * @rem;
    bottom: 100 * @rem;
    width: 50 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/exclusive-activity/return-top.png) center
      center no-repeat;
    background-size: 50 * @rem 50 * @rem;
    z-index: 100;
  }
}
</style>
