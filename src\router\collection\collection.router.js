export default [
  {
    path: '/collection',
    name: 'Collection',
    redirect: '/collection/game_collection',
    component: () =>
      import(/* webpackChunkName: "collection" */ '@/views/Collection'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
    children: [
      {
        path: 'game_collection',
        name: 'GameCollection',
        component: () =>
          import(
            /* webpackChunkName: "collection" */ '@/views/Collection/GameCollection'
          ),
        meta: {
          keepAlive: true,
          requiresAuth: true,
          pageTitle: '我的收藏',
        },
      },
      {
        path: 'xiaohao_collection',
        name: 'XiaohaoCollection',
        component: () =>
          import(
            /* webpackChunkName: "collection" */ '@/views/Collection/XiaohaoCollection'
          ),
        meta: {
          keepAlive: true,
          requiresAuth: true,
          pageTitle: '我的收藏',
        },
      },
    ],
  },
];
