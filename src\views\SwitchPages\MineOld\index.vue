<template>
  <rubber-band :topColor="'#feffff'" :bottomColor="'#f5f5f6'">
    <div class="mine-page">
      <nav-bar-2
        title=""
        bgStyle="transparent"
        :placeholder="false"
        :bgColor="`rgba(255,255,255, ${navbarOpacity})`"
        :border="navbarOpacity ? true : false"
      >
        <template #left>
          <div class="page-title">{{ navbarOpacity ? $t('我的') : '' }}</div>
        </template>
        <template #right>
          <div class="top-icon-list">
            <div @click="goToSetting" class="icon setting"></div>
            <div @click="goToNotice" class="icon message">
              <div class="dot-red" v-if="unreadCount.sum > 0"></div>
              <!-- <div class="dot" v-if="unreadCount.sum > 0">
                {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
              </div> -->
            </div>
            <div @click="goToKefu" class="icon kefu"></div>
          </div>
        </template>
      </nav-bar-2>
      <div class="top-user-info">
        <div class="top">
          <status-bar></status-bar>
          <div class="icon-list"></div>
        </div>
        <div class="center">
          <div
            class="user-info"
            v-if="userInfo.username"
            @click.stop="goToUserInfo"
          >
            <user-avatar class="avatar"></user-avatar>
            <div class="detail">
              <div class="big-text">
                {{ userInfo.nickname }}
                <span @click.stop="goToExpHelp" class="item item1">{{
                  userInfo.exp_level_name
                }}</span
                ><span @click.stop="goToPayHelp" class="item item2">{{
                  userInfo.pay_level_name
                }}</span>
              </div>
              <div class="flex-container">
                <div class="flex-left">
                  <div class="small-text">
                    {{ $t('账号') }}：{{ userInfo.username }}
                  </div>
                  <div class="small-text">
                    {{ $t('实名') }}：{{ ifAuthStatus }}
                  </div>
                  <div class="small-text">
                    {{ $t('手机') }}：{{
                      userInfo.mobile ? userInfo.mobile : $t('未绑定')
                    }}
                  </div>
                </div>
                <div class="flex-right">
                  <div @click.stop="goToGoldCoinCenter" class="sign-in"></div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="user-info no-login"
            v-if="!userInfo.username"
            @click="goToLogin"
          >
            <user-avatar class="avatar"></user-avatar>
            <div class="detail">
              <div class="big-text">{{ $t('登录/注册') }}</div>
              <div class="small-text">{{ $t('立即登录体验更多服务') }}</div>
            </div>
          </div>
        </div>
        <div class="bottom"></div>
      </div>
      <section class="ptb-section">
        <div class="ptb-line">
          <div class="ptb-icon">
            <img src="@/assets/images/mine/ptb-icon.png" alt="" />
          </div>
          <div class="left">
            <div class="ptb-num">
              <span>{{ userInfo.nickname ? userInfo.ptb : 0 }}</span>
              <i v-if="userInfo.nickname && userInfo.ptb_fake"
                >（含{{ userInfo.ptb_fake }}绑定）</i
              >
            </div>
            <div class="left-title" @click="goToPlatformCoinDetail">
              <span>平台币</span><i></i>
            </div>
          </div>
          <div class="ptb-recharge" @click="goToPlatformCoin">去充值</div>
        </div>
        <div class="coupont-info">
          <div class="coupon-item" @click="goToMyCoupon">
            <span>{{ userInfo.nickname ? userInfo.coupon : 0 }}</span>
            {{ $t('代金券') }}
          </div>
          <div class="coupon-item" @click="goToGoldCoinCenter">
            <span>{{ userInfo.nickname ? userInfo.gold : 0 }}</span>
            {{ $t('金币') }}
            <!-- <i @click.stop="goToGoldCoinExchange">去使用&gt;</i> -->
          </div>
          <div class="coupon-item" @click="goToMyCashGift">
            <span>{{ userInfo.nickname ? userInfo.lijin : 0 }}</span>
            礼金
          </div>
        </div>
      </section>

      <div class="bottom">
        <div @click="goToSvip" class="bottom-item svip">
          <div class="big-text">{{ $t('SVIP会员') }}</div>
          <div v-if="!userInfo.sviptime" class="small-text">
            {{ initData.svip_desc }}<i class="icon"></i>
          </div>
          <div v-if="userInfo.sviptime" class="small-text">
            {{ $t('有效期至') }}：{{ svipTime }}
          </div>
        </div>
        <!-- 5.9这里需要改成省钱卡剩余时间，还没改 -->
        <div @click="goToSavingsCard" class="bottom-item changwan">
          <div class="big-text">省钱卡</div>
          <div v-if="!userInfo.sqk_remain_days" class="small-text">
            {{ initData.sqk_desc }}
          </div>
          <div v-if="userInfo.sqk_remain_days" class="small-text">
            {{ $t('剩余') }}：{{ userInfo.sqk_remain_days }}{{ $t('天') }}
          </div>
        </div>
      </div>
      <!-- <section
        v-if="configs.my_gold_img"
        class="ptb-exchange"
        @click="goToGoldCoinCenter()"
      >
        <img :src="configs.my_gold_img" />
      </section> -->
      <section
        v-if="bannerInfo.banner_url"
        class="banner-info"
        @click="handleActionCode(bannerInfo)"
      >
        <img :src="bannerInfo.banner_url" />
      </section>
      <section
        v-if="configs.clock_in"
        class="clock-challenge"
        @click="toPage(...functionCode[configs.clock_in.action])"
      >
        <img :src="configs.clock_in && configs.clock_in.img" alt="" />
      </section>
      <template v-for="(section, sectionIndex) in functionList">
        <section class="function-section" :key="sectionIndex">
          <div class="function-list">
            <div
              class="function-item"
              v-for="(item, index) in section"
              :key="index"
              @click="goToFunction(item)"
            >
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="center-text">
                <div class="text">{{ item.title }}</div>
                <div class="desc" v-if="item.desc">{{ item.desc }}</div>
              </div>
              <div class="right-icon"></div>
            </div>
          </div>
        </section>
      </template>

      <!-- 每日金币弹窗 -->

      <van-popup
        v-model="dailyGoldPopup"
        position="center"
        :lock-scroll="false"
        round
        class="daily-gold-container-popup"
        :close-on-popstate="true"
      >
        <div class="daily-gold-container">
          <div class="daily-gold-bg">
            <img src="@/assets/images/mine/daily-gold-bg.png" alt="" />
          </div>
          <div @click="dailyGoldPopup = false" class="close btn"></div>
          <div class="title">今日任务尚未完成</div>
          <div class="desc">
            完成任务可得
            <div class="gold-icon"></div>
            <span>{{ totalDayGold }}</span
            >金币
          </div>
          <div class="gold-btn" @click="goToTaskDaily">马上领取</div>
        </div>
      </van-popup>

      <change-href
        :show="!isWebApp && userInfo && userInfo.is_official"
      ></change-href>
      <div
        v-if="test"
        @click="
          toPage('Activity', {
            url: 'http://***********:8080/#/250101_activity',
          })
        "
        class="test"
      >
        这里是测试路口
      </div>
      <!-- <div v-if="test" @click="downloadH5Game" class="test">这里是测试路口</div> -->
      <!-- <a href="http://d2.xz3733.com/test/69730.mobileconfig">测试游戏</a> -->
    </div>
  </rubber-band>
</template>

<script>
import { downloadH5Game } from '@/utils/function.js';
import StatusBar from '@/components/status-bar';
import rubberBand from '@/components/rubber-band';
import { mapActions, mapGetters } from 'vuex';
import { BOX_openInNewNavWindow } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
import {
  ApiUserMyModular,
  ApiUserMyModularV2,
  ApiUserGetUserDayGoldTotal,
} from '@/api/views/users.js';
import { isWebApp } from '@/utils/userAgent.js';

export default {
  name: 'Mine',
  components: {
    StatusBar,
    rubberBand,
  },
  data() {
    return {
      isWebApp,
      navbarOpacity: 0,
      modularVersion: 0,
      //icon列表
      iconList: [],
      functionList: [],
      bannerInfo: {},
      iconListShow: true, //是否显示完全iconList
      isEditing: false,
      functionCode: {
        1: ['Requite'], // 六倍返还
        2: ['Recycle'], // 小号回收
        3: ['Rebate'], // 返利申请
        4: ['MyGift'], // 我的礼包
        5: ['TurnTable'], // 金币转盘
        6: ['Invite'], // 邀请赚佣金
        7: ['GoldMall'], // 金币商城
        8: ['Zhuanyou'], // 转游中心
        9: ['MyGame'], // 我的游戏
        10: ['XiaohaoManage'], // 小号管理
        11: ['Kefu'], // 联系客服
        12: ['Collection'], // 我的收藏
        13: ['Iframe', { title: '使用指南', url: this.$h5Page.shiyongzhinan }], // 使用指南
        // 14: [""], // 我的问答
        // 15: [""], // 我的发布
        16: ['Feedback'], // 投诉反馈
        17: ['BindWeChat'], // 微信提醒
        18: ['RoleTransfer'], // 交易
        19: ['MyRebate'], // 我的返利
        20: ['Questionnaire'], // 有奖调研
        21: ['PayHelp'], // 财富特权
        22: ['UpGame'], //我要Up
        23: ['FllowUp'], //关注Up主
        24: ['UpMine'], //我的主页(Up)
        25: ['ClockChallenge'], //打卡挑战
        26: ['OrderRecord'], //订单记录
        28: ['Svip'], // SVIP
        29: ['CloudHangup'], //云挂机
        30: ['AddAssistant'], //添加福利官
      },

      dailyGoldPopup: false, // 每日金币弹窗
      totalDayGold: 0, // 每日金币数量
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      showTransaction: 'system/showTransaction',
      configs: 'system/configs',
      initData: 'system/initData',
    }),
    // 实名制状态
    ifAuthStatus() {
      let res;
      switch (parseInt(this.userInfo.auth_status)) {
        case 0:
          res = this.$t('未实名');
          break;
        case 1:
          res = this.$t('审核中');
          break;
        case 2:
          res = this.$t('已认证');
          break;
        case 3:
          res = this.$t('认证未通过');
          break;
        default:
          res = this.$t('未实名');
          break;
      }
      return res;
    },
    // svip剩余时间
    svipTime() {
      const time = this.$handleTimestamp(this.userInfo.sviptime);
      return `${time.year}-${time.date}`;
    },
    // 测试路口
    test() {
      if (process.env.NODE_ENV == 'development') {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
    this.$dragging.$on('dragged', value => {});
    this.$dragging.$on('dragend', value => {});
  },
  async activated() {
    await this.getFunctionList();
    await this.checkDailyGold();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    handleActionCode,
    downloadH5Game,
    goToSetting() {
      this.CLICK_EVENT('M5_SETTING');
      this.toPage('UserInfo');
    },
    goToNotice() {
      this.CLICK_EVENT('M5_MSG');
      this.toPage('Notice');
    },
    goToKefu() {
      this.CLICK_EVENT('M5_KF');
      this.toPage('Kefu');
    },
    goToUserInfo() {
      this.CLICK_EVENT('M5_SELF');
      this.toPage('UserInfo');
    },
    goToLogin() {
      this.CLICK_EVENT('M5_LOGIN');
      this.toPage('UserInfo');
    },
    goToExpHelp() {
      this.CLICK_EVENT('M5_LEVEL');
      this.toPage('ExpHelp');
    },
    goToPayHelp() {
      this.CLICK_EVENT('M5_MONEY');
      this.toPage('PayHelp');
    },
    goToGoldCoinCenter() {
      this.CLICK_EVENT('M5_SIGNIN');
      this.toPage('GoldCoinCenter');
    },
    goToPlatformCoinDetail() {
      this.CLICK_EVENT('M5_PTBDTL');
      this.toPage('PlatformCoinDetail');
    },
    goToPlatformCoin() {
      this.CLICK_EVENT('M5_PAYPTB');
      this.toPage('PlatformCoin');
    },
    goToMyCoupon() {
      this.CLICK_EVENT('M5_COUPON');
      this.toPage('MyCoupon');
    },
    goToGoldCoin() {
      this.CLICK_EVENT('M5_GOLDDTL');
      this.toPage('GoldCoin');
    },
    goToGoldCoinExchange() {
      this.CLICK_EVENT('M5_USEGOLD');
      this.toPage('GoldCoinExchange');
    },
    goToMyCashGift() {
      this.CLICK_EVENT('M5_CASHGIFT');
      this.toPage('MyCashGift');
    },
    goToSvip() {
      this.CLICK_EVENT('M5_SVIP');
      this.toPage('Svip');
    },
    goToSavingsCard() {
      this.CLICK_EVENT('M5_SAVING');
      this.toPage('SavingsCard');
    },
    clickGoldAd() {
      this.CLICK_EVENT('M5_GOLDADV');
      this.toPage('GoldCoinExchange');
    },
    goToFunction(item) {
      if (item.click_id) {
        this.CLICK_EVENT(item.click_id);
      }
      this.toPage(...this.functionCode[item.action]);
    },
    async checkDailyGold() {
      if (this.userInfo.token) {
        await this.SET_USER_INFO(true);
        let nowDate = new Date().getDate();
        if (nowDate != localStorage.getItem('dailyGoldPopup')) {
          const res = await ApiUserGetUserDayGoldTotal();
          this.totalDayGold = res.data.total_day_gold;
          this.dailyGoldPopup = true;
          localStorage.setItem('dailyGoldPopup', nowDate);
        }
      }
    },
    async getFunctionList() {
      const res = await ApiUserMyModularV2();
      this.functionList = res.data.list_v2;
      this.bannerInfo = res.data.user_ad || {};
    },
    // 点击测试链接
    clickTest() {
      this.toPage('WeeklyRewards');
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    toPage(name, params = {}) {
      switch (name) {
        case 'UpMine':
          params = { mem_id: this.userInfo.user_id };
          break;
      }
      this.$router.push({ name: name, params: params });
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 50) {
        this.navbarOpacity = 1;
      } else {
        this.navbarOpacity = 0;
      }
    },
    goToTaskDaily() {
      this.dailyGoldPopup = false;
      this.toPage('GoldCoin');
    },
  },
};
</script>

<style lang="less" scoped>
.mine-page {
  flex: 1;
  flex-shrink: 0;
  background: #f5f5f6;
  .page-title {
    font-size: 18 * @rem;
    color: #000000;
    padding: 0 2 * @rem;
  }
  .top-icon-list {
    display: flex;
    flex-direction: row-reverse;
    .icon {
      width: 24 * @rem;
      height: 24 * @rem;
      margin-left: 10 * @rem;
      background-size: 100%;
      background-repeat: no-repeat;
      &.kefu {
        background-image: url(~@/assets/images/mine/icon_kefu_new.png);
      }
      &.setting {
        background-image: url(~@/assets/images/mine/icon_setting_new.png);
      }
      &.message {
        background-image: url(~@/assets/images/mine/icon_message_new.png);
        position: relative;
        .dot {
          font-variant-numeric: tabular-nums;
          position: absolute;
          left: 50%;
          top: -5 * @rem;
          padding: 0 5 * @rem;
          height: 14 * @rem;
          border-radius: 7 * @rem;
          background-color: #fe4a55;
          color: #fff;
          font-size: 10 * @rem;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .dot-red {
          width: 5 * @rem;
          height: 5 * @rem;
          border-radius: 50%;
          border: 1 * @rem solid #fff;
          background-color: #ff0000;
          position: absolute;
          top: 2 * @rem;
          right: 2 * @rem;
        }
      }
    }
  }
}
.top-user-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 277 * @rem;
  background-image: url(~@/assets/images/mine/bg_user_new.png);
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  .icon-list {
    height: 44 * @rem;
  }
  .center {
    margin-left: 14 * @rem;
    .user-info {
      width: 100%;
      display: flex;
      &.no-login {
        .detail {
          .big-text {
            margin: 10 * @rem 0;
          }
        }
      }
      .avatar {
        width: 70 * @rem;
        height: 70 * @rem;
        border-radius: 50%;
      }
      .detail {
        flex: 1;
        min-width: 0;
        margin-left: 12 * @rem;
        color: #333333;
        .big-text {
          margin-bottom: 6 * @rem;
          font-size: 20 * @rem;
          white-space: nowrap;
          font-weight: bold;
          .item {
            margin-left: 6 * @rem;
            padding: 2 * @rem 8 * @rem;
            font-size: 10 * @rem;
            color: #fff;
            border-radius: 2 * @rem;
            &.item1 {
              background: #309af8;
            }
            &.item2 {
              background: #ed9239;
            }
          }
        }
        .small-text {
          margin-bottom: 4 * @rem;
          font-size: 11 * @rem;
          color: #666666;
        }
        .flex-container {
          display: flex;
          justify-content: space-between;
        }
      }
    }
    .sign-in {
      flex: 0 0 79 * @rem;
      width: 79 * @rem;
      height: 28 * @rem;
      background-image: url(~@/assets/images/mine/pic_sign_in_new.png);
      background-size: 100%;
      background-repeat: no-repeat;
      margin-top: 5 * @rem;
    }
  }
}
.bottom {
  display: flex;
  justify-content: space-between;
  height: 70 * @rem;
  margin: 0 10 * @rem 10 * @rem;
  .bottom-item {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 172 * @rem;
    height: 70 * @rem;
    padding: 3 * @rem 0 0 10 * @rem;
    background-size: 100%;
    background-repeat: no-repeat;
    &.svip {
      .image-bg('~@/assets/images/mine/svip-bg-new.png');
      .big-text {
        color: #b1736a;
        font-size: 17 * @rem;
        font-weight: bold;
        line-height: 24 * @rem;
      }
      .small-text {
        color: #cc9483;
        font-size: 11 * @rem;
        line-height: 15 * @rem;
        margin-top: 5 * @rem;
        white-space: nowrap;
      }
    }
    &.changwan {
      .image-bg('~@/assets/images/mine/savings-bg-new.png');
      .big-text {
        color: #6a76b1;
        font-size: 17 * @rem;
        font-weight: bold;
        line-height: 24 * @rem;
      }
      .small-text {
        color: #838ecc;
        font-size: 11 * @rem;
        line-height: 15 * @rem;
        margin-top: 5 * @rem;
        white-space: nowrap;
      }
    }
  }
}
section {
  box-sizing: border-box;
  width: 355 * @rem;
  margin: 10 * @rem auto 0;
  background-color: #fff;
  border-radius: 12 * @rem;
  .title-bar {
    display: flex;
    align-items: center;
    border-bottom: 0.5 * @rem solid #f3f3f8;
    justify-content: space-between;
    padding: 0 12 * @rem;
    .edit-btn {
      width: 42 * @rem;
      height: 19 * @rem;
      border: 0.5 * @rem solid #616470;
      background-color: #f6f6f6;
      border-radius: 12 * @rem;
      font-size: 12 * @rem;
      color: #616470;
      display: flex;
      align-items: center;
      justify-content: center;
      &.editing {
        background-color: #ff7554;
        border-color: #ff7554;
        color: #ffffff;
      }
    }
  }
  .title {
    font-family:
      PingFangSC-Semibold,
      PingFang SC;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #242831;
    height: 44 * @rem;
    display: flex;
    align-items: center;
  }
  .icon-list {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    .icon-item {
      flex: 0 0 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 7 * @rem;
      padding-bottom: 5 * @rem;
      user-select: none;
      border-radius: 4 * @rem;
      position: relative;
      .drag-tag {
        font-size: 12 * @rem;
        height: 18 * @rem;
        border-radius: 9 * @rem;
        padding: 0 4 * @rem;
        color: #fff;
        transform: scale(0.67);
        transform-origin: left top;
        background-color: #ff7554;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 50%;
        top: 5 * @rem;
      }
      .icon {
        display: block;
        width: 46 * @rem;
        height: 46 * @rem;
        &.swing {
          animation: swing 0.4s infinite;
          animation-timing-function: ease-in-out;
        }
      }
      .text {
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        margin-top: 2 * @rem;
        font-size: 12 * @rem;
        color: #000000;
        font-weight: 400;
        line-height: 17 * @rem;
      }
    }
  }
  .arrow {
    width: 16 * @rem;
    height: 10 * @rem;
    margin: 5 * @rem auto 0;
    padding: 10 * @rem;
    background-image: url(~@/assets/images/mine/icon_arrow_up.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 16 * @rem 10 * @rem;
    transform: rotate(180deg);
    &.down {
      transform: rotate(0deg);
    }
  }
}
.icon-section {
  margin-bottom: 10 * @rem;
  padding-bottom: 5 * @rem;
}
.ptb-section {
  width: 100%;
  height: 168 * @rem;
  margin-top: -90 * @rem;
  background: url(~@/assets/images/mine/recharge-banner-bg.png) center center
    no-repeat;
  background-size: 100% 168 * @rem;
}
.ptb-line {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 74 * @rem;
  padding: 0 30 * @rem 0 26 * @rem;
  .ptb-icon {
    width: 39 * @rem;
    height: 41 * @rem;
  }
  .left {
    flex: 1;
    min-width: 0;
    margin-left: 10 * @rem;
    .left-title {
      margin-top: 5 * @rem;
      display: flex;
      align-items: center;
      span {
        display: block;
        font-size: 11 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
      }
      i {
        display: block;
        width: 6 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/mine/right-icon-white.png) no-repeat
          right center;
        background-size: 6 * @rem 10 * @rem;
        margin-left: 5 * @rem;
      }
    }
  }
  .ptb-num {
    margin-top: 0 * @rem;
    display: flex;
    align-items: center;
    span {
      font-size: 20 * @rem;
      line-height: 24 * @rem;
      color: #ffffff;
      margin-right: 2 * @rem;
      vertical-align: -1 * @rem;
      font-weight: 600;
    }
    i {
      height: 14 * @rem;
      margin-top: 2 * @rem;
      display: block;
      font-size: 11 * @rem;
      color: #ffffff;
      margin-top: 4 * @rem;
    }
  }
  .ptb-recharge {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68 * @rem;
    height: 28 * @rem;
    font-size: 14 * @rem;
    color: #ffffff;
    background-color: #171717;
    border-radius: 14 * @rem;
  }
}
.coupont-info {
  height: 79 * @rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  text-align: center;
  .coupon-item {
    flex: 1;
    color: #666666;
    font-size: 12 * @rem;
    position: relative;
    padding: 0 5 * @rem;
    &:not(:first-of-type) {
      &::before {
        content: '';
        width: 1 * @rem;
        height: 15 * @rem;
        border-radius: 0.5 * @rem;
        background-color: #cfcfcf;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    span {
      display: block;
      cursor: pointer;
      color: #111111;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      font-size: 20 * @rem;
      margin-bottom: 5 * @rem;
    }
    i {
      color: @themeColor;
    }
  }
}
.sign-bg {
  width: 355 * @rem;
  height: 95 * @rem;
  margin: 10 * @rem auto;
  background-image: url(~@/assets/images/mine/bg_sign_in.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
.test {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44 * @rem;
  font-size: 14 * @rem;
  text-decoration: underline;
  color: #a4a4a4;
}

.function-section {
  .function-list {
    padding: 10 * @rem;
    .function-item {
      padding: 2 * @rem 0;
      display: flex;
      align-items: center;
      &:not(:last-of-type) {
        border-bottom: 0.5 * @rem solid #f3f3f8;
      }
      .icon {
        width: 46 * @rem;
        height: 46 * @rem;
      }
      .center-text {
        flex: 1;
        min-width: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 6 * @rem;
        margin-right: 10 * @rem;
        .text {
          font-size: 15 * @rem;
          color: #555555;
        }
        .desc {
          color: @themeColor;
          font-size: 13 * @rem;
        }
      }
      .right-icon {
        width: 8 * @rem;
        height: 12 * @rem;
        margin-right: 10 * @rem;
        .image-bg('~@/assets/images/mine/function-right-icon.png');
      }
    }
  }
}
@keyframes swing {
  0%,
  100% {
    transform: rotate(-10deg);
  }
  50% {
    transform: rotate(10deg);
  }
}

.daily-gold-container-popup {
  .daily-gold-container {
    position: relative;
    padding-bottom: 20 * @rem;
    .daily-gold-bg {
      width: 246 * @rem;
      height: 115 * @rem;
    }
    .close {
      position: absolute;
      top: 10 * @rem;
      right: 10 * @rem;
      width: 24 * @rem;
      height: 24 * @rem;
      .image-bg('~@/assets/images/close-black-oo.png');
    }
    .title {
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      margin-top: 7 * @rem;
      font-weight: 600;
      text-align: center;
    }
    .desc {
      font-size: 15 * @rem;
      color: #8d8d8d;
      line-height: 19 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10 * @rem;
      .gold-icon {
        width: 18 * @rem;
        height: 18 * @rem;
        background: url('~@/assets/images/mine/daily-gold-icon.png') no-repeat;
        background-size: 18 * @rem 18 * @rem;
        margin-left: 6 * @rem;
      }
      span {
        color: @themeColor;
        font-weight: 600;
        margin-left: 4 * @rem;
      }
    }
    .gold-btn {
      margin: 28 * @rem auto 0;
      width: 205 * @rem;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      background: @themeBg;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: 500;
    }
  }
}
</style>
