// 旧:
const GAME_DETAIL = 1000; //需要传extra_id(游戏id)
// const GAME_LIST      = 1002;//需要传extra_id(游戏tag),text1(游戏类型名)
// const GAME_GIFT_LIST = 1004;//礼包列表
const WEB = 1005; //需要传web_url
// const HOT_ACTIVITY   = 1006;//热门活动
// const SPEEDUP        = 1009;//加速版
// const DYNAMIC        = 1010;//动态
// const SUBSCRIBE      = 1011;//新游预约
// const EXCHANGE       = 1015;//限时兑换
// const MISSION        = 1016;//赚金任务
// const TRADE          = 1017;//账号交易
const FUN_CARD = 1018; //畅玩卡
// const MOD_GAME       = 1019;//MOD游戏
// const RECOMMEND      = 1020;//推荐
// const VIDEO          = 1021;//视频
const JIANLOU = 1022; //捡漏
const CONPON = 1023; //领券中心
const PTB_RECHARGE = 1024; //平台币充值
// const OPEN_SERVICE = 1025; //开服列表
const NEW_GAME = 1026; //新游首发
// const RANKING        = 1027;//排行榜
const CARD_HUB = 1028; //礼包中心
const GOLD_TURNTABLE = 1029; //大转盘
const ZERO_GAME = 1030; //0元畅玩
const ZHUANYOU = 1031; // 转游中心
const GOLD_MALL = 1032; // 金币商城
const GOLD_DUOBAO = 1033; // 金币夺宝
const GAME_COLLECT = 1034; // 游戏合集
const UP_GAME = 1035; // UP资源
const ACTION_RECYCLE = 1048; // 小号回收

// 新
// const AC_ZXW                  = 1;//在线玩
// const AC_NOT_ROOT             = 2;//免ROOT
// const AC_GAME                 = 3;//游戏碎片二级页面
// const AC_ZXW_XY               = 4;//在线玩-新游
// const AC_ZXW_BD               = 5;//在线玩-榜单
// const AC_ZXW_FL               = 6;//在线玩-福利
// const AC_ZXW_LB               = 7;//在线玩-礼包
// const AC_ZJW                  = 8;//在线玩-最近玩过
// const AC_ONLINE_NEW_SUBSCRIBE = 9;//网游版-新游预约
// const AC_ONLINE_LB            = 10;//网游版-礼包
// const AC_ONLINE_LQ            = 11;//网游版-领券
// const AC_INDEX_CATE           = 12;//首页-分类  //extra_id 首页tab下标
// const AC_HALL_OF_FAME         = 13;//跳转地址全屏
// const AC_ZORE_GAME            = 14;//福利中心0元首充
// const AC_CLOSED_BETA          = 15;//内测招募
// const AC_TRIAL_PLAY           = 16;//试玩
// const AC_SANDBOX              = 17;//沙盒
// const QQ_GAME                 = 18;//QQ小游戏
// const JPYX_GAME               = 19;//精品小游戏
const CARD648 = 20; //648礼包
const OPENING_ACCOUNT = 21; //开局号
const BOUNTY_TASK = 22; //赏金任务
const GAME_SIGN_IN = 23; //游戏签到
const GAME_SIGN_IN_MORE = 24; //游戏签到更多
const MONEY_SAVING_TIPS = 31; // 省钱秘籍
