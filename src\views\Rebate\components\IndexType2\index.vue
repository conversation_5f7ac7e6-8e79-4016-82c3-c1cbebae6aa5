<template>
  <div class="index-type2">
    <Empty :tips="$t('暂无返利申请')" v-if="list.length === 0" />
    <div class="content" v-else>
      <div
        v-for="(item, index) in list"
        :key="index"
        @click="toDetail(item)"
        class="item"
      >
        <img :src="item.titlepic" class="left" />
        <div class="center">
          <div class="name">{{ item.title }}</div>
          <div class="small-text">
            {{ $t('申请状态') }}：<span :style="{ color: item.status_color }">{{
              item.status_str
            }}</span>
          </div>
          <div class="small-text">
            {{ $t('申请金额') }}: {{ item.amount }}元({{ item.game_area
            }}{{ $t('服') }})
          </div>
        </div>
        <div class="right">{{ $t('申请详情') }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import Empty from '@/components/content-empty';
import { ApiRebateMine } from '@/api/views/rebate';

export default {
  name: 'IndexType2',
  data() {
    return {
      list: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const res = await ApiRebateMine();
      this.list = res.data.list;
    },
    toDetail(item) {
      this.$router.push({ name: 'RebateDetail', query: { ...item } });
    },
  },
  components: {
    Empty,
  },
};
</script>
<style lang="less" scoped>
.index-type2 {
  .min-height-safa-top(94 * @rem);
  .content {
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60 * @rem;
      padding: 18 * @rem 14 * @rem;
      .left {
        width: 60 * @rem;
        height: 60 * @rem;
        border-radius: 10px;
        background: #eeeeee;
        overflow: hidden;
      }
      .center {
        flex: 1;
        display: flex;
        height: 100%;
        box-sizing: border-box;
        padding: 3 * @rem 0;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 10 * @rem;
        overflow: hidden;
        .name {
          width: 100%;
          font-size: 15 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .small-text {
          font-size: 12 * @rem;
          color: #666666;
          span {
            color: #f6b609;
          }
        }
      }
      .right {
        width: 75 * @rem;
        height: 28 * @rem;
        line-height: 28 * @rem;
        text-align: center;
        color: #fff;
        background: @themeBg;
        font-size: 15 * @rem;
        border-radius: 4 * @rem;
      }
    }
  }
}
</style>
