<template>
  <div class="game-tester">
    <nav-bar-2
      :title="'游戏内测员'"
      v-if="navBgTransparent"
      :bgStyle="'transparent-white'"
    >
    </nav-bar-2>
    <nav-bar2 :title="'游戏内测员'" v-else></nav-bar2>
    <div class="bg1"></div>
    <div class="big-title">成为内测员，可以获得</div>
    <div class="section1">
      <div class="top">
        <div class="item">
          <div class="icon icon1"></div>
          <div class="text">内测专属代金券</div>
        </div>
        <div class="item">
          <div class="icon icon2"></div>
          <div class="text">内测礼包</div>
        </div>
        <div class="item">
          <div class="icon icon3"></div>
          <div class="text">线上特权</div>
        </div>
      </div>
      <div class="bottom">
        内测员，邀你品玩鉴赏游戏！成为内测员，领专属福利助力体验哦~
      </div>
    </div>
    <div class="section2">
      <swiper
        :options="swiperOption"
        v-if="swiper_list.length > 0"
        class="swiper-list"
      >
        <!-- slides -->
        <swiper-slide
          v-for="(item, index) in swiper_list"
          :key="index"
          class="swiper-no-swiping"
        >
          <div class="text">
            恭喜{{ item.nickname }}成为内测员，领取专属福利
          </div>
        </swiper-slide>
      </swiper>
    </div>
    <div class="big-title2">选择一款游戏，成为内测员并领取专属礼物吧</div>
    <template v-if="game.list.length">
      <div v-for="(item, index) in game.list" class="section3" :key="index">
        <div class="top">
          <div class="container">
            <img
              :src="item.game_info && item.game_info.titlepic"
              class="game-img"
            />
            <div class="content">
              <div class="big-text">
                请体验游戏：{{ item.game_info && item.game_info.title }}
              </div>
              <div class="small-text">
                已招募内测员：<span>{{ item.test_mem_count }}</span>
              </div>
            </div>
          </div>
          <div class="line"></div>
        </div>
        <div class="bottom">
          <div class="small-title">为游戏内测员们准备了以下专属礼</div>
          <div class="list">
            <div class="item" v-if="item.coupon_info.sum">
              <div class="icon icon1"></div>
              <div class="content">
                <div class="type">内测专属代金券</div>
                <div class="text">{{ item.coupon_info.sum }}元代金券</div>
              </div>
              <div
                :class="{ empty: item.coupon_info.is_take }"
                @click="handleCouponButton(item)"
                class="right btn"
              >
                {{ item.coupon_info.is_take ? '已领取' : '领取' }}
              </div>
            </div>
            <div
              v-for="(item2, index2) in item.card_info"
              class="item"
              :key="index2"
            >
              <div class="icon icon2"></div>
              <div class="content">
                <div class="type">{{ item2.class_name }}</div>
                <div class="text">{{ item2.cardbody }}</div>
              </div>
              <div
                :class="{ empty: item2.remain == 0 }"
                @click="handleGiftButton(item, item2)"
                class="right btn"
              >
                {{
                  item2.remain == 0
                    ? '已抢光'
                    : item2.cardpass
                      ? '复制'
                      : '领取'
                }}
              </div>
            </div>
            <div class="item">
              <div class="icon icon3"></div>
              <div class="content">
                <div class="type">线上特权</div>
                <div class="text">{{ item.privilege }}</div>
              </div>
              <div
                :class="{ empty: item.is_test }"
                @click="handlePrivilegeButton(item)"
                class="right btn"
              >
                {{ item.is_test ? '已领取' : '领取' }}
              </div>
            </div>
          </div>
          <div
            @click="handleJoinTester(item)"
            :class="{
              empty: !item.is_test && item.load == item.test_mem_count,
            }"
            class="bottom-button btn"
          >
            {{
              item.is_test
                ? '下载'
                : item.load == item.test_mem_count
                  ? '人员已满'
                  : '加入内测员'
            }}
          </div>
          <!-- 底下逻辑好像有问题，应该要先判断是否已经是测试员了，如果是测试员就不需要管是不是名额已满 -->
          <!-- <div
            @click="handleJoinTester(item)"
            :class="{ empty: item.load == item.test_mem_count }"
            class="bottom-button btn"
          >
            {{
              item.load == item.test_mem_count
                ? "人员已满"
                : item.is_test
                ? "下载"
                : "加入内测员"
            }}
          </div> -->
        </div>
      </div>
    </template>

    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xiaohao.popup"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohao.list_show = !xiaohao.list_show">
              <span>{{ xiaohao.current_item.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohao.list_show }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohao.list_show }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohao.list"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="xiaohao.popup = false">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="handleSelectXhConfirm">
            {{ $t('确定') }}
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="gift.popup"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">{{ $t('礼包码') }}</div>
      </div>
      <div class="cardpass">{{ gift.info.cardpass }}</div>
      <div class="desc">
        {{ `${$t('使用说明')}：${gift.info.cardtext}` }}
      </div>
      <div class="copy-btn btn" @click="copy(gift.info.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
  </div>
</template>
<script>
import {
  ApiInternalTestList,
  ApiInternalTestCoupon,
  ApiInternalTestMemList,
  ApiInternalParticipateTest,
} from '@/api/views/game.js';
import {
  ApiXiaohaoMyListByGameId,
  ApiXiaohaoCreate,
} from '@/api/views/xiaohao.js';
import { ApiCardGet } from '@/api/views/gift.js';

export default {
  data() {
    return {
      navBgTransparent: true, //title背景
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 3,
        speed: 800,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      }, //swiper参数
      swiper_list: [], // swiper数组
      game: {
        list: [],
        current_item: {},
      }, //游戏数组
      button_text: ['加入内测员', '下载', '人员已满'],
      xiaohao: {
        popup: false, //弹窗
        list: [], //列表
        list_show: false, //展示列表
        current_item: {}, //高亮项
        type: 1, //确认键执行内容1领取代金券2领取礼包
      },
      gift: {
        popup: false,
        info: {},
        current_item: {},
      },
      rewardType: 1, // 点击的奖励类型 1 => 代金券 2 => 礼包
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.init();
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async init() {
      const res = await ApiInternalTestList();
      this.game.list = res.data.list;
      const res2 = await ApiInternalTestMemList();
      this.swiper_list = res2.data.list;
    },
    // 处理滚动
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 处理代金券按钮
    async handleCouponButton(item) {
      this.rewardType = 1;
      if (item.coupon_info.is_take) {
        return false;
      }
      if (!item.is_test) {
        this.$toast('加入内测员即可领取专属福利哦~');
        return false;
      }
      this.game.current_item = item;
      const res = await ApiXiaohaoMyListByGameId({
        gameId: item.game_info?.id ?? 0,
      });
      if (!res.data.list.length) {
        const res2 = await ApiXiaohaoCreate({
          appId: item.game_info?.app_id,
        });
        this.xiaohao.list = res2.data.list;
        this.xiaohao.current_item = this.xiaohao.list[0];
        this.handleGetCoupon();
      } else {
        this.xiaohao.list = res.data.list;
        this.xiaohao.current_item = this.xiaohao.list[0];
        this.xiaohao.type = 1;
        this.xiaohao.popup = true;
      }
    },
    // 处理礼包按钮
    async handleGiftButton(item, item2) {
      this.rewardType = 2;
      if (item2.remain == 0) {
        return false;
      }
      if (!item.is_test) {
        this.$toast('加入内测员即可领取专属福利哦~');
        return false;
      }
      if (item2.cardpass) {
        this.gift.info = item2;
        this.gift.popup = true;
        return false;
      }
      this.game.current_item = item;
      this.gift.current_item = item2;
      const res = await ApiXiaohaoMyListByGameId({
        gameId: item.game_info?.id ?? 0,
      });
      if (!res.data.list.length) {
        const res2 = await ApiXiaohaoCreate({
          appId: item.game_info?.app_id,
        });
        this.xiaohao.list = res2.data.list;
        this.xiaohao.current_item = this.xiaohao.list[0];
        this.this.handleGetGift();
      } else {
        this.xiaohao.list = res.data.list;
        this.xiaohao.current_item = this.xiaohao.list[0];
        this.xiaohao.type = 2;
        this.xiaohao.popup = true;
      }
    },
    // 获取礼包
    async handleGetGift() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (this.gift.current_item.remain == 0) {
          this.$toast(this.$t('礼包已被抢光啦~'));
          return false;
        }
        this.$toast.loading({
          message: this.$t('加载中...'),
        });
        await ApiCardGet({
          cardId: this.gift.current_item.id,
          xhId: this.xiaohao.current_item.id,
        }).then(
          async res => {
            this.$toast.clear();
            this.gift.info = res.data;
            this.xiaohao.popup = false;
            this.gift.popup = true;

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${this.gift.current_item.game_id}`,
              adv_id: '暂无',
              game_name: this.gift.current_item.titlegame,
              game_type: `${this.gift.current_item.classid}`,
              game_size: '暂无',
              reward_type: this.gift.current_item.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });

            await this.init();
          },
          () => {},
        );
      }
    },
    // 点击选择小号
    xiaoHaoListClick(item) {
      this.xiaohao.current_item = item;
      this.xiaohao.list_show = false;
    },
    // 处理获取全部代金券
    async handleGetCoupon() {
      const res = await ApiInternalTestCoupon({
        id: this.game.current_item?.id,
        xh_id: this.xiaohao.current_item?.id,
      });
      if (res.code > 0 && res.msg) {
        this.$toast(res.msg);
        this.init();
        this.xiaohao.popup = false;
      }
    },
    // 处理特权按钮
    handlePrivilegeButton(item) {
      if (!item.is_test) {
        this.$toast('加入内测员即可领取专属福利哦~');
        return false;
      }
    },
    // 处理小号确认键
    async handleSelectXhConfirm() {
      if (this.rewardType == 1) {
        await this.handleGetCoupon();
      } else {
        await this.handleGetGift();
      }
    },
    // 处理加入游戏测试员
    async handleJoinTester(item) {
      if (item.is_test) {
        this.toPage('GameDetail', { id: item.game_info?.id });
        return false;
      }
      if (item.load == item.test_mem_count) {
        return false;
      }
      const res = await ApiInternalParticipateTest({ id: item.id });
      this.$toast(res.msg);
      this.init();
    },
    copy(text) {
      if (!text) {
        this.$toast('复制失败，没有内容');
        return false;
      }
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.gift.popup = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.game-tester {
  min-height: 100vh;
  .image-bg('~@/assets/images/game-tester/tester_bg1.png');
  background-color: #222242;
  overflow: hidden;
  .big-title {
    margin-top: 170 * @rem;
    font-size: 14 * @rem;
    text-align: center;
    font-weight: 600;
    color: #ffffff;
  }
  .section1 {
    margin: 12 * @rem auto 15 * @rem;
    width: 339 * @rem;
    box-sizing: border-box;
    padding: 20 * @rem 20 * @rem 10 * @rem;
    background: #434368;
    border-radius: 12 * @rem;
    .top {
      display: flex;
      justify-content: center;
      .item {
        width: 108 * @rem;
        .icon {
          width: 60 * @rem;
          height: 60 * @rem;
          margin: 0 auto 5 * @rem;
          &.icon1 {
            .image-bg('~@/assets/images/game-tester/tester_icon1.png');
          }
          &.icon2 {
            .image-bg('~@/assets/images/game-tester/tester_icon2.png');
          }
          &.icon3 {
            .image-bg('~@/assets/images/game-tester/tester_icon3.png');
          }
        }
        .text {
          text-align: center;
          color: #fff;
          font-size: 12 * @rem;
          line-height: 15 * @rem;
          margin-top: 4 * @rem;
        }
      }
    }
    .bottom {
      margin-top: 15 * @rem;
      line-height: 18 * @rem;
      color: #cca05f;
      font-size: 10 * @rem;
      color: #cca05f;
      text-align: center;
    }
  }
}
.swiper-list {
  width: 339 * @rem;
  height: 84 * @rem;
  .image-bg('~@/assets/images/game-tester/tester_bg3.png');
  /deep/ .swiper-wrapper {
    transition-timing-function: linear !important;
  }
  .text {
    text-align: center;
    color: #fff;
  }
}
.big-title2 {
  margin: 40 * @rem auto 12 * @rem;
  font-size: 14 * @rem;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
}
.section3 {
  width: 339 * @rem;
  box-sizing: border-box;
  margin: 0 auto 20 * @rem;
  color: #fff;
  .top {
    height: 98 * @rem;
    box-sizing: border-box;
    padding: 20 * @rem 20 * @rem 0;
    .image-bg('~@/assets/images/game-tester/tester_bg2.png');
    .container {
      display: flex;
      .game-img {
        flex: 0 0 50 * @rem;
        width: 50 * @rem;
        height: 50 * @rem;
        border-radius: 5 * @rem;
      }
      .content {
        flex: 1;
        margin-left: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 4 * @rem 0;
        overflow: hidden;
        .big-text {
          font-size: 14 * @rem;
          font-family:
            PingFang SC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .small-text {
          font-size: 12 * @rem;
          color: #ffffff;
          span {
            color: #ff4d00;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 1 * @rem;
      margin-top: 18 * @rem;
      background: linear-gradient(
        90deg,
        rgba(159, 101, 255, 0) 0%,
        #8080b6 35%,
        #8282ba 66%,
        rgba(233, 233, 233, 0) 100%
      );
      border-radius: 0 * @rem;
      opacity: 0.4;
    }
  }
  .bottom {
    background-color: #434368;
    overflow: hidden;
    padding: 0 20 * @rem 20 * @rem;
    border-radius: 0 0 15 * @rem 15 * @rem;
    .small-title {
      margin: 10 * @rem 0;
      text-align: center;
      color: #cdb99b;
    }
    .list {
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 14 * @rem;
        .icon {
          width: 46 * @rem;
          height: 46 * @rem;
          &.icon1 {
            .image-bg('~@/assets/images/game-tester/tester_icon1.png');
          }
          &.icon2 {
            .image-bg('~@/assets/images/game-tester/tester_icon2.png');
          }
          &.icon3 {
            .image-bg('~@/assets/images/game-tester/tester_icon3.png');
          }
        }
        .content {
          flex: 1;
          margin: 0 10 * @rem 0 15 * @rem;
          height: 46 * @rem;
          .type {
            display: inline-block;
            padding: 3 * @rem 5 * @rem;
            background: #e0b67b;
            border-radius: 4 * @rem;
            font-size: 10 * @rem;
            color: #8b5400;
          }
          .text {
            height: 24 * @rem;
            line-height: 12 * @rem;
            font-size: 10 * @rem;
            color: #fff;
            margin-top: 5 * @rem;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
        }
        .right {
          flex: 0 0 48 * @rem;
          width: 48 * @rem;
          height: 22 * @rem;
          background: linear-gradient(180deg, #9fa3ff 9%, #8f98ee 97%);
          border-radius: 31 * @rem;
          color: #fff;
          text-align: center;
          line-height: 22 * @rem;
          &.empty {
            background: #bdbde1;
          }
        }
      }
    }
    .bottom-button {
      width: 200 * @rem;
      height: 32 * @rem;
      background: linear-gradient(180deg, #9fa3ff 9%, #8f98ee 97%);
      border-radius: 31 * @rem;
      text-align: center;
      line-height: 32 * @rem;
      font-size: 14 * @rem;
      font-family:
        PingFang SC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #ffffff;
      margin: 0 auto;
      &.empty {
        background: #bdbde1;
      }
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 244 * @rem;
  border-radius: 12 * @rem;
  background-color: #fff;
  padding: 20 * @rem 16 * @rem 22 * @rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .title-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      .image-bg('~@/assets/images/games/gift-title-icon.png');
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 4 * @rem;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 209 * @rem;
    height: 39 * @rem;
    background-color: #f4f4f4;
    border-radius: 6 * @rem;
    display: flex;
    align-items: center;
    padding: 0 10 * @rem;
    margin-top: 13 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    font-weight: 400;
    word-break: break-all;
  }
  .desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    font-weight: 400;
    margin-top: 13 * @rem;
    padding: 0 5 * @rem;
  }
  .copy-btn {
    width: 186 * @rem;
    height: 36 * @rem;
    margin: 20 * @rem auto 0;
    background: @themeBg;
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
