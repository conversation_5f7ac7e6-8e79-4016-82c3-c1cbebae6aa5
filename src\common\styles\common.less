html,
body {
  height: 100%;
  max-width: @maxWidth;
  margin: 0 auto;
}

// @media screen and (min-width: 640px) {
//   html,body {
//     max-width: @pcmaxwidth;
//   }
// }

body {
  color: #333;
  line-height: 1.2;
  font-size: 12 * @rem;
  text-align: justify;
  position: relative;
}

img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

[v-cloak] {
  display: none !important;
}

div {
  user-select: none;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
}

// .switch-page {
//   display: flex;
//   flex-direction: column;
//   height: 100vh;
//   box-sizing: border-box;
//   padding-bottom: calc(50 * @rem + @safeAreaBottom);
//   padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
// }
.btn {
  position: relative;

  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    opacity: 0;
    content: ' ';
  }

  &:active::before {
    opacity: 0.1;
  }

  // position: relative;
  // &:active:before {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   width: 100%;
  //   height: 100%;
  //   background-color: rgba(50, 50, 50, 0.1);
  //   border-radius: inherit;
  // }
}

.van-button::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: #000;
  border: inherit;
  border-color: #000;
  border-radius: inherit;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  content: ' ';
}

input:disabled {
  background: #fff;
}

.van-toast--text {
  z-index: 9999999 !important;
}

.van-toast__icon {
  font-size: 36 * @rem  !important;
}

.van-dialog {
  width: 300 * @rem;
}

.van-dialog.portrait {
  top: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
}

.van-toast.portrait {
  top: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
}

.van-dialog__cancel,
.van-dialog__cancel:active {
  color: #50525A;
}

.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #161823; 

  .van-dialog__confirm {
    font-weight: bold;
  }
}
.van-dialog__header {
  font-weight: bold;
  font-size: 18px;
}

.van-image-preview__index {
  top: unset !important;
  bottom: 16px;
}

.van-nav-bar {
  z-index: 10 !important;
}

// 50*@rem以内margin和padding的less变量
each(range(50), {
    .ml-@{value} {
      margin-left: (@value * @rem) !important;
    }

    .mt-@{value} {
      margin-top: (@value * @rem) !important;
    }

    .mb-@{value} {
      margin-bottom: (@value * @rem) !important;
    }

    .mr-@{value} {
      margin-right: (@value * @rem) !important;
    }

    .mx-@{value} {
      margin: 0 (@value * @rem) !important;
    }

    .my-@{value} {
      margin: (@value * @rem) 0 !important;
    }

    .m-@{value} {
      margin: (@value * @rem) !important;
    }

    .pl-@{value} {
      padding-left: (@value * @rem) !important;
    }

    .pt-@{value} {
      padding-top: (@value * @rem) !important;
    }

    .pb-@{value} {
      padding-bottom: (@value * @rem) !important;
    }

    .pr-@{value} {
      padding-right: (@value * @rem) !important;
    }

    .px-@{value} {
      padding: 0 (@value * @rem) !important;
    }

    .py-@{value} {
      padding: (@value * @rem) 0 !important;
    }

    .p-@{value} {
      padding: (@value * @rem) !important;
    }
  }

)