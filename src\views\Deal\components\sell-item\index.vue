<template>
  <div class="deal-item-component">
    <div class="deal-item" v-if="xiaohaoInfo.id">
      <div class="top-content">
        <div class="top-left">
          <div
            class="status"
            :style="{ color: `${xiaohaoInfo.status_info.color}` }"
          >
            {{ xiaohaoInfo.status_info.str || '' }}
          </div>
        </div>

        <div class="top-right">
          <div class="game-money"
            >{{ $t('实充￥') }}{{ xiaohaoInfo.pay_sum }}</div
          >
        </div>
      </div>
      <div
        class="bottom-content"
        @click="toPage('XiaohaoDetail', { id: xiaohaoInfo.id })"
      >
        <div class="xiaohao-info">
          <div class="game-icon">
            <img :src="xiaohaoInfo.game.titlepic" alt="" />
          </div>
          <div class="center">
            <div class="game-name">
              {{ xiaohaoInfo.game.main_title
              }}<span class="game-subtitle" v-if="xiaohaoInfo.game.subtitle">{{
                xiaohaoInfo.game.subtitle
              }}</span>
            </div>
            <div class="center-info">
              <div class="server">
                {{ $t('区服') }}：{{ xiaohaoInfo.game_area }}
              </div>
              <div class="platforms">
                <div
                  class="plat"
                  v-for="(item, index) in xiaohaoInfo.platforms"
                  :key="index"
                >
                  <img :src="item.icon" />
                </div>
              </div>
            </div>
            <div class="xh-id">{{ $t('小号ID') }}：{{ xiaohaoInfo.xh_id }}</div>
          </div>
          <div class="right">
            <div class="ptb-num">{{ xiaohaoInfo.gain_ptb }}</div>
            <div class="ptb-text">{{ $t('平台币') }}</div>
          </div>
        </div>
        <div class="specify-man" v-if="xiaohaoInfo.specify_username">
          {{ $t('指定玩家') }}：{{ xiaohaoInfo.specify_username }}
        </div>
      </div>
      <div class="remark" v-if="xiaohaoInfo.remark">
        {{ xiaohaoInfo.remark }}
      </div>
      <div class="bottom-btn-group">
        <div
          class="operate-btn edit-btn"
          v-if="statusIn([1, 2, 4])"
          @click="handleEdit()"
        >
          {{ $t('编辑') }}
        </div>
        <div
          class="operate-btn publish-btn"
          v-if="statusIn([2, 4])"
          @click="handlePublish()"
        >
          {{ $t('上架') }}
        </div>
        <div
          class="operate-btn unpublish-btn"
          v-if="statusIn([3])"
          @click="handleUnpublish()"
        >
          {{ $t('下架') }}
        </div>
        <div
          class="operate-btn cancel-btn"
          v-if="statusIn([1, 2, 3, 4])"
          @click="handleCancel()"
        >
          {{ $t('取消') }}
        </div>
        <div
          class="operate-btn delete-btn"
          v-if="
            statusIn([5]) ||
            (statusIn([7]) && xiaohaoInfo.mem_id == userInfo.user_id)
          "
          @click="handleDelete()"
        >
          {{ $t('删除') }}
        </div>
      </div>
    </div>
    <!-- 只改价格 -->
    <van-dialog
      v-model="editPriceShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      get-container="body"
    >
      <div class="price-editor">
        <div class="edit-price">
          <div class="title">{{ $t('售价(元)') }}</div>
          <input
            type="number"
            :maxlength="6"
            class="input-text"
            :placeholder="pricePlaceholder"
            v-model="newPrice"
          />
        </div>
        <div class="edit-code">
          <input
            type="number"
            :maxlength="6"
            class="input-code"
            v-model="sellCode"
            :placeholder="$t('请输入验证码')"
          />
          <div class="get-code btn" v-if="!ifCount" @click="captchaClick">
            {{ $t('获取验证码') }}
          </div>
          <div class="get-code getting" v-else>
            {{ `${$t('已发送')}(${countdown}s)` }}
          </div>
        </div>
        <div class="change-btn btn" @click="changeConfirm">
          {{ $t('确认修改') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiXiaohaoChangeTradeStatus,
  ApiXiaohaoEditTradePrice,
  ApiXiaohaoTradeIndex,
} from '@/api/views/xiaohao.js';
import { mapGetters, mapMutations } from 'vuex';
import { ApiAuthCode } from '@/api/views/users';
export default {
  name: 'SellItemComponent',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      xiaohaoInfo: this.info,
      editPriceShow: false,
      timer: null,
      countdown: 60,
      ifCount: false,
      sellCode: '',
      newPrice: '',
      pricePlaceholder: '',
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
      initData: 'system/initData',
    }),
  },
  watch: {
    info(val) {
      this.xiaohaoInfo = val;
    },
  },
  created() {
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    statusIn(statusArr) {
      let index = statusArr.findIndex(item => {
        return Number(this.xiaohaoInfo.status) == item;
      });
      return index == -1 ? false : true;
    },
    async changeConfirm() {
      if (!this.newPrice) {
        this.$toast(this.$t('请输入价格！'));
        return false;
      }
      if (!this.sellCode) {
        this.$toast(this.$t('请输入验证码！'));
        return false;
      }
      // 请求接口修改价格
      const res = await ApiXiaohaoEditTradePrice({
        ssId: this.xiaohaoInfo.ss_id,
        price: this.newPrice,
        smsCode: this.sellCode,
      });
      this.editPriceShow = false;
      this.toPage('XiaohaoDetail', { id: this.xiaohaoInfo.id });
    },
    handleEdit() {
      let paramInfo = this.xiaohaoInfo;
      if (this.xiaohaoInfo.is_h5 && this.xiaohaoInfo.play_from) {
        paramInfo = {
          ...this.xiaohaoInfo,
          play_from: {
            id: this.xiaohaoInfo.play_from,
            name: this.xiaohaoInfo.play_from_name,
          },
        };
      }
      if (this.xiaohaoInfo.status == 1) {
        // 编辑信息
        // 审核失败的情况下直接进入编辑
        this.toPage('XiaohaoSellWrite', {
          info: paramInfo,
        });
      } else {
        this.$dialog
          .confirm({
            title: this.$t('提示'),
            message: this.$t('只改价格无需重新审核，编辑其他信息需要重新审核'),
            confirmButtonColor: 'black',
            confirmButtonText: this.$t('编辑信息'),
            cancelButtonText: this.$t('只改价格'),
            lockScroll: false,
          })
          .then(() => {
            // 编辑信息
            this.toPage('XiaohaoSellWrite', {
              info: paramInfo,
            });
          })
          .catch(() => {
            // 只改价格
            this.getSellPriceRule();
            this.editPriceShow = true;
          });
      }
    },
    // 可设置的最小价格规则
    async getSellPriceRule() {
      const res = await ApiXiaohaoTradeIndex();
      this.pricePlaceholder = res.data?.trade_price_text;
    },
    handlePublish() {
      this.$dialog
        .confirm({
          message: this.$t('确认上架？'),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeTradeStatus(3);
          this.toPage('XiaohaoDetail', { id: this.xiaohaoInfo.id });
        });
    },
    handleUnpublish() {
      this.$dialog
        .confirm({
          message: this.$t(
            '下架后，您可以重新上架。若要将小号转回您的账号，请在下架后取消出售',
          ),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeTradeStatus(4);
          this.toPage('XiaohaoDetail', { id: this.xiaohaoInfo.id });
        });
    },
    handleCancel() {
      this.$dialog
        .confirm({
          message: this.$t(
            '取消出售后，小号将转回您的账号。如登录游戏未见小号，请退出账号重新登录。',
          ),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeTradeStatus(5);
          this.toPage('XiaohaoDetail', { id: this.xiaohaoInfo.id });
        });
    },
    handleDelete() {
      this.$dialog
        .confirm({
          message: this.$t('删除后无法恢复，确认删除？'),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeTradeStatus(100);
          this.jianlouInfo = {};
          this.$emit('afterDelete');
        });
    },
    async changeTradeStatus(status) {
      try {
        this.$toast({
          type: 'loading',
          duration: 0,
          message: this.$t('加载中...'),
        });
        // status   3=上架，4=下架，5=取消，100 = 删除
        const res = await ApiXiaohaoChangeTradeStatus({
          status: status,
          tradeId: this.xiaohaoInfo.id,
        });
      } finally {
        this.$toast.clear();
      }
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.userInfo.mobile === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      let params = { phone: this.userInfo.mobile, type: 8 };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
  },
};
</script>

<style lang="less" scoped>
.deal-item-component {
  &:not(:first-child) {
    margin-top: 10 * @rem;
  }
  .deal-item {
    box-sizing: border-box;
    background: #ffffff;
    padding: 14 * @rem 12 * @rem;
    width: 351 * @rem;
    margin: 0 auto;
    border-radius: 12 * @rem;
    .top-content {
      display: flex;
      justify-content: space-between;
      .top-left {
        flex: 1;
        min-width: 0;
        .status {
          font-size: 14 * @rem;
          line-height: 22 * @rem;
        }
      }
      .top-right {
        display: flex;
        height: 15 * @rem;
        align-items: center;
        .game-money {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #fe6600;
        }
      }
    }
    .bottom-content {
      .xiaohao-info {
        display: flex;
        align-items: flex-start;
        padding: 12 * @rem 0 0;
        .game-icon {
          width: 72 * @rem;
          height: 72 * @rem;
          border-radius: 8 * @rem;
          background-color: #a0a0a0;
          overflow: hidden;
        }
        .center {
          height: 72 * @rem;
          flex: 1;
          min-width: 0;
          margin-left: 8 * @rem;
          .game-name {
            font-size: 15 * @rem;
            color: #30343b;
            font-weight: 600;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1px solid #e3e5e8;
              border-radius: 4 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 4 * @rem;
              color: #93999f;
              margin-left: 6 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .center-info {
            margin-top: 15 * @rem;
            display: flex;
            align-items: center;
            .server {
              white-space: nowrap;
              font-size: 12 * @rem;
              color: @themeColor;
            }
            .platforms {
              display: flex;
              align-items: center;
              .plat {
                width: 16 * @rem;
                height: 16 * @rem;
                margin-left: 6 * @rem;
              }
            }
          }
          .xh-id {
            font-size: 11 * @rem;
            margin-top: 8 * @rem;
            color: #93999f;
          }
        }
        .right {
          margin-top: 15 * @rem;
          width: 70 * @rem;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          margin-left: 5 * @rem;
          .ptb-num {
            font-size: 20 * @rem;
            color: @themeColor;
            font-weight: 600;
            white-space: nowrap;
          }
          .ptb-text {
            font-size: 12 * @rem;
            color: @themeColor;
            margin-top: 2 * @rem;
          }
          .platforms {
            display: flex;
            align-items: center;
            margin-top: 5 * @rem;
            .plat {
              width: 16 * @rem;
              height: 16 * @rem;
              margin-left: 6 * @rem;
            }
          }
        }
      }
      .specify-man {
        margin-top: 10 * @rem;
        font-size: 14 * @rem;
        color: #666;
      }
    }
    .remark {
      font-size: 13 * @rem;
      line-height: 16 * @rem;
      color: #9a9a9a;
      margin-top: 14 * @rem;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .bottom-btn-group {
      margin-top: 20 * @rem;
      display: flex;
      height: 28 * @rem;
      align-items: center;
      justify-content: flex-end;
      .operate-btn {
        box-sizing: border-box;
        padding: 0 17 * @rem;
        height: 28 * @rem;
        line-height: 28 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        border-radius: 14 * @rem;
        margin-left: 10 * @rem;
        border: 1px solid#C1C1C1;
        color: #9a9a9a;
      }
      .edit-btn {
        border: 0;
        color: #2bbe88;
        background: #ecfbf4;
      }
      .publish-btn,
      .unpublish-btn {
        border: 0;
        color: #ffffff;
        background: #1cce94;
      }
      .cancel-btn,
      .delete-btn {
        border: 0;
        color: #93999f;
        background: #f7f8fa;
      }
    }
  }
}
/deep/ .van-dialog {
  width: 300 * @rem !important;
}
.price-editor {
  box-sizing: border-box;
  padding: 20 * @rem 15 * @rem;
  .edit-price {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding: 11 * @rem 0;
    line-height: 30 * @rem;
    .title {
      font-size: 15 * @rem;
      color: #000000;
    }
    .input-text {
      display: block;
      flex: 1;
      min-width: 0;
      text-align: right;
      font-size: 15 * @rem;
    }
  }
  .edit-code {
    display: flex;
    align-items: center;
    padding: 10 * @rem 0;
    margin-top: 20 * @rem;
    border-bottom: 1px solid #e5e5e5;
    .input-code {
      flex: 1;
      min-width: 0;
    }
    .get-code {
      font-size: 13 * @rem;
      color: #60c055;
      width: 75 * @rem;
      height: 31 * @rem;
      border: 1px solid #60c055;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4 * @rem;
      &.getting {
        color: #ccc;
        border: 1px solid #ccc;
      }
    }
  }
  .change-btn {
    background: @themeBg;
    border-radius: 8 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    margin-top: 20 * @rem;
  }
}
</style>
