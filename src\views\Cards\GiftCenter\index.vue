<template>
  <div class="gift-center-page">
    <nav-bar-2 bgStyle="transparent" :placeholder="false"></nav-bar-2>
    <div class="gift-banner"></div>
    <div class="search-bar">
      <div class="search-text">
        <input
          type="text"
          v-model.trim="keyword"
          :placeholder="$t('输入游戏名、礼包内容')"
        />
      </div>
      <div class="clear" v-if="keyword" @click="handleClear"></div>
      <div class="search-btn" @click="clickSearch">{{ $t('搜索') }}</div>
    </div>
    <div class="gift-container">
      <content-empty v-if="empty"></content-empty>
      <load-more
        v-else
        class="list-container"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
      >
        <div class="gift-list" ref="list">
          <div
            class="gift-item"
            v-for="(item, index) in giftList"
            :key="`${item.id}-${index}`"
            @click="toPage('GameGift', { game_id: item.id })"
          >
            <div class="game-info">
              <div class="game-icon">
                <img :src="item.titlepic" alt="" />
              </div>
              <div class="game-name">{{ item.title }}</div>
            </div>
            <div class="gift-content">
              <div class="gift-text">{{ item.card_content }}</div>
              <div class="gift-get">{{ $t('领取') }}</div>
            </div>
          </div>
        </div>
      </load-more>
    </div>
  </div>
</template>

<script>
import { ApiCardCardList } from '@/api/views/card.js';
export default {
  data() {
    return {
      keyword: '',
      giftList: [],
      timer: null,
      loading: false,
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      this.timer = setTimeout(async () => {
        this.finished = false;
        this.empty = false;
        this.loading = true;
        this.giftList = [];
        await this.getGiftList();
        this.loading = false;
      }, 300);
    },
  },
  methods: {
    async clickSearch() {
      this.giftList = [];
      await this.getGiftList();
    },
    handleClear() {
      this.keyword = '';
    },
    async getGiftList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCardCardList({
        keyword: this.keyword,
        type: 0,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.giftList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.giftList.push(...list);
      this.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getGiftList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.gift-center-page {
  background-color: #ffffff;
  .gift-banner {
    width: 100%;
    height: 220 * @rem;
    .image-bg('~@/assets/images/cards/gift-banner.png');
  }
  .search-bar {
    margin: -75 * @rem auto 0;
    width: 339 * @rem;
    height: 38 * @rem;
    border-radius: 18 * @rem;
    background-color: #ffffff;
    overflow: hidden;
    display: flex;
    align-items: center;
    .search-text {
      flex: 1;
      min-width: 0;
      padding-left: 20 * @rem;
      height: 38 * @rem;
      input {
        display: block;
        width: 100%;
        height: 38 * @rem;
        font-size: 14 * @rem;
        color: #000000;
      }
    }
    .clear {
      width: 36 * @rem;
      height: 36 * @rem;
      background: url(~@/assets/images/cards/keyword-clear.png) center center
        no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    .search-btn {
      padding: 0 18 * @rem 0 10 * @rem;
      height: 38 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      color: #fe694a;
      position: relative;
      &::before {
        content: '';
        width: 2 * @rem;
        height: 15 * @rem;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        background-color: #ebebeb;
      }
    }
  }
  .gift-container {
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 20 * @rem 20 * @rem 0 * @rem 0 * @rem;
    margin-top: 18 * @rem;
    height: calc(100vh - 201 * @rem);
    overflow-y: auto;
    padding-top: 5 * @rem;
    .gift-list {
      padding: 0 18 * @rem;
      .gift-item {
        padding: 15 * @rem 0;
        &:not(:first-of-type) {
          border-top: 0.5 * @rem solid #f3f3f8;
        }
        .game-info {
          display: flex;
          align-items: center;
          .game-icon {
            width: 30 * @rem;
            height: 30 * @rem;
            border-radius: 4 * @rem;
            overflow: hidden;
          }
          .game-name {
            font-size: 16 * @rem;
            color: #000000;
            font-weight: 600;
            margin-left: 9 * @rem;
          }
        }
        .gift-content {
          display: flex;
          margin-top: 12 * @rem;
          .gift-text {
            flex: 1;
            min-width: 0;
            font-size: 13 * @rem;
            color: #909090;
            line-height: 22 * @rem;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            overflow: hidden;
          }
          .gift-get {
            width: 58 * @rem;
            height: 30 * @rem;
            background: linear-gradient(90deg, #ffa06c 0%, #ff5050 100%);
            border-radius: 6 * @rem;
            font-size: 13 * @rem;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 30 * @rem;
          }
        }
      }
    }
  }
}
</style>
