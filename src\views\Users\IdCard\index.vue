<template>
  <div class="id-card-page page">
    <nav-bar-2 :border="true" :title="$t('实名认证')">
      <template #right>
        <div @click="toPage('UploadIdCard')" class="right">
          {{ $t('图片认证') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="container">
      <div class="title">{{ $t('根据国家相关法律，需要进行实名认证') }}</div>
      <div class="change-content">
        <div class="form">
          <div class="field">
            <input
              type="text"
              v-model="realName"
              :placeholder="$t('请输入真实姓名')"
              :disabled="active"
            />
          </div>
          <div class="field">
            <input
              type="text"
              v-model="idCardNumber"
              :placeholder="$t('请输入身份证号码')"
              :disabled="active"
            />
          </div>
        </div>
      </div>
      <div class="save btn" @click="save" :class="{ active: active }">
        {{ activeText }}
      </div>
    </div>
    <div class="introduction">
      <div class="content">
        <p>
          1
          {{
            $t(
              '应国家未成年防沉迷政策要求，将限制未成年人每日游戏 时间，充值等，详情请查看',
            )
          }}<span @click="popupShow = true">《{{ $t('防沉迷政策') }}》</span>
        </p>
        <p>2 {{ $t('实名认证通过之后，不可更改，请如实填写') }}</p>
      </div>
    </div>
    <!-- 提交成功弹窗 -->
    <van-dialog
      v-model="dialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="false"
      class="dialog-show"
    >
      <div class="title">{{ $t('提交成功') }}</div>
      <div class="reward-pic">
        <img :src="authReward" alt="" />
      </div>
      <div class="tips">{{ $t('审核成功将自动发放认证奖励') }}</div>
      <div class="confirm-btn btn" @click="closeDialog">
        {{ $t('我知道了') }}
      </div>
    </van-dialog>
    <!-- 防沉迷弹出层 -->
    <van-popup v-model:show="popupShow" position="bottom" round>
      <div class="popup-content">
        <iframe :src="fcmUrl" frameborder="0"></iframe>
        <div @click="popupShow = false" class="button">{{ $t('确定') }}</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiUserCertification, ApiUserInfoEx } from '@/api/views/users';
import { mapGetters, mapMutations, mapActions } from 'vuex';

export default {
  name: 'IdCard',
  data() {
    return {
      realName: '',
      idCardNumber: '',
      auth_status: 0,
      active: false,
      dialogShow: false,
      authReward: '',
      popupShow: false, //防沉迷弹出层
      fcmUrl: '', //防沉迷url
    };
  },
  computed: {
    activeText() {
      let str = '';
      switch (parseInt(this.auth_status)) {
        case 1:
          str = this.$t('审核中');
          break;
        case 2:
          str = this.$t('已认证');
          break;
        default:
          str = this.$t('确认');
          break;
      }
      return str;
    },
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
      initData: 'system/initData',
    }),
  },
  created() {
    this.fcmUrl = this.initData.underage_privacy_url;
    this.auth_status = this.userInfoEx.auth_status;
    if (parseInt(this.auth_status) === 2 || parseInt(this.auth_status) === 1)
      this.active = true;
  },
  mounted() {
    if (this.userInfoEx.real_name != '') {
      this.realName = this.userInfoEx.real_name;
      this.idCardNumber = this.userInfoEx.id_card;
    }
  },
  methods: {
    ...mapMutations({
      setUserInfoEx: 'user/setUserInfoEx',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    save() {
      if (this.active) {
        return;
      }
      ApiUserCertification({
        realName: this.realName,
        idCard: this.idCardNumber,
      }).then(res => {
        // this.$toast(res.msg);
        //返回的奖励图
        this.authReward = res.data.img;
        this.dialogShow = true;
      });
    },
    closeDialog() {
      this.dialogShow = false;
      ApiUserInfoEx().then(res2 => {
        this.SET_USER_INFO();
        this.setUserInfoEx(res2.data);
        this.$router.go(-1);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.id-card-page {
  .dialog-show {
    box-sizing: border-box;
    width: 287 * @rem;
    background: #ffffff;
    border-radius: 16 * @rem;
    background-color: #fff;
    padding: 20 * @rem 0 24 * @rem;
    .title {
      font-size: 25 * @rem;
      color: @themeColor;
      font-weight: 600;
      text-align: center;
      line-height: 36 * @rem;
    }
    .reward-pic {
      width: 100%;
      height: 151 * @rem;
      margin-top: 8 * @rem;
    }
    .tips {
      font-size: 14 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      margin-top: 22 * @rem;
    }
    .confirm-btn {
      width: 238 * @rem;
      height: 44 * @rem;
      background: @themeColor;
      border-radius: 22 * @rem;
      margin: 25 * @rem auto 0;
      font-size: 15 * @rem;
      color: #ffffff;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .container {
    padding: 14 * @rem 14 * @rem 0;
    .title {
      margin: 24 * @rem 0 25 * @rem;
      font-size: 15 * @rem;
      color: #000000;
      line-height: 18 * @rem;
    }
    .tips {
      font-size: 13 * @rem;
      color: #666666;
      line-height: 20 * @rem;
      text-indent: 2em;
    }
    .change-content {
      .form {
        margin-top: 10 * @rem;
      }
      .field {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 45 * @rem;
        margin-bottom: 20 * @rem;

        input {
          width: 100%;
          height: 100%;
          padding: 0 10 * @rem;
          line-height: 44 * @rem;
          font-size: 16 * @rem;
          letter-spacing: 1 * @rem;
          background: #f5f5f6;
          border-radius: 6 * @rem;
        }
      }
    }
    .save {
      width: 347 * @rem;
      height: 44 * @rem;
      font-size: 16 * @rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6 * @rem;
      margin: 28 * @rem auto 33 * @rem;
      background: @themeBg;
      &.active {
        background: #c8c8c8;
      }
    }
  }
  .introduction {
    padding: 0 14 * @rem;
    .title {
      font-size: 12 * @rem;
      color: #666666;
      line-height: 22 * @rem;
    }
    .content {
      p {
        margin-bottom: 7 * @rem;
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        span {
          color: @themeColor;
          text-decoration: underline;
        }
      }
    }
  }
}
.popup-content {
  iframe {
    width: 100%;
    height: 450 * @rem;
  }
  .button {
    width: 339 * @rem;
    height: 44 * @rem;
    background: @themeBg;
    border-radius: 6 * @rem;
    color: #fff;
    line-height: 44 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    margin: 20 * @rem auto 30 * @rem;
  }
}
</style>
