<template>
  <div class="page">
    <nav-bar-2 title="交易记录" :border="true"> </nav-bar-2>

    <yy-list
      class="record-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
    >
      <template v-for="(item, index) in recordList">
        <div class="record-item" :key="index">
          <div class="item-main">
            <div class="record-pic">
              <img :src="item.game_icon" alt="" />
            </div>
            <div class="record-info">
              <div class="record-title">
                <div class="tag">开局号</div>
                <div class="name">{{ item.game_name }}</div>
              </div>
              <div class="area">区服：{{ item.area_text }}</div>
              <div class="order">订单号：{{ item.order_id }}</div>
            </div>
            <div class="pay-money">
              ¥<span>{{ item.amount }}</span>
            </div>
          </div>
          <div class="record-time">
            交易成功：{{ formatDate(item.create_time) }}
          </div>
        </div>
      </template>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoGetOpeningAccountLog } from '@/api/views/xiaohao.js';
export default {
  data() {
    return {
      gameId: 0,
      recordList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  async created() {
    this.gameId = this.$route.params.id;
    // console.log(this.gameId, this.getRecordList)
    await this.getRecordList();
  },
  methods: {
    formatDate(val) {
      let { year, month, day, time } = this.$handleTimestamp(val);
      return `${year}-${month}-${day} ${time}`;
    },
    async getRecordList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }

      const res = await ApiXiaohaoGetOpeningAccountLog({
        id: this.gameId,
        page: this.page,
        listRows: this.listRows,
      });

      if (action == 1 || this.page == 1) {
        this.recordList = [];
      }

      this.recordList.push(...res.data);
      if (!this.recordList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (res.data.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished == true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getRecordList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.recordList.length) {
        await this.getRecordList();
      } else {
        await this.getRecordList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .record-list {
    flex: 1;
    padding: 0 20 * @rem;
    margin-top: 10 * @rem;
    .record-item {
      padding: 16 * @rem 0;
      &:not(:first-of-type) {
        border-top: 0.5px solid #efefef;
      }
      .item-main {
        display: flex;
        align-items: center;
        .record-pic {
          width: 72 * @rem;
          height: 72 * @rem;
          border-radius: 12 * @rem;
          overflow: hidden;
        }
        .record-info {
          flex: 1;
          min-width: 0;
          margin-left: 8 * @rem;
          .record-title {
            display: flex;
            align-items: center;
            .tag {
              width: 38 * @rem;
              height: 16 * @rem;
              border: 0.5 * @rem solid #4579ff;
              border-radius: 4 * @rem;
              background: rgba(69, 121, 255, 0.05);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10 * @rem;
              color: #4579ff;
              margin-right: 6 * @rem;
              white-space: nowrap;
            }
            .name {
              font-size: 14 * @rem;
              color: #333333;
              font-weight: 500;
              line-height: 18 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .area {
            font-size: 12 * @rem;
            color: #777777;
            line-height: 15 * @rem;
            margin-top: 12 * @rem;
          }
          .order {
            font-size: 12 * @rem;
            color: #777777;
            line-height: 15 * @rem;
            margin-top: 12 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .pay-money {
          margin-left: 10 * @rem;
          display: flex;
          align-items: center;
          font-size: 12 * @rem;
          color: @themeColor;
          font-weight: 600;
          span {
            font-size: 22 * @rem;
          }
        }
      }
      .record-time {
        font-size: 14 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        margin-top: 16 * @rem;
      }
    }
  }
}
</style>
