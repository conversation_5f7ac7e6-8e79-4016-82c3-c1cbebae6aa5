<template>
  <div class="up-collection-page">
    <rubber-band :topColor="bgc" bottomColor="#ffffff">
      <div class="placeholder" :style="{ background: bgc }"></div>
      <div class="game-panel">
        <div
          class="top-shadow"
          :style="{
            background: topShadow,
          }"
        ></div>
      </div>
      <yy-list
        class="collection-container"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
        :tips="$t('暂无数据')"
      >
        <div class="collection-list">
          <up-collection-item
            v-for="(item, index) in dataList"
            :key="index"
            :info="item"
          ></up-collection-item>
        </div>
      </yy-list>
      <div class="add-collection" @click.stop="toPage('AddUpCollection')"></div>
    </rubber-band>
  </div>
</template>

<script>
import {
  ApiCollectGetUpCollectList,
  ApiCollectGetUpCollectInfo,
} from '@/api/views/upCollection.js';
import upCollectionItem from '@/components/up-collection-item';
export default {
  components: {
    upCollectionItem,
  },
  data() {
    return {
      bgc: '',
      dataList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  computed: {
    topShadow() {
      return `linear-gradient(360deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    hexToRgba() {
      return `rgba(${parseInt('0x' + this.bgc.slice(1, 3))},${parseInt(
        '0x' + this.bgc.slice(3, 5),
      )},${parseInt('0x' + this.bgc.slice(5, 7))},0)`;
    },
  },
  async created() {
    this.bgc = '#0989FF';
    await this.getDataList();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async getDataList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCollectGetUpCollectList({
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page == 1) {
        this.dataList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.dataList.push(...list);
      this.loadingObj.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getDataList();
      console.log('刷新');
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getDataList(2);
      console.log('加载更多');
    },
  },
};
</script>

<style lang="less" scoped>
.up-collection-page {
  min-height: 100vh;
  padding-bottom: 40 * @rem;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .placeholder {
    position: relative;
    display: block;
    width: 100%;
    height: calc(56 * @rem + @safeAreaTop);
    height: calc(56 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .game-panel {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 150 * @rem;
    padding-top: 45 * @rem;
    z-index: 1;
    .top-shadow {
      width: 100%;
      height: 150 * @rem;
      position: absolute;
      left: 0;
      top: -1 * @rem;
      z-index: 1;
    }
  }
  .collection-container {
    margin: -90 * @rem auto 0;
    z-index: 2;
    position: relative;
  }
}
.add-collection {
  width: 60 * @rem;
  height: 60 * @rem;
  position: fixed;
  right: 0;
  bottom: 200 * @rem;
  background: url('~@/assets/images/up-collection/add-collection.png') no-repeat;
  background-size: 60 * @rem 60 * @rem;
  z-index: 100;
}
</style>
