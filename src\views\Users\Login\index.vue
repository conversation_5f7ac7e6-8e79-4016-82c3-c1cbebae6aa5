<template>
  <div class="login-page" @click="showRemember = false">
    <nav-bar-2 :placeholder="false" bgStyle="transparent">
      <template #right>
        <div
          class="phone-login"
          @click="toPage('FastRegister')"
          v-if="[3].includes(Number(initData.login_type.type))"
        >
          快速注册
        </div>
        <div
          class="phone-login"
          @click="goToPhoneLogin"
          v-else-if="[0, 1, 2].includes(Number(initData.login_type.type))"
        >
          {{ $t('验证码登录') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="top-banner">
      <div class="page-title">{{ $t('密码登录') }}</div>
    </div>
    <div class="form">
      <div class="field">
        <input
          type="text"
          v-model="username"
          :placeholder="$t('请输入用户名/手机号')"
        />
        <div v-if="username != ''" class="clear" @click="username = ''"></div>
        <div
          class="down-arrow"
          :class="{ show: showRemember }"
          @click.stop="showRemember = !showRemember"
          v-if="rememberList.length"
        ></div>
        <div class="remember-list" v-if="showRemember">
          <div
            class="remember-item btn"
            v-for="item in rememberList"
            :key="item.username"
            @click.stop="selectRememberItem(item)"
          >
            <div class="remember-username">{{ item.username }}</div>
            <div class="remember-avatar">
              <img :src="item.avatar" alt="" />
            </div>
            <div
              class="clear-remember-item btn"
              @click.stop="clearRememberItem(item)"
            ></div>
          </div>
        </div>
      </div>
      <div class="field">
        <input
          :type="open ? 'text' : 'password'"
          v-model="password"
          :placeholder="$t('请输入密码')"
        />
        <div class="right">
          <div
            v-if="password.length > 0"
            class="eyes"
            :class="{ open: open }"
            @click="isShow()"
          ></div>
        </div>
      </div>
    </div>
    <div class="account-operation">
      <div
        class="remember-password"
        :class="{ remember: remember }"
        @click="remember = !remember"
      >
        {{ $t('保存密码') }}
      </div>
      <div class="forge-password" @click="toPage('ChangePassword')">
        {{ $t('忘记密码？') }}
      </div>
    </div>
    <div class="button" @click="commit()">{{ $t('登录') }}</div>

    <div
      class="fast-register"
      @click="toPage('FastRegister')"
      v-if="[1, 2].includes(Number(initData.login_type.type))"
    >
      快速注册
    </div>
    <div class="explain">
      <input type="checkbox" v-model="ifAgreement" />{{
        $t('登录即代表您已同意')
      }}
      <div
        class="link"
        @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
      >
        《{{ $t('用户协议') }}》
      </div>
      {{ $t('与') }}
      <div
        class="link"
        @click="handleLink($h5Page.yinsizhengce, $t('隐私政策'))"
      >
        《{{ $t('隐私政策') }}》
      </div>
    </div>
  </div>
</template>

<script>
import { ApiLogin } from '@/api/views/users';
import { loginSuccess } from '@/utils/function';

import { mapGetters } from 'vuex';

import { isWebApp } from '@/utils/userAgent.js';
import useConfirmAgreement from '@/components/yy-confirm-agreement/index.js';

export default {
  name: 'Login',
  data() {
    return {
      isWebApp,
      username: '',
      password: '',
      ifAgreement: false, //是否勾选协议
      open: false, //是否显示密码
      remember: true,
      rememberList: [],
      showRemember: false,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      postMessageData: 'system/postMessageData',
    }),
  },
  created() {
    if (localStorage.getItem('rememberList')) {
      this.rememberList = JSON.parse(localStorage.getItem('rememberList'));
      if (this.rememberList.length) {
        this.username = this.rememberList[0].username;
        this.password = this.rememberList[0].password;
      }
    }
  },
  activated() {
    // 神策埋点
    this.$sensorsTrack('loginpage_view', {
      referrer_page_name: this.$sensorsChainGet(),
    });
  },
  methods: {
    goToPhoneLogin() {
      if ([2].includes(Number(this.initData.login_type.type))) {
        this.$dialog
          .alert({
            message: this.initData.login_type.msg || '暂无消息',
            allowHtml: true,
            confirmButtonText: '我知道了',
            confirmButtonColor: '@themeColor',
          })
          .then(() => {
            this.$copyText(this.initData.login_type.copy_text).then(res => {
              this.$toast('复制链接成功');
            });
          });
        return;
      }
      this.toPage('PhoneLogin');
    },
    selectRememberItem(info) {
      this.username = info.username;
      this.password = info.password;
      this.showRemember = false;
    },
    clearRememberItem(info) {
      this.rememberList = this.rememberList.filter(item => {
        return info.username != item.username;
      });
      localStorage.setItem('rememberList', JSON.stringify(this.rememberList));
      if (this.rememberList.length) {
        this.username = this.rememberList[0].username;
        this.password = this.rememberList[0].password;
      }
    },
    isShow() {
      this.open = !this.open;
    },
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
    toPage(page) {
      if (['PhoneLogin', 'FastRegister', 'ChangePassword'].includes(page)) {
        this.$router.replace({ name: page });
      } else {
        this.$router.push({ name: page });
      }
    },
    async commit() {
      if (this.username === '') {
        this.$toast(this.$t('请输入用户名!'));
        return false;
      }
      if (this.password === '') {
        this.$toast(this.$t('请输入密码!'));
        return false;
      }

      const hadAgree = await useConfirmAgreement(this.ifAgreement);
      if (!hadAgree) return;

      this.ifAgreement = true;

      const toast1 = this.$toast.loading({
        message: this.$t('登录中...'),
        forbidClick: true,
        duration: 0,
      });
      let params = {
        username: this.username,
        password: this.password,
      };
      if (this.isWebApp) {
        params.is_ios_standalone = 1;
      }
      ApiLogin(params).then(res => {

        this.rememberList = this.rememberList.filter(item => {
          return item.username != this.username;
        });
        // 保存密码
        if (this.remember) {
          this.rememberList.unshift({
            username: this.username,
            password: this.password,
            avatar: res.data.avatar,
          });
        } else {
          // 不保存密码
          this.rememberList.unshift({
            username: this.username,
            password: '',
            avatar: res.data.avatar,
          });
        }
        if (this.rememberList.length > 3) {
          this.rememberList = this.rememberList.slice(0, 3);
        }
        localStorage.setItem('rememberList', JSON.stringify(this.rememberList));
        loginSuccess(res);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.login-page {
  .phone-login {
    font-size: 14 * @rem;
    color: #000000;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 200 * @rem;
    background-image: url(~@/assets/images/users/login-top-bg.png);
    background-size: 100% 200 * @rem;
    background-repeat: no-repeat;
    padding-left: 30 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: bold;
      line-height: 40 * @rem;
      margin-top: 113 * @rem;
    }
  }
  .form {
    padding: 0 30 * @rem;
    margin-top: 30 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 42 * @rem;
      border-bottom: 1 * @rem solid #d2d2d2;
      position: relative;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      input {
        flex: 1;
        height: 100%;
        line-height: 42 * @rem;
        font-size: 16 * @rem;
        letter-spacing: 1 * @rem;
        margin-right: 5 * @rem;
      }
      .right {
        height: 100%;
        display: flex;
        align-items: center;
        .text {
          font-size: 14 * @rem;
          text-align: right;
          color: @themeColor;
        }
        .eyes {
          width: 18 * @rem;
          height: 42 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.open {
            height: 12 * @rem;
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
      .clear {
        width: 16 * @rem;
        height: 42 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .down-arrow {
        width: 15 * @rem;
        height: 42 * @rem;
        background: url(~@/assets/images/users/arrow-down.png) center center
          no-repeat;
        background-size: 15 * @rem 6 * @rem;
        margin-left: 10 * @rem;
        &.show {
          transform: rotateX(180deg);
        }
      }
      .remember-list {
        box-sizing: border-box;
        width: 100%;
        position: absolute;
        z-index: 2;
        top: 42 * @rem;
        left: 0;
        background-color: #ebebeb;
        border-radius: 0 0 5 * @rem 5 * @rem;
        .remember-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 5 * @rem;
          &:not(:first-of-type) {
            border-top: 0.5 * @rem solid #ccc;
          }
          .remember-username {
            font-size: 14 * @rem;
            color: #999;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .remember-avatar {
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 50%;
            overflow: hidden;
          }
          .clear-remember-item {
            width: 20 * @rem;
            height: 20 * @rem;
            background: url(~@/assets/images/users/keyword-clear.png) center
              center no-repeat;
            background-size: 12 * @rem 12 * @rem;
            margin-left: 5 * @rem;
          }
        }
      }
    }
  }
  .account-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30 * @rem;
    margin-top: 12 * @rem;
    .remember-password {
      display: flex;
      align-items: center;
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      color: #797979;
      padding-left: 18 * @rem;
      background-position: left center;
      background-size: 13 * @rem 13 * @rem;
      background-repeat: no-repeat;
      background-image: url(~@/assets/images/users/remember-no.png);
      &.remember {
        background-image: url(~@/assets/images/users/remember-yes.png);
      }
    }
    .forge-password {
      font-size: 14 * @rem;
      color: #797979;
      line-height: 20 * @rem;
    }
  }
  .button {
    height: 50 * @rem;
    margin: 40 * @rem 30 * @rem 0;
    text-align: center;
    line-height: 50 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
  }
  .fast-register {
    text-align: center;
    font-size: 14 * @rem;
    color: @themeColor;
    text-decoration: underline;
    margin-top: 10 * @rem;
  }
  .explain {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #797979;
    font-size: 12 * @rem;
    margin-top: 16 * @rem;
    input[type='checkbox'] {
      width: 12 * @rem;
      height: 12 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center;
      background-size: 100%;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
}
</style>
