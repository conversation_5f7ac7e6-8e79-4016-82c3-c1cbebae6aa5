export default [
  {
    path: '/buy_cloud_devices',
    name: 'BuyCloudDevices',
    component: () =>
      import(
        /* webpackChunkName: "cloudDevices" */ '@/views/CloudDevices/BuyCloudDevices'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: false,
      pageTitle: '云挂机购买',
    },
  },
  {
    path: '/buy_cloud_devices_ruler',
    name: 'BuyCloudDevicesRuler',
    component: () =>
      import(
        /* webpackChunkName: "cloudDevices" */ '@/views/CloudDevices/BuyCloudDevicesRuler'
      ),
    meta: {
      requiresAuth: false,
      keepAlive: false,
      pageTitle: '云挂机购买规则',
    },
  },
];
