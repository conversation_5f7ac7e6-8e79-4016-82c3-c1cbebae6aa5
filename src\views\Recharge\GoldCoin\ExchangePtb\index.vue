<template>
  <div class="page exchange-page">
    <nav-bar-2
      :border="false"
      title=""
      :azShow="true"
      :placeholder="false"
      bgStyle="transparent"
    >
    </nav-bar-2>
    <div class="main">
      <div class="section">
        <div class="bg">
          <img src="@/assets/images/recharge/exchange-gold-bg.png" alt="" />
        </div>
        <div class="goods-bar">
          <div class="line">
            <div class="cost-gold">
              <span>{{ info.gold }}</span
              >金币
            </div>
            <div class="total">库存:{{ info.inventory }}</div>
          </div>
          <div class="line">
            <div class="cost-ptb">
              {{ info.title }}
            </div>
          </div>
        </div>
      </div>

      <div class="section section2">
        <div class="desc">
          <div class="title">商品描述：</div>
          <div class="text">{{ info.info }}</div>
        </div>
        <div class="notice">
          <div class="title">权益须知：</div>
          <div class="text" v-html="notice"></div>
        </div>
      </div>
    </div>

    <div class="bottom-bar fixed-center">
      <div
        class="confirm-btn btn"
        :class="{ no: info.exchange_status != 1 }"
        @click="clickExchangeBtn"
      >
        兑换
      </div>
    </div>

    <!-- 上弹兑换弹窗 -->
    <van-popup
      class="exchange-popup"
      position="bottom"
      v-model="exchangePopupShow"
      :lock-scroll="false"
    >
      <div class="title">兑换详情</div>
      <div class="buy-number-container">
        <div class="buy-number-operation">
          <div class="buy-number-title">{{ $t('购买数量') }}</div>
          <van-stepper
            class="number-stepper"
            v-model="buyNumber"
            :min="0"
            :max="leftTotal"
            :button-size="`${24 * remNumberLess}rem`"
            :input-width="`${40 * remNumberLess}rem`"
            @change="onChangeBuyNumber"
          />
        </div>
      </div>
      <div class="total-info">
        <div class="left">
          <div class="total-gold">
            合计：<span>{{ totalNeedGold }}</span>
          </div>
          <div class="left-gold">剩余金币：{{ userInfo.gold }}</div>
        </div>
        <div class="confirm-btn btn" @click="handleExchange"> 确认兑换 </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import {
  ApigoldToPtbExchange,
  ApigoldToPtbCheckExchange,
  ApigoldToPtbExchangeInfo,
} from '@/api/views/gold.js';
import { remNumberLess } from '@/common/styles/_variable.less';
export default {
  name: 'ExchangePtb',
  data() {
    return {
      remNumberLess,

      id: 0,
      info: {},

      exchangePopupShow: false,
      buyNumber: 1,
    };
  },
  computed: {
    notice() {
      if (!this.info.illustrate) {
        return '';
      }
      return this.info.illustrate.replace(/\n/g, '<br/><br/>');
    },

    leftTotal() {
      return this.info.inventory ?? 0;
    },

    totalNeedGold() {
      if (isNaN(this.buyNumber)) {
        return 0;
      }
      return this.buyNumber * this.info.gold;
    },
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getExchangeInfo();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async getExchangeInfo() {
      const res = await ApigoldToPtbExchangeInfo({ exchange_id: this.id });
      this.info = res.data;
    },

    async clickExchangeBtn() {
      this.buyNumber = 1;
      const check = await this.checkExchange();
      if (check) {
        this.exchangePopupShow = true;
      }
    },

    async handleExchange() {
      if (this.buyNumber == 0) {
        this.$toast('请选择购买数量');
        return;
      }
      // 兑换
      try {
        this.$toast.loading({
          message: '加载中',
          duration: 0,
        });
        const res = await ApigoldToPtbExchange({
          exchange_id: this.id,
          buy_num: this.buyNumber || 0,
        });
        this.$toast.clear();
        if (res.code == 1 || res.code == 3) {
          this.toPage('ExchangePtbResult', {
            info: res.data,
          });
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.SET_USER_INFO();
      }
    },
    async checkExchange() {
      try {
        const res = await ApigoldToPtbCheckExchange({
          exchange_id: this.id,
          buy_num: this.buyNumber || 0,
        });
        if (res.code != 1 && res.code != 3) {
          return false;
        }
        return true;
      } catch (e) {
        return false;
      }
    },
    async onChangeBuyNumber(e) {
      if (this.buyNumber == 0) {
        return false;
      }
      const check = await this.checkExchange();
      if (!check) {
        let num = this.buyNumber;
        if (this.totalNeedGold > this.userInfo.gold) {
          num = Math.floor(this.userInfo.gold / this.info.gold);
          if (num > this.info.illustrate) {
            num = this.info.illustrate;
          }
        }
        this.buyNumber = num;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.exchange-page {
  .main {
    padding-bottom: 120 * @rem;
    .section {
      padding-bottom: 16 * @rem;
      &:not(:first-of-type) {
        border-top: 12 * @rem solid #f8f8f9;
      }
      .bg {
        width: 100%;
        height: 248 * @rem;
      }
      .goods-bar {
        box-sizing: border-box;
        position: relative;
        z-index: 2;
        margin: -20 * @rem auto 0;
        width: 339 * @rem;
        height: 90 * @rem;
        border-radius: 12 * @rem;
        box-shadow: 0 * @rem 0 * @rem 6 * @rem 0 * @rem rgba(0, 62, 220, 0.05);
        background-color: #fff;
        padding: 18 * @rem 12 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .cost-gold {
            font-size: 11 * @rem;
            color: #999999;
            line-height: 25 * @rem;
            span {
              font-size: 20 * @rem;
              color: #f05f29;
              font-weight: 600;
              margin-right: 4 * @rem;
              vertical-align: -2 * @rem;
            }
          }
          .total {
            font-size: 11 * @rem;
            color: #666666;
            line-height: 14 * @rem;
          }
          .cost-ptb {
            font-size: 14 * @rem;
            line-height: 18 * @rem;
            color: #111111;
            font-weight: 600;
          }
        }
      }

      &.section2 {
        padding: 10 * @rem 18 * @rem;
        .desc {
          display: flex;
          align-items: center;
          margin-top: 14 * @rem;
          .title {
            font-size: 15 * @rem;
            color: #111111;
            font-weight: 600;
            line-height: 19 * @rem;
          }
          .text {
            font-size: 13 * @rem;
            color: #86888f;
            line-height: 16 * @rem;
          }
        }
        .notice {
          margin-top: 24 * @rem;
          .title {
            font-size: 15 * @rem;
            color: #111111;
            font-weight: 600;
            line-height: 19 * @rem;
          }
          .text {
            margin-top: 12 * @rem;
            font-size: 13 * @rem;
            color: #86888f;
            line-height: 18 * @rem;
          }
        }
      }
    }
  }
  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 64 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    box-shadow: 0 * @rem -5 * @rem 8 * @rem 0 * @rem rgba(0, 133, 255, 0.05);
    .confirm-btn {
      flex: 1;
      min-width: 0;
      height: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
      background: linear-gradient(75deg, #ff7a00 0%, #ffb03a 100%);
      border-radius: 22 * @rem;
      margin: 0 auto;
      &.no {
        background: #ccc;
      }
    }
  }
}

.exchange-popup {
  box-sizing: border-box;
  border-radius: 20 * @rem 20 * @rem 0 0;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  padding: 0 18 * @rem;
  .title {
    height: 62 * @rem;
    display: flex;
    align-items: center;
    border-bottom: 0.5 * @rem solid #ebebeb;
    font-size: 16 * @rem;
    color: #111111;
    font-weight: 600;
  }
  .buy-number-container {
    box-sizing: border-box;
    padding: 24 * @rem 0;
    .buy-number-operation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .buy-number-title {
        font-size: 14 * @rem;
        color: #111111;
      }
      .number-stepper {
        /deep/ .van-stepper__input {
          background-color: #fff;
          font-size: 16 * @rem;
          color: #333333;
          font-weight: bold;
          margin: 0 2 * @rem;
        }
        /deep/ .van-stepper__minus,
        /deep/ .van-stepper__plus {
          background-color: #fff;
          border: 0.5 * @rem solid #c1c1c1;
          border-radius: 6 * @rem;
        }
      }
    }
  }
  .total-info {
    border-top: 0.5 * @rem solid #ebebeb;
    display: flex;
    padding: 18 * @rem 0;
    .left {
      flex: 1;
      min-width: 0;
      .total-gold {
        font-size: 14 * @rem;
        color: #333333;
        line-height: 20 * @rem;
        span {
          color: #f05f29;
          font-size: 20 * @rem;
          font-weight: 600;
        }
      }
      .left-gold {
        font-size: 13 * @rem;
        color: #999999;
        line-height: 18 * @rem;
        margin-top: 6 * @rem;
      }
    }

    .confirm-btn {
      width: 104 * @rem;
      height: 36 * @rem;
      background: linear-gradient(75deg, #ff7a00 0%, #ffb03a 100%);
      border-radius: 18 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      color: #ffffff;
    }
  }
}
</style>
