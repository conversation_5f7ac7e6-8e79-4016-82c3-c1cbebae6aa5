<template>
  <div class="page platform-coin-page">
    <nav-bar-2
      :title="$t('平台币充值')"
      :border="true"
      :azShow="true"
      :placeholder="true"
    >
      <template #right>
        <div class="coin-detail" @click="goToPtbDetail" v-if="!isSdk">
          {{ $t('明细') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="gold-bar">
      <div class="gold-bar-item">
        <div class="item-left">{{ $t('充值账号') }}：</div>
        <div class="item-right account">{{ userInfo.username }}</div>
      </div>
      <div class="gold-bar-item">
        <div class="item-left">{{ $t('我的平台币') }}：</div>
        <div class="item-right platform-gold">{{ userInfo.ptb || 0 }}</div>
      </div>
    </div>
    <div class="main">
      <div class="top-tips">
        {{ $t('选择金额') }}<span>（{{ text_list.gold_welfare }}）</span>
      </div>
      <ul class="select-list">
        <li
          class="select-item"
          v-for="(item, index) in selectList"
          :key="index"
          :class="{
            on: selectMoney == item.money && !(!item.first && item.is_only),
            cant: item.first == false,
          }"
          @click="changeMoney(item)"
        >
          <div class="date">{{ item.date }}</div>
          <div class="date-title">{{ item.date_unit }}</div>
          <div class="money">{{ item.money }}{{ item.money_unit }}</div>
          <div class="is-first" v-if="item.is_only">{{ $t('首充') }}</div>
          <div class="is-recommend" v-if="item.is_recommend == 1">
            {{ $t('推荐') }}
          </div>
        </li>
      </ul>
      <div class="input-container">
        <input
          type="number"
          class="text text-input"
          :placeholder="placeholder"
          v-model="selectMoney"
        />
        <span class="text text-right">({{ $t('元') }})</span>
      </div>
      <div class="title2">{{ $t('选择支付方式') }}</div>
      <ul class="pay-list">
        <li
          class="pay-item"
          :class="{ on: payWay.key == item.key }"
          v-for="(item, index) in payWayList"
          :key="index"
          @click="payWay = item"
        >
          <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
          <!-- <span class="text">{{ item.name }}</span> -->

          <div class="pay-center">
            <div class="line">
              <div class="pay-name">{{ item.name }}</div>
              <div
                class="recommend-icon"
                v-if="item.tag_img"
                :style="{ backgroundImage: `url(${item.tag_img})` }"
              ></div>
            </div>
            <div v-if="item.subtitle" class="subtitle">
              {{ item.subtitle }}
            </div>
          </div>
          <div class="select-icon"></div>
        </li>
      </ul>
      <div class="buy-fixed">
        <div class="recharge btn" @click="handlePay">
          {{ $t('确认支付') }}<span>{{ totalMoney }}</span
          >{{ $t('元') }}
        </div>
      </div>
      <div class="title title2">{{ $t('温馨提示') }}</div>
      <div class="explain" v-html="text_list.illustrate"></div>
    </div>
  </div>
</template>
<script>
import {
  ApiCreateOrderPtb,
  ApiGetPayUrl,
  ApiPlatformGetInfo,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
import { formatExchangeRate } from '@/utils/tools.js';

import { mapGetters } from 'vuex';

import { platform, BOX_openInNewWindow, isSdk } from '@/utils/box.uni.js';

export default {
  name: 'PlatformCoin',
  data() {
    return {
      isSdk,

      payWay: {}, // 支付方式的对象
      selectMoney: 0, // 准备充值的金额
      selectList: [], // 充值金额列表
      payWayList: [], // 支付方式
      text_list: {}, // 一些带翻译的文案字段
    };
  },
  watch: {
    selectMoney() {
      if (Math.floor(this.selectMoney) !== Number(this.selectMoney)) {
        this.$toast.fail(this.$t('请输入整数'));
        this.selectMoney = Math.floor(this.selectMoney);
      }
    },
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    // 总金额
    totalMoney() {
      return Number(this.selectMoney) || 0;
    },
    placeholder() {
      return `${this.$t('请输入充值金额')}${this.isHw ? '10美元' : '30元'}起`;
    },
  },
  async created() {
    await this.$onAppFinished;
    await this.getPlatformInfo();
  },
  methods: {
    formatExchangeRate,
    goToPtbDetail() {
      BOX_openInNewWindow(
        { name: 'PlatformCoinDetail' },
        { url: `${window.location.origin}/#/platform_coin_detail` },
      );
    },
    handlePay() {     
      ApiCreateOrderPtb({
        isNew: 1,
        money: this.selectMoney,
        payWay: this.payWay.key,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.payWay.key,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {
            })
            .catch(() => {
            });
        });
      });
    },
    changeMoney(item) {
      if (item.first == false) {
        this.$toast('仅限首次充值');
        return false;
      }
      this.selectMoney = item.money;
    },
    async getPlatformInfo() {
      const res = await ApiPlatformGetInfo();
      let { payWayList, platiconList, text_list } = res.data;
      this.selectList = platiconList;
      this.payWayList = payWayList;
      this.payWay = this.payWayList[0];
      this.text_list = text_list;
      this.selectMoney =
        platiconList.find(item => {
          return item.is_recommend == 1;
        }).money || 0;
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-page {
  .coin-detail {
    color: #000;
    font-size: 16 * @rem;
  }
  .gold-bar {
    box-sizing: border-box;
    background-color: #f4f4f4;
    padding: 0 18 * @rem;
    width: 339 * @rem;
    margin: 15 * @rem auto 0;
    border-radius: 10 * @rem;
    .gold-bar-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44 * @rem;
      &:not(:first-of-type) {
        border-top: 0.5 * @rem solid #e4e4e4;
      }
      .item-left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .item-right {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        &.platform-gold {
          font-size: 14 * @rem;
          color: #21b98a;
          font-weight: 400;
        }
      }
    }
  }
  .main {
    padding: 0 25 * @rem 80 * @rem;

    .top-tips {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      padding-top: 20 * @rem;
      display: flex;
      align-items: center;
      span {
        font-size: 12 * @rem;
        color: #909090;
        font-weight: 400;
        margin-left: 5 * @rem;
      }
    }
    .title2 {
      margin-top: 24 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
    }
    .select-list {
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .select-item {
        position: relative;
        box-sizing: border-box;
        width: 98 * @rem;
        height: 74 * @rem;
        margin-top: 15 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1 * @rem solid #e1e1e1;
        border-radius: 8 * @rem;
        cursor: pointer;
        .is-recommend {
          box-sizing: border-box;
          position: absolute;
          left: -1 * @rem;
          top: -1 * @rem;
          width: 30 * @rem;
          height: 18 * @rem;
          background: #ff4869;
          border-radius: 6 * @rem 0 * @rem 6 * @rem 0 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
        .is-first {
          box-sizing: border-box;
          position: absolute;
          left: -1 * @rem;
          top: -1 * @rem;
          width: 30 * @rem;
          height: 18 * @rem;
          background: #21b98a;
          border-radius: 6 * @rem 0 * @rem 6 * @rem 0 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
        .date {
          text-align: center;
          font-size: 18 * @rem;
          color: #333333;
          color: #000000;
          font-weight: 600;
        }
        .date-title {
          text-align: center;
          font-size: 10 * @rem;
          color: #000000;
          font-weight: 600;
          margin-top: 2 * @rem;
        }
        .money {
          text-align: center;
          font-size: 12 * @rem;
          color: #797979;
          margin-top: 6 * @rem;
        }
        &.on {
          border-color: #21b98a;
          border-width: 1 * @rem;
          background-color: #ecfbf4;
          .date {
            color: #21b98a;
          }
          .date-title {
            color: #21b98a;
          }
          .money {
            color: #21b98a;
          }
          // &:before {
          //   content: "";
          //   position: absolute;
          //   display: block;
          //   background: url(~@/assets/images/recharge/coin-selected.png)
          //     no-repeat;
          //   background-size: 18 * @rem 15 * @rem;
          //   top: -1 * @rem;
          //   right: -1 * @rem;
          //   width: 18 * @rem;
          //   height: 15 * @rem;
          // }
        }
        &.cant::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          z-index: 999;
          width: 100%;
          height: 100%;
          background: rgba(230, 230, 230, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
    .pay-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 12 * @rem;
    }
    .pay-item {
      box-sizing: border-box;
      position: relative;
      width: 49%;
      height: 52 * @rem;
      padding-top: 10 * @rem;
      text-align: center;
      border: 1 * @rem solid #e1e1e1;
      border-radius: 8 * @rem;
      font-size: 0;
      display: flex;
      align-items: center;
      padding: 0 8 * @rem;
      &:not(:nth-of-type(-n + 2)) {
        margin-top: 10 * @rem;
      }
      &.on {
        border: 1 * @rem solid #21b98a;
      }
      .icon {
        display: block;
        width: 25 * @rem;
        height: 25 * @rem;
        background-repeat: no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }

      .pay-center {
        margin-left: 5 * @rem;
        .line {
          display: flex;
          align-items: center;
          .pay-name {
            display: block;
            font-size: 14 * @rem;
            font-weight: 500;
            color: #000;
            flex: 1;
            min-width: 0;
            text-align: left;
          }
          .recommend-icon {
            width: 39 * @rem;
            height: 17 * @rem;
            background-size: 39 * @rem 17 * @rem;
            background-position: left center;
            background-repeat: no-repeat;
            margin-left: 5 * @rem;
          }
        }
        .subtitle {
          color: #999;
          font-size: 12 * @rem;
          line-height: 20 * @rem;
          text-align: left;
        }
      }
      .select-icon {
        position: absolute;
        right: 0 * @rem;
        bottom: 0 * @rem;
        width: 20 * @rem;
        height: 20 * @rem;
        background: transparent;
        background-size: 20 * @rem 20 * @rem;
      }
      &.on .select-icon {
        background-image: url(~@/assets/images/recharge/pay-selected.png);
      }
    }
    .input-container {
      display: flex;
      box-sizing: border-box;
      margin-top: 18 * @rem;
      padding: 12 * @rem 0;
      border-bottom: 1 * @rem solid #cbcbcb;
      letter-spacing: 0.625 * @rem;
      font-size: 0;
      .text {
        display: inline-block;
        width: 50 * @rem;
        font-size: 16 * @rem;
        color: #000;
      }
      .text-right {
        text-align: right;
        padding-right: 10 * @rem;
      }
      .text-input {
        flex: 1;
        min-width: 0;
        height: 20 * @rem;
        font-size: 14 * @rem;
        color: #333;
        margin-right: 10 * @rem;
      }
    }
    .tips {
      font-size: 12 * @rem;
      color: #ffb43d;
      line-height: 18 * @rem;
      margin-top: 10 * @rem;
    }
    .explain {
      margin-top: 6 * @rem;
      font-size: 12 * @rem;
      color: #8a848a;
      line-height: 18 * @rem;
    }
    .buy-fixed {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10 * @rem 16 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      box-shadow: 0 -5 * @rem 5 * @rem rgba(0, 0, 0, 0.035);
      border-top: 1px solid rgba(0, 0, 0, 0.035);
    }
    .recharge {
      width: 100%;
      height: 44 * @rem;
      margin: 0 20 * @rem;
      margin: 0 auto;
      line-height: 44 * @rem;
      text-align: center;
      letter-spacing: 0.625 * @rem;
      font-size: 16 * @rem;
      font-weight: 500;
      color: #ffffff;
      background: linear-gradient(222deg, #6ddc8c 0%, #21b98a 100%);
      border-radius: 22 * @rem;
      &.no {
        background: #c5c5c5;
      }
    }
  }
}

.coupon {
  display: flex;
  align-items: center;
  margin-top: 14 * @rem;
  height: 30 * @rem;
  .title-text {
    font-size: 17 * @rem;
    color: #333333;
    width: 60 * @rem;
  }
  .coupon-text {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: flex-end;
    .coupon-value {
      &.have-coupon {
        height: 26 * @rem;
        border-radius: 4 * @rem;
        background: linear-gradient(90deg, #ffb43d, #ff9d3d);
        padding: 0 7 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      &.used-coupon {
        font-size: 16 * @rem;
        color: #ff4747;
        span {
          font-size: 14 * @rem;
        }
      }
      &.no-coupon {
        font-size: 14 * @rem;
        color: #999999;
      }
    }
  }
  .right-icon {
    width: 7 * @rem;
    height: 12 * @rem;
    background: url(~@/assets/images/recharge/right-icon.png) no-repeat;
    background-size: 7 * @rem 12 * @rem;
    margin-left: 7 * @rem;
  }
}
</style>
