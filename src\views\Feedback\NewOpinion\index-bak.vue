<template>
  <div class="comment-editor">
    <nav-bar-2 title="意见反馈" :border="true"> </nav-bar-2>
    <div class="editor-container">
      <div class="problem-container">
        <div class="title">请选择你遇到的问题 *</div>
        <div class="problem-list">
          <div
            class="problem-item"
            v-for="item in problemList"
            :key="item.id"
            :class="{ active: selectedProblems.includes(item.id) }"
            @click="toggleProblem(item.id)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="input-container">
        <van-field
          id="inputText"
          v-model.trim="inputText"
          rows="5"
          maxlength="200"
          autosize
          type="textarea"
          :placeholder="placeholder"
          show-word-limit
        />
      </div>
      <div class="img-container">
        <div class="title">
          <span>上传图片</span>
          <span>最多支持上传6张</span>
        </div>
        <van-uploader
          v-model="imageFileList"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="6"
          :preview-size="92"
          accept="image/*"
          :before-read="beforeRead"
          :multiple="true"
          class="uploader"
        >
          <div class="upload-btn"> </div>
          <div class="text">上传</div>
        </van-uploader>
      </div>
      <div class="contact-info">
        <div class="title">联系方式</div>
        <div class="contact-input">
          <input
            v-model="contactText"
            type="text"
            placeholder="请输入手机号、微信号"
          />
        </div>
      </div>
    </div>
    <!-- 评论按钮 -->
    <div class="bottom-container">
      <div class="bottom-fixed">
        <div class="btn" :class="{ on: canSend }" @click="handleSend">
          提交</div
        >
        <div class="kefu">
          <div class="kefu-btn" @click="toKefu">联系客服</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiFeedbackSubmit } from '@/api/views/users.js';
import md5 from 'js-md5';
import { remNumberLess } from '@/common/styles/_variable.less';
import {
  platform,
  BOX_openInNewNavWindow,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';
import h5Page from '@/utils/h5Page';
export default {
  name: 'CommentEditor',
  data() {
    return {
      inputText: '', // 反馈内容
      imageFileList: [],
      images: [],
      selectedProblems: [], // 存储选中的问题
      contactText: '', //联系方式
      isSending: false,
      remNumberLess,
      problemList: [
        { id: 1, label: '产品建议' },
        { id: 2, label: '功能故障' },
        { id: 3, label: '其他' },
        { id: 4, label: '反动内容' },
        { id: 5, label: '侵犯版权' },
        { id: 6, label: '携带病毒' },
        { id: 7, label: '恶意扣费' },
        { id: 8, label: '含有不良插件' },
        { id: 9, label: '无法安装/重启' },
        { id: 10, label: '色情内容' },
        { id: 11, label: '暴力内容' },
      ],
    };
  },
  computed: {
    placeholder() {
      return '请描述您遇到的问题或功能建议,我们将尽快处理~';
    },
    canSend() {
      if (this.inputText.length < 15) return false;
      return true;
    },
    formatContent() {
      return this.inputText.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  watch: {},
  async created() {},
  mounted() {
    this.$nextTick(() => {
      document.querySelector('#inputText').focus();
    });
  },
  methods: {
    toKefu() {
      BOX_openInNewWindow({ name: 'Kefu' }, { url: h5Page.kefu_url });
    },
    async handleSend() {
      if (this.isSending) {
        return false;
      }
      if (this.selectedProblems.length == 0) {
        this.$toast('请选择你遇到的问题');
        return false;
      }
      if (this.inputText.length < 15) {
        this.$toast(this.$t('描述的字数不得小于15个字'));
        return false;
      }
      this.isSending = true;
      const selectedProblems = this.selectedProblems;
      let problemsString;

      if (selectedProblems.length === 1) {
        problemsString = selectedProblems[0].toString();
      } else {
        problemsString = selectedProblems.join(',');
      }
      let params = {
        question_type: problemsString,
        question: this.inputText,
        contact: this.contactText,
      };
      if (this.images.length) {
        params.images = JSON.stringify(this.images);
      }
      this.$toast.loading({
        message: this.$t('正在提交中...'),
      });
      try {
        const res = await ApiFeedbackSubmit({ ...params });
        this.inputText = '';
        this.contactText = '';
        this.imageFileList = [];
        this.images = [];
        this.selectedProblems = [];
        this.$toast(res.msg);
      } finally {
        this.isSending = false;
      }
    },
    handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      ApiUploadImage(data).then(
        res => {
          this.images.push(res.data.url);
          file.status = 'done';
          file.message = this.$t('上传成功');
        },
        err => {
          file.status = 'failed';
          file.message = this.$t('上传失败');
        },
      );
    },
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    async afterRead(file) {
      console.log(file);
      
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
    toggleProblem(id) {
      const index = this.selectedProblems.indexOf(id);
      if (index > -1) {
        this.selectedProblems.splice(index, 1);
      } else {
        this.selectedProblems.push(id);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.comment-editor {
  .editor-container {
    margin-top: 26 * @rem;
    padding-bottom: 20 * @rem;
    width: 336 * @rem;
    margin: 26 * @rem auto 20 * @rem;
    // height: 486 * @rem;
    border-radius: 6 * @rem;
    position: relative;
    .problem-container {
      margin-bottom: 12 * @rem;
      display: flex;
      flex-direction: column;
      .title {
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
      .problem-list {
        margin-top: 14 * @rem;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12 * @rem;
        .problem-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 104 * @rem;
          height: 31 * @rem;
          background: #f7f8fa;
          border-radius: 6 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #191b1f;
          &.active {
            background: #ecfbf4;
            color: #2bbe88;
          }
        }
      }
    }
    .input-container {
      box-sizing: border-box;
      width: 100%;
      min-height: 160 * @rem;
      background: #f7f8fa;
      border-radius: 6 * @rem;
      /deep/.van-cell {
        background: #f7f8fa;
      }
      /deep/.van-field__control {
        font-weight: 400;
        font-size: 12 * @rem;
        color: #bec2c5;
      }
      .input-text {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 22 * @rem 18 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #bec2c5;
        line-height: 20 * @rem;
        border: 0;
        outline: none;
        background: #f7f8fa;
      }
    }
    .img-container {
      margin-top: 26 * @rem;
      box-sizing: border-box;
      width: 100%;
      min-height: 92 * @rem;
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 14 * @rem;
        > span {
          &:first-child {
            font-weight: 600;
            font-size: 16 * @rem;
            color: #191b1f;
          }
          &:last-child {
            margin-left: 4 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #bec2c5;
          }
        }
      }
      .uploader {
        width: 100%;

        /deep/ .van-uploader__wrapper {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12 * @rem;
        }
        /deep/.van-uploader__input-wrapper {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;

          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        /deep/ .van-uploader__preview-image {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;
        }
        /deep/.van-uploader__preview-delete {
          top: -5 * @rem;
          right: -2 * @rem;
          height: 14 * @rem;
          width: 14 * @rem;
          background: url('~@/assets/images/deal/upload-icon-delete.png')
            #f0f1f5 no-repeat center center;
          background-size: 14 * @rem 14 * @rem;
          border-radius: 0;
        }
        /deep/.van-icon {
          display: none;
        }
      }
      .upload-btn {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url('~@/assets/images/deal/upload-icon-1.png') #f0f1f5
          no-repeat center center;
        background-size: 24 * @rem 24 * @rem;
      }
      .text {
        margin-top: 8 * @rem;
        font-weight: 500;
        font-size: 12 * @rem;
        color: #93999f;
      }
    }
    .contact-info {
      .title {
        margin-top: 24 * @rem;
        margin-bottom: 14 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
      .contact-input {
        width: 336 * @rem;
        padding: 0 10 * @rem;
        box-sizing: border-box;
        height: 44 * @rem;
        line-height: 44 * @rem;
        background: #f7f8fa;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #bec2c5;
        input {
          width: 100%;
          background: #f7f8fa;
        }
      }
    }
  }
  .score-container {
    display: flex;
    align-items: center;
    padding: 14 * @rem 18 * @rem;
    border-bottom: 0.5 * @rem solid #e8e8e8;
    .score-title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
    }
  }
  .quick-comment {
    border-top: 10 * @rem solid #f5f5f6;
    padding: 18 * @rem;
    .title {
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      span {
        font-size: 14 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .list {
      margin-top: 22 * @rem;
      .item {
        display: table;
        margin-bottom: 10 * @rem;
        padding: 8 * @rem 15 * @rem;
        background: #fff6e9;
        border-radius: 20 * @rem;
        color: @themeColor;
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        box-sizing: border-box;
        border: 1px solid #fff6e9;
        &.current {
          border: 1px solid @themeColor;
          color: @themeColor;
          font-weight: 600;
        }
      }
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(96 * @rem + @safeAreaBottom);
    height: calc(96 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      // box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 0 12 * @rem;
      padding-bottom: calc(@safeAreaBottom + 16 * @rem);
      padding-bottom: calc(@safeAreaBottomEnv + 16 * @rem);
      .btn {
        width: 336 * @rem;
        height: 44 * @rem;
        border-radius: 29 * @rem;
        text-align: center;
        line-height: 44 * @rem;
        color: #fff;
        font-weight: 500;
        font-size: 16 * @rem;
        background-color: #c1c1c1;
        &.on {
          background-color: @themeColor;
        }
      }
      .kefu {
        margin-top: 16 * @rem;

        .kefu-btn {
          font-weight: 400;
          font-size: 14 * @rem;
          color: #2bbe88;
        }
      }
    }
  }
  .exit-popup {
    width: 300 * @rem;
    height: 200 * @rem;
    background: #fff;
    .exit-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-top: 30 * @rem;
        font-size: 16 * @rem;
        font-weight: bold;
        color: #191b1f;
      }
      .msg {
        margin-top: 25 * @rem;
        width: 196 * @rem;
        text-align: center;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #191b1f;
        line-height: 18 * @rem;
      }
      .btn-list {
        position: absolute;
        left: 25 * @rem;
        bottom: 20 * @rem;
        display: flex;
        align-items: center;
        .exit,
        .continue {
          width: 120 * @rem;
          height: 36 * @rem;
          background: #f7f8fa;
          border-radius: 22 * @rem;
          color: #93999f;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .continue {
          margin-left: 10 * @rem;
          background: #1cce94;
          color: #fff;
        }
      }
    }
  }
}
</style>
