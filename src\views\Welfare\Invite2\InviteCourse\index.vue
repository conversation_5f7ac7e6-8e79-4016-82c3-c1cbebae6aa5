<template>
  <div class="invite-course">
    <header>
      <nav-bar-2
        bgStyle="transparent"
        :title="'赚钱教程'"
        :azShow="true"
        v-if="nav_bg_transparent"
      >
      </nav-bar-2>
      <nav-bar-2 :title="'赚钱教程'" :placeholder="false" :border="true" v-else>
      </nav-bar-2>
    </header>
    <section class="sec01">
      <div class="big-text">
        朋友通过你的邀请，在3733游戏盒子注册、消费，你赚钱
      </div>
      <div class="text">
        3733盒子APP【福利中心】，点击“邀请好友”，进入邀请新成员界面
      </div>
      <div class="small-text">注：邀请新用户才算有效</div>
      <img src="@/assets/images/welfare/invite/invite_img1.png" />
      <div class="text"> 点击【复制链接邀请】按钮，将邀请分享给朋友 </div>
      <img src="@/assets/images/welfare/invite/invite_img2.png" />
      <div class="text">
        新朋友首次手机号注册，可获总价为188元的代金券礼包；当朋友试玩游戏，且在游戏内消费，邀请人可获得前三笔订单10%的奖励（该提成为微信或支付宝实际支付的百分比金额）
      </div>
      <div class="small-text">邀请越多，奖励越多哦~</div>
      <img src="@/assets/images/welfare/invite/invite_img3.png" />
    </section>
    <section class="sec02">
      <div class="big-text">随时提现到微信</div>
      <div class="text">轻松提现，首次提现可享1元提现</div>
      <img src="@/assets/images/welfare/invite/invite_img4.png" />
    </section>
  </div>
</template>
<script>
export default {
  data() {
    return {
      nav_bg_transparent: true, //导航条
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.nav_bg_transparent = false;
      } else {
        this.nav_bg_transparent = true;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.invite-course {
  overflow: hidden;
}
header {
  width: 100%;
  height: 200 * @rem;
  .image-bg('~@/assets/images/welfare/invite/invite_bg2.png');
}
section {
  position: relative;
  background: #ffffff;
  box-shadow: 0 * @rem 2 * @rem 14 * @rem 0 * @rem rgba(0, 7, 187, 0.05);
  border-radius: 16 * @rem 16 * @rem 16 * @rem 16 * @rem;
  margin: 13 * @rem 8 * @rem;
  padding: 53 * @rem 10 * @rem 10 * @rem;
  &.sec01::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 48 * @rem;
    height: 48 * @rem;
    .image-bg('~@/assets/images/welfare/invite/invite_icon2.png');
  }
  &.sec02::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 48 * @rem;
    height: 48 * @rem;
    .image-bg('~@/assets/images/welfare/invite/invite_icon3.png');
  }
  .big-text {
    margin-bottom: 10 * @rem;
    font-size: 14 * @rem;
    font-weight: 600;
    color: #333333;
    line-height: 21 * @rem;
  }
  .text {
    margin-bottom: 10 * @rem;
    font-size: 12 * @rem;
    color: #333333;
    line-height: 21 * @rem;
    padding: 0 10 * @rem;
  }
  .small-text {
    margin-bottom: 14 * @rem;
    color: #999999;
    line-height: 21 * @rem;
    padding: 0 10 * @rem;
  }
  img {
    width: auto;
    height: 200 * @rem;
    margin: 0 auto 20 * @rem;
  }
}
</style>
