import {
  request
} from '../index';

/**
 * 转游列表
 * @param {keyword} 关键字
 */
export function ApiZhuanyouGameListNew(params = {}) {
  return request('/api/zhuanyou/gameListNew', params);
}

/**
 * 转游 - 转游首页 【新】
 * @param {keyword} 关键字
 */
export function ApiZhuanyouNewGameListNew(params = {}) {
  return request('/api/zhuanyou/NewGameListNew', params);
}

/**
 * 转游 - 转游首页 【新新】
 * @param {cate} 分类
 */
export function ApiZhuanyouIndex(params = {}) {
  return request('/api/zhuanyou/index', params);
}

/**
 * 转游 - 获取转游点
 */
export function ApiZhuanyouGetBal(params = {}) {
  return request('/api/zhuanyou/getBal ', params);
}

/**
 * 转游 - 游戏转游礼包列表
 * @param {gameId} 游戏id
 */
export function ApiZhuanyouGameCard(params = {}) {
  return request('/api/zhuanyou/GameCard', params);
}

/**
 * 转游代金券详情
 * @param {gameId} 游戏id
 */
export function ApiZhuanyouZyCoupon(params = {}) {
  return request('/api/zhuanyou/zyCoupon', params);
}

/**
 * 转游点兑换代金券
 * @param {id} 转游规则id
 * @return {money} 10.00 代金券金额
 * @return {reach_money} 30.00 达标使用金额
 * @return {expire_time} 1635350399 到期时间
 */
export function ApiZhuanyouZydExchange(params = {}) {
  return request('/api/zhuanyou/zydExchange', params);
}

/**
 * 转游点明细
 */
export function ApiZhuanyouZydRecord(params = {}) {
  return request('/api/zhuanyou/zydRecord', params);
}