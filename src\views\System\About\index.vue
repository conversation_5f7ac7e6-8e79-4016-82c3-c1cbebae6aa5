<template>
  <div class="about-page page">
    <nav-bar-2 :border="true" :title="$t('关于')"></nav-bar-2>
    <div class="about-container">
      <div class="app-icon">
        <img :src="defaultAvatar" alt="" />
      </div>
      <div class="app-name">{{ appName }}</div>
      <div class="app-desc" v-if="!isAndroidBox && about_data.text1">
        {{ about_data.text1 }}
      </div>
      <div class="app-version">{{ $t('版本号') }}：{{ version }} {{ cps }}</div>
      <div class="env" v-if="env">{{ $t('环境') }}：{{ env }}</div>
    </div>
    <div class="panel">
      <div
        class="item btn"
        @click="handleLink($h5Page.yinsizhengce, $t('隐私政策'))"
      >
        <span>{{ $t('隐私政策') }}</span>
        <div class="right-icon"></div>
      </div>
      <div
        class="item btn"
        @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
      >
        <span>{{ $t('用户协议') }}</span>
        <div class="right-icon"></div>
      </div>
    </div>
    <div class="page-bottom">
      <div class="text" v-html="about_data.text2"></div>
      <div class="text">{{ about_data.text3 }}</div>
    </div>
  </div>
</template>

<script>
import { isAndroidBox } from '@/utils/userAgent.js';
import h5Page from '@/utils/h5Page';
import { getQueryVariable } from '@/utils/function.js';
import { mapGetters } from 'vuex';
import { ApiCommonAboutus } from '@/api/views/system';

export default {
  name: 'About',
  data() {
    return {
      cps: '',
      isAndroidBox,
      about_data: {},
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    version() {
      return `${process.env.VUE_APP_version}.${process.env.VUE_APP_versionCode}`;
    },
    env() {
      return h5Page.env;
    },
  },
  async created() {
    const cps = getQueryVariable('c');
    if (cps) this.cps = cps;
    const res = await ApiCommonAboutus();
    this.about_data = res.data;
    if (this.about_data.text2) {
      this.about_data.text2 = this.about_data.text2.replace(/\n/g, '<br>');
    }
  },
  methods: {
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.about-page {
  background-color: #fff;
  justify-content: space-between;
  .about-container {
    padding: 30 * @rem 0;
  }
  .app-icon {
    width: 70 * @rem;
    height: 70 * @rem;
    margin: 0 auto;
  }
  .app-name {
    font-size: 16 * @rem;
    font-weight: 600;
    color: #333333;
    text-align: center;
    margin-top: 12 * @rem;
  }
  .app-desc {
    font-size: 14 * @rem;
    color: #000000;
    text-align: center;
    margin-top: 8 * @rem;
  }
  .app-version,
  .env {
    font-size: 13 * @rem;
    color: #9a9a9a;
    text-align: center;
    margin-top: 8 * @rem;
  }
  .panel {
    flex: 1;
    border-top: 0.5 * @rem solid #f3f3f8;
    width: 100%;
    font-size: 14 * @rem;
    color: #000000;
    .item {
      box-sizing: border-box;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      border-bottom: 0.5 * @rem solid #f3f3f8;
      .right-icon {
        width: 7 * @rem;
        height: 14 * @rem;
        .image-bg('~@/assets/images/right-icon.png');
      }
    }
  }
  .page-bottom {
    margin-bottom: 50 * @rem;
    .text {
      text-align: center;
      line-height: 21 * @rem;
    }
  }
}
</style>
