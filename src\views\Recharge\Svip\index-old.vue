<template>
  <div class="svip-page">
    <nav-bar-2
      :title="$t('SVIP会员中心')"
      :placeholder="false"
      :bgStyle="bgStyle"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :azShow="true"
    >
      <template #right>
        <div
          v-if="!isSdk"
          class="sign-in-btn"
          :class="{ black: bgStyle == 'transparent' }"
          @click="goToSignIn"
        >
          {{ $t('每日签到') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="top-bar">
      <div class="user-bar">
        <div class="no-login" v-if="!userInfo.username" @click="toLogin">
          <user-avatar class="avatar"></user-avatar>
          <div class="no-login-text">点击登录</div>
        </div>
        <div class="user-info" v-else>
          <user-avatar class="avatar"></user-avatar>
          <div class="info-right">
            <div class="nickname">
              <div class="text">{{ userSvipInfo.nickname }}</div>
              <span :class="{ haveSvip: userSvipInfo.svip_status == 1 }"></span>
            </div>
            <div class="end-date" v-if="userSvipInfo.svip_status == 1">
              已享12+项SVIP会员特权 {{ userSvipInfo.svip_time }}到期
            </div>
            <div class="end-date" v-else>
              开通SVIP，尊享金币兑换，专属代金券等12+项特权
            </div>
          </div>
        </div>

        <div class="svip-intro" v-if="userSvipInfo.svip_status != 1">
          {{ text_list.vip_fuli }}
        </div>
        <div class="svip-intro" v-else>
          {{ $t('SVIP额外收益') }}：<span>{{ userSvipInfo.extra_gold }}</span
          >{{ $t('金币') }}
        </div>
      </div>
    </div>
    <div class="main">
      <!-- 套餐选择 -->
      <div class="container">
        <div class="container-title">
          {{ $t('服务选择') }}
        </div>
        <div class="svip-list">
          <template v-for="(item, index) in svipList">
            <div
              class="svip-item"
              :key="index"
              :class="{ on: selectedMeal.amount == item.amount }"
              v-if="
                item.is_first
                  ? userSvipInfo.is_first_recharge == 1
                    ? false
                    : true
                  : true
              "
              @click="selectedMeal = item"
            >
              <div
                v-if="item.tips"
                class="tips"
                :class="{ tips1: item.is_first }"
              >
                {{ item.tips }}
              </div>
              <div class="month">{{ item.title }}</div>
              <div class="money">
                <div class="now">
                  <span>{{ text_list.pay_symbol }}</span
                  >{{ item.amount }}
                </div>
                <div class="old" v-if="item.show_amount">
                  {{ text_list.pay_symbol }}<span>{{ item.show_amount }}</span>
                </div>
              </div>
              <div class="tip">
                {{ $t('开通立返') }}<span>{{ item.rebate_gold }}</span
                >{{ $t('金币') }}
              </div>
              <div
                class="bottom-desc"
                :class="{
                  hidden: !item.show_tips.amount && !item.show_tips.desc,
                }"
              >
                {{ item.show_tips.amount || item.show_tips.desc }}
              </div>
            </div>
          </template>
        </div>
        <div class="buy-fixed">
          <div class="recharge-btn btn" @click="clickRechargeBtn">
            <div class="recharge-text" v-if="userSvipInfo.svip_status == 1">{{
              $t('立即续费')
            }}</div>
            <div class="recharge-text" v-else>{{ $t('立即开通') }}</div>
            <!-- <div class="time-out">
              {{ $t('距离优惠结束仅剩') }}<span>{{ countDownText }}</span>
            </div> -->
          </div>
        </div>
      </div>

      <div class="container" v-if="buySvipList.length && swiperShow">
        <div class="white-wrapper"></div>
        <swiper :options="swiperOption" class="history-list">
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in buySvipList"
            :key="index"
            class="swiper-no-swiping"
          >
            <div class="history-item">
              <user-avatar
                class="avatar"
                :self="false"
                :src="item.avatar"
              ></user-avatar>
              <div class="nickname">{{ item.nickname }}</div>
              <div class="history-desc">
                {{ item.time }}{{ $t('购买了') }}<span>{{ item.day }}</span>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>

      <!-- SVIP专属福利 -->
      <!-- <div class="container" @click="goToSvipWelfare" v-if="!isSdk">
        <div class="svip-feature-banner">
          <img src="@/assets/images/recharge/svip-feature-banner.png" alt="" />
        </div>
      </div> -->

      <div class="container">
        <div class="welfare-section-title">
          {{ $t('SVIP会员超值权益，开卡即回本') }}
        </div>
        <div class="welfare-list">
          <template v-for="item in privilege">
            <div class="welfare-item" :key="item.title">
              <div class="welfare-icon">
                <img :src="item.img" alt="" />
              </div>
              <div class="welfare-right">
                <div class="welfare-title">{{ item.title }}</div>
                <div class="welfare-desc">{{ item.info }}</div>
              </div>
              <div
                class="welfare-btn"
                v-if="item.action_code && !isSdk"
                @click="clickWelfare(item)"
              >
                {{ item.button_text }}
              </div>
            </div>
          </template>
        </div>
        <div class="explain">
          {{ text_list.illustrate
          }}<span class="btn" @click="gameDialogShow = true"
            >{{ $t('点击查询') }}＞</span
          >
        </div>
      </div>
    </div>
    <!-- 查询可用游戏 -->
    <van-dialog
      v-model="gameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
    >
      <div class="search-container">
        <div class="close-search" @click="gameDialogShow = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="inputGame"
                :placeholder="$t('输入游戏名')"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">{{ $t('搜索') }}</div>
        </div>
        <div class="game-list" v-if="gameList.length">
          <div class="game-item btn" v-for="item in gameList" :key="item.id">
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : $t('不')
                }}{{ $t('支持使用金币抵扣') }}
              </div>
            </div>
          </div>
        </div>
        <content-empty :tips="$t('没有相关游戏')" v-else></content-empty>
      </div>
    </van-dialog>
    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="this.selectedMeal.amount"
      :unit="text_list.pay_symbol"
    ></pay-type-popup>

    <!-- 季卡广告弹窗 -->
    <van-popup
      v-model="isSvipAdPopupShow"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="svip-ad-popup"
    >
      <div class="popup-container" v-if="hide_discounts">
        <div class="close" @click="closeSvipAdPopupShow"></div>
        <div class="title">{{ hide_discounts.title }}</div>
        <div class="card-container">
          <div class="discount">
            <span>{{ hide_discounts.coupon.discount }}</span
            >{{ $t('折') }}
          </div>
          <div class="card-right">
            <div class="card-title">{{ hide_discounts.coupon.title }}</div>
            <div class="card-desc">{{ hide_discounts.coupon.subtitle }}</div>
          </div>
        </div>
        <div class="tips">
          {{ hide_discounts.offer_information
          }}<span class="now"
            >{{ text_list.pay_symbol }}{{ hide_discounts.amount }}</span
          ><span class="old"
            >{{ text_list.pay_symbol }}{{ hide_discounts.show_amount }}</span
          >
        </div>
        <div class="buy-now" @click="goToSvipDiscount">
          {{ $t('立即抢购') }}
        </div>
      </div>
    </van-popup>

    <van-popup
      v-model="double11Popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="double-11-popup"
    >
      <div class="double-11-banner" @click="goToDouble11">
        <img :src="double11_img" alt="" />
      </div>
      <div class="popup-close" @click="double11Popup = false"></div>
    </van-popup>
  </div>
</template>

<script>
import welfare0 from '@/assets/images/recharge/<EMAIL>';
import welfare1 from '@/assets/images/recharge/<EMAIL>';
import welfare2 from '@/assets/images/recharge/<EMAIL>';
import welfare3 from '@/assets/images/recharge/<EMAIL>';
import welfare4 from '@/assets/images/recharge/<EMAIL>';
import welfare5 from '@/assets/images/recharge/<EMAIL>';
import welfare6 from '@/assets/images/recharge/<EMAIL>';
import welfare7 from '@/assets/images/recharge/<EMAIL>';
import welfare8 from '@/assets/images/recharge/welfare-8.png';
import welfare9 from '@/assets/images/recharge/welfare-9.png';
import welfare10 from '@/assets/images/recharge/welfare-10.png';
import welfare11 from '@/assets/images/recharge/welfare-11.png';
import welfare12 from '@/assets/images/recharge/welfare-12.png';
import welfare13 from '@/assets/images/recharge/welfare-13.png';
import welfare14 from '@/assets/images/recharge/welfare-14.png';
import welfare15 from '@/assets/images/recharge/welfare-15.png';
import {
  ApiGetUserSvipInfo,
  ApiSvipIndex,
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGameCardSearchGame,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import {
  platform,
  BOX_openInNewNavWindow,
  BOX_openInNewNavWindowRefresh,
  BOX_showActivity,
  BOX_showActivityByAction,
  BOX_openInNewWindow,
  BOX_goToGame,
  BOX_login,
  isSdk,
  Box_postMessage,
  BOX_goToCouponCenter,
  BOX_setPayParams
} from '@/utils/box.uni.js';
import { PageName } from '@/utils/actionCode.js';
import h5Page from '@/utils/h5Page.js';
export default {
  name: 'Svip',
  data() {
    return {
      isSdk,
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      selectedMeal: {},
      svipList: [],
      payList: [],
      privilege: [], // svip特权列表
      selectedPayType: 'wx', // 支付方式
      payPopupShow: false,
      userSvipInfo: {},
      overTime: '0000-00-00', // 活动到期时间
      welfareDialogShow: false, // 特权弹窗
      welfareSelectedItem: {}, // 点击的特权项
      countdown: {
        endTime: [0, 0, 0],
        nowTime: 0,
      },
      timeClock: null,
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 2,
        speed: 800,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      },
      buySvipList: [],
      swiperShow: true,
      gameDialogShow: false,
      gameList: [],
      inputGame: '',

      isSvipAdPopupShow: false, // 当前季卡广告弹窗是否显示
      hide_discounts: null, // 季卡广告弹窗内容

      text_list: {}, // 一些带翻译的文案字段

      double11Popup: false,
      double11PopupCache: false,
      double11_img: '',
    };
  },
  computed: {
    // svip每日签到额外奖励修改 新增判断新用户签到额外奖励改成10金币（本页面有三处） 2023年6月6日16:24:52
    welfareList() {
      return [
        {
          title: '金币兑换专享',
          desc: 'SVIP享有金币兑换平台币功能',
          icon: welfare0,
        },
        {
          title: this.$t('签到奖励'),
          desc: `每日签到双倍金币，宝箱奖励翻倍`,
          icon: welfare1,
        },
        {
          title: this.$t('补签权益'),
          desc: '享受每月2次补签',
          icon: welfare2,
        },
        {
          title: this.$t('SVIP代金券'),
          desc: this.$t('可在部分游戏内兑换超值SVIP代金券'),
          icon: welfare3,
        },
        {
          title: this.$t('SVIP专属客服'),
          desc: this.$t('尊享专属SVIP客服服务'),
          icon: welfare4,
        },
        {
          title: this.$t('小号回收权益'),
          desc: this.$t('小号回收享受返8%金币'),
          icon: welfare5,
          hide: !this.userSvipInfo?.show_xhhs,
        },
        {
          title: this.$t('SVIP尊贵身份标识'),
          desc: this.$t('互动评论展示专属头像挂饰、昵称颜色'),
          icon: welfare7,
        },
        {
          title: this.$t('每日任务经验加成'),
          desc: this.$t('完成每日任务，获取额外经验值加成'),
          icon: welfare8,
        },
        {
          title: this.$t('财富值加成'),
          desc: this.$t('获取额外财富值加成'),
          icon: welfare9,
        },
        {
          title: this.$t('SVIP优惠券'),
          desc: this.$t('达到等级门槛送更多大额优惠券'),
          icon: welfare10,
        },
        {
          title: this.$t('金币抵扣比例提升'),
          desc: this.$t('享金币抵扣比例提升的福利'),
          icon: welfare11,
        },
        // {
        //   title: this.$t("每周福利日"),
        //   desc: this.$t("每周享大额优惠券、财富值翻倍加成等各种福利"),
        //   icon: welfare12,
        // },
        {
          title: this.$t('财富值不衰退'),
          desc: this.$t('SVIP享财富值不衰退特权'),
          icon: welfare13,
        },
        {
          title: this.$t('会员生日福利'),
          desc: '财富值21级及以上的会员用户生日当天享单款BT游戏半价特权',
          icon: welfare14,
        },
      ];
    },
    countDownText() {
      let currentEndTime = this.countdown.endTime.find(item => {
        return item > this.countdown.nowTime;
      });

      if (currentEndTime) {
        return this.formatTime(currentEndTime - this.countdown.nowTime);
      }
    },
  },

  async created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.swiperShow = true;
    await this.getSvipInfo();
    if (this.double11_img && !this.double11PopupCache) {
      this.double11Popup = true;
      this.double11PopupCache = true;
    }
    await this.getSvipList();
    await this.getPayMethod();
    // this.selectedPayType = this.payList[0].key;
  },
  deactivated() {
    this.swiperShow = false;
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeRouteLeave(to, from, next) {
    if (this.hide_discounts) {
      if (
        ['ClockIn', 'GoldCoinExchange', 'SvipWelfare', 'CouponCenter'].includes(
          to.name,
        )
      ) {
        next();
        return;
      }
      this.isSvipAdPopupShow = true;
      next(false);
      return;
    }
    next();
  },
  methods: {
    closeSvipAdPopupShow() {
      this.hide_discounts = null;
      this.isSvipAdPopupShow = false;
    },
    toLogin() {
      BOX_login();
    },
    goToSvipDiscount() {
      this.closeSvipAdPopupShow();
      this.$nextTick(() => {
        BOX_openInNewWindow(
          { name: 'SvipDiscount' },
          { url: `${window.location.origin}/#/svip_discount` },
        );
      });
    },
    goToSvipWelfare() {
      this.$nextTick(() => {
        BOX_openInNewWindow(
          { name: 'SvipWelfare' },
          { url: `${window.location.origin}/#/svip_welfare` },
        );
      });
    },
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let hour = this.addZero(Math.floor(timeStamp / 3600));
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return `${hour}时-${minute}分-${second}秒`;
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    goToSignIn() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'Welfare',
          type: 1,
        });
      } catch (e) {
        BOX_showActivity(
          { name: 'Welfare', params: { type: 1 } },
          { page: 'qd' },
        );
      }
    },
    clickWelfare(item) {
      switch (item.action_code) {
        // 签到
        case 1014:
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        // 金币商城
        case 1032:
          BOX_openInNewWindow(
            { name: 'SvipWelfare' },
            { url: `${window.location.origin}/#/svip_welfare` },
          );
          break;
        // 金币商城
        case 33:
          BOX_goToCouponCenter(
            { name: 'CouponCenter', params: { index: 1 } },
            { position: 1 },
          );
          break;
        // 客服页面
        case 1051:
          BOX_openInNewWindow(
            { name: 'Kefu' },
            { url: `${window.location.origin}/#/kefu` },
          );
          break;
        // 账号回收
        case 1048:
          BOX_showActivity({ name: 'Recycle' }, { page: 'xhhs' });
          break;
        // svip优惠券
        case 20:
          BOX_showActivity(
            { name: 'Welfare648' },
            { page: 'com.a3733.cwbgamebox.ui.welfareCenter.Gift648Activity' },
          );
          break;
      }
    },
    clickGiftGame(item) {
      this.toPage('GameGift', { game_id: item.id });
    },
    clickcouponGame(item) {
      this.toPage('GameCoupon', { game_id: item.id });
    },
    handleWelfareConfirm() {
      this.welfareDialogShow = false;
      if (this.welfareSelectedItem.routerName) {
        this.toPage(this.welfareSelectedItem.routerName);
      }
    },
    async getSvipList() {
      const res = await ApiSvipIndex();
      this.svipList = res.data.svipList;
      this.selectedMeal =
        this.userSvipInfo.is_first_recharge == 1
          ? this.svipList[1]
          : this.svipList[0]; // 2022年7月7日10:10:36，zyq说默认选中第二个套餐(又变了)
      // this.payList = res.data.payArr;
      this.privilege = res.data.privilege;
      this.text_list = res.data.text_list;
      this.buySvipList = res.data.buySvipList;
      this.countdown = res.data.countdown;
      this.overTime = res.data.overTime;
      // 清除定时器
      clearInterval(this.timeClock);
      this.timeClock = null;
      this.timeClock = setInterval(() => {
        this.countdown.nowTime += 1;
      }, 1000);
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payList = res.data;
    },
    clickRechargeBtn() {
      this.payPopupShow = true;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      const orderParams = {
        day: this.selectedMeal.day,
        amount: this.selectedMeal.amount,
        rebate_gold: this.selectedMeal.rebate_gold,
        payWay: this.selectedPayType,
        is_cycle: 0,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        // 安卓sdk下单上报
        BOX_setPayParams({
          order_id: orderRes.data.orderId,
          productname: '充值SVIP',
        })
        await ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 103,
          });
        });
        await this.getSvipInfo();
        await this.getSvipList();
      });
    },
    async getSvipInfo() {
      const res = await ApiGetUserSvipInfo();
      this.userSvipInfo = res.data;

      // 双十一弹窗图
      this.double11_img = res.data.double11_img;

      // this.hide_discounts = res.data.hide_discounts;
      // 有活动就不显示季卡折扣弹窗了 2024年11月7日16:00:58
      this.hide_discounts = this.double11_img ? null : res.data.hide_discounts;

      // 通知安卓端返回的时候是否要显示折扣季卡
      Box_postMessage({
        isShowSvipCloseDialog: this.hide_discounts ? true : false,
      });
    },
    toPage(name, params = {}) {
      this.$router.push({
        name: name,
        params: params,
      });
    },
    searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 10,
      })
        .then(res => {
          this.gameList = res.data.list;
        })
        .finally(() => {
          this.$toast.clear();
        });
    },
    toGame(item) {
      this.toPage('GameDetail', {
        id: item.id,
      });
    },

    goToDouble11() {
      BOX_openInNewWindow(
        {
          h5_url: `https://${h5Page.env}activity.3733.com/#/double_eleven_activity`,
        },
        {
          url: `https://${h5Page.env}activity.3733.com/#/double_eleven_activity`,
        },
      );
      this.double11Popup = false;
    },
  },
};
</script>

<style lang="less" scoped>
.svip-page {
  .sign-in-btn {
    display: flex;
    align-items: center;
    font-size: 12 * @rem;
    line-height: 15 * @rem;
    color: #fff;
    &.black {
      color: #000000;
    }

    &::before {
      content: '';
      display: block;
      width: 15 * @rem;
      height: 15 * @rem;
      background: url(~@/assets/images/recharge/right-top-icon.png) no-repeat;
      background-size: 15 * @rem 15 * @rem;
      margin-right: 2 * @rem;
      margin-top: -1 * @rem;
    }
  }
  .top-bar {
    box-sizing: border-box;
    width: 100%;
    height: 256 * @rem;
    height: calc(256 * @rem + @safeAreaTop);
    height: calc(256 * @rem + @safeAreaTopEnv);
    .image-bg('~@/assets/images/recharge/svip-top-bg-new.png');
    background-size: 100% 256 * @rem;
    background-size: 100% calc(256 * @rem + @safeAreaTop);
    background-size: 100% calc(256 * @rem + @safeAreaTopEnv);
    padding-top: 56 * @rem;
    padding-top: calc(56 * @rem + @safeAreaTop);
    padding-top: calc(56 * @rem + @safeAreaTopEnv);
    .user-bar {
      box-sizing: border-box;
      overflow: hidden;
      width: 335 * @rem;
      height: 182 * @rem;
      .image-bg('~@/assets/images/recharge/svip-top-bar-bg.png');
      margin: 0 auto;
      padding-bottom: 80 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .no-svip {
        display: flex;
        align-items: flex-end;
        margin-top: 22 * @rem;
        margin-left: 15 * @rem;
        height: 40 * @rem;
        .no-svip-icon {
          width: 74 * @rem;
          height: 40 * @rem;
          .image-bg('~@/assets/images/recharge/svip-text-new.png');
          background-position: center center;
        }
        .no-svip-text {
          font-size: 14 * @rem;
          color: #8b83fb;
          line-height: 18 * @rem;
          font-weight: bold;
          margin-left: 12 * @rem;
        }
      }
      .no-login {
        display: flex;
        align-items: center;
        margin-top: 22 * @rem;
        margin-left: 15 * @rem;
        height: 40 * @rem;
        .no-login-icon {
          width: 74 * @rem;
          height: 40 * @rem;
          .image-bg('~@/assets/images/recharge/svip-text-new.png');
          background-position: center center;
        }
        .avatar {
          width: 36 * @rem;
          height: 36 * @rem;
        }
        .no-login-text {
          flex: 1;
          min-width: 0;
          font-size: 14 * @rem;
          color: #8b83fb;
          line-height: 18 * @rem;
          font-weight: bold;
          margin-left: 12 * @rem;
        }
      }
      .user-info {
        display: flex;
        align-items: center;
        margin-top: 29 * @rem;
        margin-left: 15 * @rem;
        .avatar {
          box-sizing: border-box;
          width: 40 * @rem;
          height: 40 * @rem;
          overflow: hidden;
          border-radius: 50%;
          border: 1 * @rem solid rgba(172, 150, 240, 0.52);
        }
        .info-right {
          margin-left: 11 * @rem;
          flex: 1;
          min-width: 0;
          .nickname {
            display: flex;
            align-items: center;
            padding-right: 10 * @rem;

            .text {
              flex-shrink: 1;
              font-size: 14 * @rem;
              color: #45298b;
              line-height: 18 * @rem;
              font-weight: bold;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            span {
              flex-shrink: 0;
              display: block;
              width: 32 * @rem;
              height: 14 * @rem;
              margin-left: 4 * @rem;
              background: url(~@/assets/images/recharge/SVIPNot-icon.png)
                no-repeat;
              background-size: 32 * @rem 14 * @rem;

              &.haveSvip {
                width: 28 * @rem;
                height: 11 * @rem;
                background-image: url(~@/assets/images/recharge/SVIP-icon.png);
                background-size: 28 * @rem 11 * @rem;
              }
            }
          }
          .end-date {
            font-family:
              PingFang SC,
              PingFang SC;
            font-size: 11 * @rem;
            color: rgba(69, 41, 139, 0.8);
            line-height: 15 * @rem;
            margin-top: 6 * @rem;
          }
        }
      }
      .svip-intro {
        height: 24 * @rem;
        display: flex;
        align-items: center;
        font-size: 11 * @rem;
        color: #ffffff;
        padding: 0 14 * @rem;
        background: rgba(138, 96, 227, 0.55);
        span {
          color: #fff;
        }
      }
    }
  }
  .main {
    position: relative;
    padding-top: 1 * @rem;
    padding-bottom: 80 * @rem;
    border-radius: 16 * @rem 16 * @rem 0 0;
    margin-top: -98 * @rem;
    background-color: #fff;
    .container {
      margin-top: 19 * @rem;
      position: relative;
      .container-title {
        margin: 0 auto;
        font-size: 18 * @rem;
        font-weight: bold;
        color: #6c5e9d;
        padding: 0 20 * @rem;
      }
      .svip-list {
        display: flex;
        padding-left: 20 * @rem;
        padding-top: 14 * @rem;
        overflow-x: auto;
        padding-bottom: 2 * @rem;
        &::-webkit-scrollbar {
          display: none;
        }
        &::after {
          content: '';
          width: 20 * @rem;
          height: 1 * @rem;
          flex-shrink: 0;
        }
        .svip-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          // transition: 0.3s;
          box-sizing: border-box;
          flex-shrink: 0;
          width: 129 * @rem;
          height: 144 * @rem;
          background: #fff;
          border-radius: 12 * @rem;
          border: 1 * @rem solid #d7cdff;
          margin-left: 8 * @rem;
          position: relative;

          &:first-of-type {
            margin-left: 0;
          }

          &.on {
            border: 2 * @rem solid #cfccff;
            background: linear-gradient(158deg, #ffffff 0%, #f6f1ff 100%);
            border-radius: 12 * @rem;

            .tips {
              left: -2 * @rem;
              top: -9 * @rem;
            }

            .month {
              color: #7463ae;
              margin-top: 25 * @rem;
            }
            .money {
              .now {
                color: #ff471f;
                span {
                  color: #ff471f;
                }
              }
            }
            .tip {
              color: rgba(69, 41, 139, 0.5);
              span {
                color: #ff6649;
                font-weight: bold;
              }
            }
            .bottom-desc {
              width: 129 * @rem;
              height: 26 * @rem;
              margin-bottom: -2 * @rem;
              background-color: #8a60e3;
              font-size: 12 * @rem;
              color: #fff;
            }
          }
          .tips {
            position: absolute;
            top: -8 * @rem;
            left: -1 * @rem;
            padding: 0 10 * @rem 0 19 * @rem;
            border-radius: 0 6 * @rem 6 * @rem 0;
            height: 20 * @rem;
            line-height: 20 * @rem;
            background: linear-gradient(
              62deg,
              #ff5506 0%,
              #ff9138 59%,
              #ffbb5a 99%
            );
            color: #fff;
            text-align: center;
            font-size: 11 * @rem;
            font-weight: bold;

            &::before {
              content: '';
              display: block;
              width: 20 * @rem;
              height: 20 * @rem;
              background: url(~@/assets/images/recharge/tips-left-icon.png)
                no-repeat;
              background-size: 20 * @rem 20 * @rem;
              position: absolute;
              top: -3 * @rem;
              left: -5 * @rem;
            }
            &.tips1 {
              background-color: #ffd796;
              color: #ff5c28;

              &::before {
                display: none;
              }
            }
          }
          .month {
            font-size: 14 * @rem;
            color: #7463ae;
            line-height: 18 * @rem;
            font-weight: bold;
            margin-top: 26 * @rem;
            text-align: center;
          }
          .money {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 4 * @rem;
            .now {
              display: flex;
              align-items: center;
              font-size: 28 * @rem;
              color: #6c5e9d;
              font-weight: bold;
              line-height: 35 * @rem;

              span {
                font-weight: bold;
                line-height: 15 * @rem;
                font-size: 12 * @rem;
                margin-right: 2 * @rem;
                margin-top: 11 * @rem;
              }
            }
            .old {
              font-size: 10 * @rem;
              color: rgba(69, 41, 139, 0.5);
              text-decoration: line-through;
              height: 13 * @rem;
              line-height: 13 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 2 * @rem;
              margin-top: 12 * @rem;
            }
          }
          .tip {
            font-size: 11 * @rem;
            line-height: 14 * @rem;
            color: rgba(69, 41, 139, 0.5);
            text-align: center;
            margin-top: 16 * @rem;

            i {
              font-size: 10 * @rem;
              font-weight: 400;
            }
          }

          .bottom-desc {
            width: 100%;
            height: 25 * @rem;
            line-height: 1;
            background-color: #ebeaff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            color: #8259d9;
            font-weight: 600;
            margin-top: 4 * @rem;
            border-radius: 0 0 12 * @rem 12 * @rem;

            // 隐藏但是需要占位
            &.hidden {
              opacity: 0;
            }
          }
        }
      }
      .buy-fixed {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 10 * @rem 16 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        box-shadow: 0 -5 * @rem 5 * @rem rgba(0, 0, 0, 0.035);
        border-top: 1 * @rem solid rgba(0, 0, 0, 0.035);
      }
      .recharge-btn {
        width: 335 * @rem;
        height: 50 * @rem;
        background: #8a60e3;
        border-radius: 32 * @rem;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 * @rem 4 * @rem 8 * @rem 1 * @rem rgba(28, 49, 233, 0.2);
        .recharge-text {
          font-size: 18 * @rem;
          color: #ffffff;
          font-weight: 600;
          line-height: 22 * @rem;
        }
        .time-out {
          font-size: 10 * @rem;
          color: #ffffff;
          line-height: 12 * @rem;
          margin-top: 4 * @rem;
          span {
            color: #ffd600;
            margin-left: 5 * @rem;
          }
        }
      }
      .section-title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18 * @rem;
        color: #fcdebb;
        font-weight: 600;
        line-height: 25 * @rem;
        margin-top: 20 * @rem;
        span {
          margin-right: 8 * @rem;
          display: block;
          width: 18 * @rem;
          height: 18 * @rem;
          background-size: 18 * @rem 18 * @rem;
          background-position: center center;
          background-repeat: no-repeat;
          &.gift-icon {
            background-image: url(~@/assets/images/recharge/gift-icon.png);
          }
          &.coupon-icon {
            background-image: url(~@/assets/images/recharge/gold-coin-coupon.png);
          }
        }
      }
    }
    .white-wrapper {
      width: 100%;
      height: 27 * @rem;
      position: absolute;
      z-index: 8;
      left: 0;
      top: 0;
      background: linear-gradient(
        180deg,
        #ffffff 28%,
        rgba(255, 255, 255, 0) 82%
      );
    }
    .history-list {
      height: 80 * @rem;
      padding: 0 20 * @rem;
      position: relative;

      /deep/ .swiper-wrapper {
        transition-timing-function: linear !important;
      }
      .history-item {
        display: flex;
        align-items: center;
        height: 32 * @rem;
        .avatar {
          width: 32 * @rem;
          width: 32 * @rem;
        }
        .nickname {
          margin-left: 8 * @rem;
          font-size: 12 * @rem;
          color: #9b89c7;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .history-desc {
          width: 180 * @rem;
          color: #9b89c7;
          font-size: 12 * @rem;
          text-align: right;
          span {
            color: #ff471f;
          }
        }
      }
    }
  }
  .svip-feature-banner {
    width: 345 * @rem;
    height: 106 * @rem;
    margin: 0 auto;
  }
  .welfare-section-title {
    font-size: 16 * @rem;
    color: #6c5e9d;
    font-weight: bold;
    height: 20 * @rem;
    line-height: 20 * @rem;
    height: 20 * @rem;
    text-align: center;
    background: url('~@/assets/images/recharge/svip-sub-decoration.png') center
      center no-repeat;
    background-size: 335 * @rem 3 * @rem;
  }
  .welfare-list {
    padding: 0 20 * @rem;
    margin-top: 17 * @rem;
    .welfare-item {
      display: flex;
      align-items: center;
      background-color: #f7f5ff;
      border-radius: 10 * @rem;
      margin-bottom: 8 * @rem;
      // padding: 0 * @rem 12 * @rem;
      padding: 8 * @rem 12 * @rem;
      // height: 52 * @rem;
      .welfare-icon {
        width: 36 * @rem;
        height: 36 * @rem;
      }
      .welfare-right {
        flex: 1;
        min-width: 0;
        display: flex;
        justify-content: center;
        flex-direction: column;
        margin-left: 12 * @rem;
        .welfare-title {
          font-size: 12 * @rem;
          color: #7060a9;
          font-weight: bold;
          line-height: 15 * @rem;
        }
        .welfare-desc {
          font-size: 12 * @rem;
          color: #9b89c7;
          margin-top: 6 * @rem;
          line-height: 15 * @rem;
        }
      }
      .welfare-btn {
        width: 58 * @rem;
        height: 22 * @rem;
        border-radius: 11 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11 * @rem;
        color: #fff;
        background: linear-gradient(105deg, #f9a8d3 0%, #9669de 100%);
        margin-left: 10 * @rem;
      }
    }
  }
  .explain {
    color: #ff471f;
    font-size: 13 * @rem;
    margin: 10 * @rem 20 * @rem;
    line-height: 21 * @rem;
    span {
      color: #7060a9;
    }
  }
  .welfare-dialog {
    width: 285 * @rem;
    background-color: transparent;
    .dialog-close {
      width: 26 * @rem;
      height: 26 * @rem;
      margin: 15 * @rem auto 0;
      background: url(~@/assets/images/recharge/svip-dialog-close.png) no-repeat;
      background-size: 26 * @rem 26 * @rem;
    }
    .welfare-dialog-content {
      width: 285 * @rem;
      height: 300 * @rem;
      background: url(~@/assets/images/recharge/svip-dialog-bg.png) no-repeat;
      background-size: 285 * @rem 300 * @rem;
      overflow: hidden;
      .title {
        font-size: 18 * @rem;
        color: #2b242d;
        font-weight: 600;
        margin-top: 94 * @rem;
        text-align: center;
      }
      .desc {
        box-sizing: border-box;
        font-size: 14 * @rem;
        color: #2b242d;
        height: 80 * @rem;
        width: 100%;
        padding: 0 30 * @rem;
        line-height: 20 * @rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        margin-top: 20 * @rem;
        text-align: center;
      }
      .welfare-btn {
        width: 166 * @rem;
        height: 38 * @rem;
        border-radius: 19 * @rem;
        font-size: 14 * @rem;
        background: linear-gradient(90deg, #dc7467 0%, #ac6eb7 100%);
        box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem
          rgba(175, 111, 176, 0.47);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20 * @rem auto 0;
      }
    }
  }
  .pay-container-popup {
    .pay-container {
      padding: 10 * @rem 14 * @rem 0;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .pay-way-close {
        position: absolute;
        right: 10 * @rem;
        top: 10 * @rem;
        width: 40 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/recharge/recharge-popup-close.png)
          center center no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }
      .pay-way-title {
        font-size: 16 * @rem;
        color: #333;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pay-list {
        .pay-item {
          display: flex;
          align-items: center;
          padding: 16 * @rem 0;
          border-bottom: 1 * @rem solid #eeeeee;
          .icon {
            width: 24 * @rem;
            height: 24 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 24 * @rem 24 * @rem;
            // &.wx {
            //   background-image: url(~@/assets/images/recharge/wx-icon.png);
            //   background-size: 24 * @rem 21 * @rem;
            // }
            // &.zfb_dmf {
            //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
            //   background-size: 24 * @rem 24 * @rem;
            // }
          }
          .pay-center {
            margin-left: 8 * @rem;
            .line {
              display: flex;
              align-items: center;
              .pay-name {
                font-size: 15 * @rem;
                color: #333;
              }
              .recommend-icon {
                width: 39 * @rem;
                height: 17 * @rem;
                background-size: 39 * @rem 17 * @rem;
                background-position: left center;
                background-repeat: no-repeat;
                margin-left: 5 * @rem;
              }
            }
            .subtitle {
              color: #999;
              font-size: 12 * @rem;
              line-height: 20 * @rem;
            }
          }
          .choose {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/recharge/n_radio.png) center center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            margin-left: auto;
            &.on {
              background-image: url(~@/assets/images/recharge/c_radio.png);
            }
          }
        }
      }
      .pay-btn {
        width: 290 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #8a60e3;
        font-size: 18 * @rem;
        font-weight: bold;
        color: #ffffff;
        margin: 30 * @rem auto;
        border-radius: 23 * @rem;
        box-shadow: 0 * @rem 2 * @rem 13 * @rem 0 * @rem rgba(99, 68, 24, 0.24);
      }
    }
  }
  /deep/ .van-dialog {
    overflow: unset;
    width: 320 * @rem;
  }
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    max-height: 100vh;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/recharge/close-search.png) center center
        no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 10 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}

.svip-ad-popup {
  .popup-container {
    box-sizing: border-box;
    width: 305 * @rem;
    height: 278 * @rem;
    border-radius: 16 * @rem;
    background-color: #fff;
    position: relative;
    padding: 28 * @rem 0 0;
    .close {
      width: 48 * @rem;
      height: 48 * @rem;
      background: url(~@/assets/images/recharge/svip-ad-close.png) center center
        no-repeat;
      background-size: 16 * @rem 16 * @rem;
      position: absolute;
      right: 0;
      top: 0;
    }
    .title {
      font-size: 18 * @rem;
      color: #333333;
      line-height: 23 * @rem;
      font-weight: 600;
      text-align: center;
    }
    .card-container {
      width: 227 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/recharge/svip-ad-card-bg-new.png)
        no-repeat;
      background-size: 227 * @rem 72 * @rem;
      margin: 30 * @rem auto 0;
      display: flex;
      align-items: center;
      .discount {
        width: 82 * @rem;
        font-size: 16 * @rem;
        color: #fff;
        text-align: center;
        span {
          font-size: 26 * @rem;
        }
      }
      .card-right {
        flex: 1;
        min-width: 0;
        margin-left: 20 * @rem;
        .card-title {
          font-size: 14 * @rem;
          color: #ffffff;
          line-height: 18 * @rem;
        }
        .card-desc {
          font-size: 11 * @rem;
          color: #ffffff;
          line-height: 14 * @rem;
          margin-top: 5 * @rem;
        }
      }
    }
    .tips {
      font-size: 15 * @rem;
      color: #333333;
      line-height: 25 * @rem;
      text-align: center;
      margin: 10 * @rem auto 0;
      .old {
        font-size: 15 * @rem;
        color: #999999;
        text-decoration: line-through;
      }
      .now {
        font-size: 20 * @rem;
        color: #fd2727;
        font-weight: 600;
      }
    }
    .buy-now {
      font-size: 15 * @rem;
      color: #fff;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20 * @rem;
      width: 165 * @rem;
      height: 40 * @rem;
      margin: 25 * @rem auto 0;
      background-color: #895ff6;
      box-shadow: 0 * @rem 4 * @rem 8 * @rem 0 * @rem rgba(28, 36, 233, 0.2);
    }
  }
}

.double-11-popup {
  background: transparent;
  .double-11-banner {
    width: 300 * @rem;
    height: 350 * @rem;
  }
  .popup-close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/recharge/activity-popup-close.png) center
      center no-repeat;
    background-size: 28 * @rem 28 * @rem;
    margin: 28 * @rem auto 0;
  }
}
</style>
