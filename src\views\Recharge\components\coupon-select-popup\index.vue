<template>
  <div class="coupon-popup">
    <van-popup
      v-model="popupShow"
      position="bottom"
      :lock-scroll="false"
      round
      @click-overlay="closePopup"
      class="coupon-container-popup"
    >
      <div class="coupon-container">
        <div class="popup-title">{{ $t('选择优惠券') }}</div>
        <div class="close-btn" @click="closePopup"></div>
        <div
          class="coupon-list"
          v-if="satisfy_coupon_list.length || not_satisfy_coupon_list.length"
        >
          <div
            class="coupon-item"
            v-for="coupon in satisfy_coupon_list"
            :key="coupon.id"
            @click="chooseCoupon(coupon)"
          >
            <div class="coupon-top">
              <div class="discount"><span>￥</span>{{ coupon.money }}</div>
              <div class="coupon-title">{{ coupon.title }}</div>
              <div
                class="select"
                :class="{ selected: coupon.id == selectedCoupon.id }"
              ></div>
            </div>
            <div class="coupon-bottom">
              <div class="date">{{ coupon.expire_time_text }}</div>
              <div class="limit">{{ coupon.remark }}</div>
            </div>
          </div>
          <div
            class="no-use-btn"
            @click="notChooseCoupon"
            v-if="satisfy_coupon_list.length"
            >{{ $t('不使用优惠券') }}</div
          >
          <div class="list-title">{{ $t('以下此订单暂不适用') }}</div>
          <div
            class="coupon-item not-use-coupon"
            v-for="coupon in not_satisfy_coupon_list"
            :key="coupon.id"
            @click="showCantUseCoupon"
          >
            <div class="coupon-top">
              <div class="discount"><span>￥</span>{{ coupon.money }}</div>
              <div class="coupon-title">{{ coupon.title }}</div>
            </div>
            <div class="coupon-bottom">
              <div class="date">{{ coupon.expire_time_text }}</div>
              <div class="limit">{{ coupon.remark }}</div>
            </div>
          </div>
        </div>
        <div class="empty-box" v-else>
          <img
            src="@/assets/images/recharge/coupon-popup/empty-box.png"
            alt=""
          />
          <div class="empty-text">暂无可用的优惠券</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'couponSelectPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    satisfy_coupon_list: {
      type: Array,
      default: [],
    },
    not_satisfy_coupon_list: {
      type: Array,
      default: [],
    },
    nowCoupon: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      selectedCoupon: {},
    };
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },

  watch: {
    nowCoupon(val) {
      this.selectedCoupon = val;
    },
  },
  methods: {
    closePopup() {
      this.$emit('chooseCoupon', this.selectedCoupon);
      this.popupShow = false;
    },
    chooseCoupon(coupon) {
      this.selectedCoupon = coupon;
      this.closePopup();
    },
    notChooseCoupon() {
      this.selectedCoupon = {};
      this.closePopup();
    },
    showCantUseCoupon() {
      this.$toast('此订单暂不适用');
    },
  },
};
</script>
<style lang="less" scoped>
.coupon-container {
  .popup-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 56 * @rem;
    line-height: 56 * @rem;
    text-align: center;
    font-weight: 600;
    font-size: 18 * @rem;
    color: #191b1f;
  }
  .close-btn {
    position: absolute;
    top: 18 * @rem;
    right: 12 * @rem;
    width: 24 * @rem;
    height: 24 * @rem;
    background: url(~@/assets/images/recharge/coupon-popup/coupon-popup-close.png)
      no-repeat;
    background-size: 24 * @rem 24 * @rem;
  }

  .coupon-list {
    width: 100%;
    max-height: 60vh;
    padding: 16 * @rem 18 * @rem;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;

    .coupon-item {
      width: 100%;
      height: 115 * @rem;
      background: url(~@/assets/images/recharge/coupon-popup/can-use-coupon-bg.png)
        no-repeat;
      background-size: 100% 115 * @rem;
      margin-bottom: 12 * @rem;

      .coupon-top {
        display: flex;
        align-items: center;
        width: 100%;
        height: 72 * @rem;

        .discount {
          display: flex;
          align-items: baseline;
          justify-content: center;
          width: 100 * @rem;
          font-weight: 600;
          font-size: 26 * @rem;
          color: #fd5635;
          text-align: center;
          margin-right: 6 * @rem;
          span {
            font-weight: 600;
            font-size: 13 * @rem;
            color: #fd5635;
            margin-right: 3 * @rem;
          }
        }
        .coupon-title {
          flex: 1;
          min-width: 0;
          max-height: 42 * @rem;
          line-height: 21 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          color: #30343b;
          overflow: hidden;
        }
        .select {
          width: 22 * @rem;
          height: 22 * @rem;
          background: url(~@/assets/images/recharge/coupon-popup/select.png)
            no-repeat;
          background-size: 22 * @rem 22 * @rem;
          margin-left: 5 * @rem;
          margin-right: 19 * @rem;

          &.selected {
            background-image: url(~@/assets/images/recharge/coupon-popup/selected.png);
          }
        }
      }

      .coupon-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 43 * @rem;
        margin: 0 18 * @rem;
        border-top: 1 * @rem dashed #efd8bb;

        .date {
          font-weight: 400;
          font-size: 11 * @rem;
          color: #ff775b;
        }

        .limit {
          font-weight: 400;
          font-size: 11 * @rem;
          color: #ff775b;
        }
      }

      &.not-use-coupon {
        background-image: url(~@/assets/images/recharge/coupon-popup/cant-use-coupon-bg.png);
        .coupon-top {
          .discount {
            color: #93999f;
            span {
              color: #93999f;
            }
          }
          .coupon-title {
            color: #93999f;
            font-weight: 500;
          }
        }
        .coupon-bottom {
          border-top-color: #93999f;
          .date {
            color: #93999f;
          }
          .limit {
            color: #93999f;
          }
        }
      }
    }

    .no-use-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 35 * @rem;
      background: #ffffff;
      border-radius: 6 * @rem;
      border: 1 * @rem solid #bec2c5;
      margin: 14 * @rem 0;
      font-weight: 400;
      font-size: 15 * @rem;
      color: #30343b;
      line-height: 19 * @rem;
      text-align: center;
    }

    .list-title {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16 * @rem 0;
      font-weight: 400;
      font-size: 13 * @rem;
      line-height: 18 * @rem;
      color: #93999f;
      text-align: center;

      &::before {
        content: '';
        display: block;
        flex-grow: 1;
        height: 1 * @rem;
        background-color: #e3e5e8;
        margin-right: 12 * @rem;
        max-width: 80 * @rem;
      }
      &::after {
        content: '';
        display: block;
        flex-grow: 1;
        height: 1 * @rem;
        background-color: #e3e5e8;
        margin-left: 12 * @rem;
        max-width: 80 * @rem;
      }
    }
  }
  .empty-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    font-size: 16 * @rem;
    color: #191b1f;
    padding: 110 * @rem 0 157 * @rem;
    img {
      width: 128 * @rem;
      height: 128 * @rem;
    }

    .empty-text {
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
      line-height: 15 * @rem;
      text-align: center;
      margin-top: 6 * @rem;
    }
  }
}
</style>
