<template>
  <div class="page savings-card-page">
    <nav-bar-2
      :border="false"
      :title="$t('省钱卡')"
      :placeholder="false"
      :bgStyle="bgStyle"
      :azShow="true"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :backShow="!hideBack"
    >
      <template #right>
        <div
          class="coin-tips"
          :class="{ black: bgStyle == 'transparent' }"
          @click="toPage('GoldCoinTips')"
        >
          {{ $t('金币小贴士') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-container">
        <div class="user-card">
          <div class="card-info">
            <div class="user-info">
              <user-avatar
                class="avatar"
                :src="pageInfo.user_info && pageInfo.user_info.avatar"
              ></user-avatar>
              <div class="user-right">
                <div class="line-1">
                  <div class="nickname">
                    {{ pageInfo.user_info && pageInfo.user_info.nickname }}
                  </div>
                  <div class="no-buy" v-if="!pageInfo.is_member">
                    {{ $t('暂未购买') }}
                  </div>
                  <div class="card-time" v-else>
                    {{ $t('有效期至') }}：{{ pageInfo.end_time }}
                  </div>
                </div>
                <div class="card-intro">{{ text_list.gold_welfare }}</div>
              </div>
            </div>
          </div>
          <div class="card-bottom">
            <div class="bottom-left">
              <div class="text">{{ $t('累计已省') }}（{{ $t('元') }}）</div>
              <div class="total-gold">{{ pageInfo.payTotal }}</div>
            </div>
            <div class="card-btn btn" v-if="!isSdk" @click="goToGoldCoinCenter">
              {{ $t('金币中心') }}
            </div>
          </div>
        </div>
      </div>

      <template v-if="countdown && countdown.endTime && countdownPopup">
        <div class="countdown-container" @click.stop="">
          <div class="countdown-text">超值限时秒杀 | 倒计时</div>
          <div class="countdown-clock">
            <span>{{ countdownObj.day }}</span
            >天 <span>{{ countdownObj.hour }}</span
            >时 <span>{{ countdownObj.minute }}</span
            >分
            <span>{{ countdownObj.second }}</span>
          </div>
          <!-- <div class="countdown-close" @click="clickCloseCountdownPopup"></div> -->
        </div>
      </template>
      <div class="card-list">
        <div
          class="card-item"
          :class="{ current: item.type == selectedCard.type }"
          v-for="(item, index) in cardList"
          :key="index"
          @click="clickCard(item)"
        >
          <div class="card">
            <div v-if="item.tips_icon" class="tag">
              <img :src="item.tips_icon" />
            </div>
            <div class="icon">
              <img :src="item.icon" :alt="item.title" />
            </div>
            <div class="card-center">
              <div class="title">{{ item.welfare }}</div>
              <div class="desc">{{ text_list.gold_day }}</div>
              <div
                class="desc"
                :style="{ color: item.total_gold_info.color }"
                >{{ item.total_gold_info.text }}</div
              >
            </div>
            <div class="card-right">
              <div class="money-now">
                {{ text_list.pay_symbol }}{{ item.amount }}
              </div>
              <div class="money-old">{{ item.original_price_text }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="buy-fixed">
        <div class="buy-btn" @click="clickBuyBtn" v-if="!pageInfo.is_member">
          <div class="subscript">{{ $t('开通即回本') }}</div>
          {{ $t('立即购买') }}
        </div>
        <template v-else>
          <div
            class="buy-btn"
            v-if="pageInfo.is_receive == 1"
            @click="takeReward()"
          >
            {{ $t('领取金币') }}
          </div>
          <div
            class="buy-btn had"
            v-else-if="pageInfo.is_receive == 2"
            @click="takeReward(true)"
          >
            {{ $t('已领取') }}
          </div>
        </template>
      </div>
      <div class="explain-container">
        <div class="explain-title"></div>
        <div class="content" v-html="text_list.illustrate"></div>
      </div>
      <!-- 支付弹窗抽屉 -->
      <pay-type-popup
        :show.sync="payPopupShow"
        :list="payList"
        @choosePayType="choosePayType"
        :money="selectedCard.amount"
        :unit="text_list.pay_symbol"
      ></pay-type-popup>
      <!-- <van-popup
        v-model="payPopupShow"
        position="bottom"
        :lock-scroll="false"
        round
        class="pay-container-popup"
        :close-on-popstate="true"
      >
        <div class="pay-container">
          <div class="pay-way-title">{{ $t('选择支付方式') }}</div>
          <div class="pay-way-close btn" @click="payPopupShow = false"></div>
          <div class="pay-list">
            <div
              class="pay-item"
              v-for="(item, index) in payList"
              :key="index"
              @click="selectedPayType = item.key"
            >
              <div
                class="icon"
                :style="{ backgroundImage: `url(${item.icon})` }"
              ></div>
              <div class="pay-center">
                <div class="line">
                  <div class="pay-name">{{ item.name }}</div>
                  <div
                    class="recommend-icon"
                    v-if="item.tag_img"
                    :style="{ backgroundImage: `url(${item.tag_img})` }"
                  ></div>
                </div>
                <div v-if="item.subtitle" class="subtitle">
                  {{ item.subtitle }}
                </div>
              </div>
              <div
                class="choose"
                :class="{ on: selectedPayType == item.key }"
              ></div>
            </div>
          </div>
          <div class="pay-btn btn" @click="handlePay">
            {{ $t('确认支付') }}{{ text_list.pay_symbol
            }}{{ selectedCard.amount }}
          </div>
        </div>
      </van-popup> -->
    </div>
  </div>
</template>

<script>
import {
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiSavingsCardIndex,
  ApiSavingsCardCreateOrder,
  ApiSavingsCardTakeReceive,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import {
  platform,
  BOX_openInNewNavWindow,
  BOX_openInNewNavWindowRefresh,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_showActivityByAction,
  BOX_goToGame,
  isSdk,
  BOX_setPayParams,
  isAndroidSdk,
} from '@/utils/box.uni.js';
import { getQueryVariable } from '@/utils/function.js';
import { mapActions } from 'vuex';
export default {
  name: 'SavingsCard',
  data() {
    return {
      isSdk,
      isAndroidSdk,
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      hideBack: false, //是否隐藏返回按钮
      pageInfo: {},
      cardList: [],
      payPopupShow: false,
      selectedCard: {},
      selectedPayType: 'wx',
      payList: [],

      text_list: {}, // 一些带翻译的文案字段
      countdown: {}, // 显示倒计时对象
      timeClock: null, // 倒计时定时器
      countdownPopup: false, // 倒计时弹窗是否展示
    };
  },
  computed: {
    countdownObj() {
      if (this.countdown && this.countdown.endTime) {
        return this.formatTime(this.countdown.endTime - this.countdown.nowTime);
      } else {
        return {};
      }
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = '省钱卡';
    }
    this.hideBack = !!getQueryVariable('hideBack');
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.SET_USER_INFO(true);
    await this.getSavingsCardIndex();
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  deactivated() {
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      this.SET_USER_INFO(true);
      await this.getSavingsCardIndex();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    goToGoldCoinCenter() {
      BOX_openInNewWindow(
        { name: 'GoldCoinCenter' },
        { url: `${window.location.origin}/#/gold_coin_center` },
      );
    },
    async getSavingsCardIndex() {
      const res = await ApiSavingsCardIndex();
      this.cardList = res.data.card_list;
      // this.payList = res.data.payArr;
      this.pageInfo = res.data.info;
      this.text_list = res.data.text_list;
      // this.selectedPayType = this.payList[0].key;
      this.countdown = res.data.countdown;
      if (!this.pageInfo.is_member) {
        this.selectedCard = this.cardList.find(item => {
          return item.is_default == 1;
        });

        // 判断是否有弹窗显示的缓存
        const SAVINGS_COUNTDOWN_SHOW = localStorage.getItem(
          'SAVINGS_COUNTDOWN_SHOW',
        );
        if (
          SAVINGS_COUNTDOWN_SHOW &&
          SAVINGS_COUNTDOWN_SHOW == new Date().getDate()
        ) {
          // 有缓存，并且是当天
          this.countdownPopup = false;
        } else {
          // 需要弹窗和定时器
          this.countdownPopup = true;
          // 清除定时器
          clearInterval(this.timeClock);
          this.timeClock = null;
          this.timeClock = setInterval(() => {
            this.countdown.nowTime += 1;
            if (this.countdown.endTime - this.countdown.nowTime <= 0) {
              clearInterval(this.timeClock);
              this.timeClock = null;
              this.countdownPopup = false;
            }
          }, 1000);
        }
      } else {
        this.selectedCard = {};
        this.countdownPopup = false;
      }
      this.getPayMethod();
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 301,
      });
      this.payList = res.data;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      ApiSavingsCardCreateOrder({
        type: this.selectedCard.type,
        payWay: this.selectedPayType,
      }).then(orderRes => {
        // 安卓sdk下单上报
        BOX_setPayParams({
          order_id: orderRes.data.orderId,
          productname: '充值省钱卡',
        })
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 301,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 301,
          })
            .then(async res2 => {
              await this.getSavingsCardIndex();
            })
            .catch(() => {});
        });
      });
    },
    clickCard(card) {
      if (this.pageInfo.is_member) {
        return false;
      }
      this.selectedCard = card;
    },
    clickBuyBtn() {
      this.payPopupShow = true;
    },
    async takeReward(geted = false) {
      if (this.isAndroidSdk && !geted) {
        try {
          BOX_showActivityByAction({
            action_code: 13,
            web_url: this.$h5Page.changwan_url,
          });
          return false;
        } catch (error) {}
      }

      this.$toast.loading('加载中');
      try {
        const res = await ApiSavingsCardTakeReceive();
      } finally {
        await this.getSavingsCardIndex();
      }
    },

    // 格式化时间戳为日时分秒
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let day = this.addZero(Math.floor(timeStamp / 3600 / 24));
      let hour = this.addZero(Math.floor(timeStamp / 3600) % 24);
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return {
        day,
        hour,
        minute,
        second,
      };
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    clickCloseCountdownPopup() {
      this.countdownPopup = false;
      localStorage.setItem('SAVINGS_COUNTDOWN_SHOW', new Date().getDate());
    },
  },
};
</script>

<style lang="less" scoped>
.savings-card-page {
  background: #f8f8f8;
  .coin-tips {
    color: #fff;
    font-size: 14 * @rem;
    &.black {
      color: #000000;
    }
  }
  .main {
    background: #f8f8f8;
    .top-container {
      width: 100%;
      height: 195 * @rem;
      height: calc(195 * @rem + @safeAreaTop);
      height: calc(195 * @rem + @safeAreaTopEnv);
      background: url('~@/assets/images/recharge/savings-card/savings-card-top-bg.png')
        center top no-repeat;
      background-size: 100% 195 * @rem;
      background-size: 100% calc(195 * @rem + @safeAreaTop);
      background-size: 100% calc(195 * @rem + @safeAreaTopEnv);
      padding-top: 1 * @rem;
      .user-card {
        box-sizing: border-box;
        width: 345 * @rem;
        height: 126 * @rem;
        margin: 116 * @rem auto 0;
        margin-top: 52 * @rem;
        margin-top: calc(52 * @rem + @safeAreaTop);
        margin-top: calc(52 * @rem + @safeAreaTopEnv);
        background: url('~@/assets/images/recharge/savings-card/savings-card-bg-new.png')
          no-repeat;
        background-size: 345 * @rem 126 * @rem;
        padding: 0 16 * @rem 0 26 * @rem;
        .card-info {
          padding: 15 * @rem 0 10 * @rem;
          .card-title {
            width: 58 * @rem;
            height: 19 * @rem;
            background: url('~@/assets/images/recharge/savings-card/savings-card-text.png')
              no-repeat;
            background-size: 58 * @rem 19 * @rem;
          }
          .user-info {
            display: flex;
            align-items: center;
            .avatar {
              border: 1 * @rem solid #762d10;
              width: 40 * @rem;
              height: 40 * @rem;
              background-color: #fff;
            }
            .user-right {
              margin-left: 6 * @rem;
              flex: 1;
              .line-1 {
                display: flex;
                align-items: center;
                .nickname {
                  font-size: 14 * @rem;
                  color: #762d10;
                  font-weight: 600;
                  line-height: 18 * @rem;
                  height: 18 * @rem;
                }
                .no-buy {
                  line-height: 13 * @rem;
                  border-radius: 8 * @rem 8 * @rem 8 * @rem 1 * @rem;
                  margin-left: 6 * @rem;
                  padding: 2 * @rem 4 * @rem;
                  font-size: 10 * @rem;
                  color: #ffffff;
                  background-color: #762d10;
                }
                .card-time {
                  font-size: 10 * @rem;
                  line-height: 13 * @rem;
                  color: rgba(118, 45, 16, 0.71);
                  margin-left: auto;
                }
              }
              .card-intro {
                font-size: 10 * @rem;
                color: rgba(101, 47, 26, 0.78);
                line-height: 13 * @rem;
                margin-top: 6 * @rem;
                height: 13 * @rem;
              }
            }
          }
        }
        .card-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 3 * @rem;
          .bottom-left {
            .text {
              font-size: 11 * @rem;
              color: rgba(101, 47, 26, 0.78);
              line-height: 14 * @rem;
            }
            .total-gold {
              font-size: 24 * @rem;
              color: rgba(101, 47, 26, 0.78);
              line-height: 30 * @rem;
              margin-top: 4 * @rem;
              font-weight: 600;
            }
          }
          .card-btn {
            width: 78 * @rem;
            height: 30 * @rem;
            border-radius: 15 * @rem;
            background: linear-gradient(180deg, #535051 0%, #1f1e1e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 13 * @rem;
            color: #fedba8;
            font-weight: 500;
          }
        }
      }
    }

    .countdown-container {
      box-sizing: border-box;
      padding-top: 4 * @rem;
      padding-left: 32 * @rem;
      z-index: 20;
      width: 358 * @rem;
      margin: -10 * @rem auto 8 * @rem;
      height: 53 * @rem;
      background: url(~@/assets/images/recharge/savings-card/countdown-bg.png)
        left top no-repeat;
      background-size: 358 * @rem 53 * @rem;
      display: flex;
      align-items: center;
      transition: 0.3s;
      .countdown-text {
        font-size: 12 * @rem;
        font-weight: 500;
        color: #ffffff;
        white-space: nowrap;
        margin-left: 5 * @rem;
      }
      .countdown-clock {
        display: flex;
        font-size: 12 * @rem;
        font-weight: 600;
        color: #8f0e00;
        height: 28 * @rem;
        line-height: 28 * @rem;
        margin-left: 15 * @rem;
        span {
          display: block;
          width: 24 * @rem;
          height: 28 * @rem;
          background: linear-gradient(180deg, #ffd979 0%, #ffde69 100%);
          box-shadow: 0 * @rem 0 * @rem 2 * @rem 0 * @rem rgba(0, 0, 0, 0.25);
          border-radius: 6 * @rem 6 * @rem 6 * @rem 6 * @rem;
          line-height: 28 * @rem;
          text-align: center;
          font-size: 14 * @rem;
          color: #ff1800;
          font-weight: 600;
          margin: 0 4 * @rem;
        }
      }
      .countdown-close {
        width: 40 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/recharge/savings-card/countdown-close.png)
          center center no-repeat;
        background-size: 12 * @rem 12 * @rem;
      }
    }
    .card-list {
      .card-item {
        box-sizing: border-box;
        width: 345 * @rem;
        height: 86 * @rem;
        border: 2 * @rem solid #fff;
        border-radius: 12 * @rem;
        background-color: #fff;
        margin: 10 * @rem auto 0;
        position: relative;
        .tag {
          width: 45 * @rem;
          height: 19 * @rem;
          position: absolute;
          left: -2 * @rem;
          top: -2 * @rem;
          img {
            width: auto;
            object-fit: fill;
          }
        }
        &:first-of-type {
          margin-top: 0;
        }
        &.current {
          border: 2 * @rem solid #ffc876;
          position: relative;
          &::after {
            content: '';
            width: 16 * @rem;
            height: 15 * @rem;
            background: url('~@/assets/images/recharge/savings-card/savings-card-selected.png')
              no-repeat;
            background-size: 16 * @rem 15 * @rem;
            position: absolute;
            right: -2 * @rem;
            top: -2 * @rem;
          }
        }
        .card {
          height: 100%;
          display: flex;
          align-items: center;
          padding: 0 12 * @rem;
          .icon {
            width: 77 * @rem;
            height: 52 * @rem;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
          .card-center {
            flex: 1;
            min-width: 0;
            margin-left: 8 * @rem;
            .title {
              font-size: 14 * @rem;
              color: #424242;
              line-height: 18 * @rem;
              font-weight: 600;
            }
            .desc {
              font-size: 11 * @rem;
              color: #9f9f9f;
              line-height: 14 * @rem;
              margin-top: 3 * @rem;
            }
          }
          .card-right {
            .money-now {
              font-size: 20 * @rem;
              color: #121212;
              line-height: 30 * @rem;
              font-weight: 600;
              text-align: right;
            }
            .money-old {
              font-size: 11 * @rem;
              color: #7b7b7b;
              line-height: 14 * @rem;
              text-decoration: line-through;
              margin-top: 4 * @rem;
              text-align: right;
            }
          }
        }
      }
    }
    .buy-fixed {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10 * @rem 16 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      box-shadow: 0 -5 * @rem 5 * @rem rgba(0, 0, 0, 0.035);
      border-top: 1px solid rgba(0, 0, 0, 0.035);
    }
    .buy-btn {
      width: 278 * @rem;
      height: 46 * @rem;
      background: linear-gradient(180deg, #535051 0%, #1f1e1e 100%);
      border-radius: 30 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: 600;
      margin: 0 auto;
      position: relative;
      &.had {
        background: #fff;
        border: 1 * @rem solid #121212;
        color: #121212;
      }
      .subscript {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22 * @rem;
        background: linear-gradient(
          180deg,
          #ffc683 0%,
          #ffc683 0%,
          #ffd6a7 100%
        );
        border-radius: 20 * @rem 20 * @rem 20 * @rem 5 * @rem;
        border: 1 * @rem solid #f8f8f8;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 12 * @rem;
        color: #652f1a;
        line-height: 15 * @rem;
        text-align: center;
        padding: 0 6 * @rem;
        position: absolute;
        top: -11 * @rem;
        right: -10 * @rem;
      }
    }
    .explain-container {
      box-sizing: border-box;
      width: 345 * @rem;
      border-radius: 12 * @rem;
      background-color: #ffffff;
      padding: 1 * @rem 15 * @rem 18 * @rem;
      margin: 20 * @rem auto 120 * @rem;
      .explain-title {
        width: 146 * @rem;
        height: 32 * @rem;
        background: url('~@/assets/images/recharge/savings-card/savings-card-explain.png');
        background-size: 146 * @rem 32 * @rem;
        margin: -6 * @rem auto 0;
      }
      .content {
        padding-top: 16 * @rem;
        font-size: 13 * @rem;
        color: #000000;
        line-height: 20 * @rem;
      }
    }
  }

  .pay-container-popup {
    .pay-container {
      box-sizing: border-box;
      position: relative;
      padding: 10 * @rem 14 * @rem 0;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .pay-way-close {
        position: absolute;
        right: 10 * @rem;
        top: 10 * @rem;
        width: 40 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/recharge/recharge-popup-close.png)
          center center no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }
      .pay-way-title {
        font-size: 16 * @rem;
        color: #333;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pay-list {
        .pay-item {
          display: flex;
          align-items: center;
          padding: 16 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .icon {
            width: 24 * @rem;
            height: 24 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 24 * @rem 24 * @rem;
            // &.wx {
            //   background-image: url(~@/assets/images/recharge/wx-icon.png);
            //   background-size: 24 * @rem 21 * @rem;
            // }
            // &.zfb_dmf {
            //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
            //   background-size: 24 * @rem 24 * @rem;
            // }
          }

          .pay-center {
            margin-left: 8 * @rem;
            .line {
              display: flex;
              align-items: center;
              .pay-name {
                font-size: 15 * @rem;
                color: #333;
              }
              .recommend-icon {
                width: 39 * @rem;
                height: 17 * @rem;
                background-size: 39 * @rem 17 * @rem;
                background-position: left center;
                background-repeat: no-repeat;
                margin-left: 5 * @rem;
              }
            }
            .subtitle {
              color: #999;
              font-size: 12 * @rem;
              line-height: 20 * @rem;
            }
          }
          .choose {
            margin-left: auto;
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/recharge/n_radio.png) center center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            &.on {
              background-image: url(~@/assets/images/recharge/c_radio.png);
            }
          }
        }
      }
      .pay-btn {
        width: 290 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;

        background: linear-gradient(180deg, #535051 0%, #1f1e1e 100%);
        font-size: 18 * @rem;
        font-weight: bold;
        color: #fff;
        margin: 30 * @rem auto;
        border-radius: 23 * @rem;
      }
    }
  }
  /deep/ .van-dialog {
    overflow: unset;
    width: 320 * @rem;
  }
}
</style>
