<template>
  <div class="page rebate-apply-page">
    <nav-bar-2 :border="true" title="返利申请" :azShow="true"> </nav-bar-2>
    <div class="main">
      <div class="form">
        <form @submit.prevent="">
          <div class="form-item">
            <div class="form-item-title">活动标题：</div>
            <div class="form-item-content">
              <span>专属活动</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏账号：</div>
            <div class="form-item-content">
              <span>{{ userInfo.nickname }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏名：</div>
            <div class="form-item-content">
              <span>{{ gameInfo.title }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏小号：</div>
            <div class="form-item-content">
              <div class="form-item-select" @click="clickXh">
                {{ xh_info.nickname }}
              </div>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏区服：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="xh_info.role_area_name"
                placeholder="请输入您充值的角色区服"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">角色名：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="xh_info.role_name"
                placeholder="请填写您充值的角色名"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">角色ID：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="xh_info.role_id"
                placeholder="游戏头像或者游戏设置里查看ID"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">礼包选择：</div>
            <div class="form-item-content">
              <span>{{ reward_title }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">联系方式：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="contact"
                placeholder="请填写您的微信号或手机号"
              />
            </div>
          </div>
          <div class="form-item-column">
            <div class="form-item-title">
              上传图片：<span>（需上传相关截图，最多支持上传3张）</span>
            </div>
            <div class="photos">
              <van-uploader
                v-model="imageFileList"
                :after-read="afterRead"
                @delete="deletePic"
                :max-count="3"
                accept="image/*"
                :before-read="beforeRead"
                :multiple="true"
              >
                <div class="upload-btn">
                  <img src="@/assets/images/feedback/upload-add.png" />
                </div>
              </van-uploader>
            </div>
          </div>
          <div class="form-item-column">
            <div class="form-item-title">
              申请备注：<span>（可自选奖励请备注说明）</span>
            </div>
            <textarea
              class="form-item-textarea"
              v-model="remark"
              placeholder="如有返利道具需要选择，请填写您所要的奖励(如：礼包B)"
            ></textarea>
          </div>
          <button class="submit" @click="submit">提交</button>
        </form>
      </div>
      <div class="rule-container">
        <div class="rule-title"></div>
        <div class="rule-content">
          <ul>
            <li>
              活动详细信息以公告内的信息为准。当玩家有冠名权时，可自定义区服名称，且不能包含:渠道、广告、政治敏感、不文明等内容，需经过运营团队审核；
            </li>
            <li>如果奖励里有自选奖励或者您有特殊情况请填写在备注里。</li>
            <li>
              提交审核成功后，如果后续审核进度更新会通过我的—消息以及短信通知您，请留意，如有疑请联系客服。
            </li>
            <li>
              奖励是否可获取或者重复获取，以各游戏公告为准，以上页面只用作提交。
            </li>
            <li>
              活动审核成功后会通过短信以及消息通知发送给您，也可以我的—我的返利中查看申请状态，如果运营活动奖励有礼包码也可在此复制，审核前可进行仅一次的填写信息修改，请注意是否信息和游戏信息相符。
            </li>
            <li>
              奖励发放时间以公告为准，如奖励发放和奖励有异常请联系客服处理。
            </li>
          </ul>
        </div>
      </div>
    </div>
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="gameId"
      :defaultXh="xh_info.id"
      @onSelectSuccess="onSelectSuccess"
    ></yy-xh-select>
  </div>
</template>

<script>
import {
  ApiDownActivityRebateInfo,
  ApiDownActivitySubmitRebate,
} from '@/api/views/downActivity.js';
import { ApiUploadImage } from '@/api/views/system';
import md5 from 'js-md5';
export default {
  data() {
    return {
      gameId: this.$route.params.gameId,
      actId: this.$route.params.actId,
      rewardType: this.$route.params.type,

      gameInfo: {},
      reward_title: '',
      xh_info: {},
      contact: '',
      photos: [],
      remark: '',

      imageFileList: [],

      xhDialogShow: false,
    };
  },
  async created() {
    await this.getRebateInfo();
  },
  methods: {
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.photos.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          await this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.photos.splice(detail.index, 1);
    },
    beforeRead() {
      return true;
    },
    async getRebateInfo() {
      const res = await ApiDownActivityRebateInfo({
        game_id: this.gameId,
        act_id: this.actId,
        rewardType: this.rewardType,
      });
      this.gameInfo = res.data.game ?? {};
      this.reward_title = res.data.reward_title;
      this.xh_info = res.data.xh_info ?? {};
    },
    async submit() {
      const params = {
        actId: this.actId,
        gameId: this.gameId,
        rewardType: this.rewardType,
        xhId: this.xh_info.id,
        gameArea: this.xh_info.role_area_name,
        gameRoleName: this.xh_info.role_name,
        gameRoleId: this.xh_info.role_id,
        contact: this.contact,
        photos: this.photos.join(','),
        remark: this.remark,
      };

      try {
        this.$toast.loading({
          message: this.$t('提交中'),
          forbidClick: false,
          duration: 2000,
        });
        const res = await ApiDownActivitySubmitRebate(params);
        this.back();
      } finally {
      }
    },
    onSelectSuccess(info) {
      console.log(info);
      this.xh_info.nickname = info.nickname;
      this.xh_info.id = info.xhId;
    },
    clickXh() {
      this.xhDialogShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.rebate-apply-page {
  .main {
    box-sizing: border-box;
    padding: 16 * @rem 18 * @rem 40 * @rem;
    .form {
      .form-item {
        width: 100%;
        display: flex;
        align-items: center;
        height: 28 * @rem;
        &:not(:first-of-type) {
          margin-top: 10 * @rem;
        }
        .form-item-title {
          width: 70 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 18 * @rem;
          height: 18 * @rem;
          text-align: justify;
          &::after {
            content: '';
            width: 100%;
            display: inline-block;
          }
        }
        .form-item-content {
          height: 100%;
          flex: 1;
          min-width: 0;
          font-size: 13 * @rem;
          color: #000000;
          line-height: 18 * @rem;
          margin-left: 5 * @rem;
          display: flex;
          align-items: center;
          input {
            height: 100%;
            width: 100%;
            border-bottom: 0.5px solid #ebebeb;
          }
          .form-item-select {
            padding-right: 20 * @rem;
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              display: block;
              width: 18 * @rem;
              height: 18 * @rem;
              background: url('~@/assets/images/game-down-activity/form-item-right-icon.png')
                right center no-repeat;
              background-size: 6 * @rem 10 * @rem;
            }
          }
        }
      }
      .form-item-column {
        margin-top: 10 * @rem;
        .form-item-title {
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 18 * @rem;
          display: flex;
          align-items: center;
          span {
            font-size: 11 * @rem;
            color: #333;
          }
        }
        .photos {
          margin-top: 15 * @rem;
          /deep/ .van-uploader__preview {
            margin: 0 15 * @rem 15 * @rem 0;
          }
          /deep/ .van-uploader__preview-image {
            width: 88 * @rem;
            height: 88 * @rem;
            border-radius: 7 * @rem;
          }
          /deep/ .van-uploader__preview-delete {
            width: 20 * @rem;
            height: 20 * @rem;
            top: -10 * @rem;
            right: -10 * @rem;
            transform: scale(0.8);
            border-radius: 10 * @rem;
            background-color: rgb(154, 154, 154);
          }
          /deep/ .van-uploader__preview-delete-icon {
            transform: scale(1);
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .upload-btn {
            width: 88 * @rem;
            height: 88 * @rem;
          }
        }
        .form-item-textarea {
          box-sizing: border-box;
          width: 339 * @rem;
          height: 127 * @rem;
          background-color: #f4f4f4;
          border-radius: 6 * @rem;
          border: 0;
          resize: none;
          outline: none;
          margin: 7 * @rem auto 0;
          padding: 9 * @rem 10 * @rem;
          line-height: 22 * @rem;
          font-size: 13 * @rem;
          color: #333;
        }
      }
      .submit {
        margin: 30 * @rem auto 0;
        width: 255 * @rem;
        height: 49 * @rem;
        border-radius: 25 * @rem;
        background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
        outline: none;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        color: #ffffff;
        font-weight: 500;
      }
    }
    .rule-container {
      margin-top: 40 * @rem;
      .rule-title {
        width: 352 * @rem;
        height: 22 * @rem;
        background: url('~@/assets/images/game-down-activity/rebate-rule.png')
          no-repeat;
        background-size: 352 * @rem 22 * @rem;
        margin: 0 auto;
      }
      .rule-content {
        ul {
          margin: 16 * @rem auto 0;
          li {
            line-height: 17 * @rem;
            font-size: 12 * @rem;
            color: #757575;
            position: relative;
            padding-left: 11 * @rem;
            &:not(:first-of-type) {
              margin-top: 10 * @rem;
            }
            &::before {
              content: '·';
              font-weight: 600;
              font-size: 14 * @rem;
              line-height: 14 * @rem;
              width: 10 * @rem;
              position: absolute;
              left: 0;
              top: 0;
            }
          }
        }
      }
    }
  }
}
</style>
