<template>
  <div class="buy-list-waterfall">
    <waterfall :col="2" :data="list">
      <template>
        <div
          class="buy-item btn"
          v-for="(item, index) in list"
          :key="index"
          @click="toPage('XiaohaoDetail', { id: item.id })"
        >
          <img class="pic" :src="item.images[0]" />
          <div class="info">
            <div class="game">
              <img class="game-icon" :src="item.game.titlepic" alt="" />
              <div class="game-name">{{ item.game.title }}</div>
            </div>
            <div class="title">{{ item.title }}</div>
            <div class="bottom">
              <div class="price">
                ¥<span :class="{ small: parseInt(item.price) > 999 }">{{
                  parseInt(item.price)
                }}</span>
              </div>
              <div class="gold">
                {{ $t('立返') }}{{ item.gold_num }}{{ $t('金币') }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </waterfall>
  </div>
</template>

<script>
export default {
  name: 'buyListWaterfall',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="less" scoped>
.buy-list-waterfall {
  box-sizing: border-box;
  width: 100%;
  padding: 0 12 * @rem;
  overflow: hidden;
  background: #fff;
  .buy-item {
    box-sizing: border-box;
    width: 165 * @rem;
    margin: 0 auto;
    border-radius: 12 * @rem;
    overflow: hidden;
    background-color: #fff;
    margin-bottom: 10 * @rem;
    border: 0.5 * @rem solid #ddd;
    .pic {
      width: 100%;
      height: auto;
      max-height: 237 * @rem;
      min-height: 130 * @rem;
      object-fit: cover;
      border-radius: 12 * @rem 12 * @rem 0 0;
    }
    .info {
      padding: 0 6 * @rem 12 * @rem;
      .game {
        display: flex;
        align-items: center;
        padding: 10 * @rem 0 0;
        .game-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          border-radius: 4 * @rem;
        }
        .game-name {
          font-size: 12 * @rem;
          color: #797979;
          font-weight: 400;
          margin-left: 4 * @rem;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .title {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 600;
        line-height: 20 * @rem;
        margin-top: 7 * @rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .bottom {
        display: flex;
        align-items: center;
        margin-top: 9 * @rem;
        justify-content: space-between;
        .price {
          font-size: 12 * @rem;
          color: @themeColor;
          font-weight: 600;
          span {
            font-weight: 600;
            font-size: 20 * @rem;
            &.small {
              font-size: 16 * @rem;
            }
          }
        }
        .gold {
          height: 22 * @rem;
          padding: 0 8 * @rem;
          font-size: 10 * @rem;
          color: @themeColor;
          background-color: #fff7f2;
          border-radius: 14 * @rem;
          display: flex;
          align-items: center;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
