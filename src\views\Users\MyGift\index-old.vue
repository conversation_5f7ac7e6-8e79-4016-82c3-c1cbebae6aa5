<template>
  <div class="page my-gift-page">
    <nav-bar-2 title="我的礼包" :border="true"></nav-bar-2>
    <yy-list
      class="my-gift-content"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :check="false"
      :empty="empty"
    >
      <div class="gift-list">
        <div
          class="gift-item"
          v-for="(item, index) in giftList"
          :key="index"
          @click="
            toPage('GiftDetail', {
              game_id: item.game_id,
              gift_id: item.id,
              xh_id: item.xh_id,
            })
          "
        >
          <div class="gift-icon">
            <img :src="item.titlepic" :alt="item.titlegame" />
          </div>
          <div class="center">
            <div class="gift-title">{{ item.title }}</div>
            <div class="cardpass">礼包码：{{ item.cardpass }}</div>
            <div class="desc">{{ item.cardbody }}</div>
          </div>
          <div class="copy-btn btn" @click.stop="copy(item.cardpass)">复制</div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiCardMine } from '@/api/views/card.js';
export default {
  name: 'MyGift',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      giftList: [],
      empty: false,
    };
  },
  async created() {
    await this.getMyGiftList();
  },
  methods: {
    async getMyGiftList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      let res = await ApiCardMine({
        listRows: this.listRows,
        page: this.page,
      });
      this.loadingObj.loading = false;
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.giftList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.giftList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getMyGiftList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getMyGiftList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },

    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$toast(this.$t('复制失败，请手动复制'));
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.my-gift-page {
  .my-gift-content {
    .gift-list {
      padding: 12 * @rem 18 * @rem;
      .gift-item {
        display: flex;
        align-items: center;
        padding: 10 * @rem 0;
        .gift-icon {
          width: 60 * @rem;
          height: 60 * @rem;
          border-radius: 12 * @rem;
          overflow: hidden;
        }
        .center {
          flex: 1;
          min-width: 0;
          margin-left: 8 * @rem;
          margin-right: 20 * @rem;
          .gift-title {
            font-size: 14 * @rem;
            color: #111111;
            line-height: 18 * @rem;
            font-weight: 600;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .cardpass {
            font-size: 11 * @rem;
            color: #ff7b01;
            font-weight: 600;
            line-height: 14 * @rem;
            margin-top: 7 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .desc {
            font-size: 11 * @rem;
            color: #999999;
            line-height: 15 * @rem;
            margin-top: 6 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .copy-btn {
          width: 58 * @rem;
          height: 28 * @rem;
          background: #fe6601;
          border-radius: 19 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 13 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
