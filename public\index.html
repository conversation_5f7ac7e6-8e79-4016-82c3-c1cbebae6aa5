<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta name="renderer" content="webkit">
  <meta name="force-rendering" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
  <meta name="wap-font-scale" content="no">
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
  <meta name="theme-color" content="#ffffff">
  <meta name="msapplication-tap-highlight" content="no">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <!-- 状态栏背景色！！！默认值为default（白色），可以定为black（黑色）和black-translucent（灰色半透明）。 -->
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="cache" content="no-cache" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="expires" content="0" />
  <!--QQ强制全屏-->
  <meta name="x5-fullscreen" content="true" />
  <!--UC强制全屏-->
  <meta name="fullscreen" content="yes" />
  <script src="https://static-hw.3733.com/wa/public/static/js/flexible.min.js"></script>
  <script src="https://static-hw.3733.com/wa/public/static/js/trackingIO_h5_sdk_2.1.0.js"></script>
  <script src="https://static-hw.3733.com/wa/public/static/js/cloud_game/saas-sdk.js"></script>
  <script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
  <style>
    /* 去ad */
    [id][data-text] {
      display: none !important;
    }
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <script>
    // 取消safari双击滚动页面
    window.onload = function () {
      document.addEventListener('dblclick', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      })
    }
    /* 去ad */
    setTimeout(() => {
      const oUC = document.querySelector("[id][data-text]");
      const oBody = document.querySelector("body");
      oUC && oBody.removeChild(oUC);
    }, 1000);
  </script>
</body>

</html>