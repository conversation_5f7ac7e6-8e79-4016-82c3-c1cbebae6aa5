<template>
  <div class="today-kaifu-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
    >
      <div class="game-container">
        <div v-for="(game, gameIndex) in gameList" :key="gameIndex">
          <div class="time">
            <div class="time-icon"></div>
            <div class="time-text">
              {{ handleTimestamp(game[0].newstime).time }}
            </div>
          </div>
          <div class="time-list">
            <div
              class="game-section"
              v-for="(item, index) in game"
              :key="index"
              @click="toPage('GameDetail', { id: item.game_id })"
            >
              <div class="game-icon">
                <img :src="item.game.titlepic" alt="" />
              </div>
              <div class="game-center">
                <div class="game-name">
                  {{ item.game.main_title
                  }}<span
                    class="game-subtitle"
                    v-if="item.game && item.game.subtitle"
                    >{{ item.game.subtitle }}</span
                  >
                </div>
                <div class="tags">
                  <div
                    class="tag"
                    v-for="(tag, tagIndex) in item.game.extra_tag"
                    :key="tagIndex"
                  >
                    <template v-if="tagIndex < 3">
                      <div class="tag-name">{{ tag.name }}</div>
                    </template>
                  </div>
                </div>
              </div>
              <div class="game-area">{{ item.state }}</div>
            </div>
          </div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiServerIndex } from '@/api/views/game.js';
export default {
  name: 'TodayKaifu',
  data() {
    return {
      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  activated() {
    this.getGameList();
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiServerIndex({
        page: this.page,
        listRows: this.listRows,
        type: 1,
      });
      let { data: list } = res;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      let formatArr = this.classify(list, 'newstime');
      if (formatArr.length) {
        // 这个一定要判断，可能是一个空数组[]
        this.gameList.push(...formatArr);
      }

      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },
    // 按key值分类
    classify(arr, key) {
      let obj = {};
      arr.forEach(e => {
        if (typeof obj[e[key]] == 'undefined') {
          obj[e[key]] = [];
        }
        if (
          this.gameList.length &&
          this.gameList[this.gameList.length - 1][0][key] == e[key]
        ) {
          // 如果该项的时间和gameList最后一项的时间一样，则push到gameList的最后一项
          this.gameList[this.gameList.length - 1].push(e);
          delete obj[e[key]];
        } else {
          obj[e[key]].push(e);
        }
      });
      return Object.values(obj);
    },
    // 处理开服表的字符串
    handleTimestamp(ts) {
      let temp = new Date(ts * 1000),
        m = temp.getMonth() + 1,
        d = temp.getDate(),
        h = temp.getHours(),
        min = temp.getMinutes();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      h = h < 10 ? '0' + h : h;
      min = min < 10 ? '0' + min : min;
      return {
        date: `${m}-${d}`,
        time: `${h}:${min}`,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.today-kaifu-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }

  .game-container {
    padding: 0 18 * @rem;
    .time {
      display: flex;
      align-items: center;
      height: 34 * @rem;
      margin-top: 22 * @rem;
      .time-icon {
        width: 18 * @rem;
        height: 18 * @rem;
        background: url(~@/assets/images/games/kaifu-time-icon.png) no-repeat;
        background-size: 18 * @rem 18 * @rem;
      }
      .time-text {
        margin-left: 5 * @rem;
        font-size: 18 * @rem;
        color: #000000;
        font-weight: bold;
        line-height: 25 * @rem;
      }
    }
    .time-list {
      .game-section {
        display: flex;
        align-items: center;
        padding: 18 * @rem 0;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
        }
        .game-center {
          flex: 1;
          min-width: 0;
          margin: 0 8 * @rem;
          .game-name {
            font-size: 16 * @rem;
            color: #000000;
            font-weight: 500;
            line-height: 22 * @rem;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1 * @rem solid fade(@themeColor, 80);
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              color: @themeColor;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .tags {
            display: flex;
            height: 17 * @rem;
            overflow: hidden;
            flex-wrap: wrap;
            margin-top: 6 * @rem;
            .tag {
              height: 17 * @rem;
              margin-right: 5 * @rem;
              display: flex;
              align-items: center;
              flex-wrap: nowrap;
              color: #9a9a9a;
              background-color: #f5f5f6;
              border-radius: 5 * @rem;
              padding: 0 4 * @rem;
              .tag-icon {
                width: 13 * @rem;
                height: 13 * @rem;
              }
              .tag-name {
                font-size: 11 * @rem;
                white-space: nowrap;
                margin-left: 2 * @rem;
              }
            }
          }
        }
        .game-area {
          width: 100 * @rem;
          height: 28 * @rem;
          line-height: 28 * @rem;
          border-radius: 4 * @rem;
          border: 1 * @rem solid #fe4a55;
          font-size: 12 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #fe4a55;
          text-align: center;
          padding: 0 3 * @rem;
        }
      }
    }
  }
}
</style>
