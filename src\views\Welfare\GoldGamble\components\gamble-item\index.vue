<template>
  <div
    class="gamble-item"
    @click="
      toPage('GambleDetail', {
        id: gambleInfo.dbi_id,
      })
    "
  >
    <div class="info-container">
      <div class="icon">
        <img :src="gambleInfo.cover" alt="" />
      </div>
      <div class="center">
        <div class="title">{{ gambleInfo.title }}</div>
        <div class="period">{{ $t('参与期号') }}：{{ gambleInfo.period }}</div>
        <div class="open-time">
          {{ $t('开奖时间') }}：{{
            gambleInfo.state == 3
              ? formatDate(gambleInfo.open_time)
              : $t('未开奖')
          }}
        </div>
      </div>
      <div class="status">
        <div class="status-text" v-if="gambleInfo.state != 3">
          {{ $t('进行中') }}
        </div>
        <div class="status-icon win" v-else-if="gambleInfo.result == 1"></div>
        <div class="status-icon lose" v-else></div>
      </div>
    </div>
    <div class="bottom-container">
      <div class="num">{{ $t('数量') }}：{{ gambleInfo.num }}</div>
      <div class="more">
        <div class="more-text">{{ $t('查看详情') }}</div>
        <div class="more-icon"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      gambleInfo: {},
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  watch: {
    info(val) {
      this.gambleInfo = val;
    },
  },
  created() {
    this.gambleInfo = this.info;
  },
  methods: {
    formatDate(timestamp) {
      let { year, date, time, second } = this.$handleTimestamp(timestamp);
      return `${year}-${date} ${time}:${second}`;
    },
  },
};
</script>

<style lang="less" scoped>
.gamble-item {
  background-color: #fff;
  margin: 12 * @rem auto 0;
  width: 351 * @rem;
  height: 139 * @rem;
  border-radius: 12 * @rem;
  padding: 0 12 * @rem;
  box-sizing: border-box;
  .info-container {
    display: flex;
    align-items: center;
    padding: 17 * @rem 0 15 * @rem;
    border-bottom: 0.5 * @rem solid #f0f1f5;
    .icon {
      width: 52 * @rem;
      height: 52 * @rem;
    }
    .center {
      flex: 1;
      min-width: 0;
      margin-left: 12 * @rem;
      .title {
        width: 180 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 15 * @rem;
        color: #30343b;
        font-weight: bold;
        line-height: 19 * @rem;
      }
      .period,
      .open-time {
        width: 180 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12 * @rem;
        color: #93999f;
        line-height: 15 * @rem;
        margin-top: 6 * @rem;
      }
    }
    .status {
      .status-text {
        font-size: 14 * @rem;
        color: #fd6a53;
        font-weight: bold;
      }
      .status-icon {
        width: 54 * @rem;
        height: 50 * @rem;
        background-size: 54 * @rem 50 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        &.win {
          background-image: url(~@/assets/images/welfare/gold-gamble/gamble-win.png);
        }
        &.lose {
          background-image: url(~@/assets/images/welfare/gold-gamble/gamble-lose.png);
        }
      }
    }
  }
  .bottom-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 41 * @rem;
    .num {
      font-size: 13 * @rem;
      color: #60666c;
    }
    .more {
      display: flex;
      align-items: center;
      .more-text {
        font-weight: 400;
        font-size: 13 * @rem;
        color: #30343b;
      }
      .more-icon {
        width: 8 * @rem;
        height: 12 * @rem;
        .image-bg('~@/assets/images/welfare/gold-gamble/triangle-right.png');
        margin-left: 3 * @rem;
      }
    }
  }
}
</style>
