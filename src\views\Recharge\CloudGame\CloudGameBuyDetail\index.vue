<template>
  <div class="page cloud-game-detail-page">
    <nav-bar-2
      :border="true"
      :title="$t('订单详情')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="status section" v-if="orderInfo.title">
        <div class="top">        
          {{ orderInfo.status_str }}
        </div>
        <div class="countdown" v-if="this.orderInfo.cancel_time"
          >{{ $t('还剩') }}<span>{{ format(this.orderInfo.cancel_time) }}</span
          >{{ $t('订单自动取消') }}</div
        >
      </div>
      <div class="detail-info section" v-if="orderInfo.title">
        <div class="detail-title">{{ $t('商品详情') }}</div>
        <div class="item-info">
          <div class="order-icon">
            <img
              src="@/assets/images/recharge/cloud-game/vip-icon.png"
              alt=""
              v-if="orderInfo.type == 1"
            />
            <img
              src="@/assets/images/recharge/cloud-game/duration-icon.png"
              alt=""
              v-else
            />
          </div>
          <div class="info" v-if="orderInfo.type_str">
            <div class="title">
              <span>{{ orderInfo.type_str.title }}</span>
              <div class="price">{{ unit }}{{ orderInfo.amount }}</div>
            </div>
            <div class="desc">
              <span>{{ orderInfo.title }}</span>
              <div class="count">*1</div>
            </div>
          </div>
        </div>
        <div class="total"
          >合计：<span>{{ unit }}{{ orderInfo.amount }}</span></div
        >
      </div>
      <div class="order-info section" v-if="orderInfo.title">
        <div class="order-info-list">
          <div class="item" v-if="orderInfo.order_id">
            <div class="title">{{ $t('订单编号') }}：</div>
            <div class="value">{{ orderInfo.order_id }}</div>
          </div>
          <div class="item" v-if="orderInfo.pay_name">
            <div class="title">{{ $t('支付方式') }}：</div>
            <div class="value">{{ orderInfo.pay_name }}</div>
          </div>
          <div class="item" v-if="orderInfo.trade_no">
            <div class="title">{{ $t('支付订单号') }}：</div>
            <div class="value">{{ orderInfo.trade_no }}</div>
          </div>
          <div class="item" v-if="orderInfo.create_date">
            <div class="title">{{ $t('创建日期') }}：</div>
            <div class="value">{{ orderInfo.create_date }}</div>
          </div>
          <div class="item" v-if="orderInfo.pay_date">
            <div class="title">{{ $t('支付日期') }}：</div>
            <div class="value">{{ orderInfo.pay_date }}</div>
          </div>
        </div>
      </div>
      <div class="fixed-bottom" v-if="orderInfo.status == 1">
        <div class="pay-btn btn" @click="payPopupShow = true"
          >{{ $t('支付') }}{{ unit }}{{ orderInfo.amount }}</div
        >
      </div>
    </div>
    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(this.orderInfo.amount)"
      :unit="unit"
    ></pay-type-popup>
  </div>
</template>
<script>
import {
  ApiCloudPayOrderInfo,
  ApiGetPaymentMethod,
  ApiGetPayUrl,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
export default {
  name: 'CloudGameBuyDetail',
  data() {
    return {
      orderInfo: {},
      orderId: '',
      timer: null,
      orderType: 502,
      selectedPayType: 'wx',
      payList: [],
      payPopupShow: false,
    };
  },
  async created() {
    this.orderId = this.$route.params.id;
    await this.getOrderInfo();
    await this.getPayMethod();
  },
  computed: {
    unit() {
      return this.userInfo.is_hw ? '$' : '￥';
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    async getOrderInfo() {
      let res = await ApiCloudPayOrderInfo({
        order_id: this.orderId,
      });
      clearInterval(this.timer);
      this.timer = null;
      this.orderInfo = res.data.info;
      this.orderType = this.orderInfo.type == 1 ? 501 : 502;
      console.log(this.orderInfo);
      if (this.orderInfo.cancel_time) {
        this.timer = setInterval(() => {
          this.orderInfo.cancel_time--;
          if (this.orderInfo.cancel_time == 0) {
            clearInterval(this.timer);
            this.timer = null;
            this.getOrderInfo();
          }
        }, 1000);
      }
    },
    format(time) {
      let hours = Math.floor(time / 3600);
      let minutes = Math.floor((time % 3600) / 60);
      let seconds = Math.floor(time % 60);
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: this.orderType || 501,
      });
      this.payList = res.data;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    async handlePay() {
      this.payPopupShow = false;
      await ApiGetPayUrl({
        orderId: this.orderInfo.order_id,
        orderType: this.orderType,
        payWay: this.selectedPayType,
        packageName: '',
      }).finally(() => {
        ApiGetOrderStatus({
          order_id: this.orderInfo.order_id,
          order_type: this.orderType,
        });
        setTimeout(() => {
          this.getOrderInfo();
        }, 2000);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.cloud-game-detail-page {
  background-color: #f6f7f9;
  .section {
    width: 100%;
    padding: 0 18 * @rem;
    background-color: #fff;
    margin-bottom: 10 * @rem;
    box-sizing: border-box;
  }
  .status {
    height: 128 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .top {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bolder;
      font-size: 20 * @rem;
      color: #222222;
      line-height: 21 * @rem;

      img {
        width: 24 * @rem;
        height: 24 * @rem;
        margin-right: 6 * @rem;
      }
    }
    .countdown {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10 * @rem;
      font-size: 12 * @rem;
      color: #666;
      line-height: 14 * @rem;

      span {
        color: #f44040;
      }
    }
  }
  .detail-info {
    height: 173 * @rem;

    .detail-title {
      display: block;
      height: 20 * @rem;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #222222;
      line-height: 20 * @rem;
      padding-top: 16 * @rem;
    }

    .item-info {
      display: flex;
      margin-top: 12 * @rem;

      .order-icon {
        width: 72 * @rem;
        height: 72 * @rem;
        margin-right: 10 * @rem;
      }

      .info {
        flex: 1;
        min-width: 0;
        padding-top: 8 * @rem;

        .title {
          display: flex;
          justify-content: space-between;

          span {
            flex: 1;
            min-width: 0;
            max-height: 36 * @rem;
            line-height: 18 * @rem;
            color: #222222;
            font-size: 14 * @rem;
            font-weight: bold;
            overflow: hidden;
          }

          .price {
            flex-shrink: 0;
            font-size: 14 * @rem;
            line-height: 28 * @rem;
            color: #222;
            font-weight: bold;
            margin-left: 10 * @rem;
          }
        }
        .desc {
          display: flex;
          justify-content: space-between;
          margin-top: 12 * @rem;
          font-size: 12 * @rem;
          color: #868686;
          line-height: 15 * @rem;
        }
      }
    }

    .total {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 14 * @rem;
      font-size: 13 * @rem;
      line-height: 16 * @rem;
      color: #222;

      span {
        font-size: 18 * @rem;
        line-height: 22 * @rem;
        color: #ff6649;
        font-weight: bold;
      }
    }
  }
  .order-info {
    .order-info-list {
      padding: 20 * @rem 0;

      .item {
        display: flex;
        justify-content: space-between;
        font-size: 14 * @rem;
        color: #222222;
        line-height: 16 * @rem;
        margin-top: 20 * @rem;
        word-break: break-all;

        &:first-of-type {
          margin-top: 0;
        }

        .title {
          flex-shrink: 0;
          color: #909090;
          margin-right: 15 * @rem;
        }
      }
    }
  }

  .fixed-bottom {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 99;
    width: 100%;
    padding: 20 * @rem 10 * @rem;
    box-sizing: border-box;

    .pay-btn {
      width: 100%;
      height: 47 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      border-radius: 9 * @rem;
      font-size: 16 * @rem;
      font-weight: bold;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
