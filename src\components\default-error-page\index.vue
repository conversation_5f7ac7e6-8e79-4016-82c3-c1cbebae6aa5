<template>
  <div class="error-page">
    <div class="page-box">
      <div class="err-image">
        <img :src="errorImage" alt="" />
      </div>
      <div class="err-title">{{ errorTitle }}</div>
      <div class="err-btn-box">
        <div class="err-btn" @click="clickOnCallback()"> {{ errorBtn }} </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defaultBase64 } from '@/utils/defaultBase64.js';
export default {
  name: 'defaultErrorPage',
  props: {
    errorImage: {
      type: String,
      default: defaultBase64.networkImage,
    },
    errorTitle: {
      type: String,
      default() {
        return this.$t('网络异常');
      },
    },
    errorBtn: {
      type: String,
      default() {
        return this.$t('重试');
      },
    },
  },
  methods: {
    clickOnCallback() {
      this.$emit('callback');
    },
  },
};
</script>

<style lang="less" scoped>
.error-page {
  height: 100%;
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  .page-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .err-image {
      width: 128 * @rem;
      height: 128 * @rem;
    }
    .err-title {
      margin-top: 8 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #9b9b9b;
    }
    .err-btn-box {
      margin-top: 16 * @rem;
      .err-btn {
        width: 108 * @rem;
        height: 34 * @rem;
        line-height: 34 * @rem;
        background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
        border-radius: 29 * @rem;
        text-align: center;
        font-weight: 500;
        font-size: 14 * @rem;
        color: #ffffff;
      }
    }
  }
}
</style>
