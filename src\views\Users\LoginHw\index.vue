<template>
  <div class="page login-page" @click="showRemember = false">
    <nav-bar-2 :placeholder="false" bgStyle="transparent">
      <template #right>
        <div class="register" @click="goToRegisterHw"> 账号注册 </div>
      </template>
    </nav-bar-2>
    <div class="top-banner">
      <div class="page-title">用户登录</div>
    </div>
    <form class="form">
      <div class="field">
        <div class="field-icon user-icon"></div>
        <input
          type="text"
          v-model="username"
          placeholder="请输入用户名/手机号/邮箱"
        />
        <div v-if="username != ''" class="clear" @click="username = ''"></div>
        <div
          class="down-arrow"
          :class="{ show: showRemember }"
          @click.stop="showRemember = !showRemember"
          v-if="rememberList.length"
        ></div>
        <div class="remember-list" v-if="showRemember">
          <div
            class="remember-item btn"
            v-for="item in rememberList"
            :key="item.username"
            @click.stop="selectRememberItem(item)"
          >
            <div class="remember-username">{{ item.username }}</div>
            <div class="remember-avatar">
              <img :src="item.avatar" alt="" />
            </div>
            <div
              class="clear-remember-item btn"
              @click.stop="clearRememberItem(item)"
            ></div>
          </div>
        </div>
      </div>
      <div class="field">
        <div class="field-icon lock-icon"></div>
        <input
          :type="eyeOpen ? 'text' : 'password'"
          v-model="password"
          placeholder="请输入密码"
        />
        <div class="right">
          <div
            v-if="password.length > 0"
            class="eyes"
            :class="{ open: eyeOpen }"
            @click="clickEyeBtn()"
          ></div>
        </div>
      </div>
    </form>
    <div class="account-operation">
      <div
        class="remember-password"
        :class="{ remember: remember }"
        @click="remember = !remember"
      >
        记住密码
      </div>
      <div class="forget-password" @click="toPage('ChangePasswordHw')">
        找回密码
      </div>
    </div>
    <div class="button" :class="{ on: canSubmit }" @click="commit()">
      {{ $t('登录') }}
    </div>

    <div
      class="phone-login"
      @click="toPage('PhoneLogin')"
      v-if="initData.sms_login"
    >
      验证码登录
    </div>

    <div class="explain">
      <input id="checkbox" type="checkbox" v-model="ifAgreement" />
      <label for="checkbox">{{ $t('登录即代表您已同意') }}</label>
      <div
        class="link"
        @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
      >
        《{{ $t('用户协议') }}》
      </div>
      {{ $t('与') }}
      <div
        class="link"
        @click="handleLink($h5Page.yinsizhengce, $t('隐私政策'))"
      >
        《{{ $t('隐私政策') }}》
      </div>
    </div>
  </div>
</template>

<script>
import { ApiLogin } from '@/api/views/users';
import { loginSuccess } from '@/utils/function';

import { mapGetters } from 'vuex';

import { isWebApp } from '@/utils/userAgent.js';
import useConfirmAgreement from '@/components/yy-confirm-agreement/index.js';

export default {
  name: 'Login',
  data() {
    return {
      isWebApp,
      username: '',
      password: '',
      ifAgreement: false, //是否勾选协议
      eyeOpen: false, //是否显示密码
      remember: true,
      rememberList: [],
      showRemember: false,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    canSubmit() {
      return this.username && this.password && this.ifAgreement;
    },
  },
  created() {
    if (localStorage.getItem('rememberListHw')) {
      this.rememberList = JSON.parse(localStorage.getItem('rememberListHw'));
      console.log(this.rememberList);
      if (this.rememberList.length) {
        this.username = this.rememberList[0].username;
        this.password = this.rememberList[0].password;
      }
    }
  },
  methods: {
    goToPhoneLogin() {
      if ([2].includes(Number(this.initData.login_type.type))) {
        this.$dialog
          .alert({
            message: this.initData.login_type.msg || '暂无消息',
            allowHtml: true,
            confirmButtonText: '我知道了',
            confirmButtonColor: '@themeColor',
          })
          .then(() => {
            this.$copyText(this.initData.login_type.copy_text).then(res => {
              this.$toast('复制链接成功');
            });
          });
        return;
      }
      this.toPage('PhoneLogin');
    },
    selectRememberItem(info) {
      this.username = info.username;
      this.password = info.password;
      this.showRemember = false;
    },
    clearRememberItem(info) {
      this.rememberList = this.rememberList.filter(item => {
        return info.username != item.username;
      });
      localStorage.setItem('rememberListHw', JSON.stringify(this.rememberList));
      if (this.rememberList.length) {
        this.username = this.rememberList[0].username;
        this.password = this.rememberList[0].password;
      }
    },
    clickEyeBtn() {
      this.eyeOpen = !this.eyeOpen;
    },
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
    goToRegisterHw() {
      this.toPage('RegisterHw');
    },
    toPage(page) {
      [
        'Login',
        'PhoneLogin',
        'LoginHw',
        'RegisterHw',
        'ChangePasswordHw',
      ].includes(page)
        ? this.$router.replace({ name: page })
        : this.$router.push({ name: page });
    },
    async commit() {
      if (this.username === '') {
        this.$toast('请输入用户名/手机号/邮箱');
        return false;
      }
      if (this.password === '') {
        this.$toast('请输入密码!');
        return false;
      }

      const hadAgree = await useConfirmAgreement(this.ifAgreement);
      if (!hadAgree) return;

      this.ifAgreement = true;

      const toast1 = this.$toast.loading({
        message: this.$t('登录中...'),
        forbidClick: true,
        duration: 0,
      });
      let params = {
        username: this.username,
        password: this.password,
      };
      if (this.isWebApp) {
        params.is_ios_standalone = 1;
      }
      ApiLogin(params).then(res => {

        this.rememberList = this.rememberList.filter(item => {
          return item.username != this.username;
        });
        // 保存密码
        if (this.remember) {
          this.rememberList.unshift({
            username: this.username,
            password: this.password,
            avatar: res.data.avatar,
          });
        } else {
          // 不保存密码
          this.rememberList.unshift({
            username: this.username,
            password: '',
            avatar: res.data.avatar,
          });
        }
        if (this.rememberList.length > 3) {
          this.rememberList = this.rememberList.slice(0, 3);
        }
        localStorage.setItem(
          'rememberListHw',
          JSON.stringify(this.rememberList),
        );
        loginSuccess(res);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.login-page {
  background-color: #f6f7f8;
  .register {
    font-size: 14 * @rem;
    color: #111111;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 160 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: 600;
      line-height: 40 * @rem;
      margin-top: 120 * @rem;
      text-align: center;
    }
  }
  .form {
    padding: 0 16 * @rem;
    margin-top: 66 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 50 * @rem;
      position: relative;
      background: #ffffff;
      padding: 0 16 * @rem;
      border-radius: 25 * @rem;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      .field-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background-size: 24 * @rem 24 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        &.user-icon {
          background-image: url('~@/assets/images/users/login-user-icon.png');
        }
        &.lock-icon {
          background-image: url('~@/assets/images/users/login-lock-icon.png');
        }
      }
      input {
        flex: 1;
        height: 100%;
        line-height: 50 * @rem;
        font-size: 14 * @rem;
        letter-spacing: 1 * @rem;
        padding: 0 16 * @rem;
      }
      .right {
        height: 100%;
        display: flex;
        align-items: center;
        .text {
          font-size: 14 * @rem;
          text-align: right;
          color: @themeColor;
        }
        .eyes {
          width: 18 * @rem;
          height: 50 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.open {
            height: 12 * @rem;
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
      .clear {
        width: 16 * @rem;
        height: 50 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .down-arrow {
        width: 15 * @rem;
        height: 50 * @rem;
        background: url(~@/assets/images/users/arrow-down.png) center center
          no-repeat;
        background-size: 15 * @rem 6 * @rem;
        margin-left: 10 * @rem;
        &.show {
          transform: rotateX(180deg);
        }
      }
      .remember-list {
        box-sizing: border-box;
        width: 100%;
        position: absolute;
        z-index: 2;
        top: 50 * @rem;
        left: 0;
        background-color: #ebebeb;
        border-radius: 0 0 5 * @rem 5 * @rem;
        .remember-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 5 * @rem;
          &:not(:first-of-type) {
            border-top: 0.5 * @rem solid #ccc;
          }
          .remember-username {
            font-size: 14 * @rem;
            color: #999;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .remember-avatar {
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 50%;
            overflow: hidden;
          }
          .clear-remember-item {
            width: 20 * @rem;
            height: 20 * @rem;
            background: url(~@/assets/images/users/keyword-clear.png) center
              center no-repeat;
            background-size: 12 * @rem 12 * @rem;
            margin-left: 5 * @rem;
          }
        }
      }
    }
  }
  .account-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16 * @rem;
    margin-top: 12 * @rem;
    .remember-password {
      display: flex;
      align-items: center;
      font-size: 13 * @rem;
      line-height: 20 * @rem;
      color: #323233;
      padding-left: 18 * @rem;
      background-position: left center;
      background-size: 13 * @rem 13 * @rem;
      background-repeat: no-repeat;
      background-image: url(~@/assets/images/users/remember-no.png);
      &.remember {
        background-image: url(~@/assets/images/users/remember-yes.png);
      }
    }
    .forget-password {
      font-size: 13 * @rem;
      color: @themeColor;
      line-height: 20 * @rem;
    }
  }
  .button {
    height: 44 * @rem;
    margin: 52 * @rem 16 * @rem 0;
    text-align: center;
    line-height: 44 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
    opacity: 0.5;
    transition: opacity 0.2s;
    &.on {
      opacity: 1;
    }
  }
  .phone-login {
    font-size: 12 * @rem;
    color: #333333;
    margin: 20 * @rem auto 10 * @rem;
    width: fit-content;
    padding: 0 20 * @rem;
  }
  .explain {
    box-sizing: border-box;
    padding: 0 16 * @rem;
    display: flex;
    align-items: center;
    color: #323233;
    font-size: 12 * @rem;
    margin-top: 10 * @rem;
    height: 20 * @rem;
    line-height: 20 * @rem;
    input[type='checkbox'] {
      width: 14 * @rem;
      height: 14 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center center;
      background-size: 14 * @rem auto;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
}
</style>
