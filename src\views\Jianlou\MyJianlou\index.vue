<template>
  <div class="page my-jianlou-page">
    <nav-bar-2 :title="$t('我的捡漏订单')">
      <template #right>
        <div class="kefu-btn" @click="toPage('Kefu')"></div>
      </template>
    </nav-bar-2>
    <van-tabs
      v-model="active"
      swipeable
      animated
      title-active-color="#000000"
      title-inactive-color="#797979"
      :border="true"
      :color="themeColorLess"
      class="my-container"
    >
      <van-tab
        v-for="(item, index) in tabList"
        :key="index"
        :title="item.title"
        class="my-container-tab"
      >
        <jianlou-order-list :status="item.status"></jianlou-order-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import JianlouOrderList from '../components/jianlou-order-list';
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  components: {
    JianlouOrderList,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          title: this.$t('全部'),
          status: -1,
        },
        {
          title: this.$t('待支付'),
          status: 0,
        },
        {
          title: this.$t('已购买'),
          status: 1,
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.my-jianlou-page {
  height: 100vh;
  background-color: #f5f5f6;
  /deep/ .nav-bar-component .white .van-nav-bar {
    background: #f5f5f6;
  }

  .kefu-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/mine/icon_kefu_black.png) right center
      no-repeat;
    background-size: 20 * @rem 20 * @rem;
  }
  /deep/ .van-tabs--line {
    .van-tabs__nav--line {
      padding-bottom: 0;
    }
    .van-tabs__wrap {
      height: 50 * @rem;
    }
  }
  /deep/ .van-tab {
    font-size: 15 * @rem;
    color: #797979;
  }
  /deep/ .van-tab--active .van-tab__text {
    font-weight: 600;
  }
  /deep/ .van-tabs__line {
    height: 4 * @rem;
    width: 12 * @rem;
    bottom: 8 * @rem;
  }
  .my-container {
    border-radius: 20 * @rem 20 * @rem 0 0;
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    .fixed-center;
    .my-container-tab {
      overflow-y: hidden;
    }
  }
}
</style>
