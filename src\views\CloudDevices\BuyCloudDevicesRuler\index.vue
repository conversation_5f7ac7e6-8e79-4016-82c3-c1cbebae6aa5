<template>
  <div class="page">
    <nav-bar-2 :title="'购买须知'" :azShow="true"></nav-bar-2>
    <div class="content" v-html="text"></div>
  </div>
</template>

<script>
import { ApiCloudBuyNotice } from '@/api/views/upCloud.js';
export default {
  data() {
    return {
      text: '',
    };
  },
  created() {
    this.getCloudMountedBuyNotice();
  },
  methods: {
    async getCloudMountedBuyNotice() {
      const res = await ApiCloudBuyNotice();
      this.text = res.data.txt.replace(/\r\n/g, '<br/><br/>');
      this.text = this.text.replace(/\n/g, '<br/><br/>');
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  width: 340 * @rem;
  margin: 20 * @rem auto;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 21px;
  text-align: justify;

  p {
    width: 100%;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 21px;
    text-align: justify;
    margin-bottom: 30 * @rem;
  }
}
</style>
