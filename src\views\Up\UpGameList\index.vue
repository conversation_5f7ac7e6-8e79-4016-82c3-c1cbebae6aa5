<template>
  <div class="page">
    <nav-bar-2 :title="title" :placeholder="true" :border="true"> </nav-bar-2>
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
    >
      <div class="game-list" v-if="game_list.length">
        <game-item-3
          :gameInfo="item"
          v-for="(item, index) in game_list"
          :key="index"
        ></game-item-3>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiUpGameSpMore } from '@/api/views/game';
export default {
  data() {
    return {
      title: '',
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 20,
      game_list: [],
      empty: false,
    };
  },
  async created() {
    this.loadingObj.loading = true;
    await this.getGameList();
    this.loadingObj.loading = false;
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiUpGameSpMore({
        page: this.page,
        listRows: this.listRows,
        id: this.$route.params.id,
      });
      if (action === 1 || this.page === 1) {
        this.game_list = [];
        this.title = res.data.title;
        if (!res.data.game_list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.game_list.push(...res.data.game_list);
      if (res.data.game_list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGameList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>
<style lang="less" scoped>
.game-list {
  margin: 0 18 * @rem;

  .game-item {
    margin-top: 20 * @rem;
    display: flex;
    align-items: center;

    .game-icon {
      width: 64px;
      height: 64px;
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;
    }
    .game-info {
      flex: 1;
      min-width: 0;
      margin: 0 16px 0 8px;

      .game-name {
        height: 21px;
        font-size: 15px;
        font-weight: bold;
        color: #333333;
        line-height: 21px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .game-type {
        margin: 5px 0;
        display: flex;
        align-items: center;
      }

      .game-desc {
        height: 15px;
        font-size: 11px;
        color: #666666;
        line-height: 15px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .play-btn {
      width: 58px;
      height: 28px;
      line-height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: @themeColor;
      border-radius: 25px;
      font-size: 13px;
      color: #fff;
      flex-shrink: 0;
    }
  }
}
</style>
