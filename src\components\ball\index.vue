<script>
import { mapGetters, mapMutations } from 'vuex';

export default {
  data() {
    return {
      flags: false, //是否允许移动
      position: { x: 0, y: 0 }, //小球目前坐标
      nx: '', //本次手指横线移动的距离
      ny: '', //本次手指纵向移动的距离
      dx: '', //和(0,0)当前的横向坐标
      dy: '', //和(0,0)当前的纵向坐标
      xPum: '', //小球移动后目前横坐标
      yPum: '', //小球移动后目前纵坐标
      screenWidth: 0, //当前屏幕的宽度
      screenHeight: 0, //当前屏幕的高度
      isMoving: false, //是否移动中
    };
  },
  computed: {
    ...mapGetters({
      h5gamePopup: 'game/h5gamePopup',
    }),
  },
  mounted() {
    this.initBallPosition();
  },
  methods: {
    initBallPosition() {
      // 记录当前的坐标，如果3秒后没有变动过，改成半隐藏;
      const moveDiv = this.$refs.deviceInfo;
      const LEFT = moveDiv.offsetLeft;
      const TOP = moveDiv.offsetTop;
      moveDiv.style.right = '0';
      moveDiv.style.top = '50%';
      this.position = { x: 0, y: 0 };
      this.xPum = moveDiv.offsetLeft;
      this.yPum = moveDiv.offsetTop;

      if (this.isTouchSupport()) {
        // 按下
        moveDiv.addEventListener('touchstart', e => {
          this.touchstart(e);
        });
        // 拖动
        moveDiv.addEventListener('touchmove', e => {
          this.touchmove(e);
        });
        // 释放
        moveDiv.addEventListener('touchend', e => {
          this.touchend(e);
        });
      } else {
        // 鼠标按下
        moveDiv.addEventListener(
          'mousedown',
          event => {
            this.touchstart(event);
          },
          false,
        );
        // 鼠标拖动
        document.body.addEventListener(
          'mousemove',
          event => {
            this.touchmove(event);
          },
          false,
        );

        // 鼠标释放
        document.body.addEventListener(
          'mouseup',
          event => {
            this.touchend(event);
            document.body.removeEventListener(
              'mousemove',
              this.touchmove,
              false,
            );
            document.body.removeEventListener('mouseup', this.touchend, false);
          },
          false,
        );
      }
    },
    touchstart() {
      const moveDiv = this.$refs.deviceInfo;
      this.flags = true;
      let touch;
      if (event.touches) {
        touch = event.touches[0]; //获取当前事件
      } else {
        touch = event;
      }
      this.position.x = touch.clientX; //获取手指位置坐标
      this.position.y = touch.clientY;
      this.dx = moveDiv.offsetLeft; //获取小球相对于离它最近的（指包含层级上的最近）包含小球的定位元素或者最近的 table,td,th,body元素左部内边距的距离
      this.dy = moveDiv.offsetTop;
    },
    touchmove() {
      const moveDiv = this.$refs.deviceInfo;
      if (this.flags) {
        let touch;
        if (event.touches) {
          touch = event.touches[0];
        } else {
          touch = event;
        }
        this.nx = touch.clientX - this.position.x; //手指横向移动距离
        this.ny = touch.clientY - this.position.y;
        // 判断小球是否移动过，移动了就不触发点击事件打开弹窗
        if (this.nx > 5 || this.ny > 5) {
          this.isMoving = true;
        }
        this.xPum = this.dx + this.nx; //计算小球移动后的位置
        this.yPum = this.dy + this.ny;
        if (this.yPum < 0) {
          this.yPum = 0;
        } else if (
          this.yPum >
          (document.documentElement.clientHeight ||
            document.body.clientHeight) -
            moveDiv.offsetHeight
        ) {
          this.yPum =
            (document.documentElement.clientHeight ||
              document.body.clientHeight) - moveDiv.offsetHeight;
        }
        if (this.xPum < 0) {
          this.xPum = 0;
        } else if (
          this.xPum >
          (document.documentElement.clientWidth || document.body.clientWidth) -
            moveDiv.offsetWidth
        ) {
          this.xPum =
            (document.documentElement.clientWidth ||
              document.body.clientWidth) - moveDiv.offsetWidth;
        }
        moveDiv.style.left = this.xPum + 'px'; //将计算得到的位置赋值给style
        moveDiv.style.top = this.yPum + 'px';
      }
    },
    //鼠标释放时候的函数
    touchend() {
      setTimeout(() => {
        this.isMoving = false;
      }, 300);
      const moveDiv = this.$refs.deviceInfo;
      // if (this.nx > 5 || this.ny > 5) {
      moveDiv.style.left =
        (document.documentElement.clientWidth || document.body.clientWidth) /
          2 <=
        this.xPum
          ? `${
              (document.documentElement.clientWidth ||
                document.body.clientWidth) - moveDiv.offsetWidth
            }px`
          : '0px';
      moveDiv.classList.add('animation');
      setTimeout(() => {
        moveDiv.classList.remove('animation');
      }, 300);
      this.flags = false;

      // }
    },
    clickEve() {
      if (this.isMoving) {
        return false;
      }
      const moveDiv = this.$refs.deviceInfo;
      if (
        moveDiv.style.left ===
          `${
            (document.documentElement.clientWidth ||
              document.body.clientWidth) -
            moveDiv.offsetWidth / 2
          }px` ||
        moveDiv.style.left === `-${moveDiv.offsetWidth / 2}px`
      ) {
        moveDiv.style.left =
          (document.documentElement.clientWidth || document.body.clientWidth) /
            2 <=
          this.xPum
            ? `${
                (document.documentElement.clientWidth ||
                  document.body.clientWidth) - moveDiv.offsetWidth
              }px`
            : '0px';
        setTimeout(() => {
          moveDiv.style.left =
            (document.documentElement.clientWidth ||
              document.body.clientWidth) /
              2 <=
            this.xPum
              ? `${
                  (document.documentElement.clientWidth ||
                    document.body.clientWidth) -
                  moveDiv.offsetWidth / 2
                }px`
              : `-${moveDiv.offsetWidth / 2}px`;
        }, 2500);
      } else {
        // 处理点击事件
        this.setH5gamePopup(1);
      }
    },
    isTouchSupport() {
      return (
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0
      );
    },
    closeH5() {
      this.setH5GameUrl('');
      this.setH5gamePopup(0);
    },
    ...mapMutations({
      setH5GameUrl: 'game/setH5GameUrl',
      setH5gamePopup: 'game/setH5gamePopup',
    }),
  },
};
</script>
<template>
  <div
    v-show="h5gamePopup == 2"
    ref="deviceInfo"
    @click.stop="clickEve()"
    id="ball"
  >
    <div @click.stop="closeH5()" class="close"></div>
  </div>
</template>
<style lang="less">
#ball {
  position: fixed;
  right: 0;
  top: 50%;
  z-index: 10000;
  width: 68.6 * @rem;
  height: 65 * @rem;
  .image-bg('~@/assets/images/ball.png');
  cursor: pointer;
  touch-action: none;
  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 15 * @rem;
    height: 15 * @rem;
    .image-bg('~@/assets/images/close-black-oo.png');
  }
}
.animation {
  transition: left 0.3s ease-in-out;
}
</style>
