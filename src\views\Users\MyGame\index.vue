<template>
  <div class="my-game-page">
    <nav-bar-2 :border="false" title="在玩" :azShow="true" :placeholder="false">
    </nav-bar-2>
    <div class="tab-bar" v-if="platform == 'iosBox' && tabList.length > 1">
      <div
        class="tab-item btn"
        v-for="(tab, tabIndex) in tabList"
        :key="tabIndex"
        :class="{ on: current === tabIndex }"
        @click="tapNav(tabIndex)"
      >
        {{ tab.name }}
      </div>
      <div
        class="line"
        :style="{
          left: `${(current * 2 + 1) * (375 / tabList.length / 2) * remNumberLess}rem`,
        }"
      ></div>
    </div>
    <div
      class="my-game-container"
      :class="{ pd50: platform == 'iosBox' && tabList.length > 1 }"
      v-for="(data, dataIndex) in dataList"
      :key="dataIndex"
      v-show="current === dataIndex"
    >
      <div
        class="cloud_vip_content"
        :class="{
          no_login_padding: !vipInfoList.login_status || !userInfo.token,
        }"
        v-if="tabList[current].id == 4"
      >
        <div class="vip_info">
          <div
            class="no_login_box"
            v-if="!vipInfoList.login_status || !userInfo.token"
          >
            <div class="cloud_novip_bg">
              <img
                src="~@/assets/images/cloud-game/cloud_novip_bg.png"
                alt=""
              />
            </div>
            <div class="cloud_novip_item">
              <div class="left_msg1">
                <div class="title">云玩会员</div>
                <div class="desc">排队优先·&nbsp; 高画质 ·&nbsp; 低延迟</div>
              </div>
              <div class="login_btn" @click.stop="toPage('PhoneLogin')"
                >登录</div
              >
            </div>
          </div>
          <div class="login_box" v-else>
            <div class="vip_top" @click.prevent="toPage('CloudGameUse')">
              <div class="cloud_vip_bg">
                <img
                  src="~@/assets/images/cloud-game/cloud_vip_bg.png"
                  alt=""
                />
              </div>
              <div class="left_msg2">
                <div class="title">云玩会员</div>
                <div class="time_desc">
                  <div class="vip_time">
                    <div
                      class="time"
                      :class="{
                        fontSize: vipInfoList?.user?.duration / 60 > 999,
                      }"
                      >{{ taskTime }}</div
                    >
                    <div class="task" @click.stop="openTaskListPopup">
                      <img
                        src="~@/assets/images/cloud-game/task_time_icon.png"
                        alt=""
                      />
                      <div class="task-text"
                        ><div> 做任务，得时长 </div>
                        <img
                          src="~@/assets/images/cloud-game/task_time_right.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                  <div class="text">剩余时长</div>
                </div>
              </div>
            </div>
            <div class="vip_bottom">
              <div class="vip_bottom_bg">
                <img
                  src="~@/assets/images/cloud-game/vip_bottom_bg.png"
                  alt=""
                />
              </div>
              <div class="msg">
                <div class="box open_vip" v-if="!vipInfoList.isVip">
                  <div class="title">开通会员送免费时长</div>
                  <div class="desc">排队优先·&nbsp; 高画质 ·&nbsp; 低延迟</div>
                </div>
                <div
                  class="box renew_vip"
                  v-else-if="
                    vipInfoList.isVip && vipInfoList.expirestatus !== 0
                  "
                >
                  <div class="title">尊享8项+会员专属特权</div>
                  <div class="desc"
                    >到期时间：{{ formatTime(vipInfoList.expiretime) }}</div
                  >
                </div>
                <div
                  class="box expire_vip"
                  v-else-if="vipInfoList.isVip && !vipInfoList.expirestatus"
                >
                  <div class="title">会员即将到期</div>
                  <div class="desc"
                    >到期时间：{{ formatTime(vipInfoList.expiretime) }}</div
                  >
                </div>
              </div>
              <div class="btn_box">
                <div class="btn_item" @click.stop="toPage('CloudGameBuy')">
                  <div v-if="!vipInfoList.isVip">开通会员</div>
                  <div v-else>立即续费</div>
                </div>
              </div>
            </div>
            <div class="cloud_vip_logo">
              <img
                src="~@/assets/images/cloud-game/cloud_vip_logo.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
      <yy-list
        v-if="data.data.length"
        class="my-game-content"
        v-model="data.loadingObj"
        :finished="data.finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
        :empty="data.empty"
      >
        <div class="header-title" v-if="tabList[current].id == 4">
          <span>最近玩过</span>
        </div>
        <div class="game-list">
          <div
            class="main-game-item"
            v-for="(item, index) in data.data"
            :key="index"
          >
            <game-item :info="item" @updateList="getMyGames"></game-item>
          </div>
        </div>
      </yy-list>
      <template v-else>
        <content-empty
          tips="暂无玩过的PC云游戏"
          v-if="tabList[current].id == 4"
        ></content-empty>
        <content-empty
          tips="暂无玩过的模拟器游戏"
          v-else-if="tabList[current].id == 5"
        ></content-empty>
        <content-empty tips="暂无在玩的游戏" v-else></content-empty>
      </template>
      <div class="recom-game-container" v-if="recommendGameList.length">
        <div class="header-title">
          <span>{{ tabList[current].id == 4 ? '云玩推荐' : '游戏推荐' }}</span>
          <div class="change-btn" @click="refreshRecomGame">换一换</div>
        </div>
        <div class="recom-game-list">
          <div
            class="game-item"
            v-for="(item, index) in recommendGameList"
            :key="index"
          >
            <game-item-4 :gameInfo="item" :iconSize="76"></game-item-4>
            <div
              v-if="item.detailid == 1"
              class="download-btn"
              @click.stop="cloudPlayInit(item, item.id)"
              >云玩</div
            >
            <div
              v-else-if="item.detailid == 2"
              class="download-btn"
              @click.stop="downJymyBtn(item)"
              >下载</div
            >
            <div
              v-else-if="item.classid == 140"
              class="download-btn"
              @click.stop="playDirectly(item)"
              >直接玩</div
            >
            <yy-download-btn
              class="game-download btn"
              :gameInfo="item"
              v-else
            ></yy-download-btn>
          </div>
        </div>
      </div>
    </div>
    <!-- pc云游戏任务弹窗 -->
    <taskListPopup
      @updateVipInfo="getMyGames"
      :show.sync="taskListPopupShow"
      v-if="platform == 'iosBox'"
    ></taskListPopup>
  </div>
</template>

<script>
import {
  ApiPlayingTabSpList,
  ApiPlayingGetRecommendedGames,
  ApiPlayingTabList,
} from '@/api/views/game.js';
import { remNumberLess } from '@/common/styles/_variable.less';
import { platform } from '@/utils/box.uni.js';
import ContentEmpty from './components/content-empty.vue';
import GameItem from './components/game-item.vue';
import taskListPopup from '@/components/task-list-popup';
import { downJymyBtnCallback, navigateToGameDetail } from '@/utils/function';
import { mapGetters, mapMutations, mapActions } from 'vuex';
export default {
  name: 'MyGame',
  components: { ContentEmpty, GameItem, taskListPopup },
  data() {
    return {
      remNumberLess,
      tabList: [],
      current: 0,
      dataList: [],
      platform,
      recommendGameList: [],
      vipInfoList: {},
      taskListPopupShow: false,
    };
  },
  computed: {
    ...mapGetters({
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
      simulatorInitLoading: 'game/simulatorInitLoading',
    }),
    taskTime() {
      const totalMinutes = this.vipInfoList?.user?.duration || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // 如果小时数大于 99999，则只显示小时
      if (hours > 99999) {
        return `${hours}小时`;
      }
      return `${hours}小时${minutes}分`;
    },
  },
  async activated() {
    await this.getTab();
    await this.getMyGames();
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
      setReceiveData: 'cloud_hangup/setReceiveData',
      setSimulatorInitLoadingEmpty: 'game/setSimulatorInitLoadingEmpty',
    }),
    ...mapActions({
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
      OPEN_SIMULATOR_GAME: 'game/OPEN_SIMULATOR_GAME',
    }),
    async getTab() {
      let res = await ApiPlayingTabList();
      this.tabList = res.data;
      this.tabList.forEach(tab => {
        this.dataList.push({
          data: [],
          finished: false,
          loadingObj: {
            loading: false,
            reloading: false,
          },
          empty: false,
          params: {
            page: 1,
            listRows: 10,
            tab_id: tab.id,
          },
        });
      });
    },
    tapNav(index) {
      if (this.current != index) {
        this.current = index;
        this.recommendGameList = [];
        this.getMyGames();
      }
    },
    async getMyGames(action = 1) {
      let active = this.dataList[this.current];
      if (action === 1) {
        active.params.page = 1;
      } else {
        if (active.finished) {
          return;
        }
        active.params.page++;
      }
      active.loadingObj.loading = true;

      const res = await ApiPlayingTabSpList(active.params);
      let list = [];
      res.data.forEach(item => {
        if (item.view_type == 75) {
          list = item.game_list;
        }
        if (item.view_type == 76) {
          this.recommendGameList = item.game_list;
        }
        if (item.view_type == 1) {
          this.vipInfoList = item.pcgame_list;
        }
      });
      if (action === 1 || active.params.page === 1) {
        active.data = [];
        if (!list.length) {
          active.empty = true;
        } else {
          active.empty = false;
        }
      }
      active.data.push(...list);
      if (list.length < active.params.listRows) {
        active.finished = true;
      } else {
        if (active.finished === true) {
          active.finished = false;
        }
      }
      active.loadingObj.loading = false;
    },
    async onRefresh() {
      let active = this.dataList[this.current];
      active.finished = false;
      try {
        await this.getMyGames();
      } finally {
        active.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      let active = this.dataList[this.current];
      await this.getMyGames(2);
      active.loadingObj.loading = false;
    },
    async refreshRecomGame() {
      this.$toast.loading();
      try {
        let res = await ApiPlayingGetRecommendedGames({
          tab_id: this.tabList[this.current].id,
        });
        this.recommendGameList = res.data.list;
      } finally {
        this.$toast.clear();
      }
    },
    formatTime(timestamp) {
      let { year, month, day, time } = this.$handleTimestamp(timestamp);
      return `${year}.${month}.${day} ${time}`;
    },
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    async playDirectly(item) {
      if (
        this.simulatorInitLoading[item.id] ||
        Object.values(this.simulatorInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_SIMULATOR_GAME(item);
    },
    openTaskListPopup() {
      this.taskListPopupShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.my-game-page {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
  background-color: #f5f6f7;

  .tab-bar {
    position: fixed;
    width: 100%;
    height: 50 * @rem;
    z-index: 200;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .tab-item {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      font-weight: 400;
      color: #797979;
      &.on {
        font-weight: 600;
        color: #000000;
      }
    }
    .line {
      width: 12 * @rem;
      height: 4 * @rem;
      border-radius: 2 * @rem;
      background-color: @themeColor;
      position: absolute;
      bottom: 2 * @rem;
      transform: translateX(-6 * @rem);
      transition: 0.3s;
    }
  }
  .header-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20 * @rem;

    span {
      height: 20 * @rem;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
    }

    .change-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 6 * @rem;
      border: 0.5 * @rem solid #bcbfc3;
      border-radius: 12 * @rem;
      height: 22 * @rem;
      line-height: 1;
      font-size: 11 * @rem;
      color: #adafb8;

      &::before {
        content: '';
        display: block;
        width: 10 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/change-icon.png) no-repeat center;
        background-size: 10 * @rem 10 * @rem;
        margin-right: 4 * @rem;
      }
    }
  }
  .my-game-container {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 1;
    padding: 50 * @rem 18 * @rem 0;

    .platform-coin-bg {
      position: relative;
      width: 339 * @rem;
      height: 70 * @rem;
      margin: 10 * @rem auto 0;
      .image-bg('~@/assets/images/mine/my_game_bg1_new.png');
      .button {
        position: absolute;
        top: 20 * @rem;
        right: 9 * @rem;
        width: 75 * @rem;
        height: 32 * @rem;
        border-radius: 30 * @rem;
      }
    }

    &.pd50 {
      padding-top: 100 * @rem;
    }
    .cloud_vip_content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      .no_login_box {
        .cloud_novip_bg {
          width: 339 * @rem;
          height: 71 * @rem;
        }
        .cloud_novip_item {
          position: absolute;
          width: 100%;
          top: 14 * @rem;
          padding: 0 12 * @rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          .left_msg1 {
            .title {
              font-weight: 600;
              font-size: 16 * @rem;
              color: #e9d39c;
            }
            .desc {
              margin-top: 6 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #9b8b63;
              white-space: nowrap;
            }
          }
          .login_btn {
            width: 84 * @rem;
            height: 32 * @rem;
            line-height: 32 * @rem;
            text-align: center;
            background: linear-gradient(137deg, #ffc180 0%, #ffe9a7 99%);
            border-radius: 16 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #712203;
          }
        }
      }
      .vip_info {
        box-sizing: border-box;
        .login_box {
          color: #fff;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          position: relative;
          margin-top: 12 * @rem;

          .vip_top {
            height: 101 * @rem;
            .cloud_vip_bg {
              width: 339 * @rem;
              height: 101 * @rem;
            }
            .left_msg2 {
              position: absolute;
              top: 14 * @rem;
              left: 12 * @rem;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              .title {
                height: 22 * @rem;
                font-weight: 600;
                font-size: 16 * @rem;
                color: #e9d39c;
                line-height: 22 * @rem;
              }
              .time_desc {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                margin-top: 14 * @rem;
                .vip_time {
                  display: flex;
                  align-items: center;
                  .time {
                    height: 25 * @rem;
                    font-weight: 600;
                    font-size: 20 * @rem;
                    color: #f9e3ac;
                    line-height: 25 * @rem;
                  }
                  .fontSize {
                    font-size: 17 * @rem;
                  }
                  .task {
                    position: relative;
                    img {
                      background-size: 112 * @rem 24 * @rem;
                      width: 112 * @rem;
                      height: 24 * @rem;
                    }
                    .task-text {
                      position: absolute;
                      top: 5 * @rem;
                      left: 12 * @rem;
                      display: flex;
                      align-items: center;
                      div {
                        height: 15 * @rem;
                        font-weight: 500;
                        font-size: 12 * @rem;
                        color: #701818;
                        line-height: 15 * @rem;
                      }
                      img {
                        margin-left: 1 * @rem;
                        width: 8 * @rem;
                        height: 8 * @rem;
                      }
                    }
                  }
                }

                .text {
                  margin-top: 3 * @rem;
                  height: 15 * @rem;
                  font-weight: 400;
                  font-size: 11 * @rem;
                  color: #ac9a6c;
                  line-height: 15 * @rem;
                }
              }
            }
          }

          .vip_bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            height: 52 * @rem;
            // background: linear-gradient(
            //   92deg,
            //   #fde9bf 0%,
            //   #fef7db 37%,
            //   #ffd69c 91%,
            //   #ffe6c2 98%
            // );
            // border: 1 * @rem solid;
            // border-image: linear-gradient(
            //     108deg,
            //     rgba(251, 221, 161, 1),
            //     rgba(251, 243, 193, 1),
            //     rgba(255, 201, 119, 1)
            //   )
            //   1 1;
            // padding: 9 * @rem 12 * @rem;
            position: relative;
            .vip_bottom_bg {
              position: absolute;
            }
            .msg {
              position: absolute;
              top: 9 * @rem;
              left: 12 * @rem;
              .box {
                .title {
                  height: 20 * @rem;
                  font-weight: 600;
                  font-size: 14 * @rem;
                  color: #772b21;
                  line-height: 20 * @rem;
                }
                .desc {
                  height: 14 * @rem;
                  font-weight: 400;
                  font-size: 10 * @rem;
                  color: #772b21;
                  line-height: 14 * @rem;
                }
              }
            }
            .btn_box {
              position: absolute;
              top: 9 * @rem;
              right: 12 * @rem;
              border-radius: 16 * @rem;
              overflow: hidden;
              .btn_item {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 32 * @rem;
                line-height: 32 * @rem;
                box-sizing: border-box;
                background: linear-gradient(137deg, #98432b 0%, #772b21 100%);

                div {
                  padding: 0 14 * @rem;
                  font-weight: 500;
                  font-size: 14 * @rem;
                  white-space: nowrap;
                }
              }
            }
          }
          .cloud_vip_logo {
            position: absolute;
            right: 0;
            top: -8 * @rem;
            background-size: 91 * @rem 93 * @rem;
            width: 91 * @rem;
            height: 93 * @rem;
          }
        }
      }
      &.no_login_padding {
        width: 339 * @rem;
        height: 71 * @rem;
        border-radius: 12 * @rem;
        overflow: hidden;
      }
    }
    .game-list {
      padding: 2 * @rem 0 0;
      .main-game-item {
        margin-top: 12 * @rem;
      }

      .game-item {
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        position: relative;
        display: flex;
        align-items: center;
        padding: 5 * @rem 0;
        .start {
          box-sizing: border-box;
          width: 64 * @rem;
          height: 30 * @rem;
          font-size: 12 * @rem;
          color: #fff;
          border-radius: 30 * @rem;
          background: @themeBg;
          display: flex;
          align-items: center;
          justify-content: center;
          &.h5 {
            width: 64 * @rem;
            height: 30 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            color: #fffcfa;
            background: linear-gradient(270deg, #fe8661 0%, #fe4d42 100%);
            border-radius: 6 * @rem;
            border: none;
          }
        }
      }
    }
  }
  .recom-game-container {
    .recom-game-list {
      margin-top: 10 * @rem;

      .game-item {
        display: flex;
        align-items: center;

        /deep/ .game-name {
          width: 100%;
          .text {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .game-subtitle {
            flex-shrink: 0;
          }
        }
      }
      .h5-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68 * @rem;
        height: 28 * @rem;
        background: #ecfbf4;
        border-radius: 25 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: @themeColor;
        line-height: 15 * @rem;
        text-align: center;
        margin-left: 12 * @rem;
      }
      .download-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68 * @rem;
        height: 28 * @rem;
        background: @themeBg;
        border-radius: 25 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #fff;
        line-height: 15 * @rem;
        text-align: center;
        margin-left: 12 * @rem;
      }
    }
  }
}
</style>
