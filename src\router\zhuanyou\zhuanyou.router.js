export default [
  {
    path: '/zhuanyou',
    name: '<PERSON><PERSON><PERSON>',
    component: () =>
      import(/* webpackChunkName: "zhuanyou" */ '@/views/Zhuanyou'),
    meta: {
      keepAlive: true,
      pageTitle: '转游中心',
    },
  },
  {
    path: '/zhuanyou/zhuanyoudian_detail',
    name: 'ZhuanyoudianDetail',
    component: () =>
      import(
        /* webpackChunkName: "zhuanyou" */ '@/views/Zhuanyou/ZhuanyoudianDetail'
      ),
    meta: {
      keepAlive: false,
      pageTitle: '转游明细',
    },
  },
  {
    path: '/zhuanyou/exchange_welfare/:id',
    name: 'ExchangeWelfare',
    component: () =>
      import(
        /* webpackChunkName: "zhuanyou" */ '@/views/Zhuanyou/ExchangeWelfare'
      ),
    meta: {
      keepAlive: false,
      pageTitle: '转游兑换福利',
    },
  },
];
