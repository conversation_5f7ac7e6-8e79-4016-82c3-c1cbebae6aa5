<template>
  <div class="page select-user-page">
    <nav-bar-2 :title="$t('指定玩家出售')" :border="true"></nav-bar-2>
    <div class="main">
      <div class="input-tip">
        {{
          $t(
            '请输入指定玩家的用户名，指定后，所出售的小号只有指定的 玩家可以购买。',
          )
        }}
      </div>
      <div class="input-container">
        <input
          type="text"
          class="input-text"
          :placeholder="$t('请输入指定玩家用户名')"
          v-model="username"
          :maxlength="15"
        />
      </div>
      <div class="search-container" v-if="username">
        <div class="search-user" v-if="!sellUserInfo.username">
          <div class="user-username">{{ username }}</div>
          <div class="search-tip">
            <div class="danger-icon"></div>
            <div class="tip-text">{{ tipText || $t('查询无该玩家') }}</div>
          </div>
        </div>
        <div class="user-bar" v-else>
          <div class="user-avatar">
            <img :src="sellUserInfo.avatar" alt="" />
          </div>
          <div class="user-username">{{ sellUserInfo.username }}</div>
        </div>
      </div>
    </div>
    <div class="bottom-bar fixed-center">
      <div class="confirm-btn btn" @click="confirmSell">
        {{ $t('确认出售') }}
      </div>
    </div>
    <sell-tip-dialog
      v-model="sellTipShow"
      :isRewrite="isRewrite"
    ></sell-tip-dialog>
  </div>
</template>

<script>
import SellTipDialog from '../components/sell-tip-dialog';
import { mapMutations, mapGetters } from 'vuex';
import { ApiUserCheckUserInfo } from '@/api/views/xiaohao.js';
export default {
  name: 'SelectUser',
  components: {
    SellTipDialog,
  },
  data() {
    return {
      username: '',
      sellUserInfo: {},
      timer: null,
      sellTipShow: false,
      isRewrite: false,
      tipText: '',
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  watch: {
    username() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(async () => {
        await this.getUser();
      }, 300);
    },
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    async getUser() {
      try {
        const res = await ApiUserCheckUserInfo({
          username: this.username,
        });
        // 全局code == 0的时候的处理有待商榷
        this.sellUserInfo = res.data;
      } catch (e) {
        this.sellUserInfo = {};
      }
    },
    confirmSell() {
      if (!this.sellUserInfo.username) {
        this.$toast(this.$t('请先选择指定用户'));
        return false;
      }
      this.setXiaohaoSellInfo({
        ...this.xiaohaoSellInfo,
        specifyUser: this.sellUserInfo.username,
      });
      this.sellTipShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.select-user-page {
  .main {
    padding: 0 14 * @rem;
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
    .input-tip {
      font-size: 13 * @rem;
      color: #666666;
      line-height: 20 * @rem;
      padding: 10 * @rem 0;
    }
    .input-container {
      height: 40 * @rem;
      display: flex;
      align-items: center;
      border-radius: 4 * @rem;
      background-color: #f6f6f6;

      .input-text {
        flex: 1;
        min-width: 0;
        font-size: 15 * @rem;
        color: #333;
        padding: 0 12 * @rem;
        background-color: transparent;
      }
    }
    .search-container {
      margin-top: 100 * @rem;
      display: flex;
      align-items: center;
      background-color: #f6f6f6;
      border-radius: 4 * @rem;
      padding: 10 * @rem;
      .search-user {
        display: flex;
        align-items: center;
        .user-username {
          font-size: 15 * @rem;
          color: #333333;
          white-space: nowrap;
        }
        .search-tip {
          display: flex;
          align-items: center;
          margin-left: 10 * @rem;
          .danger-icon {
            width: 14 * @rem;
            height: 14 * @rem;
            background: url(~@/assets/images/deal/danger-icon.png) no-repeat;
            background-size: 14 * @rem 14 * @rem;
          }
          .tip-text {
            font-size: 13 * @rem;
            color: #f53030;
            margin-left: 5 * @rem;
            flex: 1;
            min-width: 0%;
          }
        }
      }
      .user-bar {
        display: flex;
        align-items: center;
        .user-avatar {
          width: 25 * @rem;
          height: 25 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
        .user-username {
          font-size: 15 * @rem;
          color: #333333;
          margin-left: 7 * @rem;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(70 * @rem + @safeAreaBottom);
    height: calc(70 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .confirm-btn {
      flex: 1;
      min-width: 0;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
      background: @themeBg;
      border-radius: 8 * @rem;
      margin: 0 auto;
    }
  }
}
</style>
