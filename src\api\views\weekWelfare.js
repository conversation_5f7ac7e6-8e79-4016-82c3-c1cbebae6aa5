import { request } from '../index';

/**
 * 周周福利 - 首页
 */
export function ApiWeekWelfareIndex(params = {}) {
  return request('/api/week_welfare/index', params);
}

/**
 * 周周福利详情
 * @param { type } 4
 */
export function ApiWeekWelfareItem(params = {}) {
  return request('/api/week_welfare/item', params);
}

/**
 * 周周福利 - 领取
 * @param { type } 4
 */
export function ApiWeekWelfareTake(params = {}) {
  return request('/api/week_welfare/take', params);
}

/**
 * 新邀请 - 兑换现金
 * @param { string } id 现金奖品id
 */
export function ApiInviteExchangeMoneyPost(params = {}) {
  return request('/api/user/inviterExchangeMoneyPost', params);
}

/**
 * 新邀请 - 兑换现金页面
 */
export function ApiInviteExchangeMoney(params = {}) {
  return request('/api/user/inviterExchangeMoney', params);
}

/**
 * 新邀请 - 兑换金币
 * @param { string } id 金币奖品id
 */
export function ApiInviteExchangeGoldPost(params = {}) {
  return request('/api/user/inviterExchangeGoldPost', params);
}

/**
 * 新邀请 - 兑换金币页面
 */
export function ApiInviteExchangeGold(params = {}) {
  return request('/api/user/inviterExchangeGold', params);
}

/**
 * 新邀请 - 判断是否满足新版邀请条件
 */
export function ApiInviteIsInviter(params = {}) {
  return request('/api/user/isInviter', params);
}

/**
 * 新邀请 - 对换记录
 * @param {string} type 金币3 现金2
 */
export function ApiInviteExchangeLogList(params = {}) {
  return request('/api/user/inviterExchangeLogList', params);
}

/**
 * 新邀请 - 收入记录
 */
export function ApiInviteRewardList(params = {}) {
  return request('/api/user/inviterRewardList', params);
}

/**
 * 新邀请 - 新版邀请页面
 */
export function ApiInvitert(params = {}) {
  return request('/api/user/inviter', params);
}

/**
 * 新邀请 - 是否绑定微信 关注公众号
 */
export function ApiInviteExchangeWx(params = {}) {
  return request('/api/user/inviterExchangeWx', params);
}

/**
 * 新邀请 - 邀请记录
 */
export function ApiInviteUserList(params = {}) {
  return request('/api/user/inviterUserList', params);
}

/**
 * 腾讯充值专区
 * */

export function ApiTxRechargeArea(params = {}) {
  return request('/api/game/getTencentRecharge', params);
}
