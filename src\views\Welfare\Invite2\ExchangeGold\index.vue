<template>
  <div class="exchange-gold">
    <div class="header">
      <nav-bar-2
        bgStyle="transparent-white"
        :azShow="true"
        :title="'兑换金币'"
        class="nav"
      >
        <template #right>
          <div
            @click="toPage('InviteCostRecord', { type: 1 })"
            class="btn right"
          >
            兑换记录
          </div>
        </template>
      </nav-bar-2>
      <div class="bottom">
        <div class="left">
          <div class="small-text">当前现金</div>
          <div class="big-text"><span>￥</span>{{ money }}</div>
        </div>
        <div class="right">
          <div class="small-text">当前金币</div>
          <div class="big-text">{{ gold }}</div>
        </div>
      </div>
    </div>
    <main>
      <div class="title">兑换金币数量</div>
      <div class="list">
        <div
          v-for="(item, index) in list"
          :key="index"
          :class="{ current: current == index }"
          @click="current = index"
          class="item"
        >
          <div class="left">
            {{ item.title }}<span>（{{ item.subtitle }}）</span>
          </div>
          <div class="right">{{ item.consume_desc }}</div>
        </div>
      </div>
      <div @click="submit" class="btn big-button">兑换金币</div>
      <div class="tips">
        <div class="tips-title">重要提示</div>
        <div class="text">
          1.兑换比例“100金币=1元RMB”<br />
          2.兑换后不可撤回，请谨慎操作<br />
          3.若遇到问题，请联系客服
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import {
  ApiInviteExchangeGold,
  ApiInviteExchangeGoldPost,
} from '@/api/views/weekWelfare.js';

export default {
  data() {
    return {
      money: 0,
      gold: 0,
      list: [],
      current: 1,
    };
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      const res = await ApiInviteExchangeGold();
      this.money = res.data.money;
      this.gold = res.data.gold;
      this.list = res.data.gold_reward;
    },
    async submit() {
      const res = await ApiInviteExchangeGoldPost({
        id: this.list[this.current].id,
      });
      if (res.msg) {
        this.$toast(res.msg);
      }
      await this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.exchange-gold {
  .header {
    background: linear-gradient(100.18deg, #ffc875 1.05%, #ff7e55 98.1%);
    overflow: hidden;
    .nav {
      .right {
        color: #fff;
      }
    }
    .bottom {
      margin: 30 * @rem auto;
      color: #fff;
      display: flex;
      .big-text {
        margin-top: 5 * @rem;
        font-size: 32 * @rem;
        font-weight: 600;
        line-height: 38 * @rem;
        span {
          font-size: 20 * @rem;
        }
      }
      .left,
      .right {
        flex: 0 0 50%;
      }
      .left {
        padding-left: 18 * @rem;
        box-sizing: border-box;
      }
      .right {
        border-left: 1 * @rem solid rgba(255, 255, 255, 0.23);
        padding-left: 28 * @rem;
        box-sizing: border-box;
      }
    }
  }
  main {
    padding: 0 18 * @rem;
    .title {
      margin: 15 * @rem 0;
      font-size: 15 * @rem;
      font-weight: 600;
      color: #333438;
    }
    .item {
      display: flex;
      justify-content: space-between;
      position: relative;
      align-items: center;
      width: 339 * @rem;
      margin-bottom: 10 * @rem;
      height: 48 * @rem;
      padding: 0 14 * @rem;
      box-sizing: border-box;
      background: #fff9f1;
      border: 1 * @rem solid #fff9f1;
      border-radius: 8 * @rem;
      &.current {
        border: 1 * @rem solid #ff7f56;
        &::after {
          position: absolute;
          top: -1 * @rem;
          right: -1 * @rem;
          content: '';
          width: 14 * @rem;
          height: 14 * @rem;
          .image-bg('~@/assets/images/welfare/invite/invite_icon1.png');
        }
      }
      .left {
        font-size: 14 * @rem;
        font-weight: 600;
        color: #333333;
        span {
          font-size: 12 * @rem;
          font-weight: 400;
          color: #888888;
        }
      }
      .right {
        font-size: 14 * @rem;
        font-weight: 600;
        color: #676767;
      }
    }
    .big-button {
      width: 200 * @rem;
      height: 40 * @rem;
      margin: 28 * @rem auto;
      background: @themeColor;
      border-radius: 40 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .tips {
      margin: 60 * @rem auto 30 * @rem;
      .tips-title {
        margin-bottom: 8 * @rem;
        font-size: 14 * @rem;
        font-weight: 600;
        color: #4a4a4a;
      }
      .text {
        line-height: 18 * @rem;
        color: #666666;
      }
    }
  }
}
</style>
