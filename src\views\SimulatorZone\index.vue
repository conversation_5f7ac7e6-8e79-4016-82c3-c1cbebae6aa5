<template>
  <div class="simulator-zone-page">
    <van-loading class="loading-box" v-if="!loadSuccess" />
    <template v-for="(section, sectionIndex) in pageData" v-else>
      <!-- 最近在玩 -->
      <div
        class="recently-playing"
        :key="sectionIndex"
        v-if="section.view_type == 71"
      >
        <div class="recently-box">
          <!-- <div class="playing-title-icon">
            <img
              src="@/assets/images/simulator-zone/playing-title-icon.png"
              alt=""
            />
          </div> -->
          <div class="left-title">最近在玩</div>
          <recently-playing-list
            class="playing-list"
            :navList="section.game_list"
          ></recently-playing-list>
        </div>
      </div>
      <!-- 新游上线 -->
      <div
        class="new-game-launched"
        :key="sectionIndex"
        v-if="section.view_type == 72"
      >
        <div class="new-game-nav">
          <div class="left-title"> {{ section.header_title }} </div>
          <div
            class="right-more"
            v-if="section.more_status == 1"
            @click="btnMore"
          >
            <span>{{ section.more_text }} </span>
            <div>
              <img
                src="@/assets/images/simulator-zone/right-more-arrow.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="new-game-list">
          <div
            class="new-game-item"
            v-for="item in section.game_list"
            :key="item.id"
          >
            <div
              class="logo"
              @click="toPage('EmulatorGameDetail', { id: item.id })"
            >
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="title">{{ item.title }}</div>
            <div
              class="btn"
              :class="{ loading: simulatorInitLoading[item.id] }"
              @click="playDirectly(item)"
              >直接玩</div
            >
          </div>
        </div>
      </div>
      <!-- 广告模块 -->
      <div
        class="advertising-module"
        :key="sectionIndex"
        v-if="section.view_type == 28 && section.banner.length"
      >
        <yy-banner
          :bannerList="section.banner"
          :width="375 - section.banner[0].padding * 2"
          :height="
            (375 - section.banner[0].padding * 2) * section.banner[0].scale
          "
          :type="section.banner[0].type"
        ></yy-banner>
      </div>
      <!-- 模拟器专区tabs栏 -->
      <div
        class="simulator-zone-tabs"
        :key="sectionIndex"
        v-if="section.view_type == 74"
      >
        <van-sticky :offset-top="stickyOffsetTop" @scroll="scrollSticky">
          <swiper
            ref="classList11"
            class="class-list"
            :options="swiperOptions"
            :auto-update="true"
            style="width: 100%; margin: 0 auto"
          >
            <swiper-slide
              class="class-item btn"
              v-for="item in section.cate_list"
              :class="{ active: item.id == classId }"
              :key="item.id"
            >
              {{ item.title }}
            </swiper-slide>
          </swiper>
        </van-sticky>
        <yy-list
          class="simulator-zone-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="empty"
          :check="false"
        >
          <template v-for="item in SimulatorZoneList">
            <div class="simulator-zone-item" :key="item.id">
              <simulator-zone-tabs-item
                :info="item"
                :isShowBtn="true"
              ></simulator-zone-tabs-item>
            </div>
          </template>
        </yy-list>
      </div>
    </template>
  </div>
</template>

<script>
import RecentlyPlayingList from './components/recently-playing-list/index.vue';
import SimulatorZoneTabsItem from './components/simulator-zone-tabs-item/index.vue';
import {
  ApiSimulatorIndex,
  ApiSimulatorGetCateGameList,
} from '@/api/views/game';
import { mapActions, mapMutations, mapGetters } from 'vuex';
export default {
  name: 'SimulatorZone',
  components: {
    RecentlyPlayingList,
    SimulatorZoneTabsItem,
  },
  data() {
    let that = this;
    return {
      loadSuccess: false,
      // 页面碎片数据
      pageData: [],
      navList: [],
      //最近在玩列表
      swiper_click_flag: false,
      classList: [],
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        slideToClickedSlide: false,
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        on: {
          click: function () {
            if (that.swiper_click_flag) {
              return false;
            }
            that.swiper_click_flag = true;
            let list = that.pageData.find(item => item.view_type == 74);
            that.CLICK_EVENT(list.cate_list[this.clickedIndex]?.click_id);
            that.classChange(list.cate_list[this.clickedIndex]);
            that.swiper_click_flag = false;
          },
          init() {
            this.slideTo(that.currentSlideIndex, 0, true);
          },
        },
      },
      SimulatorZoneList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      classId: 0,
      page: 1,
      listRows: 10,
      empty: false,
      stickyOffsetTop: '0px', //顶部导航栏的高度
      currentSlideIndex: 0,
      isFixed: false, // 记录当前 isFixed 状态
      prevIsFixed: false, // 记录上一次 isFixed 状态
      scrollTop: 0,
    };
  },
  async mounted() {
    this.setSimulatorInitLoadingEmpty({});
    this.stickyOffsetTop =
      document.querySelector('.fixed').offsetHeight - 1 + 'px';
    await this.getSimulatorIndexInfo();
    await this.getSimulatorZoneList();
  },
  methods: {
    scrollSticky({ scrollTop, isFixed }) {
      this.isFixed = isFixed;

      // 只在 isFixed 状态改变时获取 scrollTop
      if (this.prevIsFixed !== isFixed) {
        this.scrollTop = scrollTop;
      }

      // 更新 prevIsFixed
      this.prevIsFixed = isFixed;
    },
    async updateData() {
      await this.updateSimulatorIndexInfo();
    },
    // 回到粘性顶部
    goToSticky() {
      if (!this.isFixed) return;
      window.scrollTo({
        top: this.scrollTop,
      });
    },
    // 直接玩
    async playDirectly(item) {
      if (
        this.simulatorInitLoading[item.id] ||
        Object.values(this.simulatorInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_SIMULATOR_GAME(item);
    },
    // 获取模拟器碎片信息
    async getSimulatorIndexInfo() {
      const res = await ApiSimulatorIndex();
      this.pageData = res.data;
      (this.classId = this.pageData.find(
        item => item.view_type == 74,
      )?.cate_list[0].id),
        (this.loadSuccess = true);
      this.navList = res.data[0].game_list;
      localStorage.setItem(
        'header_title',
        this.pageData.find(item => item.view_type === 72)?.header_title,
      );
    },
    // 更新模拟器碎片信息
    async updateSimulatorIndexInfo() {
      const res = await ApiSimulatorIndex();
      this.pageData = res.data;
      this.navList = res.data[0].game_list;
      localStorage.setItem(
        'header_title',
        this.pageData.find(item => item.view_type === 72)?.header_title,
      );
    },
    // 更多
    btnMore() {
      this.toPage('NewTourList');
    },
    async classChange(item) {
      if (this.classId == item?.id) {
        return false;
      }
      if (item?.id == undefined) return;
      this.classId = item?.id;

      this.goToSticky();
      await this.getSimulatorZoneList();
    },
    async getSimulatorZoneList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      try {
        const res = await ApiSimulatorGetCateGameList({
          id: this.classId,
          page: this.page,
          listRows: this.listRows,
        });
        let { list } = res.data;
        if (action === 1 || this.page == 1) {
          this.SimulatorZoneList = [];
          if (!list.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.SimulatorZoneList.push(...list);
        if (list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished) {
            this.finished = false;
          }
        }
      } finally {
        this.$nextTick(() => {
          this.loadingObj.loading = false;
        });
      }
    },
    async onRefresh() {
      await this.getSimulatorZoneList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getSimulatorZoneList(2);
    },
    ...mapActions({
      OPEN_SIMULATOR_GAME: 'game/OPEN_SIMULATOR_GAME',
    }),
    ...mapMutations({
      setSimulatorInitLoadingEmpty: 'game/setSimulatorInitLoadingEmpty',
    }),
  },
  computed: {
    ...mapGetters({
      simulatorInitLoading: 'game/simulatorInitLoading',
    }),
  },
};
</script>

<style lang="less" scoped>
.simulator-zone-page {
  width: 100%;
  box-sizing: border-box;
  /deep/.van-sticky--fixed {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 99;
    background: #fff;
    padding: 0 18 * @rem;
  }
  .loading-box {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .recently-playing {
    width: 375 * @rem;
    height: 149 * @rem;

    border-radius: 6 * @rem;
    padding: 0 18 * @rem;
    margin-bottom: 24 * @rem;
    box-sizing: border-box;
    .recently-box {
      background: linear-gradient(
        180deg,
        #f2fff6 0%,
        rgba(242, 255, 246, 0) 100%
      );
      border-radius: 6 * @rem;
      .playing-title-icon {
        padding: 8 * @rem 0 0 6 * @rem;
        background-position: -3 * @rem 0;
        background-size: 60 * @rem 29 * @rem;
        width: 57 * @rem;
        height: 25 * @rem;
      }
      .left-title {
        padding: 12 * @rem 0 0 12 * @rem;
        white-space: nowrap;
        overflow: hidden;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #000000;
      }
    }
  }
  .new-game-launched {
    padding: 0 18 * @rem 20 * @rem 18 * @rem;
    .new-game-nav {
      height: 25 * @rem;
      line-height: 25 * @rem;
      margin-bottom: 10 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-title {
        white-space: nowrap;
        overflow: hidden;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #000000;
      }
      .right-more {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        span {
          white-space: nowrap;
          font-weight: 400;
          font-size: 12px;
          color: #9a9a9a;
        }
        div {
          margin-left: 5 * @rem;
          background-origin: -1 * @rem -1 * @rem;
          background-size: 8 * @rem 13 * @rem;
          width: 7 * @rem;
          height: 12 * @rem;
        }
      }
    }
    .new-game-list {
      display: flex;
      align-items: center;

      .new-game-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 106 * @rem;
        height: 155 * @rem;
        background: #f8f8f8;
        border-radius: 10 * @rem;
        padding: 14 * @rem 19 * @rem;
        box-sizing: border-box;
        .logo {
          width: 68 * @rem;
          height: 68 * @rem;
          border-radius: 12 * @rem;
          overflow: hidden;
        }
        .title {
          width: 78 * @rem;
          margin-top: 8 * @rem;
          font-weight: 600;
          font-size: 11 * @rem;
          color: #333333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: center;
        }
        .btn {
          position: relative;
          margin-top: 12 * @rem;
          width: 54 * @rem;
          height: 24 * @rem;
          line-height: 24 * @rem;
          background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
          border-radius: 19 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #ffffff;
          text-align: center;
          &.loading {
            position: relative;
            font-size: 0;
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              -webkit-transform: translate(-50%, -50%);
              display: block;
              width: 16 * @rem;
              height: 16 * @rem;
              background-size: 16 * @rem 16 * @rem;
              background-image: url(~@/assets/images/downloadLoading.png);
              -webkit-animation: rotate 1s infinite linear;
              animation: rotate 1s infinite linear;
            }
          }
        }
        &:not(:first-child) {
          margin-left: 10 * @rem;
        }
      }
    }
  }
  .advertising-module {
    padding: 0 0 20 * @rem 0;
    /deep/.yy-swiper {
      margin: 0 auto 0;
    }
  }
  .simulator-zone-tabs {
    .class-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      .class-item {
        width: auto;
        display: flex;
        align-items: center;
        font-size: 14 * @rem;
        height: 20 * @rem;
        line-height: 20 * @rem;
        color: #868686;
        font-weight: 400;
        padding: 6 * @rem 10 * @rem 6 * @rem 10 * @rem;
        &:first-child {
          padding: 6 * @rem 10 * @rem 6 * @rem 18 * @rem;
        }
        &:last-child {
          padding: 6 * @rem 18 * @rem 6 * @rem 10 * @rem;
        }
        &.active {
          position: relative;
          font-weight: bolder;
          font-size: 16 * @rem;
          color: #111111;
        }
      }
    }
    .simulator-zone-list {
      padding: 0 18 * @rem;

      .simulator-zone-item {
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:not(:first-child) {
          margin-top: 14 * @rem;
        }
      }
    }
    /deep/.van-sticky--fixed {
      padding: 0;
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
