<template>
  <div class="sell-list-normal">
    <div
      class="sell-item btn"
      v-for="(item, index) in list"
      :key="index"
      @click="toPage('XiaohaoDetail', { id: item.id })"
    >
      <div class="left">
        <div class="info">
          <div class="pic">
            <img :src="item.images[0]" alt="" />
          </div>
          <div class="game-info">
            <div class="title">{{ item.game.title }}</div>
            <div class="game-name">{{ $t('区服') }}：{{ item.game_area }}</div>
            <div class="game-port">
              {{ $t('成交时间') }}：{{ item.update_time | formatTime }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="price">¥{{ Number(item.price).toFixed(2) }}</div>
        <div class="platform">
          <div
            class="plat-icon"
            v-for="(plat, platIndex) in item.platforms"
            :key="platIndex"
          >
            <img :src="plat.icon" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'sellListNormal',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
};
</script>

<style lang="less" scoped>
.sell-list-normal {
  box-sizing: border-box;
  width: 100%;
  padding: 10 * @rem 14 * @rem;
  .sell-item {
    display: flex;
    padding: 10 * @rem 0;
    .left {
      flex: 1;
      min-width: 0;
      .info {
        display: flex;
        .pic {
          width: 70 * @rem;
          height: 70 * @rem;
          border-radius: 10 * @rem;
          background-color: #dcdcdc;
          overflow: hidden;
          img {
            object-fit: cover;
          }
        }
        .game-info {
          flex: 1;
          min-width: 0;
          margin-left: 14 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .title {
            font-size: 16 * @rem;
            color: #000000;
            // font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .game-name {
            font-size: 12 * @rem;
            color: #ff8c05;
            margin-top: 10 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .game-port {
            font-size: 12 * @rem;
            color: #666666;
            margin-top: 6 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .right {
      width: 80 * @rem;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      .price {
        font-size: 15 * @rem;
        color: #ee1d44;
        font-weight: bold;
      }
      .gold {
        font-size: 10 * @rem;
        color: #ff395e;
        background-color: #ff395d10;
        border-radius: 3 * @rem;
        height: 15 * @rem;
        padding: 0 4 * @rem;
        display: flex;
        align-items: center;
        margin-top: 5 * @rem;
      }
      .platform {
        display: flex;
        align-items: center;
        margin-top: 15 * @rem;
        .plat-icon {
          width: 17 * @rem;
          height: 17 * @rem;
        }
      }
    }
  }
}
</style>
