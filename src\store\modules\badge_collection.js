export default {
    state: {
        mineBadgeInfo: null, // 我的 - 佩戴徽章
        wearBadgeInfo: null, // 成就徽章 - 佩戴徽章
    },
    getters: {
        mineBadgeInfo(state) {
            return state.mineBadgeInfo;
        },
        wearBadgeInfo(state) {
            return state.wearBadgeInfo;
        },
    },
    mutations: {
        setMineBadgeInfo(state, mineBadgeInfo) {
            state.mineBadgeInfo = mineBadgeInfo;
        },
        setWearBadgeInfo(state, wearBadgeInfo) {
            state.wearBadgeInfo = wearBadgeInfo;
            state.mineBadgeInfo = wearBadgeInfo;
        },
    },
    actions: {},
};
