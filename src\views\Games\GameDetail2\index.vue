<template>
  <div class="game-detail-page page">
    <nav-bar-2
      :bgStyle="computedBgStyle"
      :placeholder="false"
      v-if="navBgTransparent"
    >
      <template #left>
        <slot name="left">
          <div
            class="back"
            :class="{
              'back-black': computedBgStyle == 'transparent' || current !== 0,
            }"
            @click="back"
          ></div>
          <div
            class="nav-tabs"
            :class="{
              navBgTransparent: current !== 0,
            }"
            v-if="promo_image || detail?.morepic?.big.length"
          >
            <div
              class="nav-tab"
              v-for="(tab, index) in currentTabList"
              :class="{
                active: current === tab.tabIndex,
              }"
              :key="index"
              @click="clickTab(tab.tabIndex)"
            >
              <span>
                {{ tab.name }}
                <div
                  class="comment_count"
                  :class="{ comment_count_gray: current !== 0 }"
                  v-if="tab.tabIndex == 1 && Number(cmtSum)"
                >
                  {{ cmtSum }}
                </div>
              </span>
            </div>
          </div>
        </slot>
      </template>
      <template #right>
        <div
          class="collect-btn btn"
          :class="{
            'had': collected == 1,
            'collect-btn-white': computedBgStyle == 'transparent-white',
            'collect-btn-black': current !== 0,
          }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-2>
    <nav-bar-2
      :placeholder="false"
      :title="
        !promo_image && !detail?.morepic?.big.length ? detail?.main_title : ''
      "
      :border="true"
      v-else
    >
      <template #left>
        <slot name="left">
          <div class="back" @click="back"></div>
          <div
            class="nav-tabs navBgTransparent"
            v-if="promo_image || detail?.morepic?.big.length"
          >
            <div
              class="nav-tab btn"
              v-for="(tab, index) in currentTabList"
              :class="{
                active: current === tab.tabIndex,
              }"
              :key="index"
              @click="clickTab(tab.tabIndex)"
            >
              <span>
                {{ tab.name }}
                <div
                  class="comment_count"
                  :class="{
                    comment_count_gray: current !== 0 || !navBgTransparent,
                  }"
                  v-if="tab.tabIndex == 1 && Number(cmtSum)"
                >
                  {{ cmtSum }}
                </div>
              </span>
            </div>
          </div>
        </slot>
      </template>
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-2>
    <!-- 骨架屏占位 -->
    <game-detail-skeleton v-if="!loadSuccess" />
    <template v-else>
      <content-empty v-if="noGame" :tips="$t('没有该游戏')"></content-empty>
      <template v-else>
        <template>
          <!-- 详情页内容 -->
          <div
            v-if="
              current === 0 || (!promo_image && !detail?.morepic?.big.length)
            "
            class="top-banner"
            :class="{
              'not-banner': !promo_image && !detail.morepic.big.length,
              'top-banner-atlas': selectedModule == 'gallery',
            }"
          >
            <div
              class="game-cover"
              :class="{
                'detail-banner-bg': promo_image || detail.morepic.big.length,
              }"
            >
              <div class="banner-bg" :class="{ 'f-blur': loadSuccess }">
                <img :src="promo_image" alt="" />
              </div>
              <swiper
                id="bannerSwiper"
                class="banner-swiper"
                ref="bannerSwiper"
                :options="swiperOptions"
                v-if="promo_image || detail.morepic.big.length"
              >
                <swiper-slide
                  class="swiper-slide-h"
                  :style="{ height: `${currentHeight * remNumberLess}rem` }"
                >
                  <img
                    class="slide-img"
                    @click="showBigImage(0)"
                    :src="promo_image"
                    alt=""
                  />
                </swiper-slide>
                <swiper-slide
                  class="swiper-slide-h video-container"
                  :style="{ height: `${currentHeight * remNumberLess}rem` }"
                  v-if="detail.video_url"
                >
                  <video
                    ref="videoPlayer"
                    id="video"
                    :src="detail.video_url"
                    :poster="detail.video_thumb"
                    :controls="isPlaying"
                    x5-playsinline=""
                    :playsinline="true"
                    :webkit-playsinline="true"
                    :x-webkit-airplay="true"
                    x5-video-player-type="h5"
                    :x5-video-player-fullscreen="true"
                    x5-video-orientation="portraint"
                    muted
                  ></video>
                  <div class="mask" v-show="!isPlaying">
                    <div class="play-btn" @click="handlePlay"></div>
                  </div>
                </swiper-slide>
                <swiper-slide
                  v-for="(item, index) in detail.morepic
                    ? detail.morepic.small
                    : []"
                  :key="index"
                  class="swiper-slide-h morepic-slide"
                  :style="{
                    height: `${currentHeightAtlas * remNumberLess}rem`,
                  }"
                >
                  <img
                    :src="item"
                    class="slide-img"
                    @click="showBigImage(index + 1)"
                  />
                </swiper-slide>
              </swiper>
            </div>
            <div
              class="banner-bar"
              v-if="promo_image && detail.morepic.big.length"
            >
              <div class="bar-box">
                <div class="tab-bar">
                  <div class="tab-list">
                    <span
                      class="tab-item"
                      :class="{ active: selectedModule === 'promo' }"
                      @click="selectModule('promo')"
                      >宣传图</span
                    >
                    <span
                      class="tab-item"
                      :class="{ active: selectedModule === 'gallery' }"
                      @click="selectModule('gallery')"
                      >图集</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="
              current === 0 || (!promo_image && !detail?.morepic?.big.length)
            "
            class="game-info"
            :class="{
              'not-game-info': !promo_image && !detail.morepic.big.length,
              'not-morepic-info': promo_image && !detail.morepic.big.length,
            }"
          >
            <div class="game-top-info">
              <div class="left-info">
                <div class="game-img">
                  <img :src="detail.titlepic" alt="" />
                </div>
                <div class="game-msg">
                  <div class="game-name">
                    <div class="title"> {{ detail.main_title }}</div>
                    <div
                      class="sub-title"
                      v-if="detail.subtitle && !isPcCloudGame"
                      >{{ detail.subtitle }}</div
                    >
                  </div>
                  <template v-if="isPcCloudGame">
                    <div
                      class="tag-list"
                      v-if="
                        extra_info?.cate_info &&
                        extra_info?.cate_info?.length &&
                        detail.detailid == 1
                      "
                    >
                      <div class="tags">
                        <div
                          class="tag"
                          v-for="tag in extra_info?.cate_info?.slice(0, 3)"
                          :key="tag.id"
                        >
                          {{ tag.title }}
                        </div>
                      </div>
                    </div>
                    <div
                      class="share-box"
                      v-if="
                        extra_info.up_info &&
                        Object.keys(extra_info.up_info).length
                      "
                    >
                      <span class="text2">由</span>
                      <span
                        class="up-icon"
                        @click="
                          toPage('UpMine', {
                            mem_id: extra_info.up_info.user_id,
                          })
                        "
                      >
                        <UserAvatar
                          :src="extra_info.up_info.avatar"
                          :self="false"
                        />
                      </span>
                      <span class="text3">分享</span>
                    </div>
                    <div
                      class="jy_app"
                      v-if="detail.detailid == 2 && head_text.text"
                    >
                      {{ head_text.text }}
                    </div>
                  </template>
                  <template v-else>
                    <div class="game-version">
                      <div class="version">
                        {{
                          (isIos ? detail.version_i : detail.version) || '1.0.0'
                        }}</div
                      >
                      <div class="volume">
                        <template v-if="isIos && detail.size_i_str">{{
                          detail.size_i_str
                        }}</template>
                        <template v-if="!isIos && detail.size_a_str">{{
                          detail.size_a_str
                        }}</template>
                      </div>
                    </div>

                    <div
                      class="game-new-time"
                      @click="getKaifuList()"
                      v-if="detail.classid !== 115"
                    >
                      <div
                        class="time-box"
                        :class="{ 'msg-info': !head_text.type }"
                      >
                        <span>{{ head_text.text }}</span>
                        <span v-if="head_text.icon">
                          <img :src="head_text.icon" alt="" />
                        </span>
                      </div>
                    </div>
                    <div class="game-new-time" v-else>
                      <div class="msg-info" v-if="extra_info.company_name"
                        >来源于：{{ extra_info.company_name }}</div
                      >
                    </div>
                  </template>
                </div>
              </div>
              <div class="right-info" v-if="extra_info.score || Number(cmtSum)">
                <span class="score" v-if="extra_info.score">{{
                  extra_info.score
                }}</span>
                <div class="number" v-if="Number(cmtSum)">{{ cmtSum }}评价</div>
              </div>
            </div>
            <div class="game-tags" v-if="rankingTypeList.length">
              <template v-for="(item, index) in rankingTypeList">
                <div class="tag" :key="index">
                  <span class="icon" v-if="item.icon">
                    <img :src="item.icon" alt="" />
                  </span>
                  {{ item.title }}
                </div>
              </template>
            </div>
            <div
              class="activity-bar"
              v-if="!userInfo.is_svip"
              @click="toPage('Svip')"
            >
              <div class="left-info">
                <div class="icon">
                  <img src="~@/assets/images/games/activity-icon.png" alt="" />
                </div>
                <div class="title"
                  >SVIP会员金币奖励翻倍，享金币兑换平台币等18+项特权</div
                >
              </div>
              <div class="right-arrow">
                <span> </span>
              </div>
            </div>

            <!-- 新版详情小金刚区 648,代金券,礼包,返利 -->
            <div
              class="welfare-list"
              :class="{
                'not-img-welfare-list':
                  !promo_image && !detail.morepic.big.length,
                'not-welfare-box': !extra_info?.pay_coin_tag?.length,
              }"
              v-if="
                detail.classid != 41 &&
                (card648?.status ||
                  extra_info.card_count > 0 ||
                  parseInt(coupon.s_sum) + parseInt(coupon.sum) > 0 ||
                  fanli_count > 0)
              "
            >
              <!-- 648 -->
              <div class="welfare-item gift-648">
                <div class="line-1">
                  <div class="num" :class="{ 'no-num': !card648?.status }">
                    <span>¥</span>648
                  </div>
                </div>
                <div
                  class="line-2"
                  :class="{
                    'line-3': card648?.status == 2,
                    'line-4': card648?.status == 1,
                  }"
                >
                  <div
                    class="btn-648"
                    v-if="card648?.status"
                    @click="takeGift648"
                  >
                    {{ card648?.status == 1 ? '去使用' : '领取' }}
                    <img
                      v-if="card648?.status == 2"
                      src="~@/assets/images/games/welfare-right-icon.png"
                      alt=""
                    />
                    <img
                      v-else-if="card648?.status == 1"
                      src="~@/assets/images/games/welfare-right-icon1.png"
                      alt=""
                    />
                  </div>
                  <div v-else class="not-item-title"> 无 </div>
                </div>
              </div>
              <!-- 礼包 -->
              <div class="welfare-item" @click="selectCurrent(1)">
                <div class="line-1">
                  <div
                    class="num"
                    :class="{ 'no-num1': !extra_info.card_count }"
                  >
                    {{ extra_info.card_count || '无' }}
                  </div>
                </div>
                <div class="line-2">
                  <div class="icon1">
                    <img
                      v-if="!extra_info.card_count"
                      src="@/assets/images/games/libao-icon-grey.png"
                      alt=""
                    />
                    <img
                      v-else
                      src="@/assets/images/games/libao-icon.png"
                      alt=""
                    />
                  </div>
                  <div
                    class="item-title"
                    :class="{ 'not-item-title': !extra_info.card_count }"
                    >{{ $t('礼包') }}</div
                  >
                </div>
              </div>
              <!-- 代金券 -->
              <div class="welfare-item" @click="selectCurrent(0)">
                <div class="line-1">
                  <div
                    class="num"
                    :class="{
                      'no-num1': !(
                        parseInt(coupon.s_sum) + parseInt(coupon.sum)
                      ),
                    }"
                  >
                    <span
                      v-if="parseInt(coupon.s_sum) + parseInt(coupon.sum) > 0"
                      >¥</span
                    >{{ parseInt(coupon.s_sum) + parseInt(coupon.sum) || '无' }}
                  </div>
                </div>
                <div class="line-2">
                  <div class="icon2">
                    <img
                      v-if="!(parseInt(coupon.s_sum) + parseInt(coupon.sum))"
                      src="@/assets/images/games/daijinquan-icon-grey.png"
                      alt=""
                    />
                    <img
                      v-else
                      src="@/assets/images/games/daijinquan-icon.png"
                      alt=""
                    />
                  </div>
                  <div
                    class="item-title"
                    :class="{
                      'not-item-title': !(
                        parseInt(coupon.s_sum) + parseInt(coupon.sum)
                      ),
                    }"
                    >{{ $t('代金券') }}</div
                  >
                </div>
              </div>
              <!-- 返利 -->
              <div class="welfare-item" @click="selectCurrent(2)">
                <div class="line-1">
                  <div class="num" :class="{ 'no-num1': !fanli_count }">
                    {{ fanli_count || '无' }}
                  </div>
                </div>
                <div class="line-2">
                  <div class="icon3">
                    <img
                      v-if="!fanli_count"
                      src="@/assets/images/games/fanli-icon-grey.png"
                      alt=""
                    />
                    <img
                      v-else
                      src="@/assets/images/games/fanli-icon.png"
                      alt=""
                    />
                  </div>
                  <div
                    class="item-title"
                    :class="{ 'not-item-title': !fanli_count }"
                    >返利</div
                  >
                </div>
              </div>
            </div>
            <div
              v-if="
                extra_info?.pay_coin_tag && extra_info?.pay_coin_tag?.length
              "
              class="welfare-box"
              :class="{
                'not-648': !(
                  detail.classid != 41 &&
                  (card648?.status ||
                    extra_info.card_count > 0 ||
                    parseInt(coupon.s_sum) + parseInt(coupon.sum) > 0 ||
                    fanli_count > 0)
                ),
                'not-img': !promo_image && !detail.morepic.big.length,
              }"
            >
              <div class="welfare-box-list">
                <div
                  class="welfare-item"
                  v-for="(item, index) in extra_info.pay_coin_tag"
                  :key="index"
                  @click="toPayCoinTag(item)"
                >
                  <img
                    v-if="item.is_ok"
                    src="~@/assets/images/games/welfare-yes.png"
                    alt=""
                  />
                  <img
                    v-else
                    src="~@/assets/images/games/welfare-no.png"
                    alt=""
                  />
                  <span :class="{ on: item.is_ok }">
                    {{ item.title }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            class="tab-container"
            :class="{
              'not-tab-container':
                (!promo_image && !detail.morepic.length) || current !== 0,
              'not-morepic-container':
                current == 0 && promo_image && !detail.morepic.big.length,
            }"
            ref="tabContainer"
          >
            <van-sticky
              v-if="!promo_image && !detail?.morepic?.big.length"
              :offset-top="stickyOffsetTop"
              @scroll="handleStickyScroll"
            >
              <div class="tabs" :class="{ 'default-shadow': !isSticky }">
                <div
                  class="tab btn"
                  v-for="(tab, index) in currentTabList"
                  :key="index"
                  :class="{
                    active: current === tab.tabIndex,
                    text3_active: tab.name.length === 3,
                    text4_active: tab.name.length >= 4,
                  }"
                  @click="clickTab(tab.tabIndex)"
                >
                  <span>
                    {{ tab.name }}
                    <div
                      class="comment_count"
                      v-if="tab.tabIndex == 1 && Number(cmtSum)"
                    >
                      {{ cmtSum }}
                    </div>
                  </span>
                </div>
              </div>
            </van-sticky>
            <!-- 详情tab -->
            <div class="tab-content tab-detail" v-if="current === 0">
              <van-loading v-if="!loadSuccess" />
              <template v-else>
                <!-- 独家福利 -->
                <template v-if="exclusive_benefits.length">
                  <div class="benefits-container section">
                    <div class="section-title">
                      <div
                        class="title-text"
                        @click="platformBenefitsShow = true"
                        >独家福利
                        <img
                          class="section-arrow"
                          src="~@/assets/images/games/section-arrow.png"
                          alt=""
                        />
                      </div>
                    </div>
                    <div class="benefits-list">
                      <div
                        class="benefits-item"
                        @click="clickBenefitItem(item)"
                        v-for="(item, index) in exclusive_benefits"
                        :key="index"
                      >
                        <div class="icon">
                          <img :src="item.icon" alt="" />
                        </div>
                        <div class="center">
                          <div class="title-line">
                            <div class="title">{{ item.title }}</div>
                            <div class="subtitle" v-if="item.subtitle">
                              {{ item.subtitle }}
                            </div>
                          </div>
                          <div class="desc">{{ item.desc }}</div>
                        </div>
                        <div class="right-icon"></div>
                      </div>
                    </div>
                  </div>
                </template>
                <!-- 福利公告 -->
                <div class="welfare-info section" v-if="notice_list.length">
                  <div class="section-title">
                    <div class="title-text" @click="welfareNoticeShow = true"
                      >{{ $t('福利公告') }}
                      <img
                        class="section-arrow"
                        src="~@/assets/images/games/section-arrow.png"
                        alt=""
                    /></div>
                  </div>
                  <div class="notice-list">
                    <div
                      v-for="(item, index) in notice_list"
                      :key="index"
                      class="notice-item"
                    >
                      <span class="notice-type">{{ item.tag_name }}</span>
                      <span @click="toNoticeDetail(item)" class="notice-title">
                        <span class="text">{{ item.title }}</span>
                        <i class="notice-icon"></i>
                      </span>
                    </div>
                  </div>
                </div>
                <!-- 内置插件 -->
                <div
                  class="built-in-plugins section"
                  v-if="built_in_plugins.length"
                >
                  <div class="section-title">
                    <div class="title-text">内置插件</div>
                  </div>
                  <div class="built-in-plugins-list">
                    <div
                      class="built-in-plugin-item"
                      v-for="(item, index) in built_in_plugins"
                      :key="index"
                    >
                      <div class="icon">
                        <img :src="item.icon" alt="" />
                      </div>
                      <div class="title">{{ item.title }}</div>
                      <div class="desc">{{ item.desc }}</div>
                    </div>
                  </div>
                </div>
                <!-- 上线福利 -->
                <div class="welfare-container section" v-if="detail.features">
                  <div class="welfare-box">
                    <div class="welfare-title">
                      <div class="title">
                        <img
                          src="~@/assets/images/games/welfare-container-title.png"
                          alt=""
                        />
                      </div>
                      <div class="arrow" @click="platformBenefitsShow = true">
                        <img
                          src="~@/assets/images/games/welfare-container-arrow.png"
                          alt=""
                        />
                      </div>
                    </div>
                    <div class="wel-section">
                      <div
                        class="content-text on"
                        ref="content1"
                        v-html="detail.features"
                      ></div>
                      <!-- <div
                        class="more-text"
                        @click="isAll1 = true"
                        v-if="contentHeight1 > 120 && !isAll1"
                      >
                        <div> {{ !isAll1 ? '&nbsp;...' : '' }}</div>
                        <span>{{ !isAll1 ? $t('展开') : '' }}</span>
                      </div> -->
                    </div>
                  </div>
                </div>
                <!-- 更新内容 -->
                <div class="update-content section" v-if="update_content">
                  <div class="section-title">
                    <div class="title-text">更新内容</div>
                  </div>
                  <div class="update-box" v-html="update_content"></div>
                </div>

                <!-- 游戏介绍 -->
                <div class="introduction section" v-if="detail.newstext">
                  <div class="section-title">
                    <div
                      class="title-text"
                      @click="
                        toPage('GameDetailIntro', {
                          detail: detail,
                          information: extra_info.information,
                        })
                      "
                      >{{ $t('游戏介绍') }}
                      <img
                        class="section-arrow"
                        src="~@/assets/images/games/section-arrow.png"
                        alt=""
                    /></div>
                    <div
                      v-if="extra_info.pay_tutorials_url"
                      class="recharge-back"
                      @click="
                        toPage('Iframe', {
                          title: '氪金攻略',
                          url: extra_info.pay_tutorials_url,
                        })
                      "
                    >
                      <span class="text">氪金攻略</span>
                      <span class="icon"> </span>
                    </div>
                  </div>
                  <div class="category-tags">
                    <div
                      class="category-tag-item"
                      v-for="(item, index) in extra_info?.cate_info"
                      :key="index"
                      @click="handleCategoryTag(item)"
                    >
                      {{ item.title }}
                    </div>
                  </div>
                  <div
                    v-html="detail.newstext"
                    class="introduction-text"
                    ref="content3"
                    :class="{ on: !isAll3 }"
                  ></div>
                  <div
                    class="more-text"
                    @click="isAll3 = true"
                    v-if="contentHeight3 > 156 && !isAll3"
                  >
                    <div> {{ !isAll3 ? '&nbsp;&nbsp;...' : '' }}</div>
                    <span>{{ !isAll3 ? $t('展开') : '' }}</span>
                  </div>
                </div>
                <!-- 开服动态 -->
                <div class="open-server-info section" v-if="openList.length">
                  <div class="section-title">
                    <div class="title-text" @click="getKaifuList()"
                      >{{ $t('开服动态') }}
                      <img
                        class="section-arrow"
                        src="~@/assets/images/games/section-arrow.png"
                        alt=""
                    /></div>
                  </div>
                  <div class="kaifu-container">
                    <div class="kaifu-list">
                      <div
                        class="kaifu-item"
                        v-for="(kaifu, kaifuIndex) in openList"
                        :key="kaifuIndex"
                      >
                        <div class="left">
                          <div class="time">
                            <div class="day">
                              {{
                                kaifu.is_today
                                  ? $t('今日')
                                  : $handleTimestamp(kaifu.newstime).date
                              }}
                            </div>
                            <div class="hour">
                              {{ $handleTimestamp(kaifu.newstime).time }}
                            </div>
                          </div>
                          <div class="quhao">{{ kaifu.state }}</div>
                        </div>
                        <div
                          class="status had"
                          v-if="kaifu.countdown_second < 0"
                        >
                          {{ $t('已开服') }}
                        </div>
                        <template v-else>
                          <div
                            class="status btn"
                            v-if="kaifu.status == 0"
                            @click="remind(kaifu.id, 1)"
                          >
                            {{ $t('提醒我') }}
                          </div>
                          <div
                            class="status btn"
                            v-if="kaifu.status == 1"
                            @click="remind(kaifu.id, 0)"
                          >
                            {{ $t('取消') }}
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 温馨提示 -->
                <div class="section reminder-text" v-if="detail.characteristic">
                  <div class="section-title">
                    <div class="title-text">温馨提示</div>
                  </div>
                  <div class="content">
                    <div
                      class="content-text"
                      ref="content4"
                      :class="{ on: !isAll4 }"
                      v-html="detail.characteristic"
                    ></div>
                    <div
                      class="more-text"
                      @click="isAll4 = true"
                      v-if="contentHeight4 > 100 && !isAll4"
                    >
                      <div> {{ !isAll4 ? '&nbsp;...' : '' }}</div>
                      <span>{{ !isAll4 ? $t('展开') : '' }}</span>
                    </div>
                  </div>
                </div>

                <!-- vip价格表 -->
                <div class="vip-table section" v-if="detail?.vip_price?.length">
                  <div class="section-title">
                    <div class="title-text">{{ $t('VIP价格表') }}</div>
                  </div>
                  <div class="vip-desc">
                    {{ detail.vip_content }}
                  </div>
                  <div class="table-container" :class="{ open: vipOpen }">
                    <div class="table" ref="vipTable">
                      <div class="row">
                        <div class="th">{{ $t('VIP等级') }}</div>
                        <div class="th">RMB</div>
                      </div>
                      <div
                        class="row"
                        v-for="(item, index) in detail?.vip_price"
                        :key="index"
                      >
                        <div class="td">{{ item.level_title }}</div>
                        <div class="td">{{ item.recharge }}</div>
                      </div>
                    </div>
                    <div
                      class="vip-open"
                      v-if="vipOpen == false"
                      @click="vipOpen = true"
                    >
                      <span>{{ $t('展开全部') }}</span>
                      <div class="more-text-icon"></div>
                    </div>
                  </div>
                </div>
                <!-- 广告位 -->
                <div class="banner-fragment" v-if="bannerFragment.length">
                  <ad-banner
                    :bannerList="bannerFragment"
                    :width="375 - bannerFragment[0].padding * 2"
                    :height="
                      (375 - bannerFragment[0].padding * 2) *
                      bannerFragment[0].scale
                    "
                    :type="bannerFragment[0].type"
                  >
                  </ad-banner>
                </div>

                <!-- 游戏评价 -->
                <div class="hot-comments section" v-if="topCommentList?.length">
                  <div class="section-title">
                    <div class="title-text" @click="clickTab(1)"
                      >{{ $t('游戏评价') }}
                      <span class="number" v-if="cmtSum">({{ cmtSum }})</span>
                      <img
                        class="section-arrow"
                        src="~@/assets/images/games/section-arrow.png"
                        alt=""
                      />
                    </div>
                    <div class="comments-btn" @click="clickComment">
                      <span class="icon"> </span>
                      <span class="text">去评价</span>
                    </div>
                  </div>
                  <div
                    class="hot-comment-list"
                    :class="{ 'margin-b32': shouldShow }"
                  >
                    <comment-item-2
                      class="item"
                      :comment="item"
                      :showScoreTime="true"
                      v-for="(item, index) in topCommentList.slice(0, 3)"
                      :key="index"
                    ></comment-item-2>
                  </div>
                  <div
                    class="comment-more"
                    v-if="shouldShow"
                    @click="current = 1"
                  >
                    <div class="comment-more-text">{{
                      $t('查看全部游戏评价')
                    }}</div>
                    <i></i>
                  </div>
                </div>

                <!-- 详细信息 -->
                <div class="section version-section">
                  <div class="section-title">
                    <div class="title-text">详细信息 </div>
                    <div
                      class="feed-back"
                      @click="
                        toPage('NewOpinion', {
                          title: detail.main_title,
                          game_id: detail.id,
                        })
                      "
                    >
                      <span class="text">反馈</span>
                      <span class="icon"> </span>
                    </div>
                  </div>
                  <div class="version-container">
                    <template
                      v-for="item in extra_info.information.slice(0, 6)"
                    >
                      <div
                        class="content-item"
                        v-if="item.text || item.url || item.up_info"
                        :key="item.key"
                      >
                        <div class="label">{{ item.title }}</div>
                        <div class="value-box" v-if="item.text">
                          <div class="value1">{{ item.text }}</div>
                        </div>
                        <div class="value-box" v-if="item?.url">
                          <template v-for="(val, index) in item.url">
                            <div
                              :key="index"
                              class="value2"
                              @click="handleUrl(val)"
                            >
                              {{ val.text }}
                            </div>
                          </template>
                        </div>
                        <div
                          class="up-info"
                          @click="
                            toPage('UpMine', { mem_id: item.up_info.user_id })
                          "
                          v-if="item?.up_info"
                        >
                          <UserAvatar
                            :src="item?.up_info.avatar"
                            :self="false"
                            class="avatar"
                          />
                          <span class="nickname">{{
                            item?.up_info?.nickname
                          }}</span>
                          <span class="arrow"> </span>
                        </div>
                      </div>
                    </template>

                    <div
                      class="see-more-info"
                      @click="
                        toPage('GameDetailIntro', {
                          detail: detail,
                          information: extra_info.information,
                        })
                      "
                    >
                      <div>查看全部信息</div>
                      <span> </span>
                    </div>
                  </div>
                </div>

                <!-- 更多游戏推荐 -->
                <div
                  class="related section"
                  v-if="formatRecommendGameInfo.list.length"
                >
                  <div class="section-title">
                    <!-- <div class="title-icon icon-xiangguan"></div> -->
                    <div class="title-text">{{
                      formatRecommendGameInfo.title
                    }}</div>
                  </div>
                  <div class="game-list">
                    <div
                      class="game-group"
                      v-for="(
                        related, relatedIndex
                      ) in formatRecommendGameInfo.list"
                      :key="relatedIndex"
                    >
                      <div
                        class="game-item"
                        @click="toPage('GameDetail', { id: item.id })"
                        v-for="(item, index) in related"
                        :key="index"
                      >
                        <game-item-4
                          :gameInfo="item"
                          :iconSize="68"
                        ></game-item-4>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 同开发者其他游戏 -->
                <!-- <div
                  class="related section"
                  v-if="formatSameCpGameInfo.list.length"
                >
                  <div class="section-title">
                    <div class="title-text">{{
                      formatSameCpGameInfo.title
                    }}</div>
                  </div>
                  <div class="game-list">
                    <div
                      class="game-group"
                      v-for="(
                        related, relatedIndex
                      ) in formatSameCpGameInfo.list"
                      :key="relatedIndex"
                    >
                      <div
                        class="game-item"
                        @click="toPage('GameDetail', { id: item.id })"
                        v-for="(item, index) in related"
                        :key="index"
                      >
                        <game-item-4
                          :gameInfo="item"
                          :iconSize="68"
                        ></game-item-4>
                      </div>
                    </div>
                  </div>
                </div> -->
              </template>
            </div>
            <div class="fixed-review-box" v-if="current === 1 && !noGame">
              <div class="fixed-review-btn" @click="clickComment()">
                <img src="~@/assets/images/games/fixed-review-btn.png" alt="" />
              </div>
            </div>
            <!-- 评论tab -->
            <div
              class="tab-content tab-comment"
              :class="{
                'tab-content-top': promo_image || detail?.morepic?.big.length,
              }"
              v-if="current === 1"
            >
              <comment-tab></comment-tab>
            </div>
            <!-- 交易tab -->
            <div
              class="tab-content tab-deal"
              :class="{
                'tab-content-top': promo_image || detail?.morepic?.big.length,
              }"
              v-if="detail.classid != 41 && current === 2"
            >
              <deal-tab :gameId="detail.id"></deal-tab>
            </div>
            <!-- 开服表tab -->
            <div
              class="tab-content tab-kaifu"
              :class="{
                'tab-content-top': promo_image || detail?.morepic?.big.length,
              }"
              v-if="current === 3"
            >
              <div class="kaifu-section">
                <div class="kaifu-tips">
                  {{ $t('开服信息仅供参考，以游戏内实际开服为准') }}
                </div>
                <div class="kaifu-active btn" @click="openKefu"></div>
              </div>
              <kaifu-list :gameId="detail.id"></kaifu-list>
            </div>
            <!-- 工具箱tab -->
            <div
              class="tab-content tab-tool"
              :class="{
                'tab-content-top': promo_image || detail?.morepic?.big.length,
              }"
              v-if="current === 4"
            >
              <tool-tab :gameId="detail.id"></tool-tab>
            </div>
            <!-- UP资源 -->
            <div
              class="tab-content tab-up"
              :class="{
                'tab-content-top': promo_image || detail?.morepic?.big.length,
              }"
              v-if="
                detail.classid == 41 &&
                current == 2 &&
                ![1, 2].includes(detail?.detailid)
              "
            >
              <content-empty
                v-if="up_game_list.length === 0"
                :tips="'该UP尚未上传其他游戏'"
              ></content-empty>
              <template v-else>
                <game-item-3
                  class="up-game-item"
                  v-for="(item, index) in up_game_list"
                  :key="index"
                  :gameInfo="item"
                ></game-item-3>
              </template>
            </div>
          </div>
        </template>

        <!-- 底部fixed -->
        <div
          class="bottom-container"
          :class="{ 'tool-bg': current == 4 }"
          v-if="loadSuccess"
        >
          <div class="bottom-fixed">
            <div
              v-if="extra_info?.bottom_btn && extra_info?.bottom_btn?.type == 3"
              class="comment btn"
              @click="clickComment"
            >
              <div class="comment-icon"></div>
              <div class="comment-title">{{ $t('评价') }}</div>
            </div>
            <div v-else class="gift-item">
              <div class="benefit-item" @click="handleBenefitItem">
                <div
                  class="gift-icon-1"
                  v-if="extra_info?.bottom_btn?.type == 1"
                ></div>
                <div
                  class="gift-icon-2"
                  v-if="extra_info?.bottom_btn?.type == 2"
                ></div>
                <div class="benefit-icon">
                  <img src="~@/assets/images/games/benefit-icon.png" alt="" />
                </div>
                <div class="benefit-text">福利</div>
              </div>
            </div>
            <!-- ios的h5的判断优先值最高 2022年9月30日10:57:33----------------------------------------------------------- -->
            <!-- detail.state == 2 为预约游戏，直接使用下面就的download-bar -->
            <!-- ios的h5不再是优先级最高了,有游戏下载的还是显示'下载' 2023年8月3日18:16:10 -->
            <div class="download-bar" v-if="isPcCloudGame">
              <div class="download-content">
                <div
                  v-if="detail.detailid == 1"
                  class="btn pc-cloud-download-btn"
                  @click="cloudPlayInit(detail, detail.id)"
                >
                  <div
                    class="text"
                    :class="{ loading: pcCloudGameInitLoading[detail.id] }"
                    >云玩</div
                  >
                </div>
                <div
                  v-else
                  class="btn pc-cloud-download-btn"
                  @click="downJymyBtn(detail)"
                >
                  <div
                    class="text"
                    :class="{ loading: pcCloudGameInitLoading[detail.id] }"
                    >下载</div
                  >
                </div>
              </div>
            </div>
            <div
              class="download-bar"
              v-else-if="
                isIos &&
                detail.h5_url &&
                detail.state != 2 &&
                !downloadButtonShow &&
                !cloudGameButtonShow
              "
            >
              <div class="download-content">
                <div class="btn h5-btn" @click="startGame">
                  {{ $t('开始游戏') }}
                  <i
                    class="question"
                    @click.stop="
                      toPage('Iframe', {
                        title: $t('H5游戏优化推荐'),
                        url: $h5Page.h5GameQA,
                      })
                    "
                  ></i>
                </div>
                <div class="btn download-btn" @click="downloadH5Game">
                  {{ $t('添加到桌面') }}
                  <i
                    class="question"
                    @click.stop="
                      toPage('Iframe', {
                        title: $t('H5游戏添加到桌面'),
                        url: $h5Page.h5GameDownloadQA,
                      })
                    "
                  ></i>
                </div>
              </div>
            </div>
            <div class="download-bar" v-else>
              <!-- 根据dl_config隐藏下载按钮按钮 -->
              <template v-if="dlConfig != 1">
                <div class="download-content" v-if="detail.state == 2">
                  <div
                    class="btn subscribe-btn"
                    @click="handleSubscribe()"
                    v-if="detail.subscribed != 1"
                  >
                    {{ $t('预约新游首发') }}
                  </div>
                  <div class="btn subscribe-no" v-else>
                    {{ $t('已预约') }}
                  </div>
                </div>
                <div class="download-content" v-else-if="!isAllNull">
                  <div
                    class="btn h5-btn"
                    @click="cloudGame()"
                    v-if="cloudGameButtonShow"
                  >
                    {{ $t('开始玩（云玩）') }}
                    <i
                      v-if="isIos"
                      class="question"
                      @click.stop="
                        toPage('Iframe', {
                          title: $t('云玩的FAQ'),
                          url: $h5Page.cloudGameQA,
                        })
                      "
                    ></i>
                  </div>
                  <div
                    class="btn h5-btn"
                    @click="startGame"
                    v-if="showStartGame"
                  >
                    {{ $t('开始游戏') }}
                    <i
                      v-if="isIos"
                      class="question"
                      @click.stop="
                        toPage('Iframe', {
                          title: $t('H5游戏优化推荐'),
                          url: $h5Page.h5GameQA,
                        })
                      "
                    ></i>
                  </div>
                  <div
                    class="btn download-btn"
                    v-if="downloadButtonShow && !downloadChangeCloudGame"
                    @click="handleDownload"
                    :class="{ loading: downloadLoading }"
                  >
                    <!-- web_down_gq == 1是通过 可以下载（充值过18元） -->
                    <!-- web_down_gq只要不是1就显示下载，充值18元后再显示游戏价格 -->
                    <template
                      v-if="
                        detail.buy_info &&
                        detail.buy_info.status == 1 &&
                        (!userInfo.token || userInfo.web_down_gq == 1)
                      "
                    >
                      购买 <span class="unit">¥</span
                      >{{ detail.buy_info.buy_amount }}
                    </template>
                    <template v-else>
                      {{ $t('下载') }}
                      <template v-if="isIos && detail.size_i_str"
                        >({{ detail.size_i_str }})</template
                      >
                      <template v-if="!isIos && detail.size_a_str"
                        >({{ detail.size_a_str }})</template
                      >
                    </template>

                    <i
                      v-if="isIos"
                      class="question"
                      @click.stop="
                        detail.repair
                          ? toPage('Iframe', {
                              title: $t('个人签FAQ'),
                              url: $h5Page.grqQA,
                            })
                          : toPage('Iframe', {
                              title: $t('企业签FAQ'),
                              url: $h5Page.qyqQA,
                            })
                      "
                    ></i>
                  </div>
                  <div
                    v-else
                    class="btn download-btn pd-7"
                    @click="handleDownload"
                  >
                    <template> 云玩 </template>
                  </div>
                </div>
                <!-- 没有下载链接没有云游也没有h5 -->
                <div class="download-content" v-else>
                  <div
                    v-if="isPartMJAndRroidGame"
                    class="btn download-btn pd-7"
                    @click="goToCloudGame()"
                  >
                    <template> 云玩 </template>
                  </div>
                  <div v-else class="download-no">{{ $t('暂不支持下载') }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </template>
    </template>
    <!-- 安装配置文件，是safari -->
    <van-popup
      v-model="getUdidPopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      class="grqPopup"
    >
      <div class="top-right" @click="getUdidPopupShow = false">
        {{ $t('暂不安装') }}
      </div>
      <div class="container">
        <div class="text">
          {{ $t('安装至尊版游戏前须安装苹果系统描述文件。安装时点击“')
          }}<span class="color1">{{ $t('允许') }}</span
          >{{ $t('”，切勿点击“') }}<span class="color2">{{ $t('忽略') }}</span
          >”
        </div>
        <div
          :class="{ loading: grqLoading }"
          @click="checkUdid()"
          class="button"
        >
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-popup>
    <!-- 个人签安装进度 -->
    <van-dialog
      v-model="downloadGrqDialogShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="downloadGrqDialog"
    >
      <div v-for="(item, index) in grqStatusName" :key="index" class="item">
        <div class="left">{{ item }}</div>
        <div
          class="right"
          :class="{
            loading: grqStatus === index,
            success: grqStatus === index + 1 || grqStatus === index + 2,
          }"
        ></div>
      </div>
      <div class="text">{{ $t('请不要离开此页面并保持屏幕常亮') }}</div>
      <div @click="toPage('GrqList')" class="button">
        {{ $t('查看进度') }}<i class="icon"></i>
      </div>
    </van-dialog>
    <!-- 无论个人签企业签在下载的时候都给他弹的提示窗口 -->
    <van-popup
      v-model="downloadPopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      class="no-safari-popup"
    >
      <div class="title">
        {{ $t('下载小贴士')
        }}<span @click="downloadPopupShow = false" class="close"></span>
      </div>
      <div class="content">
        <div class="item">
          <div class="big-text">
            <span class="sign">1.</span
            ><span class="text">{{ $t('同意安装游戏') }}</span>
          </div>
          <div class="small-text">{{ $t('游戏将在桌面自动下载哦') }}</div>
        </div>
        <div class="item">
          <div class="big-text">
            <span class="sign">2.</span
            ><span class="text">{{ $t('游戏安装成功后') }}</span>
          </div>
          <div class="small-text">
            {{ $t('若需授权信任，请打开')
            }}<span class="color green">{{
              $t('设置->通用->设备管理->信任企业级应用授权哦')
            }}</span>
          </div>
        </div>
        <div class="item">
          <div class="big-text">
            <span class="sign">3.</span
            ><span class="text">{{ $t('无法弹出安装弹窗') }}</span>
          </div>
          <div class="small-text">
            {{ $t('请复制链接后，在“Safari”浏览器打开。')
            }}<span @click="copy()" class="color blue">{{ $t('复制') }}</span>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>
    <cardpass-copy-popup
      :show.sync="giftCopyPopupShow"
      v-if="card648 && Object.keys(card648).length"
      :info="card648"
    ></cardpass-copy-popup>

    <!-- 充值18元平台币提示弹窗 -->
    <van-popup
      v-model="ptbTipsShow"
      :close-on-click-overlay="false"
      position="center"
      round
      :lock-scroll="false"
    >
      <div class="ptb-tips-popup">
        <div class="title">温馨提示</div>
        <div class="desc">
          仅需充值{{ ptbCount / 10 }}元平台币即可享无限下载哟~
        </div>
        <div class="tip">注：平台币可用于游戏内充值</div>
        <div class="operation-bar">
          <div class="btn cancel" @click="ptbTipsShow = false">取消</div>
          <div class="btn confirm" @click="openPtbRecharge">立即充值</div>
        </div>
      </div>
    </van-popup>

    <!-- 18元平台币充值弹窗 -->
    <van-popup
      v-model="ptbRechargeShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="ptb-recharge-popup">
        <div class="close" @click="ptbRechargeShow = false"></div>
        <div class="title">平台币充值</div>
        <div class="recharge-info">
          <div class="ptb-num">{{ ptbCount }}平台币</div>
          <div class="money">
            ¥<span>{{ ptbCount / 10 }}</span>
          </div>
        </div>

        <div class="pay-list">
          <div
            class="pay-item"
            v-for="(item, index) in payWayList"
            :key="index"
            @click="selectedPayType = item.key"
          >
            <div class="icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="pay-name">{{ item.name }}</div>
            <div
              class="choose"
              :class="{ on: selectedPayType == item.key }"
            ></div>
          </div>
        </div>
        <div class="recharge-btn" @click="handlePtbRecharge">
          立即充值¥{{ ptbCount / 10 }}
        </div>
      </div>
    </van-popup>

    <!-- 购买游戏弹窗 -->
    <van-popup
      v-model="buyGamePopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="buy-game-popup">
        <div class="close" @click="buyGamePopupShow = false"></div>
        <div class="title">购买游戏</div>
        <div class="buy-game-bar" v-if="buyInfo.game">
          <div class="game-icon">
            <img :src="buyInfo.game.titlepic" alt="" />
          </div>
          <div class="buy-game-info">
            <div class="game-title">{{ buyInfo.game.title }}</div>
          </div>
          <div class="amount">
            ¥<span>{{ buyInfo.buy_amount }}</span>
          </div>
        </div>
        <div class="pay-way-title">支付方式</div>
        <div class="pay-list">
          <template v-for="(item, index) in buyInfo.payway_list">
            <div
              class="pay-item"
              v-if="item.is_priority || !buyInfo.payway_more"
              :key="index"
              @click="selectedBuyGamePayType = item.key"
            >
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="pay-name">{{ item.name }}</div>
              <div
                class="choose"
                :class="{ on: selectedBuyGamePayType == item.key }"
              ></div>
            </div>
          </template>
        </div>
        <div
          v-if="buyInfo.payway_more"
          class="more-pay-way"
          @click="buyInfo.payway_more = 0"
        >
          改变支付方式
        </div>
        <div class="recharge-btn" @click="handleBuyGame">立即购买</div>
      </div>
    </van-popup>

    <!-- 游戏购买成功 -->
    <van-popup
      v-model="buyGameSeccessShow"
      :close-on-click-overlay="false"
      position="center"
      round
      :lock-scroll="false"
    >
      <div class="buy-game-success-popup">
        <div class="title">购买成功</div>
        <div class="order-list">
          <div class="order-item">
            <div class="order-title">订单号</div>
            <div class="order-desc">{{ buyGameOrderInfo.order_id }}</div>
          </div>
          <div class="order-item">
            <div class="order-title">订单金额</div>
            <div class="order-desc">¥{{ buyGameOrderInfo.amount }}</div>
          </div>
        </div>
        <div class="operation-bar">
          <div class="btn confirm" @click="closeBuyGameSuccessPopup">
            知道了
          </div>
        </div>
      </div>
    </van-popup>

    <van-dialog
      class="wx-popup"
      v-model="wx_popup.show"
      :lock-scroll="false"
      show-cancel-button
      confirmButtonText="设置微信提醒"
      @confirm="toPage('BindWeChat')"
    >
      <img class="bg" src="~@/assets/images/games/ic_subscribe_success.png" />
      <div class="big-text">设置微信提醒</div>
      <div class="small-text">
        开服提醒需先绑定微信，关注公众号后才可生效哦~
      </div>
    </van-dialog>

    <van-overlay
      class="banner-images"
      v-if="showBannerImage"
      :show="showBannerImage"
      @click="showBannerImage = false"
    >
      <div class="wrapper">
        <div class="block">
          <swiper
            id="bannerImagesSwiper"
            ref="bannerImagesSwiper"
            class="banner-center"
            :options="swiperBannerOptions"
            :auto-update="false"
            @slideChange="onSlideBannerChange"
          >
            <swiper-slide
              class="swiper-slide"
              v-for="(item, index) in bannerImagesList"
              :key="index"
            >
              <img :src="item" />
            </swiper-slide>
            <swiper-slide class="swiper-slide">
              <div class="ad-info">
                <div class="titlepic">
                  <img :src="detail.titlepic" alt="" />
                </div>
                <div class="title">{{ detail.main_title }}</div>
                <div class="info-bottom">
                  <template>
                    <div v-for="(item, index) in discountTagList" :key="index">
                      <div class="discount-tag" v-if="item.tag === 5">
                        <img
                          class="discount-icon discount-01"
                          src="@/assets/images/games/discount-01.png"
                          v-if="item.title.includes('0.1')"
                        />
                        <img
                          class="discount-icon"
                          src="@/assets/images/games/discount-normal.png"
                          v-else
                        />
                        <div class="discount-text"
                          ><span>{{ item.title }}</span></div
                        >
                      </div>
                      <div v-else class="tags">
                        <div class="tag">
                          <div class="tag-name">{{ item.title }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
                <div class="tips" v-if="detail?.newstext?.length"
                  ><div class="tips-content" v-html="detail?.newstext"> </div
                ></div>
                <div class="down-btn">
                  <div class="download-bar" v-if="isPcCloudGame">
                    <div class="download-content">
                      <div
                        v-if="detail.detailid == 1"
                        class="btn pc-cloud-download-btn"
                        @click="cloudPlayInit(detail, detail.id)"
                      >
                        <div
                          class="text"
                          :class="{
                            loading: pcCloudGameInitLoading[detail.id],
                          }"
                          >云玩</div
                        >
                      </div>
                      <div
                        v-else
                        class="btn pc-cloud-download-btn"
                        @click="downJymyBtn(detail)"
                      >
                        <div
                          class="text"
                          :class="{
                            loading: pcCloudGameInitLoading[detail.id],
                          }"
                          >下载</div
                        >
                      </div>
                    </div>
                  </div>
                  <div
                    class="download-bar"
                    v-else-if="
                      isIos &&
                      detail.h5_url &&
                      detail.state != 2 &&
                      !downloadButtonShow &&
                      !cloudGameButtonShow
                    "
                  >
                    <div class="download-content">
                      <div class="btn h5-btn" @click="startGame">
                        {{ $t('开始游戏') }}
                        <i
                          class="question"
                          @click.stop="
                            toPage('Iframe', {
                              title: $t('H5游戏优化推荐'),
                              url: $h5Page.h5GameQA,
                            })
                          "
                        ></i>
                      </div>
                      <div class="btn download-btn" @click="downloadH5Game">
                        {{ $t('添加到桌面') }}
                        <i
                          class="question"
                          @click.stop="
                            toPage('Iframe', {
                              title: $t('H5游戏添加到桌面'),
                              url: $h5Page.h5GameDownloadQA,
                            })
                          "
                        ></i>
                      </div>
                    </div>
                  </div>
                  <div class="download-bar" v-else>
                    <!-- 根据dl_config隐藏下载按钮按钮 -->
                    <template v-if="dlConfig != 1">
                      <div class="download-content" v-if="detail.state == 2">
                        <div
                          class="btn subscribe-btn"
                          @click="handleSubscribe()"
                          v-if="detail.subscribed != 1"
                        >
                          {{ $t('预约新游首发') }}
                        </div>
                        <div class="btn subscribe-no" v-else>
                          {{ $t('已预约') }}
                        </div>
                      </div>
                      <div class="download-content" v-else-if="!isAllNull">
                        <div
                          class="btn h5-btn"
                          @click="cloudGame()"
                          v-if="cloudGameButtonShow"
                        >
                          {{ $t('开始玩（云玩）') }}
                          <i
                            v-if="isIos"
                            class="question"
                            @click.stop="
                              toPage('Iframe', {
                                title: $t('云玩的FAQ'),
                                url: $h5Page.cloudGameQA,
                              })
                            "
                          ></i>
                        </div>
                        <div
                          class="btn h5-btn"
                          @click="startGame"
                          v-if="showStartGame"
                        >
                          {{ $t('开始游戏') }}
                          <i
                            v-if="isIos"
                            class="question"
                            @click.stop="
                              toPage('Iframe', {
                                title: $t('H5游戏优化推荐'),
                                url: $h5Page.h5GameQA,
                              })
                            "
                          ></i>
                        </div>
                        <div
                          class="btn download-btn pd-7"
                          v-if="downloadButtonShow && !downloadChangeCloudGame"
                          @click="handleDownload"
                          :class="{ loading: downloadLoading }"
                        >
                          <!-- web_down_gq == 1是通过 可以下载（充值过18元） -->
                          <!-- web_down_gq只要不是1就显示下载，充值18元后再显示游戏价格 -->
                          <template
                            v-if="
                              detail.buy_info &&
                              detail.buy_info.status == 1 &&
                              (!userInfo.token || userInfo.web_down_gq == 1)
                            "
                          >
                            购买 <span class="unit">¥</span
                            >{{ detail.buy_info.buy_amount }}
                          </template>
                          <template v-else>
                            {{ $t('下载') }}
                            <template v-if="isIos && detail.size_i_str"
                              >({{ detail.size_i_str }})</template
                            >
                            <template v-if="!isIos && detail.size_a_str"
                              >({{ detail.size_a_str }})</template
                            >
                          </template>

                          <i
                            v-if="isIos"
                            class="question"
                            @click.stop="
                              detail.repair
                                ? toPage('Iframe', {
                                    title: $t('个人签FAQ'),
                                    url: $h5Page.grqQA,
                                  })
                                : toPage('Iframe', {
                                    title: $t('企业签FAQ'),
                                    url: $h5Page.qyqQA,
                                  })
                            "
                          ></i>
                        </div>
                        <div
                          v-else
                          class="btn download-btn pd-7"
                          @click="handleDownload"
                        >
                          <template> 云玩 </template>
                        </div>
                      </div>
                      <!-- 没有下载链接没有云游也没有h5 -->
                      <div class="download-content" v-else>
                        <div
                          v-if="isPartMJAndRroidGame"
                          class="btn download-btn pd-7"
                          @click="goToCloudGame()"
                        >
                          <template> 云玩 </template>
                        </div>
                        <div v-else class="download-no">{{
                          $t('暂不支持下载')
                        }}</div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
        <div class="sort" v-if="currentSlide < bannerImagesList.length"
          >{{ currentSlide + 1 }}/{{ bannerImagesList.length }}</div
        >
        <div class="back"> </div>
      </div>
    </van-overlay>
    <!-- 开服动态 -->
    <kaifu-list-popup
      v-if="kaifuListShow"
      :soonOpenList="soonOpenList"
      :openOverList="openOverList"
      :show.sync="kaifuListShow"
      :gameId="Number($route.params.id)"
    ></kaifu-list-popup>

    <!-- 福利公告 -->
    <welfare-notice-popup
      :show.sync="welfareNoticeShow"
      :notice_list="notice_list"
    ></welfare-notice-popup>

    <!-- 平台福利 -->
    <platform-benefits-popup
      :show.sync="platformBenefitsShow"
      :exclusive_benefits="exclusive_benefits"
      :features="detail.features"
    ></platform-benefits-popup>

    <!-- 平台活动（代金券 礼包 返利整合） -->
    <platform-activity-popup
      :currentIndex="currentIndex"
      :game_id="Number(detail.id)"
      :class_id="detail.classid"
      :vip_content="extra_info?.vip_content"
      :vip_price_list="extra_info?.vip_price"
      :show.sync="platformActivityShow"
    ></platform-activity-popup>

    <!-- pc云游戏任务弹窗 -->
    <taskListPopup :show.sync="_taskListPopupShow"></taskListPopup>

    <!-- 时长耗尽弹窗 -->
    <durationOverPopup :show.sync="_durationOverShow"> </durationOverPopup>
  </div>
</template>

<script>
import { Sticky, ImagePreview } from 'vant';
import BottomSafeArea from '@/components/bottom-safe-area';
import StatusBar from '@/components/status-bar';
import KaifuList from './components/kaifu-list';
import CommentTab from './components/comment-tab';
import DealTab from './components/deal-tab';
import ToolTab from './components/tool-tab';
import AdBanner from './components/ad-banner';
import CommentItem2 from '@/components/comment-item-2';
import KaifuListPopup from '@/components/kaifu-list-popup';
import WelfareNoticePopup from '@/components/welfare-notice-popup';
import PlatformBenefitsPopup from '@/components/platform-benefits-popup';
import platformActivityPopup from '@/components/platform-activity-popup';
import durationOverPopup from '@/components/duration-over-popup';
import taskListPopup from '@/components/task-list-popup';
import { PageName, handleActionCode } from '@/utils/actionCode.js';
import {
  ApiGameDetail,
  ApiGameDetailRecommendGame,
  ApiGameBuyGameInfo,
  ApiGameCreateBuyGameOrder,
  ApiServerIndex,
  ApigrqGameList,
  ApiResourceCollect,
  ApiResourceCollectStatus,
  ApiGameGetPermissionInfo,
  ApiGameSubscribe,
  ApiGameCheckDown,
  ApiGameWebDownSignVerify,
  ApiGetGameAct,
  ApiServerRemind,
} from '@/api/views/game.js';
import {
  ApiCreateOrderPtb,
  ApiGetPayUrl,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
import {
  isIos,
  isIosBox,
  isWebApp,
  isSafari,
  needGuide,
  isAndroidBox,
} from '@/utils/userAgent';
import {
  ApiPersonalSign,
  ApiCheckUdid,
  ApiUserCheckGrqAuthStatus,
} from '@/api/views/users.js';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import {
  startCloudGame,
  startH5Game,
  downloadH5Game,
  downloadAdd,
  jumpAnnouncement,
  downJymyBtnCallback,
} from '@/utils/function';
import {
  ApiCommentComments,
  ApiCommentClickComment,
} from '@/api/views/comment.js';
import { ApiCardGet } from '@/api/views/gift.js';
import {
  platform,
  BOX_showActivity,
  BOX_openInBrowser,
  BOX_getPackageName,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';
import { remNumberLess } from '@/common/styles/_variable.less';
import { GameDetailSkeleton } from '@/components/skeleton';
import BASEPARAMS from '@/utils/baseParams';
import { mapState } from 'vuex';

export default {
  name: 'GameDetail',
  components: {
    StatusBar,
    BottomSafeArea,
    KaifuList,
    CommentTab,
    DealTab,
    ToolTab,
    AdBanner,
    CommentItem2,
    KaifuListPopup,
    WelfareNoticePopup,
    PlatformBenefitsPopup,
    platformActivityPopup,
    durationOverPopup,
    taskListPopup,
    'van-sticky': Sticky,
    GameDetailSkeleton,
  },
  data() {
    let _this = this;
    return {
      platform,
      current: 0,
      bgStyle: 'transparent-white',
      tabList: [
        { name: this.$t('详情'), tabIndex: 0 },
        { name: this.$t('评价'), tabIndex: 1 },
        { name: this.$t('交易'), tabIndex: 2 },
        // { name: this.$t('开服表'), tabIndex: 3 },
        // { name: '工具箱', tabIndex: 4 },
      ],
      tabList2: [
        { name: '详情', tabIndex: 0 },
        { name: '评价', tabIndex: 1 },
        { name: 'UP资源', tabIndex: 2 },
      ], //up资源的tab
      tabList3: [
        { name: '详情', tabIndex: 0 },
        { name: '评价', tabIndex: 1 },
      ], //pc云游戏的tab
      tabShowList: [], //tab显示列表
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
      },
      stickyOffsetTop: '0px', //顶部导航栏的高度
      navBgTransparent: true,
      isAll1: true, // 展开 -> 是否显示全部
      isAll2: true,
      isAll3: true,
      isAll4: true,
      recommendGameInfo: {}, // 相关游戏
      sameCpGameInfo: {}, // 同开发者其他游戏
      detail: {}, // 游戏详情信息
      extra_info: {}, // 游戏详情额外信息
      openList: [], // 开服列表
      coupon: {}, // 代金券信息
      isPlaying: false, //是否正在播放
      contentHeight1: 0, //展开收起内容高度1
      contentHeight2: 0, //展开收起内容高度2
      contentHeight3: 0, //展开收起内容高度3（游戏介绍）
      contentHeight4: 0, //展开收起内容高度3（温馨提示）
      empty: true, //是否支持下载
      loadSuccess: false, //加载完毕
      isIos, //是ios
      isIosBox, //是iosbox
      getUdidPopupShow: false, //是否显示UDID获取弹窗,
      downloadGrqDialogShow: false, //是否显示个人签下载弹窗
      grqStatus: 0, //签名状态
      downloadLoading: false, //下载loading
      grqLoading: false, //个人签loading
      isSafari: isSafari, //判断是否是safari浏览器
      needGuide: needGuide, //判断是否需要引导
      downloadPopupShow: false, //IOS下载弹窗
      permissionList: [], //游戏获取权限列表
      permissionShow: false, //游戏获取权限列表展示
      collected: 0, //是否已收藏
      hotCommentList: [], // 热门评论
      bannerFragment: [], //广告位
      cmtSum: '', // 评论数量
      vipHeight: 0, // vip表高度
      vipOpen: true, // vip表是否全部展开
      noGame: false, // 是否没有该游戏
      up_game_list: [], //该UP主的游戏列表
      notice_list: [], //公告列表
      topCommentList: [], // 置顶评论
      fanli_count: 0, // 返利数量
      rebate_activity: [], //返利活动
      head_text: {}, //开服提示
      kf_list: [], //开服列表
      rankingTypeList: [], //排名类型列表
      wx_popup: {
        show: false,
      },
      exclusive_benefits: [],
      card648: {},
      giftCopyPopupShow: false,

      // 充值18元平台币
      ptbTipsShow: false,
      ptbRechargeShow: false,
      selectedPayType: '',
      payWayList: [],
      ptbCount: 0,

      // 购买游戏
      buyGamePopupShow: false,
      buyInfo: {},
      selectedBuyGamePayType: 'ptb',
      buyGameSeccessShow: false,
      buyGameOrderInfo: {},
      swiperOptions: {
        slidesPerView: 'auto',
        spaceBetween: 10,
        observer: true,
        observeSlideChildren: true,
        observeParents: true,
        on: {
          // 过渡开始时触发
          transitionStart: function () {
            if (this.translate < 0) {
              _this.selectedModule = 'gallery';
              _this.currentHeight = 197;
            } else {
              _this.currentHeight = 197;
            }
          },
          // 过渡结束时触发
          transitionEnd: function () {
            if (this.translate == 0) {
              setTimeout(() => {
                _this.selectedModule = 'promo';
                _this.currentHeight = 197;
              }, 0);
            }
          },
        },
      },
      swiperBannerOptions: {
        slidesPerView: 1,
        centeredSlides: true,
        spaceBetween: 12,
        observer: true,
        observeSlideChildren: true,
      },
      finishedText: '',
      kaifuListShow: false, //开服动态弹窗
      welfareNoticeShow: false, //福利公告弹窗
      platformBenefitsShow: false, //平台福利弹窗
      platformActivityShow: false, //平台活动弹窗（代金券礼包返利整合）
      showBannerImage: false, //是否显示查看大图banner
      currentIndex: 0, //download-content
      currentHeight: 197, //宣传图高度
      currentHeightAtlas: 197, //图集高度
      selectedModule: 'promo', // 当前选中的模块
      remNumberLess,
      currentSlide: 0, //swiper_banner_images
      soonOpenList: [], //即将开服列表
      openOverList: [], //已开服列表
      update_content: '', //更新内容
      built_in_plugins: [], //内置插件
      promo_image: '',
      bannerImagesList: [],
      isSticky: false,
    };
  },
  computed: {
    ...mapGetters({
      dlConfig: 'system/dlConfig',
      kefuQQLink: 'system/kefuQQLink',
      initData: 'system/initData',
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
      durationOverShow: 'game/durationOverShow',
      taskListPopupShow: 'game/taskListPopupShow',
      hereItIosBoxTaskShow: 'game/hereItIosBoxTaskShow',
    }),
    _taskListPopupShow: {
      //是否显示任务弹窗
      get() {
        return this.taskListPopupShow;
      },
      set(value) {
        this.setTaskListPopupShow(value);
      },
    },
    _durationOverShow: {
      //是否显示时长不足弹窗
      get() {
        return this.durationOverShow;
      },
      set(value) {
        this.setDurationOverShow(value);
      },
    },
    shouldShow() {
      const num = Number(this?.cmtSum);
      return !Number.isInteger(num) || num > 3;
    },
    currentTabList() {
      let tabList = [];
      if (this.isPcCloudGame) {
        tabList = [...this.tabList3];
      } else if (
        this.detail.classid == 41 &&
        ![1, 2].includes(this.detail?.detailid)
      ) {
        tabList = [...this.tabList2];
      } else {
        tabList = [...this.tabList];
        if (!this.tabShowList.pay) {
          const payTabIndex = tabList.findIndex(tab => tab.tabIndex === 2);
          if (payTabIndex > -1) {
            tabList.splice(payTabIndex, 1)[0];
          }
        }
      }
      // 把评价选项移到最后
      const commentTabIndex = tabList.findIndex(tab => tab.tabIndex === 1);
      if (commentTabIndex > -1) {
        const commentTab = tabList.splice(commentTabIndex, 1)[0];
        tabList.push(commentTab);
      }
      return tabList;
    },
    // 是否出现下载按钮
    downloadButtonShow() {
      // h5渠道特殊判断 有h5_url就不显示下载按钮了
      if (parseInt(this.detail.is_h5_first) == 1) {
        if (this.detail.h5_url) {
          return false;
        }
      }

      // 判断是安卓和ios dl_config_i: 后台判断是否开启ios按钮，0隐藏，1正常显示，2敬请期待（暂不处理）
      // 是ios、后台勾选了ios、不是修复中（显示企业签） || 是ios、后台勾选了ios、修复中、勾选了个人签、且个人签状态不为0 （显示个人签）
      if (
        (this.isIos &&
          parseInt(this.detail.dl_config_i) !== 0 &&
          !this.detail.repair) ||
        (this.isIos &&
          parseInt(this.detail.dl_config_i) !== 0 &&
          this.detail.repair &&
          parseInt(this.detail.grq_status))
      ) {
        return true;
      } else if (!this.isIos && this.detail.down_a) {
        return true;
      } else {
        return false;
      }
    },
    // 是否有开始玩按钮
    showStartGame() {
      // h5渠道特殊判断 有h5_url就直接显示
      if (parseInt(this.detail.is_h5_first) == 1) {
        if (this.detail.h5_url) {
          return true;
        } else {
          return false;
        }
      }

      if (this.isIos) {
        // if (this.detail.h5_url && parseInt(this.detail.dl_config_i) === 0) {
        if (this.detail.h5_url) {
          return true;
        }
      } else {
        if (this.detail.h5_url) {
          return true;
        }
      }
      return false;
    },
    // 是否能玩游戏（有下载、开始玩、云游等）
    isAllNull() {
      if (this.isIos) {
        if (
          !this.showStartGame &&
          !this.cloudGameButtonShow &&
          !this.downloadButtonShow
        )
          return true;
      } else {
        if (!this.detail.h5_url && !this.detail.down_a && !this.detail.yyx_type)
          return true;
      }
      return false;
    },
    // 云游戏按钮是否显示
    cloudGameButtonShow() {
      // yyx_type.code为1、是IOS、企业签个人签全部都没有的情况下且后台开启了云游按钮
      return this.detail.yyx_type
        ? parseInt(this.detail.yyx_type.code) !== 0 &&
            this.isIos &&
            parseInt(this.detail.yyx_status) === 1 &&
            !this.downloadButtonShow
        : false;
    },
    // 个人签进度状态名
    grqStatusName() {
      switch (this.grqStatus) {
        case 0:
          return [this.$t('打包中'), this.$t('签名'), this.$t('下载')];
        case 1:
          return [this.$t('打包完成'), this.$t('签名中'), this.$t('下载')];
        case 2:
          return [this.$t('打包完成'), this.$t('签名成功'), this.$t('下载中')];
        case -1:
          return [this.$t('打包完成'), this.$t('签名失败'), this.$t('下载')];
        default:
          return [this.$t('打包完成'), this.$t('签名成功'), this.$t('下载中')];
      }
    },
    // 判断用户来源
    userAgent() {
      if (isIosBox) {
        return 2;
      } else if (isWebApp) {
        return 0;
      } else {
        return 1;
      }
    },
    // 相关游戏
    formatRecommendGameInfo() {
      let title = this.recommendGameInfo?.title;
      let arr = [];
      for (let i = 0; i < this.recommendGameInfo?.list?.length; i += 3) {
        arr.push(this.recommendGameInfo?.list?.slice(i, i + 3));
      }
      return { title, list: arr };
    },
    // 同开发者其他游戏
    // formatSameCpGameInfo() {
    //   let title = this.sameCpGameInfo?.title;
    //   let arr = [];
    //   for (let i = 0; i < this.sameCpGameInfo?.list?.length; i += 3) {
    //     arr.push(this.sameCpGameInfo?.list?.slice(i, i + 3));
    //   }
    //   return { title, list: arr };
    // },
    // 是否是折扣游戏
    haveDiscount() {
      return this.detail.classid != 107 ? false : true;
    },
    isShowComment() {
      return (
        this.card648.status ||
        this.extra_info.card_count > 0 ||
        parseInt(this.coupon.s_sum) + parseInt(this.coupon.sum) > 0
      );
    },
    //判断角标是否显示领礼包
    showGift() {
      if (this.card648.status || this.extra_info.card_count > 0) {
        return true;
      }
      //显示领代金券 或 默认false
      return false;
    },
    // 是否显示折扣tag
    discountTag() {
      if (this.detail.classid != 107) {
        return '';
      }
      if (this.detail.f_pay_rebate && this.detail.f_pay_rebate != 100) {
        return `${parseFloat(this.detail.f_pay_rebate) / 10}`;
      } else if (this.detail.pay_rebate && this.detail.pay_rebate != 100) {
        return `${parseFloat(this.detail.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
    // 是否pc云游戏
    isPcCloudGame() {
      // detail.detailid == 0|1|2  1是云玩 2是鲸云漫游 0其他游戏默认返回 2025年3月25日16:25:54 整合
      return this.detail?.detailid && [1, 2].includes(this.detail?.detailid);
    },
    // 计算nav_bar背景样式
    computedBgStyle() {
      if (!this.loadSuccess) {
        return 'transparent-white';
      }

      if (!this?.promo_image && !this.detail?.morepic?.big?.length) {
        return 'transparent';
      }
      return 'transparent-white';
    },
    // 折扣标签列表，过滤 rankingTypeList 中 tag 为 5 和 7 的项
    discountTagList() {
      const discountTags = this.rankingTypeList.filter(item => item.tag === 5);
      const otherTags = this.rankingTypeList.filter(item => item.tag === 7);
      return [...discountTags, ...otherTags];
    },
    // 部分马甲包渠道下载改成云玩
    downloadChangeCloudGame() {
      if (
        this.initData?.is_yw_cps &&
        this.detail?.is_yw_game &&
        (platform === 'ios' || platform === 'iosBox')
      ) {
        return true;
      } else {
        return false;
      }
    },
    // 判断部分马甲包渠道中当前游戏是否为安卓游戏
    isPartMJAndRroidGame() {
      if (
        this.initData?.is_yw_cps &&
        this.detail?.is_yw_game &&
        this.detail.platform.includes('安卓') &&
        (platform === 'ios' || platform === 'iosBox')
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    hereItIosBoxTaskShow(val) {
      if (val) {
        this.setTaskListPopupShow(true);
      }
    },
  },
  async created() {
    this.detail = this.$route.params.gameInfo || {};
    // this.$toast({
    //   type: "loading",
    //   duration: 0,
    //   message: "拼命加载中...",
    // });
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    await this.getDetail();
    if (this.$route.params.isShowFanli) {
      this.selectCurrent(2);
    }

    // 神策埋点
    this.$sensorsTrack('game_detail_show', {
      source_module: this.$sensorsModuleGet(),
      game_id: `${this.detail.id}`,
      adv_id: '暂无',
      game_name: this.detail.main_title,
      game_type: `${this.detail.classid}`,
      game_size: this.isIos ? this.detail.size_i_str : this.detail.size_a_str,
      $title: this.$sensorsPageGet(),
      referrer_page_name: this.$sensorsChainGet(),
      referrer_page_button: '暂无',
    });

    if (!this.noGame) {
      await this.getRecommendGame();
      await this.getCollectStatus();
      await this.getRebateActivity();
      // await this.getCommentList();
    }
    this.SET_USER_INFO();
  },
  beforeDestroy() {
    // window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    // ios马甲包部分渠道下载改成云玩（云挂机）
    goToCloudGame() {
      const deviceListShowItem = [];
      this.setReceiveData(this.detail);
      this.setCheckCloudDeviceItem(deviceListShowItem);
      this.$router.push({
        name: 'CloudHangupInterface',
        params: {
          receiveData: this.detail,
          checkCloudDeviceItem: deviceListShowItem,
        },
        query: { pageEntrySource: 1 }, // 添加来源标识 0云挂机 1云玩(详情页)
      });
    },
    back() {
      // 如果存在宣传图或图集 且当前tab==详情返回上一页 否则回到详情tab
      if (this.promo_image || this.detail?.morepic?.big?.length) {
        if (this.current == 0) {
          this.$router.back();
        } else {
          this.current = 0;
          window.scrollTo(0, 0);
          this.navBgTransparent = true;
          this.selectedModule = 'promo';
        }
        return false;
      }
      this.$router.back();
    },
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    handleCategoryTag(item) {
      this.toPage('CateGameList', {
        basis_id: 0,
        cate_id: item.id || 0,
        title: item.title || '',
      });
    },
    async remind(serverId, status) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        const res = await ApiServerRemind({
          serverId,
          status,
        });
        if (res.msg == '已取消提醒') {
          this.openList.forEach(item => {
            if (item.id == serverId) {
              item.status = 0;
            }
          });
        } else if ((res.msg = '已添加提醒')) {
          this.openList.forEach(item => {
            if (item.id == serverId) {
              item.status = 1;
            }
          });
        }
        if (res.code > 0) {
          this.$toast(res.msg);
        }
      } catch (e) {
        if (e.code == -16) {
          this.wx_popup.show = true;
        }
      }
    },
    // 跳转支付标签
    toPayCoinTag(item) {
      switch (item.type) {
        case 1: //小号交易
          this.handleDealDialog(item);
          break;
        case 2: //金币支付
          this.handleGoldPayDialog(item);
          break;
        case 3: //畅玩卡
          this.handleCwkDialog(item);
          break;
        default: //该游戏可使用
          if (!item.is_ok) {
            this.$dialog({
              message: '该游戏内不可使用' + item.title,
              confirmButtonColor: '#FE6600',
              confirmButtonText: this.$t('知道了'),
              width: '280',
              lockScroll: false,
            });
          } else {
            this.$dialog({
              message: '该游戏内可使用' + item.title,
              confirmButtonColor: '#FE6600',
              confirmButtonText: this.$t('知道了'),
              width: '280',
              lockScroll: false,
            });
          }
          break;
      }
    },
    // 选择当前活动
    selectCurrent(index) {
      this.currentIndex = index;
      this.platformActivityShow = true;
      if (index == 1 && !this.extra_info.card_count) {
        return false;
      } else if (
        index == 0 &&
        !(parseInt(this.coupon.s_sum) + parseInt(this.coupon.sum))
      ) {
        return false;
      } else if (index == 2 && !this.fanli_count) {
        return false;
      }
      this.currentIndex = index;
      this.platformActivityShow = true;
    },
    adjustHeight() {
      this.$nextTick(() => {
        const galleryImage = this.$refs.bannerSwiper.$el.querySelector(
          '.swiper-slide-active img',
        );
        if (galleryImage) {
          this.currentHeight = galleryImage.naturalHeight;
        }
      });
    },
    selectModule(module) {
      if (this.selectedModule === module) return;
      this.selectedModule = module;
      const swiper = this.$refs.bannerSwiper.$swiper;
      if (module === 'promo') {
        swiper.slideTo(0);
      } else {
        swiper.slideTo(1);
      }
    },

    onSlideBannerChange() {
      const swiper = this.$refs.bannerImagesSwiper.$swiper;
      this.currentSlide = swiper.activeIndex;

      let startX = 0; // 记录起始位置

      // 监听触摸开始事件
      swiper.on('touchStart', e => {
        if (e.touches && e.touches.length > 0) {
          startX = e.touches[0].clientX;
        }
      });

      // 监听触摸结束事件
      swiper.on('touchEnd', e => {
        if (e.changedTouches && e.changedTouches.length > 0) {
          const endX = e.changedTouches[0].clientX;
          const deltaX = endX - startX; // 计算滑动方向

          if (swiper.isEnd && deltaX < -50) {
            //最后一页 用户左滑
            this.$toast('已经浏览到底啦~');
          } else if (deltaX > 50) {
            // 右滑
          } else if (deltaX < -50) {
            // 左滑
          }
        }
      });
    },
    async takeGift648() {
      this.$toast.loading('加载中');
      if (!this.card648.cardpass) {
        const res = await ApiCardGet({
          cardId: this.card648.id,
          autoXh: 1,
        });
        this.card648.status = 1;
        this.card648 = { ...this.card648, ...res.data };
        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${this.detail.id}`,
          adv_id: '暂无',
          game_name: this.detail.main_title,
          game_type: `${this.detail.classid}`,
          game_size: '暂无',
          reward_type: '648礼包', // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });
      }
      this.$toast.clear();
      this.$nextTick(() => {
        this.giftCopyPopupShow = true;
      });
    },
    // 处理tab底下高亮标记位移
    handleLeftDistance(current) {
      let width = document.body.clientWidth;
      let length =
        this.detail.classid == 41 ? this.tabList2.length : this.tabList.length;
      let left = width / length / 2 - 6;
      let distance = current * (width / length);
      return `${left + distance}px`;
    },

    handleBenefitItem() {
      if (this.extra_info.card_count > 0) {
        this.selectCurrent(1);
        return;
      } else if (parseInt(this.coupon.s_sum) + parseInt(this.coupon.sum) > 0) {
        this.selectCurrent(0);
        return;
      } else {
        this.takeGift648();
      }
    },
    // 点击评论时先判断有没有评论的权限
    async clickComment() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      if (![1, 2].includes(parseInt(this.userInfo.auth_status))) {
        this.$toast.clear();
        this.$dialog({
          title: '提示',
          message: '评论失败：您还未完善实名认证信息！',
          confirmButtonText: '我知道了',
        });
        return;
      }
      try {
        const res = await ApiCommentClickComment({
          classId: 103,
          sourceId: this.detail.id,
        });

        if (res.data && res.data.length) {
          this.setCommentTypeList(res.data);
        }

        this.$toast.clear();
        this.toPage('CommentEditor', {
          source_id: this.detail.id,
          class_id: 103,
        });
      } catch (e) {
        if (e.code == 0) {
          // 不能评论，弹窗提示
          this.$toast.clear();
          this.$dialog({
            title: '提示',
            message: e.msg,
            confirmButtonText: '我知道了',
          });
        }
      }
    },
    // 获取热门评论
    async getCommentList() {
      const res = await ApiCommentComments({
        page: 1,
        listRows: 1,
        classId: 103,
        sourceId: this.detail.id,
        order: 0,
      });
      let { tops, hots, cmt_sum } = res.data;
      this.hotCommentList = [];
      if (tops && tops.length) {
        this.hotCommentList.push(...tops);
      }
      if (hots && hots.length) {
        this.hotList = hots;
        this.hotCommentList.push(...hots);
      }
      this.cmtSum = cmt_sum;
    },
    // 获取开服信息
    async getKaifuList() {
      this.$toast.loading({
        message: this.$t('加载中...'),
        duration: 0,
      });
      try {
        const res = await ApiServerIndex({
          gameId: this.detail.id,
        });
        if (res.data.length) {
          this.soonOpenList = res.data.filter(item => {
            return item.countdown_second > 0;
          });
          this.openOverList = res.data.filter(item => {
            return item.countdown_second < 0;
          });
        }
      } catch (error) {
      } finally {
        this.$toast.clear();
        this.kaifuListShow = true;
      }
    },
    // 获取返利活动
    async getRebateActivity() {
      const res = await ApiGetGameAct({
        id: this.detail.id,
      });
      this.rebate_activity = res.data.list;
    },
    // 打开活动详情
    toNewsDetail(item) {
      this.$router.push({
        name: 'Iframe',
        params: { url: item.titleurl, title: item.title },
      });
    },
    // 获取收藏状态
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 3,
        sourceId: this.detail.id,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    // 设置收藏
    setCollectStatus() {
      if (!this.loadSuccess) return false;
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;

        // 神策埋点
        this.$sensorsTrack('game_collect_click', {
          module: '暂无',
          game_id: `${this.detail.id}`,
          adv_id: '暂无',
          game_name: this.detail.main_title,
          game_type: `${this.detail.classid}`,
          game_size: this.isIos
            ? this.detail.size_i_str
            : this.detail.size_a_str,
        });
      }
      ApiResourceCollect({
        classId: 3,
        sourceId: this.detail.id,
        status: status,
      }).then(res => {
        this.collected = res.data.status;
      });
    },
    // 获取详情页信息
    async getDetail() {
      try {
        const res = await ApiGameDetail({
          id: this.$route.params.id,
        });
        this.noGame = false;
        this.detail = res.data.detail;
        this.extra_info = res.data.extra_info;
        this.tabShowList = res.data.tab;
        if (
          res.data.extra_info.toolbox[0] &&
          !this.tabList.find(item => item.name === '工具箱')
        ) {
          this.tabList.push({ name: '工具箱', tabIndex: 4 });
        }
        this.rankingTypeList = res.data.extra_info.special_tag || [];
        this.bannerFragment = res.data.extra_info.ad_info || [];
        this.coupon = res.data.extra_info.coupon_info || {};
        this.head_text = res.data.extra_info.head_text || {};
        this.topCommentList = res.data.extra_info.top_comment || [];
        this.openList = res.data.extra_info.server_info || [];
        this.notice_list = res.data.extra_info.fuli_notice || [];
        this.fanli_count = res.data.extra_info.fanli_count || 0;
        this.exclusive_benefits = res.data.extra_info.exclusive_benefits || [];
        this.update_content = res.data.detail.update_content || '';
        this.built_in_plugins = res.data.extra_info.built_in_plugins || [];
        // 宣传图优先级 video_thumb>pic>img
        this.promo_image =
          res.data.detail.video_thumb ||
          res.data.detail.pic ||
          res.data.detail.img ||
          '';
        this.bannerImagesList = [
          this.promo_image,
          ...res.data.detail.morepic.big,
        ];

        this.card648 = res.data.extra_info.card648_info;
        this.cmtSum = res.data.extra_info.comment_count || '';
        if (this.detail.classid == 41) {
          this.up_game_list = res.data.up_game_list || [];
        }
        this.setGameInfo(this.detail);
        this.loadSuccess = true;
        // 如果有视频就播放 ===> 暂时改成一进来不自动播放
        // if (this.detail.video_url) {
        //   this.$nextTick(() => {
        //     this.handlePlay();
        //   });
        // }
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'GameNotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
          this.noGame = true;
        }
      } finally {
        this.$nextTick(() => {
          this.$toast.clear();
          if (!this.contentHeight1 && this.$refs.content1) {
            this.contentHeight1 = this.$refs.content1.clientHeight;
          }
          if (!this.contentHeight2 && this.$refs.content2) {
            this.contentHeight2 = this.$refs.content2.clientHeight;
          }
          if (!this.contentHeight3 && this.$refs.content3) {
            this.contentHeight3 = this.$refs.content3.clientHeight;
          }
          if (!this.contentHeight4 && this.$refs.content4) {
            this.contentHeight4 = this.$refs.content4.clientHeight;
          }

          if (this.contentHeight1 > 120) {
            this.isAll1 = false;
          }
          if (this.contentHeight2 > 100) {
            this.isAll2 = false;
          }
          if (this.contentHeight3 > 156) {
            this.isAll3 = false;
          }
          if (this.contentHeight4 > 100) {
            this.isAll4 = false;
          }

          if (this.$refs.vipTable) {
            this.vipHeight = this.$refs.vipTable.clientHeight;
            if (this.vipHeight > 250) {
              this.vipOpen = false;
            }
          }
        });
      }
    },
    // 获取推荐游戏
    async getRecommendGame() {
      const res = await ApiGameDetailRecommendGame({
        id: this.detail.id,
      });
      this.recommendGameInfo = res.data.recommend_game;
      // this.sameCpGameInfo = res.data.same_cp_game;
    },
    async getBuyGameInfo() {
      const res = await ApiGameBuyGameInfo({ game_id: this.$route.params.id });
      this.buyInfo = res.data;
    },
    // updateCommentSum(sum) {
    //   this.cmtSum = sum;
    // },
    // 处理视频播放
    handlePlay() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
    // 处理视频暂停
    handlePause() {
      this.$refs.videoPlayer.pause();
      this.isPlaying = false;
    },
    // 点击充值返利申请
    welfareApply() {
      this.setRebateInit(this.detail);
      this.$router.push({
        name: 'RebateFirst',
        params: { game_id: this.detail.id },
      });
    },
    // 点击切换tab
    clickTab(index) {
      if (!this.loadSuccess || this.current === index) return false;
      window.scrollTo(0, 0);
      if (index == 0) {
        this.navBgTransparent = true;
      }
      this.$nextTick(() => {
        setTimeout(() => {
          this.current = index;
        }, 100);
      });
      this.selectedModule = 'promo';
      // if (index === 3 && !this.openList.length) {
      //   ApiServerIndex({
      //     gameId: this.$route.params.id,
      //   }).then((res) => {
      //     this.openList = res.data;
      //   });
      // }
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (
        this.current !== 0 &&
        (this.promo_image || this.detail?.morepic?.big.length)
      ) {
        this.navBgTransparent = false;
        return;
      }
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 截图查看大图
    showBigImage(index) {
      this.currentSlide = index;
      this.showBannerImage = true;
      this.$nextTick(() => {
        const swiper = this.$refs.bannerImagesSwiper.$swiper;
        swiper.slideTo(index, 0);
      });
      // ImagePreview({
      //   images: list,
      //   startPosition: index,
      //   lockScroll: false,
      // });
    },
    // 开始游戏
    startGame() {
      startH5Game(this.detail.h5_url, this.detail.app_id);
    },
    // h5游戏下载 2022年9月29日14:03:05
    async downloadH5Game() {
      // 神策埋点
      this.$sensorsTrack('game_download', {
        source_page: this.$sensorsPageGet(),
        module: '暂无',
        game_id: `${this.detail.id}`,
        adv_id: '暂无',
        game_name: this.detail.main_title,
        game_type: `${this.detail.classid}`,
        game_size: this.isIos ? this.detail.size_i_str : this.detail.size_a_str,
      });

      // 下载上报
      await downloadAdd({
        gameId: this.detail.id,
        classId: this.detail.classid,
      });

      downloadH5Game(this.detail.app_id);
    },
    // 下载按钮防沉迷check
    async checkDownload() {
      const res = await ApiGameCheckDown();
      if (res.code > 0) {
        return true;
      }
      return false;
    },
    // 下载按钮处理
    async handleDownload() {
      // 判断webapp未添加桌面版时，点击下载均弹出添加桌面引导弹窗
      if (this.isIos && needGuide) {
        document.querySelector('.fix-button.yindao-button').click();
        return false;
      }

      // loading动画
      this.downloadLoading = true;
      try {
        const checkRes = await this.checkDownload();
        if (!checkRes) {
          this.downloadLoading = false;
          return false;
        }
      } catch (e) {
        this.downloadLoading = false;
        return false;
      }

      // 特定渠道需用户先充值18元平台币后才可进行下载
      if (this.isIos) {
        try {
          const res = await ApiGameWebDownSignVerify();
          const { is_ok, payWayList, ptbCount } = res.data;
          if (!is_ok) {
            this.payWayList = payWayList;
            this.selectedPayType = this.payWayList[0].key;
            this.ptbCount = ptbCount;
            this.downloadLoading = false;
            this.ptbTipsShow = true;
            return false;
          }
        } catch (e) {
          this.downloadLoading = false;
          return false;
        }
      }

      if (this.detail.buy_info?.status == 1) {
        await this.getBuyGameInfo();
        this.selectedBuyGamePayType = 'ptb';
        this.downloadLoading = false;
        this.buyGamePopupShow = true;
        return false;
      }

      // 神策埋点
      this.$sensorsTrack('game_download', {
        source_page: this.$sensorsPageGet(),
        module: '暂无',
        game_id: `${this.detail.id}`,
        adv_id: '暂无',
        game_name: this.detail.main_title,
        game_type: `${this.detail.classid}`,
        game_size: this.isIos ? this.detail.size_i_str : this.detail.size_a_str,
      });

      // 下载上报
      downloadAdd({
        gameId: this.detail.id,
        classId: this.detail.classid,
      });

      setTimeout(() => {
        this.downloadLoading = false;
      }, 2000);
      // 安卓下载
      if (!this.isIos) {
        if (isAndroidBox) {
          // 安卓马甲包
          window.BOX.startDownload(JSON.stringify(this.detail));
          setTimeout(() => {
            BOX_showActivity({ isAndroidBoxToNative: true }, { page: 'yygl' });
          }, 200);
        } else {
          window.location.href = this.detail.down_a;
        }
      } else {
        // 判断登录
        if (this.userInfo.token) {
          // 如果是个人签
          if (this.detail.repair) {
            // 走我们自己的下载方法
            if (parseInt(this.detail.grq_status) === 1) {
              ApiPersonalSign({
                game_id: this.detail.id,
                from_plat: this.userAgent,
                packageName: this.userAgent === 2 ? BOX_getPackageName() : '',
              }).then(res => {
                switch (parseInt(res.data.status)) {
                  // 不是svip
                  case 2:
                    this.toPage('Svip');
                    break;
                  // 没有绑定udid
                  case 3:
                    this.getUdidPopupShow = true;
                    // 能走到这里的只有ios的马甲包和webapp
                    setTimeout(() => {
                      let { jump_url, mobileconfig_url, mobileprovision_url } =
                        res.data;
                      if (isIosBox) {
                        // 下载配置文件,双url才能跳转到配置文件
                        BOX_openInBrowser({
                          h5_url: `${jump_url}?url1=${mobileconfig_url}&url2=${mobileprovision_url}`,
                        });
                      } else {
                        window.location.href = mobileconfig_url;
                        setTimeout(() => {
                          // 非套壳情况需要第二遍让其自动跳转
                          window.location.href = mobileprovision_url;
                        }, 1000);
                      }
                    }, 200);
                    break;
                  // 已经签完
                  case 5:
                    this.downloadPopupShow = true;
                    // 下载游戏
                    BOX_openInBrowser({ h5_url: res.data.grq_down_ip });
                    break;
                  default:
                    this.getUdidPopupShow = false;
                    this.downloadGrqDialogShow = true;
                    this.handleGrqDownload();
                }
              });
            }
            // 走纸片内测
            else if (parseInt(this.detail.grq_status) === 2) {
              if (this.downloadChangeCloudGame) {
                this.goToCloudGame();
              } else {
                ApiUserCheckGrqAuthStatus({
                  grq_status: this.detail.grq_status,
                  game_id: this.detail.id,
                }).then(res => {
                  // 第三方个人签，纸片内测
                  BOX_openInBrowser({ h5_url: res.data.zp_grq_url });
                });
              }
            }
          }
          // 如果是企业签
          else {
            this.downloadPopupShow = true;
            // 下载游戏
            BOX_openInBrowser({ h5_url: this.detail.down_ip });
          }
        }
        // 如果没登录
        else {
          this.toPage('PhoneLogin');
        }
      }
    },
    // 处理开服表的字符串
    handleTimestamp(ts) {
      let temp = new Date(ts * 1000),
        m = temp.getMonth() + 1,
        d = temp.getDate(),
        h = temp.getHours(),
        min = temp.getMinutes();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      h = h < 10 ? '0' + h : h;
      min = min < 10 ? '0' + min : min;
      return {
        date: `${m}-${d}`,
        time: `${h}:${min}`,
      };
    },
    // 检查udid是否获取
    async checkUdid() {
      this.grqLoading = true;
      try {
        await ApiCheckUdid({ game_id: this.detail.id });
      } finally {
        this.grqLoading = false;
      }
      this.getUdidPopupShow = false;
      this.downloadGrqDialogShow = true;
      this.handleGrqDownload();
    },
    // 个人签弹窗处理
    async handleGrqDownload() {
      // 循环调用接口，更改各个状态
      const res = await ApigrqGameList({ game_id: this.detail.id });
      switch (parseInt(res.data.list[0].grq_status)) {
        case 0:
          this.grqStatus = 0;
          setTimeout(this.handleGrqDownload, 4000);
          break;
        case 1:
          this.grqStatus = 1;
          setTimeout(this.handleGrqDownload, 4000);
          break;
        case 2:
          this.downloadGrqDialogShow = false;
          // 下载游戏
          BOX_openInBrowser({ h5_url: res.data.list[0].grq_down_ip });
          this.grqStatus = 2;
          break;
        case 3:
        case 4:
          this.downloadGrqDialogShow = false;
          this.$toast(this.$t('签名失败'));
          this.grqStatus = -1;
          break;
      }
    },
    // copy
    copy() {
      this.$copyText(window.location.href).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    // 开始云游戏
    cloudGame() {
      startCloudGame(this.detail.id);
    },
    // 处理权限隐私说明
    handleUrl(val) {
      if (val.url && val.text == '隐私政策') {
        this.toPage('Iframe', {
          url: val.url,
          title: '隐私政策',
        });
      } else if (val.text == '应用权限') {
        this.handlePermission();
      } else {
        this.$toast('开发者正在努力完善中...');
      }
    },
    // 处理游戏权限详情
    async handlePermission() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
    // 点击是否可交易
    handleDealDialog(item) {
      if (item.is_ok) {
        // 可出售
        this.$dialog({
          message: this.$t('该游戏支持小号出售服务'),
          showCancelButton: true,
          cancelButtonText: this.$t('知道了'),
          width: '280',
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('去查看'),
          lockScroll: false,
        }).then(res => {
          if (res == 'confirm') {
            this.toPage('Deal');
          }
        });
      } else {
        this.$dialog({
          message: this.$t('该游戏支暂不持小号出售服务'),
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('知道了'),
          width: '280',
          lockScroll: false,
        });
      }
    },
    // 点击是否可使用畅玩卡
    handleCwkDialog(item) {
      if (item.is_ok) {
        // 可使用畅玩卡
        this.$dialog({
          message: this.$t('该游戏支持使用畅玩卡，\n6元代金券天天送！'),
          showCancelButton: true,
          cancelButtonText: this.$t('知道了'),
          width: '280',
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('去查看'),
          lockScroll: false,
        }).then(res => {
          if (res == 'confirm') {
            this.toPage('ChangwanCard');
          }
        });
      } else {
        this.$dialog({
          message: this.$t('该游戏暂不支持使用畅玩卡'),
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('知道了'),
          width: '280',
          lockScroll: false,
        });
      }
    },
    // 点击是否可使用金币抵扣
    handleGoldPayDialog(item) {
      if (item.is_ok) {
        // 可使用金币抵扣
        this.$dialog({
          message: this.$t('该游戏内可使用金币抵扣'),
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('知道了'),
          width: '280',
          lockScroll: false,
        });
      } else {
        this.$dialog({
          message: this.$t('该游戏内不可使用金币抵扣'),
          confirmButtonColor: '#FE6600',
          confirmButtonText: this.$t('知道了'),
          width: '280',
          lockScroll: false,
        });
      }
    },
    // 预约
    async handleSubscribe() {
      let status = Number(this.detail.subscribed) == 0 ? 1 : -1;
      try {
        const res = await ApiGameSubscribe({ gameId: this.detail.id, status });
        if (res.code > 0) {
          this.detail.subscribed = status == 1 ? 1 : 0;
          this.$toast(res.msg);
        }
      } catch (e) {}
    },
    toNoticeDetail(item) {
      jumpAnnouncement(item);
    },
    clickBenefitItem(item) {
      switch (item.action_code) {
        case 13: // web内页
          handleActionCode(item);
          break;
        case 23: // 游戏签到
          this.toPage('GameSignInDetail', { id: this.detail.id });
          break;
        case 21: // 开局号
          this.toPage('OpeningAccount', { id: this.detail.id });
          break;
      }
    },

    openPtbRecharge() {
      this.ptbTipsShow = false;
      this.ptbRechargeShow = true;
    },
    handlePtbRecharge() {
      this.ptbRechargeShow = false;
      this.$toast.loading('加载中');
      ApiCreateOrderPtb({
        isNew: 1,
        isWebDown: 1,
        money: this.ptbCount / 10,
        payWay: this.selectedPayType,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {})
            .catch(() => {});
        });
      });
    },

    // 购买游戏
    async handleBuyGame() {
      this.buyGamePopupShow = false;
      ApiGameCreateBuyGameOrder({
        game_id: this.detail.id,
        payWay: this.selectedBuyGamePayType,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: orderRes.data.orderType,
          payWay: this.selectedBuyGamePayType,
          packageName: '',
        })
          .then(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: orderRes.data.orderType,
            }).then(res2 => {
              if (res2.code == 1) {
                this.buyGameOrderInfo = orderRes.data.orderInfo;
                this.buyGameSeccessShow = true;
              }
            });
          })
          .catch(e => {
            setTimeout(() => {
              this.$toast(e.msg);
            }, 30);
          });
      });
    },
    async refreshDetail() {
      this.$toast.loading('加载中');
      await this.getDetail();
      this.$toast.clear();
    },
    async closeBuyGameSuccessPopup() {
      this.buyGameSeccessShow = false;
      await this.refreshDetail();
    },
    // 云玩
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    // 处理 sticky 滚动事件
    handleStickyScroll(params) {
      this.isSticky = params.isFixed;
    },
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
      setRebateInit: 'rebate/setInit',
      setPcCloudGameInitLoadingEmpty: 'game/setPcCloudGameInitLoadingEmpty',
      setDurationOverShow: 'game/setDurationOverShow',
      setTaskListPopupShow: 'game/setTaskListPopupShow',
      setCommentTypeList: 'comment/setCommentTypeList',
      setReceiveData: 'cloud_hangup/setReceiveData',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
    }),
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-sticky--fixed {
  .fixed-center;
  width: 100%;
}
/deep/ .van-nav-bar__left,
/deep/ .van-nav-bar__right {
  padding: 0;
}
/deep/.van-nav-bar__left::after {
  border: none;
}
/deep/.van-nav-bar__left:active {
  opacity: 1;
}
.game-detail-page {
  .back {
    width: 30 * @rem;
    height: 50 * @rem;
    padding: 0 4 * @rem;
    background: url(~@/assets/images/nav-bar-back-white.png) center center
      no-repeat;
    background-size: 10 * @rem 18 * @rem;
    &.back-black {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(~@/assets/images/nav-bar-back-black.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
  }

  .nav-tabs {
    display: flex;
    align-items: center;
    .nav-tab {
      &:first-child {
        margin-left: 4 * @rem;
        margin-right: 9 * @rem;
      }
      &:not(:first-child) {
        margin-left: 9 * @rem;
        margin-right: 9 * @rem;
      }
      flex: 1;
      white-space: nowrap;
      span {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        font-weight: 400;
        font-size: 15 * @rem;
        color: rgba(255, 255, 255, 0.5);
        .comment_count {
          margin-left: 2 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: rgba(190, 194, 197, 0.8);
        }
        .comment_count_gray {
          color: #93999f;
        }
      }
      &.active {
        span {
          font-weight: bold;
          font-size: 18 * @rem;
          color: #ffffff;
        }
      }
    }
    &.navBgTransparent {
      .nav-tab {
        span {
          color: #000;
        }
      }
    }
  }
  .collect-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    padding: 10 * @rem;
    background: url(~@/assets/images/games/collect-black.png) center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    &.collect-btn-white {
      background: url(~@/assets/images/games/collect.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.collect-btn-black {
      background: url(~@/assets/images/games/collect-black.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.had {
      background-image: url(~@/assets/images/games/collect-success.png);
      background-size: 28 * @rem 28 * @rem;
    }
  }
  .section {
    padding: 20 * @rem 12 * @rem 0;
    .section-title {
      margin-bottom: 10 * @rem;
    }
    .title-text {
      font-size: 16 * @rem;
      color: #191b1f;
      font-weight: 600;
      display: flex;
      height: 20 * @rem;
      line-height: 20 * @rem;
      align-items: center;
      .section-arrow {
        margin-left: 4 * @rem;
        width: 8 * @rem;
        height: 12 * @rem;
      }
    }
  }
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-icon {
      width: 22 * @rem;
      height: 22 * @rem;
      .image-bg('~@/assets/images/games/sun-icon.png');
      margin-right: 5 * @rem;
      &.icon-jieshao {
        .image-bg('~@/assets/images/games/icon-jieshao.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-xinxi {
        .image-bg('~@/assets/images/games/icon-xinxi.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-jiagebiao {
        .image-bg('~@/assets/images/games/icon-jiagebiao.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-pinglun {
        .image-bg('~@/assets/images/games/icon-pinglun.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-fuli {
        .image-bg('~@/assets/images/games/icon-fuli.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
      &.icon-xiangguan {
        .image-bg('~@/assets/images/games/icon-xiangguan.png');
        background-position: center center;
        background-size: 18 * @rem 18 * @rem;
      }
    }
    .title-text {
      font-size: 16 * @rem;
      color: #191b1f;
      font-weight: 600;
      display: flex;
      height: 20 * @rem;
      line-height: 20 * @rem;
      align-items: center;
      .number {
        font-weight: 600;
        font-size: 13 * @rem;
        color: #303236;
      }
    }
    .title-right {
      display: flex;
      align-items: center;
      span {
        font-size: 15 * @rem;
        color: @themeColor;
      }
      .title-right-icon {
        width: 10 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/games/right-arrow.png) center center
          no-repeat;
        background-size: 10 * @rem 10 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
  .no-banner {
    width: 100%;
    height: 55 * @rem;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    background: @themeBg;
    text-align: center;
    line-height: 55 * @rem;
    font-size: 18 * @rem;
    color: #fff;
  }
  .top-banner {
    // flex-shrink: 0;
    width: 100%;
    height: calc(301 * @rem + @safeAreaTop);
    height: calc(301 * @rem + @safeAreaTopEnv);
    transition: height 0.3s ease;
    position: relative;
    // z-index: 1;
    &.top-banner-atlas {
      height: calc(301 * @rem + @safeAreaTop);
      height: calc(301 * @rem + @safeAreaTopEnv);
    }
    .game-cover {
      width: 100%;
      height: calc(301 * @rem + @safeAreaTop);
      height: calc(301 * @rem + @safeAreaTopEnv);
      overflow: hidden;
      position: relative;
      &.detail-banner-bg {
        background: rgba(0, 0, 0, 0.6);
      }
      .banner-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        &.f-blur {
          filter: blur(60px);
        }
      }
      .banner-swiper {
        padding-top: calc(@safeAreaTop + 4 * @rem);
        padding-top: calc(@safeAreaTopEnv + 4 * @rem);
        .swiper-slide-h {
          margin-top: 50 * @rem;

          &:first-child {
            width: 351 * @rem;
            margin-left: 10 * @rem;
            border-radius: 12 * @rem;
            img {
              width: 100%;
            }
          }
          // &:not(:first-child) {
          //   .slide-img {
          //     margin-left: 10 * @rem;
          //   }
          // }
          .slide-img {
            width: auto;
            height: 100%;
            // max-width: 100vw;
            object-fit: fill;
          }
          &.video-container {
            width: 267 * @rem;
            height: 150 * @rem;
            overflow: hidden;
            #video {
              display: block;
              width: 267 * @rem;
              height: 150 * @rem;
              outline: none;
              border: none;
            }
            .mask {
              width: 100%;
              height: 150 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              left: 0;
              top: 0;
              .fixed-center;
              background-color: rgba(0, 0, 0, 0);
              z-index: 10;
              .play-btn {
                width: 38 * @rem;
                height: 38 * @rem;
                background: url(~@/assets/images/video-play.png) no-repeat;
                background-size: 38 * @rem 38 * @rem;
              }
            }
          }
        }
        /deep/ .swiper-slide {
          width: auto;
          border-radius: 8 * @rem;
          overflow: hidden;
          // &:not(:first-child) {
          //   margin-right: 10 * @rem;
          // }
          // &:last-child {
          //   margin-right: 0 * @rem;
          // }
          // .slide-img {
          //   margin-left: 10 * @rem;
          // }
          &.video-container {
            width: 267 * @rem;
            height: 150 * @rem;
            overflow: hidden;
            #video {
              display: block;
              width: 267 * @rem;
              height: 150 * @rem;
              outline: none;
              border: none;
            }
            .mask {
              width: 100%;
              height: 150 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              left: 0;
              top: 0;
              .fixed-center;
              background-color: rgba(0, 0, 0, 0);
              z-index: 10;
              .play-btn {
                width: 38 * @rem;
                height: 38 * @rem;
                background: url(~@/assets/images/video-play.png) no-repeat;
                background-size: 38 * @rem 38 * @rem;
              }
            }
          }
        }
      }
    }
    .banner-bar {
      box-sizing: border-box;
      // padding: 8 * @rem 8 * @rem;
      width: 100%;
      position: absolute;
      bottom: 22 * @rem;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9;
      .bar-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .tab-bar {
          display: flex;
          align-items: center;
          justify-content: center;
          .tab-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 84 * @rem;
            height: 20 * @rem;
            line-height: 20 * @rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20 * @rem;
            box-sizing: border-box;
            .tab-item {
              height: 20 * @rem;
              line-height: 20 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              color: rgba(255, 255, 255, 0.5);
              font-size: 9 * @rem;
              font-weight: 500;
              white-space: nowrap;
              &.active {
                box-sizing: border-box;
                padding: 0 8 * @rem;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 20 * @rem;
                color: #fff;
              }
              &:first-of-type {
                width: 44 * @rem;
              }
              &:last-of-type {
                width: 36 * @rem;
              }
            }
          }
        }
      }
    }
    &.not-banner {
      height: 102 * @rem !important;
      background: url('~@/assets/images/games/detail_not_banner.png') no-repeat
        center center;
      background-size: 100% 100%;
    }
  }

  .game-info {
    z-index: 9;
    position: relative;
    top: -14 * @rem;
    border-radius: 12 * @rem 12 * @rem 0 0;
    background: #ffffff;
    padding: 14 * @rem 12 * @rem 0;
    &.not-game-info {
      padding: 0 12 * @rem;
      top: 0;
    }
    &.not-morepic-info {
      top: -36 * @rem;
    }
    .game-top-info {
      margin-bottom: 7 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        flex-shrink: 0;
        .game-img {
          flex-shrink: 0;
          width: 72 * @rem;
          height: 72 * @rem;
          background: #d9d9d9;
          overflow: hidden;
          border-radius: 16 * @rem;
        }
        .game-msg {
          flex: 1;
          margin-left: 10 * @rem;
          width: 190 * @rem;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .game-name {
            display: flex;
            align-items: center;
            .title {
              font-size: 16 * @rem;
              color: #191b1f;
              font-weight: bold;
              white-space: nowrap;
            }
            .sub-title {
              white-space: nowrap;
              border-radius: 4 * @rem;
              margin-left: 6 * @rem;
              border: 1 * @rem solid #e3e5e8;
              color: #93999f;
              font-size: 11 * @rem;
              font-weight: 400;
              padding: 2 * @rem 4 * @rem;
              box-sizing: border-box;
            }
          }
          .game-version {
            margin-top: 8 * @rem;
            display: flex;
            align-items: center;
            .version {
              max-width: 100 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .version,
            .volume {
              font-weight: 400;
              font-size: 11 * @rem;
              color: #93999f;
              white-space: nowrap;
            }
            .volume {
              margin-left: 14 * @rem;
            }
          }
          .game-new-time {
            width: 190 * @rem;
            margin-top: 8 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #1cce94;
            display: flex;
            align-items: center;
            .time-box {
              width: 190 * @rem;
              display: flex;
              align-items: center;
              overflow: hidden;
              text-overflow: ellipsis;
              span {
                &:first-of-type {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                > img {
                  flex-shrink: 0;
                  margin-left: 4 * @rem;
                  width: 6 * @rem;
                  height: 8 * @rem;
                }
              }
            }
            .msg-info {
              color: #93999f;
            }
          }
          .tag-list {
            display: flex;
            align-items: center;

            .tags {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              overflow: hidden;
              margin-top: 10 * @rem;
              height: 17 * @rem;
              line-height: 17 * @rem;
              .tag {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                white-space: nowrap;
                border-radius: 4 * @rem;
                padding: 2 * @rem 4 * @rem;
                font-weight: 400;
                font-size: 10 * @rem;
                color: rgb(134, 134, 134);
                background: rgba(0, 0, 0, 0.05);
                height: 17 * @rem;
                line-height: 17 * @rem;
                border: 1 * @rem solid rgba(255, 255, 255, 0.5);
                box-sizing: border-box;
                &:not(:first-child) {
                  margin-left: 8 * @rem;
                }
              }
            }
          }
          .share-box {
            margin-top: 8 * @rem;
            display: flex;
            align-items: center;
            .text2,
            .text3 {
              font-weight: 400;
              font-size: 11 * @rem;
              color: #93999f;
            }
            .up-icon {
              width: 14 * @rem;
              height: 14 * @rem;
              border-radius: 50%;
              padding: 0 7 * @rem;
              overflow: hidden;
            }
          }
          .jy_app {
            margin-top: 8 * @rem;
            width: 200 * @rem;
            height: 17 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #93999f;
            line-height: 14 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .right-info {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .score {
          height: 28 * @rem;
          font-weight: 800;
          font-weight: bolder;
          font-size: 28 * @rem;
          color: #1cce94;
          line-height: 28 * @rem;
        }
        .number {
          margin-top: 4 * @rem;
          height: 10 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #93999f;
          line-height: 10 * @rem;
        }
      }
    }
    .welfare-box {
      background: #fff7f0;
      background-size: 351 * @rem 32 * @rem;
      width: 351 * @rem;
      height: 32 * @rem;
      line-height: 32 * @rem;
      border-radius: 0 0 8 * @rem 8 * @rem;
      position: relative;
      &.not-648 {
        border-radius: 8 * @rem;
      }
      &.not-img {
        margin-bottom: 10 * @rem;
      }
      .welfare-box-list {
        display: flex;
        align-items: center;
        overflow-x: auto;
        -webkit-mask: linear-gradient(
          to right,
          #fff calc(100% - 30 * @rem),
          transparent
        );
        &::-webkit-scrollbar {
          display: none;
        }
        .welfare-item {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #30343b;
          position: relative;
          display: flex;
          align-items: center;
          &:not(:first-of-type) {
            margin-left: 13 * @rem;
          }
          &:first-of-type {
            margin-left: 15 * @rem;
          }
          &:last-of-type {
            margin-right: 13 * @rem;
          }
          img {
            width: 10 * @rem;
            height: 10 * @rem;
          }
          > span {
            margin-left: 3 * @rem;
            text-decoration: line-through;
            white-space: nowrap;
            &.on {
              text-decoration: none;
            }
          }
        }
      }
    }
    .game-tags {
      height: 23 * @rem;
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      margin-top: 14 * @rem;
      margin-bottom: 10 * @rem;
      box-sizing: border-box;
      &::-webkit-scrollbar {
        display: none !important;
      }
      .tag {
        border-radius: 5 * @rem;
        color: #93999f;
        font-size: 10 * @rem;
        font-weight: 400;
        padding: 0 5 * @rem;
        display: flex;
        align-items: center;
        height: 22 * @rem;
        margin-right: 6 * @rem;
        border: 1 * @rem solid #e3e5e8;
        box-sizing: border-box;
        .icon {
          width: 14 * @rem;
          height: 14 * @rem;
          margin-right: 2 * @rem;
        }
      }
    }
    .activity-bar {
      margin: 10 * @rem 0;
      width: 351 * @rem;
      height: 36 * @rem;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 11 * @rem 0 8 * @rem;
      box-sizing: border-box;
      background: url('~@/assets/images/games/activity-bg.png') #fff5db
        no-repeat top center;
      background-size: 351 * @rem 36 * @rem;
      .left-info {
        display: flex;
        align-items: center;
        .icon {
          width: 28 * @rem;
          height: 28 * @rem;
        }
        .title {
          margin-left: 6 * @rem;
          white-space: nowrap;
          font-weight: 500;
          font-size: 11 * @rem;
          color: #6c4537;
        }
      }
      .right-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          width: 8 * @rem;
          height: 12 * @rem;
          background: url('~@/assets/images/games/activity-btn.png') no-repeat
            center center;
          background-size: 8 * @rem 12 * @rem;
        }
      }
    }
    .hot-bar {
      display: flex;
      align-items: center;
      padding: 10 * @rem 0;
      margin-top: 4 * @rem;
      .hot-item {
        display: flex;
        align-items: center;
        &:not(:first-of-type) {
          margin-left: 40 * @rem;
        }
        .hot-title {
          font-size: 15 * @rem;
          color: #000000;
          font-weight: 600;
        }
        .hot-num {
          font-size: 15 * @rem;
          color: @themeColor;
          font-weight: 600;
        }
        .hot-stars {
          width: 65 * @rem;
          height: 13 * @rem;
          background: url(~@/assets/images/games/star.png) left center repeat;
          background-size: 13 * @rem 13 * @rem;
          margin-left: 5 * @rem;
          .stars-current {
            width: 0%;
            height: 13 * @rem;
            background: url(~@/assets/images/games/star-on.png) left center
              repeat;
            background-size: 13 * @rem 13 * @rem;
          }
        }
      }
      .flex1 {
        flex: 1;
        min-width: 0;
      }
      .discount-info {
        display: flex;
        align-items: center;
        height: 22 * @rem;
        border-radius: 11 * @rem;
        overflow: hidden;
        .pay-rebate {
          padding: 0 8 * @rem;
          font-size: 11 * @rem;
          color: #ab6464;
          background: #fbf2f1;
          height: 100%;
          line-height: 22 * @rem;
          &.f-pay-rebate {
            background-color: #ff581b;
            color: #ffffff;
          }
        }
      }
    }
    .other-navs {
      display: flex;
      justify-content: space-between;
      padding: 7 * @rem 0 5 * @rem;
      .other-nav {
        box-sizing: border-box;
        background-color: #f6f6f6;
        border-radius: 6 * @rem;
        height: 60 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 0 10 * @rem;
        position: relative;
        &:not(:first-of-type) {
          margin-left: 10 * @rem;
        }

        .title {
          display: flex;
          align-items: center;
          .icon {
            width: 17 * @rem;
            height: 17 * @rem;
            background: url(~@/assets/images/games/news-icon-2.png) center
              center no-repeat;
            background-size: 17 * @rem 17 * @rem;
          }
          .title-text {
            margin-left: 4 * @rem;
            font-size: 13 * @rem;
            color: #000000;
            font-weight: 600;
            display: flex;
            align-items: center;
            i {
              width: 6 * @rem;
              height: 10 * @rem;
              .image-bg('~@/assets/images/games/arrow-right-black.png');
              margin-left: 5 * @rem;
            }
          }
        }
        .desc {
          font-size: 13 * @rem;
          color: #797979;
          margin-top: 5 * @rem;
          line-height: 18 * @rem;
          white-space: nowrap;
          &.small {
            font-size: 12 * @rem;
          }
        }
        .notice {
          position: absolute;
          right: 0;
          top: -7 * @rem;
          height: 14 * @rem;
          line-height: 14 * @rem;
          background: #ff504f;
          color: #ffffff;
          font-size: 10 * @rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 6 * @rem;
          border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
        }
      }
      .news {
        width: 135 * @rem;
        background-color: #fbf2f1;
        .title {
          .icon {
            background-image: url(~@/assets/images/games/news-icon-2.png);
          }
        }
      }
      .gift-648 {
        width: 135 * @rem;
        background-color: #fbf2f1;
        position: relative;
        overflow: hidden;
        .title {
          .icon {
            background-image: url(~@/assets/images/games/gift-648-icon.png);
            background-size: 20 * @rem 20 * @rem;
          }
        }
        .desc {
          color: @themeColor;
        }
        .gift-648-btn {
          height: 60 * @rem;
          width: 20 * @rem;
          position: absolute;
          right: 0;
          top: 0;
          background-color: #ff6a2b;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12 * @rem;
          color: #ffffff;
          text-align: center;
          &.had {
            background-color: #778dfb;
          }
        }
      }
      .quan {
        flex: 1;
        min-width: 0;
        background-color: #f1f4fb;
        .title {
          .icon {
            background-image: url(~@/assets/images/games/quan-icon-2.png);
          }
        }
      }
      .gift {
        flex: 1;
        min-width: 0;
        background-color: #fcf6f1;
        .title {
          .icon {
            background-image: url(~@/assets/images/games/gift-icon-2.png);
          }
        }
      }
    }
  }
  .fanli-container {
    width: 339 * @rem;
    position: relative;
    margin: 5 * @rem auto 0;
    padding: 10 * @rem;
    box-sizing: border-box;
    border-radius: 10 * @rem;
    border: 1 * @rem solid #ffefd2;

    .fanli-top {
      display: flex;
      align-items: center;
      img {
        flex-shrink: 0;
        width: 71 * @rem;
        height: 17 * @rem;
      }
      .fanli-text {
        flex: 1;
        min-width: 0;
        height: 17 * @rem;
        line-height: 17 * @rem;
        margin: 0 6 * @rem;
        font-size: 12 * @rem;
        color: #c2964a;
      }
      .fanli-more {
        flex-shrink: 0;
        height: 17 * @rem;
        font-size: 12 * @rem;
        font-weight: 400;
        color: #ff773d;
        line-height: 17 * @rem;
      }
    }

    .fanli-list {
      margin-top: 16 * @rem;

      .fanli-item {
        margin-bottom: 12 * @rem;

        &:last-of-type {
          margin-bottom: 4 * @rem;
        }

        .fanli-type {
          display: inline-block;
          width: auto;
          height: 18 * @rem;
          line-height: 18 * @rem;
          padding: 0 13 * @rem;
          font-size: 10 * @rem;
          color: #e7642f;
          background-color: #ffefd2;
          border-radius: 7 * @rem 7 * @rem 0 0;
        }

        .fanli-detail {
          display: flex;
          align-items: center;
          padding: 10 * @rem 12 * @rem;
          box-sizing: border-box;
          border-radius: 0 10 * @rem 10 * @rem 10 * @rem;
          border: 1 * @rem solid #ffefd2;

          .fanli-info {
            flex: 1;
            min-width: 0;

            .fanli-name {
              display: block;
              font-size: 12 * @rem;
              font-weight: 600;
              color: #2f3338;
              line-height: 14 * @rem;
            }

            .fanli-date {
              height: 14 * @rem;
              font-size: 10 * @rem;
              font-weight: 400;
              color: #999999;
              line-height: 12 * @rem;
              margin-top: 9 * @rem;
            }
          }

          .right-arrow {
            flex-shrink: 0;
            display: block;
            width: 7 * @rem;
            margin: 0 2 * @rem 0 10 * @rem;
          }
        }
      }
    }
  }
  .tab-container {
    position: relative;
    background: #fff;
    top: -14 * @rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    &.not-tab-container {
      top: 0;
    }
    &.not-morepic-container {
      top: -36 * @rem;
    }
  }
  .tabs {
    display: flex;
    position: relative;
    background-color: #fff;
    &.default-shadow {
      box-shadow: 0px -6px 8px 0px rgba(239, 240, 240, 0.3);
    }
    .tab {
      // width: 187 * @rem;
      width: 93.75 * @rem;
      height: 38 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        font-size: 16 * @rem;
        background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg.png)
          no-repeat center center;
        background-size: 48 * @rem 18 * @rem;
        &.text3_active {
          background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg1.png)
            no-repeat center center;
          background-size: 58 * @rem 18 * @rem;
        }
        &.text4_active {
          background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg2.png)
            no-repeat center center;
          background-size: 64 * @rem 18 * @rem;
        }
        span {
          font-size: 16 * @rem;
          text-align: center;
          font-weight: bold;
          color: #191b1f;
          &::before {
            content: '';
            display: block;
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/games/tabs-active-icon/tabs-active-dot.png)
              no-repeat;
            background-size: 12 * @rem 12 * @rem;
            position: absolute;
            top: -4 * @rem;
            right: -12 * @rem;
          }
        }
      }
      span {
        display: block;
        font-size: 16 * @rem;
        font-weight: 400;
        color: #000000;
        position: relative;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        .comment_count {
          position: absolute;
          right: -12 * @rem;
          flex-shrink: 0;
          width: 10 * @rem;
          height: 18 * @rem;
          line-height: 18 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #60666c;
        }
      }
      .notice {
        position: absolute;
        left: 50%;
        top: 2 * @rem;
        transform: translateX(12 * @rem);
        height: 14 * @rem;
        line-height: 14 * @rem;
        background: #ff504f;
        color: #ffffff;
        font-size: 10 * @rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 4 * @rem;
        border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
      }
    }
    .tab-line {
      position: absolute;
      width: 12px;
      height: 4 * @rem;
      border-radius: 2 * @rem;
      background-color: @themeColor;
      left: 0;
      bottom: 0;
      transition: 0.3s;
    }
  }
  .tab-content {
    &.tab-content-top {
      margin-top: calc(@safeAreaTop + 50 * @rem);
      margin-top: calc(@safeAreaTopEnv + 50 * @rem);
    }
    /deep/ .van-loading {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &.tab-up {
      .up-game-item {
        margin: 20 * @rem 18 * @rem 20 * @rem;
      }
    }
    &.tab-deal,
    &.tab-tool,
    &.tab-comment {
      flex: 1;
      min-height: 0;
      flex-shrink: 0;
    }
    &.tab-tool {
      background: #f7f8fa;
    }
    &.tab-kaifu {
      padding: 18 * @rem 0;
      .kaifu-section {
        .kaifu-tips {
          text-align: center;
        }
        .kaifu-active {
          width: 96 * @rem;
          height: 34 * @rem;
          .image-bg('~@/assets/images/games/kaifu-active.png');
          margin: 15 * @rem auto;
        }
      }
    }
    &.tab-detail {
      padding: 0 0 15 * @rem 0 * @rem;

      .benefits-container {
        .benefits-title {
          height: 20 * @rem;
          line-height: 20 * @rem;
          // background: url(~@/assets/images/games/dujia-title-icon.png) left
          //   center no-repeat;
          // background-size: 18 * @rem 18 * @rem;
          // padding-left: 24 * @rem;
          font-size: 16 * @rem;
          color: #191b1f;
          font-weight: 600;
          display: flex;
          align-items: center;
        }
        .benefits-list {
          border-radius: 8 * @rem;
          // background-color: #f9f9f9;
          // padding: 14 * @rem 10 * @rem;
          .benefits-item {
            display: flex;
            align-items: center;
            height: 54 * @rem;
            position: relative;
            &:not(:first-of-type) {
              margin-top: 12 * @rem;
            }
            .icon {
              width: 54 * @rem;
              height: 54 * @rem;
              border-radius: 12 * @rem;
              overflow: hidden;
            }
            .center {
              flex: 1;
              min-width: 0;
              margin-left: 8 * @rem;
              .title-line {
                display: flex;
                align-items: center;
                .title {
                  font-size: 14 * @rem;
                  color: #303236;
                  font-weight: 600;
                  line-height: 18 * @rem;
                  flex-shrink: 0;
                }
                .subtitle {
                  box-sizing: border-box;
                  border: 0.5 * @rem solid #ff7171;
                  border-radius: 4 * @rem;
                  margin-left: 4 * @rem;
                  height: 16 * @rem;
                  line-height: 16 * @rem;
                  padding: 0 2 * @rem;
                  background-color: #ffecec;
                  font-size: 11 * @rem;
                  color: #ff5050;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
              }
              .desc {
                font-size: 12 * @rem;
                color: #93999f;
                line-height: 14 * @rem;
                margin-top: 8 * @rem;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
            .right-icon {
              width: 9 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/games/benefits-arrow.png) left
                center no-repeat;
              background-size: 9 * @rem 12 * @rem;
            }
          }
        }
      }

      .game-picture-swiper {
        box-sizing: border-box;
        width: 100%;
        overflow: hidden;
        .swiper-container {
          box-sizing: border-box;
          padding: 0 18 * @rem;
          width: 100%;
        }
      }
    }
    .kaifu-tip {
      margin-top: 20 * @rem;
      font-size: 12 * @rem;
      color: #ffa800;
      text-align: center;
    }
    .kaifu-list {
      margin-top: 10 * @rem;
      .kaifu-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 17 * @rem 14 * @rem;
        &:nth-of-type(1) {
          border-top: 0;
        }
        &.today {
          .left {
            .time {
              color: #47a83a;
            }
          }
        }
      }
      .left {
        .time {
          font-size: 13 * @rem;
          color: #30343b;
          display: flex;
          align-items: center;
          .hour {
            margin-left: 2 * @rem;
          }
        }
        .quhao {
          font-size: 10 * @rem;
          margin-top: 6 * @rem;
        }
      }
      .right {
        font-size: 15 * @rem;
        color: #666666;
      }
    }
    .more-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 42 * @rem;
      position: absolute;
      right: 10 * @rem;
      bottom: 17 * @rem;
      background: #fff;
      span {
        font-size: 12 * @rem;
        color: #32b768;
        height: 17 * @rem;
        line-height: 17 * @rem;
      }
      .more-text-icon {
        width: 11 * @rem;
        height: 7 * @rem;
        background: url(~@/assets/images/games/bottom-arrow-green.png) center
          center no-repeat;
        background-size: 11 * @rem 7 * @rem;
        margin-left: 4 * @rem;
        transition: 0.3s;
        &.on {
          transform: rotateZ(180deg);
        }
      }
    }
    .welfare-container {
      width: 100%;
      box-sizing: border-box;
      position: relative;
      .welfare-box {
        background: #f6fefc;
        border-radius: 8 * @rem;
        border: 1 * @rem solid #e1f9f0;
        background: url('~@/assets/images/games/welfare-container-bg.png')
          #f6fefc no-repeat top center;
        width: 351 * @rem;
        background-size: 351 * @rem 108 * @rem;
        .welfare-title {
          padding: 10 * @rem 15 * @rem 0 12 * @rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .title {
            width: 73 * @rem;
            height: 25 * @rem;
          }
          .arrow {
            width: 24 * @rem;
            height: 24 * @rem;
          }
        }
        .wel-section {
          box-sizing: border-box;
          padding: 16 * @rem 12 * @rem;
          // background: linear-gradient(360deg, #ffffff 0%, #fff5f3 100%);
          border-radius: 12 * @rem;
          width: 100%;
          position: relative;
        }
        .content-text {
          font-size: 14px;
          color: #60666c;
          line-height: 24px;
          display: -webkit-box;
          max-height: unset;
          height: auto;
          transition: 0.3s;
          &.on {
            max-height: 144px;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 6;
            overflow: hidden;
          }
        }
      }
    }
    .welfare-info {
      padding: 16 * @rem 12 * @rem 0;
      .section-title {
        margin-bottom: 8 * @rem;
      }
      .notice-list {
        // margin: 0 18 * @rem 18 * @rem;
        // background: #f9f9f9;
        // border-radius: 6 * @rem;
        .notice-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 32 * @rem;
          .notice-type {
            margin-right: 7 * @rem;
            font-size: 10 * @rem;
            font-weight: 400;
            background: linear-gradient(90deg, #ecfbf4 0%, #ffffff 100%);
            border-radius: 4 * @rem;
            border: 1 * @rem solid #191b1f;
            color: #191b1f;
            padding: 3 * @rem 5 * @rem;
            box-sizing: border-box;
            flex-shrink: 0;
          }
          .notice-title {
            display: flex;
            justify-content: space-between;
            flex: 1;
            color: #303236;
            font-size: 13 * @rem;
            .text {
              width: 284 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .notice-icon {
              display: block;
              width: 14 * @rem;
              height: 14 * @rem;
              .image-bg('~@/assets/images/more-right-arrow.png');
            }
          }
        }
      }
    }
    .update-content {
      .update-box {
        width: 351 * @rem;
        box-sizing: border-box;
        padding: 12 * @rem 13 * @rem;
        background: #f7f8fa;
        border-radius: 8 * @rem;
        font-weight: 400;
        font-size: 13 * @rem;
        color: #60666c;
        line-height: 21 * @rem;
        display: -webkit-box;
        max-height: unset;
      }
    }
    .built-in-plugins {
      .built-in-plugins-list {
        display: flex;
        flex-direction: column;
        .built-in-plugin-item {
          display: flex;
          align-items: center;
          overflow: hidden;
          white-space: nowrap;
          .icon {
            flex-shrink: 0;
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 6 * @rem;
            overflow: hidden;
          }
          .title {
            flex-shrink: 0;
            white-space: nowrap;
            margin: 0 10 * @rem 0 6 * @rem;
            font-weight: 400;
            font-size: 13 * @rem;
            color: #191b1f;
          }
          .desc {
            flex: 1;
            min-width: 0;
            height: 13 * @rem;
            line-height: 13 * @rem;
            padding-left: 10 * @rem;
            font-weight: 400;
            font-size: 13 * @rem;
            color: #60666c;
            position: relative;
            display: flex;
            align-items: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            &:before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1 * @rem;
              height: 13 * @rem;
              background: #e3e5e8;
              border-radius: 1 * @rem;
            }
          }
        }
      }
    }
    .introduction {
      width: 100%;
      box-sizing: border-box;
      position: relative;
      padding: 20 * @rem 0 0 12 * @rem;
      .section-title {
        padding: 0 12 * @rem 0 0;
      }
      .recharge-back {
        padding: 0 8 * @rem 0 13 * @rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 28 * @rem;
        border-radius: 26 * @rem;
        border: 1 * @rem solid #e3e5e8;
        .text {
          white-space: nowrap;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #93999f;
        }
        .icon {
          margin-left: 3 * @rem;
          background: url('~@/assets/images/games/version-arrow.png') no-repeat
            top center;
          width: 8 * @rem;
          height: 8 * @rem;
          background-size: 8 * @rem 8 * @rem;
        }
      }
      .category-tags {
        display: flex;
        align-items: center;
        overflow-x: auto;
        -webkit-mask: linear-gradient(
          to right,
          #fff calc(100% - 30 * @rem),
          transparent
        );
        &::-webkit-scrollbar {
          display: none;
        }
        .category-tag-item {
          white-space: nowrap;
          height: 22 * @rem;
          margin-right: 6 * @rem;
          font-size: 10 * @rem;
          background: #f7f8fa;
          padding: 0 5 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 5 * @rem;
          color: #93999f;
        }
      }
      .introduction-text {
        padding: 0 12 * @rem 0 0;
        font-size: 15px;
        color: #30343b;
        line-height: 26px;
        // display: -webkit-box;
        margin-top: 10 * @rem;
        max-height: unset;
        transition: 0.3s;
        &.on {
          max-height: 156px;
          height: 156px;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 6;
          overflow: hidden;
        }
      }
      .more-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 50 * @rem;
        position: absolute;
        right: 12 * @rem;
        bottom: 0;
        background: #fff;
        white-space: nowrap;
        span {
          font-size: 15px;
          color: #2bbe88;
          height: 26px;
          line-height: 26px;
        }
      }
    }
    .reminder-text {
      .content {
        background: #f7f8fa;
        border-radius: 8 * @rem;
        padding: 12 * @rem 14 * @rem 14 * @rem 12 * @rem;
        width: 100%;
        box-sizing: border-box;
        position: relative;
        .content-text {
          font-size: 14px;
          color: #9a9a9a;
          line-height: 20px;
          display: -webkit-box;
          max-height: unset;
          height: auto;
          transition: 0.3s;
          display: flex;
          flex-wrap: wrap;
          &.on {
            max-height: 100px;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 5;
            overflow: hidden;
          }
        }
        .more-text {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 42 * @rem;
          position: absolute;
          right: 10 * @rem;
          bottom: 17 * @rem;
          background: #fafafa;
          span {
            font-size: 12 * @rem;
            color: #32b768;
            height: 17 * @rem;
            line-height: 17 * @rem;
          }
        }
        .ellipsis-content {
          font-weight: 400;
          font-size: 13px;
          color: #9a9a9a;
          position: relative;
          line-height: 1.5;
          text-align: justify;
          word-spacing: 2px;
          .btn {
            color: #32b768;
          }
        }
      }
    }
    .version-section {
      .section-title {
        .feed-back {
          padding: 0 8 * @rem 0 13 * @rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          height: 28 * @rem;
          border-radius: 26 * @rem;
          border: 1 * @rem solid #e3e5e8;
          .text {
            font-weight: 500;
            font-size: 12 * @rem;
            color: #93999f;
          }
          .icon {
            margin-left: 3 * @rem;
            background: url('~@/assets/images/games/version-arrow.png')
              no-repeat top center;
            width: 8 * @rem;
            height: 8 * @rem;
            background-size: 8 * @rem 8 * @rem;
          }
        }
      }
      .version-container {
        margin: 12 * @rem 0;
        width: 351 * @rem;
        background: #f7f8fa;
        border-radius: 8 * @rem;
        box-sizing: border-box;
        padding: 14 * @rem 10 * @rem 13 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .content-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .label {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #93999f;
            white-space: nowrap;
          }
          .value-box {
            width: 240 * @rem;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .value1 {
              font-weight: 400;
              font-size: 14 * @rem;
              color: #30343b;
            }
            .value2 {
              font-weight: 400;
              font-size: 14 * @rem;
              color: #2bbe88;
              line-height: 14 * @rem;
              // text-decoration: underline;
              display: inline;
              border-bottom: solid 1px #2bbe88;
              &:not(:first-child) {
                margin-left: 9 * @rem;
              }
            }
          }
          .up-info {
            display: flex;
            align-items: center;
            .avatar {
              width: 16 * @rem;
              height: 16 * @rem;
              border-radius: 50%;
              overflow: hidden;
            }
            .nickname {
              margin-left: 6 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #30343b;
            }
            .arrow {
              width: 8 * @rem;
              height: 12 * @rem;
              margin-left: 2 * @rem;
              background: url(~@/assets/images/games/arrow-right-grey1.png)
                right center no-repeat;
              background-size: 8 * @rem 12 * @rem;
            }
          }
          &:not(:first-child) {
            margin-top: 13 * @rem;
          }
        }
        .see-more-info {
          display: flex;
          align-items: center;
          margin-top: 23 * @rem;
          > div {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #60666c;
          }
          > span {
            width: 8 * @rem;
            height: 12 * @rem;
            margin-left: 3 * @rem;
            background: url(~@/assets/images/games/arrow-right-grey1.png) right
              center no-repeat;
            background-size: 8 * @rem 12 * @rem;
          }
        }
      }
    }
    .open-server-info {
      .kaifu-container {
        .kaifu-component {
          padding: 0;
        }
      }
    }
    .vip-table {
      .vip-desc {
        font-size: 14px;
        color: #797979;
        line-height: 20px;
      }
      .table-container {
        height: 250px;
        overflow: hidden;
        position: relative;
        &.open {
          height: auto;
        }
        .vip-open {
          box-sizing: border-box;
          padding-top: 20 * @rem;
          width: 100%;
          position: absolute;
          bottom: -1 * @rem;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 40 * @rem;
          font-size: 12 * @rem;
          color: @themeColor;
          background: linear-gradient(
            to top,
            rgba(255, 255, 255, 1) 20 * @rem,
            rgba(255, 255, 255, 0)
          );
          .more-text-icon {
            width: 11 * @rem;
            height: 7 * @rem;
            background: url(~@/assets/images/games/bottom-arrow-green.png)
              center center no-repeat;
            background-size: 11 * @rem 7 * @rem;
            margin-left: 4 * @rem;
            transition: 0.3s;
            &.on {
              transform: rotateZ(180deg);
            }
          }
        }
      }
      .table {
        box-sizing: border-box;
        width: 100%;
        margin-top: 10 * @rem;
        border-radius: 12 * @rem;
        border: 0.5 * @rem solid #d3bba2;
        overflow: hidden;
        .row {
          display: flex;
          &:nth-of-type(n + 2) {
            border-top: 0.5 * @rem solid #d3bba2;
          }
          &:nth-of-type(2n + 1) {
            background-color: #f5f5f6;
          }
        }
        .th,
        .td {
          width: 50%;
          text-align: center;
          font-size: 12 * @rem;
          color: #000000;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 11 * @rem 5 * @rem;
          &:nth-of-type(n + 2) {
            border-left: 0.5 * @rem solid #d3bba2;
          }
        }
        .th {
          background-color: #f5e2ce;
          font-size: 14 * @rem;
          color: #7a532a;
          font-weight: normal;
        }
      }
    }
    .banner-fragment {
      box-sizing: border-box;
      background-color: #fff;
      overflow: hidden;
      /deep/ .swiper-slide {
        border-radius: 0 !important;
        // &:not(:last-child) {
        //   margin-right: 18 * @rem;
        // }
      }
    }
    .hot-comments {
      .section-title {
        .comments-btn {
          padding: 0 10 * @rem;
          box-sizing: border-box;
          height: 28 * @rem;
          border-radius: 26 * @rem;
          border: 1 * @rem solid #e3e5e8;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          .icon {
            background: url(~@/assets/images/games/comments-btn-icon.png)
              no-repeat top center;
            background-size: 12 * @rem 12 * @rem;
            width: 12 * @rem;
            height: 12 * @rem;
          }
          .text {
            margin-left: 4 * @rem;
            height: 15 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #93999f;
          }
        }
      }
      .hot-comment-list {
        .item {
          margin-top: 16 * @rem;
          &:not(:last-child) {
            padding-bottom: 16 * @rem;
            border-bottom: 1px solid #f9f9f9;
          }
        }
        width: 100%;
        &.margin-b32 {
          margin-bottom: 32 * @rem;
        }
      }
      .comment-more {
        width: 200 * @rem;
        height: 40 * @rem;
        margin: 0 auto;
        border: 1px solid #e3e5e8;
        border-radius: 80 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .comment-more-text {
          font-size: 14 * @rem;
          color: #1cce94;
          font-weight: 400;
        }
        i {
          display: block;
          width: 8 * @rem;
          height: 12 * @rem;
          background: url(~@/assets/images/games/welfare-right-icon.png) center
            center no-repeat;
          background-size: 8 * @rem 12 * @rem;
          margin-left: 3 * @rem;
        }
      }
    }
    .detail-info {
      margin-top: 5 * @rem;
      .info-content {
        .info-item {
          height: 22 * @rem;
          display: flex;
          align-items: center;
          color: rgb(153, 153, 153);
          font-size: 12 * @rem;
          .info-text {
            margin-right: 20 * @rem;
          }
          span {
            color: #51adff;
            font-size: 12 * @rem;
            margin-right: 20 * @rem;
          }
        }
      }
      .detail-info-content {
        margin-top: 15 * @rem;
        background-color: #f8f8f8;
        border-radius: 5 * @rem;
        .info-list {
          display: flex;
          flex-wrap: wrap;
          .info-item {
            box-sizing: border-box;
            padding: 15 * @rem 20 * @rem;
            width: 50%;
            .info-name {
              font-size: 14 * @rem;
              color: #666666;
              font-weight: bold;
            }
            .info-value {
              font-size: 12 * @rem;
              color: #b2b2b2;
              font-weight: bold;
              margin-top: 8 * @rem;
            }
          }
        }
      }
    }

    .related {
      margin-top: 20 * @rem;
      padding: 0;
      .section-title {
        padding-left: 12 * @rem;
      }
      .game-list {
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        margin-top: 12 * @rem;
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        padding-left: 12 * @rem;
        &::-webkit-scrollbar {
          display: none !important;
        }
        .game-group {
          &:not(:first-of-type) {
            margin-left: 10 * @rem;
          }
          &:last-of-type {
            padding-right: 18 * @rem;
          }
        }
        .game-item {
          box-sizing: border-box;
          width: 275 * @rem;
          height: 68 * @rem;
          border-radius: 12 * @rem;
          display: flex;
          align-items: center;
          &:not(:first-of-type) {
            margin-top: 20 * @rem;
          }
        }
      }
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    &.tool-bg {
      background: #f7f8fa;
    }
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12 * @rem;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .gift-item {
        position: relative;
        padding: 0 10 * @rem;
        .benefit-item {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .benefit-icon {
            width: 24 * @rem;
            height: 21 * @rem;
          }
          .benefit-text {
            margin-top: 6 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #191b1f;
          }
          .gift-icon-1,
          .gift-icon-2 {
            position: absolute;
            top: -19 * @rem;
            right: 0;
            background: url('~@/assets/images/games/gift-icon-1.png') no-repeat
              top center;
            width: 44 * @rem;
            height: 19 * @rem;
            background-size: 44 * @rem 19 * @rem;
            left: 50%;
            transform: translate(-50%);
          }
          .gift-icon-2 {
            background: url('~@/assets/images/games/gift-icon-2.png') no-repeat
              top center;
            width: 44 * @rem;
            height: 19 * @rem;
            background-size: 44 * @rem 19 * @rem;
          }
        }
      }

      .comment {
        margin-right: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .comment-icon {
          width: 24 * @rem;
          height: 24 * @rem;
          .image-bg('~@/assets/images/games/comment-icon.png');
        }
        .comment-title {
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 500;
          margin-top: 5 * @rem;
        }
      }
    }
  }

  .feedback {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30 * @rem 0 0;
    color: #666666;
    font-size: 15 * @rem;
    span {
      color: #ffa800;
    }
  }
}
.grqPopup {
  box-sizing: border-box;
  padding: 20 * @rem 14 * @rem;
  .top-right {
    position: absolute;
    right: 14 * @rem;
    font-size: 14 * @rem;
    color: #666;
  }
  .container {
    margin-top: 50 * @rem;
    font-size: 16 * @rem;
    line-height: 28 * @rem;
    .color2 {
      color: #ff475d;
    }
    .color1 {
      color: @themeColor;
    }
  }
  .button {
    width: 200 * @rem;
    height: 40 * @rem;
    margin: 20 * @rem auto 5 * @rem;
    text-align: center;
    line-height: 40 * @rem;
    background: @themeBg;
    color: #fff;
    border-radius: 25px;
    &.loading {
      position: relative;
      font-size: 0;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: block;
        width: 16 * @rem;
        height: 16 * @rem;
        background-size: 16 * @rem 16 * @rem;
        background-image: url(~@/assets/images/downloadLoading.png);
        animation: rotate 1s infinite linear;
      }
    }
  }
}
.downloadGrqDialog {
  box-sizing: border-box;
  width: 280 * @rem;
  padding: 16 * @rem;
  border-radius: 10 * @rem;
  .item {
    display: flex;
    justify-content: space-between;
    margin-top: 15 * @rem;
    .right {
      width: 16 * @rem;
      height: 16 * @rem;
      background-image: url(~@/assets/images/games/success2.png);
      background-size: 100%;
      &.loading {
        background-image: url(~@/assets/images/games/refresh.png);
        animation: roll 1s linear infinite;
      }
      &.success {
        background-image: url(~@/assets/images/games/success.png);
      }
      &.fail {
        background-image: url(~@/assets/images/games/success2.png);
      }
    }
  }
  .text {
    margin-top: 30 * @rem;
    text-align: center;
    font-size: 14 * @rem;
    color: #ff8a00;
  }
  .button {
    width: 180 * @rem;
    height: 37 * @rem;
    margin: 15 * @rem auto 0;
    text-align: center;
    line-height: 37 * @rem;
    background: #fff4e7;
    color: #ff8a00;
    border-radius: 6 * @rem;
    .icon {
      position: relative;
      top: -1 * @rem;
      left: 3 * @rem;
      display: inline-block;
      width: 6 * @rem;
      height: 8 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/games/arrow-right.png);
      background-repeat: no-repeat;
    }
  }
}
.no-safari-popup {
  .title {
    height: 44 * @rem;
    text-align: center;
    line-height: 44 * @rem;
    font-size: 16 * @rem;
    border-bottom: 1px solid #dedede;
  }
  .close {
    position: absolute;
    top: 14 * @rem;
    right: 14 * @rem;
    width: 14 * @rem;
    height: 14 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-size: no-repeat;
  }
  .content {
    padding: 14 * @rem;
    .item {
      margin-bottom: 20 * @rem;
    }
    .big-text {
      line-height: 21 * @rem;
      font-size: 15 * @rem;
      .sign {
        font-weight: bolder;
      }
    }
    .small-text {
      margin-top: 5 * @rem;
      line-height: 16 * @rem;
      font-size: 12 * @rem;
      i.icon {
        position: relative;
        top: 3 * @rem;
        display: inline-block;
        width: 16 * @rem;
        height: 16 * @rem;
        margin: 0 5 * @rem;
        background-image: url(~@/assets/images/safari-icon-32x32.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .color {
        &.red {
          color: #f63838;
        }
        &.blue {
          color: #51adff;
        }
        &.green {
          color: @themeColor;
        }
      }
    }
    .button {
      width: 240 * @rem;
      height: 40 * @rem;
      margin: 20 * @rem auto;
      text-align: center;
      line-height: 40 * @rem;
      font-size: 14 * @rem;
      color: #fff;
      background: @themeBg;
      border-radius: 10 * @rem;
    }
    .explain {
      margin-bottom: 10 * @rem;
      text-align: center;
      color: #999;
    }
  }
}
.permission-popup {
  max-height: 400 * @rem;
  min-height: 200 * @rem;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 16 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 60 * @rem;
    background-color: #fff;
    flex-shrink: 0;
  }
  .permission-list {
    padding: 0 14 * @rem 20 * @rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    .permission-item {
      padding: 10 * @rem;
      border-bottom: 1px solid #ebebeb;
      .name {
        font-size: 14 * @rem;
        color: #666;
      }
      .desc {
        font-size: 12 * @rem;
        color: #999;
        line-height: 18 * @rem;
        margin-top: 10 * @rem;
      }
    }
  }
}
.fixed-review-box {
  position: fixed;
  right: 14 * @rem;
  bottom: 0;
  z-index: 99;
  padding-bottom: calc(95 * 0.0267rem + env(safe-area-inset-bottom));
  .fixed-review-btn {
    background-size: 58 * @rem 56 * @rem;
    width: 58 * @rem;
    height: 56 * @rem;
  }
}
.kaifu-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .kaifu-item {
    box-sizing: border-box;
    width: 171 * @rem;
    padding: 0 9 * @rem;
    background: #f5f5f6;
    border-radius: 8 * @rem;
    height: 64 * @rem;
    display: flex;
    align-items: center;
    &:not(:nth-of-type(-n + 2)) {
      margin-top: 10 * @rem;
    }
    .left {
      flex: 1;
      min-width: 0;
      .time {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: bold;
        display: flex;
        align-items: center;
        .hour {
          margin-left: 5 * @rem;
        }
      }
      .quhao {
        font-size: 12 * @rem;
        color: #9a9a9a;
        font-weight: 400;
        margin-top: 2 * @rem;
        line-height: 17 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .status {
      box-sizing: border-box;
      width: 54 * @rem;
      height: 30 * @rem;
      // border: 1 * @rem solid @themeColor;
      background: #e4fcf0;
      color: #2bbe88;
      font-size: 11 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6 * @rem;
      &.had {
        border: 1 * @rem solid #c1c1c1;
        color: #fff;
        background-color: #c1c1c1;
      }
    }
  }
}
.wx-popup {
  .bg {
    display: block;
    width: 150 * @rem;
    margin: 25 * @rem auto;
  }
  .big-text {
    font-size: 20 * @rem;
    text-align: center;
  }
  .small-text {
    margin: 15 * @rem 20 * @rem 30 * @rem;
    font-size: 14 * @rem;
    text-align: center;
  }
}
.download-bar {
  flex: 1;
  min-width: 0;
  height: 60 * @rem;
  .download-content {
    height: 60 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .download-btn {
      .unit {
        font-size: 12 * @rem;
      }
    }
    .download-btn,
    .subscribe-btn {
      flex: 1;
      height: 44 * @rem;
      // background: @themeBg;
      background: #1cce94;
      margin: 0 7 * @rem;
      text-align: center;
      line-height: 44 * @rem;
      color: #fefefe;
      font-size: 16 * @rem;
      font-weight: 500;
      border-radius: 8 * @rem;
      .question {
        position: absolute;
        top: 0;
        right: 0;
        display: block;
        width: 25 * @rem;
        height: 25 * @rem;
        background-size: 100%;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/games/download-question.png);
      }
      &.loading {
        position: relative;
        font-size: 0;
        .unit {
          font-size: 0;
        }
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          animation: rotate 1s infinite linear;
        }
      }
      &.pd-7 {
        padding: 0 7 * @rem;
        white-space: nowrap;
      }
    }
    .h5-btn {
      flex: 1;
      height: 44 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fefefe;
      font-size: 16 * @rem;
      font-weight: 500;
      border-radius: 6 * @rem;
      // background-color: #fe4a55;
      background: linear-gradient(270deg, #ffab3d 0%, #fc7115 100%);
      margin: 0 7 * @rem;
      .question {
        position: absolute;
        top: 0;
        right: 0;
        display: block;
        width: 25 * @rem;
        height: 25 * @rem;
        background-size: 100%;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/games/download-question.png);
      }
    }
    .download-no,
    .subscribe-no {
      background-color: #bbb;
      flex: 1;
      height: 44 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fefefe;
      font-size: 16 * @rem;
      font-weight: bold;
      border-radius: 6 * @rem;
      margin: 0 7 * @rem;
    }
    .pc-cloud-download-btn {
      flex: 1;
      height: 44 * @rem;
      background: @themeBg;
      text-align: center;
      line-height: 44 * @rem;
      color: #fefefe;
      font-size: 16 * @rem;
      font-weight: 500;
      border-radius: 8 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 29 * @rem;
      .icon {
        background-origin: -1 * @rem -1 * @rem;
        background-size: 22 * @rem 24 * @rem;
        width: 21 * @rem;
        height: 23 * @rem;
      }
      .text {
        margin-left: 4 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #ffffff;
        &.loading {
          position: relative;
          font-size: 0;
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: block;
            width: 16 * @rem;
            height: 16 * @rem;
            background-size: 16 * @rem 16 * @rem;
            background-image: url(~@/assets/images/downloadLoading.png);
            animation: rotate 1s infinite linear;
          }
        }
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes roll {
  0% {
    transform: rotate(0deg) translate(0, 1px);
  }
  25% {
    transform: rotate(90deg) translate(1px, 0);
  }
  50% {
    transform: rotate(180deg) translate(0, -1px);
  }
  75% {
    transform: rotate(270deg) translate(-1px, 0);
  }
  100% {
    transform: rotate(360deg) translate(0, 1px);
  }
}

// 18元平台币充值提示弹窗
.ptb-tips-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  height: 202 * @rem;
  border-radius: 16 * @rem;
  background: #fff;
  padding-top: 30 * @rem;
  .title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    text-align: center;
    line-height: 20 * @rem;
  }
  .desc {
    margin-top: 20 * @rem;
    font-size: 15 * @rem;
    color: #333333;
    line-height: 21 * @rem;
    padding-left: 19 * @rem;
  }
  .tip {
    font-size: 12 * @rem;
    color: #f44040;
    line-height: 16 * @rem;
    padding-left: 19 * @rem;
    margin-top: 8 * @rem;
  }
  .operation-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30 * @rem auto 0;
    .cancel {
      width: 110 * @rem;
      height: 32 * @rem;
      border-radius: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #7d7d7d;
      background: #f2f2f2;
      margin: 0 10 * @rem;
    }
    .confirm {
      width: 110 * @rem;
      height: 32 * @rem;
      border-radius: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #ffffff;
      background: @themeBg;
      margin: 0 10 * @rem;
    }
  }
}

.buy-game-success-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 16 * @rem;
  background: #fff;
  padding-top: 30 * @rem;
  padding-bottom: 20 * @rem;
  .title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    text-align: center;
    line-height: 20 * @rem;
  }
  .order-list {
    padding: 0 24 * @rem;
    .order-item {
      .order-title {
        font-size: 12 * @rem;
        color: #777777;
        line-height: 17 * @rem;
        margin-top: 20 * @rem;
      }
      .order-desc {
        font-size: 16 * @rem;
        color: #333333;
        font-weight: 600;
        line-height: 22 * @rem;
        margin-top: 10 * @rem;
      }
    }
  }
  .operation-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30 * @rem auto 0;
    .cancel {
      width: 110 * @rem;
      height: 38 * @rem;
      border-radius: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #7d7d7d;
      background: #f2f2f2;
      margin: 0 10 * @rem;
    }
    .confirm {
      width: 238 * @rem;
      height: 38 * @rem;
      border-radius: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #ffffff;
      background: @themeBg;
      margin: 0 10 * @rem;
    }
  }
}

.ptb-recharge-popup,
.buy-game-popup {
  padding: 18 * @rem 18 * @rem 32 * @rem;
  position: relative;
  .close {
    width: 52 * @rem;
    height: 52 * @rem;
    background: url('~@/assets/images/close-dialog.png') center center no-repeat;
    background-size: 16 * @rem 16 * @rem;
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0.5;
  }
  .title {
    font-size: 16 * @rem;
    color: #111111;
    font-weight: 600;
    text-align: center;
    line-height: 22 * @rem;
  }
  .recharge-info {
    box-sizing: border-box;
    width: 339 * @rem;
    height: 71 * @rem;
    border-radius: 8 * @rem;
    background: #f6f6f6;
    border: 1px solid #efefef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 17 * @rem;
    margin: 24 * @rem auto 0;
    .ptb-num {
      font-size: 18 * @rem;
      font-weight: 600;
      color: #333438;
    }
    .money {
      font-size: 12 * @rem;
      color: @themeColor;
      font-weight: 600;
      span {
        font-size: 22 * @rem;
        font-weight: 600;
      }
    }
  }

  .buy-game-bar {
    display: flex;
    align-items: center;
    padding: 20 * @rem 0;
    margin-top: 20 * @rem;
    border-top: 0.5px solid #ebebeb;
    border-bottom: 0.5px solid #ebebeb;
    .game-icon {
      width: 56 * @rem;
      height: 56 * @rem;
    }
    .buy-game-info {
      margin: 0 8 * @rem;
      flex: 1;
      min-width: 0;
      .game-title {
        font-size: 14 * @rem;
        color: #333333;
        font-weight: 600;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .amount {
      font-size: 12 * @rem;
      color: @themeColor;
      font-weight: 600;
      span {
        font-size: 22 * @rem;
        color: @themeColor;
        font-weight: 600;
      }
    }
  }
  .pay-way-title {
    font-size: 15 * @rem;
    color: #333333;
    line-height: 19 * @rem;
    font-weight: 600;
    margin-bottom: -10 * @rem;
    margin-top: 23 * @rem;
  }
  .more-pay-way {
    margin-top: 20 * @rem;
    font-size: 14 * @rem;
    color: @themeColor;
    line-height: 18 * @rem;
  }
  .pay-list {
    margin-top: 14 * @rem;
    .pay-item {
      display: flex;
      align-items: center;
      padding: 18 * @rem 0;
      border-bottom: 1px solid #eeeeee;
      .icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        &.wx {
          background-image: url(~@/assets/images/recharge/wx-icon.png);
          background-size: 24 * @rem 21 * @rem;
        }
        &.zfb_dmf {
          background-image: url(~@/assets/images/recharge/zfb-icon.png);
          background-size: 24 * @rem 24 * @rem;
        }
      }
      .pay-name {
        font-size: 15 * @rem;
        color: #555555;
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
      }
      .choose {
        width: 18 * @rem;
        height: 18 * @rem;
        background: url(~@/assets/images/recharge/n_radio.png) center center
          no-repeat;
        background-size: 18 * @rem 18 * @rem;
        &.on {
          background-image: url(~@/assets/images/recharge/c_radio.png);
        }
      }
    }
  }
  .recharge-btn {
    width: 295 * @rem;
    height: 40 * @rem;
    border-radius: 20 * @rem;
    background: @themeBg;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 500;
    margin: 16 * @rem auto 0;
  }
}

.welfare-list {
  box-sizing: border-box;
  width: 351 * @rem;
  height: 80 * @rem;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: url('~@/assets/images/games/new-welfare-bg.png') center center
    no-repeat;
  background-size: 351 * @rem 80 * @rem;
  // padding: 4 * @rem 0;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 10 * @rem;
  background: #fffcfa;
  border-radius: 8 * @rem 8 * @rem 0 0;
  &.not-img-welfare-list {
    margin-bottom: 12 * @rem;
  }
  &.not-welfare-box {
    border-radius: 8 * @rem;
  }
  &.no-648 {
    background-image: url(~@/assets/images/games/welfare-bg-no648.png);
  }
  .welfare-item {
    box-sizing: border-box;
    height: 100%;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &:first-of-type {
      &::after {
        content: '';
        width: 0 * @rem;
        height: 56 * @rem;
        position: absolute;
        right: 0;
        border-right: 1 * @rem dashed #f4eae6;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    &:not(:first-of-type) {
      &::after {
        content: '';
        width: 0 * @rem;
        height: 28 * @rem;
        position: absolute;
        right: 0;
        border-right: 1 * @rem solid #f4eae6;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    &:last-of-type {
      &::after {
        border-right: 0;
      }
    }
    &.gift-648 {
      box-sizing: border-box;
      width: 96 * @rem;
      flex: unset;
      // &::after {
      //   height: 56 * @rem;
      //   border-right: 1 * @rem dashed #e4e5e5;
      // }
    }
    .line-1 {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 23 * @rem;
      .num {
        font-size: 18 * @rem;
        font-weight: bold;
        color: #30343b;
        span {
          font-weight: bold;
          font-size: 11 * @rem;
          margin-right: 4 * @rem;
        }
        &.no-num {
          color: #bcb5b3;
        }
        &.no-num1 {
          font-weight: 500;
          font-size: 13 * @rem;
          color: #bcb5b3;
        }
      }
    }
    .line-2 {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 14 * @rem;
      line-height: 14 * @rem;
      margin-top: 8 * @rem;
      .btn-648 {
        height: 14 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #1cce94;
        > img {
          margin-left: 3 * @rem;
          width: 6 * @rem;
          height: 8 * @rem;
        }
      }

      .icon1 {
        width: 14 * @rem;
        height: 13 * @rem;
      }
      .icon2 {
        width: 14 * @rem;
        height: 12 * @rem;
      }
      .icon3 {
        width: 14 * @rem;
        height: 14 * @rem;
      }
      .item-title {
        margin-left: 3 * @rem;
        font-size: 11 * @rem;
        color: #93999f;
      }
      .not-item-title {
        font-size: 11 * @rem;
        color: #bec2c5;
      }
    }
    .line-3 {
      margin: 0 auto;
      margin-top: 8 * @rem;
      padding: 0 11 * @rem 0 10 * @rem;
      height: 24 * @rem;
      background: #ff9429;
      border-radius: 12 * @rem;
      .btn-648 {
        font-weight: 400;
        font-size: 11 * @rem;
        color: #ffffff;
      }
    }
    .line-4 {
      .btn-648 {
        color: #ff9429;
      }
    }
  }
}
.banner-images {
  z-index: 9999 !important;
  background-color: #000000;
  .wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    height: 100%;
    .block {
      width: 100%;
      height: 628 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      // background-color: #fff;
      .banner-center {
        width: 100%;
        /deep/.swiper-wrapper {
          display: flex;
          align-items: center;
        }
        .swiper-slide {
          width: 100%;
          // height: auto;
          overflow: hidden;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          .ad-info {
            display: flex;
            flex-direction: column;
            align-items: center;

            .titlepic {
              width: 100 * @rem;
              height: 100 * @rem;
              overflow: hidden;
              border-radius: 22 * @rem;
            }
            .title {
              margin-top: 12 * @rem;
              font-weight: 600;
              font-size: 18px;
              color: #ffffff;
            }
            .info-bottom {
              margin-top: 8 * @rem;
              display: flex;
              align-items: center;
              .discount-tag {
                display: flex;
                align-items: center;
                width: fit-content;
                margin-right: 6 * @rem;
                flex-shrink: 0;

                .discount-icon {
                  width: 30 * @rem;
                  height: 18 * @rem;
                  position: relative;
                  z-index: 1;
                  &.discount-01 {
                    width: 49 * @rem;
                  }
                }
                .discount-text {
                  display: flex;
                  align-items: center;
                  height: 18 * @rem;
                  padding-right: 4 * @rem;
                  flex: 1;
                  min-width: 0;
                  font-size: 11 * @rem;
                  color: #ff6649;
                  white-space: nowrap;
                  background-color: #fff5ed;
                  border-radius: 0 2 * @rem 2 * @rem 0;
                  margin-left: -7 * @rem;
                  padding-left: 7 * @rem;
                }
              }
              .is-accelerate {
                width: 73 * @rem;
                height: 18 * @rem;
                background: url('~@/assets/images/games/accelerate-tag.png');
                background-position: left center;
                background-size: 73 * @rem 18 * @rem;
                margin-right: 6 * @rem;
                flex-shrink: 0;
              }
              .tags {
                display: flex;
                height: 18 * @rem;
                overflow: hidden;
                flex-wrap: wrap;
                .tag {
                  height: 18 * @rem;
                  margin-right: 6 * @rem;
                  display: flex;
                  align-items: center;
                  flex-wrap: nowrap;
                  color: #93999f;
                  background: #f7f7f8;
                  border-radius: 4 * @rem;
                  padding: 0 4 * @rem;
                  margin-bottom: 1 * @rem;
                  .tag-icon {
                    width: 13 * @rem;
                    height: 13 * @rem;
                    margin-right: 2 * @rem;
                  }
                  .tag-name {
                    font-size: 11 * @rem;
                    white-space: nowrap;
                    color: #93999f;
                  }
                }
                .modify-tag {
                  background-color: #fff;
                }
              }
              .gold-discount-text {
                color: #21b98a;
                white-space: nowrap;
                width: 139 * @rem;
                height: 15 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #21b98a;
                line-height: 15 * @rem;
              }
            }
            .tips {
              margin-top: 23 * @rem;
              width: 264 * @rem;
              height: 69 * @rem;
              text-align: center;
              font-weight: 400;
              font-size: 15 * @rem;
              color: #ffffff;
              line-height: 23 * @rem;
              display: flex;
              justify-content: center;
              .tips-content {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              &::before {
                content: '';
                display: block;
                flex-shrink: 0;
                margin-right: 4 * @rem;
                width: 14 * @rem;
                height: 14 * @rem;
                background: url('~@/assets/images/games/ad-tips-left.png')
                  no-repeat center center;
                background-size: 14 * @rem 14 * @rem;
              }
              &::after {
                content: '';
                display: block;
                flex-shrink: 0;
                margin-left: 4 * @rem;
                width: 14 * @rem;
                height: 14 * @rem;
                background: url('~@/assets/images/games/ad-tips-right.png')
                  no-repeat top center;
                background-size: 14 * @rem 14 * @rem;
              }
            }
            .down-btn {
              margin-top: 24 * @rem;
              width: 100%;
              box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 12 * @rem;
            }
          }
        }
      }
    }
    .sort {
      position: absolute;
      margin-top: @safeAreaTop;
      margin-top: @safeAreaTopEnv;
      height: 50 * @rem;
      line-height: 50 * @rem;
      top: 0;
      font-weight: 600;
      font-size: 14 * @rem;
      color: #fff;
      z-index: 999;
    }
    .back {
      position: absolute;
      z-index: 999;
      margin-top: @safeAreaTop;
      margin-top: @safeAreaTopEnv;
      left: 14 * @rem;
      top: 0;
      width: 30 * @rem;
      height: 50 * @rem;
      background: url('~@/assets/images/nav-bar-back-white.png') center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
  }
}
</style>
