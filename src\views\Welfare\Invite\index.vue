<template>
  <div class="page invite-page">
    <nav-bar-2
      title="邀请好友"
      :border="false"
      :placeholder="true"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bg">
        <img src="@/assets/images/welfare/invite/invite-top-bg.png" alt="" />
      </div>
      <div class="section process-section">
        <div class="section-title">邀请流程</div>
        <div class="process">
          <div class="process-item">
            <img
              class="process-icon"
              src="@/assets/images/welfare/invite/process-1.png"
              alt=""
            />
            <div class="process-title">生成海报分享</div>
          </div>
          <div class="arrow"></div>
          <div class="process-item">
            <img
              class="process-icon"
              src="@/assets/images/welfare/invite/process-2.png"
              alt=""
            />
            <div class="process-title">好友注册登录</div>
            <div class="reward-gold">300金币</div>
          </div>
        </div>
        <div class="to-invite" @click="openQrCodePopup">去邀请赚金币</div>
        <div class="invite-rule-btn" @click="inviteRulePopup = true">
          邀请好友赚金币规则&gt;
        </div>
      </div>

      <div class="my-invite">
        <div class="my-list">
          <div class="my-item">
            <div class="num">{{ inviterData.count }}</div>
            <div class="title">邀请人数</div>
          </div>
          <div class="my-item">
            <div class="num">{{ inviterData.gold_total }}</div>
            <div class="title">金币奖励</div>
          </div>
        </div>
      </div>

      <div class="section record-section">
        <div class="section-title">邀请记录</div>
        <div class="record-list">
          <div class="record-title-bar">
            <div class="line-item">被邀请人</div>
            <div class="line-item">注册登录</div>
            <div class="line-item">邀请时间</div>
          </div>
          <template v-if="inviterData.list && inviterData.list.length">
            <div
              class="record-item"
              v-for="(item, index) in inviterData.list"
              :key="index"
            >
              <div class="line-item">{{ item.username }}</div>
              <div class="line-item">+{{ item.gold }}金币</div>
              <div class="line-item">{{ formatTime(item.create_time) }}</div>
            </div>
          </template>
          <content-empty
            style="padding-bottom: 30px"
            tips="-暂无数据-"
            v-else
          ></content-empty>
        </div>
      </div>
    </div>

    <!-- 邀请规则 -->
    <van-popup
      v-model="inviteRulePopup"
      position="bottom"
      :lock-scroll="false"
      round
      class="invite-rule-popup"
      :close-on-popstate="true"
    >
      <div class="close" @click="inviteRulePopup = false"></div>
      <div class="title">邀请好友赚金币规则</div>
      <div class="content">
        <div class="rule-title">参与方式</div>
        <div class="rule-desc">
          用户通过邀请好友功能，邀请好友注册并登录3733游戏盒。
        </div>
        <div class="rule-title">奖励标准</div>
        <div class="rule-desc"> 用户完成注册登录，奖励300金币/人。 </div>
        <div class="rule-title">参与规则</div>
        <div class="rule-desc">
          1.被邀请的设备需为首次安装3733游戏盒的设备；<br />
          2.好友账号需绑定手机号；<br />
          3.好友账号需是设备上注册的首个账号；<br />
          4.好友在模拟器注册或绑定虚拟手机号无效；<br />
          5.iOS好友需注册登录桌面端3733游戏盒；<br />
        </div>
        <div class="rule-title">其他说明</div>
        <div class="rule-desc">
          活动期间若出现违反法律法规、用户存在恶意刷单、虚假操作等作弊行为，我方有权收回用户所有收益。
        </div>
      </div>
      <div class="tips">活动期间如遇其他情况可联系在线客服进行咨询。</div>
    </van-popup>

    <!-- 邀请函弹窗 -->
    <van-popup
      v-model="qrCodePopup"
      position="center"
      :lock-scroll="false"
      round
      class="qr-code-popup"
      :close-on-popstate="false"
    >
      <div class="qr-code-content">
        <canvas id="canvas" style="display: none"></canvas>
        <div id="qrcode" ref="qrcode"></div>

        <div class="canvas-pic">
          <img :src="canvasPic" v-if="canvasPic" alt="" />
          <div class="ps">PS：若是保存失败，请截屏</div>
        </div>
      </div>
      <div class="operation-bar">
        <div class="operation-btn" v-if="isIos && iosSystem >= 16">
          长按二维码保存
        </div>
        <div class="operation-btn" @click="saveAsImage" v-else>
          保存海报图片
        </div>
        <div class="operation-btn" @click="handleCopy">复制链接邀请</div>
      </div>
      <div class="close" @click="qrCodePopup = false"></div>
    </van-popup>
  </div>
</template>
<script>
import { platform } from '@/utils/box.uni.js';
import { ApiUserInviteNew } from '@/api/views/users.js';
import qrCodeBg from '@/assets/images/welfare/invite/qrcode-bg.png';
import QRCode from 'qrcodejs2';
import { iosSystem, isIos } from '@/utils/userAgent.js';
import { mapGetters } from 'vuex';
export default {
  name: 'Invite',
  data() {
    return {
      iosSystem,
      isIos,

      inviterData: {},
      inviteUrl: '',

      inviteRulePopup: false,
      qrCodePopup: false,
      qrCodeCanvas: null,

      canvas: null,
      ctx: null,
      qrCode: null,
      canvasPic: '',
      shareTitle:
        '我在玩3733游戏，尊享独家福利！诚邀你同玩，一起开启赚金之旅！',
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  async created() {
    await this.getIndexData();
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  methods: {
    async onResume() {
      await this.SET_USER_INFO(true);
      await this.getIndexData();
    },
    async getIndexData() {
      const res = await ApiUserInviteNew();
      let { url, inviter_data } = res.data;
      this.inviteUrl = url;
      this.inviterData = inviter_data;
    },
    formatTime(temp) {
      const { year, month, day, time } = this.$handleTimestamp(temp);
      return `${year}/${month}/${day} ${time}`;
    },
    openQrCodePopup() {
      if (this.initData?.share_info?.length) {
        window.BOX.mobShare(4, this.userInfo.user_id);
        return false;
      }
      this.qrCodePopup = true;
      this.$nextTick(() => {
        if (!this.canvas) {
          this.initCanvas();
        }
      });
    },
    initCanvas() {
      this.canvas = document.getElementById('canvas');
      this.ctx = this.canvas.getContext('2d');

      let bg = new Image();
      bg.setAttribute('crossOrigin', 'anonymous');
      bg.src = qrCodeBg;
      this.createQrCode();
      let qrcodeImg = this.convertCanvasToImage(
        document.querySelector('#qrcode canvas'),
      );

      let p1 = new Promise(resolve => {
        qrcodeImg.onload = () => {
          resolve();
        };
      });

      let p2 = new Promise(resolve => {
        bg.onload = () => {
          resolve();
        };
      });

      Promise.all([p1, p2]).then(() => {
        this.qrCodeCanvas = document.querySelector('#qrcode canvas');
        this.canvas.width = 1011;
        this.canvas.height = 1260;
        this.ctx.drawImage(bg, 0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(
          qrcodeImg,
          this.qrCodeCanvas.width * 0.43,
          this.qrCodeCanvas.height * 0.8,
          this.qrCodeCanvas.width,
          this.qrCodeCanvas.height,
        );
        this.canvasPic = this.canvas.toDataURL('image/png');
      });
    },
    createQrCode() {
      new QRCode(this.$refs.qrcode, {
        text: this.inviteUrl, // 需要转换为二维码的内容
        width: 180 * 3,
        height: 180 * 3,
        colorDark: '#000000',
        colorLight: '#ffffff',
        render: 'canvas',
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    convertCanvasToImage(canvas) {
      var image = new Image();
      image.setAttribute('crossOrigin', 'anonymous');
      image.src = canvas.toDataURL('image/png');
      return image;
    },

    handleCopy() {
      this.$copyText(this.shareTitle + this.inviteUrl).then(
        res => {
          this.$toast(this.$t('链接已复制到剪贴板，快去邀请好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请联系客服'),
            lockScroll: false,
          });
        },
      );
    },
    saveAsImage() {
      this.$toast.loading('加载中');
      let img = this.canvas.toDataURL('image/png');
      let link = document.createElement('a');
      link.href = img;
      link.download = 'ewm.png'; // 自定义图片名称W
      link.click();
      this.$toast.clear();
      // this.canvas.toBlob((blob) => {
      //   let link = document.createElement('a');
      //       link.href = window.URL.createObjectURL(blob);
      //       link.download = 'ewm'; // 自定义图片名称
      //       link.click();
      // }, "image/jpeg")
    },
  },
};
</script>

<style lang="less" scoped>
.invite-page {
  font-size: 14 * @rem;
  min-height: 100vh;
  background: #ff4839;
  .main {
    padding-bottom: 40 * @rem;
    background-color: #fa6a69;
    .top-bg {
      width: 100%;
      height: 312 * @rem;
    }
    .section {
      width: 355 * @rem;
      background-color: #ffffff;
      border-radius: 15 * @rem;
      margin: 20 * @rem auto 0;
      position: relative;
      border-radius: 15 * @rem;
      overflow: hidden;
      &.process-section {
        margin: -24 * @rem auto 0;
      }
      .section-title {
        box-sizing: border-box;
        font-size: 15 * @rem;
        color: #ffffff;
        height: 36 * @rem;
        width: 355 * @rem;
        background: linear-gradient(180deg, #ffb670 0%, #ff7636 100%);
        display: flex;
        align-items: center;
        padding: 0 16 * @rem;
      }
      .process {
        display: flex;
        justify-content: center;
        padding-top: 20 * @rem;
        .process-item {
          .process-icon {
            width: 50 * @rem;
            height: 50 * @rem;
            margin: 0 auto;
          }
          .process-title {
            font-size: 12 * @rem;
            color: #910c0c;
            text-align: center;
            line-height: 15 * @rem;
            margin-top: 8 * @rem;
          }
          .reward-gold {
            margin-top: 2 * @rem;
            line-height: 14 * @rem;
            font-size: 11 * @rem;
            color: #eb3e2d;
            text-align: center;
          }
        }
        .arrow {
          width: 136 * @rem;
          height: 17 * @rem;
          background: url('~@/assets/images/welfare/invite/process-arrow.png')
            center center no-repeat;
          background-size: 16 * @rem 17 * @rem;
          margin: 19 * @rem 3 * @rem 0;
          position: relative;
          &::before {
            content: '';
            width: 54 * @rem;
            height: 2 * @rem;
            background: url('~@/assets/images/welfare/invite/process-line.png')
              center center no-repeat;
            background-size: 54 * @rem 2 * @rem;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0 * @rem;
          }
          &::after {
            content: '';
            width: 54 * @rem;
            height: 2 * @rem;
            background: url('~@/assets/images/welfare/invite/process-line.png')
              center center no-repeat;
            background-size: 54 * @rem 2 * @rem;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0 * @rem;
          }
        }
      }
      .to-invite {
        width: 168 * @rem;
        height: 48 * @rem;
        background: url('~@/assets/images/welfare/invite/to-invite-btn.png')
          center center no-repeat;
        background-size: 168 * @rem 48 * @rem;
        margin: 13 * @rem auto 0;
        text-align: center;
        line-height: 42 * @rem;
        font-size: 15 * @rem;
        color: #ffffff;
        font-weight: 600;
      }
      .invite-rule-btn {
        font-size: 11 * @rem;
        color: #eb3e2d;
        text-align: center;
        line-height: 14 * @rem;
        margin-top: 5 * @rem;
        text-decoration: underline;
        padding-bottom: 20 * @rem;
      }

      .record-list {
        padding: 16 * @rem 10 * @rem;
        min-height: 200 * @rem;
        .record-title-bar {
          height: 30 * @rem;
          display: flex;
          border-radius: 4 * @rem;
          overflow: hidden;
          background: #fff5f2;
          .line-item {
            height: 30 * @rem;
            width: 111 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 13 * @rem;
            color: #910c0c;
          }
        }
        .record-item {
          height: 40 * @rem;
          display: flex;
          .line-item {
            box-sizing: border-box;
            height: 40 * @rem;
            width: 111 * @rem;
            padding: 0 10 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 11 * @rem;
            color: rgba(145, 12, 12, 0.8);
          }
        }
      }
    }
    .my-invite {
      width: 355 * @rem;
      height: 183 * @rem;
      background: url('~@/assets/images/welfare/invite/my-invite-bg.png') center
        center no-repeat;
      background-size: 355 * @rem 183 * @rem;
      margin: 20 * @rem auto 0;
      .my-list {
        display: flex;
        padding-top: 75 * @rem;
        .my-item {
          width: 50%;
          position: relative;
          &:not(:first-of-type) {
            &::before {
              content: '';
              width: 0.5 * @rem;
              height: 52 * @rem;
              background: rgba(145, 12, 12, 0.2);
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }
          .num {
            font-size: 24 * @rem;
            line-height: 30 * @rem;
            font-weight: 600;
            text-align: center;
            color: #910c0c;
            white-space: nowrap;
          }
          .title {
            font-size: 13 * @rem;
            color: #910c0c;
            line-height: 16 * @rem;
            margin-top: 10 * @rem;
            text-align: center;
          }
        }
      }
    }
  }
}

.invite-rule-popup {
  box-sizing: border-box;
  padding: 18 * @rem 20 * @rem 24 * @rem;
  .close {
    position: absolute;
    right: 0;
    top: 0;
    width: 48 * @rem;
    height: 48 * @rem;
    background: url(~@/assets/images/recharge/svip-ad-close.png) center center
      no-repeat;
    background-size: 16 * @rem 16 * @rem;
  }
  .title {
    font-size: 16 * @rem;
    color: #111111;
    line-height: 22 * @rem;
    font-weight: 600;
    text-align: center;
  }
  .content {
    margin-top: 24 * @rem;
    .rule-title {
      font-size: 14 * @rem;
      color: #333333;
      line-height: 18 * @rem;
      font-weight: 600;
    }
    .rule-desc {
      font-size: 13 * @rem;
      color: #666666;
      line-height: 16 * @rem;
      margin-bottom: 20 * @rem;
      margin-top: 8 * @rem;
    }
  }
  .tips {
    font-size: 13 * @rem;
    color: @themeColor;
    line-height: 16 * @rem;
  }
}
.qr-code-popup {
  background: transparent;
  width: 337px;
  .qr-code-content {
    #canvas {
      width: 1011px;
      height: 1260px;
      margin: 0 auto;
    }
    #qrcode {
      width: 540px;
      height: 540px;
      margin: 0 auto;
      display: none;
    }
    .canvas-pic {
      display: block;
      width: 337px;
      height: 420px;
      margin: 0 auto;
      position: relative;
      .ps {
        font-size: 12px;
        color: #666666;
        position: absolute;
        bottom: 65px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .operation-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 26px;
    .operation-btn {
      margin: 0 8px;
      width: 160px;
      height: 44px;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #ffffff;
      font-weight: 600;
      background: linear-gradient(180deg, #ffa439 0%, #ff4839 100%);
    }
  }
  .close {
    width: 28px;
    height: 28px;
    margin: 38px auto 0;
    background: url('~@/assets/images/welfare/invite/qrcode-close.png') center
      center no-repeat;
    background-size: 28px 28px;
  }
}
</style>
