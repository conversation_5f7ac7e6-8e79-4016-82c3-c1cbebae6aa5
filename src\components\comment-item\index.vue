<template>
  <div class="comment-item">
    <user-avatar
      class="user-avatar"
      :src="info.user.avatar"
      :self="false"
    ></user-avatar>
    <div class="item-right">
      <div>
        <div class="user-bar">
          <div
            class="nickname"
            :class="{
              gold: info.user.is_svip,
              orange: info.user.is_official == 1,
            }"
          >
            {{ info.user.nickname }}
          </div>
          <div class="exp-level" v-if="info.user.exp_level_name">
            {{ info.user.exp_level_name }}
          </div>
          <div
            v-if="info.user.pay_level_name"
            class="pay-level"
            :style="{ backgroundColor: info.user.pay_level_color }"
          >
            {{ info.user.pay_level_name }}
          </div>
          <div v-if="info.top == 1" class="top-level">
            <div class="top-icon"></div>
            <div class="top-text">{{ $t('置顶') }}</div>
          </div>
        </div>
      </div>
      <div class="comment-info">
        <div class="comment-date">{{ $getDateDiff(info.create_time) }}</div>
      </div>
      <div class="comment-content">
        <div class="content-text">
          <span style="white-space: pre-wrap" v-html="showContent"></span
          ><span
            class="open-btn"
            v-if="formatEmoji(info.content).length > showLimit"
            @click.stop="toggle"
            ><span v-if="info.content.length > showLimit && !isOpen">...</span
            ><span v-if="!isOpen" class="open-text">{{ $t('展开') }}</span
            ><span v-else class="open-text">{{ $t('收起') }}</span></span
          >
        </div>
        <div class="content-pics" v-if="info.images">
          <div
            class="pic"
            v-for="(pic, picIndex) in info.images"
            :key="picIndex"
            @click="showBigImage(info.images, picIndex)"
          >
            <img :src="pic" alt="" />
          </div>
        </div>
      </div>
      <div class="bottom-bar">
        <div class="operation">
          <div
            class="user-like-btn"
            @click="clickLike(info)"
            :class="{ red: info.is_like }"
          >
            <div class="like-icon"></div>
            <div class="like-num">
              {{ info.support_count }}
            </div>
          </div>
          <div class="user-comment-btn" @click="clickComment">
            <div class="comment-icon"></div>
            <div class="comment-text"> 回复 </div>
          </div>
        </div>
      </div>

      <div class="reply-bar" v-if="showReplyList.length">
        <div class="reply-list">
          <template v-for="(reply, replyIndex) in showReplyList">
            <div class="reply-item" :key="replyIndex">
              <div class="reply-content-line">
                <div class="reply-user" v-if="!reply.reply_user">
                  {{ reply.user.nickname }}：
                </div>
                <div class="reply-content" v-else>
                  <div class="reply-user">{{ reply.user.nickname }}</div>
                  {{ $t('回复') }}
                  <div class="reply-user">
                    {{ reply.reply_user.nickname }}：
                  </div>
                </div>
                <div
                  class="reply-content"
                  :class="{ orange: reply.user.is_official == 1 }"
                  style="white-space: pre-wrap"
                  v-html="formatEmoji(reply.content)"
                ></div>
                <div
                  class="kefu-btn"
                  v-if="reply.kefu_type > 0"
                  @click.stop="connectKefu(reply.kefu_type)"
                >
                  点击联系客服&gt;&gt;
                </div>
              </div>

              <div class="bottom-bar">
                <div class="reply-date">
                  {{ formatDate(reply.create_time) }}
                </div>
                <div class="operation">
                  <div
                    class="user-like-btn"
                    @click="clickLike(reply)"
                    :class="{ red: reply.is_like == true }"
                  >
                    <div class="like-icon"></div>
                    <div class="like-num">
                      {{ reply.support_count }}
                    </div>
                  </div>
                  <div class="user-comment-btn" @click="clickReply(reply)">
                    <div class="comment-icon"></div>
                    <div class="comment-text"> 回复 </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <template v-if="info.reply_count > 1">
          <div class="reply-more" v-if="loading">{{ $t('加载中...') }}</div>
          <div class="reply-more" v-else-if="page == 1" @click="getReplyList()">
            {{ $t('共') }}{{ info.reply_count }}{{ $t('条回复') }}<i></i>
          </div>
          <div
            class="reply-more"
            v-else-if="!finished"
            @click="getReplyList(2)"
          >
            {{ $t('查看更多') }}<i></i>
          </div>
          <div class="reply-more" v-else @click="packUp">
            {{ $t('收起') }}<i class="rotate"></i>
          </div>
        </template>
      </div>
    </div>
    <van-popup v-model="isReplying" :lock-scroll="false" position="bottom">
      <div class="reply-editor" v-if="isReplying">
        <div class="reply-title">
          <div class="reply-type">
            {{ replyType == 1 ? $t('评论给：') : $t('正在回复：') }}
          </div>
          <div class="reply-user">{{ replyNickname }}</div>
        </div>
        <div class="input-container">
          <textarea
            id="inputText"
            class="input-text"
            v-model="replyInput"
            rows="10"
            :placeholder="$t('写两句你想说的话...')"
          ></textarea>
        </div>
        <div class="reply-operation">
          <div
            class="send btn"
            :class="{ on: replyInput.length > 0 }"
            @click="sendReply"
          >
            {{ $t('发送') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ImagePreview } from 'vant';
import {
  ApiResourceSupport,
  ApiCommentSubmit,
  ApiCommentReplies,
} from '@/api/views/comment.js';

export default {
  name: 'CommentItem',
  data() {
    return {
      isOpen: false,
      showLimit: 95, // 默认显示最大长度
      supportCount: 0,
      hadLike: false,
      replyInput: '', // 回复内容
      replyNickname: '', // 回复给谁
      isReplying: false,
      replyType: 1, // 1=评论 2=回复
      replyInfo: {}, // 评论、回复对象
      // 评论回复列表
      finished: false,
      loading: false,
      page: 1,
      listRows: 10,
      showReplyList: [],
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
    classId: {
      type: Number,
      default: 103, // 103-游戏评论 104-玩家推荐
    },
  },
  computed: {
    showContent() {
      let newContent = this.formatEmoji(this.info.content);
      if (!this.isOpen) {
        return newContent.length > this.showLimit
          ? newContent.substring(0, this.showLimit)
          : newContent;
      } else {
        return newContent;
      }
    },
    formatReplyInput() {
      return this.replyInput.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  created() {
    this.supportCount = this.info.support_count;
    if (this.info.replies && this.info.replies.length) {
      this.showReplyList = [this.info.replies[0]];
    }
  },
  methods: {
    connectKefu(kefu_type) {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      switch (Number(kefu_type)) {
        case 1:
          this.toPage('Kefu');
          break;
        case 2:
          this.openKefu();
          break;
        default:
          break;
      }
    },
    async getReplyList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
      }
      this.loading = true;
      try {
        const res = await ApiCommentReplies({
          page: this.page,
          listRows: this.listRows,
          commentId: this.info.comment_id,
          sourceId: this.info.source_id,
          classId: this.classId,
        });
        let { replies } = res.data;
        if (action === 1 || this.page === 1) {
          this.showReplyList = [];
          if (!replies.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.showReplyList.push(...replies);
        this.page++;
        if (replies.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } finally {
        this.$nextTick(() => {
          this.loading = false;
        });
      }
    },
    packUp() {
      this.page = 1;
      this.finished = false;
      this.showReplyList = [this.info.replies[0]];
    },
    // 点击评论
    clickComment() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.replyType = 2;
      this.replyNickname = this.info.user.nickname + this.$t('(作者)');
      this.replyInfo = {
        comment_id: this.info.reply_outer_id,
        reply_outer_id: this.info.comment_id,
      };
      this.isReplying = true;
      this.$nextTick(() => {
        document.querySelector('#inputText').focus();
      });
    },
    // 点击回复
    clickReply(reply) {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.replyType = 2;
      this.replyNickname = reply.user.nickname;
      this.replyInfo = reply;
      this.isReplying = true;
      this.$nextTick(() => {
        document.querySelector('#inputText').focus();
      });
    },
    async clickLike(info) {
      const res = await ApiResourceSupport({
        classId: this.classId,
        sourceId: info.comment_id,
      });
      if (!info.is_like) {
        if (!info.support_count) {
          info.support_count = res.data.count;
        } else {
          info.support_count += 1;
        }
        info.is_like = true;
      }
    },
    toggle() {
      this.isOpen = !this.isOpen;
    },
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    // 发送回复
    async sendReply() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.replyInput.length < 1) {
        this.$toast(this.$t('输入的内容太少'));
        this.isReplying = false;
        return false;
      }
      this.$toast.loading({
        message: this.$t('发送中...'),
      });
      let params = {
        sourceId: this.info.source_id,
        classId: this.classId,
        model: 'iPhone X',
        content: this.formatReplyInput,
        replyCommentId: this.replyInfo.reply_outer_id,
        replyOuterId: this.replyInfo.comment_id,
      };
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
      } finally {
        this.isReplying = false;
      }
    },
    formatEmoji(str) {
      var pattern = /\[emoji\:(.+?)\]/g;
      var result = str.replace(pattern, a => {
        let code = a.substring(1, a.length - 1).split(':')[1];
        let strs;
        if (code.length > 4) {
          strs =
            unescape('%u' + code.substr(0, 4)) +
            unescape('%u' + code.substr(4));
        } else {
          strs = unescape('%u' + code.substr(0, 4));
        }

        return strs;
      });
      return result;
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },

    formatDate(val) {
      let { year, date, time } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
  },
};
</script>

<style lang="less" scoped>
.comment-item {
  display: flex;
  padding: 20 * @rem 0;
  &:not(:last-of-type) {
    border-bottom: 0.5 * @rem solid #e8e8e8;
  }
  .user-avatar {
    width: 40 * @rem;
    height: 40 * @rem;
    border-radius: 50%;
    overflow: hidden;
    img {
      object-fit: cover;
    }
  }
  .item-right {
    flex: 1;
    min-width: 0;
    margin-left: 12 * @rem;
    .user-bar {
      display: flex;
      align-items: center;
      .nickname {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 600;
        &.gold {
          color: #f8ab31;
        }
        &.orange {
          color: @themeColor;
        }
      }
      .exp-level {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 2 * @rem;
        background-color: #309af8;
        padding: 0 5 * @rem;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #ffffff;
        margin-left: 8 * @rem;
      }
      .pay-level {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 2 * @rem;
        background-color: #ed9239;
        padding: 0 5 * @rem;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #ffffff;
        margin-left: 6 * @rem;
      }
      .top-level {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 2 * @rem;
        background-color: #f34a4a;
        padding: 0 5 * @rem;
        margin-left: 6 * @rem;
        .top-icon {
          width: 8 * @rem;
          height: 8 * @rem;
          .image-bg('~@/assets/images/games/top-icon.png');
        }
        .top-text {
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
          margin-left: 2 * @rem;
        }
      }
    }
    .comment-info {
      .comment-date {
        font-size: 12 * @rem;
        color: #797979;
        margin-top: 5 * @rem;
      }
    }
    .comment-content {
      overflow: hidden;
      .content-text {
        font-size: 12 * @rem;
        color: #111111;
        font-weight: 500;
        line-height: 18 * @rem;
        margin-top: 11 * @rem;
        overflow: hidden;
        // text-align: justify;
        .open-btn {
          .open-text {
            color: @themeColor;
            font-size: 13 * @rem;
            margin-left: 4 * @rem;
          }
        }
      }
      .content-pics {
        display: flex;
        flex-wrap: wrap;
        margin-top: 5 * @rem;
        .pic {
          width: 80 * @rem;
          height: 80 * @rem;
          border-radius: 4 * @rem;
          overflow: hidden;
          &:not(:nth-of-type(-n + 3)) {
            margin-top: 5 * @rem;
          }
          &:not(:nth-of-type(3n + 1)) {
            margin-left: 5 * @rem;
          }
          img {
            object-fit: cover;
          }
        }
      }
    }
    .reply-bar {
      background-color: #f7f7f7;
      border-radius: 6 * @rem;
      padding: 5 * @rem 10 * @rem 10 * @rem;
      margin-top: 10 * @rem;
      .reply-list {
        .reply-item {
          font-size: 12 * @rem;
          color: #000000;
          font-weight: 400;
          line-height: 18 * @rem;
          margin-top: 10 * @rem;
          &:not(:first-of-type) {
            margin-top: 14 * @rem;
          }

          .bottom-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 7 * @rem;
            .reply-date {
              font-size: 11 * @rem;
              color: #c0c0c0;
            }
            .operation {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              .user-comment-btn {
                display: flex;
                align-items: center;
                .comment-icon {
                  width: 14 * @rem;
                  height: 14 * @rem;
                  .image-bg('~@/assets/images/games/user-comment.png');
                }
                .comment-text {
                  font-size: 11 * @rem;
                  color: #9a9a9a;
                  font-weight: 400;
                  margin-left: 6 * @rem;
                  margin-top: 1 * @rem;
                }
              }
              .user-like-btn {
                display: flex;
                align-items: center;
                margin-right: 12 * @rem;
                .like-icon {
                  width: 14 * @rem;
                  height: 14 * @rem;
                  .image-bg('~@/assets/images/games/user-like.png');
                }
                .like-num {
                  font-size: 11 * @rem;
                  color: #9a9a9a;
                  font-weight: 400;
                  margin-left: 6 * @rem;
                  margin-top: 1 * @rem;
                }
                &.red {
                  .like-icon {
                    background-image: url(~@/assets/images/games/user-like-on.png);
                  }
                  .like-num {
                    color: @themeColor;
                  }
                }
              }
            }
          }
          .reply-user {
            flex-shrink: 0;
            color: @themeColor;
            display: inline;
          }
          .reply-content {
            color: #000000;
            display: inline;
            &.orange {
              color: @themeColor;
            }
          }
          .kefu-btn {
            display: block;
            color: #43addb;
            text-decoration: underline;
            font-size: 12 * @rem;
            font-weight: bold;
          }
        }
      }
      .reply-more {
        display: flex;
        align-items: center;
        font-size: 12 * @rem;
        color: @themeColor;
        font-weight: 400;
        margin-top: 6 * @rem;
        padding-right: 12 * @rem;
        i {
          display: block;
          width: 10 * @rem;
          height: 10 * @rem;
          background: url('~@/assets/images/games/right-arrow.png') right center
            no-repeat;
          background-size: 9 * @rem 9 * @rem;
          margin-left: 4 * @rem;
          &.rotate {
            transform: rotateZ(-90deg);
          }
        }
      }
    }
    .bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5 * @rem;
      .operation {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-left: auto;
        .user-comment-btn {
          display: flex;
          align-items: center;
          .comment-icon {
            width: 17 * @rem;
            height: 17 * @rem;
            .image-bg('~@/assets/images/games/user-comment.png');
          }
          .comment-text {
            font-size: 13 * @rem;
            color: #9a9a9a;
            font-weight: 400;
            margin-left: 6 * @rem;
            margin-top: 3 * @rem;
          }
        }
        .user-like-btn {
          display: flex;
          align-items: center;
          margin-right: 13 * @rem;
          .like-icon {
            width: 17 * @rem;
            height: 17 * @rem;
            .image-bg('~@/assets/images/games/user-like.png');
          }
          .like-num {
            font-size: 13 * @rem;
            color: #9a9a9a;
            font-weight: 400;
            margin-left: 6 * @rem;
            margin-top: 3 * @rem;
          }
          &.red {
            .like-icon {
              background-image: url(~@/assets/images/games/user-like-on.png);
            }
            .like-num {
              color: @themeColor;
            }
          }
        }
      }
    }
  }
}
.reply-editor {
  width: 100%;
  height: 160 * @rem;
  background-color: #fff;
  .reply-title {
    box-sizing: border-box;
    height: 40 * @rem;
    background-color: #f5f5f6;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    font-size: 13 * @rem;
    color: #797979;
    font-weight: 400;
    .reply-user {
      font-size: 13 * @rem;
      color: #000000;
      font-weight: 500;
    }
  }
  .input-container {
    box-sizing: border-box;
    width: 100%;
    height: 82 * @rem;
    .input-text {
      box-sizing: border-box;
      display: block;
      width: 100%;
      height: 100%;
      padding: 12 * @rem;
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
      line-height: 20 * @rem;
      border: 0;
      outline: none;
    }
  }
  .reply-operation {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 12 * @rem;
    .send {
      width: 56 * @rem;
      height: 28 * @rem;
      border-radius: 14 * @rem;
      background-color: #c1c1c1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #ffffff;
      font-weight: 400;
      &.on {
        background-color: @themeColor;
      }
    }
  }
}
</style>
