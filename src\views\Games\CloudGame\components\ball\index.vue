<template>
  <div
    ref="deviceInfo"
    @touchstart.stop="touchstart()"
    @touchmove.stop="touchmove()"
    @touchend.stop="touchend()"
    :class="{
      column: !gamePortrait && (orientation === 0 || orientation === 180),
    }"
    class="ball"
  >
    <div
      :class="{ red: networkDelay > 100 }"
      @click="ballCollapse"
      class="item network-delay"
    >
      <i class="icon"></i>
      <div class="text">{{ networkDelay }}ms</div>
    </div>
    <div @click="qulityListShow = !qulityListShow" class="item quality">
      <i class="icon"></i>
      <div class="text">{{ qualityText }}</div>
      <div v-if="qulityListShow" class="quality-list">
        <div
          v-for="(item, index) in qualityList"
          :key="index"
          @click="changeQuality(item)"
          class="quality-item"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="item close" @click="exit()">
      <i class="icon"></i>
      <div class="text">{{ $t('退出') }}</div>
    </div>
    <!-- <i @click="ballCollapse()" class="icon arrow"></i> -->
  </div>
</template>
<script>
export default {
  name: 'Ball',
  props: {
    networkDelay: {
      // 网络延迟
      type: Number,
      default: '',
    },
    gamePortrait: {
      type: Number,
      default: 1,
    },
    qualityList: {
      type: Array,
      default: [],
    },
    selectedQuality: {
      type: Number,
      default: 0,
    },
    orientation: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      flags: false, //是否允许移动
      position: { x: 0, y: 0 }, //小球目前坐标
      nx: '', //本次手指横线移动的距离
      ny: '', //本次手指纵向移动的距离
      dx: '', //和(0,0)当前的横向坐标
      dy: '', //和(0,0)当前的纵向坐标
      xPum: '', //小球移动后目前横坐标
      yPum: '', //小球移动后目前纵坐标
      collapse: 1, //0展开 1折叠
      qulityListShow: false, //画质列表
    };
  },
  computed: {
    qualityText() {
      return this.selectedQuality != 0
        ? this.qualityList[this.selectedQuality - 1].name
        : this.$t('画质');
    },
  },
  watch: {
    gamePortrait() {
      this.initBallPosition();
    },
    orientation() {
      this.initBallPosition();
    },
  },
  mounted() {
    this.initBallPosition();
  },
  methods: {
    initBallPosition() {
      let moveDiv = this.$refs.deviceInfo;
      if (this.orientation === 90 || this.orientation === -90) {
        moveDiv.style.left = '20px';
        moveDiv.style.top = '20px';
        this.position = { x: 20, y: 20 };
      } else if (
        this.gamePortrait === 1 &&
        (this.orientation === 0 || this.orientation === 180)
      ) {
        moveDiv.style.left = '20px';
        moveDiv.style.top = `40px`;
        this.position = { x: 20, y: 40 };
      } else if (
        this.gamePortrait === 0 &&
        (this.orientation === 0 || this.orientation === 180)
      ) {
        moveDiv.style.left = `${
          window.screen.width -
          moveDiv.offsetWidth / 2 -
          moveDiv.offsetHeight / 2 -
          20
        }px`;
        moveDiv.style.top = `${40 + moveDiv.offsetWidth / 2}px`;
        this.position = {
          x:
            window.screen.width -
            moveDiv.offsetWidth / 2 -
            moveDiv.offsetHeight / 2,
          y: 40 + moveDiv.offsetWidth / 2,
        };
      }
    },
    touchstart() {
      const moveDiv = this.$refs.deviceInfo;
      this.flags = true;
      let touch;
      if (event.touches) {
        touch = event.touches[0]; //获取当前事件
      } else {
        touch = event;
      }
      this.position.x = touch.clientX; //获取手指位置坐标
      this.position.y = touch.clientY;
      this.dx = moveDiv.offsetLeft; //获取小球相对于离它最近的（指包含层级上的最近）包含小球的定位元素或者最近的 table,td,th,body元素左部内边距的距离
      this.dy = moveDiv.offsetTop;
    },
    touchmove() {
      const moveDiv = this.$refs.deviceInfo;
      if (this.flags) {
        let touch;
        if (event.touches) {
          touch = event.touches[0];
        } else {
          touch = event;
        }
        this.nx = touch.clientX - this.position.x; //手指横向移动距离
        this.ny = touch.clientY - this.position.y;
        this.xPum = this.dx + this.nx; //计算小球移动后的位置
        this.yPum = this.dy + this.ny;
        if (
          this.gamePortrait === 1 &&
          (this.orientation === 0 || this.orientation === 180)
        ) {
          // 游戏是竖屏且手机是竖直
          if (this.yPum < 40) {
            this.yPum = 40;
          } else if (
            this.yPum >
            window.screen.height - moveDiv.offsetHeight - 40
          ) {
            this.yPum = window.screen.height - moveDiv.offsetHeight - 40;
          }
          if (this.xPum < 0) {
            this.xPum = 0;
          } else if (this.xPum > window.screen.width - moveDiv.offsetWidth) {
            this.xPum = window.screen.width - moveDiv.offsetWidth;
          }
        } else if (
          this.gamePortrait === 0 &&
          (this.orientation === 0 || this.orientation === 180)
        ) {
          // 游戏是横屏且手机是竖直
          if (this.yPum < 40 + moveDiv.offsetWidth / 2) {
            this.yPum = 40 + moveDiv.offsetWidth / 2;
          } else if (
            this.yPum >
            window.screen.height -
              (moveDiv.offsetHeight + 40 + moveDiv.offsetWidth / 2)
          ) {
            this.yPum =
              window.screen.height -
              (moveDiv.offsetHeight + 40 + moveDiv.offsetWidth / 2);
          }
          if (
            this.xPum <
            0 - moveDiv.offsetWidth / 2 + moveDiv.offsetHeight / 2
          ) {
            this.xPum = 0 - moveDiv.offsetWidth / 2 + moveDiv.offsetHeight / 2;
          } else if (
            this.xPum >
            window.screen.width -
              moveDiv.offsetWidth / 2 -
              moveDiv.offsetHeight / 2
          ) {
            this.xPum =
              window.screen.width -
              moveDiv.offsetWidth / 2 -
              moveDiv.offsetHeight / 2;
          }
        } else if (this.gamePortrait === 0 && this.orientation === 90) {
          // 游戏是横屏且手机是90度
          if (this.yPum < 20) {
            this.yPum = 20;
          } else if (this.yPum > window.screen.width - 60) {
            this.yPum = window.screen.width - 60;
          }
          if (this.xPum < 0) {
            this.xPum = 0;
          } else if (this.xPum > window.screen.height - moveDiv.offsetWidth) {
            this.xPum = window.screen.height - moveDiv.offsetWidth;
          }
        } else if (this.gamePortrait === 0 && this.orientation === -90) {
          // 游戏是横屏且手机是180度
          if (this.yPum < 20) {
            this.yPum = 20;
          } else if (this.yPum > window.screen.width - 60) {
            this.yPum = window.screen.width - 60;
          }
          if (this.xPum < 0) {
            this.xPum = 0;
          } else if (this.xPum > window.screen.height - moveDiv.offsetWidth) {
            this.xPum = window.screen.height - moveDiv.offsetWidth;
          }
        }
        moveDiv.style.left = this.xPum + 'px'; //将计算得到的位置赋值给style
        moveDiv.style.top = this.yPum + 'px';
      }
    },
    //鼠标释放时候的函数
    touchend() {
      const moveDiv = this.$refs.deviceInfo;
      // if (this.gamePortrait && (this.orientation===0||this.orientation===180)) {
      //   if (
      //     this.xPum / (window.screen.width / 2 - moveDiv.offsetWidth / 2) >=
      //     1
      //   ) {
      //     this.xPum = window.screen.width - moveDiv.offsetWidth;
      //   } else {
      //     this.xPum = 0;
      //   }
      // }
      moveDiv.style.left = this.xPum + 'px';
      this.flags = false;
    },
    exit() {
      this.$emit('closePopup');
    },
    changeQuality(item) {
      this.$emit('changeQuality', item.id);
    },
    ballCollapse() {},
  },
};
</script>
<style lang="less" scoped>
.ball {
  position: absolute;
  left: 0;
  z-index: 99;
  width: 156 * @rem;
  height: 38 * @rem;
  box-sizing: border-box;
  display: flex;
  padding: 3 * @rem 8 * @rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 18 * @rem;
  touch-action: none; /*防止谷歌浏览器监听touch事件时报错*/
  animation: light 2s ease-in-out infinite alternate;
  &.column {
    top: calc(70 * @rem + @safeAreaBottom);
    top: calc(70 * @rem + @safeAreaBottomEnv);
    transform: rotate(90deg);
  }
  .icon {
    display: block;
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .item {
    flex: 0 0 45 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    // margin: 0 5 * @rem 0 0;
    &.close {
      .icon {
        width: 14 * @rem;
        height: 14 * @rem;
        background-image: url(~@/assets/images/cloud-game/exit.png);
      }
    }
    &.quality {
      position: relative;
      .icon {
        width: 14 * @rem;
        height: 14 * @rem;
        background-image: url(~@/assets/images/cloud-game/quality.png);
      }
      .quality-list {
        position: absolute;
        top: 35 * @rem;
        left: 0;
        width: 100%;
        box-sizing: border-box;
        padding: 3 * @rem 8 * @rem;
        background-color: rgba(0, 0, 0, 0.8);
        color: #fff;
        border-radius: 5 * @rem;
        animation: light 2s ease-in-out infinite alternate;
        .quality-item {
          height: 30 * @rem;
          text-align: center;
          line-height: 30 * @rem;
        }
      }
    }
    &.network-delay {
      .icon {
        width: 15.6 * @rem;
        height: 14 * @rem;
        background-image: url(~@/assets/images/cloud-game/xinhao.png);
      }
      &.red {
        .icon {
          background-image: url(~@/assets/images/cloud-game/xinhao-red.png);
        }
      }
    }
    .text {
      margin-top: 2 * @rem;
      font-size: 12 * @rem;
    }
  }
  .icon.arrow {
    flex: 0 0 23 * @rem;
    width: 23 * @rem;
    height: 32 * @rem;
    background-size: 11 * @rem 18 * @rem;
    background-position: 8 * @rem 8 * @rem;
    background-image: url(~@/assets/images/cloud-game/back.png);
  }
}
// .ball.collapse {
//   width: 38*@rem;
//   .item {
//     display: none;
//   }
// }

@keyframes light {
  from {
    box-shadow: 0 0 4px #1f8cfa;
  }
  to {
    box-shadow: 0 0 20px #1f8cfa;
  }
}
</style>
