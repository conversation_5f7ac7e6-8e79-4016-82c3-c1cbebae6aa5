<template>
  <div class="page select-xiaoxiao-page">
    <nav-bar-2 :title="$t('选择小号')" :border="true"></nav-bar-2>
    <div class="main">
      <yy-list
        class="yy-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="game">
          <div class="game-icon">
            <img :src="xiaohaoSellInfo.game_icon" alt="" />
          </div>
          <div class="game-name">{{ xiaohaoSellInfo.game_name }}</div>
        </div>
        <div class="xiaohao-list">
          <div
            class="xiaohao-item"
            v-for="(item, index) in xiaohaoList"
            :key="index"
            @click="selectXiaohao(item)"
          >
            <div class="left">
              <div class="title">{{ $t('小号') }}：{{ item.nickname }}</div>
              <div class="desc">
                {{ $t('该小号已创建') }}{{ item.xh_days }}{{ $t('天') }}
              </div>
              <div class="xh-id">{{ $t('小号ID') }}：{{ item.xh_id }}</div>
            </div>
            <div class="right">
              <div class="right-top">
                <div class="platforms">
                  <div
                    class="plat"
                    v-for="(plat, platIndex) in item.platforms"
                    :key="platIndex"
                  >
                    <img :src="plat.icon" alt="" />
                  </div>
                </div>
                <div
                  class="status"
                  :style="{ color: `${item.status_info.color}` }"
                >
                  {{ item.status_info.str }}
                </div>
              </div>
              <div class="price">{{ $t('实际充值') }}：{{ item.paySum }}元</div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiXiaohaoGetXhList, ApiXiaohaoPaySum } from '@/api/views/xiaohao.js';
import { mapMutations, mapGetters } from 'vuex';
export default {
  name: 'SelectXiaohao',
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      xiaohaoList: [],
      page: 1,
      listRows: 10,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },

  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    async selectXiaohao(item) {
      // if (Number(item.paySum) < 10) {
      //   this.$toast(this.$t("该小号的充值金额不足10元"));
      //   return;
      // }
      const res = await ApiXiaohaoPaySum({
        xhId: item.id,
        type: 1,
      });
      let xiaohaoInfo = {
        nickname: item.nickname,
        platforms: item.platforms,
        pay_sum: item.paySum,
        xh_id: item.id,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...xiaohaoInfo });
      this.$router.go(-1);
    },
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      try {
        let res = await ApiXiaohaoGetXhList({
          appId: this.xiaohaoSellInfo.app_id,
          page: this.page,
          listRows: this.listRows,
        });
        if (action === 1 || this.page === 1) {
          this.xiaohaoList = [];
        }
        this.xiaohaoList.push(...res.data.list);
        if (res.data.list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (e) {
        this.finished = true;
      }
    },
    async onRefresh() {
      await this.getXiaohaoList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.xiaohaoList.length) {
        await this.getXiaohaoList();
      } else {
        await this.getXiaohaoList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.select-xiaoxiao-page {
  .main {
    box-sizing: border-box;
    min-width: 0;
    padding: 17 * @rem 14 * @rem 0;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    .game {
      display: flex;
      align-items: center;
      .game-icon {
        width: 28 * @rem;
        height: 28 * @rem;
        border-radius: 6 * @rem;
        overflow: hidden;
        background-color: #bfbfbf;
      }
      .game-name {
        font-size: 16 * @rem;
        color: #333333;
        margin-left: 8 * @rem;
      }
    }
    .xiaohao-list {
      .xiaohao-item {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #eeeeee;
        padding: 15 * @rem 0;
        .left {
          font-size: 13 * @rem;
          color: #666666;
          .desc {
            margin: 6 * @rem 0;
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          .right-top {
            display: flex;
            align-items: center;
            .platforms {
              display: flex;
              align-items: center;
              .plat {
                width: 17 * @rem;
                height: 17 * @rem;
                margin-left: 8 * @rem;
              }
            }
            .status {
              margin-left: 11 * @rem;
              font-size: 12 * @rem;
            }
          }
          .price {
            margin-top: 20 * @rem;
            font-size: 14 * @rem;
            color: #ff8f00;
          }
        }
      }
    }
  }
}
</style>
