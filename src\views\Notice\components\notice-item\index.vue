<template>
  <div class="notice-item" @click="clickNotice">
    <div class="top-content" v-if="info.gamename">
      <div class="icon">
        <img :src="info.gamepic" alt="" />
      </div>
      <div class="title">{{ info.gamename }}</div>
    </div>
    <div class="top-content" v-else>
      <div class="icon">
        <img src="@/assets/images/notice/notice-icon.png" alt="" />
      </div>
      <div class="title">{{ info.title }}</div>
    </div>

    <div
      class="desc"
      v-if="[441, 442, 443, 444].includes(info.type)"
      v-html="info.desc"
    ></div>
    <div class="desc" v-else>{{ info.desc }}</div>
    <div class="cdk" v-if="[410, 420].includes(info.type) && info.extra">
      <div class="cdk-text">礼包码：{{ info.extra }}</div>
      <div class="copy">复制</div>
    </div>
    <div class="bottom-content">
      <div class="time">{{ info.create_time | formatTime }}</div>
      <div class="to-detail" v-if="noticeCode[parseInt(info.type)]">
        {{ $t('详情') }}<span></span>
      </div>
    </div>
    <div class="dot" v-if="info.read == false"></div>
  </div>
</template>

<script>
import { handleTimestamp } from '@/utils/datetime.js';
import noticeCode from '@/utils/noticeCode.js';
import { ApiUserPushMsgRead } from '@/api/views/users.js';
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      noticeCode,
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time } = handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
  },
  methods: {
    clickNotice() {
      ApiUserPushMsgRead({ pushRecordId: this.info.id });
      this.info.read = true;
      if ([410, 420].includes(this.info.type) && this.info.extra) {
        this.$copyText(this.info.extra).then(
          res => {
            this.$toast(this.$t('复制成功'));
          },
          err => {
            this.$dialog.alert({
              message: this.$t('复制失败，请手动复制'),
              lockScroll: false,
            });
          },
        );
        return false;
      }
      switch (parseInt(this.info.type)) {
        case 36:
          const regex = /equip_id=(\d+)/;
          const equip_id_match = regex.exec(this.info.extra);
          if (equip_id_match && equip_id_match[1]) {
            this.toPage(noticeCode[parseInt(this.info.type)], {
              equip_id: Number(equip_id_match[1]), // 云挂机设备id
            });
          } else {
            this.toPage(noticeCode[parseInt(this.info.type)]);
          }
          break;
        case 5: // 5是外部浏览器（相对于Ifame用location.href）
          window.location.href = this.info.extra;
          break;
        case 4: // 4是内部webview, 如果是新版活动的话要用路由跳转Activity页面
          // activity活动通知跳转
          if (this.info.extra.indexOf('activity.3733.com') > -1) {
            this.toPage('Activity', { url: this.info.extra });
            return false;
          }
          // 平台币通知跳转
          if (this.info.extra.indexOf('/#/platform_coin') > -1) {
            this.toPage('PlatformCoin');
            return false;
          }
          // 游戏专服活动通知跳转
          if (this.info.extra.indexOf('/#/exclusive_activity/') > -1) {
            this.toPage('ExclusiveActivity', {
              id: this.info.extra.split('exclusive_activity/')[1],
            });
            return false;
          }
          // 其他url通知跳转
          this.toPage(noticeCode[parseInt(this.info.type)], {
            url: this.info.extra, // iframe url
            title: this.info.title, // iframe title
          });
          break;
        case 440: // 付费专服活动
          this.toPage(noticeCode[parseInt(this.info.type)], {
            id: this.info.extra, // 活动id
          });
          break;

        default:
          this.toPage(noticeCode[parseInt(this.info.type)], {
            id: this.info.extra, // 游戏详情id
            url: this.info.extra, // iframe url
            title: this.info.title, // iframe title
            type: this.info.type, // 砍价是需要type
          });
          break;
      }
    },
    envFun() {
      let url = window.location.href;
      let arr = url.split('.');
      if (arr[0].indexOf('aa') > -1) {
        return 'aa';
      } else if (arr[0].indexOf('cc') > -1) {
        return 'cc';
      } else {
        return '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.notice-item {
  box-sizing: border-box;
  width: 351 * @rem;
  margin: 12 * @rem auto 0;
  background-color: #fff;
  border-radius: 12 * @rem;
  padding: 15 * @rem 18 * @rem 12 * @rem;
  position: relative;
  .dot {
    position: absolute;
    width: 5 * @rem;
    height: 5 * @rem;
    border-radius: 50%;
    background-color: #fe4a55;
    right: 18 * @rem;
    top: 15 * @rem;
  }
  .top-content {
    display: flex;
    align-items: center;
    .icon {
      width: 28 * @rem;
      height: 28 * @rem;
      overflow: hidden;
      border-radius: 6 * @rem;
    }
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: bold;
      margin-left: 8 * @rem;
      flex: 1;
      min-width: 0;
      text-align: left;
    }
  }
  .desc {
    font-size: 14 * @rem;
    color: #000;
    line-height: 22 * @rem;
    margin-top: 10 * @rem;
    text-align: left;
    word-break: break-word;
  }
  .cdk {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    line-height: 22 * @rem;
    background-color: #f5f5f5;
    border-radius: 4 * @rem;
    padding: 5 * @rem 8 * @rem;
    margin-top: 10 * @rem;
    .cdk-text {
      margin-right: 8 * @rem;
      font-size: 13 * @rem;
      color: @themeColor;
    }
    .copy {
      width: 32 * @rem;
      height: 18 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10 * @rem;
      color: #ffffff;
      background-color: @themeColor;
      border-radius: 9 * @rem;
    }
  }
  .bottom-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4 * @rem;
    .time {
      font-size: 12 * @rem;
      color: #9a9a9a;
      line-height: 22 * @rem;
    }
    .to-detail {
      font-size: 12 * @rem;
      color: #9a9a9a;
      display: flex;
      align-items: center;
      span {
        display: block;
        width: 6 * @rem;
        height: 9 * @rem;
        .image-bg('~@/assets/images/notice/right-arrow.png');
        margin-left: 5 * @rem;
      }
    }
  }
}
</style>
