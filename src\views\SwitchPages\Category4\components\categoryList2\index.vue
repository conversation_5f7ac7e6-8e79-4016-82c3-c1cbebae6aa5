<template>
  <div class="category-list2">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
      <content-empty v-if="empty"></content-empty>
      <van-list
        v-else
        v-model="loading"
        :finished="finished"
        :finished-text="$t('没有更多了')"
        :offset="50"
        @load="loadMore()"
      >
        <template v-for="(item, index) in game_list">
          <div
            v-if="item.classid == 40 || item.classid == 41"
            :key="index"
            class="item-container2"
            :class="`item-${index + 1}`"
          >
            <game-item-3
              class="game-item"
              :isShowTo="false"
              :gameInfo="item"
            ></game-item-3>
          </div>
          <div
            v-else
            :key="'item' + index"
            class="item-container"
            :class="`item-${index + 1}`"
          >
            <div class="item-content">
              <div class="game-index">{{ index + 1 }}</div>
              <game-item-4 :gameInfo="item" class="game-item"></game-item-4>
            </div>
          </div>
        </template>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { ApiGameIndexV1 } from '@/api/views/game.js';
import { isIos } from '@/utils/userAgent';
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    classId: {
      type: Number,
    },
    //up分类页用的theme
    up_category: {
      type: Number || String,
      default: 0,
    },
  },
  data() {
    return {
      game_list: [],
      finished: false,
      loading: false,
      reloading: false,
      refreshing: false,
      page: 1,
      empty: false,
    };
  },
  watch: {
    async info(val, oldVal) {
      if (val && val != oldVal) {
        this.finished = false;
        this.page = 1;
        this.game_list = [];
        this.loading = true;
        await this.getGameList();
        this.loading = false;
      }
    },
    async up_category(val, oldVal) {
      if (val != oldVal) {
        this.finished = false;
        this.page = 1;
        this.game_list = [];
        this.loading = true;
        await this.getGameList();
        this.loading = false;
      }
    },
  },
  methods: {
    async getGameList() {
      this.empty = false;
      let data = {
        page: this.page,
        listRows: 10,
        classId: this.classId,
      };
      if (this.info.type == 1) {
        data.type = this.info.old_id;
      }
      if (this.info.type == 2) {
        data.theme = this.info.old_id;
      }
      if (this.classId == 40 && this.up_category > 0) {
        data.theme = this.up_category;
      }

      if (this.classId == 40 && this.up_category == 0) {
        if (isIos) {
          data.classId = 40;
        } else {
          data.classId = 10007;
        }
      }
      const res = await ApiGameIndexV1(data);
      if (this.page === 1) this.game_list = [];
      this.game_list.push(...res.data.list);
      if (!this.game_list.length) {
        this.empty = true;
      }

      if (res.data.list.length < 10) {
        this.finished = true;
      } else {
        this.finished = false;
      }
    },
    async onChange() {
      this.game_list = [];
      this.page = 1;
      this.finished = false;
      this.loading = true;
      await this.getGameList();
      this.loading = false;
      this.$toast.clear();
    },
    async onRefresh() {
      this.page = 1;
      await this.getGameList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getGameList();
      this.loading = false;
      this.page++;
    },
  },
};
</script>
<style lang="less" scoped>
.category-list2 {
  display: flex;
  height: 100%;
  .container {
    display: flex;
    flex-flow: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
    /deep/ .load-more {
      // overflow-y: scroll;
    }
  }
  .list {
    flex: 1;
    // overflow-y: scroll;
    // -webkit-overflow-scrolling: touch;
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .item-container {
    margin: 10 * @rem 5 * @rem 10 * @rem 21 * @rem;
    border-radius: 16 * @rem;
    &.item-1 {
      background: linear-gradient(270deg, #ff7156 0%, #ffb053 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-1.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          .game-icon {
            box-sizing: border-box;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                border-width: 0.5 * @rem;
                color: #fff;
                border-color: #fff;
              }
            }
            .info-line {
              .item-info {
                color: #fff;
                &.main-color {
                  color: #fff;
                }
                &.cate-type:not(:first-of-type)::before {
                  color: #fff;
                }
                &.discount-tag {
                  .discount-text {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
                &.tag {
                  background-color: rgba(255, 255, 255, 0.85);

                  &.blue-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                  &.orange-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
              }
            }
            .info-center {
              .game-hot {
                color: #fff;
              }

              .types .type {
                color: #fff;

                &:not(:first-child):before {
                  background-color: #fff;
                }
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
              .tags {
                .tag {
                  .tag-name {
                    font-weight: bold;
                  }
                }
              }
              .score::before {
                .image-bg('~@/assets/images/category/star-2.png');
              }
            }
            .info-bottom {
              .discount-text {
                background-color: #fff;
              }
            }
          }
        }
      }
    }
    &.item-2 {
      background: linear-gradient(270deg, #738ac0 0%, #74a4cd 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-2.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          padding: 8 * @rem 0;
          .game-icon {
            box-sizing: border-box;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                border-width: 0.5 * @rem;
                color: #fff;
                border-color: #fff;
              }
            }
            .info-line {
              .item-info {
                color: #fff;
                &.main-color {
                  color: #fff;
                }
                &.cate-type:not(:first-of-type)::before {
                  color: #fff;
                }
                &.discount-tag {
                  .discount-text {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
                &.tag {
                  background-color: rgba(255, 255, 255, 0.85);

                  &.blue-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                  &.orange-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
              }
            }
            .info-center {
              .game-hot {
                color: #fff;
              }

              .types .type {
                color: #fff;

                &:not(:first-child):before {
                  background-color: #fff;
                }
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
              .tags {
                .tag {
                  .tag-name {
                    font-weight: bold;
                  }
                }
              }
            }
            .info-bottom {
              .discount-text {
                background-color: #fff;
              }
            }
            .score::before {
              .image-bg('~@/assets/images/category/star-2.png');
            }
          }
        }
      }
    }
    &.item-3 {
      background: linear-gradient(270deg, #d99d64 0%, #f0ca9e 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-3.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          padding: 8 * @rem 0;
          .game-icon {
            box-sizing: border-box;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                border-width: 0.5 * @rem;
                color: #fff;
                border-color: #fff;
              }
            }
            .info-line {
              .item-info {
                color: #fff;
                &.main-color {
                  color: #fff;
                }
                &.cate-type:not(:first-of-type)::before {
                  color: #fff;
                }
                &.discount-tag {
                  .discount-text {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
                &.tag {
                  background-color: rgba(255, 255, 255, 0.85);

                  &.blue-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                  &.orange-tag {
                    background-color: rgba(255, 255, 255, 0.85);
                  }
                }
              }
            }
            .info-center {
              .game-hot {
                color: #fff;
              }

              .types .type {
                color: #fff;

                &:not(:first-child):before {
                  background-color: #fff;
                }
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
              .tags {
                .tag {
                  .tag-name {
                    font-weight: bold;
                  }
                }
              }
              .score::before {
                .image-bg('~@/assets/images/category/star-2.png');
              }
            }
            .info-bottom {
              .discount-text {
                background-color: #fff;
              }
            }
          }
        }
      }
    }
    .item-content {
      display: flex;
      align-items: center;
      margin-left: -16 * @rem;
      .game-index {
        width: 22 * @rem;
        height: 22 * @rem;
        margin: 5 * @rem;
        font-size: 14 * @rem;
        color: #797979;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          width: 64 * @rem;
          height: 64 * @rem;
        }
        .game-info {
          height: unset;
          .game-name {
            font-size: 14 * @rem;
            font-weight: bold;
            line-height: 20 * @rem;
          }
          .game-bottom {
            margin-top: 0;
            font-size: 11 * @rem;
            line-height: 16 * @rem;
          }
          .tags {
            margin: 4 * @rem 0;
          }
          .score {
            font-size: 12 * @rem;
            font-weight: 600;
            &::before {
              content: '';
              display: block;
              width: 8 * @rem;
              height: 8 * @rem;
              margin-right: 4 * @rem;
              .image-bg('~@/assets/images/category/star-3.png');
            }
          }
          .types {
            .type {
              font-size: 11 * @rem;
            }
          }
        }
      }
    }
  }
  .item-container2 {
    margin: 20 * @rem 18 * @rem 20 * @rem;
  }
}
</style>
