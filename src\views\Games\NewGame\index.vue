<template>
  <div class="page new-game-page">
    <nav-bar-2 :title="pageTitle" :border="true">
      <template #right>
        <div class="search-icon" @click="toPage('Search')"></div>
      </template>
    </nav-bar-2>
    <main>
      <van-tabs v-model="active" animated class="yy-tabs">
        <van-tab v-for="(item, index) in tabList" :key="index">
          <template #title>
            <div class="new-nav" :class="{ active: active == index }">
              <div class="new-nav-title">
                {{ item.title }}
              </div>
            </div>
          </template>
          <new-first v-if="index == 0"></new-first>
          <new-forecast v-if="index == 1"></new-forecast>
          <today-kaifu v-if="index == 2"></today-kaifu>
        </van-tab>
      </van-tabs>
    </main>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import NewFirst from './NewFirst/index.vue';
import NewForecast from './NewForecast/index.vue';
import TodayKaifu from './TodayKaifu/index.vue';
import newFirstIcon from '@/assets/images/games/newFirstIcon.png';
import newFirstIconOn from '@/assets/images/games/newFirstIconOn.png';
import newForecastIcon from '@/assets/images/games/newForecastIcon.png';
import newForecastIconOn from '@/assets/images/games/newForecastIconOn.png';
import kaifuIcon from '@/assets/images/games/kaifuIcon.png';
import kaifuIconOn from '@/assets/images/games/kaifuIconOn.png';
export default {
  name: 'NewGame',
  components: {
    NewFirst,
    NewForecast,
    TodayKaifu,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      pageTitle: this.$t('新游'),
      tabList: [
        {
          name: 'NewFirst',
          title: this.$t('新游首发'),
          icon: newFirstIcon,
          icon_on: newFirstIconOn,
        },
        {
          name: 'NewForecast',
          title: this.$t('新游预告'),
          icon: newForecastIcon,
          icon_on: newForecastIconOn,
        },
        {
          name: 'Kaifu',
          title: this.$t('今日开服'),
          icon: kaifuIcon,
          icon_on: kaifuIconOn,
        },
      ],
    };
  },
  activated() {
    let active = this.$route.params.active;
    if (active || active == 0) this.active = active;
  },
};
</script>

<style lang="less" scoped>
.new-game-page {
  .search-icon {
    width: 18 * @rem;
    height: 18 * @rem;
    padding: 10 * @rem;
    .image-bg('~@/assets/images/nav-bar-search-black.png');
    background-size: 18 * @rem 18 * @rem;
    background-position: center center;
  }
}
main {
  background-color: #fff;
  height: calc(100vh - 50 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
  position: fixed;
  top: calc(50 * @rem + @safeAreaTop);
  top: calc(50 * @rem + @safeAreaTopEnv);
  left: 0;
  width: 100%;
  .fixed-center;
  .new-nav {
    width: 97 * @rem;
    height: 32 * @rem;
    background-color: #f5f5f6;
    border-radius: 16 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;
    &.active {
      color: #fff;
      background-color: @themeColor;
    }
  }
  /deep/ .van-tabs__wrap {
    background-color: transparent !important;
    .van-tabs__nav--line {
      background-color: transparent;
      padding-bottom: 0;
    }
    .van-tabs__nav {
      box-sizing: border-box;
      background-color: transparent;
      justify-content: space-between;
      margin: 0 20 * @rem;
    }
    .van-tabs__line {
      display: none;
    }
    .van-tab {
      flex: unset;
      padding: 0;
    }
    .van-tab__text--ellipsis {
      display: block;
    }
  }

  /deep/ .van-tab__pane {
    height: 100%;
  }
  /deep/ .van-tabs--line .van-tabs__wrap {
    height: 52 * @rem;
  }
}
main /deep/ .van-tabs.van-tabs--line {
  height: 100%;
}
main /deep/ .van-tabs--line .van-tabs__wrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 1000;
}
main /deep/ .van-tabs--line .van-tabs__content {
  box-sizing: border-box;
  height: 100%;
  padding-top: 52 * @rem;
  overflow: hidden;
}
</style>
