<template>
  <div class="page my-deal-page">
    <nav-bar-2 :title="$t('交易记录')">
      <template #right>
        <div class="kefu-btn" @click="connectQiYu">
          <div class="dot-red" v-if="unReadMesNumber > 0">{{
            unReadMesNumber > 99 ? '99+' : unReadMesNumber
          }}</div>
        </div>
      </template>
    </nav-bar-2>
    <div class="main fixed-center">
      <div class="navs">
        <div
          class="nav"
          v-for="(item, index) in navs"
          :key="index"
          :class="[{ current: $route.path.includes(item.path) }]"
          @click="toPage(item.name, {}, 1)"
        >
          {{ item.title }}
        </div>
      </div>
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import { ApiUserGetSingleWealth } from '@/api/views/users.js';

export default {
  name: 'MyDeal',
  data() {
    return {
      currentLevel: {},
      limitLevel: 0,
      navs: [
        {
          title: this.$t('购买'),
          path: 'my_buy_list',
          name: 'MyBuyList',
        },
        {
          title: this.$t('出售'),
          path: 'my_sell_list',
          name: 'MySellList',
        },
      ],
    };
  },
  created() {
    this.getUserLevel();
  },
  methods: {
    async getUserLevel() {
      const res = await ApiUserGetSingleWealth();
      this.currentLevel = res.data.level;
      this.limitLevel = res.data.level_limit;
    },
    // 打开客服
    connectQiYu() {
      // 财富等级未达标
      if (this.currentLevel.level_id >= this.limitLevel) {
        this.openKefu({ is_zx: 1 });
      } else {
        this.openKefu();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.my-deal-page {
  /deep/ .nav-bar-component .white .van-nav-bar {
    background: #fff;
  }
  .kefu-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/deal/deal-kefu.png) right center no-repeat;
    background-size: 20 * @rem 20 * @rem;
    position: relative;

    .dot-red {
      display: block;
      padding: 0 3 * @rem;
      height: 11 * @rem;
      background: #f44040;
      border-radius: 6 * @rem;
      border: 1px solid #ffffff;
      font-weight: 600;
      font-size: 9 * @rem;
      color: #ffffff;
      line-height: 11 * @rem;
      text-align: center;
      position: absolute;
      top: 6 * @rem;
      left: 10 * @rem;
      white-space: nowrap;
    }
  }
  .main {
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    background-color: #fff;
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    width: 100%;
    .navs {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50 * @rem;
      overflow: hidden;
      background-color: #fff;
      margin: 0 * @rem auto;
      .nav {
        flex: 1;
        height: 50 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        font-weight: bold;
        color: #191b1f;
        padding: 0 15 * @rem;
        &.current {
          color: @themeColor;
        }
      }
    }
  }
}
</style>
