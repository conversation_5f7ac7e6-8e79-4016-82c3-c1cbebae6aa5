<template>
  <!-- 广告轮播通过action_code跳转 -->
  <van-swipe
    class="yy-swiper"
    :autoplay="5000"
    :indicator-color="indicatorColor"
    :style="{
      width: `${width * remNumberLess}rem !important`,
      height: `${height * remNumberLess}rem !important`,
    }"
    v-if="bannerList.length"
  >
    <template v-for="banner in bannerList">
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        :style="{
          width: `${width * remNumberLess}rem !important`,
          height: `${height * remNumberLess}rem !important`,
        }"
        @click="handleActionCode(banner)"
      >
        <img class="banner" :src="banner.bg_img_url" />
      </van-swipe-item>
    </template>
  </van-swipe>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'YyBanner',
  data() {
    return {
      remNumberLess,
    };
  },
  props: {
    bannerList: {
      type: Array,
      default: () => [],
    },
    indicatorColor: {
      type: String,
      default: '#21b98a',
    },
    width: {
      type: Number,
      default: 340, // 单位rem
    },
    height: {
      type: Number,
      default: 190, // 单位rem
    },
  },
  methods: {
    handleActionCode,
  },
};
</script>
<style lang="less" scoped>
.yy-swiper {
  width: 340 * @rem;
  height: 190 * @rem;
  margin: 10 * @rem auto 0;
  border-radius: 10 * @rem;
  overflow: hidden;
  &.collect {
    padding-bottom: 13 * @rem;
    /deep/ .van-swipe__indicators {
      bottom: 0 * @rem;
      .van-swipe__indicator {
        background-color: #ebebeb;
      }
    }
  }
  .swiper-slide {
    position: relative;
    width: 340 * @rem;
    height: 190 * @rem;
    border-radius: 10 * @rem;
    overflow: hidden;
    img {
      object-fit: cover;
    }
    .banner-title {
      box-sizing: border-box;
      height: 60 * @rem;
      width: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.7) 100%
      );
      position: absolute;
      bottom: 0;
      left: 0;
      color: #fff;
      line-height: 22 * @rem;
      font-size: 16 * @rem;
      font-weight: bold;
      padding: 13 * @rem;
      padding-top: 28 * @rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  /deep/ .van-swipe__indicator {
    width: 5 * @rem;
    height: 5 * @rem;
    background-color: #fff;
    opacity: 1;
    border-radius: 3 * @rem;
  }
  /deep/ .van-swipe__indicator--active {
    width: 13 * @rem;
    height: 5 * @rem;
    background-color: #faae86;
  }
  /deep/ .van-swipe__indicator:not(:last-child) {
    margin-right: 3 * @rem;
  }
}
</style>
