<template>
  <div class="comment-editor page">
    <nav-bar-2 :title="$t('发表评论')" :border="true">
      <template #left>
        <slot name="left">
          <div class="back" @click="back"></div>
        </slot>
      </template>
    </nav-bar-2>
    <!-- 评分 -->
    <!-- <div v-if="class_id != 101" class="rate-container">
      <div class="rate-click">ellipsis-content
        <van-rate
          v-model="score"
          color="#1CCE94"
          void-icon="star"
          void-color="#E3E5E8"
          :size="28"
        />
      </div>
      <div class="tips">轻点星星打个分</div>
    </div> -->
    <div class="editor-container">
      <div class="input-container">
        <div
          id="inputText"
          class="input-text"
          contenteditable="true"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          :placeholder="placeholder"
          ref="editor"
        >
        </div>
      </div>
      <div class="img-container">
        <van-uploader
          v-model="imageFileList"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="10"
          :preview-size="92"
          accept="image/*"
          :before-read="beforeRead"
          :multiple="true"
          class="uploader"
        >
          <div class="upload-btn"> </div>
          <div class="text">上传</div>
        </van-uploader>
      </div>
      <div class="check-box">
        <span class="check-box-time">
          <van-checkbox
            v-model="is_show_playtime"
            :icon-size="`${14 * remNumberLess}rem`"
            checked-color="#1CCE94"
          >
            显示游戏时长</van-checkbox
          >
        </span>
        <!-- <span class="check-box-phone">
          <van-checkbox
            v-model="is_show_model"
            :icon-size="`${14 * remNumberLess}rem`"
            checked-color="#1CCE94"
          >
            显示机型</van-checkbox
          ></span
        > -->
      </div>
    </div>
    <!-- <div v-if="class_id != 101" class="score-container">
      <div class="score-title">{{ $t('评分') }}：</div>
      <van-rate
        v-model="score"
        color="#FE6600"
        void-icon="star"
        void-color="#E0E0E0"
        :size="14"
      />
    </div> -->
    <!-- 评论按钮 -->
    <div class="bottom-container">
      <div class="bottom-fixed">
        <div class="btn" :class="{ on: canSend }" @click="handleSend">
          {{ $t('发表') }}</div
        >
      </div>
    </div>
    <div
      v-if="showToolbar && commentTypeList.length"
      class="comment-type-container"
      :style="{ bottom: toolbarBottom + 'px' }"
      @mousedown.prevent
    >
      <div class="comment-type-title">评价类型</div>
      <div class="comment-type-list">
        <div
          class="comment-type-item"
          v-for="item in commentTypeList"
          :key="item.id"
          @click.stop="handleCommentType(item)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <!-- 退出弹窗 -->
    <van-popup
      v-model="isExitPopupShow"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="exit-popup"
    >
      <div class="exit-container">
        <div class="title">确定退出评价吗？</div>
        <div class="msg">已编辑的内容将不会保存噢~</div>
        <div class="btn-list">
          <div class="exit" @click="confirmExit">退出评价</div>
          <div class="continue" @click="isExitPopupShow = false">继续评价</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiCommentSubmit, ApiCommentQuick } from '@/api/views/comment.js';
import md5 from 'js-md5';
import { remNumberLess } from '@/common/styles/_variable.less';
import { mapGetters } from 'vuex';

export default {
  name: 'CommentEditor',
  data() {
    return {
      score: 5,
      inputText: '', // 评论内容 - 纯文本内容
      rawHTML: '', // 富文本HTML内容
      imageFileList: [], // 本地图片列表
      images: [], // 上传后的图片URL列表
      isSending: false,
      class_id: 0,
      source_id: 0,
      model: 'iPhone X',
      comment_id: null, //评论id
      is_update: 1, //是否修改 1-修改 0-获取数据
      is_show_playtime: true,
      is_show_model: false,
      isExitPopupShow: false,
      remNumberLess,
      params: {},
      showToolbar: false,
      toolbarBottom: 0,
    };
  },
  computed: {
    ...mapGetters({
      commentTypeList: 'comment/commentTypeList',
      gameInfo: 'game/gameInfo',
    }),
    placeholder() {
      return this.class_id == 101
        ? '说点什么...'
        : '写出你对游戏玩法、操作、攻略等方面的可观评价吧，优质评论将给予金币奖励与前排展示~';
    },
    canSend() {
      if (this.inputText.length < 15) return false;
      if (this.score < 1) return false;
      return true;
    },
    formatContent() {
      return this.inputText.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
    formatRawHTMLContent() {
      return this.rawHTML.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  async created() {
    this.class_id = this.$route.params.class_id;
    this.source_id = Number(this.$route.params.source_id);
    this.comment_id = this.$route.params.comment_id || 0;
    this.totaldown = this.$route.params.totaldown || 0;
    this.is_update = this.$route.params.is_update;
    if (!this.is_update && this.comment_id) {
      this.$toast.loading({
        message: '加载中...',
      });
      this.getCommentInfo();
    } else {
      this.is_update = 1;
    }
    // 获取设备信息 没有详细写 部分获取设备信息不准
    this.model = this.getUserDevice();
  },
  mounted() {
    if (!this.is_update && this.comment_id) {
      this.$nextTick(() => {
        document.querySelector('#inputText').focus();
      });
    }

    // 监听 viewport 变化
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', this.onViewportResize);
      window.visualViewport.addEventListener(
        'scroll',
        this.updateToolbarPosition,
      );
    }
  },
  beforeDestroy() {
    if (window.visualViewport) {
      window.visualViewport.removeEventListener(
        'resize',
        this.onViewportResize,
      );
      window.visualViewport.removeEventListener(
        'scroll',
        this.updateToolbarPosition,
      );
    }
  },
  methods: {
    handleCommentType(info) {
      // 获取类型文本
      const typeItem = this.commentTypeList.find(item => item.id === info.id);
      const typeTitle = typeItem.title;

      // 获取编辑器
      const editor = this.$refs.editor;

      // 确保编辑器获得焦点
      editor.focus();

      // 检查编辑器是否有内容
      const hasContent = editor.textContent.trim() !== '';

      // 创建要插入的内容
      let fragment = document.createDocumentFragment();

      // 如果已有内容，先添加换行符
      if (hasContent) {
        fragment.appendChild(document.createElement('br'));
      }

      // 添加标签文本
      fragment.appendChild(document.createTextNode(`${typeTitle}：`));

      // 将光标移到编辑器末尾
      const selection = window.getSelection();
      const range = document.createRange();

      // 设置范围到编辑器的末尾
      range.selectNodeContents(editor);
      range.collapse(false); // 折叠到末尾

      // 删除当前选择
      selection.removeAllRanges();
      selection.addRange(range);

      // 插入内容
      range.insertNode(fragment);

      // 将光标移到插入内容之后
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);

      // 更新内容
      this.rawHTML = editor.innerHTML;
      this.inputText = editor.innerText;

      // 保持工具栏显示
      this.showToolbar = true;
    },
    handleInput(e) {
      // 保存内容
      this.rawHTML = e.target.innerHTML;
      this.inputText = e.target.innerText;
    },
    updateToolbarPosition() {
      if (window.visualViewport) {
        const viewportHeight = window.visualViewport.height;
        const windowInnerHeight = window.innerHeight;
        const keyboardHeight = windowInnerHeight - viewportHeight;
        this.toolbarBottom = keyboardHeight > 0 ? keyboardHeight : 0;
      }
    },
    onViewportResize() {
      this.updateToolbarPosition();
    },
    handleFocus() {
      this.$nextTick(() => {
        this.showToolbar = true;
        this.updateToolbarPosition();
      });
    },
    handleBlur() {
      this.showToolbar = false;

      // 如果编辑器没有内容，则清空编辑器
      if (this.$refs.editor && this.$refs.editor.innerText.trim() === '') {
        this.clearEditor();
      }
    },
    // 获取当前评论（需要修改以支持富文本）
    async getCommentInfo() {
      try {
        const res = await ApiCommentSubmit({
          sourceId: this.source_id,
          classId: this.class_id,
          comment_id: this.comment_id,
          is_update: this.is_update,
        });
        this.sourceId = res.data.source_id;

        // 处理富文本内容
        const contentHtml = res.data.content.replace(/\n/g, '<br>') || '';

        // 1. 创建临时DOM元素来解析HTML
        // const tempDiv = document.createElement('div');
        // tempDiv.innerHTML = contentHtml;

        // 2. 提取纯文本内容
        // const plainText = tempDiv.innerText || tempDiv.textContent || '';

        // 3. 设置编辑器数据
        // this.inputText = plainText; // 设置纯文本内容用于表单验证
        this.inputText = contentHtml; // 设置纯文本内容用于表单验证
        this.rawHTML = contentHtml; // 保存原始HTML内容

        // 4. 设置编辑器内容 - 使用原始HTML
        if (this.$refs.editor) {
          this.$refs.editor.innerHTML = contentHtml;
        }

        // 设置其他字段
        this.score = res.data.rating;
        if (res.data.is_show_playtime == false) {
          this.is_show_playtime = false;
        }
        this.is_show_model = res.data.is_show_model;
        this.is_update = 1;

        // 确保 images 是一个包含 URL 的数组
        if (res.data.images && Array.isArray(res.data.images)) {
          this.images = res.data.images;
          this.imageFileList = res.data.images.map(image => ({
            url: image, // 确保这里是正确的 URL
          }));
        }

        // 在内容设置完成后，将光标移动到内容末尾
        this.$nextTick(() => {
          const editor = this.$refs.editor;
          if (editor) {
            // 聚焦编辑器
            editor.focus();

            // 创建一个新的选择范围
            const selection = window.getSelection();
            const range = document.createRange();

            // 将范围设置到编辑器内容的末尾
            range.selectNodeContents(editor);
            range.collapse(false); // 折叠到末尾

            // 应用这个范围
            selection.removeAllRanges();
            selection.addRange(range);
          }
        });
      } catch (error) {
      } finally {
        if (window.visualViewport) {
          window.visualViewport.addEventListener(
            'resize',
            this.onViewportResize,
          );
          window.visualViewport.addEventListener(
            'scroll',
            this.updateToolbarPosition,
          );
        }
        this.$toast.clear();
      }
    },
    back() {
      this.isExitPopupShow = true;
    },
    confirmExit() {
      this.$router.go(-1);
    },
    // 清空编辑器内容
    clearEditor() {
      this.inputText = '';
      this.rawHTML = '';
      if (this.$refs.editor) {
        this.$refs.editor.innerHTML = '';
      }
    },
    async handleSend() {
      if (this.isSending) {
        return false;
      }
      if (this.inputText.length < 15) {
        this.$toast(this.$t('评论的字数不得小于15个字'));
        return false;
      }
      if (this.commentTypeList.length > 1000) {
        this.$toast('评论字数太长了哦~');
        return false;
      }
      // if (this.score < 1) {
      //   this.$toast(this.$t('请选择你的评分'));
      //   return false;
      // }
      this.inputText = this.inputText.replace(/<br>/g, '\n');
      this.isSending = true;
      this.$toast.loading({
        message: '正在提交中...',
        duration: 0,
      });
      try {
        // 如果图片列表不一样，则上传所有图片
        if (this.images !== this.imageFileList) {
          await this.uploadAllImages();
        }

        // 构建提交参数 - 使用纯文本或必要时从富文本提取
        this.params = {
          sourceId: this.source_id,
          classId: this.class_id,
          model: this.model,
          content: this.formatContent, // 使用计算属性处理表情符号
          // rawHTML: this.formatRawHTMLContent,
          rating: this.score,
          is_show_playtime: this.is_show_playtime ? 1 : 0,
          is_show_model: this.is_show_model ? 1 : 0,
          is_update: this.is_update,
        };
        if (this.comment_id) {
          this.params.comment_id = this.comment_id;
        }
        if (this.images.length) {
          this.params.images = JSON.stringify(this.images);
        }

        // 提交评论
        const res = await ApiCommentSubmit({ ...this.params });

        // 神策埋点
        if (!this.comment_id) {
          // 评价游戏的才上报神策
          this.$sensorsTrack('publish_game_reviews', {
            game_id: `${this.gameInfo.id}`,
            game_name: `${this.gameInfo.main_title}`,
            game_type: `${this.gameInfo.classid}`,
            reviews_content: `${this.formatContent}`,
          });
        }

        // 重置表单
        this.clearEditor();
        this.imageFileList = [];
        this.images = [];
        this.is_update = 0;

        this.$toast(res.msg);
        setTimeout(() => {
          this.$router.go(-1);
        }, 500);
      } finally {
        this.isSending = false;
      }
    },

    // 上传所有图片的方法，保持原始顺序
    async uploadAllImages() {
      // 清空之前的图片URL数组
      this.images = [];

      // 逐个上传图片，保持顺序
      for (let i = 0; i < this.imageFileList.length; i++) {
        try {
          const fileItem = this.imageFileList[i];
          // 只有本地文件需要上传，已有URL的图片直接保留
          if (fileItem.file) {
            await this.uploadSingleImage(fileItem.file, i);
          } else if (fileItem.url) {
            // 如果是已有的图片URL，直接添加到images数组
            this.images[i] = fileItem.url;
          }
        } catch (error) {
          console.error(`上传第${i + 1}张图片失败:`, error);
          this.$toast(`第${i + 1}张图片上传失败`);
          throw error;
        }
      }
    },

    // 上传单张图片，指定索引位置
    async uploadSingleImage(uploadFile, index) {
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; // 写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);

      try {
        const res = await ApiUploadImage(data);
        // 将图片URL放在对应的索引位置
        this.images[index] = res.data.url;
        return res.data.url;
      } catch (err) {
        this.$toast('图片上传失败');
        throw err;
      }
    },

    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    async afterRead(file) {
      // 直接添加到本地列表，不上传
      // 这里不需要做任何处理，因为van-uploader已经将文件添加到imageFileList中
    },

    deletePic(file, detail) {
      // 只需要从本地列表中删除，不需要处理远程图片
      this.images.splice(detail.index, 1);
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
    // 获取用户设备信息
    getUserDevice() {
      const ua = navigator.userAgent;
      let deviceInfo = '';

      // 检测设备类型
      if (/iPhone|iPad|iPod/i.test(ua)) {
        // iOS 设备
        const match = ua.match(/iPhone OS (\d+)_(\d+)/i);
        if (match) {
          const model = this.getIOSModel();
          const version = `${match[1]}.${match[2]}`;
          deviceInfo = model || `iPhone (iOS ${version})`;
        } else {
          deviceInfo = 'iPhone';
        }
      } else if (/Android/i.test(ua)) {
        // Android 设备
        const match = ua.match(/Android (\d+)\.(\d+)/i);
        const modelMatch = ua.match(/\((.+?)\)/);
        let model = modelMatch ? modelMatch[1].split(';')[1] : '';
        model = model ? model.trim() : 'Android';

        if (match) {
          const version = `${match[1]}.${match[2]}`;
          deviceInfo = `${model} (Android ${version})`;
        } else {
          deviceInfo = model;
        }
      } else {
        // 其他设备
        deviceInfo = '其他设备';
      }

      this.model = deviceInfo;
      return deviceInfo;
    },

    // 获取 iOS 设备型号（基于屏幕尺寸和像素密度的估计）
    getIOSModel() {
      const width = window.screen.width;
      const height = window.screen.height;
      const ratio = window.devicePixelRatio;

      // 根据屏幕尺寸和像素密度估计设备型号
      if (width === 375 && height === 812 && ratio === 3) return 'iPhone X/XS';
      if (width === 414 && height === 896 && ratio === 2) return 'iPhone XR';
      if (width === 414 && height === 896 && ratio === 3)
        return 'iPhone XS Max';
      if (width === 375 && height === 667 && ratio === 2) return 'iPhone 6/7/8';
      if (width === 414 && height === 736 && ratio === 3)
        return 'iPhone 6/7/8 Plus';
      if (width === 320 && height === 568 && ratio === 2)
        return 'iPhone 5/5S/SE';
      if (width === 390 && height === 844 && ratio === 3)
        return 'iPhone 12/12 Pro/13/13 Pro';
      if (width === 428 && height === 926 && ratio === 3)
        return 'iPhone 12/13 Pro Max';
      if (width === 360 && height === 780 && ratio === 3)
        return 'iPhone 12/13 mini';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 14 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 14 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 14 Pro';
      if (width === 393 && height === 852 && ratio === 2) return 'iPhone 15';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 15 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 15 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 15 Pro';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 16 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 16 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 16 Pro';
      if (width === 430 && height === 926 && ratio === 3) return 'iPhone 16';

      return 'iPhone';
    },
  },
};
</script>

<style lang="less" scoped>
.comment-editor {
  position: relative;
  overflow: hidden;
  padding-bottom: calc(80 * @rem + @safeAreaBottom);
  padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
  .send {
    width: 52 * @rem;
    height: 26 * @rem;
    border-radius: 13 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    color: #ffffff;
    font-weight: 600;
    background-color: #c1c1c1;
    &.on {
      background-color: @themeColor;
    }
  }
  .rate-container {
    margin-top: 24 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .tips {
      margin-top: 16 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
  .editor-container {
    margin-top: 26 * @rem;
    width: 336 * @rem;
    margin: 26 * @rem auto 20 * @rem;
    // min-height: 486 * @rem;
    background: #f7f8fa;
    border-radius: 6 * @rem;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100vh - 80 * @rem - @safeAreaBottomEnv);
    height: calc(100vh - 80 * @rem - @safeAreaBottom);
    flex: 1;
    .input-container {
      box-sizing: border-box;
      width: 100%;
      // min-height: 162 * @rem;
      padding-bottom: 20 * @rem;
      .input-text {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 22 * @rem 18 * @rem;
        font-size: 14 * @rem;
        color: #60666c;
        font-weight: 400;
        line-height: 20 * @rem;
        border: 0;
        outline: none;
        background: #f7f8fa;
        overflow-y: auto;
        word-break: break-all;
        .input-text-title {
          font-weight: 500;
          font-size: 14 * @rem;
          color: #93999f;
        }
        .input-text-content {
          font-size: 14 * @rem;
          color: #60666c;
        }
        // 为空时显示placeholder
        &:empty::before {
          content: attr(placeholder);
          color: #93999f;
        }
      }
    }
    .img-container {
      box-sizing: border-box;
      width: 100%;
      // min-height: 92 * @rem;
      padding: 0 12 * @rem;
      flex: 1;
      .uploader {
        width: 100%;
        /deep/ .van-uploader__wrapper {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12 * @rem;
        }
        /deep/.van-uploader__input-wrapper {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        /deep/ .van-uploader__preview-image {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;
        }
        /deep/.van-uploader__preview-delete {
          top: -5 * @rem;
          right: -2 * @rem;
          height: 14 * @rem;
          width: 14 * @rem;
          background: url('~@/assets/images/deal/upload-icon-delete.png')
            #f0f1f5 no-repeat center center;
          background-size: 14 * @rem 14 * @rem;
          border-radius: 0;
        }
        /deep/.van-icon {
          display: none;
        }
      }
      .upload-btn {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url('~@/assets/images/deal/upload-icon-1.png') #f0f1f5
          no-repeat center center;
        background-size: 24 * @rem 24 * @rem;
      }
      .text {
        margin-top: 8 * @rem;
        font-weight: 500;
        font-size: 12 * @rem;
        color: #93999f;
      }
    }
    .check-box {
      // position: absolute;
      padding: 12 * @rem 0 12 * @rem 12 * @rem;
      display: flex;
      align-items: center;
      .check-box-time,
      .check-box-phone {
        // margin-left: 12 * @rem;
        /deep/.van-checkbox {
          display: flex;
          flex-direction: row-reverse;
        }
        /deep/.van-checkbox__label {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #93999f;
          margin-left: 0;
          margin-right: 4 * @rem;
        }
      }
    }
  }
  .score-container {
    display: flex;
    align-items: center;
    padding: 14 * @rem 18 * @rem;
    border-bottom: 0.5 * @rem solid #e8e8e8;
    .score-title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
    }
  }
  .quick-comment {
    border-top: 10 * @rem solid #f5f5f6;
    padding: 18 * @rem;
    .title {
      font-size: 16 * @rem;
      font-family:
        PingFang SC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      span {
        font-size: 14 * @rem;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .list {
      margin-top: 22 * @rem;
      .item {
        display: table;
        margin-bottom: 10 * @rem;
        padding: 8 * @rem 15 * @rem;
        background: #fff6e9;
        border-radius: 20 * @rem;
        color: @themeColor;
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        font-family:
          PingFang SC-Regular,
          PingFang SC;
        font-weight: 400;
        box-sizing: border-box;
        border: 1px solid #fff6e9;
        &.current {
          border: 1px solid @themeColor;
          color: @themeColor;
          font-weight: 600;
        }
      }
    }
  }
  .bottom-container {
    box-sizing: border-box;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      // box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: calc(@safeAreaBottom + 20 * @rem);
      padding-bottom: calc(@safeAreaBottomEnv + 20 * @rem);
      .btn {
        width: 336 * @rem;
        height: 44 * @rem;
        border-radius: 29 * @rem;
        text-align: center;
        line-height: 44 * @rem;
        color: #fff;
        font-weight: 500;
        font-size: 16 * @rem;
        background-color: #c1c1c1;
        &.on {
          background-color: @themeColor;
        }
      }
    }
  }
  .comment-type-container {
    position: absolute;
    padding: 10 * @rem 0 10 * @rem 12 * @rem;
    box-sizing: border-box;
    z-index: 99999;
    left: 0;
    right: 0;
    height: 41 * @rem;
    line-height: 41 * @rem;
    background: #f0f1f5;
    display: flex;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
      display: none;
    }
    .comment-type-title {
      font-weight: 500;
      font-size: 12 * @rem;
      color: #191b1f;
      flex-shrink: 0;
    }
    .comment-type-list {
      display: flex;
      align-items: center;
      justify-content: space-around;
      .comment-type-item {
        height: 21 * @rem;
        line-height: 21 * @rem;
        background: #ffffff;
        border-radius: 4 * @rem;
        padding: 0 8 * @rem;
        box-sizing: border-box;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #191b1f;
        white-space: nowrap;
        margin-right: 6 * @rem;
        &:first-child {
          margin-left: 8 * @rem;
        }
        &.active {
          background: #e6fff6;
          color: #1cce94;
          border: 1px solid #1cce94;
        }
        &[disabled] {
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }
  }
  .exit-popup {
    width: 300 * @rem;
    height: 200 * @rem;
    background: #fff;
    .exit-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-top: 30 * @rem;
        font-size: 16 * @rem;
        font-weight: bold;
        color: #191b1f;
      }
      .msg {
        margin-top: 25 * @rem;
        width: 196 * @rem;
        text-align: center;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #191b1f;
        line-height: 18 * @rem;
      }
      .btn-list {
        position: absolute;
        left: 25 * @rem;
        bottom: 20 * @rem;
        display: flex;
        align-items: center;
        .exit,
        .continue {
          width: 120 * @rem;
          height: 36 * @rem;
          background: #f7f8fa;
          border-radius: 22 * @rem;
          color: #93999f;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .continue {
          margin-left: 10 * @rem;
          background: #1cce94;
          color: #fff;
        }
      }
    }
  }
}
</style>
