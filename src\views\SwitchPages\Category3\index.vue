<template>
  <div class="category-page">
    <div class="fixed">
      <status-bar
        bgColor="linear-gradient(to bottom, rgba(225, 225, 225, 1) 0%, rgba(255, 255, 255, 1) 100%)"
      ></status-bar>
      <div class="search-bar" @click="$router.push('/search')">
        <div class="icon"></div>
        <div class="search-text">{{ $t('搜你想玩的游戏') }}</div>
      </div>
    </div>
    <main v-if="slidebarList.length > 2">
      <van-sidebar class="left-container" v-model="cate_value">
        <van-sidebar-item
          v-for="(item, index) in slidebarList"
          :key="index"
          :title="item.title"
        />
      </van-sidebar>
      <div class="container">
        <template v-if="cate_value === 0">
          <categoryList1></categoryList1>
        </template>
        <template v-else>
          <categoryList2 :info="slidebarList[cate_value]"></categoryList2>
        </template>
      </div>
    </main>
  </div>
</template>

<script>
import { ApiGameCate } from '@/api/views/game.js';
import categoryList1 from './components/categoryList1';
import categoryList2 from './components/categoryList2';
import { themeColorLess } from '@/common/styles/_variable.less';

export default {
  components: {
    categoryList1,
    categoryList2,
  },
  data() {
    return {
      themeColorLess,
      cate_value: 0,
      slidebarList: [
        {
          title: this.$t('精选'),
        },
      ],
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.params.cate_value != undefined) {
        vm.cate_value = to.params.cate_value;
        vm.$nextTick(() => {
          setTimeout(() => {
            document
              .querySelector('.van-sidebar-item--select')
              .scrollIntoView();
          }, 0);
        });
      }
    });
  },
  created() {
    ApiGameCate().then(res => {
      let { game_cate, theme_list } = res.data;
      game_cate = game_cate.map(item => {
        item.type = item.id;
        return item;
      });
      theme_list = theme_list.map(item => {
        item.theme = item.id;
        return item;
      });
      this.slidebarList = [...this.slidebarList, ...game_cate, ...theme_list];
    });
  },
};
</script>

<style lang="less" scoped>
.category-page {
  padding-bottom: 0;
  height: 100%;
}

.fixed {
  position: fixed;
  left: 0;
  .fixed-center;
  top: 0;
  width: 100%;
  padding: 0px 0 10 * @rem;
  background: #fff;
  z-index: 2000;
}
.search-bar {
  box-sizing: border-box;
  padding: 0 16 * @rem;
  width: 331 * @rem;
  height: 35 * @rem;
  border-radius: 18 * @rem;
  background-color: #f6f6f6;
  margin: 10 * @rem auto 0;
  display: flex;
  align-items: center;
  .icon {
    width: 14 * @rem;
    height: 14 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 14 * @rem 14 * @rem;
  }
  .search-text {
    font-size: 14 * @rem;
    color: #9a9a9a;
    margin-left: 7 * @rem;
  }
}
main {
  position: fixed;
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - 105 * @rem - @safeAreaTop - @safeAreaBottom);
  height: calc(100vh - 105 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
  margin-top: calc(55 * @rem + @safeAreaTop);
  margin-top: calc(55 * @rem + @safeAreaTopEnv);
  display: flex;
  .fixed-center;
  .left-container {
    border-radius: 0 12 * @rem 0 0;
    margin-top: 5 * @rem;
  }
  /deep/ .van-sidebar {
    flex: 0 0 72px;
    width: 72px;
    height: calc(100% - 5 * @rem);
    background-color: #f5f5f6;
    &::-webkit-scrollbar {
      display: none;
    }
    .van-sidebar-item {
      padding: 16px 0px 16px 0px;
      display: flex;
      justify-content: center;
      background-color: #f5f5f6;
    }
    .van-sidebar-item--select::before {
      background-color: @themeColor;
      right: 0;
      left: unset;
      border-radius: 2 * @rem;
      width: 4 * @rem;
      height: 14 * @rem;
    }
    .van-sidebar-item__text {
      font-size: 14 * @rem;
      font-weight: 400;
      color: #000;
    }
    .van-sidebar-item--select {
      background-color: #f5f5f6;
      .van-sidebar-item__text {
        color: @themeColor;
      }
    }
  }
}
.container {
  display: flex;
  flex-flow: column;
  flex: 1;
  height: 100%;
  overflow: hidden;
  height: 100%;
  /deep/ .item-container:first-of-type {
    margin-top: 5 * @rem;
  }
  /deep/ .item-container:last-of-type {
    margin-bottom: 0;
  }
  /deep/ .van-dropdown-menu {
    position: relative;
    .van-dropdown-menu__bar {
      height: 44px;
      box-shadow: none;
      .van-dropdown-menu__title--active {
        color: @themeColor;
      }
    }
    .van-dropdown-item {
      position: absolute;
      top: 44px !important;
      height: calc(100vh - 149 * @rem - @safeAreaTop);
      height: calc(100vh - 149 * @rem - @safeAreaTopEnv);
    }
  }
  .game-container {
    height: 100vh;
  }
}
</style>
