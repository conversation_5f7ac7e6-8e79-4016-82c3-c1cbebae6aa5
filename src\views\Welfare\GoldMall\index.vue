<template>
  <rubber-band topColor="rgb(255,0,70)" bottomColor="#fff">
    <div class="gold-mall-page">
      <nav-bar-2 bgStyle="transparent" :placeholder="false"></nav-bar-2>
      <div class="main">
        <div class="top-bar"></div>
        <div class="content">
          <div class="gold-bar" @click="toPage('GoldCoin')">
            <div class="gold-info">
              <div class="title">{{ $t('当前金币') }}</div>
              <div class="gold-num">{{ userInfo.gold }}</div>
            </div>
            <div class="gold-btn" @click.stop="toPage('TaskDaily')"></div>
          </div>
          <!-- 金币兑换 -->
          <div
            class="exchange-container"
            v-for="(section, sectionIndex) in exchangeList"
            :key="sectionIndex"
          >
            <div class="section-title">
              <div class="title-icon"></div>
              <div class="title-text">
                {{ $t('金币兑换')
                }}<span>({{ section.start_time }}~{{ section.end_time }})</span>
              </div>
            </div>
            <div class="exchange-list">
              <template v-for="(item, index) in section.info">
                <exchange-item :key="index" :info="item"></exchange-item>
              </template>
            </div>
          </div>
          <!-- 金币活动 -->
          <div class="activity-container">
            <div class="section-title">
              <div class="title-icon"></div>
              <div class="title-text">{{ $t('金币活动') }}</div>
            </div>
            <div class="activity-list">
              <div class="activity-item btn" @click="toPage('GoldGamble')">
                <div class="activity-info">
                  <div class="title">{{ $t('金币夺宝') }}</div>
                  <div class="desc">{{ $t('小投入大收获') }}</div>
                </div>
                <div class="activity-icon">
                  <img src="~@/assets/images/welfare/mall-duobao.png" alt="" />
                </div>
              </div>
              <div class="activity-item btn" @click="toPage('TurnTable')">
                <div class="activity-info">
                  <div class="title">{{ $t('金币转盘') }}</div>
                  <div class="desc">{{ $t('好礼转不停') }}</div>
                </div>
                <div class="activity-icon">
                  <img
                    src="~@/assets/images/welfare/mall-zhuanpan.png"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </rubber-band>
</template>
<script>
import { ApigoldExchangeList } from '@/api/views/gold.js';
import ExchangeItem from '../comonents/exchange-item';
export default {
  name: 'GoldMall',
  components: {
    ExchangeItem,
  },
  data() {
    return {
      exchangeList: [],
      text: '',
    };
  },
  async created() {
    await this.getExchangeList();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async getExchangeList() {
      const res = await ApigoldExchangeList();
      let { list, text } = res.data;
      this.exchangeList = list;
      this.text = text;
    },
  },
};
</script>
<style lang="less" scoped>
.gold-mall-page {
  .main {
    margin-bottom: 40 * @rem;
  }
  .top-bar {
    width: 100%;
    height: 228 * @rem;
    .image-bg('~@/assets/images/welfare/gold-mall-bg.png');
  }
  .content {
    background: #f5f5f6;
    border-radius: 20 * @rem 20 * @rem 0 * @rem 0 * @rem;
    .gold-bar {
      display: flex;
      align-items: center;
      height: 82 * @rem;
      margin-top: -22 * @rem;
      background-color: #fff;
      border-radius: 20 * @rem 20 * @rem 0 * @rem 0 * @rem;
      padding-right: 12 * @rem;
      .gold-info {
        flex: 1;
        min-width: 0;
        padding-left: 32 * @rem;
        .title {
          font-size: 14 * @rem;
          color: #9a9a9a;
          font-weight: 400;
          line-height: 20 * @rem;
        }
        .gold-num {
          font-size: 28 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 40 * @rem;
        }
      }
      .gold-btn {
        width: 114 * @rem;
        height: 66 * @rem;
        .image-bg('~@/assets/images/welfare/get-coin-btn.png');
      }
    }
    .exchange-container {
      padding: 18 * @rem;
      padding-bottom: 0;
    }
    .activity-container {
      padding: 18 * @rem;
      .activity-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20 * @rem;
        .activity-item {
          box-sizing: border-box;
          padding-left: 14 * @rem;
          padding-right: 8 * @rem;
          display: flex;
          align-items: center;
          width: 162 * @rem;
          height: 74 * @rem;
          background: #fbf2ec;
          border-radius: 6 * @rem;
          .activity-info {
            flex: 1;
            min-width: 0;
            .title {
              font-size: 16 * @rem;
              color: #000000;
              font-weight: 500;
              line-height: 22 * @rem;
            }
            .desc {
              font-size: 12 * @rem;
              color: #c08863;
              font-weight: 400;
              margin-top: 3 * @rem;
              line-height: 17 * @rem;
            }
          }
          .activity-icon {
            width: 66 * @rem;
            height: 66 * @rem;
          }
        }
      }
    }
  }
  .section-title {
    display: flex;
    align-items: center;
    .title-icon {
      width: 24 * @rem;
      height: 24 * @rem;
      .image-bg('~@/assets/images/welfare/gold-icon.png');
    }
    .title-text {
      margin-left: 7 * @rem;
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 600;
      span {
        font-size: 14 * @rem;
        color: #333;
        margin-left: 5 * @rem;
      }
    }
  }
  .exchange-container,
  .activity-container {
    margin-top: 10 * @rem;
    background-color: #fff;
    padding: 18 * @rem;
  }
}
</style>
