<script>
import { ApiGameSignInList } from '@/api/views/game.js';

export default {
  data() {
    return {
      game_list: [],
      finished: false,
      loading: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  async created() {
    this.init();

    // 神策埋点
    this.$sensorsTrack('game_sign_in_pageView')
  },
  methods: {
    async init() {
      await this.getList();
    },
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiGameSignInList({
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.game_list = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.game_list.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getList(2);
    },
    handleActiveTime(item) {
      let time = `${this.$handleTimestamp(item.start_time).date} - ${
        this.$handleTimestamp(item.end_time).date
      }`;
      return time;
    },
  },
};
</script>
<template>
  <div class="sign-in-list">
    <nav-bar-2 :title="'签到礼包'" :border="true"></nav-bar-2>
    <div class="explain">更多签到活动</div>
    <content-empty v-if="empty"></content-empty>
    <load-more
      v-else
      class="game-list"
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <div v-for="(item, index) in game_list" class="item">
        <div class="game-info">
          <img :src="item.game_titlepic" class="game-img" />
          <div class="game-detail">
            <div class="game-title">{{ item.game_title }}</div>
            <div class="detail-text">
              活动时间：{{ handleActiveTime(item) }}
            </div>
          </div>
          <div
            @click="toPage('GameSignInDetail', { id: item.game_id })"
            class="button"
          >
            签到
          </div>
        </div>
        <div class="game-boon">{{ item.desc }}</div>
      </div>
    </load-more>
  </div>
</template>
<style lang="less" scoped>
.sign-in-list {
  background: #f5f7fb;
}
.explain {
  margin: 20 * @rem 15 * @rem;
  font-size: 14 * @rem;
  font-weight: 600;
}
.game-list {
  margin: 0 15 * @rem;
  .item {
    padding: 0 12 * @rem;
    border-radius: 8 * @rem;
    margin-bottom: 12 * @rem;
    background: #fff;
    .game-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18 * @rem 0 16 * @rem;
      .game-img {
        flex: 0 0 56 * @rem;
        width: 56 * @rem;
        height: 56 * @rem;
      }
      .game-detail {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 50 * @rem;
        margin: 0 8 * @rem;
        .game-title {
          font-size: 15 * @rem;
          font-weight: 500;
        }
        .detail-text {
          color: #777777;
        }
      }
      .button {
        flex: 0 0 78 * @rem;
        height: 32 * @rem;
        background: @themeBg;
        border-radius: 6 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 78 * @rem;
        color: #fff;
      }
    }
    .game-boon {
      height: 50 * @rem;
      border-top: 1px solid #efefef;
      font-size: 13 * @rem;
      color: #666666;
      line-height: 50 * @rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
