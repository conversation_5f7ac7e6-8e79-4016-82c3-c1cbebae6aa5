import { request } from '../index';

// 限时兑换列表
export function ApigoldExchangeList(params = {}) {
  return request('/api/gold_exchange/exchangeList', params);
}

// 兑换
/**
 * @param ex_info_id 兑换列表中info列表的id
 * */
export function ApigoldExchangeRaffle(params = {}) {
  return request('/api/gold_exchange/raffle', params);
}

/**
 * 金币兑换平台币
 */
// 金币兑换平台币 - 兑换
export function ApigoldToPtbExchange(params = {}) {
  return request('/api/gold_to_ptb/exchange', params);
}
// 金币兑换平台币 - 兑换列表
export function ApigoldToPtbExchangeList(params = {}) {
  return request('/api/gold_to_ptb/exchangeList', params);
}
// 金币兑换平台币 - 兑换详情
export function ApigoldToPtbExchangeInfo(params = {}) {
  return request('/api/gold_to_ptb/exchangeInfo', params);
}
// 金币兑换平台币 - 检查兑换条件
export function ApigoldToPtbCheckExchange(params = {}) {
  return request('/api/gold_to_ptb/checkExchange', params);
}

/**
 * 金币兑换游戏道具
 */

// 金币兑换游戏道具 - 更多游戏
export function ApiGameGetCardGameList(params = {}) {
  return request('/api/game/getCardGameList', params);
}
// 金币兑换游戏道具 - 单个游戏的礼包信息
export function ApiGameGetGameCardList(params = {}) {
  return request('/api/game/getGameCardList', params);
}


/**
 * 金币兑换SVIP、省钱卡、云挂机时长等
 */

// 金币兑换详情
export function ApiCoinCenterGetExchangeGoodsDetail(params = {}) {
  return request('/v2024/coin_center/getExchangeGoodsDetail', params);
}

// 兑换
export function ApiCoinCenterExchange(params = {}) {
  return request('/v2024/coin_center/exchange', params);
}
