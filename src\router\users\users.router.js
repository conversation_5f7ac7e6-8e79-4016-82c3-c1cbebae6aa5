export default [
  {
    path: '/login',
    name: 'Login',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/login_hw',
    name: 'LoginHw',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/register_hw',
    name: 'RegisterHw',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/change_email',
    name: 'ChangeEmail',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/force_email',
    name: 'ForceEmail',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: false,
    },
  },
  {
    path: '/phone_login',
    name: 'PhoneLogin',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/fast_register',
    name: 'FastRegister',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/select_h5_acount',
    name: 'SelectH5Acount',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/user_info',
    name: 'UserInfo',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
      keepAlive: false,
    },
  },
  {
    path: '/id_card',
    name: 'IdCard',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/upload_idcard',
    name: 'UploadIdCard',
    component: () =>
      import(
        /* webpackChunkName: "users" */ '@/views/Users/<USER>/UploadIdCard'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/change_nickname',
    name: 'ChangeNickname',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/account_cancel',
    name: 'AccountCancel',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/change_qq',
    name: 'ChangeQQ',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/change_phone',
    name: 'ChangePhone',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/change_sex',
    name: 'ChangeSex',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/change_birthday',
    name: 'ChangeBirthday',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/my_gift',
    name: 'MyGift',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/gift_recovery',
    name: 'GiftRecovery',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_coupon',
    name: 'MyCoupon',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/coupon_use_detail/:id',
    name: 'CouponUseDetail',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/change_password',
    name: 'ChangePassword',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/change_password_hw',
    name: 'ChangePasswordHw',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/set_security',
    name: 'SetSecurity',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/my_feedback',
    name: 'MyFeedback',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_cashgift',
    name: 'MyCashGift',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_cashgift/cashgift_explain',
    name: 'CashGiftExplain',
    component: () =>
      import(
        /* webpackChunkName: "users" */ '@/views/Users/<USER>/CashGiftExplain'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_complaint',
    name: 'MyComplaint',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/exp_help',
    name: 'ExpHelp',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/pay_help',
    name: 'PayHelp',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/pay_help_explain',
    name: 'PayHelpExplain',
    component: () =>
      import(
        /* webpackChunkName: "users" */ '@/views/Users/<USER>/PayHelpExplain'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/pay_help_detail',
    name: 'PayHelpDetail',
    component: () =>
      import(
        /* webpackChunkName: "users" */ '@/views/Users/<USER>/PayHelpDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_rebate',
    name: 'MyRebate',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/questionnaire',
    name: 'Questionnaire',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/add_assistant',
    name: 'AddAssistant',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/box_beta',
    name: 'BoxBeta',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      requiresAuth: false,
      keepAlive: true,
    },
  },
];
