<template>
  <div class="yy-subscribed-btn" @click.stop="handleClick()">
    <slot>
      <div
        class="yuyue btn"
        :class="{ had: Number(gameInfo.subscribed) }"
        @click.stop="handleSubscribe(gameInfo)"
      >
        {{ Number(gameInfo.subscribed) ? `${$t('已预约')}` : `${$t('预约')}` }}
      </div>
    </slot>
  </div>
</template>

<script>
import { ApiGameSubscribe } from '@/api/views/game';
import { isIos, isAndroid } from '@/utils/userAgent';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'yySubscribeBtn',
  props: {
    gameInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      isIos,
      isAndroid,
    };
  },
  computed: {
    ...mapGetters({
      dlConfig: 'system/dlConfig',
    }),
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
    // 预约
    async handleSubscribe(gameInfo) {
      if (Number(gameInfo.subscribed) == 1) {
        return false;
      }
      let status = Number(gameInfo.subscribed) == 0 ? 1 : -1;
      try {
        const res = await ApiGameSubscribe({ gameId: gameInfo.id, status });
        if (res.code > 0) {
          this.gameInfo.subscribed = status == 1 ? 1 : 0;
          if (res.data && res.data.wxstatus == 1) {
            this.$dialog({
              title: this.$t('预约成功'),
              message: res.data.wxinfo,
              showCancelButton: true,
              cancelButtonText: this.$t('关闭'),
              confirmButtonText: this.$t('设置微信提醒'),
              lockScroll: false,
            }).then(() => {
              this.toPage('BindWeChat');
            });
          } else {
            this.$toast(res.msg);
          }
        }
      } catch (e) {}
    },
  },
};
</script>

<style lang="less" scoped>
.yuyue {
  box-sizing: border-box;
  width: 64 * @rem;
  height: 30 * @rem;
  background: linear-gradient(84deg, #ff8d23 0%, #ffb546 100%);
  border-radius: 31 * @rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12 * @rem;
  color: #fff;
  &.had {
    font-size: 12 * @rem;
    background: #d1d1d1;
    color: #fff;
  }
}
</style>
