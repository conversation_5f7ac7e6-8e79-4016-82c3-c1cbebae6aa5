<template>
  <div class="rebate">
    <nav-bar-2 :border="true" class="nav-bar" :title="$t('返利申请')">
      <template #right>
        <div
          class="text"
          @click="toPage('Iframe', { title: $t('返利指南'), url: guideUrl })"
        >
          {{ $t('返利指南') }}
        </div>
      </template>
    </nav-bar-2>
    <van-tabs v-model="current" color="#FE6600" animated swipeable>
      <van-tab :title="$t('返利申请')">
        <index-type1></index-type1>
      </van-tab>
      <van-tab :title="$t('申请记录')">
        <index-type2></index-type2>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import H5page from '@/utils/h5Page';
import IndexType1 from './components/IndexType1';
import IndexType2 from './components/IndexType2';

export default {
  name: 'Rebate',
  data() {
    return {
      guideUrl: H5page.fanlizhinan,
      tabList: [
        {
          title: this.$t('返利申请'),
        },
        {
          title: this.$t('申请记录'),
        },
      ],
      current: 0,
    };
  },
  components: {
    IndexType1,
    IndexType2,
  },
};
</script>
<style lang="less" scoped>
/deep/ .van-tab--active .van-tab__text--ellipsis {
  color: @themeColor;
}
/deep/ .van-tab__text--ellipsis {
  font-size: 15 * @rem;
}
.nav-bar {
  .text {
    color: #000;
  }
}
</style>
