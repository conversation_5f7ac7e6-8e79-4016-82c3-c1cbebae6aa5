<template>
  <div class="change-nickname-page page">
    <nav-bar-2 :border="true" :title="$t('更改用户昵称')">
      <template #right>
        <div class="save" @click="save">{{ $t('保存') }}</div>
      </template>
    </nav-bar-2>
    <div class="input-container">
      <input
        class="input-text"
        v-model="inputText"
        type="text"
        :placeholder="$t('请输入昵称')"
        autofocus="autofocus"
      />
    </div>
  </div>
</template>

<script>
import { ApiChangeInfo } from '@/api/views/users';
import { mapMutations } from 'vuex';

export default {
  name: 'ChangeNickname',
  data() {
    return {
      inputText: '',
    };
  },
  mounted() {
    if (this.userInfo.nickname != '') {
      this.inputText = this.userInfo.nickname;
    }
  },
  methods: {
    ...mapMutations({
      setUserInfo: 'user/setUserInfo',
    }),
    save() {
      ApiChangeInfo({ nickname: this.inputText }).then(res => {
        this.$toast(res.msg);
        this.setUserInfo(res.data);
        this.$router.go(-1);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.change-nickname-page {
  background-color: #f6f6f6;
  .save {
    font-size: 15 * @rem;
    color: #000;
  }
  .input-container {
    box-sizing: border-box;
    width: 100%;
    height: 55 * @rem;
    padding: 0 14 * @rem;
    margin-top: 20 * @rem;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .input-text {
      outline: none;
      border: 0;
      display: block;
      width: 100%;
      height: 100%;
      font-size: 15 * @rem;
      color: #333333;
      background-color: transparent;
    }
  }
}
</style>
