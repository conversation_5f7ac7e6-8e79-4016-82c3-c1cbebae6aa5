<template>
  <div class="page">
    <nav-bar-2
      bgStyle="transparent"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :azShow="true"
      :title="pageTitle"
    >
      <template #right>
        <div class="total-num btn" :style="{ background: totalBgColor }">
          {{ $t('共') }}{{ num }}{{ $t('题') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="content">
        <div class="title">
          {{ $t('首次提交可随机获得')
          }}<span>{{ min_gold }}~{{ max_gold }}{{ $t('金币') }}</span>
        </div>

        <div class="desc">{{ content }}</div>
        <div class="question-list" v-if="answerList.length">
          <div
            class="question-item"
            v-for="(item, index) in questionList"
            :key="index"
          >
            <div class="question-title">
              {{ index + 1 }}&emsp;{{ item.title
              }}<span v-if="item.is_answer">*</span>
            </div>
            <van-radio-group
              v-model="answerList[index].option_id"
              v-if="item.type == 0"
              class="option-list"
            >
              <template v-for="(option, optionIndex) in item.options">
                <van-radio
                  class="option-item"
                  :name="option.options_id"
                  :key="optionIndex"
                  checked-color="#FE6600"
                  icon-size="16"
                  >{{ option.options_name }}</van-radio
                >
                <textarea
                  class="textarea"
                  v-model="answerList[index].content"
                  :key="'textarea' + optionIndex"
                  v-if="
                    option.is_other &&
                    option.options_id == answerList[index].option_id
                  "
                  placeholder="请输入"
                ></textarea>
              </template>
            </van-radio-group>
            <van-checkbox-group
              v-model="answerList[index].option_id"
              v-if="item.type == 1"
              class="option-list"
            >
              <template v-for="(option, optionIndex) in item.options">
                <van-checkbox
                  class="option-item"
                  checked-color="#FE6600"
                  icon-size="16"
                  shape="square"
                  :name="option.options_id"
                  :key="optionIndex"
                  >{{ option.options_name }}</van-checkbox
                >
                <textarea
                  class="textarea"
                  v-model="answerList[index].content"
                  :key="'textarea' + optionIndex"
                  v-if="
                    option.is_other &&
                    answerList[index].option_id.includes(option.options_id)
                  "
                  placeholder="请输入"
                ></textarea>
              </template>
            </van-checkbox-group>

            <textarea
              class="textarea"
              v-model="answerList[index].content"
              placeholder="请输入"
              v-if="item.type == 2"
            ></textarea>
          </div>
        </div>
        <div class="submit" @click="submit" v-if="answerList.length">提交</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiQuestionnaireIndex,
  ApiQuestionnaireSubmit,
} from '@/api/views/questionnaire.js';
export default {
  data() {
    return {
      pageTitle: '',
      questionList: [],
      max_gold: 0,
      min_gold: 0,
      question_id: 0,
      num: 0,
      content: '',
      navbarOpacity: 0,
      answerList: [],
      totalBgColor: 'rgba(255, 255, 255, .3)',
    };
  },
  watch: {
    answerList(val) {},
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.getQuestionList();
    this.answerList = this.questionList.map(item => {
      switch (item.type) {
        case 0: // 单选
          return {
            question_id: item.id,
            option_id: 0,
            content: '',
          };
        case 1: // 多选
          return {
            question_id: item.id,
            option_id: [],
            content: '',
          };
        case 2: // 填空
          return {
            question_id: item.id,
            content: '',
          };
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async submit() {
      const res = await ApiQuestionnaireSubmit({
        question_id: this.question_id,
        answerList: this.answerObj(this.answerList),
      });
      setTimeout(() => {
        this.back();
      }, 1000);
    },
    answerObj(answerList) {
      // 答案数组转成对象
      let obj = {};
      answerList.forEach(item => {
        let { question_id, ...left } = item;
        obj[question_id] = left;
      });
      return obj;
    },
    async getQuestionList() {
      const res = await ApiQuestionnaireIndex();
      let { list, max_gold, min_gold, question_id, num, content } = res.data;
      this.questionList = list;
      this.max_gold = max_gold;
      this.min_gold = min_gold;
      this.question_id = question_id;
      this.num = num;
      this.content = content;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 120) {
        this.totalBgColor = 'rgba(255,210,117, .5)';
        this.pageTitle = '有奖调研';
      } else {
        this.totalBgColor = 'rgba(255, 255, 255, .3)';
        this.pageTitle = '';
      }
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  height: auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #ffe483 0%, #ff9649 100%);
  display: flex;
  flex-direction: column;
  padding-top: calc(50 * @rem + @safeAreaTop);
  padding-top: calc(50 * @rem + @safeAreaTopEnv);
  .total-num {
    width: 60 * @rem;
    height: 24 * @rem;
    border-radius: 35 * @rem;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    color: #a63f13;
    transition: 0.3s background-color;
  }
  .main {
    box-sizing: border-box;
    flex: 1;
    flex-grow: 1;
    margin: 8 * @rem 10 * @rem;
    position: relative;
    z-index: 2;
    background-color: #fff;
    border-radius: 30 * @rem;
    &::before {
      content: '';
      position: absolute;
      top: -8 * @rem;
      left: 50%;
      transform: translateX(-50%);
      margin: 0 auto;
      width: 375 * @rem;
      height: 146 * @rem;
      background: url(~@/assets/images/questionnaire/questionnaire-top-bg.png)
        center center no-repeat;
      background-size: 375 * @rem 146 * @rem;
      z-index: 1;
    }
    .content {
      box-sizing: border-box;
      width: 100%;
      position: relative;
      z-index: 2;
      padding: 46 * @rem 15 * @rem 30 * @rem;
      .title {
        font-size: 14 * @rem;
        color: #333333;
        font-weight: bold;
        line-height: 17 * @rem;
        span {
          color: #ff3d00;
          font-weight: bold;
        }
      }
      .desc {
        font-size: 12 * @rem;
        color: #777777;
        line-height: 17 * @rem;
        margin-top: 8 * @rem;
      }
      .question-list {
        .question-item {
          margin-top: 30 * @rem;
          .question-title {
            font-size: 14 * @rem;
            font-weight: bold;
            color: #333333;
            line-height: 16 * @rem;
            span {
              color: @themeColor;
            }
          }
          .option-list {
            display: flex;
            flex-wrap: wrap;
            .option-item {
              min-width: 50%;
              margin-top: 20 * @rem;
              /deep/ span {
                font-size: 14 * @rem;
                color: #333333;
                line-height: 18 * @rem;
              }
            }
          }
          .textarea {
            box-sizing: border-box;
            display: block;
            width: 100%;
            min-height: 32 * @rem;
            border: 1 * @rem solid #efefef;
            border-radius: 6 * @rem;
            padding: 5 * @rem 10 * @rem;
            font-size: 14 * @rem;
            margin-top: 10 * @rem;
            resize: none;
          }
        }
      }
      .submit {
        width: 160 * @rem;
        height: 36 * @rem;
        margin: 30 * @rem auto 0;
        border-radius: 18 * @rem;
        background-color: #ff7e06;
        color: #ffffff;
        font-size: 15 * @rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    &::after {
      content: '';
      position: absolute;
      bottom: -12 * @rem;
      left: 50%;
      transform: translateX(-50%);
      margin: 0 auto;
      width: 375 * @rem;
      height: 146 * @rem;
      background: url(~@/assets/images/questionnaire/questionnaire-bottom-bg.png)
        center center no-repeat;
      background-size: 375 * @rem 146 * @rem;
      z-index: 1;
    }
  }
}
</style>
