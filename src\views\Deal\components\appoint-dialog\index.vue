<template>
  <div>
    <!-- 指定确认/取消框 -->
    <van-dialog
      v-model="isShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      :before-close="close"
      class="dialog-container"
      get-container="body"
    >
      <div class="title">{{ $t('重要提示') }}</div>
      <div class="text">{{ text }}</div>
      <div class="operate-bar">
        <div class="operate-btn cancel" @click="close">{{ $t('取消') }}</div>
        <div class="operate-btn confirm" @click="confirm">{{ $t('确定') }}</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiXiaohaoAppointUser,
  ApiXiaohaoCancelBargain,
} from '@/api/views/xiaohao.js';
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
      required: true,
    },
    tradeId: {
      type: Number | String,
      required: true,
    },
    bargainId: {
      type: Number | String,
      required: true,
    },
    cancelAppoint: {
      type: Boolean,
      default: false,
    },
    amount: {
      type: Number,
    },
  },
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit('update:isShow', false);
    },
    async confirm() {
      let res;
      try {
        if (this.cancelAppoint) {
          // 取消指定
          res = await ApiXiaohaoCancelBargain({
            tradeId: this.tradeId,
            bargainId: this.bargainId,
          });
        } else {
          // 确认指定
          res = await ApiXiaohaoAppointUser({
            tradeId: this.tradeId,
            bargainId: this.bargainId,
            oldAmount: this.amount,
          });
        }
      } finally {
        this.close();
        this.$emit('refresh');
      }
      if (res.code > 0) {
        this.$toast(res.msg);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dialog-container {
  box-sizing: border-box;
  width: 244 * @rem !important;
  height: 161 * @rem;
  background-color: #fff;
  padding: 16 * @rem 15 * @rem 19 * @rem;
  .title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: bold;
    line-height: 25 * @rem;
    text-align: center;
  }
  .text {
    text-align: center;
    font-size: 13 * @rem;
    color: #000000;
    line-height: 23 * @rem;
    margin-top: 19 * @rem;
  }
  .operate-bar {
    margin-top: 24 * @rem;
    display: flex;
    justify-content: space-between;
    .operate-btn {
      width: 102 * @rem;
      height: 32 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 18 * @rem;
      font-size: 13 * @rem;
      &.confirm {
        background: @themeBg;
        color: #fff;
      }
      &.cancel {
        background: #f2f2f2;
        color: #7d7d7d;
      }
    }
  }
}
</style>
