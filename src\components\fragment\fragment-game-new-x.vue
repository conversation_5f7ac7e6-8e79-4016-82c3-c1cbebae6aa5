<template>
  <div class="fragment-game-list">
    <div class="fragment-title">
      <div class="title-text">{{ info.header_title }}</div>
      <div class="right-icon" v-if="info.action_code" @click="clickMore"></div>
    </div>
    <div class="game-list">
      <div
        class="game-item"
        v-for="(item, index) in info.big_update_list"
        @click="goToGame(item.game, index)"
        :key="index"
        v-sensors-exposure="gameExposure(item.game, index)"
      >
        <div class="date" v-html="formateDate(item.date_text)"></div>
        <div class="line"></div>
        <div class="game-icon">
          <img :src="item.game.titlepic" alt="" />
        </div>
        <div class="game-name">{{ item.game.title }}</div>
        <div class="download-btn" v-if="info.is_show_btn">下载</div>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js'
export default {
  name: 'FragmentGameListX',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item, index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
      });
      this.$sensorsModuleSet(this.info.header_title)
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
    formateDate(date) {
      return date.replace(/\n/g, '<br>');
    },
    clickMore() {
      handleActionCode(this.info)
    },
  },
};
</script>

<style lang="less" scoped>
.fragment-game-list {
  padding: 7 * @rem 0;
  margin: 0 12 * @rem;
  background: #fff;
  border-radius: 12 * @rem;
  .fragment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7 * @rem 0;
    margin: 0 12 * @rem;
    .title-text {
      font-size: 16 * @rem;
      font-weight: bold;
      color: #191b1f;
      line-height: 16 * @rem;
    }
    .right-icon {
      width: 6 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/right-icon.png) right center no-repeat;
      background-size: 6 * @rem 10 * @rem;
      padding-left: 20 * @rem;
    }
  }
  .game-list {
    display: flex;
    overflow-x: auto;
    padding: 10 * @rem 3 * @rem;
    &::-webkit-scrollbar {
      display: none;
    }
    .date {
      text-align: center;
      font-size: 10 * @rem;
      color: #60666c;
      line-height: 12 * @rem;
    }

    .game-item {
      flex-shrink: 0;
      width: 70 * @rem;
      .line {
        margin: 12 * @rem 0;
        height: 3 * @rem;
        background: #ecfbf4;
        position: relative;
        &::after {
          content: '';
          box-sizing: border-box;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%) rotate(45deg);
          width: 9 * @rem;
          height: 9 * @rem;
          border: 3 * @rem solid #ecfbf4;
          background: #1cce94;
        }
      }
      .game-icon {
        width: 48 * @rem;
        height: 48 * @rem;
        margin: 0 auto;

        img {
          border-radius: 12 * @rem;
        }
      }

      .game-name {
        box-sizing: border-box;
        width: 100%;
        padding: 0 5 * @rem;
        height: 12 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #191b1f;
        line-height: 12 * @rem;
        margin-top: 5 * @rem;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .download-btn {
        width: 54 * @rem;
        height: 26 * @rem;
        border-radius: 14 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11 * @rem;
        color: #ffffff;
        background: #1cce94;
        margin: 6 * @rem auto 0;
      }
    }
  }
}
</style>
