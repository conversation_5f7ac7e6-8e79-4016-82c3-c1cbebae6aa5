<template>
  <div class="popularize-list">
    <nav-bar-2
      :placeholder="false"
      :bgStyle="navbarOpacity > 0.8 ? 'transparent' : 'transparent-white'"
      :title="'安利墙'"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="list">
      <div
        v-for="item in list"
        :key="item.id"
        @click="
          toPage('PopularizeDetail', {
            id: item.game.id,
            gameInfo: item.game,
            content: item.content,
          })
        "
        class="item"
      >
        <div class="top">
          <img :src="item.game.titlepic" class="game-img" />
          <div class="right">
            <div class="game-title">{{ item.game.title }}</div>
            <div class="tag-list">
              <div
                v-for="(item2, index) in item.game.app_tag"
                :key="index"
                class="tag-item"
              >
                {{ item2.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="center">{{ item.content }}</div>
        <div v-if="item.game.up_info" class="user">
          <user-avatar
            class="avatar"
            :src="item.game.up_info.avatar"
            :self="false"
          />
          <div class="user-name">{{ item.game.up_info.nickname }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ApiIndexSpIndexV2 } from '@/api/views/home.js';
import { ApiGameGetRecommendList } from '@/api/views/game.js';

export default {
  name: 'PopularizeList',
  data() {
    return {
      navbarOpacity: 0,
      list: [],
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    const res = await ApiGameGetRecommendList();
    this.list = res.data.list;
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.popularize-list {
  min-height: 100vh;
  .image-bg('~@/assets/images/up/popularize_list_bg.png');
  background-color: #6e62ff;
  overflow: hidden;
  .list {
    margin-top: 190 * @rem;
    .item {
      padding: 15 * @rem;
      margin: 12 * @rem 18 * @rem;
      background: linear-gradient(180deg, #ffffff 24%, #ffffff 100%);
      box-shadow: 0 * @rem 0 * @rem 10 * @rem 0 * @rem rgba(7, 7, 7, 0.03);
      opacity: 0.95;
      border-radius: 5 * @rem;
      .top {
        display: flex;
        .game-img {
          flex: 0 0 60 * @rem;
          width: 60 * @rem;
          height: 60 * @rem;
          margin-right: 10 * @rem;
          border-radius: 10 * @rem;
        }
        .game-title {
          margin-top: 5 * @rem;
          font-size: 18 * @rem;
          font-weight: 600;
          color: #333333;
          line-height: 23 * @rem;
        }
        .tag-list {
          margin-top: 5 * @rem;
          .tag-item {
            color: #999;
          }
        }
      }
      .center {
        margin: 12 * @rem 0 24 * @rem;
        color: #666666;
        line-height: 15 * @rem;
      }
      .user {
        display: flex;
        align-items: center;
        .avatar {
          width: 24 * @rem;
          height: 24 * @rem;
        }
        .user-name {
          margin-left: 8 * @rem;
          font-size: 14 * @rem;
          color: #333333;
          line-height: 16 * @rem;
        }
      }
    }
  }
}
</style>
