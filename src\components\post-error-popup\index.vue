<template>
  <div class="error-page">
    <nav-bar-2 title="" :azShow="true"></nav-bar-2>
    <content-empty
      :emptyImg="defaultBase64.networkImage"
      :tips="tips"
      :showRetry="true"
      @retry="retry"
      class="empty-content"
    ></content-empty>
  </div>
</template>

<script>
import { defaultBase64 } from '@/utils/defaultBase64.js';
import store from '@/store';
export default {
  name: 'postErrorPopup',
  data() {
    return { defaultBase64, tips: '网络异常，请稍候重试' };
  },
  created() {
    if (!navigator.onLine) {
      this.tips = '无网络，请检查网络设置';
    }
  },
  methods: {
    retry() {
      if (navigator.onLine) {
        store.commit('system/setPostError', false);
      } else {
        this.$toast('网络异常，请重试');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.error-page {
  display: flex;
  // align-items: center;
  justify-content: center;
  height: 100vh;
}
.empty-content {
  display: block !important;
  margin-top: 25vh;
  /deep/ .van-empty {
    .van-empty__description {
      margin-top: 8 * @rem;
    }

    .retry {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 108 * @rem;
      height: 40 * @rem;
      line-height: 1;
      text-align: center;
      color: #fff;
      font-size: 15 * @rem;
      background: @themeBg;
      border-radius: 29 * @rem;
      text-decoration: none;
    }
  }
}
</style>
