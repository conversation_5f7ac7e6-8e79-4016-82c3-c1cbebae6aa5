<template>
  <div class="game-active-page">
    <nav-bar-2
      bgStyle="transparent"
      :placeholder="false"
      :azShow="true"
    ></nav-bar-2>
    <!-- <div class="main" @click="clickActive"> -->
    <div class="main" @click.stop="clickGet">
      <img :src="activeImg" alt="" />
      <div class="get-btn"></div>
    </div>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">{{ $t('礼包码') }}</div>
      </div>
      <div class="cardpass">{{ cardInfo.cardpass }}</div>
      <div class="desc">{{ introduction }}</div>
      <div class="copy-btn btn" @click="copy(cardInfo.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
      :close-on-click-overlay="true"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span v-if="xiaohaoList.length">{{
                currentXiaohao.nickname
              }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div
              class="xiaohao-list"
              :class="{ on: xiaohaoListShow }"
              v-if="xiaohaoList.length"
            >
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(id)"
    ></xh-create-tip-dialog>
    <!-- 金币不足弹窗 -->
    <van-dialog> </van-dialog>
  </div>
</template>
<script>
import { platform } from '@/utils/box.uni.js';
import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog/index.vue';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import { ApiCardRead, ApiCardGet } from '@/api/views/gift.js';
export default {
  // 少年西游记
  name: 'GameSnxyj',
  components: {
    xhCreateTipDialog,
  },
  data() {
    return {
      id: '20016883', // 游戏id
      cardId: '43405', // 礼包id
      activeImg: 'https://static-hw.3733.com/wa/activity/snxyj.png', // 主题图
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      currentXiaohao: {}, //当前选择小号
      cardInfo: {}, // 礼包码信息
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    introduction() {
      return `${this.$t('使用说明')}：${this.cardInfo.cardtext}`;
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('少年西游记（送毕业金蝉）');
    }
    // a环境测试 游戏id 和礼包id
    if (this.$h5Page.env == 'aa') {
      this.id = '20016211';
      this.cardId = '38471';
    }
    await this.getXhList();
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    // clickActive() {
    //   BOX_goToGame(
    //     {
    //       params: {
    //         id: this.id,
    //       },
    //     },
    //     { id: this.id }
    //   );
    // },
    clickGet() {
      // 先判断是否登录，未登录则跳转登录页
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        // 判断该游戏是否有小号，没有则弹出对话框
        if (!this.xiaohaoList.length) {
          this.createDialogShow = true;
          return false;
        }
        // 弹出小号选择框
        this.xiaohaoListShow = false;
        this.xhDialogShow = true;
      }
    },
    // 点击选择小号
    xiaoHaoListClick(item) {
      this.currentXiaohao = item;
      this.xiaohaoListShow = false;
    },
    // 关闭选择小号弹窗
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    // 选择完小号执行领取操作
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      // 开始领取操作
      let params = {
        gameId: this.id,
        xhId: this.currentXiaohao.id,
        cardId: this.cardId,
      };
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        this.$nextTick(async () => {
          const res = await ApiCardRead({
            cardId: this.cardId,
            xhId: this.currentXiaohao.id,
          });
          let info = res.data;
          if (!info.cardpass) {
            const getRes = await ApiCardGet({
              cardId: this.cardId,
              xhId: this.currentXiaohao.id,
            });
            this.cardInfo = getRes.data;
          } else {
            this.cardInfo = info;
          }
          this.$toast.clear();
          this.copyDialogShow = true;
        });
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    // 获取小号列表
    async getXhList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
      // 如果没有选择的小号，默认选择第一个
      if (!this.currentXiaohao.id) {
        this.currentXiaohao = this.xiaohaoList[0];
      }
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.game-active-page {
  min-height: 100vh;
  .main {
    width: 100%;
    position: relative;
    img {
      width: 100%;
      height: auto;
    }
    .get-btn {
      position: absolute;
      top: 800 * @rem;
      left: 50%;
      transform: translateX(-50%);
      width: 170 * @rem;
      height: 45 * @rem;
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 244 * @rem;
  border-radius: 12 * @rem;
  background-color: #fff;
  padding: 20 * @rem 16 * @rem 22 * @rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .title-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      .image-bg('~@/assets/images/games/gift-title-icon.png');
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 4 * @rem;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 209 * @rem;
    height: 39 * @rem;
    background-color: #f4f4f4;
    border-radius: 6 * @rem;
    display: flex;
    align-items: center;
    padding: 0 10 * @rem;
    margin-top: 13 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    font-weight: 400;
  }
  .desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    font-weight: 400;
    margin-top: 13 * @rem;
    padding: 0 5 * @rem;
  }
  .copy-btn {
    width: 186 * @rem;
    height: 36 * @rem;
    margin: 20 * @rem auto 0;
    background: @themeBg;
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
