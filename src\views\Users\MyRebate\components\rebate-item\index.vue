<template>
  <div class="rebate-item-component">
    <div class="game-bar">
      <div class="game-icon">
        <img :src="info.game_info.titlepic" alt="" />
      </div>
      <div class="game-name">{{ info.game_info.title }}</div>
    </div>
    <div class="list-container">
      <div class="item">
        <div class="tag" :style="{ color: info.status_color }">
          {{ info.status_str }}
        </div>
        <div class="title">{{ info.activity_title }}</div>
        <div class="info-bar">
          <div class="info-item">
            {{ $t('申请条件') }}：{{ info.threshold_text }}
          </div>
          <div class="info-item" v-if="info.role_info">
            {{ $t('角色区服') }}：{{ info.role_info.role_area_name }}
          </div>
          <div class="info-item" v-if="info.role_info">
            {{ $t('角色ID') }}：{{ info.role_id }}
          </div>
          <div class="info-item" v-if="info.role_info">
            {{ $t('角色名称') }}：{{ info.role_info.name }}
          </div>
        </div>
        <div class="bottom-bar" v-if="info.status != 2">
          <!-- 驳回理由 -->
          <div class="reason" v-if="info.rejection">
            {{ $t('驳回理由') }}：{{ info.rejection }}
          </div>
          <!-- 发放模式 -->
          <div class="tips" v-if="info.send_type == 1">
            {{ $t('发放模式') }}：{{
              $t('您的返利已发放至游戏内邮箱,请登录游戏查看领取。')
            }}
          </div>
          <div class="tips" v-if="info.send_type == 2">
            {{ $t('发放模式') }}：{{
              $t('您的返利以礼包码形式发放，复制后进游戏领取。')
            }}
          </div>
          <!-- 礼包码 -->
          <div class="gift-code" v-if="info.card">
            <div class="code-text">{{ $t('礼包码') }}：{{ info.card }}</div>
            <div class="copy" @click="copy(info.card)">{{ $t('复制') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'rebateItem',
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.rebate-item-component {
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 12 * @rem;
  margin: 12 * @rem 0 0;
  padding: 0 14 * @rem;
  .game-bar {
    height: 42 * @rem;
    display: flex;
    align-items: center;
    .game-icon {
      width: 20 * @rem;
      height: 20 * @rem;
    }
    .game-name {
      font-size: 13 * @rem;
      color: #000000;
      margin-left: 5 * @rem;
    }
  }
  .list-container {
    .item {
      position: relative;
      border-top: 0.5 * @rem solid #f3f3f8;
      padding: 10 * @rem 0 15 * @rem;
      .tag {
        position: absolute;
        right: 0;
        top: 12 * @rem;
        font-size: 12 * @rem;
        color: #46a83a;
        line-height: 17 * @rem;
        &.green {
          color: #46a83a;
        }
        &.orange {
          color: @themeColor;
        }
        &.red {
          color: #fe4a55;
        }
      }
      .title {
        font-size: 15 * @rem;
        color: #000000;
        font-weight: bold;
        line-height: 22 * @rem;
        padding-right: 50 * @rem;
      }
      .info-bar {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10 * @rem;
        .info-item {
          box-sizing: border-box;
          width: 50%;
          font-size: 13 * @rem;
          line-height: 22 * @rem;
          color: #545454;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-bottom: 4 * @rem;
          &:nth-of-type(2n + 1) {
            padding-right: 10 * @rem;
          }
        }
      }
      .bottom-bar {
        box-sizing: border-box;
        border-radius: 4 * @rem;
        background-color: #f5f5f6;
        margin-top: 10 * @rem;
        padding: 5 * @rem 8 * @rem;
        .reason {
          font-size: 13 * @rem;
          color: #3b3b3b;
          line-height: 22 * @rem;
          padding: 0 4 * @rem;
        }
        .tips {
          font-size: 11 * @rem;
          line-height: 16 * @rem;
          color: #9a9a9a;
        }
        .gift-code {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          line-height: 22 * @rem;
          .code-text {
            margin-right: 8 * @rem;
            font-size: 11 * @rem;
            color: @themeColor;
          }
          .copy {
            width: 32 * @rem;
            height: 18 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10 * @rem;
            color: #ffffff;
            background-color: @themeColor;
            border-radius: 9 * @rem;
          }
        }
      }
    }
  }
}
</style>
