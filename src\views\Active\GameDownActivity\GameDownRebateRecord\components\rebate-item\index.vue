<template>
  <div class="rebate-item-component">
    <div class="game-bar">
      <div class="game-icon">
        <img :src="info.titlepic" alt="" />
      </div>
      <div class="game-info">
        <div class="game-name">{{ info.title }}</div>
        <div class="status">
          申请状态：<span
            :class="{ success: info.status == 1, fail: info.status == 2 }"
            >{{ info.status_str }}</span
          >
        </div>
        <div class="content">申请礼包：{{ info.reward_type_str }}</div>
      </div>
      <div class="right-btn" @click="goToDetail">申请详情</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'rebateItem',
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    goToDetail() {
      this.toPage('GameDownRebateDetail', {
        id: this.info.id,
        info: this.info,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.rebate-item-component {
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 12 * @rem;
  margin: 12 * @rem 0 0;
  padding: 0 12 * @rem;
  .game-bar {
    height: 83 * @rem;
    display: flex;
    align-items: center;
    .game-icon {
      width: 60 * @rem;
      height: 60 * @rem;
    }
    .game-info {
      flex: 1;
      min-width: 0;
      margin-left: 8 * @rem;
      .game-name {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 500;
        line-height: 20 * @rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .status {
        font-size: 11 * @rem;
        color: #909090;
        line-height: 15 * @rem;
        margin-top: 4 * @rem;
        span {
          color: #ff6957;
          &.success {
            color: #21b98a;
          }
          &.fail {
            color: #909090;
          }
        }
      }
      .content {
        font-size: 11 * @rem;
        color: #909090;
        line-height: 15 * @rem;
        margin-top: 5 * @rem;
      }
    }
    .right-btn {
      width: 56 * @rem;
      height: 26 * @rem;
      border-radius: 6 * @rem;
      background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11 * @rem;
      color: #ffffff;
    }
  }
}
</style>
