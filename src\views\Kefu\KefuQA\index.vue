<template>
  <div class="kefu-qa">
    <nav-bar-2 :title="pageTitle" :border="true" :azShow="true"></nav-bar-2>
    <van-collapse v-model="activeNames" accordion>
      <van-collapse-item
        v-for="(item, index) in list"
        :key="index"
        :title="item.title"
        :name="index"
      >
        <div v-html="item.content"></div>
      </van-collapse-item>
    </van-collapse>
    <div class="bottom-bar">
      <div @click="connectQiYu()" class="bottom-button" v-if="!noNeedWykefu">
        {{ $t('联系在线客服') }}
      </div>
    </div>
  </div>
</template>
<script>
import { ApiFeedGetQuestionList } from '@/api/views/feedback.js';
import {
  platform,
  BOX_getPackageName,
  BOX_openInNewNavWindow,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'KefuQA',
  data() {
    return {
      pageTitle: '',
      platform,
      id: 0,
      list: [],
      activeNames: ['1'],
    };
  },
  computed: {
    ...mapGetters({
      kefuQQNumber: 'system/kefuQQNumber',
      kefuQQLink: 'system/kefuQQLink',
    }),
    noNeedWykefu() {
      return !!this.kefuQQNumber && !this.kefuQQLink;
    },
  },
  async created() {
    this.id = this.$route.params.id;
    const res = await ApiFeedGetQuestionList({ type: this.id });
    this.pageTitle = res.data.title;
    this.list = res.data.list;
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
    // 不是h5则获取包名 --> 拿到appName -----------> 2022年5月12日14:59:42 现在appName可以从index/extra接口拿
    // if (this.platform != "h5") {
    //   this.updatePackageInfo();
    // }
  },
  methods: {
    connectQiYu() {
      this.openKefu();
    },
    formatAnswer(str) {
      return str.replace(/3733游戏盒子/g, this.appName);
    },
  },
};
</script>
<style lang="less" scoped>
.kefu-qa {
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: calc(80 * @rem + @safeAreaBottom);
  padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
  /deep/ .van-collapse-item__title {
    font-size: 15 * @rem;
    color: #000000;
    padding: 16 * @rem 18 * @rem;
  }
  /deep/ .van-collapse-item__title .van-cell__right-icon::before {
    line-height: 24px;
  }
  /deep/ .van-collapse-item__content {
    font-size: 14 * @rem;
    color: #757575;
  }
}
.bottom-bar {
  width: 100%;
  height: 74 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .bottom-button {
    width: 311 * @rem;
    height: 48 * @rem;
    background: @themeColor;
    border-radius: 24 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
