<template>
  <div ref="saveDom" class="change-phone-page page">
    <nav-bar-2 :border="true" :title="$t('修改密码')"></nav-bar-2>
    <div class="change-content">
      <div class="field">
        <input type="tel" v-model="phone" :placeholder="$t('请输入手机号码')" />
      </div>
      <div class="field">
        <input
          type="number"
          v-model="authCode"
          maxlength="6"
          :placeholder="$t('请输入验证码')"
        />
        <div class="right">
          <div class="text" v-if="!ifCount" @click="captchaClick()">
            {{ $t('获取验证码') }}
          </div>
          <div class="text text2" v-else>{{ `${countdown}s` }}</div>
        </div>
      </div>
      <div class="field">
        <input
          :type="open ? 'text' : 'password'"
          v-model="password"
          :placeholder="$t('请输入新密码')"
        />
        <div class="right">
          <div class="eyes" :class="{ open: open }" @click="isShow()"></div>
        </div>
      </div>
    </div>
    <div class="save btn" :class="{ gray: isCommit }" @click="save">
      {{ $t('提交') }}
    </div>
  </div>
</template>

<script>
import { ApiResetPassword, ApiAuthCode } from '@/api/views/users';
import { checkIphone } from '@/utils/formValidation';

export default {
  name: 'ChangePassword',
  data() {
    return {
      phone: '', //手机号码
      authCode: '', //验证码
      countdown: 60, //倒计时
      ifCount: false, //倒计时开关
      password: '', //当前密码
      open: false, //是否显示当前密码
      isCommit: false, //当前是否提交中
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  created() {
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    isShow() {
      this.open = !this.open;
    },
    save() {
      if (this.isCommit) {
        this.$toast(this.$t('正在修改中...'));
        return false;
      }
      if (!this.phone) {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (!checkIphone(this.phone)) {
        this.$toast(this.$t('请输入正确手机号码'));
        return false;
      }
      if (!this.authCode) {
        this.$toast(this.$t('请输入验证码'));
        return false;
      }
      if (!this.password) {
        this.$toast(this.$t('密码不能为空'));
        return false;
      }
      this.isCommit = !this.isCommit;
      ApiResetPassword({
        phone: this.phone,
        code: this.authCode,
        password: this.password,
      })
        .then(res => {
          this.$toast(res.msg);
          this.$router.go(-1);
        })
        .finally(() => {
          this.isCommit = !this.isCommit;
        });
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.phone === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    async getAuthCode(captcha) {
      if (!checkIphone(this.phone)) {
        this.$toast(this.$t('请输入正确手机号码'));
        return false;
      }
      let params = { phone: this.phone, type: 4 };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      // 获取验证码
      let res = await ApiAuthCode(params);
      this.$toast(res.msg);
      // 出现倒计时，颜色变暗
      this.ifCount = !this.ifCount;
      let fun = setInterval(() => {
        this.countdown--;
        if (this.countdown === -1) {
          clearInterval(fun);
          this.countdown = 60;
          this.ifCount = !this.ifCount;
        }
      }, 1000);
    },
  },
};
</script>

<style lang="less" scoped>
.change-phone-page {
  .change-content {
    padding: 0 14 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 45 * @rem;
      margin-top: 20 * @rem;
      border-bottom: 1 * @rem solid #d5d3d3;
      input {
        width: 100%;
        height: 100%;
        padding: 0 5 * @rem;
        line-height: 44 * @rem;
        font-size: 16 * @rem;
        letter-spacing: 1 * @rem;
      }
      .right {
        position: relative;
        flex: 0 0 100 * @rem;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 85 * @rem;
        height: 27 * @rem;
        line-height: 27 * @rem;
        border-radius: 14px;
        text-align: center;
        font-size: 13 * @rem;
        overflow: hidden;
        .text {
          width: 100%;
          background: @themeBg;
          color: #fff;
          &.text2 {
            color: #a4a4a4;
            background: #eeeeee;
          }
        }
        span {
          position: absolute;
          top: 0;
          right: 0;
          display: block;
          color: #a4a4a4;
        }
        .eyes {
          width: 18 * @rem;
          height: 7 * @rem;
          padding: 5 * @rem 10 * @rem 5 * @rem 0;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: 0 5 * @rem;
          &.open {
            height: 12 * @rem;
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
    }
  }
  .save {
    width: 347 * @rem;
    height: 45 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 23 * @rem;
    margin: 50 * @rem auto 0;
    background: @themeBg;
    &.gray {
      background: #c4c4c4;
    }
  }
}
</style>
