<template>
  <div
    class="deal-item-component"
    @click="toPage('XiaohaoDetail', { id: info.id })"
  >
    <div class="deal-info">
      <div class="deal-banner" v-if="type == 1">
        <img :src="info.images[0]" alt="" />
      </div>
      <div class="deal-banner game-icon" v-else-if="type == 2">
        <img :src="info.game.titlepic" alt="" />
      </div>
      <div class="right-info">
        <div class="center-info">
          <div class="top-game-info">
            <span class="title" v-if="type == 1">{{ info.title }}</span>
            <span class="title" v-else-if="type == 2">
              {{ info.game.main_title }}
            </span>
            <span
              class="game-subtitle"
              v-if="info.game.subtitle && isShowSubtitle"
              >{{ info.game.subtitle }}</span
            >
          </div>
          <div class="game-info">
            <div class="game-item">
              <div class="game-name" v-if="type == 1">{{
                info.game.main_title
              }}</div>
              <div class="game-area" v-else-if="type == 2">
                {{ $t('区服') }}：{{ info.game_area }}
              </div>
              <div class="platform">
                <div
                  class="plat-icon"
                  v-for="(plat, platIndex) in info.platforms"
                  :key="platIndex"
                >
                  <img :src="plat.icon" alt="" />
                </div>
              </div>
            </div>
            <div class="game-money">{{ $t('实充￥') }}{{ info.pay_sum }}</div>
          </div>

          <div class="bottom-section" v-if="type == 1">
            <div class="date">
              {{ $t('上架时间') }}：{{ formatDate(info.ss_create_time) }}
            </div>
            <div class="appoint-tag" v-if="!!Number(info.specify_mem_id)">
              <div class="appoint-icon"></div>
              <div class="appoint-text">{{ $t('指定出售') }}</div>
            </div>
            <div class="gold gold-vip" v-else-if="info.gold_num_vip">
              {{ $t('立返') }}{{ info.gold_num_vip }}{{ $t('金币') }}
            </div>
            <div class="gold" v-if="info.gold_num_normal">
              {{ $t('立返') }}{{ info.gold_num_normal }}{{ $t('金币') }}
            </div>
            <div class="price">
              ¥<span>{{ Number(info.price).toFixed(0) }}</span>
            </div>
          </div>
          <div class="bottom-section" v-else-if="type == 2">
            <div class="date">
              {{ $t('成交时间') }}：{{ formatDate(info.pay_time) }}
            </div>
            <div class="price">
              ¥<span>{{ Number(info.price).toFixed(0) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dealItem',
  props: {
    info: {
      type: Object,
      required: true,
    },
    type: {
      type: Number,
      default: 1, // 1=购买 2=售出
    },
    isShowSubtitle: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    formatDate(val) {
      let { year, date } = this.$handleTimestamp(val);
      return `${year}-${date}`;
    },
  },
};
</script>

<style lang="less" scoped>
.deal-item-component {
  padding: 14 * @rem 18 * @rem;
  position: relative;
  overflow: hidden;
  .deal-info {
    display: flex;
    align-items: center;
    .deal-banner {
      width: 100 * @rem;
      height: 70 * @rem;
      border-radius: 8 * @rem;
      overflow: hidden;
      background: #e3e5e8;
      &.game-icon {
        width: 70 * @rem;
        height: 70 * @rem;
      }
      img {
        object-fit: cover;
      }
    }
    .right-info {
      flex: 1;
      min-width: 0;
      height: 70 * @rem;
      .center-info {
        margin-left: 12 * @rem;
        flex: 1;
        min-width: 0;
        .top-game-info {
          display: flex;
          align-items: center;
          overflow: hidden;
          .title {
            height: 19 * @rem;
            line-height: 19 * @rem;
            font-size: 15 * @rem;
            color: #30343b;
            font-weight: 600;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .game-subtitle {
            box-sizing: border-box;
            border: 1px solid #e3e5e8;
            border-radius: 4 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 4 * @rem;
            color: #93999f;
            margin-left: 6 * @rem;
            vertical-align: middle;
            line-height: 1;
            white-space: nowrap;
          }
        }
        .title {
          font-size: 14 * @rem;
          color: #30343b;
          font-weight: 600;
          line-height: 18 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .game-info {
          margin-top: 15 * @rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .game-item {
            display: flex;
            align-items: center;
            .game-name {
              max-width: 70 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #60666c;
              line-height: 15 * @rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .game-area {
              max-width: 110 * @rem;
              font-size: 12 * @rem;
              color: #2bbe88;
              font-weight: 400;
              line-height: 15 * @rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .platform {
              margin-left: 6 * @rem;
              display: flex;
              align-items: center;
              .plat-icon {
                width: 16 * @rem;
                height: 16 * @rem;
                border-radius: 6 * @rem;
                overflow: hidden;
                &:not(:first-of-type) {
                  margin-left: 6 * @rem;
                }
              }
            }
          }
          .game-money {
            font-weight: 400;
            font-size: 12 * @rem;
            color: #fe6600;
            white-space: nowrap;
            overflow: hidden;
          }
        }

        .bottom-section {
          display: flex;
          align-items: center;
          height: 25 * @rem;
          line-height: 25 * @rem;
          .date {
            padding-top: 5 * @rem;
            font-size: 11 * @rem;
            color: #93999f;
            font-weight: 400;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
          }
          .gold {
            font-size: 10 * @rem;
            color: #a02d2d;
            background: linear-gradient(90deg, #ffeded 0%, #fff4de 100%);
            border-radius: 4 * @rem;
            height: 22 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 5 * @rem;
            margin-left: 8 * @rem;
            &.gold-vip {
              padding-left: 20 * @rem;
              position: relative;
              &::before {
                content: '';
                display: block;
                width: 31 * @rem;
                height: 24 * @rem;
                .image-bg('~@/assets/images/deal-vip-icon.png');
                position: absolute;
                left: -14 * @rem;
                top: 50%;
                transform: translateY(-50%);
              }
            }
          }
          .appoint-tag {
            display: flex;
            align-items: center;
            height: 22 * @rem;
            background-color: #ffefd8;
            border-radius: 9 * @rem;
            padding: 0 5 * @rem;
            .appoint-icon {
              width: 12 * @rem;
              height: 12 * @rem;
              .image-bg('~@/assets/images/deal/ic_finger.png');
              margin-right: 2 * @rem;
            }
            .appoint-text {
              font-size: 10 * @rem;
              color: #f95725;
            }
          }
          .price {
            height: 25 * @rem;
            line-height: 25 * @rem;
            width: 60 * @rem;
            font-size: 12 * @rem;
            color: #2bbe88;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            span {
              font-size: 20 * @rem;
              color: #2bbe88;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
</style>
