<template>
  <div class="page select-port-page">
    <nav-bar-2 :title="$t('角色端口')" :border="true"></nav-bar-2>
    <div class="main">
      <div class="port-list">
        <div
          class="port-item"
          v-for="(item, index) in play_from"
          :key="index"
          @click="selectPort(item)"
        >
          <div class="port-icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="port-name">{{ item.name }}</div>
          <div class="radio" :class="{ on: current.id == item.id }"></div>
        </div>
      </div>
      <div class="text" v-if="text" v-html="text"></div>
    </div>
  </div>
</template>

<script>
import { ApiXiaohaoGetGameList } from '@/api/views/xiaohao.js';
import { mapMutations, mapGetters } from 'vuex';
export default {
  name: 'SelectXiaohao',
  data() {
    return {
      play_from: {},
      text: '',
      current: {},
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  async created() {
    await this.getPortList();
    if (this.xiaohaoSellInfo.play_from) {
      this.current = this.xiaohaoSellInfo.play_from;
    }
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    selectPort(item) {
      this.current = item;
      let xiaohaoInfo = {
        play_from: this.current,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...xiaohaoInfo });
      this.$router.go(-1);
    },
    async getPortList() {
      try {
        let res = await ApiXiaohaoGetGameList();
        this.text = res.data.text;
        this.play_from = res.data.play_from;
      } catch (e) {}
    },
  },
};
</script>

<style lang="less" scoped>
.select-port-page {
  .main {
    box-sizing: border-box;
    padding: 0 18 * @rem;
    .port-list {
      .port-item {
        height: 55 * @rem;
        display: flex;
        align-items: center;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        .port-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          margin-right: 10 * @rem;
        }
        .port-name {
          font-size: 14 * @rem;
          color: #000000;
          flex: 1;
          min-width: 0;
        }
        .radio {
          width: 16 * @rem;
          height: 16 * @rem;
          .image-bg('~@/assets/images/gou-no.png');
          &.on {
            .image-bg('~@/assets/images/gou-yes.png');
          }
        }
      }
    }
    .text {
      box-sizing: border-box;
      padding: 12 * @rem 10 * @rem;
      font-size: 13 * @rem;
      color: #9a9a9a;
      line-height: 19 * @rem;
      background-color: #f5f5f6;
      border-radius: 12 * @rem;
    }
  }
}
</style>
