import { request } from '../index';
import h5Page from '@/utils/h5Page';

export function ApiLogin(params = {}) {
  return request('/api/user/login', params);
}

// export function ApiLogin(params = {}) {
//   return request('/api/user/login', params, true, h5Page.api_url3)
// }

export function ApiUserInfo(params = {}) {
  return request('/api/user/info', params);
}

export function ApiUserInfoEx(params = {}) {
  return request('/api/user/infoEx', params);
}

export function ApiUserInfoExt(params = {}) {
  return request('/api/user/infoExt', params);
}

export function ApiAuthCode(params = {}) {
  //type: "1=通用认证 2=登录（登录接口已废弃），3=登录，4=重置密码，5=绑定，6=解绑，7=小号回收，8=小号交易 9=账号注销",
  return request('/api/sms/send', params);
}

// 用户 - 发送邮箱验证码
export function ApiMailSend(params = {}) {
  //type: 2=注册，4=重置密码，10=换绑邮箱，11=绑定邮箱 7=小号回收
  return request('/api/mail/send', params);
}

/**
 * 账号注销
 * code 验证码
 *  */
export function ApiCancelUser(params = {}) {
  return request('/api/user/cancelUser', params);
}

/**
 * 设置密码
 * phone 手机号
 * code 验证码
 * password 密码
 *  */
export function ApiResetPassword(params = {}) {
  return request('/api/user/resetPassword', params);
}

/**
 * 用户 - 重置密码-密保重置
 *  */
export function ApiRestPasswordBySecurity(params = {}) {
  return request('/api/user/restPasswordBySecurity', params);
}

/**
 * 用户 - 设置密保
 *  */
export function ApiUserSetSecurity(params = {}) {
  return request('/api/user/setSecurity', params);
}

export function ApiChangeInfo(params = {}) {
  return request('/api/user/changeInfo', params);
}

export function ApiChangeInfoEx(params = {}) {
  // 修改性别、生日等
  return request('/api/user/changeInfoEx', params);
}

export function ApiUserCertification(params = {}) {
  return request('/api/user/certification', params);
}

export function ApibindWx(params = {}) {
  return request('/api/user/wxBind', params);
}

export function ApibindPhone(params = {}) {
  return request('/api/user/bindPhone', params);
}

export function ApiUnBindPhone(params = {}) {
  return request('/api/user/unbindPhone', params);
}

export function ApiChangeAvatar(params = {}) {
  return request('/api/user/changeAvatar', params);
}

/**
 * 设置密码
 * phone 手机号
 * code 验证码
 * password 密码
 *  */

/**
 * 获取我的反馈列表
 *  */
export function ApiMyFeedback(params = {}) {
  return request('/web/feedback/myFeedback', params);
}

/**
 * 获取我的投诉列表
 *  */
export function ApiMyComplaint(params = {}) {
  return request('/web/feedback/myComplain', params);
}

/**
 * 签到
 */
export function ApiUserClockIn(params = {}) {
  return request('/api/user/clockIn', params);
}

/**
 * 点击签到
 */
export function ApiUserActiveSign(params = {}) {
  return request('/api/user/activeSign', params);
}

/**
 * 签到获取宝箱奖励
 * @param box_num 天数 3   7  14   28  领取最后两个宝箱，需要助力。SVIP可以不用
 */
export function ApiUserGetGoldBox(params = {}) {
  return request('/api/user/getGoldBox', params);
}

/**
 * 补签所需金币数
 */
export function ApiUserRepairDeductGold(params = {}) {
  return request('/api/user/repairDeductGold', params);
}

/**
 * 补签
 * @param signDate 2021-08-11 补签时间
 */
export function ApiUserRepairSign(params = {}) {
  return request('/api/user/repairSign', params);
}

/**
 * 金币转盘列表
 */
export function ApiGoldDialList(params = {}) {
  return request('/api/gold_dial/goldDialList', params);
}

/**
 * 我的中奖列表
 */
export function ApiGoldDialMyWonList(params = {}) {
  return request('/api/gold_dial/myWonList', params);
}

/**
 * 抽奖
 * @param type 1 1连抽  2 10连抽  3 免费
 */
export function ApiGoldDialRaffle(params = {}) {
  return request('/api/gold_dial/raffle', params);
}

/**
 * 获取个人签
 * @param game_id
 * @param uuid
 * @param udid
 * req
 * status 1成功、2不是SVIP、3未绑定设备、4已经在队列中、5已经签名成功、6svip天数小于七天
 */
export function ApiPersonalSign(params = {}) {
  return request('/index/api/addPersonalSign', params, false, h5Page.apigrq);
}

/**
 * 检查用户是否绑定成功
 */
export function ApiCheckUdid(params = {}) {
  return request('/index/api/realBindUdid', params, false, h5Page.apigrq);
}

/**
 * 回归相关数据
 */
export function ApiUserRegressionList(params = {}) {
  return request('/api/user/isRegressionV1', params);
}

/**
 * 领取回归奖励
 */
export function ApiUserTakeRegression(params = {}) {
  return request('/api/user/takeRegressionV1', params);
}

//检测个人签是否实名认证，并返回相应链接（用于第三方个人签）
/**
 * @param {number} game_id 游戏ID
 * @param {number} grq_status 个人签状态
 */
export function ApiUserCheckGrqAuthStatus(params = {}) {
  return request('/index/api/checkGrqAuthStatus', params, false, h5Page.apigrq);
}

// 获取用户经验等级
export function ApiUserExpRank(params = {}) {
  return request('/api/user/expRank', params);
}

// 获取用户财富等级
export function ApiUserPayRank(params = {}) {
  return request('/api/user/payRank', params);
}

// 财富等级-新
export function ApiUserPayRankV1(params = {}) {
  return request('/api/user/payRankV1', params);
}

// 财富等级-每个等级的福利界面
/**
 * @param {levelId} 36
 */
export function ApiUserLevelWelfare(params = {}) {
  return request('/api/user/levelWelfare', params);
}

// 财富等级-领取福利
/**
 * @param {levelId} 36 领取福利的等级id
 * @param {couponId} 36 领取福利的代金券id
 * @param {isSvip} 1 是否为svip领取
 */
export function ApiUserTakeLevel(params = {}) {
  return request('/api/user/takeLevel', params);
}

/**
 * 获取财富积分明细
 */
export function ApiUserGetWealthPointsLog(params = {}) {
  return request('/api/user/getWealthPointsLog', params);
}

// 邀请好友数据
export function ApiUserInviteCount(params = {}) {
  return request('/api/user/inviteCount', params);
}

// 新邀请 - 新版邀请页面 2023年11月29日09:50:50
export function ApiUserInviteNew(params = {}) {
  return request('/api/user/inviterNew', params);
}

// 微信提醒
export function ApiNewsWeixin(params = {}) {
  return request('/api/news/weixin', params);
}

export function ApiUserInviterExchangeWx(params = {}) {
  return request('/api/user/inviterExchangeWx ', params);
}

// 微信绑定
export function ApiNewsWxBind(params = {}) {
  return request('/api/news/wxBind', params);
}

// 微信解绑
export function ApiUserWxUnbind(params = {}) {
  return request('/api/user/wxUnbind', params);
}

/**
 * @param {string} packageName 包名
 * */
export function ApiNewUser(params = {}) {
  return request('/api/user/newUser', params);
}

/**
 * 通知
 * */
export function ApiUserInform(params = {}) {
  return request('/api/user/inform', params);
}

/**
 * 添加通知阅读记录(消除小红点)
 * @param pushRecordId inform中id
 * */
export function ApiUserPushMsgRead(params = {}) {
  return request('/api/user/pushMsgRead', params);
}

/**
 * 我的开服提醒列表
 * */
export function ApiServerMyList(params = {}) {
  return request('/api/server/myList', params);
}

/**
 * 我的 - 综合
 * @return {action}
 * #  ACTION_REQUITE           = 1;//六倍返还
#  ACTION_RECYCLE           = 2;//小号回收
#  ACTION_REBATE            = 3;//返利申请
#  ACTION_CARD              = 4;//我的礼包
#  ACTION_TURN_TABLE        = 5;//金币转盘
#  ACTION_SPREADER          = 6; //邀请赚佣金
#  ACTION_GOLD_SHOP         = 7;//金币商城
#  ACTION_ZHUANYOU          = 8;//转游中心
#  ACTION_MY_GAME           = 9;//我的游戏
#  ACTION_XIAOHAO           = 10;//小号管理
#  ACTION_KEFU              = 11;//联系客服
#  ACTION_MY_COLLECTION     = 12;//我的收藏
#  ACTION_USE_GUIDE         = 13;//使用指南
#  ACTION_QUESTIONS_ANSWERS = 14;//我的问答
#  ACTION_MY_SUBMIT         = 15;//我的发布
#  ACTION_FEEDBACK          = 16;//投诉反馈
#  ACTION_WX                = 17;//微信提醒
#   ACTION_TRADE             = 18;//交易中心
 * */
export function ApiUserMyModular(params = {}) {
  return request('/api/user/myModular', params);
}

export function ApiUserMyModularV2(params = {}) {
  return request('/api/user/myModularV2', params);
}

export function ApiUserMyModularV3(params = {}) {
  return request('/api/user/myModularV3', params);
}

// 接下来4个接口是上报接口 ios马甲包专用 uuid从壳获取
/**
 * 上报安装信息-iOS使用 只有首次安装后登录才进行上报
 * */
export function ApiUserSubmitInstallInfo(params = {}) {
  return request('/api/user/submitInstallInfo', params);
}
/**
 * 上报登录信息-iOS使用
 * */
export function ApiUserSubmitLoginInfo(params = {}) {
  return request('/api/user/submitLoginInfo', params);
}
/**
 * 上报注册信息-iOS使用
 * */
export function ApiUserSubmitRegisterInfo(params = {}) {
  return request('/api/user/submitRegisterInfo', params);
}
/**
 * 上传玩家绑定信息，后续上报投放平台使用
 * @param uuid iOS使用 使用iOS壳的uuid
 * @param plat 平台类型： ios,android,h5
 * */
export function ApiUserSubmitUserInfo(params = {}) {
  return request('/api/user/submitUserInfo', params);
}

export function ApiUserImgCertification(params = {}) {
  return request('/api/user/imgCertification', params);
}

/**
 * 验证生日
 * @param is_close 是否关闭 0 不关闭 1关闭
 * @return content 内容
 * @return is_birth 1展示 -1不展示
 * */
export function ApiUserCheckBirthday(params = {}) {
  return request('/api/user/checkBirthday', params);
}

/**
 * up资源功能 - 关注用户
 * @param memId up主id
 * @param type 1关注0取消
 * */
export function ApiUserFollowUser(params = {}) {
  return request('/api/user/focusUser', params);
}

/**
 * up资源功能 - 获取主页
 * @param memId up主id
 * */
export function ApiUserIndexInfo(params = {}) {
  return request('/api/user/indexInfo', params);
}

/**
 * up资源功能 - 获取指定用户发布的动态
 * @param memId up主id
 * @param page
 * */
export function ApiUserGetUpComment(params = {}) {
  return request('/api/user/getUpComment', params);
}

/**
 * up资源功能 - 获取指定用户发布的游戏
 * @param memId up主id
 * @param page
 * */
export function ApiUserGetUpGameList(params = {}) {
  return request('/api/user/getUpGameList', params);
}
/*
 * up资源功能 - 获取关注的up
 * */
export function ApiUserGetFocusUp(params = {}) {
  return request('/api/user/getFocusUp', params);
}

/**
 * up资源功能 - 获取关注的up上传的游戏
 * */
export function ApiUserGetFocusUpGame(params = {}) {
  return request('/api/user/getFocusUpGame', params);
}

/**
 * up资源功能 - 修改是否展示主页
 * @param status 必填，状态1隐藏 0显示
 * */
export function ApiUserChangeOpenIndex(params = {}) {
  return request('/api/user/changeOpenIndex', params);
}

/**
 * up资源功能 - 提交资源
 * @param file_url 必填，文件地址
 * @param file_name 必填，apk名字或文件名
 * @param content 必填，必填，内容
 * @param apk_icon 选填，图标
 * @param imgs 选填，截图
 * */
export function ApiUserUpGame(params = {}) {
  return request('/api/user/upGame', params);
}

/**
 * up资源功能 - 获取自己得到提交资源
 * */
export function ApiGetSelfUpGame(params = {}) {
  return request('/api/user/getSelfUpGame', params);
}

/**
 * 礼金 - 我的礼金
 * */
export function ApiUserWodelijin(params = {}) {
  return request('/api/user/wodelijin', params);
}

/**
 * 礼金 - 我的礼金日志
 * */
export function ApiUserGetLiJinLog(params = {}) {
  return request('/api/user/getLiJinLog', params);
}

/**
 * 礼金 - 礼金兑换金币
 * */
export function ApiUserExpiringLiJin(params = {}) {
  return request('/api/user/expiringLiJin', params);
}

/**
 * 礼金 - 礼金兑换金币
 * */
export function ApiUserWelfareIndex(params = {}) {
  return request('/api/user/welfareIndex', params);
}

/**
 * 获取vip微信号
 * */
export function ApiUserGetVipWx(params = {}) {
  return request('/api/user/getVipWx', params);
}

/**
 * 名人堂
 * */
export function ApiUserHallOfFame(params = {}) {
  return request('/api/user/hallOfFame', params);
}

/**
 * 用户 - 获取用户名
 * */
export function ApiUserGetUsername(params = {}) {
  return request('/api/user/getUsername', params);
}

/**
 * 用户 - 用户注册
 * */
export function ApiUserRegister(params = {}) {
  return request('/api/user/register', params);
}

/**
 * 用户 - 快速注册
 * */
export function ApiUserRegisterNew(params = {}) {
  return request('/api/user/registerNew', params);
}

/**
 * 用户 - 邮箱注册的一些信息
 * */
export function ApiUserRegByEmailInfo(params = {}) {
  return request('/api/user/regByEmailInfo', params);
}

// 添加小助手
export function ApiAddAssistant(params = {}) {
  return request('/api/user/addAssistant', params);
}
/**
 * 获取用户每日任务今日还能获取多少金币
 * */
export function ApiUserGetUserDayGoldTotal(params = {}) {
  return request('/api/user/getUserDayGoldTotal', params);
}

/**
 * 用户 - 换绑邮箱
 * */
export function ApiUserReplaceEmail(params = {}) {
  return request('/api/user/replaceEmail', params);
}

/**
 * 用户 - 绑定邮箱
 * */
export function ApiUserBindEmail(params = {}) {
  return request('/api/user/bindEmail', params);
}

// 检测账号 海外用
export function ApiUserCheckAccount(params = {}) {
  return request('/api/user/checkAccount', params);
}

/**
 * 用户 - 尊享客服
 */
export function ApiUserGetSingleWealth(params = {}) {
  return request('/api/user/getSingleWealth', params);
}

/**
 * @function 用户 - 内测先锋计划
 * @param {string} packageName 包名
 * @param {number} versionCode 版本号
 * @param {string} channel 渠道
 * @param {boolean} isBeta 是否内测
 */
export function ApiUpdateGetTestPlan(params = {}) {
  return request('/api/update/getTestPlan', params);
}
/**
 * @function 金币中心
 * @param {number} page 默认1 / 2
 */
export function ApiCoinCenterIndex(params = {}) {
  return request('/v2024/coin_center/index', params);
}
/**
 * @function 金币折扣游戏
 * @param {string} page 选填，分页
 * @param {string} listRows 选填，page
 */
export function ApiCoinCenterGetGoldBoxList(params = {}) {
  return request('/v2024/Coin_Center/getGoldBoxList', params);
}

/**
 * @function 评论/提交反馈 - 提交反馈
 * @param {int} question_type 1 产品建议 2 功能故障 3其他
 * @param {string} question 问题描述
 * @param {string} imgs 图片 json格式的图片数组
 * @param {string} contact 联系方式 手机号|QQ号|邮箱
 */
export function ApiFeedbackSubmit(params = {}) {
  return request('/v2024/Feedback/submit', params);
}
