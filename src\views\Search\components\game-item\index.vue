<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item">
      <div class="game-info">
        <div class="game-item-components">
          <div class="game-icon">
            <img :src="couponItem.titlepic" alt="" />
          </div>
          <div class="game-info-item">
            <div class="game-name">
              {{ couponItem.main_title }}
              <span class="game-discount" v-if="couponItem.subtitle">{{
                couponItem.subtitle
              }}</span>
            </div>
            <div class="game-score-item">
              <div class="game-score">
                <div class="score">
                  {{ couponItem.rating ? couponItem.rating.rating : '10' }}
                </div>
              </div>
              <div class="game-type">
                <template v-for="(type, typeIndex) in couponItem.type">
                  <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                    type
                  }}</span>
                </template>
              </div>
            </div>
            <div class="game-subtitle" v-if="couponItem.extra_tag.length > 0">
              <!-- <template v-for="(item, index) in couponItem.extra_tag">
                <div
                  class="game-subtitle-item"
                  :style="{
                    color: item.color,
                    border: `1px solid ${item.color}`,
                  }"
                  :key="index"
                >
                  {{ item.name }}
                </div>
              </template> -->
              <div class="tags">
                <span
                  v-for="(tag, tagIndex) in couponItem.extra_tag"
                  :key="tagIndex"
                  >{{ tag.name }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameQuanItem',
  props: ['couponItem'],
  data() {
    return {
      item: {},
    };
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  .game-quan-item {
    // width: 347 * @rem;
    height: 64 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // background-color: #f5f5f5;
    border-radius: 12 * @rem;
    .game-info {
      //   padding-left: 12 * @rem;
      flex: 1;
      min-width: 0;

      .game-item-components {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: flex-start;
        flex: 1;
        min-width: 0;
        .game-icon {
          position: relative;
          flex: 0 0 64 * @rem;
          width: 64 * @rem;
          height: 64 * @rem;
          border-radius: 14 * @rem;
          background-color: #eeeeee;
        }
        .game-info-item {
          margin-left: 6 * @rem;
          // margin-right: 17 * @rem;
          flex: 1;
          min-width: 0;
          // height: 65 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .game-name {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #111111;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .game-title {
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
            .game-discount {
              margin-left: 8 * @rem;
              box-sizing: border-box;
              padding: 2 * @rem 4 * @rem;
              height: 17 * @rem;
              background: #f5f5f6;
              border-radius: 4 * @rem;
              font-weight: 400;
              font-size: 10 * @rem;
              color: #808080;
              line-height: 13 * @rem;
              text-align: left;
              font-style: normal;
              text-transform: none;
              display: flex;
              align-items: center;
              justify-content: center;
              white-space: nowrap;
            }
          }
          .game-score-item {
            width: 186 * @rem;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            margin: 7 * @rem 0;
            color: #999999;
            line-height: 15 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            .game-score {
              display: flex;
              align-items: center;
              .score {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 15 * @rem;
                line-height: 15 * @rem;
                font-weight: 600;
                font-size: 12 * @rem;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;

                &::before {
                  content: '';
                  display: block;
                  background: url(~@/assets/images/search/search_score.png)
                    no-repeat;
                  background-size: 10 * @rem 10 * @rem;
                  width: 10 * @rem;
                  height: 10 * @rem;
                  margin-right: 4 * @rem;
                }
              }
            }
            .game-type {
              display: flex;
              align-items: center;
              margin-left: 3 * @rem;
              height: 17 * @rem;
              flex-wrap: wrap;
              overflow: hidden;
              .type {
                padding: 0 5 * @rem;
                position: relative;
                display: flex;
                align-items: center;
                &:not(:first-child) {
                  &:before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1 * @rem;
                    height: 10 * @rem;
                    background-color: #929292;
                  }
                }
              }
            }
          }
          .game-subtitle {
            font-weight: 400;
            font-size: 11 * @rem;
            color: #666666;
            line-height: 14 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            max-width: 100%;
            display: flex;
            align-items: center;
            .game-subtitle-item {
              box-sizing: border-box;
              padding: 2 * @rem 4 * @rem;
              height: 17 * @rem;
              line-height: 17 * @rem;
              display: flex;
              align-items: center;
              background: rgba(255, 102, 73, 0.02);
              border-radius: 4 * @rem;
              border: 1 * @rem solid @themeColor;
              font-weight: 400;
              font-size: 10 * @rem;
              color: @themeColor;
              text-align: center;
              font-style: normal;
              text-transform: none;
              &:not(:first-of-type) {
                margin-left: 8 * @rem;
              }
            }
            .game-subtitle-tow {
              box-sizing: border-box;
              display: flex;
              align-items: center;
              padding: 2 * @rem 4 * @rem;
              height: 17 * @rem;
              background: #ecfbf4;
              border-radius: 4 * @rem;
              border: 1 * @rem solid #61d47d;
              color: #22a03c;
              margin-left: 8 * @rem;
              font-weight: 400;
              font-size: 10 * @rem;

              line-height: 17 * @rem;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .tags {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              margin-top: 2 * @rem;
              height: 19 * @rem;
              overflow: hidden;
              span {
                flex-shrink: 0;
                height: 17 * @rem;
                line-height: 17 * @rem;
                background: #f5f5f6;
                border-radius: 4 * @rem;
                border: 1 * @rem solid rgba(0, 0, 0, 0.05);
                padding: 0 4 * @rem;
                font-size: 10 * @rem;
                font-weight: 400;
                color: #808080;
                margin-right: 8 * @rem;

                &:first-of-type {
                  background-color: fade(@themeColor, 5);
                  border-color: @themeColor;
                  color: @themeColor;
                }
                &:not(:first-of-type) {
                  background-color: #ecfbf4;
                  border: 1 * @rem solid #61d47d;
                  color: #22a03c;
                }
              }
            }
          }
        }
      }
    }
    .game-quan-get {
      width: 58 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
      .get {
        font-weight: 500;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 58 * @rem;
        height: 28 * @rem;
        background: #5abf73;
        border-radius: 29 * @rem;
        margin-top: 4 * @rem;
      }
    }
  }
}
</style>
