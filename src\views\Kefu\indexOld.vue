<template>
  <div class="kefu-page">
    <nav-bar-2 :border="true" :title="$t('客服中心')"></nav-bar-2>
    <img src="@/assets/images/kefu/kefu_bg1.png" class="bg1" />
    <section class="explain">
      <div class="big-text">{{ $t('如遇紧急问题请联系在线客服：') }}</div>
      <div class="small-text">
        {{ $t('（客服不会以任何方式要求玩家转账，请警惕诈骗）') }}
      </div>
      <div class="small-text">
        （{{ $t('工作日') }}：8:30-23:00 {{ $t('节假日') }}：9:00-22:00）
      </div>
    </section>
    <section class="connect">
      <div class="part qq">
        <div class="big-text">{{ $t('官方客服QQ号') }}</div>
        <div class="small-text">{{ kefuQQNumber }}</div>
        <div @click="connectQQ()" class="button">{{ $t('撩她') }}</div>
      </div>
      <!-- <div class="part wechat">
        <div class="big-text">微信客服</div>
        <div class="small-text">{{ gongzhonghao }}</div>
        <div @click="attentionWX()" class="button">撩她</div>
      </div> -->
      <div class="part online">
        <div class="big-text">{{ $t('在线客服') }}</div>
        <div class="small-text">{{ gongzhonghao }}</div>
        <div class="button" v-if="noNeedWykefu">{{ $t('暂无') }}</div>
        <div @click="connectQiYu()" class="button" v-else>
          {{ $t('撩她') }}
        </div>
      </div>
    </section>
    <section class="other">
      <div class="list">
        <div
          v-for="(item, index) in questionList"
          :key="index"
          @click="toDetail(item.id)"
          class="item"
        >
          <div :class="item.icon" class="icon"></div>
          <div class="text">{{ item.title }}</div>
        </div>
        <div @click="toUploadPage()" class="item" v-if="platform != 'h5'">
          <div class="icon icon6"></div>
          <div class="text">{{ $t('上传资源') }}</div>
        </div>
      </div>
    </section>
    <div @click="connectQiYu()" class="bottom-button" v-if="!noNeedWykefu">
      {{ $t('联系在线客服') }}
    </div>
    <div @click="feedback()" v-if="feedbackShow" class="bottom-button feedback">
      {{ $t('投诉与建议') }}
    </div>
  </div>
</template>

<script>
import baseParams from '@/utils/baseParams.js';
import { questionList } from '@/utils/constants.js';
import {
  platform,
  BOX_openInNewWindow,
  BOX_openWx,
  BOX_openInNewNavWindow,
  BOX_openInBrowser,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'Kefu',
  data() {
    return {
      platform,
      feedbackShow: false, // 意见反馈是否显示
      gongzhonghao: this.$t('3733游戏'),
      questionList,
    };
  },
  computed: {
    ...mapGetters({
      kefuQQNumber: 'system/kefuQQNumber',
      kefuQQLink: 'system/kefuQQLink',
    }),
    noNeedWykefu() {
      return !!this.kefuQQNumber && !this.kefuQQLink;
    },
  },
  created() {
    // 反馈按钮显示条件
    if (baseParams.versionCode > 1122 || baseParams.build > 54) {
      this.feedbackShow = true;
    }
    if (this.platform == 'android') {
      document.title = this.$t('客服中心');
    }
  },
  methods: {
    // 跳转问题详情
    toDetail(id) {
      BOX_openInNewNavWindow(
        { name: 'KefuQA', params: { id: id } },
        { url: `https://${this.$h5Page.env}game.3733.com/#/kefu_qa/${id}` },
      );
    },
    // 关注微信公众号 改为->微信客服
    attentionWX() {
      // 打开微信客服
      BOX_openInBrowser(
        { h5_url: 'https://work.weixin.qq.com/kfid/kfcfd46436459c55333' },
        { url: 'https://work.weixin.qq.com/kfid/kfcfd46436459c55333' },
      );
      // 跳转到微信
      // this.$copyText(this.gongzhonghao)
      //   .then(() => {
      //     this.$toast("复制成功");
      //   })
      //   .catch(() => {
      //     this.$toast("复制失败，请手动复制");
      //   });
      // BOX_openWx();
    },
    connectQQ() {
      if (this.kefuQQLink) {
        // 有qq客服链接的情况下直接打开链接
        this.$toast('正在唤起QQ，请稍等...');
        BOX_openInNewNavWindow(
          { h5_url: this.kefuQQLink },
          { url: this.kefuQQLink },
        );
      } else {
        // ios马甲包打开不了生成的链接，复制QQ号;
        if (platform == 'iosBox') {
          this.$copyText(this.kefuQQNumber)
            .then(() => {
              this.$toast('QQ号已复制，请打开QQ进行添加');
              setTimeout(() => {
                window.location.href = 'mqq://'; // ios马甲包打不开
              }, 1000);
            })
            .catch(() => {
              this.$toast('复制失败，请手动复制');
            });
          return false;
        }

        // 没有QQ链接的情况不复制打开QQ了，直接拿QQ号转成能打开的链接
        let url = `mqqwpa://im/chat?chat_type=wpa&uin=${this.kefuQQNumber}&version=1&src_type=web`;
        BOX_openInNewNavWindow({ h5_url: url }, { url: url });
      }
    },
    connectQiYu() {
      this.openKefu();
    },
    // 跳转反馈页面
    feedback() {
      let url = `https://${this.$h5Page.env}game.3733.com/#/feedback`;
      BOX_openInNewWindow({ name: 'Feedback' }, { url });
    },
    // 跳转上传资源页面（客户端）
    toUploadPage() {
      BOX_openInNewNavWindow(
        {},
        { url: `https://${this.$h5Page.env}api.a3733.com/h5/apk/index` },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.kefu-page {
  box-sizing: border-box;
  background: #ffffff;
  padding-bottom: 30 * @rem;
  .bg1 {
    width: 100%;
    margin-top: -6 * @rem;
  }
  .explain {
    .big-text {
      margin: 18 * @rem auto 5 * @rem;
      text-align: center;
      font-size: 15 * @rem;
      font-family:
        PingFangSC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 21 * @rem;
      margin-bottom: 5 * @rem;
    }
    .small-text {
      text-align: center;
      font-size: 12 * @rem;
      color: #9a9a9a;
      line-height: 18 * @rem;
    }
  }
  .connect {
    display: flex;
    justify-content: space-between;
    margin: 15 * @rem 32 * @rem 0;
    .part {
      width: 146 * @rem;
      height: 133 * @rem;
      .big-text {
        margin-top: 40 * @rem;
        font-size: 15 * @rem;
        color: #c2f5da;
        line-height: 21 * @rem;
        text-align: center;
      }
      .small-text {
        margin-top: 5 * @rem;
        font-size: 14 * @rem;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-weight: 600;
        color: #ffffff;
        line-height: 20 * @rem;
        text-align: center;
      }
      .button {
        width: 86 * @rem;
        height: 52 * @rem;
        margin: 0 auto;
        font-size: 14 * @rem;
        font-family: PingFangHK-Medium, PingFangHK;
        font-weight: 500;
        line-height: 44 * @rem;
        text-align: center;
      }
      &.qq {
        .image-bg('~@/assets/images/kefu/kefu_bg3.png');
        .button {
          color: #5e95ca;
          .image-bg('~@/assets/images/kefu/kefu_button1.png');
        }
        .big-text {
          color: #bee2ff;
        }
      }
      &.wechat {
        .image-bg('~@/assets/images/kefu/kefu_bg2.png');
        .button {
          color: #4cac79;
          .image-bg('~@/assets/images/kefu/kefu_button2.png');
        }
      }
      &.online {
        .image-bg('~@/assets/images/kefu/kefu_bg4.png');
        .button {
          color: #4cac79;
          .image-bg('~@/assets/images/kefu/kefu_button2.png');
        }
      }
    }
  }
  .other {
    padding: 0 18 * @rem 15 * @rem;
    .title {
      font-size: 15 * @rem;
      font-family:
        PingFangSC-Medium,
        PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 21 * @rem;
    }
    .list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      &::after {
        content: '';
        flex: 0 0 33.33%;
      }
      .item {
        flex: 0 0 33.33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 25 * @rem;
        .icon {
          display: block;
          width: 40 * @rem;
          height: 40 * @rem;
          &.icon1 {
            .image-bg('~@/assets/images/kefu/kefu_icon1.png');
          }
          &.icon2 {
            .image-bg('~@/assets/images/kefu/kefu_icon2.png');
          }
          &.icon3 {
            .image-bg('~@/assets/images/kefu/kefu_icon3.png');
          }
          &.icon4 {
            .image-bg('~@/assets/images/kefu/kefu_icon4.png');
          }
          &.icon5 {
            .image-bg('~@/assets/images/kefu/kefu_icon5.png');
          }
          &.icon6 {
            .image-bg('~@/assets/images/kefu/kefu_icon6.png');
          }
        }
        .text {
          margin-top: 8 * @rem;
          font-size: 14 * @rem;
          color: #000000;
          line-height: 20 * @rem;
        }
      }
    }
  }
  .bottom-button {
    box-sizing: border-box;
    width: 311 * @rem;
    height: 48 * @rem;
    line-height: 48 * @rem;
    text-align: center;
    color: #fff;
    background: @themeBg;
    font-size: 16 * @rem;
    font-family: PingFangHK-Medium, PingFangHK;
    font-weight: 400;
    border-radius: 50 * @rem;
    margin: 15 * @rem auto 0;
    &.feedback {
      border: 1 * @rem solid @themeColor;
      color: @themeColor;
      background-color: #fff;
    }
  }
}
</style>
