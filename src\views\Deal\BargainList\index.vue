<template>
  <div class="bargain-list-page">
    <nav-bar-2 :title="$t('砍价')" :border="true"></nav-bar-2>
    <div class="main">
      <yy-list
        class="bargain-container"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
      >
        <div class="bargain-list">
          <div
            class="bargain-item"
            v-for="(item, index) in bargainList"
            :key="index"
          >
            <bargain-item @refresh="onRefresh" :info="item"></bargain-item>
          </div>
        </div>
      </yy-list>
      <div
        class="bottom-container"
        v-if="bargainList.length && !bargainList[0].is_show"
      >
        <div class="bottom-fixed">
          <div class="submit">
            <div class="submit-btn" @click="clickBargain">
              {{ $t('砍价') }}
            </div>
          </div>
          <bottom-safe-area></bottom-safe-area>
        </div>
      </div>
    </div>
    <bargain-popup
      :tradeId="tradeId"
      :isShow.sync="bargainPopupShow"
      @success="getBargainList"
    ></bargain-popup>
  </div>
</template>

<script>
import bargainItem from '../components/bargain-item/index.vue';
import {
  ApiXiaohaoBargainList,
  ApiXiaohaoCheckBargain,
} from '@/api/views/xiaohao.js';
import bargainPopup from '../components/bargain-popup/index.vue';
export default {
  components: {
    bargainItem,
    bargainPopup,
  },
  data() {
    return {
      tradeId: this.$route.params.id,
      page: 1,
      listRows: 10,
      bargainList: [],
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      empty: false,
      bargainPopupShow: false,
    };
  },
  async created() {
    await this.getBargainList();
  },
  methods: {
    // 点击砍价按钮
    async clickBargain() {
      const res = await ApiXiaohaoCheckBargain({
        tradeId: this.tradeId,
      });
      if (res.code > 0) {
        this.bargainPopupShow = true;
      }
    },
    async getBargainList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoBargainList({
        tradeId: this.tradeId,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.bargainList = [];
      }
      this.bargainList.push(...list);
      if (!this.bargainList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getBargainList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getBargainList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.bargain-list-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .main {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    .bargain-list {
      padding: 0 18 * @rem;
      .bargain-item {
        &:not(:last-of-type) {
          border-bottom: 1 * @rem solid #f3f3f8;
        }
      }
    }
    .bottom-container {
      height: calc(58 * @rem + @safeAreaBottom);
      height: calc(58 * @rem + @safeAreaBottomEnv);
      .bottom-fixed {
        background-color: #fff;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        z-index: 2000;
        border-top: 0.5 * @rem solid #ebebeb;
        .submit {
          height: 58 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .submit-btn {
            width: 339 * @rem;
            margin: 0 * @rem auto;
            height: 44 * @rem;
            background: @themeBg;
            border-radius: 22 * @rem;
            color: #fff;
            font-size: 16 * @rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
