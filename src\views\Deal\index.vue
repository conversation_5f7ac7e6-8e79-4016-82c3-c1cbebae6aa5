<template>
  <div class="page deal-page">
    <nav-bar-2 ref="navBar" :title="$t('账号交易')" :border="true"> </nav-bar-2>
    <div class="main">
      <div class="nav-list" :class="{ 'no-border': hasNotice }">
        <div class="nav-item btn" @click="toPage('XiaohaoSellWrite')">
          <div class="nav-icon">
            <img src="@/assets/images/deal/nav-maihao.png" alt="" />
          </div>
          <div class="nav-title">{{ $t('卖号') }}</div>
        </div>
        <div class="nav-item btn" @click="toPage('DealTrends')">
          <div class="nav-icon">
            <img src="@/assets/images/deal/nav-dongtai.png" alt="" />
          </div>
          <div class="nav-title">{{ $t('成交动态') }}</div>
        </div>
        <div class="nav-item btn" @click="toPage('MyDeal')">
          <div class="nav-icon">
            <img src="@/assets/images/deal/nav-jilu.png" alt="" />
          </div>
          <div class="nav-title">{{ $t('交易记录') }}</div>
        </div>
        <div class="nav-item btn" @click="toIntroduction">
          <div class="nav-icon">
            <img src="@/assets/images/deal/nav-xuzhi.png" alt="" />
          </div>
          <div class="nav-title">{{ $t('交易须知') }}</div>
        </div>
      </div>
      <div class="container">
        <van-sticky ref="container" :offset-top="navBarHeight">
          <van-notice-bar
            v-if="hasNotice"
            class="notice-bar"
            :left-icon="noticeIcon"
            text="自2022年7月7日12:00起，购买账号将不再返还金币。"
            background="#FEE7DB"
            color="#FE6600"
            mode="closeable"
            @close="hasNotice = false"
            @click="showNoticePopup = true"
          />
          <div class="deal-nav-bar">
            <van-dropdown-menu
              active-color="#FE6600"
              :lock-scroll="false"
              class="order-bar"
              :z-index="4000"
            >
              <van-dropdown-item
                v-model="order_value"
                :options="order_list"
                :lock-scroll="false"
                @change="onChange"
              />
            </van-dropdown-menu>
            <div
              class="search-bar btn"
              v-if="!gameInfo.id"
              @click="toSearchGame"
            >
              <div class="search-icon"></div>
              <div class="search-text">{{ $t('搜索游戏') }}</div>
            </div>
            <div class="search-bar search-game btn" v-else @click="clearGame">
              <div class="close-icon"></div>
              <div class="game-name">{{ gameInfo.title }}</div>
            </div>
            <div
              class="filter-bar btn"
              :class="{ on: filterShow }"
              @click="clickFilterBar"
            >
              <div class="filter-name">{{ $t('筛选') }}</div>
              <div class="filter-icon"></div>
            </div>
            <div class="switch-bar btn" @click="showWaterfall = !showWaterfall">
              <div
                class="switch-icon"
                :class="{ 'list-icon': !showWaterfall }"
              ></div>
            </div>
          </div>
        </van-sticky>
        <div class="deal-content">
          <content-empty
            v-if="empty"
            :tips="$t('暂无游戏发布')"
          ></content-empty>
          <yy-list
            v-else
            class="yy-list"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
          >
            <buy-list-normal
              :list="buyList"
              v-if="!showWaterfall"
            ></buy-list-normal>
            <buy-list-waterfall :list="buyList" v-else></buy-list-waterfall>
          </yy-list>
        </div>
      </div>
    </div>
    <van-popup
      class="filter-sheet"
      position="bottom"
      v-model="filterShow"
      :lock-scroll="false"
    >
      <div class="close" @click="filterShow = false"></div>
      <div class="filter-sheet-title">{{ $t('设备') }}</div>
      <div class="filter-list">
        <div
          class="filter-item"
          :class="{ on: platform.value == platform_value }"
          v-for="platform in platform_list"
          :key="platform.value"
          @click="platform_value = platform.value"
        >
          {{ platform.text }}
        </div>
      </div>
      <div class="filter-sheet-title mt">{{ $t('游戏分类') }}</div>
      <div class="filter-list">
        <div
          class="filter-item"
          :class="{ on: type.value == type_value }"
          v-for="type in type_list"
          :key="type.value"
          @click="type_value = type.value"
        >
          {{ type.text }}
        </div>
      </div>
      <div class="filter-confirm" @click="handleFilterConfirm">
        {{ $t('确定') }}
      </div>
    </van-popup>
    <van-dialog
      v-model="showNoticePopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="notice-popup"
    >
      <div class="title">
        由于业务调整，自2022年7月11日12:00起，购买账号将不再返还金币。
      </div>
      <div class="desc">感谢您对3733游戏平台的支持！</div>
      <div class="button-container">
        <div class="button-btn btn" @click="showNoticePopup = false">
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import noticeIcon from '@/assets/images/deal/notice-icon.png';
import { themeColorLess } from '@/common/styles/_variable.less';
import buyListNormal from './components/buy-list-normal';
import buyListWaterfall from './components/buy-list-waterfall';
import { mapMutations } from 'vuex';
import { isIos } from '@/utils/userAgent.js';
import {
  ApiXiaohaoTradeList,
  ApiGameCateForTrade,
} from '@/api/views/xiaohao.js';
import h5Page from '@/utils/h5Page';
export default {
  name: 'Deal',
  components: {
    buyListNormal,
    buyListWaterfall,
  },
  data() {
    return {
      noticeIcon,
      hasNotice: true,
      showNoticePopup: false,
      themeColorLess,
      navBarHeight: 0,
      showWaterfall: true,
      buyList: [],
      page: 1,
      listRows: 10,
      finished: false,
      empty: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      gameInfo: {},
      filterShow: false,
      order_list: [
        {
          text: this.$t('最新发布'),
          value: 1,
        },
        {
          text: this.$t('价格最低'),
          value: 2,
        },
        {
          text: this.$t('价格最高'),
          value: 3,
        },
      ],
      type_list: [
        {
          text: this.$t('全部'),
          value: 0,
        },
      ],
      platform_list: [
        {
          text: this.$t('双端'),
          value: 0,
        },
        {
          text: this.$t('安卓'),
          value: 11,
        },
        {
          text: 'IOS',
          value: 12,
        },
      ],
      order_value: 1,
      type_value: 0,
      platform_value: isIos ? 12 : 11,
    };
  },
  async created() {
    await this.getCate();
  },
  mounted() {
    this.$nextTick(() => {
      this.navBarHeight = this.$refs.navBar.clientHeight;
    });
  },
  activated() {
    let info = this.$route.params.info;
    if (info) {
      this.gameInfo = info;
      this.getBuyList();
    }
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == 'XiaohaoSellWrite' && from.name == 'Deal') {
      this.setXiaohaoSellInfo();
    }
    next();
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    openNoticePopup() {},
    clickFilterBar() {
      this.filterShow = true;
    },
    async handleFilterConfirm() {
      this.buyList = [];
      this.filterShow = false;
      await this.getBuyList();
    },
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: { url: h5Page.jiaoyixuzhi, title: this.$t('交易须知') },
      });
    },
    toSearchGame() {
      this.toPage('SearchGame', {
        from: 'Deal',
      });
    },
    clearGame() {
      this.gameInfo = {};
      this.getBuyList();
    },
    async onChange() {
      this.buyList = [];
      this.page = 1;
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getBuyList();
      this.loadingObj.loading = false;
      this.$toast.clear();
    },
    async getCate() {
      const res = await ApiGameCateForTrade();
      let arr = res.data.map(item => {
        return {
          text: item.title,
          value: item.id,
        };
      });
      this.type_list.push(...arr);
    },
    async getBuyList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      let params = {
        isDone: 0,
        page: this.page,
        listRows: this.listRows,
        order: this.order_value,
        deviceFrom: this.platform_value,
      };
      if (this.type_value) {
        params = { ...params, cateType: this.type_value };
      }
      if (this.gameInfo.id) {
        params = { ...params, gameId: this.gameInfo.id };
      }
      const res = await ApiXiaohaoTradeList(params);
      if (action === 1 || this.page === 1) {
        this.buyList = [];
      }
      this.buyList.push(...res.data.list);
      this.$nextTick(() => {
        this.loadingObj.loading = false;
      });
      if (this.buyList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getBuyList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.buyList.length) {
        await this.getBuyList();
      } else {
        await this.getBuyList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.deal-page {
  .question {
    width: 17 * @rem;
    height: 17 * @rem;
    background: url(~@/assets/images/deal/question-icon.png) center center
      no-repeat;
    background-size: 17 * @rem 17 * @rem;
  }
  .my-deal {
    font-size: 14 * @rem;
    color: #fff;
    margin-left: 5 * @rem;
  }
  .main {
    flex-shrink: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    .nav-list {
      display: flex;
      align-items: center;
      padding: 0 8 * @rem;
      border-bottom: 8 * @rem solid #f5f5f6;
      &.no-border {
        border-bottom: 0;
      }
      .nav-item {
        padding: 20 * @rem 0 15 * @rem;
        flex: 1;
        .nav-icon {
          width: 30 * @rem;
          height: 30 * @rem;
          margin: 0 auto;
        }
        .nav-title {
          font-size: 13 * @rem;
          color: #000000;
          text-align: center;
          margin-top: 7 * @rem;
        }
      }
    }
    .container {
      padding: 0 0 9 * @rem;
      flex-shrink: 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      background: #f7f8fa;
      .notice-bar {
        height: 28 * @rem;
        /deep/ .van-notice-bar__content {
          font-size: 13 * @rem;
        }
      }
      .deal-nav-bar {
        box-sizing: border-box;
        height: 50 * @rem;
        display: flex;
        align-items: center;
        font-size: 13 * @rem;
        color: #000000;
        font-weight: 400;
        padding: 0 18 * @rem;
        max-width: @maxWidth;
        margin: 0 auto;
        background-color: #fff;
        .order-bar {
          display: flex;
          align-items: center;
          /deep/ .van-dropdown-menu__bar {
            box-shadow: unset;
            .van-dropdown-menu__title {
              font-size: 13 * @rem;
              color: #000000;
              font-weight: 400;
              padding: 0;
              padding-right: 6 * @rem;
            }
          }
          .order-name {
            font-size: 13 * @rem;
            color: #000000;
            font-weight: 400;
          }
          .order-icon {
            width: 8 * @rem;
            height: 5 * @rem;
            .image-bg('~@/assets/images/deal/bottom-arrow-black.png');
            margin-left: 6 * @rem;
          }
        }
        .search-bar {
          box-sizing: border-box;
          flex: 1;
          min-width: 0;
          display: flex;
          align-items: center;
          height: 30 * @rem;
          padding: 0 10 * @rem;
          background: #f5f5f6;
          border-radius: 15 * @rem;
          margin-left: 16 * @rem;
          margin-right: 10 * @rem;
          .search-icon {
            width: 13 * @rem;
            height: 13 * @rem;
            .image-bg('~@/assets/images/deal/search-icon.png');
          }
          .search-text {
            margin-left: 4 * @rem;
            font-size: 13 * @rem;
            color: #c1c1c1;
            font-weight: 400;
            flex: 1;
            min-width: 0;
          }
          .close-icon {
            width: 13 * @rem;
            height: 13 * @rem;
            .image-bg('~@/assets/images/close-black-oo.png');
          }
          .game-name {
            margin-left: 4 * @rem;
            font-size: 13 * @rem;
            color: @themeColor;
            font-weight: 400;
            flex: 1;
            min-width: 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .filter-bar {
          display: flex;
          align-items: center;
          height: 30 * @rem;
          .filter-name {
            font-size: 13 * @rem;
            color: #000000;
          }
          .filter-icon {
            width: 14 * @rem;
            height: 14 * @rem;
            .image-bg('~@/assets/images/deal/filter-icon.png');
            margin-left: 6 * @rem;
          }
          &.on {
            .filter-name {
              color: @themeColor;
            }
            .filter-icon {
              .image-bg('~@/assets/images/deal/filter-icon-on.png');
            }
          }
        }
        .switch-bar {
          margin-left: 9 * @rem;
          padding: 5 * @rem;
          .switch-icon {
            width: 14 * @rem;
            height: 14 * @rem;
            .image-bg('~@/assets/images/deal/waterfall-css.png');
            &.list-icon {
              .image-bg('~@/assets/images/deal/list-css.png');
            }
          }
        }
      }
      .deal-content {
        padding: 10 * @rem 0 0;
        flex-shrink: 0;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        .yy-list {
          /deep/.buy-list-waterfall {
            background: #f7f8fa;

            .buy-item {
              border: 0;
            }
          }
          /deep/.buy-list-normal {
            padding: 0 12 * @rem;
          }
        }
        /deep/ .deal-item-component {
          border-bottom: 0;
          background: #fff;
          border-radius: 8 * @rem;
          .game-name {
            max-width: 60 * @rem !important;
          }
          &:not(:first-of-type) {
            margin-top: 12 * @rem;
          }
        }
      }
    }
  }
  .filter-sheet {
    box-sizing: border-box;
    padding: 20 * @rem 15 * @rem 40 * @rem;
    border-radius: 20 * @rem 20 * @rem 0 0;
    .close {
      position: absolute;
      right: 0;
      top: 0;
      width: 45 * @rem;
      height: 45 * @rem;
      background: url(~@/assets/images/close-dialog.png) center center no-repeat;
      background-size: 15 * @rem 15 * @rem;
    }
    .filter-sheet-title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      &.mt {
        margin-top: 25 * @rem;
      }
    }
    .filter-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 16 * @rem;
      .filter-item {
        width: 60 * @rem;
        height: 26 * @rem;
        border: 1 * @rem solid #c1c1c1;
        font-size: 13 * @rem;
        color: #797979;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8 * @rem;
        border-radius: 3 * @rem;
        &:nth-of-type(5n + 1) {
          margin-left: 0;
        }
        &:not(:nth-of-type(-n + 5)) {
          margin-top: 12 * @rem;
        }
        &.on {
          border: 1 * @rem solid @themeColor;
          color: @themeColor;
        }
      }
    }
    .filter-confirm {
      width: 289 * @rem;
      height: 44 * @rem;
      border-radius: 22 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: 500;
      margin: 28 * @rem auto 0;
      background-color: @themeColor;
    }
  }
}
.notice-popup {
  box-sizing: border-box;
  width: 335 * @rem;
  padding: 20 * @rem 16 * @rem;
  .title {
    font-size: 16 * @rem;
    color: #000000;
    text-align: center;
    font-weight: bold;
    margin-bottom: 10 * @rem;
  }
  .desc {
    font-size: 14 * @rem;
    color: #000000;
    text-align: center;
    margin-bottom: 10 * @rem;
  }
  .intro-content {
    font-size: 13 * @rem;
    color: #000000;
    line-height: 22 * @rem;
    margin-top: 10 * @rem;
    .intro-p {
      span {
        color: @themeColor;
      }
    }
  }
  .button-container {
    display: flex;
    justify-content: center;
  }
  .button-btn {
    flex: 1;
    box-sizing: border-box;
    max-width: 204 * @rem;
    height: 40 * @rem;
    background: @themeBg;
    border-radius: 5 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    color: #ffffff;
    margin-top: 12 * @rem;
    font-weight: 600;
    border-radius: 20px;
  }
}
</style>
