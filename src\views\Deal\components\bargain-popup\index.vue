<template>
  <div>
    <van-popup
      class="bargain-popup"
      position="bottom"
      v-model="isShow"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      @click-overlay="close"
    >
      <div class="bargain-popup-container">
        <div class="bargin-popup-icon">
          <img src="@/assets/images/deal/bargin-popup-icon.png" alt="" />
        </div>
        <div class="bargain-title">{{ $t('我要出价') }}</div>
        <div class="bargain-input-container">
          <input
            type="number"
            v-model.trim="barginInput"
            :placeholder="$t('请输入整数')"
          />
          <div class="cell">元</div>
        </div>
        <div
          class="bargin-confirm"
          :class="{ gray: !barginInput }"
          @click="confirmBargain"
        >
          {{ $t('确定') }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiXiaohaoBargainList,
  ApiXiaohaoSubmitBargain,
} from '@/api/views/xiaohao.js';
export default {
  props: {
    // 砍价弹窗
    isShow: {
      type: Boolean,
      default: false,
    },
    tradeId: {
      type: Number | String,
      required: true,
    },
  },
  data() {
    return {
      barginInput: '', // 砍价输入金额
    };
  },
  methods: {
    // 确认砍价
    async confirmBargain() {
      if (!this.barginInput) {
        this.$toast('请输入价格');
        return false;
      }
      if (this.barginInput % 1 !== 0) {
        this.$toast('请输入整数');
        return false;
      }
      const res = await ApiXiaohaoSubmitBargain({
        amount: parseInt(this.barginInput),
        tradeId: this.tradeId,
      });
      this.$emit('success');
      this.close();
      this.$toast(res.msg);
    },
    close() {
      this.$emit('update:isShow', false);
    },
  },
};
</script>

<style lang="less" scoped>
.bargain-popup {
  background-color: transparent;
  overflow: visible;
  .bargain-popup-container {
    overflow: hidden;
    height: 267 * @rem;
    border-radius: 30 * @rem 30 * @rem 0 * @rem 0 * @rem;
    background: #ffffff;
    .bargin-popup-icon {
      width: 130 * @rem;
      height: 130 * @rem;
      position: absolute;
      left: 50%;
      top: -65 * @rem;
      transform: translateX(-50%);
    }
    .bargain-title {
      font-size: 17 * @rem;
      line-height: 24 * @rem;
      color: #000000;
      font-weight: bold;
      text-align: center;
      margin-top: 60 * @rem;
    }
    .bargain-input-container {
      box-sizing: border-box;
      width: 253 * @rem;
      height: 44 * @rem;
      display: flex;
      align-items: center;
      padding: 0 20 * @rem;
      background: #f5f5f6;
      border-radius: 22 * @rem;
      margin: 20 * @rem auto 0;
      input {
        flex: 1;
        min-width: 0;
        font-size: 14 * @rem;
        color: #000000;
        background-color: transparent;
      }
      .cell {
        font-size: 13 * @rem;
        color: #797979;
        margin-left: 10 * @rem;
      }
    }
    .bargin-confirm {
      width: 140 * @rem;
      height: 44 * @rem;
      border-radius: 22 * @rem;
      background: linear-gradient(90deg, #ff9f00 0%, @themeColor 100%);
      font-size: 14 * @rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 35 * @rem auto 0;
      &.gray {
        background: #c1c1c1;
      }
    }
  }
}
</style>
