export default [
  {
    path: '/jianlou',
    name: '<PERSON><PERSON><PERSON>',
    component: () =>
      import(/* webpackChunkName: "jianlou" */ '@/views/Jianlou'),
    meta: {
      keepAlive: true,
      pageTitle: '捡漏'
    },
  },
  {
    path: '/jianlou_detail/:id',
    name: 'JianlouDetail',
    component: () =>
      import(/* webpackChunkName: "jianlou" */ '@/views/Jianlou/JianlouDetail'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/jianlou_order_detail',
    name: 'JianlouOrderDetail',
    component: () =>
      import(
        /* webpackChunkName: "jianlou" */ '@/views/Jianlou/JianlouOrderDetail'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/my_jianlou',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    component: () =>
      import(/* webpackChunkName: "jianlou" */ '@/views/Jianlou/My<PERSON>ianlou'),
    meta: {
      keepAlive: false,
    },
  },
];
