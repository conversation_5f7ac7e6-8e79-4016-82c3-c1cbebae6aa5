<template>
  <div class="recycle-select-xiaohao-page">
    <nav-bar-2 :border="true" :title="$t('选择小号')"></nav-bar-2>
    <div class="main">
      <div class="search-bar">
        <div class="search-icon"></div>
        <input
          class="search-text"
          type="text"
          :placeholder="$t('请输入小号昵称或游戏名')"
          v-model="keyword"
        />
      </div>
      <content-empty v-if="empty"></content-empty>
      <load-more
        v-else
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
      >
        <div class="xiaohao-list">
          <div
            class="xiaohao-item"
            v-for="(item, index) in xiaohaoList"
            :key="index"
          >
            <xiaohao-item :info="item" @refresh="refresh"></xiaohao-item>
          </div>
        </div>
      </load-more>
    </div>
  </div>
</template>

<script>
import xiaohaoItem from '../components/xiaohao-item';
import { ApiXiaohaoAllPayerList } from '@/api/views/xiaohao.js';
export default {
  name: 'RecycleSelectXiaohao',
  components: {
    xiaohaoItem,
  },
  data() {
    return {
      xiaohaoList: [],
      page: 1,
      listRows: 10,
      loading: false,
      finished: false,
      empty: false,
      timer: null,
      keyword: '',
    };
  },
  watch: {
    keyword() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this.getXiaohaoList();
      }, 500);
    },
  },
  methods: {
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoAllPayerList({
        page: this.page,
        listRows: this.listRows,
        type: 2,
        keyword: this.keyword,
      });
      let { text1, text2, text3, list } = res.data;
      if (!this.text1) {
        this.text1 = text1.replace(/\n/g, '<br>');
      }
      if (!this.text2) {
        this.text2 = text2;
      }
      if (!this.text3) {
        this.text3 = text3;
      }
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
      }
      this.xiaohaoList.push(...list);
      if (!this.xiaohaoList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async refresh() {
      await this.getXiaohaoList();
    },
    async loadMore() {
      if (!this.xiaohaoList.length) {
        await this.getXiaohaoList();
      } else {
        await this.getXiaohaoList(2);
      }

      this.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.recycle-select-xiaohao-page {
  .main {
    padding: 0 18 * @rem;
    overflow: hidden;
    .search-bar {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 339 * @rem;
      height: 38 * @rem;
      border-radius: 6 * @rem;
      background-color: #fff;
      border: 1 * @rem solid #ebebeb;
      margin: 14 * @rem auto;
      padding: 0 15 * @rem;
      .search-icon {
        width: 14 * @rem;
        height: 14 * @rem;
        background: url(~@/assets/images/recycle/search-icon.png) center center
          no-repeat;
        background-size: 14 * @rem 14 * @rem;
      }
      .search-text {
        display: block;
        padding: 0 7 * @rem;
        width: 100%;
        height: 100%;
        font-size: 15 * @rem;
        color: #333;
        background-color: transparent;
      }
    }
    .xiaohao-list {
      .xiaohao-item {
        margin-top: 20 * @rem;
      }
    }
  }
}
</style>
