<template>
  <div class="game-detail-intro page">
    <nav-bar-2 title="游戏介绍" :placeholder="false" :border="true">
    </nav-bar-2>
    <van-loading class="mart-56" v-if="!loadSuccess" />
    <div v-else class="container">
      <!-- 游戏介绍 -->
      <div class="introduction section" v-if="detail.newstext">
        <div class="section-title">
          <div class="title-text">{{ $t('游戏介绍') }} </div>
        </div>
        <div
          v-html="detail.newstext"
          class="introduction-text"
          ref="content3"
        ></div>
      </div>

      <!-- 详细信息 -->
      <div
        class="section version-section"
        v-if="Object.keys(information).length"
      >
        <div class="section-title">
          <div class="title-text">详细信息 </div>
        </div>
        <div class="version-container">
          <template v-for="item in information">
            <div
              class="content-item"
              v-if="item.text || item.url || item.up_info"
              :key="item.key"
            >
              <div class="label">{{ item.title }}</div>
              <div class="value-box" v-if="item.text">
                <div class="value1">{{ item.text }}</div>
              </div>
              <div class="value-box" v-if="item.url">
                <template v-for="(val, index) in item.url">
                  <div :key="index" class="value2" @click="handleUrl(val)">
                    {{ val.text }}
                  </div>
                </template>
              </div>
              <div
                class="up-info"
                @click="toPage('UpMine', { mem_id: item.up_info.user_id })"
                v-if="item?.up_info"
              >
                <UserAvatar
                  :src="item?.up_info.avatar"
                  :self="false"
                  class="avatar"
                />
                <span class="nickname">{{ item?.up_info?.nickname }}</span>
                <span class="arrow"> </span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { isIos, isIosBox } from '@/utils/userAgent';
import { ApiGameGetPermissionInfo, ApiGameDetail } from '@/api/views/game.js';
export default {
  name: 'GameDetailIntro',
  data() {
    return {
      isIos, //是ios
      isIosBox, //是iosbox
      permissionList: [], //游戏获取权限列表
      permissionShow: false, //游戏获取权限列表展示
      loadSuccess: false, //加载完毕
      detail: {},
      information: {},
    };
  },
  async created() {
    this.detail = this.$route.params.detail || {};
    this.information = this.$route.params.information || {};
    if (Object.keys(this.detail).length) {
      this.loadSuccess = true;
    }
  },
  methods: {
    // 获取详情页信息
    async getDetail() {
      try {
        const res = await ApiGameDetail({
          id: this.$route.params.id,
        });
        this.detail = res.data.detail;
        this.information = res.data.extra_info.information;
        this.loadSuccess = true;
      } catch (error) {}
    },
    // 处理权限隐私说明
    handleUrl(val) {
      if (val.url && val.text == '隐私政策') {
        this.toPage('Iframe', {
          url: val.url,
          title: '隐私政策',
        });
      } else if (val.text == '应用权限') {
        this.handlePermission();
      } else {
        this.$toast('开发者正在努力完善中...');
      }
    },
    // 处理游戏权限详情
    async handlePermission() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
  },
  async activated() {
    await this.getDetail();
  },
};
</script>

<style lang="less" scoped>
.game-detail-intro {
  position: relative;
  background: #fff;
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
  .mart-56 {
    margin-top: 56 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .container {
    margin-top: 56 * @rem;
    display: flex;
    flex-direction: column;
    .introduction {
      width: 100%;
      box-sizing: border-box;
      position: relative;
      .category-tags {
        display: flex;
        align-items: center;
        .category-tag-item {
          height: 23 * @rem;
          margin-right: 6 * @rem;
          font-size: 10 * @rem;
          color: #93999f;
          padding: 0 5 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 5 * @rem;
          background: #f7f8fa;
        }
      }
      .introduction-text {
        font-weight: 400;
        font-size: 14 * @rem;
        color: #60666c;
        line-height: 24 * @rem;
        display: -webkit-box;
        margin-top: 12 * @rem;
        max-height: unset;
      }
      .more-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 42 * @rem;
        position: absolute;
        right: 10 * @rem;
        bottom: 0;
        background: #fafafa;
        span {
          font-size: 12 * @rem;
          color: #32b768;
          height: 17 * @rem;
          line-height: 17 * @rem;
        }
      }
    }
    .version-section {
      .section-title {
      }
      .version-container {
        margin: 12 * @rem 0;
        width: 351 * @rem;
        background: #f7f8fa;
        border-radius: 8 * @rem;
        box-sizing: border-box;
        padding: 14 * @rem 10 * @rem 13 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .content-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .label {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #93999f;
            white-space: nowrap;
          }
          .value-box {
            width: 240 * @rem;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .value1 {
              font-weight: 400;
              font-size: 14 * @rem;
              color: #30343b;
            }
            .value2 {
              font-weight: 400;
              font-size: 14 * @rem;
              color: #1cce94;
              line-height: 14 * @rem;
              // text-decoration: underline;
              display: inline;
              border-bottom: solid 1px #1cce94;
              &:not(:first-child) {
                margin-left: 9 * @rem;
              }
            }
          }
          .up-info {
            display: flex;
            align-items: center;
            .avatar {
              width: 16 * @rem;
              height: 16 * @rem;
              border-radius: 50%;
              overflow: hidden;
            }
            .nickname {
              margin-left: 6 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #30343b;
            }
            .arrow {
              width: 8 * @rem;
              height: 12 * @rem;
              margin-left: 2 * @rem;
              background: url(~@/assets/images/games/arrow-right-grey1.png)
                right center no-repeat;
              background-size: 8 * @rem 12 * @rem;
            }
          }
          &:not(:first-child) {
            margin-top: 13 * @rem;
          }
        }
      }
    }
  }
}
.permission-popup {
  max-height: 400 * @rem;
  min-height: 200 * @rem;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 16 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 60 * @rem;
    background-color: #fff;
    flex-shrink: 0;
  }
  .permission-list {
    padding: 0 14 * @rem 20 * @rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    .permission-item {
      padding: 10 * @rem;
      border-bottom: 1px solid #ebebeb;
      .name {
        font-size: 14 * @rem;
        color: #666;
      }
      .desc {
        font-size: 12 * @rem;
        color: #999;
        line-height: 18 * @rem;
        margin-top: 10 * @rem;
      }
    }
  }
}
.section {
  padding: 12 * @rem 12 * @rem 0;
  .section-arrow {
    margin-left: 4 * @rem;
    width: 8 * @rem;
    height: 12 * @rem;
  }
}
.section-title {
  display: flex;
  justify-content: space-between;
  // height: 25 * @rem;
  align-items: center;
  .title-icon {
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/games/sun-icon.png');
    margin-right: 5 * @rem;
    &.icon-jieshao {
      .image-bg('~@/assets/images/games/icon-jieshao.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
    &.icon-xinxi {
      .image-bg('~@/assets/images/games/icon-xinxi.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
    &.icon-jiagebiao {
      .image-bg('~@/assets/images/games/icon-jiagebiao.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
    &.icon-pinglun {
      .image-bg('~@/assets/images/games/icon-pinglun.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
    &.icon-fuli {
      .image-bg('~@/assets/images/games/icon-fuli.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
    &.icon-xiangguan {
      .image-bg('~@/assets/images/games/icon-xiangguan.png');
      background-position: center center;
      background-size: 18 * @rem 18 * @rem;
    }
  }
  .title-text {
    font-size: 16 * @rem;
    color: #191b1f;
    font-weight: 600;
    display: flex;
    height: 20 * @rem;
    line-height: 20 * @rem;
    align-items: center;
    .number {
      font-weight: 600;
      font-size: 13 * @rem;
      color: #303236;
    }
  }
  .title-right {
    display: flex;
    align-items: center;
    span {
      font-size: 15 * @rem;
      color: @themeColor;
    }
    .title-right-icon {
      width: 10 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/games/right-arrow.png) center center
        no-repeat;
      background-size: 10 * @rem 10 * @rem;
      margin-left: 4 * @rem;
    }
  }
}
</style>