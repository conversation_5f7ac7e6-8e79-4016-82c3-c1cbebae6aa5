<template>
  <div>
    <template v-for="item in GameInfoList">
      <div class="game-info-item" :key="item.id">
        <div class="game-img" @click="toPageDetail(item)">
          <div class="img-box">
            <img v-show="item.img" :src="item.img" alt="" />
          </div>
          <div class="title-item">
            <div class="title">{{ item.title }}</div>
          </div>
        </div>

        <div class="game-info">
          <div class="game-desc">
            <div class="desc-item">
              <span class="title">任务要求：</span>
              <span class="desc">{{ item.desc }}</span>
            </div>
            <div class="desc-item">
              <span class="title">时限要求：</span>
              <span class="desc">{{ item.limited_time_desc }}</span>
            </div>
          </div>
          <div
            class="game-btn"
            :class="{
              submit: item.is_done === 1,
              success: item.is_done === 2,
            }"
          >
            <div v-if="item.is_done === 0" @click="clickReceive(item)"
              >领取</div
            >
            <div v-if="item.is_done === 1" @click="clickGoComplete(item)"
              >提交任务</div
            >
            <div v-if="item.is_done === 2">已完成</div>

            <div class="duration" v-if="item.duration"
              >+{{ item.duration }}分钟</div
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  ApiCloudCloudTaskTake,
  ApiCloudCloudTaskReward,
} from '@/api/views/game.js';
import useTaskToast from '../TaskToast/index.js';
import { navigateToGameDetail } from '@/utils/function';
import { mapMutations } from 'vuex';
import { getPcCloudGameInfoCallback } from '@/utils/function.js';
export default {
  name: 'GameInfoList',
  props: {
    GameInfoList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  methods: {
    toPageDetail(item) {
      const itemTask = JSON.parse(JSON.stringify(item));
      const updatedTask = {
        ...itemTask,
        id: itemTask.game_id,
        index: itemTask.id,
      };
      navigateToGameDetail(updatedTask);
    },
    // 去完成
    async clickGoComplete(item) {
      await this.submitTask(item);
    },
    // 提交任务
    async submitTask(item) {
      try {
        const res = await ApiCloudCloudTaskReward({ task_id: item.id });
        useTaskToast(
          {
            msg: res.msg,
          },
          1000,
        );
        await this.refreshDataType();
        await getPcCloudGameInfoCallback();
      } catch (error) {
        // 处理错误
      }
    },
    // 领取任务
    async clickReceive(item) {
      try {
        const res = await ApiCloudCloudTaskTake({ task_id: item.id });
        await this.refreshDataType();
      } catch (error) {}
    },
    // 更新任务状态
    refreshDataType() {
      this.$nextTick(() => {
        this.$emit('refreshDataType');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.game-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 212 * @rem;
  background: #ffffff;
  box-shadow: 0 0 8 * @rem 0 rgba(0, 41, 90, 0.08);
  border-radius: 12 * @rem;
  overflow: hidden;
  .game-img {
    position: relative;

    .img-box {
      width: 339 * @rem;
      height: 150 * @rem;
      background: #eee;
      img {
        // filter: blur(1px);
      }
    }

    .title-item {
      position: absolute;
      bottom: 0;
      width: 339 * @rem;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      background: linear-gradient(
        180deg,
        rgba(3, 0, 20, 0) 0%,
        rgba(3, 0, 20, 0.5) 34%,
        #030014 82%
      );
      .title {
        margin-left: 10 * @rem;
        height: 19 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #ffffff;
        line-height: 19 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .game-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    width: 100%;
    padding: 0 8 * @rem 0 12 * @rem;
    box-sizing: border-box;
    .game-desc {
      display: flex;
      flex-direction: column;
      .desc-item {
        display: flex;
        align-items: center;

        .title {
          min-width: 0;
          flex-shrink: 0;
          white-space: nowrap;
          height: 17 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #5c5c5c;
          line-height: 14 * @rem;
        }
        .desc {
          width: 180 * @rem;
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #777777;
          line-height: 14 * @rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-left: 4 * @rem;
        }
        &:last-child {
          margin-top: 4 * @rem;
        }
      }
    }
    .game-btn {
      width: 72 * @rem;
      height: 28 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      color: #fff;
      border-radius: 25 * @rem;
      background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
      font-size: 12 * @rem;

      &.success {
        background: #cccccc;
        color: #ffffff;
      }
      &.submit {
        background: #ecfbf4;
        color: #21b98a;
      }
      position: relative;
      .text {
        font-weight: 500;
        font-size: 14 * @rem;
        color: #ffffff;
      }
      .duration {
        position: absolute;
        top: -13 * @rem;
        right: -4 * @rem;
        min-width: 44 * @rem;
        white-space: nowrap;
        height: 18 * @rem;
        line-height: 18 * @rem;
        background: #ffe100;
        border-radius: 4 * @rem;
        font-weight: 500;
        font-size: 10 * @rem;
        padding: 0 4 * @rem;
        color: #6d0909;
      }
    }
  }
  &:not(:first-child) {
    margin-top: 16 * @rem;
  }
  &:last-child {
    margin-bottom: 16 * @rem;
  }
}
</style>
