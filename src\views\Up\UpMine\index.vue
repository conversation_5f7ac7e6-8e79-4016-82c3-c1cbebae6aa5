<template>
  <div class="up-detail">
    <div class="bg-color"></div>
    <div class="bg"></div>
    <nav-bar-2
      bgStyle="transparent-white"
      :title="''"
      :placeholder="false"
      v-if="nav_bg_transparent"
      class="nav-bar"
    >
    </nav-bar-2>
    <nav-bar-2 :title="''" :placeholder="false" :border="true" v-else>
    </nav-bar-2>
    <header>
      <status-bar></status-bar>
      <div class="wrapper">
        <div v-if="up_info.up" class="container">
          <div class="left">
            <UserAvatar :src="up_info.up.avatar" :self="self" class="avatar" />
            <div class="info">
              <div class="top">{{ up_info.up.nickname }}</div>
              <div class="bottom">
                <div class="text mr">IP归属地：{{ up_info.up.ip_region }}</div>
              </div>
            </div>
          </div>
          <div
            v-if="!self"
            :class="{ empty: up_info.is_focus == 1 }"
            @click="handleFollow"
            class="right"
          >
            {{ follow_status }}
          </div>
        </div>
      </div>
    </header>
    <main>
      <div class="tab-container">
        <van-sticky :offset-top="sticky_offset_top">
          <div class="tabs">
            <div
              class="tab btn"
              v-for="(tab, index) in tab_list"
              :key="index"
              :class="{ active: current === index }"
              @click="clickTab(index)"
            >
              <span>{{ tab.name }} </span>
            </div>
            <div
              class="tab-line"
              :style="{ left: `${current * 172 * remNumberLess}rem` }"
            ></div>
          </div>
        </van-sticky>
        <!-- 动态tab -->
        <div class="tab-content" v-if="current === 0">
          <CommentTab :self="self" :mem_id="mem_id" />
        </div>
        <!-- 发布tab -->
        <div class="tab-content" v-if="current === 1">
          <GameTab :self="self" :mem_id="mem_id" />
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { ApiUserIndexInfo, ApiUserFollowUser } from '@/api/views/users';
import GameTab from './components/game-tab';
import CommentTab from './components/comment-tab';

export default {
  name: 'UpMine',
  data() {
    return {
      nav_bg_transparent: true, //导航背景是否透明
      up_info: {}, //up相关信息
      sticky_offset_top: 0, //顶部状态栏高度
      load_success: false, //加载是否完毕
      current: 0, //当前高亮tab
      tab_list: [
        {
          name: '动态',
        },
        {
          name: '发布',
        },
      ], //tab按钮
      remNumberLess, //rem大小转换
      self: false, //是否是自己
      mem_id: 0, //up主id
    };
  },
  computed: {
    follow_status() {
      return parseInt(this.up_info.is_focus) === 0 ? '关注' : '已关注';
    },
  },
  async created() {
    await this.init();
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.sticky_offset_top =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 初始化
    async init() {
      this.self =
        this.$route.params.mem_id == this.userInfo.user_id ? true : false;
      this.mem_id = parseInt(
        this.$route.params.mem_id
          ? this.$route.params.mem_id
          : this.userInfo.user_id,
      );
      const res = await ApiUserIndexInfo({ memId: this.mem_id });
      this.up_info = res.data;
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.nav_bg_transparent = false;
      } else {
        this.nav_bg_transparent = true;
      }
    },
    // 点击tab
    clickTab(index) {
      window.scrollTo(0, 0);
      this.current = index;
    },
    // 处理关注
    async handleFollow() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.finished == false) return false;
      this.finished = false;
      try {
        const res = await ApiUserFollowUser({
          memId: this.up_info.up.user_id,
          type: this.up_info.is_focus == 0 ? 1 : 0,
        });
        await this.init();
        this.$toast(res.msg);
      } finally {
        this.finished = true;
      }
    },
  },
  components: {
    GameTab,
    CommentTab,
  },
};
</script>
<style lang="less" scoped>
.up-detail {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.bg-color {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 222 * @rem;
  background: linear-gradient(180deg, #6f2c2c 0%, #251a33 86.49%);
}
.bg {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  width: 375 * @rem;
  height: 180 * @rem;
  .image-bg('~@/assets/images/up/up_bg2.png');
}
header {
  overflow: hidden;
  position: relative;
  z-index: 2;
  .wrapper {
    position: relative;
    margin-top: 50 * @rem;
    height: 96 * @rem;
    overflow: hidden;
    .container {
      position: relative;
      z-index: 2;
      display: flex;
      margin: 28 * @rem 18 * @rem;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      .left {
        display: flex;
        .avatar {
          width: 40 * @rem;
          height: 40 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
        .info {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 8 * @rem;
          .top {
            font-size: 15 * @rem;
          }
          .bottom {
            display: flex;
            justify-content: space-between;
            font-size: 11 * @rem;
            .text.mr {
              margin-right: 24 * @rem;
            }
          }
        }
      }
      .right {
        width: 56 * @rem;
        height: 24 * @rem;
        background-color: rgba(0, 0, 0, 0.4);
        border-radius: 12 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        &.empty {
          background: #dcdde1;
          color: rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
main {
  flex: 1 0;
  z-index: 2;
  border-radius: 20 * @rem 20 * @rem 0 0;
  background: #fff;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .tab-container {
    display: flex;
    flex-direction: column;
    flex: 1 0;
    .tabs {
      display: flex;
      position: relative;
      background-color: #fff;
      align-items: center;
      padding: 0 18 * @rem;
      .tab {
        flex: 1;
        height: 44 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          display: block;
          font-size: 18 * @rem;
          font-family:
            PingFang SC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #333333;
          position: relative;
        }
      }
      .line {
        flex: 1;
        height: 0 * @rem;
        border: 1 * @rem solid #e8e8e8;
      }
      .tab-line {
        position: absolute;
        width: 12 * @rem;
        height: 4 * @rem;
        border-radius: 2 * @rem;
        background-color: @themeColor;
        left: 0;
        bottom: 0;
        transform: translateX(95 * @rem);
        transition: 0.3s;
      }
    }
    .tab-content {
      display: flex;
      flex-direction: column;
      flex: 1 0;
    }
  }
}
</style>
