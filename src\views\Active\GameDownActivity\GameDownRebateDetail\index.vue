<template>
  <div class="page game-down-rebate-detail">
    <nav-bar-2 :border="true" title="返利申请" :azShow="true"> </nav-bar-2>
    <div class="main">
      <div class="game-bar">
        <div class="game-icon">
          <img :src="info.titlepic" alt="" />
        </div>
        <div class="game-info">
          <div class="game-title">{{ info.title }}</div>
          <div class="xh-nickname">{{ info.xh_name }}</div>
        </div>
        <div
          class="status"
          :class="{ success: info.status == 1, fail: info.status == 2 }"
        >
          {{ info.status_str }}
        </div>
      </div>

      <div class="card-container" v-if="info.id">
        <div class="card-item">
          <div class="card-title">活动标题</div>
          ：
          <div class="card-right">
            <div class="desc">专属活动</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">游戏账号</div>
          ：
          <div class="card-right">
            <div class="desc">{{ userInfo.username }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">游戏区服</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.game_area }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">角色名</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.game_role_name }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">角色ID</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.game_role_id }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">礼包选择</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.reward_type_str }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">联系方式</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.contact }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">备注</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.user_remark }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">上传图片</div>
          ：
          <div class="card-right">
            <div class="photos" v-if="info.photos">
              <div
                class="photo"
                v-for="(item, index) in info.photos.split(',')"
                :key="index"
              >
                <img :src="item" alt="" />
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">申请时间</div>
          ：
          <div class="card-right">
            <div class="desc">{{ formatDate(info.create_time) }}</div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">温馨提示</div>
          ：
          <div class="card-right">
            <div class="desc">{{ info.check_remark_str }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      info: {},
    };
  },
  created() {
    this.info = this.$route.params.info;
    console.log(555, this.info.photos);
  },
  methods: {
    formatDate(val) {
      let { year, date, time, second } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
};
</script>

<style lang="less" scoped>
.game-down-rebate-detail {
  .main {
    box-sizing: border-box;
    background-color: #f5f5f6;
    padding: 16 * @rem 18 * @rem;
    flex: 1;
    .game-bar {
      box-sizing: border-box;
      width: 339 * @rem;
      height: 76 * @rem;
      display: flex;
      align-items: center;
      padding: 8 * @rem 12 * @rem;
      border-radius: 12 * @rem;
      background-color: #ffffff;
      .game-icon {
        width: 60 * @rem;
        height: 60 * @rem;
      }
      .game-info {
        flex: 1;
        min-width: 0;
        margin-left: 12 * @rem;
        .game-title {
          font-size: 16 * @rem;
          color: #121212;
          font-weight: 600;
          line-height: 16 * @rem;
        }
        .xh-nickname {
          font-size: 13 * @rem;
          line-height: 13 * @rem;
          color: #757575;
          margin-top: 15 * @rem;
        }
      }
      .status {
        font-size: 13 * @rem;
        color: #ff6957;
        margin-top: 30 * @rem;
        &.success {
          color: #3dc198;
        }
        &.fail {
          color: #757575;
        }
      }
    }
    .card-container {
      background: #fff;
      border-radius: 12 * @rem;
      margin: 20 * @rem auto 0;
      padding: 16 * @rem;
      .card-item {
        display: flex;
        &:not(:first-of-type) {
          margin-top: 12 * @rem;
        }

        .card-title {
          width: 55 * @rem;
          color: #757575;
          font-size: 13 * @rem;
          height: 18 * @rem;
          line-height: 18 * @rem;
          text-align: justify;
          &::after {
            content: '';
            width: 100%;
            display: inline-block;
          }
        }
        .card-right {
          line-height: 18 * @rem;
          flex: 1;
          min-width: 0;
          .desc {
            font-size: 13 * @rem;
            color: #121212;
            line-height: 18 * @rem;
          }
          .photos {
            display: flex;
            gap: 12 * @rem;
            .photo {
              width: 52 * @rem;
              height: 52 * @rem;
              border-radius: 8 * @rem;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
}
</style>
