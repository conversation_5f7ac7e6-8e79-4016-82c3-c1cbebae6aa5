<template>
  <van-popup
    v-model="popupShow"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    position="bottom"
    round
    get-container="body"
    class="server-popup"
  >
    <div class="kefu" @click="openKefu">动态开服</div>
    <div class="title">开服动态</div>
    <div class="close-btn" @click="popupShow = false"></div>
    <div class="popup-content">
      <content-empty v-if="empty" :tips="tips"></content-empty>
      <load-more
        v-else
        class="list-container"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
      >
        <div class="server-list">
          <div
            class="server-item"
            v-for="(server, serverIndex) in openList"
            :key="serverIndex"
          >
            <div class="left">
              <div class="time">
                <div class="day">
                  {{
                    server.is_today
                      ? $t('今日')
                      : $handleTimestamp(server.newstime).date
                  }}
                </div>
                <div class="hour">
                  {{ $handleTimestamp(server.newstime).time }}
                </div>
              </div>
              <div class="quhao">{{ server.state }}</div>
            </div>
            <div class="status had" v-if="server.countdown_second < 0">
              {{ $t('已开服') }}
            </div>
            <template v-else>
              <div
                class="status btn"
                v-if="server.status == 0"
                @click="remind(server.id, 1)"
              >
                {{ $t('提醒我') }}
              </div>
              <div
                class="status btn"
                v-if="server.status == 1"
                @click="remind(server.id, 0)"
              >
                {{ $t('取消') }}
              </div>
            </template>
          </div>
        </div>
      </load-more>
      <van-dialog
        class="wx-popup"
        v-model="wx_popup.show"
        :lock-scroll="false"
        show-cancel-button
        confirmButtonText="设置微信提醒"
        @confirm="toPage('BindWeChat')"
        get-container="body"
      >
        <div class="bg"></div>
        <div class="big-text">设置微信提醒</div>
        <div class="small-text">
          开服提醒需先绑定微信，关注公众号后才可生效哦~
        </div>
      </van-dialog>
    </div>
  </van-popup>
</template>
<script>
import { ApiServerIndex, ApiServerRemind } from '@/api/views/game.js';
export default {
  name: 'serverList',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      required: true,
    },
  },
  data() {
    return {
      openList: [],
      finished: false,
      loading: false,
      empty: false,
      tips: '',
      wx_popup: {
        show: false,
      },
    };
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },
  methods: {
    async remind(serverId, status) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        const res = await ApiServerRemind({
          serverId,
          status,
        });
        await this.getserverList();
        if (res.code > 0) {
          this.$toast(res.msg);
        }
      } catch (e) {
        if (e.code == -16) {
          this.wx_popup.show = true;
        }
      }
    },
    async getserverList() {
      const res = await ApiServerIndex({
        gameId: this.id,
      });

      if (!res.data.length) {
        this.empty = true;
        this.tips = res.msg;
      } else {
        this.openList = res.data;
      }
      this.loading = false;
      this.finished = true;
    },
    async loadMore() {
      if (this.finished) return false;
      await this.getserverList();
      this.loading = false;
    },
  },
};
</script>
<style lang="less" scoped>
.server-popup {
  display: flex;
  flex-direction: column;
  background-color: #fbfbfe;
  padding: 0 18 * @rem 20 * @rem;
  box-sizing: border-box;
  height: 60vh;

  .title {
    padding: 26 * @rem 0 24 * @rem;
    width: 100%;
    text-align: center;
    height: 22 * @rem;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #000000;
    line-height: 22 * @rem;
  }

  .kefu {
    display: flex;
    align-items: center;
    height: 17 * @rem;
    font-weight: 400;
    font-size: 12 * @rem;
    color: #999999;
    line-height: 14 * @rem;
    position: absolute;
    top: 28 * @rem;
    left: 18 * @rem;
    z-index: 1;

    &::before {
      content: '';
      display: block;
      width: 12 * @rem;
      height: 12 * @rem;
      background: url(~@/assets/images/my-game/kefu.png) no-repeat;
      background-size: 12 * @rem 12 * @rem;
      margin-right: 4 * @rem;
    }
  }

  .close-btn {
    display: block;
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/my-game/close.png) no-repeat center;
    background-size: 16 * @rem 16 * @rem;
    padding: 10 * @rem;
    position: absolute;
    top: 15 * @rem;
    right: 8 * @rem;
  }

  .popup-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;

    .server-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .server-item {
        box-sizing: border-box;
        width: 165 * @rem;
        padding: 0 12 * @rem;
        background: #fff;
        border-radius: 12 * @rem;
        border: 0.5 * @rem solid #f0f0f0;
        height: 69 * @rem;
        display: flex;
        align-items: center;
        &:not(:nth-of-type(-n + 2)) {
          margin-top: 12 * @rem;
        }
        .left {
          flex: 1;
          min-width: 0;
          .time {
            font-size: 14 * @rem;
            color: #222;
            font-weight: bold;
            display: flex;
            align-items: center;
            line-height: 16 * @rem;
            .hour {
              margin-left: 5 * @rem;
            }
          }
          .quhao {
            font-size: 11 * @rem;
            color: #999;
            font-weight: 400;
            margin-top: 3 * @rem;
            line-height: 14 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .status {
          box-sizing: border-box;
          width: 52 * @rem;
          height: 24 * @rem;
          color: @themeColor;
          background-color: #ecfbf4;
          font-size: 12 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4 * @rem;
          &.had {
            color: #fff;
            background-color: #ced4d7;
          }
        }
      }
    }
  }
}
.wx-popup {
  .bg {
    display: block;
    width: 150 * @rem;
    height: 99 * @rem;
    margin: 25 * @rem auto;
    background: url(~@/assets/images/games/ic_subscribe_success.png) no-repeat;
    background-size: 150 * @rem 99 * @rem;
  }
  .big-text {
    font-size: 20 * @rem;
    text-align: center;
  }
  .small-text {
    margin: 15 * @rem 20 * @rem 30 * @rem;
    font-size: 14 * @rem;
    text-align: center;
  }
}
</style>
