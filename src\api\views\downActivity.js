import { request } from '../index';

/**
 * 2.返利申请-详情
 */
export function ApiDownActivityRebateInfo(params = {}) {
  return request('/api/down_activity/rebateInfo', params);
}

/**
 * 5.积分兑换接口（返回兑换成功与否及兑换后的数据）
 */
export function ApiDownActivityExchange(params = {}) {
  return request('/api/down_activity/exchange', params);
}

/**
 * 6.积分明细接口（积分的获得与使用记录）
 */
export function ApiDownActivityPointLog(params = {}) {
  return request('/api/down_activity/pointLog', params);
}

/**
 * 7.兑换记录接口（兑换物品的记录数据）
 */
export function ApiDownActivityExchangeLog(params = {}) {
  return request('/api/down_activity/exchangeLog', params);
}

/**
 * 9.游戏签到接口
 */
export function ApiDownActivityGameSign(params = {}) {
  return request('/api/down_activity/gameSign', params);
}

/**
 * 10.锦鲤礼包领取接口
 */
export function ApiDownActivityLuckyReceive(params = {}) {
  return request('/api/down_activity/luckyReceive', params);
}

/**
 * 11.返利奖励领取接口
 */
export function ApiDownActivityRebateReceive(params = {}) {
  return request('/api/down_activity/rebateReceive', params);
}

/**
 * 12.中奖记录
 */
export function ApiDownActivityLotteryLog(params = {}) {
  return request('/api/down_activity/lotteryLog', params);
}

/**
 * 13.检测是否中奖
 */
export function ApiDownActivityCheckLottery(params = {}) {
  return request('/api/down_activity/checkLottery', params);
}

/**
 * 提交返利申请
 */
export function ApiDownActivitySubmitRebate(params = {}) {
  return request('/api/down_activity/submitRebate', params);
}

/**
 * 14.我的返利
 * status：0-待审核，1-审核成功，2-审核失败 -1-全部
 */
export function ApiDownActivityMineRebate(params = {}) {
  return request('/api/down_activity/mineRebate', params);
}
