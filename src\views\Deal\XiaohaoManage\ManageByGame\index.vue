<template>
  <div class="manage-by-game">
    <div class="search-container">
      <div class="search-bar">
        <div class="search-icon"></div>
        <form>
          <input
            type="text"
            v-model="keyword"
            :placeholder="$t('请输入游戏名')"
          />
        </form>
      </div>
    </div>
    <content-empty
      v-if="empty"
      :tips="$t('咦，什么都没找到哦~')"
    ></content-empty>
    <yy-list
      v-else
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <div class="game-list">
        <div
          class="game-item"
          v-for="item in gameList"
          :key="item.id"
          @click="clickGameItem(item)"
        >
          <div class="game-icon">
            <img :src="item.game_icon" alt="" />
          </div>
          <div class="game-right">
            <div class="game-name">
              {{ item.title
              }}<span class="game-subtitle" v-if="item.subtitle">{{
                item.subtitle
              }}</span>
            </div>
            <div class="count">
              <span>{{ item.xh_count }}</span
              >{{ $t('个小号') }}
            </div>
          </div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoGetGameList } from '@/api/views/xiaohao.js';
export default {
  name: 'ManageByGame',
  data() {
    return {
      keyword: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
      gameList: [],
      timer: null,
    };
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      this.timer = setTimeout(async () => {
        this.finished = false;
        await this.getGameList();
      }, 300);
    },
  },
  async activated() {
    this.finished = false;
    await this.getGameList();
  },
  methods: {
    clickGameItem(item) {
      this.toPage('GameXiaohaoList', { id: item.app_id });
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoGetGameList({
        keyword: this.keyword,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      this.gameList.push(...res.data.list);
      if (this.gameList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.manage-by-game {
  position: fixed;
  width: 100%;
  height: calc(100vh - 94 * @rem - @safeAreaTop);
  height: calc(100vh - 94 * @rem - @safeAreaTopEnv);
  top: calc(44 * @rem);
  display: flex;
  flex-direction: column;
  .search-container {
    width: 100%;
    height: 60 * @rem;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .search-bar {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 347 * @rem;
      height: 38 * @rem;
      background: #f4f4f4;
      border-radius: 19 * @rem;
      padding: 0 18 * @rem;
      margin: 0 auto;
      .search-icon {
        width: 17 * @rem;
        height: 17 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat;
        background-size: 17 * @rem 17 * @rem;
      }
      form {
        flex: 1;
        min-width: 0;
        display: flex;
        background-color: transparent;
        margin-left: 7 * @rem;
        input {
          display: block;
          flex: 1;
          min-width: 0;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
    }
  }
  .yy-list {
    flex: 1;
    overflow-y: auto;
  }
  .game-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 10 * @rem 14 * @rem 0;
    .game-item {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 165 * @rem;
      height: 70 * @rem;
      background: #ffffff;
      box-shadow: 0 * @rem 0 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.16);
      border-radius: 8 * @rem;
      margin-top: 20 * @rem;
      padding: 0 12 * @rem;
      &:nth-of-type(-n + 2) {
        margin-top: 0;
      }
      .game-icon {
        width: 42 * @rem;
        height: 42 * @rem;
        border-radius: 8 * @rem;
        background-color: #bfbfbf;
        overflow: hidden;
      }
      .game-right {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .game-name {
          font-size: 14 * @rem;
          color: #000000;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: flex;
          align-items: center;
          .game-subtitle {
            box-sizing: border-box;
            border: 1 * @rem solid fade(@themeColor, 80);
            border-radius: 3 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 3 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            vertical-align: middle;
            line-height: 1;
          }
        }
        .count {
          font-size: 12 * @rem;
          color: #666666;
          margin-top: 5 * @rem;
          span {
            color: #f99412;
          }
        }
      }
    }
  }
}
</style>
