<template>
  <div class="page platform-coin-page">
    <nav-bar-2 :title="$t('平台币充值')" :border="true">
      <template #right>
        <div class="coin-detail" @click="toPage('PlatformCoinDetail')">
          {{ $t('明细') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="gold-bar">
      <div class="gold-bar-item">
        <div class="item-left">{{ $t('充值账号') }}：</div>
        <div class="item-right account">{{ userInfo.username }}</div>
      </div>
      <div class="gold-bar-item">
        <div class="item-left">{{ $t('我的平台币') }}：</div>
        <div class="item-right platform-gold">{{ userInfo.ptb || 0 }}</div>
      </div>
    </div>
    <div class="main">
      <div class="top-tips">
        1000平台币={{ formatExchangeRate(100, exchangeRateInfo.exchange_rate)
        }}{{ exchangeRateInfo.unit }}（{{ exchangeRateInfo.fh_unit }}={{
          exchangeRateInfo.unit
        }}）
      </div>
      <ul class="select-list">
        <li
          class="select-item"
          v-for="(item, index) in selectList"
          :key="index"
          :class="{
            on: selectPtb == item.date && !(!item.first && item.is_only),
            cant: item.first == false,
          }"
          @click="changePtb(item)"
        >
          <div class="date">{{ item.date }}</div>
          <div class="date-title">{{ item.date_unit }}</div>
          <div class="money" v-if="!isHw">
            {{ item.money }}{{ item.money_unit }}
          </div>
          <div class="is-recommend" v-if="item.is_only">首充</div>
          <div class="is-recommend" v-if="item.is_recommend == 1">
            {{ $t('推荐') }}
          </div>
        </li>
      </ul>
      <div class="input-container" v-if="!isHw">
        <input
          type="number"
          class="text text-input"
          :placeholder="placeholder"
          v-model="selectMoney"
        />
        <span class="text text-right">({{ $t('元') }})</span>
      </div>
      <div class="input-container" v-else>
        <input
          type="number"
          class="text text-input"
          :placeholder="placeholder_hw"
          v-model="selectPtb"
        />
        <span class="text text-right">(个)</span>
      </div>
      <div class="title2">{{ $t('请选择支付方式') }}</div>
      <ul class="pay-list">
        <li
          class="pay-item"
          :class="{ on: payWay.key == item.key }"
          v-for="(item, index) in payWayList"
          :key="index"
          @click="payWay = item"
        >
          <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
          <span class="text">{{ item.name }}</span>
          <div class="select-icon"></div>
        </li>
      </ul>
      <div class="recharge btn" @click="handlePay">
        {{ $t('支付') }}<span>{{ totalMoney }}</span
        >{{ exchangeRateInfo.unit }}
      </div>
      <div class="title title2">{{ $t('温馨提示') }}</div>
      <div class="explain">
        平台币不会过期，可用于本平台任意游戏充值（腾讯小游戏、第三方代理游戏除外）<br />
        {{ $t('游戏中支付时选择“平台币支付”即可') }}<br />
        若订单支付失败，在关闭订单后，抵扣的平台币将自动返回到账号上。若有疑问，可联系客服。<br />
        用户首次充值最低可充值100平台币，后续充值平台币需大于等于300平台币。
      </div>
    </div>
  </div>
</template>
<script>
import {
  ApiCreateOrderPtb,
  ApiGetPayUrl,
  ApiPlatformGetInfo,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';

import { formatExchangeRate } from '@/utils/tools.js';

import { mapGetters } from 'vuex';

export default {
  name: 'PlatformCoin',
  data() {
    return {
      payWay: {}, // 支付方式的对象
      selectMoney: 0, // 准备充值的金额
      selectPtb: 0,
      maxMoney: 0,
      selectList: [], // 充值金额列表
      payWayList: [], // 支付方式
    };
  },
  watch: {
    selectMoney() {
      if (!this.isHw) {
        if (!this.selectMoney) {
          this.selectPtb = 0;
        }
        if (Math.ceil(this.selectMoney) !== Number(this.selectMoney)) {
          this.$toast.fail(this.$t('请输入整数'));
          this.selectMoney = Number(this.selectMoney);
        }
        if (this.selectMoney > this.maxMoney) this.selectMoney = this.maxMoney;
        this.selectPtb = Number(this.selectMoney) * 10;
      }
    },
    selectPtb() {
      if (Math.ceil(this.selectPtb) !== Number(this.selectPtb)) {
        this.$toast.fail(this.$t('请输入整数'));
        this.selectPtb = Number(this.selectPtb);
      }
      if (this.selectPtb > this.maxMoney * 10) {
        this.selectPtb = this.maxMoney * 10;
      }
    },
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    // 总金额
    totalMoney() {
      // return Number(this.selectMoney) || 0;
      return this.formatExchangeRate(
        Number(this.selectPtb / 10),
        this.exchangeRateInfo.exchange_rate,
      );
    },
    placeholder() {
      return `${this.$t('请输入充值金额')}30~${this.maxMoney}`;
    },
    placeholder_hw() {
      return `${this.$t('请输入充值金额')}300~${this.maxMoney * 10}`;
    },
    exchangeRateInfo() {
      return this.payWay?.exchange_rate_info || {};
    },
  },
  async created() {
    await this.$onAppFinished;
    await this.getPlatformInfo();
  },
  methods: {
    formatExchangeRate,
    handlePay() {
      if (this.selectPtb / 10 != Math.ceil(this.selectPtb / 10)) {
        this.$toast.fail('平台币充值个数仅支持10的倍数');
        return;
      }
      ApiCreateOrderPtb({
        isNew: 1,
        money: this.selectPtb / 10,
        payWay: this.payWay.key,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.payWay.key,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {
            })
            .catch(() => {
            });
        });
      });
    },
    changePtb(item) {
      if (item.first == false) {
        this.$toast('仅限首次充值');
        return false;
      }
      this.selectMoney = item.money;
      this.selectPtb = item.date;
    },
    async getPlatformInfo() {
      const res = await ApiPlatformGetInfo();
      let { maxMoney, payWayList, platiconList } = res.data;
      this.maxMoney = maxMoney;
      this.selectList = platiconList;
      this.payWayList = payWayList;
      this.payWay = this.payWayList[0];
      this.selectMoney =
        platiconList.find(item => {
          return item.is_recommend == 1;
        }).money || 0;
      this.selectPtb = platiconList.find(item => {
        return item.is_recommend == 1;
      }).date;
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-page {
  .coin-detail {
    color: #000;
    font-size: 16 * @rem;
  }
  .gold-bar {
    box-sizing: border-box;
    background-color: #f4f4f4;
    padding: 0 18 * @rem;
    width: 339 * @rem;
    margin: 15 * @rem auto 0;
    border-radius: 10 * @rem;
    .gold-bar-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44 * @rem;
      &:not(:first-of-type) {
        border-top: 0.5 * @rem solid #e4e4e4;
      }
      .item-left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .item-right {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        &.platform-gold {
          font-size: 14 * @rem;
          color: @themeColor;
          font-weight: 400;
        }
      }
    }
  }
  .main {
    padding: 0 25 * @rem 20 * @rem;

    .top-tips {
      font-size: 12 * @rem;
      text-align: center;
      color: #909090;
      font-weight: 400;
      padding-top: 18 * @rem;
    }
    .title2 {
      margin-top: 24 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
    }
    .select-list {
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .select-item {
        position: relative;
        box-sizing: border-box;
        width: 98 * @rem;
        height: 74 * @rem;
        margin-top: 15 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1 * @rem solid #e1e1e1;
        border-radius: 8 * @rem;
        overflow: hidden;
        cursor: pointer;
        .is-recommend {
          position: absolute;
          left: -1 * @rem;
          top: -1 * @rem;
          width: 30 * @rem;
          height: 18 * @rem;
          background: @themeColor;
          border-radius: 6 * @rem 0 * @rem 6 * @rem 0 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
        .date {
          text-align: center;
          font-size: 18 * @rem;
          color: #333333;
          color: #000000;
          font-weight: 600;
        }
        .date-title {
          text-align: center;
          font-size: 10 * @rem;
          color: #000000;
          font-weight: 600;
          margin-top: 2 * @rem;
        }
        .money {
          text-align: center;
          font-size: 12 * @rem;
          color: #797979;
          margin-top: 6 * @rem;
        }
        &.on {
          border-color: @themeColor;
          border-width: 1 * @rem;
          background-color: #fff7f2;
          .date {
            color: @themeColor;
          }
          .date-title {
            color: @themeColor;
          }
          .money {
            color: @themeColor;
          }
          &:before {
            content: '';
            position: absolute;
            display: block;
            background: url(~@/assets/images/recharge/coin-selected.png)
              no-repeat;
            background-size: 18 * @rem 15 * @rem;
            top: -1 * @rem;
            right: -1 * @rem;
            width: 18 * @rem;
            height: 15 * @rem;
          }
        }
        &.cant::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          z-index: 999;
          width: 100%;
          height: 100%;
          background: rgba(230, 230, 230, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
    .pay-list {
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 12 * @rem;
    }
    .pay-item {
      box-sizing: border-box;
      width: 49%;
      height: 52 * @rem;
      padding-top: 10 * @rem;
      text-align: center;
      border: 0.5 * @rem solid #cbcbcb;
      border-radius: 6 * @rem;
      font-size: 0;
      display: flex;
      align-items: center;
      padding: 0 12 * @rem;
      &:not(:nth-of-type(-n + 2)) {
        margin-top: 10 * @rem;
      }
      &.on {
        border-color: @themeColor;
        border-width: 1 * @rem;
      }
      .icon {
        display: block;
        width: 25 * @rem;
        height: 25 * @rem;
        background-repeat: no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }
      .text {
        display: block;
        font-size: 14 * @rem;
        font-weight: 500;
        color: #000;
        flex: 1;
        min-width: 0;
        margin-left: 5 * @rem;
        text-align: left;
      }
      .select-icon {
        width: 16 * @rem;
        height: 16 * @rem;
        background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
        background-size: 16 * @rem 16 * @rem;
      }
      &.on .select-icon {
        background-image: url(~@/assets/images/recharge/pay-yes.png);
      }
    }
    .input-container {
      display: flex;
      box-sizing: border-box;
      margin-top: 18 * @rem;
      padding: 12 * @rem 0;
      border-bottom: 1 * @rem solid #cbcbcb;
      letter-spacing: 0.625 * @rem;
      font-size: 0;
      .text {
        display: inline-block;
        width: 50 * @rem;
        font-size: 16 * @rem;
        color: #000;
      }
      .text-right {
        text-align: right;
        padding-right: 10 * @rem;
      }
      .text-input {
        flex: 1;
        min-width: 0;
        height: 20 * @rem;
        font-size: 14 * @rem;
        color: #333;
        margin-right: 10 * @rem;
      }
    }
    .tips {
      font-size: 12 * @rem;
      color: #ffb43d;
      line-height: 18 * @rem;
      margin-top: 10 * @rem;
    }
    .explain {
      margin-top: 6 * @rem;
      font-size: 12 * @rem;
      color: #8a848a;
      line-height: 18 * @rem;
    }

    .recharge {
      height: 44 * @rem;
      margin: 0 20 * @rem;
      margin-top: 30 * @rem;
      line-height: 44 * @rem;
      text-align: center;
      letter-spacing: 0.625 * @rem;
      font-size: 16 * @rem;
      font-weight: 500;
      color: #ffffff;
      background: @themeBg;
      border-radius: 22 * @rem;
      &.no {
        background: #c5c5c5;
      }
    }
  }
}

.coupon {
  display: flex;
  align-items: center;
  margin-top: 14 * @rem;
  height: 30 * @rem;
  .title-text {
    font-size: 17 * @rem;
    color: #333333;
    width: 60 * @rem;
  }
  .coupon-text {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: flex-end;
    .coupon-value {
      &.have-coupon {
        height: 26 * @rem;
        border-radius: 4 * @rem;
        background: linear-gradient(90deg, #ffb43d, #ff9d3d);
        padding: 0 7 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      &.used-coupon {
        font-size: 16 * @rem;
        color: #ff4747;
        span {
          font-size: 14 * @rem;
        }
      }
      &.no-coupon {
        font-size: 14 * @rem;
        color: #999999;
      }
    }
  }
  .right-icon {
    width: 7 * @rem;
    height: 12 * @rem;
    background: url(~@/assets/images/recharge/right-icon.png) no-repeat;
    background-size: 7 * @rem 12 * @rem;
    margin-left: 7 * @rem;
  }
}
</style>
