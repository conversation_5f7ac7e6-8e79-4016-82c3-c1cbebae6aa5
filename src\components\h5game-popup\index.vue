<template>
  <iframe
    v-show="h5gamePopup > 0"
    id="h5-iframe"
    :src="h5GameUrl"
    frameborder="0"
    :class="{ mini: h5gamePopup == 2 }"
    class="h5game-iframe"
    ref="popup"
  ></iframe>
</template>
<script>
import { mapGetters, mapMutations } from "vuex";

export default {
  computed: {
    ...mapGetters({
      gameInfo: "game/gameInfo",
      h5GameUrl: "game/h5GameUrl",
      h5gamePopup: "game/h5gamePopup",
    }),
  },
  watch: {
    // h5gamePopup(value) {
    //   if (value == 2) {
    //     const ball = document.getElementById("ball");
    //     let popupDef = document.getElementsByClassName("h5game-popup")[0];
    //     popupDef.style.top = `${ball.offsetTop}px`;
    //     popupDef.style.left = `${ball.offsetLeft}px`;
    //   }
    // },
  },
  mounted() {
    if (this.h5gamePopup) {
      this.setH5gamePopup(0);
    }
  },
  methods: {
    toMax() {
      this.setH5gamePopup(1);
    },
    ...mapMutations({
      setH5gamePopup: "game/setH5gamePopup",
    }),
  },
};
</script>
<style lang="less" scoped>
.h5game-iframe {
  box-sizing: border-box;
  position: fixed;
  top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  &.mini {
    width: 0;
    height: 0;
    transition: top 3s ease-in-out;
  }
  background: #000;
  // #main {
  //   position: fixed;
  //   top: constant(safe-area-inset-top);
  //   top: env(safe-area-inset-top);
  //   left: constant(safe-area-inset-left);
  //   left: env(safe-area-inset-left);
  //   width: calc(
  //     100% - constant(safe-area-inset-left) - constant(safe-area-inset-right)
  //   );
  //   width: calc(100% - env(safe-area-inset-left) - env(safe-area-inset-right));
  //   height: calc(
  //     100vh - constant(safe-area-inset-bottom) - constant(safe-area-inset-top)
  //   );
  //   height: calc(
  //     100vh - env(safe-area-inset-bottom) - env(safe-area-inset-top)
  //   );
  // margin-top: @safeAreaTop;
  // margin-top: @safeAreaTopEnv;
  // }
}
</style>
