<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2999' }"
    class="first-coupon-popup"
  >
    <div class="content" @click="goToTaskNew">
      <img class="bg" :src="levitated_sphere.send_first_charge_big" />
      <div class="close" @click.stop="popup = false"></div>
    </div>
  </van-dialog>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex';
export default {
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.showFCPopup;
      },
      set(value) {
        this.setShowFCPopup(value);
      },
    },

    levitated_sphere() {
      return this.initData?.levitated_sphere ?? {};
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
      showFCPopup: 'user/showFCPopup',
      initData: 'system/initData',
    }),
  },
  methods: {
    ...mapActions({
      SET_FIRST_COUPON_ICON: 'user/SET_FIRST_COUPON_ICON',
    }),
    ...mapMutations({
      setShowFCPopup: 'user/setShowFCPopup',
    }),
    goToTaskNew() {
      this.popup = false;
      if (!this.userInfo.token) {
        this.$nextTick(() => {
          this.toPage('PhoneLogin');
        });
        return false;
      }
      this.toPage('TaskNew');
    },
  },
};
</script>

<style lang="less" scoped>
.first-coupon-popup {
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  z-index: 2999 !important;
  .content {
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
  }
  .bg {
    width: 100%;
    display: block;
    height: 374 * @rem;
    margin: 55 * @rem auto 0;
  }
  .close {
    position: absolute;
    right: 42 * @rem;
    top: 0 * @rem;
    width: 24 * @rem;
    height: 24 * @rem;
    background-image: url(~@/assets/images/close.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
}
</style>
