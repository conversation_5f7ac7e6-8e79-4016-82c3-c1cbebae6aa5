import router from '@/router';
import { mapGetters, mapActions } from 'vuex';
import { BOX_close, platform } from './box.uni';
import { modelName, systemVersion, systemName, appVersion, currentCps, toPage, openKefu } from '@/utils/tools.js'
import { Toast } from 'vant';


export default {
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      unReadMesNumber: 'user/unReadMesNumber',
      appName: 'system/appName',
      defaultAvatar: 'system/defaultAvatar',
      isHw: 'system/isHw',
    }),
    modelName,
    systemVersion,
    systemName,
    appVersion,
    currentCps
  },
  methods: {
    ...mapActions({
      CLICK_EVENT: 'system/CLICK_EVENT',
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    toPage,
    back() {
      if (window.sessionStorage.firstUrl === window.location.href) {
        // 首次进入的页面
        if (['android', 'ios', 'androidBox', 'iosBox'].includes(platform)) {
          // 原生端关闭窗口--包括sdk
          BOX_close();
        } else {
          // 其他-- 返回首页
          toPage('QualitySelect');
        }
      } else {
        router.back();
      }
    },
    openKefu
  },
};
