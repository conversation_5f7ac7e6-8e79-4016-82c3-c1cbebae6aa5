<template>
  <div class="game-quan-item-components">
    <div
      class="game-quan-item"
      @click="toPage('GameGift', { game_id: couponItem.game_id })"
    >
      <div class="game-info">
        <div class="game-item-components">
          <div class="game-icon">
            <img :src="couponItem.titlepic" alt="" />
          </div>
          <div class="game-info-item">
            <div class="game-name">
              <!-- <div class="game-title">{{ couponItem.game_title }}</div> -->
              {{ couponItem.game_title }}
              <span class="game-subtitle" v-if="couponItem.game_subtitle">{{
                couponItem.game_subtitle
              }}</span>
            </div>
            <div class="game-m-title">
              <!-- {{ couponItem.class_name }} -->
              {{ couponItem.title }}
            </div>
            <div class="game-describe" v-if="couponItem.cardbody">
              {{ couponItem.cardbody }}
            </div>
          </div>
        </div>
      </div>
      <div class="game-quan-get">
        <div class="get btn">
          {{ $t('领取') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameQuanItem',
  props: {
    couponItem: {
      type: Object,
      default: () => {},
    },
    isShowTitlePic: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      item: {},
    };
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  .game-quan-item {
    // width: 347 * @rem;
    height: 64 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // background-color: #f5f5f5;
    border-radius: 12 * @rem;
    .game-info {
      //   padding-left: 12 * @rem;
      flex: 1;
      min-width: 0;

      .game-item-components {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        .game-icon {
          position: relative;
          flex: 0 0 64 * @rem;
          width: 64 * @rem;
          height: 64 * @rem;
          border-radius: 14 * @rem;
          background-color: #eeeeee;
        }
        .game-info-item {
          margin-left: 6 * @rem;
          margin-right: 17 * @rem;
          flex: 1;
          min-width: 0;
          // height: 65 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .game-name {
            display: flex;
            align-items: center;
            // justify-content: space-between;
            height: 18 * @rem;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #111111;
            line-height: 18 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .game-title {
              width: 106 * @rem;
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
            .game-subtitle {
              box-sizing: border-box;
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
              border: 1 * @rem solid #e5e1ea;
              color: #888888;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          .game-m-title {
            width: 186 * @rem;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            margin: 7 * @rem 0;
            color: #999999;
            line-height: 15 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            overflow: hidden;
            white-space: nowrap;
          }
          .game-describe {
            width: 190 * @rem;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #666666;
            line-height: 14 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
        }
      }
    }
    .game-quan-get {
      width: 58 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
      .get {
        font-weight: 500;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 58 * @rem;
        height: 28 * @rem;
        background: #5abf73;
        border-radius: 29 * @rem;
        margin-top: 4 * @rem;
      }
    }
  }
}
</style>
