<template>
  <div>
    <div class="jianlou-sell-list">
      <div class="jianlou-item" v-for="(item, index) in list" :key="index">
        <jianlou-item :info="item" :isSell="true"></jianlou-item>
      </div>
    </div>
  </div>
</template>

<script>
import jianlouItem from '../jianlou-item';
export default {
  name: 'JianlouSellList',
  components: {
    jianlouItem,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-sell-list {
  padding: 8 * @rem 18 * @rem;

  .jianlou-item {
    margin-top: 10 * @rem;
    &:nth-of-type(1) {
      margin-top: 0;
    }
  }
}
</style>
