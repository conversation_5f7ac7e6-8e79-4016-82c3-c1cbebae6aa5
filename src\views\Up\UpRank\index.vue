<template>
  <div class="rank-page">
    <!-- 顶部 -->
    <div class="bg">
      <img src="@/assets/images/up/up-rank-bg.png" alt="" />
    </div>
    <div class="top-container">
      <div class="top-bar">
        <status-bar></status-bar>
        <SearchContainer :white="true" />
      </div>
      <div class="tab-container">
        <div class="tab-list">
          <div
            class="tab-item"
            :class="{ on: active == index }"
            v-for="(item, index) in tabList"
            :key="index"
            @click="tapNav(index)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="main">
      <yy-list
        class="rank-container"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
        :tips="$t('暂无数据')"
      >
        <div class="rank-list">
          <div class="rank-item" v-for="(item, index) in rankList" :key="index">
            <div class="rank-num">
              {{ index + 1 }}
            </div>
            <div class="game-info" v-if="item.classid == 40">
              <game-item-3 :gameInfo="item"></game-item-3>
            </div>
            <div v-else>
              <game-item-2 :gameInfo="item"></game-item-2>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiGameTabUpRanking, ApiGameWebRanking } from '@/api/views/game.js';
import SearchContainer from '@/components/search-container';
export default {
  components: {
    SearchContainer,
  },
  data() {
    return {
      rankList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      active: 0,
      tabList: [],
      finished: false,
      empty: false,
      page: 1,
      listRows: 20,
    };
  },
  async created() {
    await this.getTabList();
    this.loadingObj.loading = true;
    await this.getRankList();
  },
  methods: {
    async tapNav(index) {
      if (this.active === index) return;
      this.empty = false;
      this.active = index;
      this.rankList = [];
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getRankList();
    },
    async getTabList() {
      const res = await ApiGameTabUpRanking();
      this.tabList = res.data.tab_list;
    },
    async getRankList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameWebRanking({
        type: this.tabList[this.active].order,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page == 1) {
        this.rankList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.rankList.push(...list);
      this.loadingObj.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getRankList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getRankList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.rank-page {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  .bg {
    position: fixed;
    left: 0;
    top: 0;
    width: 375 * @rem;
    height: 226 * @rem;
    z-index: -1;
    .fixed-center;
  }
  .top-container {
    position: fixed;
    left: 0;
    top: 0;
    width: 375 * @rem;
    height: 226 * @rem;
    z-index: 2;
    .fixed-center;
    img {
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
  .top-bar {
    box-sizing: border-box;
    height: calc(55 * @rem + @safeAreaTop);
    height: calc(55 * @rem + @safeAreaTopEnv);
    width: 100%;
  }

  .tab-container {
    position: relative;
    z-index: 100;
    width: 100%;
    height: 50 * @rem;
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none !important;
    }
    .tab-list {
      display: flex;
      align-items: center;
      height: 50 * @rem;
      padding-left: 21 * @rem;
      .tab-item {
        flex-shrink: 0;
        flex-grow: 0;
        font-size: 16 * @rem;
        color: #fff;
        height: 100%;
        padding: 0 12 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 14 * @rem;
        &:not(:first-of-type) {
          margin-left: 12 * @rem;
        }
        &.on {
          color: #fff;
          font-size: 20 * @rem;
          font-weight: bold;
          position: relative;
          &::after {
            content: '';
            width: 12 * @rem;
            height: 3 * @rem;
            border-radius: 2 * @rem;
            background-color: #fff;
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
          }
        }
      }
    }
  }
  .main {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    margin-top: 200 * @rem;
    overflow-x: auto;
    background-color: #fff;
    background: linear-gradient(180deg, #eff3ff 0%, #ffffff 24%, #ffffff 100%);
    border-radius: 25 * @rem 25 * @rem 0 0;
    .rank-container {
      padding-top: 25 * @rem;
      flex: 1;
      min-height: 0;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 60 * @rem;
        height: 60 * @rem;
        .image-bg('~@/assets/images/up/rank-top-icon.png');
      }
    }
    .rank-list {
      .rank-item {
        box-sizing: border-box;
        margin: 0 auto;
        width: 335 * @rem;
        display: flex;
        align-items: center;
        overflow: hidden;
        .rank-num {
          color: #9a9a9a;
          font-size: 14 * @rem;
          font-weight: 400;
          width: 28 * @rem;
          height: 28 * @rem;
          overflow: hidden;
          flex-shrink: 0;
          flex-grow: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 18 * @rem;
          background-repeat: no-repeat;
          background-size: 28 * @rem 28 * @rem;
          background-position: center center;
        }
        .game-info {
          padding: 10 * @rem 0;
          flex: 1;
          min-width: 0;
        }
        &:nth-of-type(1) {
          .rank-num {
            text-indent: -99999px;
            background-image: url(~@/assets/images/up/rank-1.png);
          }
        }
        &:nth-of-type(2) {
          .rank-num {
            text-indent: -99999px;
            background-image: url(~@/assets/images/up/rank-2.png);
          }
        }
        &:nth-of-type(3) {
          .rank-num {
            text-indent: -99999px;
            background-image: url(~@/assets/images/up/rank-3.png);
          }
        }
      }
    }
  }
}
</style>
