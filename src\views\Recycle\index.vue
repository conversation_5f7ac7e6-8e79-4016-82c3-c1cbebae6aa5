<template>
  <div class="recycle-page">
    <nav-bar-2
      :title="$t('小号回收')"
      bgStyle="transparent"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
      <template #right>
        <div class="recycle-record-btn btn" @click="toPage('RecycleRecord')">
          {{ $t('我的回收记录') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="recycle-bg"></div>
      <div class="introduction">
        <div class="introduction-content">
          <p v-html="text1" :class="{ on: moreOpen }"></p>
          <div
            class="more"
            :class="{ on: moreOpen }"
            @click="moreOpen = !moreOpen"
          ></div>
        </div>
      </div>
      <div class="xiaohao-container">
        <div class="top-bar">
          <div class="title">{{ $t('我的小号') }}</div>
          <div class="svip-btn btn" @click="toPage('Svip')">
            <!-- {{ !this.userInfo.is_svip ? $t('充值svip') : $t('续费svip') }} -->
          </div>
        </div>
        <div class="search-bar" @click="toPage('RecycleSelectXiaohao')">
          <div class="search-icon"></div>
          <div class="search-text">{{ $t('搜索游戏名/小号昵称') }}</div>
        </div>
        <load-more
          v-model="loading"
          :finished="finished"
          @loadMore="loadMore"
          :check="false"
        >
          <div class="xiaohao-list">
            <div
              class="xiaohao-item"
              v-for="(item, index) in xiaohaoList"
              :key="index"
            >
              <xiaohao-item :info="item" @refresh="refresh"></xiaohao-item>
            </div>
          </div>
        </load-more>
      </div>
      <div class="kefu" @click="connectQiYu">
        <div class="dot-red" v-if="unReadMesNumber > 0">{{
          unReadMesNumber > 99 ? '99+' : unReadMesNumber
        }}</div>
      </div>
    </div>
    <recycle-dialog
      v-model="dialogShow"
      :info="info"
      :payInfo="payInfo"
      @refresh="refresh"
    ></recycle-dialog>
  </div>
</template>

<script>
import xiaohaoItem from './components/xiaohao-item';
import {
  ApiXiaohaoAllPayerList,
  ApiXiaohaoPaySum,
} from '@/api/views/xiaohao.js';
import recycleDialog from './components/recycle-dialog/index.vue';
import { ApiUserGetSingleWealth } from '@/api/views/users.js';
export default {
  name: 'Recycle',
  components: {
    xiaohaoItem,
    recycleDialog,
  },
  data() {
    return {
      xiaohaoList: [],
      page: 1,
      listRows: 10,
      text1: '',
      text2: '',
      text3: '',
      loading: false,
      finished: false,
      dialogShow: false,
      payInfo: {},
      info: {},
      navbarOpacity: 0,
      moreOpen: false,
      currentLevel: {},
      limitLevel: 0,
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.getUserLevel();
    let info = this.$route.params.info;
    console.log(info);
    // 判断有没有收到小号信息，有则显示回收弹窗
    if (info) {
      this.info = info;
      const res = await ApiXiaohaoPaySum({
        xhId: this.info.id,
        type: 2,
      });
      this.payInfo = res.data;
      this.dialogShow = true;
    }
    await this.getXiaohaoList();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoAllPayerList({
        page: this.page,
        listRows: this.listRows,
        type: 2,
      });
      let { text1, text2, text3, list } = res.data;
      if (!this.text1) {
        this.text1 = text1.replace(/\n/g, '<br>');
      }
      if (!this.text2) {
        this.text2 = text2;
      }
      if (!this.text3) {
        this.text3 = text3;
      }
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
      }
      this.xiaohaoList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async refresh() {
      await this.getXiaohaoList();
    },
    async loadMore() {
      await this.getXiaohaoList(2);
      this.loading = false;
    },
    async getUserLevel() {
      const res = await ApiUserGetSingleWealth();
      this.currentLevel = res.data.level;
      this.limitLevel = res.data.level_limit;
    },
    // 打开客服
    connectQiYu() {
      // 财富等级未达标
      if (this.currentLevel.level_id >= this.limitLevel) {
        this.openKefu({ is_zx: 1 });
      } else {
        this.openKefu();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.recycle-page {
  .recycle-record-btn {
    font-size: 14 * @rem;
    color: #000000;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main {
    .recycle-bg {
      width: 100%;
      height: 225 * @rem;
      .image-bg('~@/assets/images/recycle/recycle-bg.png');
    }
    .step-container {
      position: relative;
      .step-line {
        width: 270 * @rem;
        background-color: #85b97e;
        height: 1 * @rem;
        position: absolute;
        top: 94 * @rem;
        left: 50%;
        transform: translateX(-50%);
      }
      .step-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 23 * @rem;
        position: relative;
        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .step-icon {
            width: 51 * @rem;
            height: 51 * @rem;
          }
          .step-num {
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            color: #fff;
            font-weight: bold;
            background-color: #47a83a;
            margin-top: 10 * @rem;
          }
          .step-title {
            font-size: 14 * @rem;
            color: #47a83a;
            margin-top: 10 * @rem;
          }
        }
      }
    }
    .introduction {
      background-color: #f5f5f6;
      padding: 0 18 * @rem 14 * @rem;
      .introduction-content {
        box-sizing: border-box;
        width: 339 * @rem;
        padding: 14 * @rem 17 * @rem 0;
        background: #ffffff;
        border-radius: 12 * @rem;
        position: relative;
        top: -15 * @rem;
        p {
          font-size: 14 * @rem;
          color: #000000;
          line-height: 22 * @rem;
          height: 176 * @rem;
          overflow: hidden;
          margin-bottom: 4 * @rem;
          &.on {
            height: auto;
          }
        }
      }
      .more {
        width: 100%;
        height: 30 * @rem;
        background: url(~@/assets/images/games/bottom-arrow-green.png) center
          center no-repeat;
        background-size: 16 * @rem 9 * @rem;
        margin-left: 4 * @rem;
        transition: 0.3s;
        &.on {
          transform: rotateZ(180deg);
        }
      }
    }
    .xiaohao-container {
      padding: 0 18 * @rem;
      .top-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15 * @rem 0;
        .title {
          font-size: 18 * @rem;
          line-height: 1;
          color: #000000;
          font-weight: 600;
        }
        .svip-btn {
          width: 117 * @rem;
          height: 24 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: url(~@/assets/images/recycle/svip-icon.png) no-repeat;
          background-size: 117 * @rem 24 * @rem;
          // background: @themeBg;
          // border-radius: 12 * @rem;
          font-size: 12 * @rem;
          // color: #ffffff;
          font-weight: 600;
        }
      }
      .search-bar {
        display: flex;
        align-items: center;
        height: 38 * @rem;
        border: 1 * @rem solid #ebebeb;
        border-radius: 6 * @rem;
        padding: 0 15 * @rem;
        .search-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          background: url(~@/assets/images/recycle/search-icon.png) center
            center no-repeat;
          background-size: 14 * @rem 14 * @rem;
        }
        .search-text {
          font-size: 15 * @rem;
          color: #9a9a9a;
          margin-left: 7 * @rem;
        }
      }
      .xiaohao-list {
        .xiaohao-item {
          margin-top: 20 * @rem;
        }
      }
    }
    .kefu {
      display: block;
      width: 58px;
      height: 56px;
      position: fixed;
      bottom: 110px;
      right: 18px;
      background: url(~@/assets/images/kefu-icon.png) no-repeat;
      background-size: 58px 56px;

      .dot-red {
        display: block;
        padding: 0 4 * @rem;
        height: 11 * @rem;
        background: #f44040;
        border-radius: 6 * @rem;
        border: 1px solid #ffffff;
        font-weight: 600;
        font-size: 9 * @rem;
        color: #ffffff;
        line-height: 11 * @rem;
        text-align: center;
        position: absolute;
        top: 0;
        left: 28 * @rem;
        white-space: nowrap;
      }
    }
  }
}
</style>
