<template>
  <div class="kefu-page">
    <nav-bar-2
      :border="true"
      :title="$t('客服中心')"
      :azShow="true"
    ></nav-bar-2>
    <section class="other">
      <div class="kuaijiefuwu">快捷服务</div>
      <div class="list">
        <div
          v-for="(item, index) in questionList"
          :key="index"
          @click="toDetail(item.id)"
          class="item"
        >
          <div class="icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="text">{{ item.title }}</div>
        </div>
        <div
          @click="toUploadPage()"
          class="item"
          v-if="platform == 'android' && !isSdk"
        >
          <div class="icon icon8"></div>
          <div class="text">{{ $t('上传资源') }}</div>
        </div>
        <div class="item-last"></div>
        <div class="item-last"></div>
      </div>
    </section>

    <section class="agent-kefu" v-if="agentKefu">
      <!-- 客服qq -->
      <div class="kefu-item" v-if="agentKefu.kefu_qq">
        <div
          class="line"
          @click="clickAgentKefu(agentKefu.kefu_qq, agentKefu.kefu_qq_url)"
        >
          <img
            class="kefu-icon"
            src="@/assets/images/kefu/kefu-qq-icon.png"
            alt=""
          />
          <div class="kefu-title">联系QQ客服</div>
          <div class="right-icon"></div>
        </div>
        <div class="num"
          >QQ：<span>{{ agentKefu.kefu_qq }}</span></div
        >
      </div>
      <!-- qq群 -->
      <div class="kefu-item" v-if="agentKefu.qq_qun">
        <div class="line" @click="clickAgentKefu(agentKefu.qq_qun)">
          <img
            class="kefu-icon"
            src="@/assets/images/kefu/qq-qun-icon.png"
            alt=""
          />
          <div class="kefu-title">加入QQ群聊</div>
          <div class="right-icon"></div>
        </div>
        <div class="num"
          >QQ群：<span>{{ agentKefu.qq_qun }}</span></div
        >
      </div>
    </section>

    <section class="zhuanye-container">
      <div class="zhuanye-list">
        <!-- 当财富等级达标后，该入口展示于第一位，普通在线客服展示于第二位 -->
        <div
          @click="connectVipQiYu()"
          class="zhuanye-item"
          v-if="currentLevel.level_id >= limitLevel"
        >
          <img
            class="zhuanye-icon"
            src="@/assets/images/kefu/kefu-vip.png"
            alt=""
          />
          <div
            class="dot-red"
            v-if="currentLevel.level_id >= limitLevel && unReadMesNumber > 0"
            >{{ unReadMesNumber > 99 ? '99+' : unReadMesNumber }}</div
          >
          <div class="zhuanye-text">尊享客服</div>
        </div>
        <div @click="connectQiYu()" class="zhuanye-item">
          <img
            class="zhuanye-icon"
            src="@/assets/images/kefu/kefu-online.png"
            alt=""
          />
          <div
            class="dot-red"
            v-if="
              currentLevel.level_id < this.limitLevel && unReadMesNumber > 0
            "
            >{{ unReadMesNumber > 99 ? '99+' : unReadMesNumber }}</div
          >
          <div class="zhuanye-text">{{ $t('在线客服') }}</div>
        </div>
        <div
          @click="connectVipQiYu()"
          class="zhuanye-item"
          v-if="currentLevel.level_id < limitLevel"
        >
          <img
            class="zhuanye-icon"
            src="@/assets/images/kefu/kefu-vip.png"
            alt=""
          />
          <div class="zhuanye-text">尊享客服</div>
        </div>
        <div @click="feedback()" class="zhuanye-item feedback">
          <img
            class="zhuanye-icon"
            src="@/assets/images/kefu/kefu-feedback.png"
            alt=""
          />
          <div class="zhuanye-text">{{ $t('投诉与建议') }}</div>
        </div>
      </div>
      <Zhuanyekefu />
    </section>
    <section class="hot-question-container">
      <div class="hot-question-title">常见问题</div>
      <div class="question-list">
        <div
          class="question-item"
          v-for="(item, index) in hotQuestionList"
          :key="index"
          @click="toHotQuestionDetail(item.id)"
        >
          <div class="question-text">{{ item.title }}</div>
          <div class="right-icon"></div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import {
  ApiFeedGetTopQuestions,
  ApiFeedGetQuestionType,
} from '@/api/views/feedback.js';
import { ApiUserGetSingleWealth } from '@/api/views/users.js';
import {
  platform,
  BOX_login,
  BOX_openInNewWindow,
  BOX_openInNewNavWindow,
  BOX_openInBrowser,
  BOX_checkInstall,
  BOX_getKefuUnreadCount,
  isSdk,
} from '@/utils/box.uni.js';
import { mapGetters, mapMutations } from 'vuex';
import Zhuanyekefu from '@/components/zhuanye-kefu';

import { ApiCommonGetAgentQqInfo } from '@/api/views/system.js';

import useConfirmDialog from '@/components/yy-confirm-dialog/index.js';

export default {
  name: 'Kefu',
  data() {
    return {
      platform,
      isSdk,
      gongzhonghao: this.$t('3733游戏'),
      questionList: [],
      hotQuestionList: [],

      currentLevel: {},
      limitLevel: 0,

      agentKefu: null, //渠道客服信息

      timer: null, //定时获取客服未读消息
    };
  },
  computed: {
    ...mapGetters({
      kefuQQNumber: 'system/kefuQQNumber',
      kefuQQLink: 'system/kefuQQLink',
    }),
    noNeedWykefu() {
      return !!this.kefuQQNumber && !this.kefuQQLink;
    },
  },
  async created() {
    if (this.platform == 'android') {
      document.title = this.$t('客服中心');
      let result = BOX_getKefuUnreadCount();
      this.setUnReadMesNumber(result);
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.timer = setInterval(() => {
        let result = BOX_getKefuUnreadCount();
        this.setUnReadMesNumber(result);
      }, 10000);
    }
    await this.getUserLevel();
    await this.getQuestionType();
    await this.getAgentKefu();
    await this.getHotQuestionList();
  },
  methods: {
    ...mapMutations({
      setUnReadMesNumber: 'user/setUnReadMesNumber',
    }),
    async getAgentKefu() {
      const res = await ApiCommonGetAgentQqInfo();
      this.agentKefu = res.data;
    },
    clickAgentKefu(copyText, url) {
      if (isSdk) {
        this.$copyText(copyText)
          .then(() => {
            this.$toast('已复制，请打开QQ进行添加');
          })
          .catch(() => {
            this.$toast('复制失败，请手动复制');
          });
        return;
      }
      if (!BOX_checkInstall('com.tencent.mobileqq')) {
        useConfirmDialog({
          title: '温馨提示',
          desc: '请先安装QQ软件',
          confirmText: '确认',
        });
        return;
      }
      if (url) {
        // 有qq客服链接的情况下直接打开链接
        this.$toast('正在唤起QQ，请稍等...');
        BOX_openInBrowser({ h5_url: url }, { url: url });
      } else {
        // ios马甲包打开不了生成的链接，复制QQ号;
        if (platform == 'iosBox') {
          this.$copyText(copyText)
            .then(() => {
              this.$toast('账号已复制，请打开QQ进行添加');
              setTimeout(() => {
                window.location.href = 'mqq://'; // ios马甲包打不开
              }, 1000);
            })
            .catch(() => {
              this.$toast('复制失败，请手动复制');
            });
          return false;
        }

        // 没有QQ链接的情况不复制打开QQ了，直接拿QQ号转成能打开的链接
        let formatUrl = `mqq://im/chat?chat_type=wpa&uin=${copyText}&version=1&src_type=web`;
        BOX_openInBrowser({ h5_url: formatUrl }, { url: formatUrl });
      }
    },
    async getHotQuestionList() {
      const res = await ApiFeedGetTopQuestions();
      this.hotQuestionList = res.data;
    },
    async getQuestionType() {
      const res = await ApiFeedGetQuestionType();
      this.questionList = res.data;
    },
    async getUserLevel() {
      const res = await ApiUserGetSingleWealth();
      this.currentLevel = res.data.level;
      this.limitLevel = res.data.level_limit;
    },

    toHotQuestionDetail(id) {
      BOX_openInNewWindow(
        { name: 'QuestionDetail', params: { id: id } },
        {
          url: `https://${this.$h5Page.env}game.3733.com/#/question_detail/${id}`,
        },
      );
    },
    // 跳转问题详情
    toDetail(id) {
      BOX_openInNewWindow(
        { name: 'KefuQA', params: { id: id } },
        { url: `https://${this.$h5Page.env}game.3733.com/#/kefu_qa/${id}` },
      );
    },
    connectQiYu() {
      this.openKefu();
    },
    // 打开尊享客服
    connectVipQiYu() {
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }

      // 财富等级未达标
      if (this.currentLevel.level_id < this.limitLevel) {
        this.$dialog
          .confirm({
            title: '温馨提示',
            message: `当前财富等级为${this.currentLevel.level_id}，财富等级达到${this.limitLevel}后，尊享专属在线客服`,
            confirmButtonText: '前往查看',
          })
          .then(() => {
            BOX_openInNewWindow(
              { name: 'PayHelp' },
              {
                url: `https://${this.$h5Page.env}game.3733.com/#/pay_help`,
              },
            );
          });
        return false;
      }

      // 财富等级达标，此处需修改为调用尊享客服
      this.openKefu({ is_zx: 1 });
    },
    // 跳转反馈页面
    feedback() {
      let url = `https://${this.$h5Page.env}game.3733.com/#/feedback`;
      BOX_openInNewWindow({ name: 'Feedback' }, { url });
    },
    // 跳转上传资源页面（客户端）
    toUploadPage() {
      BOX_openInNewNavWindow(
        {},
        { url: `https://${this.$h5Page.env}api.a3733.com/h5/apk/index` },
      );
    },
  },
  components: {
    Zhuanyekefu,
  },
};
</script>

<style lang="less" scoped>
.kefu-page {
  box-sizing: border-box;
  background: #ffffff;
  padding-bottom: 30 * @rem;
  section {
    box-sizing: border-box;
    width: 339 * @rem;
    box-shadow: 0 * @rem 0 * @rem 8 * @rem 0 * @rem rgba(0, 62, 220, 0.1);
    border-radius: 7 * @rem;
    margin: 10 * @rem auto 0;
    &:not(:first-of-type) {
      margin-top: 20 * @rem;
    }
  }
  .other {
    padding: 5 * @rem 8 * @rem 15 * @rem;
    .kuaijiefuwu {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
      margin-left: 10 * @rem;
      margin-top: 10 * @rem;
    }
    .list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .item-last {
        flex: 0 0 25%;
      }
      .item {
        flex: 0 0 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 25 * @rem;
        overflow: hidden;
        .icon {
          display: block;
          width: 40 * @rem;
          height: 40 * @rem;
          &.icon1 {
            .image-bg('~@/assets/images/kefu/kefu_icon1.png');
          }
          &.icon2 {
            .image-bg('~@/assets/images/kefu/kefu_icon2.png');
          }
          &.icon3 {
            .image-bg('~@/assets/images/kefu/kefu_icon3.png');
          }
          &.icon4 {
            .image-bg('~@/assets/images/kefu/kefu_icon4.png');
          }
          &.icon5 {
            .image-bg('~@/assets/images/kefu/kefu_icon5.png');
          }
          &.icon6 {
            .image-bg('~@/assets/images/kefu/kefu_icon6.png');
          }
          &.icon7 {
            .image-bg('~@/assets/images/kefu/kefu_icon7.png');
          }
          &.icon8 {
            .image-bg('~@/assets/images/kefu/kefu_icon8-green.png');
          }
        }
        .text {
          margin-top: 8 * @rem;
          font-size: 11 * @rem;
          color: #000000;
          line-height: 15 * @rem;
          white-space: nowrap;
        }
      }
    }
  }
  .bottom-button {
    box-sizing: border-box;
    width: 339 * @rem;
    height: 42 * @rem;
    line-height: 42 * @rem;
    text-align: center;
    color: #fff;
    background: @themeBg;
    font-size: 15 * @rem;
    font-family: PingFangHK-Medium, PingFangHK;
    font-weight: 600;
    border-radius: 4 * @rem;
    margin: 15 * @rem auto 0;
  }
  .hot-question-container {
    padding: 0 18 * @rem;
    margin-top: 35 * @rem;
    .hot-question-title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .question-list {
      margin-top: 10 * @rem;
      .question-item {
        height: 50 * @rem;
        display: flex;
        align-items: center;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        .question-text {
          flex: 1;
          min-width: 0;
          font-size: 14 * @rem;
          color: #000000;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .right-icon {
          width: 6 * @rem;
          height: 11 * @rem;
          .image-bg('~@/assets/images/kefu/right-icon.png');
        }
      }
    }
  }
  .agent-kefu {
    box-sizing: border-box;
    padding: 16 * @rem 10 * @rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 9 * @rem;
    .kefu-item {
      box-sizing: border-box;
      border: 1 * @rem solid #e9f1fc;
      height: 70 * @rem;
      border-radius: 8 * @rem;
      padding: 13 * @rem 11 * @rem 11 * @rem 14 * @rem;
      .line {
        display: flex;
        align-items: center;
        .kefu-icon {
          width: 24 * @rem;
          height: 24 * @rem;
        }
        .kefu-title {
          flex: 1;
          min-width: 0;
          margin: 0 4 * @rem;
          font-size: 13 * @rem;
          color: #333333;
          font-weight: 600;
        }
        .right-icon {
          width: 6 * @rem;
          height: 10 * @rem;
          background: url(~@/assets/images/kefu/kefu-right-icon.png) center
            center no-repeat;
          background-size: 6 * @rem 10 * @rem;
        }
      }
      .num {
        font-size: 12 * @rem;
        color: #666666;
        line-height: 16 * @rem;
        margin-top: 7 * @rem;
        span {
          user-select: auto;
          font-size: 13 * @rem;
          color: #333333;
          line-height: 16 * @rem;
        }
      }
    }
  }
  .zhuanye-container {
    .zhuanye-list {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding-top: 24 * @rem;
      padding-bottom: 20 * @rem;
      border-bottom: 0.5 * @rem solid #efefef;
      margin: 0 13 * @rem;
      .zhuanye-item {
        position: relative;
        .zhuanye-icon {
          width: 40 * @rem;
          height: 40 * @rem;
          margin: 0 auto;
        }
        .zhuanye-text {
          font-size: 12 * @rem;
          color: #333333;
          text-align: center;
          line-height: 15 * @rem;
          margin-top: 11 * @rem;
        }
        .dot-red {
          display: block;
          padding: 0 4 * @rem;
          height: 11 * @rem;
          background: #f44040;
          border-radius: 6 * @rem;
          border: 1px solid #ffffff;
          font-weight: 600;
          font-size: 10 * @rem;
          color: #ffffff;
          line-height: 11 * @rem;
          text-align: center;
          position: absolute;
          top: 1 * @rem;
          left: 27 * @rem;
          white-space: nowrap;
        }
      }
    }
    /deep/ .zhuanyekefu {
      box-shadow: unset;
      .content {
        box-shadow: unset;
        border: 0;
      }
    }
  }
  .hot-question-container {
    padding-top: 14 * @rem;
  }
}
</style>
