<template>
  <!-- 隐私政策未同意弹窗 -->
  <div>
    <van-dialog
      v-model="show"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog"
      :show-confirm-button="false"
    >
      <div>
        <div class="title">服务协议及隐私保护</div>
        <div class="desc">
          为了更好地保障您的合法权益，请您阅读并同意以下协议<span
            @click="handleLink($h5Page.yonghuxieyi, '用户协议')"
            >《用户协议》</span
          >和<span @click="handleLink($h5Page.yinsizhengce, '隐私政策')"
            >《隐私协议》</span
          >
        </div>
        <div class="btn confirm-btn" @click="confirm">同意</div>
        <div class="cancel-btn" @click="cancel">不同意</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: false,
    };
  },
  methods: {
    cancel() {
      this.show = false;
    },
    confirm() {
      this.closePopup();
    },
    handleLink(link, title) {
      this.show = false;
      this.toPage('Iframe', {
        title: title,
        url: link,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.dialog {
  box-sizing: border-box;
  padding: 25 * @rem 22 * @rem;
  width: 311 * @rem;
  .title {
    font-size: 15 * @rem;
    color: #111111;
    line-height: 21 * @rem;
    text-align: center;
    font-weight: 600;
  }
  .desc {
    font-size: 13 * @rem;
    color: #757575;
    line-height: 21 * @rem;
    margin-top: 34 * @rem;
    span {
      color: @themeColor;
    }
  }
  .confirm-btn {
    width: 267 * @rem;
    height: 44 * @rem;
    background: @themeBg;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8 * @rem;
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 600;
    margin: 23 * @rem auto 0;
  }
  .cancel-btn {
    text-align: center;
    font-size: 15 * @rem;
    color: #666666;
    line-height: 21 * @rem;
    margin-top: 12 * @rem;
  }
}
</style>
