import { request } from '../index';
import store from '@/store'; // 引入Vuex store
import { loadSDK } from '@/utils/tools.js'
// 统一云挂机接口 - 根据cloud_type选择不同平台
// cloud_type: '0' - 百度云 (cloud_mounted), '2' - 臂云 (ecloud_by)
let cloudType = ''; // 默认为臂云

// 获取当前云平台基础路径
const getCloudBasePath = () => {
    if (store && store.state.system && store.state.system.cloud_type !== undefined) {
        cloudType = store.state.system.cloud_type;
    } else {
        cloudType = '0';
    }
    const cloudTypeStr = String(cloudType);
    // 设置cloud_type时动态加载对应SDK
    loadSDK(cloudTypeStr);
    return cloudTypeStr === '2' ? 'ecloud_by' : 'cloud_mounted';
};

/**
 * 云挂机 - 云挂机FAQ
 */
export function ApiCloudFaq(params = {}) {
    return request(`/api/${getCloudBasePath()}/faq`, params);
}

/**
 * 云挂机 - 免费试用
 */
export function ApiCloudBuyFree(params = {}) {
    return request(`/api/${getCloudBasePath()}/buyFree`, params);
}

/**
 * 云挂机 - 入口页面
 *  @param {keyword} 关键字
 */
export function ApiCloudList(params = {}) {
    return request(`/api/${getCloudBasePath()}/list`, params);
}

/**
 * 云挂机 - 启动加载页
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudLoadPage(params = {}) {
    return request(`/api/${getCloudBasePath()}/loadPage`, params);
}

/**
 * 云挂机 - 应用启停
 * @param {equip_id} 设备ID
 * @param {game_package_name} 包名
 * @param {operate_type} 非必填，动作 start 启动 stop 关闭
 */
export function ApiCloudAppOperate(params = {}) {
    return request(`/api/${getCloudBasePath()}/appOperate`, params);
}

/**
 * 云挂机 - 更新设备名称
 * @param {title} 标题
 * @param {equip_id} 设备ID
 */
export function ApiCloudUpdateName(params = {}) {
    return request(`/api/${getCloudBasePath()}/updateName`, params);
}

/**
 * 云挂机 - 续费设备
 * @param {num} 数量
 * @param {day} 天数
 * @param {amount} 价格
 * @param {payWay} 支付方式
 * @param {equip_id} 设备ID
 */
export function ApiCloudBuyOrderAgain(params = {}) {
    return request(`/api/${getCloudBasePath()}/buyOrderAgain`, params);
}

/**
 * 云手机 - 购买规则
 */
export function ApiCloudBuyNotice(params = {}) {
    return request(`/api/${getCloudBasePath()}/buyNotice`, params);
}

/**
 * 云挂机 - 设备列表
 */
export function ApiCloudEquipList(params = {}) {
    return request(`/api/${getCloudBasePath()}/equipList`, params);
}

/**
 * 云挂机 - 设备绑定游戏
 * @param {app_id} 游戏ID
 * @param {equip_id} 设备ID
 */
export function ApiCloudBindGame(params = {}) {
    return request(`/api/${getCloudBasePath()}/bindGame`, params);
}

/**
 * 云挂机 - 设备详情
 * @param {equip_id} 设备ID
 */
export function ApiCloudEquipDetail(params = {}) {
    return request(`/api/${getCloudBasePath()}/equipDetail`, params);
}

/**
 * 云挂机 - 购买,续费设备进入页
 * @param {equip_id} 设备ID
 */
export function ApiCloudBugEquip(params = {}) {
    return request(`/api/${getCloudBasePath()}/bugEquip`, params);
}

/**
 * 云挂机 - 购买创建订单
 * @param {num} 数量
 * @param {day} 天数
 * @param {amount} 价格
 * @param {payWay} 支付方式 上面3个参数为0时,免费2小时
 */
export function ApiCloudBuyOrder(params = {}) {
    return request(`/api/${getCloudBasePath()}/buyOrder`, params);
}

/**
 * 云挂机 - 退出挂机
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudOutLine(params = {}) {
    return request(`/api/${getCloudBasePath()}/outLine`, params);
}

/**
 * 云挂机 - 选择云挂机游戏
 * @param {is_self} 1=查询自己挂机游戏 不填=全部游戏
 * @param {keyword} 搜索关键词
 * @param {page} 页码 默认1
 * @param {list_rows} 页数 默认10
 */
export function ApiCloudChooseGame(params = {}) {
    return request(`/api/${getCloudBasePath()}/chooseGame`, params);
}

/**
 * 云挂机 - 更新设备游戏
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudUpdateGameVer(params = {}) {
    return request(`/api/${getCloudBasePath()}/updateGameVer`, params);
}

/**
 * 云挂机 - 刷新点击获取截图
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudScreenshot(params = {}) {
    return request(`/api/${getCloudBasePath()}/screenshot`, params);
}

/**
 * 云挂机 - 云挂机设备库存容量
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudEquipPercent(params = {}) {
    return request(`/api/${getCloudBasePath()}/equipPercent`, params);
}

/**
 * 云挂机 - 云挂机卸载游戏
 * @param {equip_id} 设备ID
 * @param {app_id} 游戏ID
 */
export function ApiCloudUninstallApp(params = {}) {
    return request(`/api/${getCloudBasePath()}/uninstallApp`, params);
}

/**
 * 云挂机 - 订单查询状态
 */
export function ApiCloudCharge(params = {}) {
    return request(`/api/${getCloudBasePath()}/charge`, params);
}

/**
 * 云玩 - 心跳
 */
export function ApiEcloudByJumpLog(params = {}) {
    return request(`/api/ecloud_by/jumpLog`, params);
}

/**
 * 云玩 - 初始化加载
 * @param {app_id} 游戏ID
 * @param {times} 次数
 */
export function ApiEcloudByLoadYw(params = {}) {
    return request(`/api/ecloud_by/loadYw`, params);
}

/**
 * 云玩 - 获取设备信息
 */
export function ApiEcloudByGetYwInstance(params = {}) {
    return request(`/api/ecloud_by/getYwInstance`, params);
} 