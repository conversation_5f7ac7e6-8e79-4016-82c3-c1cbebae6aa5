<template>
  <div class="category-page">
    <div class="fixed">
      <status-bar
        bgColor="linear-gradient(to bottom, rgba(225, 225, 225, 1) 0%, rgba(255, 255, 255, 1) 100%)"
      ></status-bar>
      <div class="search-bar" @click="goToSearch">
        <div class="icon"></div>
        <div
          class="search-text"
          v-if="!initData.default_search || !initData.default_search.length"
          >{{ $t('搜一搜') }}</div
        >
        <div class="search-text-box" v-else>
          <swiper
            v-if="
              initData.default_search &&
              initData.default_search.length &&
              isKeep &&
              initData.default_search.length > 1
            "
            class="hot-center"
            :options="swiperOptionsHot"
            :auto-update="false"
            ref="mySearchSwiper2"
          >
            <swiper-slide
              class="hot-box"
              v-for="(item, index) in initData.default_search"
              :key="index"
            >
              <div class="search-text">{{ item.keyword }}</div>
              <img v-if="item.keyword && item.img" :src="item.img" alt="" />
            </swiper-slide>
          </swiper>
          <template v-if="initData.default_search.length == 1">
            <div
              class="hot-box"
              v-for="(item, index) in initData.default_search"
              :key="index"
              @click="clickGoToSearch(item, index)"
            >
              <div class="search-text">{{ item.keyword }}</div>
              <img v-if="item.keyword && item.img" :src="item.img" alt="" />
            </div>
          </template>
        </div>
      </div>
      <div class="top-fixed">
        <swiper
          class="class-list"
          :options="swiperOptions"
          :auto-update="true"
          style="width: 100%; margin: 0 auto"
        >
          <swiper-slide
            class="class-item btn"
            v-for="(item, index) in classList"
            :class="{ active: item.id == classId }"
            :key="index"
          >
            {{ item.title }}
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="up" v-if="classId == 40">
      <!-- <div class="up-category">
        <swiper
          class="up-category-list"
          :options="swiperOptions"
          :auto-update="true"
          style="width: 100%; margin: 0 auto"
          v-if="up_category.list.length > 0"
        >
          <swiper-slide
            class="item btn"
            v-for="item in up_category.list"
            :class="{ current: up_category.current_id === item.id }"
            :key="item.id"
          >
            {{ item.title }}
          </swiper-slide>
        </swiper>
      </div> -->
      <!-- <div class="tab-list">
        <div class="left">
          <van-tabs v-model:active="cate_value">
            <van-tab
              v-for="(item, index) in slidebarList"
              :key="index"
              :title="item.title"
            ></van-tab>
          </van-tabs>
        </div>
      </div> -->
      <main v-if="slidebarList.length > 0" class="list">
        <van-sidebar class="left-container" v-model="up_category.current_id">
          <van-sidebar-item
            v-for="(item, index) in up_category.list"
            :key="index"
            :title="item.title"
            @click="sidebarItemClick(item)"
          />
        </van-sidebar>
        <div class="container">
          <!-- 取消了全部、最新、最热、，默认2（最热） -->
          <categoryList2
            :classId="classId"
            :info="slidebarList[2]"
            :up_category="up_category.list[up_category.current_id].id"
          ></categoryList2>
        </div>
      </main>
    </div>
    <template v-else-if="classId == 140">
      <main>
        <simulator-zone ref="simulatorZone"></simulator-zone>
      </main>
    </template>
    <template v-else>
      <main v-if="slidebarList.length > 2">
        <van-sidebar class="left-container" v-model="cate_value">
          <van-sidebar-item
            v-for="(item, index) in slidebarList"
            :key="index"
            :title="item.title"
            @click="sidebarItemClick(item)"
          />
        </van-sidebar>
        <div class="container">
          <template v-if="slidebarList[0].id == -1 && cate_value == 0">
            <categoryList1 :classId="classId"></categoryList1>
          </template>
          <template v-else>
            <categoryList2
              :classId="classId"
              :info="slidebarList[cate_value]"
            ></categoryList2>
          </template>
        </div>
      </main>
    </template>
  </div>
</template>

<script>
import {
  ApiGameGetCateTheme,
  ApiGameGetGameClassList,
  ApiGameGetUpCate,
} from '@/api/views/game.js';
import categoryList1 from './components/categoryList1';
import categoryList2 from './components/categoryList2';
import { themeColorLess } from '@/common/styles/_variable.less';
import i18n from '@/i18n';
import SimulatorZone from '../../SimulatorZone';
let slidebarDefault = [
  {
    id: -1,
    title: i18n.t('精选'),
  },
];
import { mapGetters, mapMutations } from 'vuex';
export default {
  components: {
    categoryList1,
    categoryList2,
    SimulatorZone,
  },
  data() {
    let that = this;
    return {
      searchHotText: '',
      swiperOptionsHot: {
        direction: 'vertical', // 垂直滚动
        slidesPerView: 1,
        loop: true,
        autoplay: true,
        allowTouchMove: false,
        autoplay: {
          delay: 3000,
        },
        on: {
          slideChange: function (e) {
            setTimeout(() => {
              that.currentSlideIndex = this.realIndex + 1;
              that.searchHotText = that.initData.default_search[this.realIndex];
            }, 0);
          },
          init() {
            this.slideTo(that.currentSlideIndex, 0, true);
          },
        },
      },
      themeColorLess,
      cate_value: 0,
      slidebarList: [...slidebarDefault],
      classList: [],
      classId: '',
      btInfo: null, // 搜索页跳转过来时携带的参数
      up_category: {
        //up分类页的游戏分类
        current_id: 0, //当前分类id
        list: [], //分类列表
      },
      swiper_click_flag: false,
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        slideToClickedSlide: true,
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        on: {
          tap: function () {
            if (that.swiper_click_flag) {
              return false;
            }
            that.swiper_click_flag = true;
            setTimeout(() => {
              that.CLICK_EVENT(that.classList[this.clickedIndex]?.click_id);
              that.classChange(that.classList[this.clickedIndex]);
              that.swiper_click_flag = false;
            }, 20);
          },
        },
      },
      isKeep: false, //控制页面keepAlive后轮播不播放问题
      currentSlideIndex: 0, // 保存当前swiperOptionsHot的索引
    };
  },
  computed: {
    classTitle() {
      return (
        this.classList.find(item => {
          return item.id == this.classId;
        })?.title ?? 'BT版'
      );
    },
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      initData: 'system/initData',
      searchCurrentSlideIndex: 'system/searchCurrentSlideIndex',
    }),
  },
  watch: {
    async classId(val) {
      // 获取小分类
      await this.getCateTheme();
      if (this.btInfo == null) {
        if (!this.classId == 40) {
          this.cate_value = 0;
        }
      }
      this.btInfo = null;
    },
  },
  async activated() {
    if (!this.classList.length) {
      // 获取大分类
      await this.getClassList();
      this.classId = this.classList[0].value;
    }
    if (
      this.$route.params.classId != undefined &&
      this.classId != this.$route.params.classId
    ) {
      this.classChange({ id: this.$route.params.classId });
      // this.classId = this.$route.params.classId;
      // this.cate_value = 2;
    }

    // 从其他页面跳转至分类页
    if (this.$route.params.info != undefined) {
      // 从Up资源库跳转至分类页
      if (this.$route.params.info.up_category_id) {
        this.up_category.current_id = this.$route.params.info.up_category_id;
      } else {
        this.btInfo = this.$route.params.info;
        // 搜索热门分类跳转过来
        this.classId = this.classList[0].value;
        await this.getCateTheme();
        let index = this.slidebarList.findIndex(item => {
          return item.id == this.$route.params.info.id;
        });
        if (index != -1) {
          this.cate_value = index;
        }
        this.$nextTick(() => {
          setTimeout(() => {
            document
              .querySelector('.van-sidebar-item--select')
              .scrollIntoView();
          }, 0);
        });
      }
    }
    if (this.classId == 140) {
      this.$refs.simulatorZone?.updateData();
    }
    this.isKeep = true;
    this.currentSlideIndex = this.searchCurrentSlideIndex;
  },
  methods: {
    clickGoToSearch(item, index) {
      this.currentSlideIndex = index;
      this.searchHotText = item;
    },
    goToSearch() {
      window.scrollTo(0, 0);
      this.setSearchHotText(this.searchHotText);
      this.CLICK_EVENT('M6_SEARCH');
      this.toPage('Search');
    },
    ...mapMutations({
      setSearchHotText: 'system/setSearchHotText',
      setSearchCurrentSlideIndex: 'system/setSearchCurrentSlideIndex',
    }),
    sidebarItemClick(item) {
      if (item.id == -1) {
        this.CLICK_EVENT(
          this.classList.find(item => {
            return item.id == this.classId;
          })?.jx_click_id,
        );
        return;
      }
      this.CLICK_EVENT(item.click_id);
    },
    // 获取classid的分类列表
    async getClassList() {
      const res = await ApiGameGetGameClassList();
      this.classList = res.data.class_list.map(item => {
        return {
          ...item,
          text: item.title,
          value: item.id,
        };
      });
    },
    // 获取该classid下的主题分类
    async getCateTheme() {
      //破解默认，需要先改好防列表多次拉取
      if (this.classId == 40) {
        this.cate_value = 0;
        await this.getUpCategory();
      }
      const res = await ApiGameGetCateTheme({ classId: this.classId });
      // if (this.classId != 1) {
      // this.slidebarList = res.data.cate_theme_list;
      // } else {
      this.slidebarList = [...slidebarDefault, ...res.data.cate_theme_list];
      // }
    },
    async getUpCategory() {
      const res = await ApiGameGetUpCate();
      this.up_category.list =
        [{ title: '全部', id: 0 }, ...res.data.list] || [];
    },
    async classChange(item) {
      if (this.classId == item.id) {
        return false;
      }
      this.classId = item.id;
      this.slidebarList = [];
      // 如果是破解
      // if(this.classId == 40) {
      // await this.getCateTheme();
      // }
      // 否则
      // else {
      // this.$refs.menuItem.toggle();
      // }
    },
    // 修改UP类型的分类
    changeUpTheme(id) {
      this.up_category.current_id = this.up_category.current_id == id ? -1 : id;
    },
  },
  created() {
    this.isKeep = true;
  },
  deactivated() {
    this.isKeep = false;
    if (this.$refs.mySearchSwiper2) {
      // 离开前保存当前的索引
      this.setSearchCurrentSlideIndex(this.currentSlideIndex);
    }
  },
};
</script>

<style lang="less" scoped>
.category-page {
  padding-bottom: 0;
  height: 100vh;
  width: 100%;
  background: #f5f5f6;
}

.fixed {
  width: 100%;
  padding: 0 0 2 * @rem;
  background: #fff;
  z-index: 2000;
  overflow: hidden;
  position: fixed;
  .fixed-center;
}
.top-fixed {
  width: 100%;
  height: 44 * @rem;
  position: relative;
  .class-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    height: 44 * @rem;
    padding: 0 5 * @rem;
    .class-item {
      width: auto;
      padding: 0 15 * @rem;
      display: flex;
      align-items: center;
      font-size: 16 * @rem;
      color: #111111;
      font-weight: 400;
      &.active {
        position: relative;
        font-weight: bolder;
        font-size: 18 * @rem;
        &::after {
          position: absolute;
          bottom: 3 * @rem;
          left: 50%;
          transform: translate(-50%, 0);
          content: '';
          width: 14 * @rem;
          height: 4 * @rem;
          border-radius: 16 * @rem;
          background: @themeColor;
        }
      }
    }
  }
}
.search-bar {
  box-sizing: border-box;
  padding: 0 16 * @rem;
  width: 331 * @rem;
  height: 35 * @rem;
  border-radius: 18 * @rem;
  background-color: #f6f6f6;
  margin: 10 * @rem auto 10 * @rem;
  display: flex;
  align-items: center;
  .icon {
    width: 14 * @rem;
    height: 14 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 14 * @rem 14 * @rem;
  }
  .hot-box {
    display: flex;
    align-items: center;
    .search-text {
      font-size: 14 * @rem;
      color: #9a9a9a;
      margin-left: 7 * @rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    img {
      margin-left: 4 * @rem;
      width: 14 * @rem;
      height: 14 * @rem;
    }
  }
  .search-text {
    margin-left: 7 * @rem;
  }
  .search-text-box {
    flex: 1;
    min-width: 0;
    height: 36 * @rem;
    line-height: 36 * @rem;
    .swiper-container {
      height: 100%;
    }
  }
}
main {
  box-sizing: border-box;
  padding-top: calc(109 * @rem + @safeAreaTop);
  padding-top: calc(109 * @rem + @safeAreaTopEnv);
  padding-bottom: calc(50 * @rem + @safeAreaBottom);
  padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
  width: 100%;
  display: flex;
  background: #fff;
  min-height: 100vh;
  .left-container {
    box-sizing: border-box;
    border-radius: 0 12 * @rem 0 0;
    position: fixed;
    z-index: 1000;
    height: calc(100vh - 149 * @rem - @safeAreaTop - @safeAreaBottom);
    height: calc(100vh - 149 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
    overflow-y: auto;
    padding-bottom: 50 * @rem;
  }
  /deep/ .van-sidebar {
    flex: 0 0 72 * @rem;
    width: 72 * @rem;
    background-color: #f5f5f6;
    &::-webkit-scrollbar {
      display: none;
    }
    .van-sidebar-item {
      padding: 16 * @rem 0 * @rem 16 * @rem 0 * @rem;
      display: flex;
      justify-content: center;
      background-color: #f5f5f6;
    }
    .van-sidebar-item--select::before {
      background-color: @themeColor;
      right: 0;
      left: unset;
      border-radius: 2 * @rem;
      width: 4 * @rem;
      height: 14 * @rem;
    }
    .van-sidebar-item__text {
      font-size: 14 * @rem;
      font-weight: 400;
      color: #000;
    }
    .van-sidebar-item--select {
      background-color: #f5f5f6;
      .van-sidebar-item__text {
        color: @themeColor;
        font-weight: 600;
      }
    }
  }
}
.container {
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  flex: 1;
  width: 100%;
  // height: 100%;
  // overflow: hidden;
  padding-left: 72 * @rem;
  /deep/ .item-container:first-of-type {
    margin-top: 5 * @rem;
  }
  /deep/ .item-container:last-of-type {
    margin-bottom: 0;
  }
  /deep/ .van-dropdown-menu {
    position: relative;
    .van-dropdown-menu__bar {
      height: 44px;
      box-shadow: none;
      .van-dropdown-menu__title--active {
        color: @themeColor;
      }
    }
    .van-dropdown-item {
      position: absolute;
      top: 44px !important;
      height: calc(100vh - 149 * @rem - @safeAreaTop);
      height: calc(100vh - 149 * @rem - @safeAreaTopEnv);
    }
  }
  .game-container {
    // height: 100vh;
  }
}

.up {
  .up-category {
    margin: 0 0 5 * @rem;
    .up-category-list {
      .item {
        width: auto;
        height: 28 * @rem;
        margin-right: 14 * @rem;
        padding: 0 15 * @rem;
        font-size: 13 * @rem;
        color: #585858;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        border-radius: 25 * @rem;
        &:first-of-type {
          margin-left: 15 * @rem;
        }
        &.current {
          background: @themeColor;
          color: #fff;
        }
      }
    }
  }
  .tab-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 18 * @rem;
    .left {
      /deep/ .van-tabs__nav {
        background: none;
      }
      /deep/ .van-tab {
        width: 50 * @rem;
        font-size: 16 * @rem;
      }
      /deep/ .van-tab--active {
        .van-tab__text--ellipsis {
          font-size: 20 * @rem;
          font-weight: 600;
        }
      }
      /deep/ .van-tabs__line {
        width: 12 * @rem;
        height: 0 * @rem;
        background: @themeColor;
        border-radius: 5 * @rem;
      }
    }
    .right {
      font-size: 16 * @rem;
      .icon {
        margin-left: 6 * @rem;
      }
    }
  }
  .list {
    background: #fff;
    border-radius: 20 * @rem;
  }
}
</style>
