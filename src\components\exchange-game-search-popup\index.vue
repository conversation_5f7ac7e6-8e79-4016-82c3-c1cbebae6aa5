<template>
  <div>
    <van-popup
      v-model="popupShow"
      position="bottom"
      :safe-area-inset-bottom="true"
      :lock-scroll="false"
      class="popup-container"
    >
      <div class="title-container">
        <div class="title">
          <img src="@/assets/images/game-list-popup-title.png" alt="" />
        </div>
        <div class="popup-close" @click="popupShow = false"></div>
      </div>
      <div class="search-container">
        <form @submit.prevent="handleSearch">
          <input
            v-model.trim="gameInput"
            type="search"
            placeholder="请输入游戏名"
            class="search-input"
          />
        </form>
      </div>
      <div class="game-container">
        <yy-list
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh()"
          @loadMore="loadMore()"
          :empty="empty"
        >
          <div class="game-list">
            <div
              class="game-item"
              v-for="(item, index) in gameList"
              :key="index"
              @click="handleSelctGame(item)"
            >
              <div class="game-icon">
                <img :src="item.titlepic" alt="" />
              </div>
              <div class="game-title">{{ item.title }}</div>
              <div class="game-tag">{{ item.subtitle }}</div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiGameGetCardGameList } from '@/api/views/gold.js';

function debounce(fn, delay) {
  let timer = null;
  return function (value) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.call(this, value);
    }, delay);
  };
}
export default {
  name: 'exchange-game-search-popup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      xiaohaoList: [], // 小号列表
      gameInput: '', // 游戏搜索关键词
      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
    };
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },
  watch: {
    gameInput: {
      handler: debounce(function () {
        this.handleSearch();
      }, 500),
    },
  },
  methods: {
    handleSearch() {
      this.page = 1;
      this.getGameList();
    },
    handleSelctGame(item) {
      this.$emit('onSelectSuccess', item);
      this.popupShow = false;
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameGetCardGameList({
        keyword: this.gameInput,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      this.gameList.push(...res.data.list);
      if (this.gameList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  background: #fff;
  height: 582 * @rem;
  border-radius: 14 * @rem 14 * @rem 0 0;
  display: flex;
  flex-direction: column;
  .title-container {
    box-sizing: border-box;
    padding: 20 * @rem 0;
    position: relative;
    height: 68 * @rem;
    .title {
      width: 114 * @rem;
      height: 28 * @rem;
      margin: 0 auto;
    }
    .popup-close {
      width: 20 * @rem;
      height: 20 * @rem;
      background: url(~@/assets/images/close-dialog.png) center center no-repeat;
      background-size: 18 * @rem 18 * @rem;
      position: absolute;
      right: 15 * @rem;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0.5;
    }
  }
  .search-container {
    box-sizing: border-box;
    width: 342 * @rem;
    height: 33 * @rem;
    border-radius: 17 * @rem;
    background: #f8f8f9;
    margin: 0 auto;
    overflow: hidden;
    .search-input {
      width: 100%;
      padding: 0 16 * @rem;
      line-height: 33 * @rem;
      background: #f8f8f9;
      font-size: 12 * @rem;
      color: #333;
    }
  }
  .game-container {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .game-list {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
      padding: 0 5 * @rem;
      .game-item {
        width: 25%;
        margin-top: 20 * @rem;
        .game-icon {
          width: 66 * @rem;
          height: 66 * @rem;
          margin: 0 auto;
        }
        .game-title {
          box-sizing: border-box;
          font-size: 12 * @rem;
          color: #242840;
          font-weight: 600;
          text-align: center;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 14 * @rem;
          margin-top: 7 * @rem;
          padding: 0 5 * @rem;
        }
        .game-tag {
          box-sizing: border-box;
          width: 60 * @rem;
          height: 17 * @rem;
          line-height: 17 * @rem;
          padding: 0 4 * @rem;
          overflow: hidden;
          background: url(~@/assets/images/skew-bg.png) center center no-repeat;
          background-size: 60 * @rem 17 * @rem;
          margin: 5 * @rem auto 0;
          text-align: center;
          font-size: 9 * @rem;
          color: #89888d;
        }
      }
    }
  }
}
</style>
