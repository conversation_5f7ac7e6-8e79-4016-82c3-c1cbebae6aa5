<template>
  <div class="evaluate-news-page">
    <div class="tab-nav">
      <div class="tab-list">
        <div
          class="tab"
          :class="{ active: tab == item.id }"
          v-for="(item, index) in tabList"
          :key="index"
          @click="tabChange(index)"
          >{{ item.title }}</div
        >
      </div>
      <div class="order" @click="isSelectPopupShow = true">
        <span>排序方式</span>
        <span class="icon">
          <img src="~@/assets/images/games/arrow.png" alt="" />
        </span>
      </div>
    </div>
    <div class="search-container">
      <div class="search-bar">
        <div class="search-icon"></div>
        <form>
          <input
            type="text"
            v-model.trim="keyword"
            placeholder="输入想要搜索的内容"
          />
        </form>
        <div
          class="input-clear"
          v-if="inputClearShow"
          @click="clearInput"
        ></div>
      </div>
    </div>
    <yy-list
      class="list-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :isPullRefresh="true"
      :empty="empty"
      :emptyImg="emptyImg"
      :check="false"
      :class="{ marginTop: empty }"
      tips="期待你的首次评论"
    >
      <div class="comment-list">
        <comment-item-2
          class="item"
          :comment="item"
          v-for="(item, index) in list"
          :key="index"
        ></comment-item-2>
      </div>
    </yy-list>

    <van-popup
      class="selectPopup"
      v-model="isSelectPopupShow"
      :lock-scroll="false"
      round
      position="bottom"
      @closed="popupClose"
    >
      <div class="select-content">
        <div
          class="select-list"
          v-for="(select, index) in selectList"
          :key="index"
        >
          <span>{{ select.title }}</span>
          <div class="list">
            <div
              class="item"
              :class="{ active: item.value == selected[index] }"
              v-for="(item, itemIndex) in select.list"
              :key="itemIndex"
              @click="changeSelect(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="btns">
          <div class="cancel-btn btn" @click="isSelectPopupShow = false"
            >取消</div
          >
          <div class="submit-btn btn" @click="handleSelectSubmit">确定</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiCommentComments } from '@/api/views/comment';
import CommentItem2 from '@/components/comment-item-2';
import { platform } from '@/utils/box.uni.js';
import PullRefresh from '@/components/pull-refresh';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
export default {
  name: 'evaluate-news',
  components: {
    PullRefresh,
    CommentItem2,
  },
  props: {
    game_id: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      id: this.game_id,
      bannerList: [],
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      pageReloading: false,
      userId: 0,
      tabList: [
        { id: 0, title: '全部' },
        { id: 1, title: '热门' },
        { id: 2, title: '图文' },
      ],
      equipment: '',
      order: 0,
      tab: 0,
      type: 0,
      finished: true,
      empty: false,
      page: 1,
      listRows: 10,
      isSelectPopupShow: false,
      selectList: [
        {
          title: '排序方式',
          list: [
            {
              name: '默认排序',
              value: 0,
            },
            {
              name: '最新评论',
              value: 2,
            },
            {
              name: '最新回复',
              value: 12,
            },
          ],
        },
        {
          title: '设备',
          list: [
            {
              name: '全部',
              value: 0,
            },
            {
              name: '相同机型',
              value: 'iPhone',
            },
          ],
        },
      ],
      selected: [0, 0],
      timer: null,
      keyword: '',
      emptyImg,
    };
  },
  async created() {
    this.$route.meta.keepAlive = true;
    await this.getList(2);
  },
  methods: {
    clearInput() {
      this.keyword = '';
      this.$nextTick(() => {
        document.querySelector('.search-bar input').focus();
      });
    },
    async tabChange(index) {
      if (this.loadingObj.loading || this.tab == index) {
        return false;
      }
      this.tab = index;
      if (this.tab == 3 && !this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.loadingObj.loading = true;
      await this.getList(2);
      this.loadingObj.loading = false;
    },
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
        this.finished = false;
      }
      let params = {
        page: this.page,
        listRows: this.listRows,
        classId: 103,
        sourceId: this.id,
        order: this.order,
        equipment: this.equipment,
        text: this.keyword,
      };
      if (platform == 'android' && params.equipment == 'iPhone') {
        params.equipment = this.modelName ? this.modelName : '安卓';
      }
      if (this.tab == 3) {
        params.user_id = this.userInfo.user_id;
      } else {
        params.type = this.tab;
      }
      const res = await ApiCommentComments(params);
      let list = [];
      if (res.data.tops) {
        list.push(...res.data.tops);
      }
      if (res.data.hots) {
        list.push(...res.data.hots);
      }
      list.push(...res.data.comments);
      if (list.length < this.listRows) {
        this.finished = true;
      }
      this.list.push(...list);
      if (this.list.length == 0) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      this.loadingObj.loading = false;
    },
    async onRefresh() {
      await this.getList(2);
      this.pageReloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
    activeClick(item) {
      if (item.type == 1) {
        this.toPage('PlayerVoiceDetail', { id: this.id, sourceId: this.id });
      } else if (item.type == 2) {
        this.toPage('PastList');
      }
    },
    changeSelect(item, index) {
      this.$set(this.selected, index, item.value);
    },
    popupClose() {
      this.selected = [this.order, this.equipment];
    },
    async handleSelectSubmit() {
      this.order = this.selected[0];
      this.equipment = this.selected[1];
      this.isSelectPopupShow = false;
      this.loadingObj.loading = true;
      await this.getList(2);
    },
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      this.timer = setTimeout(async () => {
        this.loadingObj.loading = true;
        await this.getList(2);
        this.loadingObj.loading = false;
      }, 500);
    },
  },
  computed: {
    inputClearShow() {
      return this.keyword.length ? true : false;
    },
  },
};
</script>

<style lang="less" scoped>
.evaluate-news-page {
  margin-top: 20 * @rem;
  position: relative;
  .tab-nav {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 18 * @rem;
    box-sizing: border-box;
    margin-top: -8 * @rem;

    .tab-list {
      display: flex;
      align-items: center;

      .tab {
        height: 30 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #999999;
        line-height: 18 * @rem;
        margin-right: 22 * @rem;
        position: relative;

        &:last-of-type {
          margin-right: 0;
        }

        &.active {
          font-weight: bold;
          font-weight: 600;
          color: #111111;
          //   &::after {
          //     content: '';
          //     display: block;
          //     width: 12 * @rem;
          //     height: 6 * @rem;
          //     border-radius: 16 * @rem;
          //     background-color: @themeColor;
          //     position: absolute;
          //     bottom: 0;
          //     left: 50%;
          //     transform: translateX(-50%);
          //   }
        }
      }
    }

    .order {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      height: 18 * @rem;

      span {
        flex-shrink: 0;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 18 * @rem;
      }

      .icon {
        width: 11 * @rem;
        height: 6 * @rem;
        margin-left: 4 * @rem;
        background-position: 0 -2 * @rem;
        background-size: 12 * @rem 6 * @rem;
      }
    }
  }
  .search-container {
    width: 100%;
    margin-top: 2 * @rem;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .search-bar {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 347 * @rem;
      height: 36 * @rem;
      background: #f4f4f4;
      border-radius: 19 * @rem;
      padding: 0 18 * @rem;
      margin: 0 auto;
      .search-icon {
        width: 17 * @rem;
        height: 17 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat;
        background-size: 17 * @rem 17 * @rem;
      }
      form {
        flex: 1;
        min-width: 0;
        display: flex;
        background-color: transparent;
        margin-left: 7 * @rem;
        input {
          display: block;
          flex: 1;
          min-width: 0;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
      .input-clear {
        width: 16 * @rem;
        height: 16 * @rem;
        .image-bg('~@/assets/images/search/search_xx.png');
      }
    }
  }
  .list-container {
    &.marginTop {
      margin-top: 30 * @rem;
    }
    .comment-list {
      padding: 22 * @rem 18 * @rem 0;

      .item {
        // margin-bottom: 40 * @rem;

        // &:last-of-type {
        //   margin-bottom: 0;
        // }
        &:not(:first-child) {
          margin-top: 16 * @rem;
        }

        &:not(:last-child) {
          padding-bottom: 16 * @rem;
          border-bottom: 1px solid #f9f9f9;
        }
      }
    }
  }

  .select-content {
    width: 100%;
    padding: 12 * @rem;
    box-sizing: border-box;

    .select-list {
      display: flex;
      margin-top: 15 * @rem;

      span {
        flex-shrink: 0;
        width: 70 * @rem;
        line-height: 35 * @rem;
        font-size: 16 * @rem;
        color: #333;
        margin-right: 10 * @rem;
      }

      .list {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 80 * @rem;
          height: 35 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 35 * @rem;
          border-radius: 20 * @rem;
          background-color: #f2f2f2;
          border: 1 * @rem solid #f2f2f2;
          color: #333;
          font-size: 14 * @rem;
          margin-right: 10 * @rem;
          margin-bottom: 10 * @rem;

          &:nth-of-type(3n) {
            margin-right: 0;
          }

          &.active {
            background-color: #e7f8f3;
            color: @themeColor;
            border-color: @themeColor;
          }
        }
      }
    }

    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20 * @rem;

      .cancel-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120 * @rem;
        height: 42 * @rem;
        background-color: #efefef;
        color: #333;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
      }
      .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 200 * @rem;
        height: 42 * @rem;
        background: @themeBg;
        color: #fff;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
      }
    }
  }
}
/deep/.van-empty__image {
  width: 128px;
  height: 128px;
}
</style>
