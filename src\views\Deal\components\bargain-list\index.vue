<template>
  <div class="bargain-list">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
    >
      <template v-for="item in list">
        <my-bargain-section
          :key="item.xh_id"
          :info="item"
          @refresh="onRefresh"
        ></my-bargain-section>
      </template>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoMyBargain } from '@/api/views/xiaohao.js';
import myBargainSection from '../my-bargain-section/index.vue';
export default {
  name: 'BargainList',
  components: {
    myBargainSection,
  },
  props: {
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      list: [],
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res;
      res = await ApiXiaohaoMyBargain({
        type: this.type,
        page: this.page,
        listRows: this.listRows,
      });

      if (action === 1 || this.page === 1) {
        this.list = [];
      }
      this.list.push(...res.data.list);
      if (!this.list.length) {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.list.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.bargain-list {
  box-sizing: border-box;
  height: calc(100vh - 144 * @rem - @safeAreaTop);
  height: calc(100vh - 144 * @rem - @safeAreaTopEnv);
  flex: 1;
  display: flex;
  flex-direction: column;
  .yy-list {
    flex: 1;
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: auto;
    }
  }
}
</style>
