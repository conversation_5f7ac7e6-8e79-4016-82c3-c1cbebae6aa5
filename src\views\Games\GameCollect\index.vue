<template>
  <div class="game-collect-page page">
    <nav-bar-2 :placeholder="false" bgStyle="transparent"></nav-bar-2>
    <div class="main">
      <div class="banner" v-if="info">
        <img :src="info.list_banner" alt="" />
      </div>
      <div class="main-info" v-if="info">
        <div class="main-title">{{ info.title }}</div>
        <div class="main-desc" v-html="info.desc"></div>
      </div>
      <content-empty
        v-if="empty"
        :tips="$t('咦，什么都没找到哦~')"
      ></content-empty>
      <load-more
        v-else
        class="list-container"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="recommend-list">
          <recommend-item
            v-for="(item, index) in this.gameList"
            :key="item.id"
            :info="item"
            :index="index"
            :morePicFirst="true"
          ></recommend-item>
        </div>
      </load-more>
    </div>
  </div>
</template>

<script>
import { ApiCollectGameCollect } from '@/api/views/game.js';
export default {
  name: 'GameCollect',
  data() {
    return {
      id: 0,
      info: {},
      gameList: [],
      page: 1,
      listRows: 10,
      finished: false,
      loading: false,
      empty: false,
    };
  },
  async created() {
    this.id = this.$route.params.id;
    this.info = this.$route.params.info;
    await this.getList();
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiCollectGameCollect({
        id: this.id,
        page: this.page,
        listRows: this.listRows,
      });
      let { games, info } = res.data;
      if (action === 1 || this.page === 1) {
        if (info) {
          this.info = info;
        }
        this.gameList = [];
        if (!games.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gameList.push(...games);
      if (games.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getList(2);
    },
    play(index) {
      this.pauseAll();
      let $video = document.querySelector(
        `.recommend-item:nth-of-type(${index + 1}) video`,
      );
      let $play = document.querySelector(
        `.recommend-item:nth-of-type(${index + 1}) .play`,
      );
      $play.style.display = 'none';
      $video.play();
      $video.setAttribute('controls', 'true');
    },
    pauseAll() {
      let $videos = document.querySelectorAll('.recommend-item video');
      let $plays = document.querySelectorAll('.recommend-item .play');
      $videos.forEach(item => {
        item.pause();
        item.removeAttribute('controls');
      });
      $plays.forEach(item => {
        item.style.display = 'block';
      });
    },
  },
};
</script>

<style lang="less" scoped>
.game-collect-page {
  .main {
    .banner {
      width: 100%;
      position: relative;
      z-index: 1;
    }
    .main-info {
      position: relative;
      z-index: 2;
      padding: 40 * @rem 18 * @rem 10 * @rem;
      margin-top: -20 * @rem;
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 1) 20 * @rem
      );
      .main-title {
        font-size: 18 * @rem;
        font-weight: 600;
        color: #000;
        margin-bottom: 10 * @rem;
      }
      .main-desc {
        font-size: 14 * @rem;
        color: #666;
        line-height: 24 * @rem;
      }
    }
    .recommend-list {
      .recommend-item {
        box-sizing: border-box;
        width: 339 * @rem;
        background: #ffffff;
        box-shadow: 0 * @rem 3 * @rem 11 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
        border-radius: 10 * @rem;
        margin: 0 auto;
        margin-top: 10 * @rem;
        padding: 13 * @rem;
        .game-top {
          display: flex;
          align-items: center;
          padding-bottom: 10 * @rem;
          .game-icon {
            width: 40 * @rem;
            height: 40 * @rem;
          }
          .game-right {
            margin-left: 10 * @rem;
            flex: 1;
            min-width: 0;
            .game-name {
              font-size: 15 * @rem;
              color: #383838;
              font-weight: 600;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .game-bottom {
              flex: 1;
              min-width: 0;
              font-size: 12 * @rem;
              color: #929292;
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              margin-top: 4 * @rem;
              .score {
                height: 17 * @rem;
                display: flex;
                align-items: center;
              }

              .types {
                display: flex;
                align-items: center;
                margin-left: 8 * @rem;
                height: 17 * @rem;
                flex: 1;
                min-width: 0;
                .type {
                  padding: 0 5 * @rem;
                  position: relative;
                  display: flex;
                  align-items: center;
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      position: absolute;
                      left: 0;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 1 * @rem;
                      height: 10 * @rem;
                      background-color: #929292;
                    }
                  }
                }
              }
              .server-date {
                width: 65 * @rem;
                height: 17 * @rem;
                background: linear-gradient(
                  90deg,
                  #ff7575 0%,
                  @themeColor 100%
                );
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12 * @rem;
                color: #ffffff;
                font-weight: 400;
                border-radius: 12 * @rem;
              }
            }
          }
        }
        .video-container {
          width: 313 * @rem;
          height: 176 * @rem;
          position: relative;
          margin: 0 auto;
          border-radius: 8 * @rem;
          overflow: hidden;
          video {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
          .play {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 45 * @rem;
            height: 45 * @rem;
            background-image: url(~@/assets/images/video-play.png);
            background-size: 100%;
          }
        }
        .game-smalltext {
          max-height: 20 * @rem;
          line-height: 20 * @rem;
          font-size: 13 * @rem;
          color: #797979;
          font-weight: 400;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 10 * @rem;
        }
      }
    }
  }
}
</style>
