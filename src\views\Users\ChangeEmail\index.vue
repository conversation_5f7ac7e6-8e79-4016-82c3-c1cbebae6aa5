<template>
  <div class="page change-email">
    <nav-bar-2 :border="true" :title="pageTitle" ref="topNavBar"></nav-bar-2>
    <!-- 换绑 -->
    <template v-if="hadBind">
      <bind-email
        v-if="step == 1"
        :step="1"
        :key="1"
        @verifySuccess="verifySuccess"
      ></bind-email>
      <bind-email
        v-else-if="step == 2"
        :step="2"
        :key="2"
        :oldEmail="email"
      ></bind-email>
    </template>
    <!-- 密保 -->
    <!-- <verify-security v-else-if="is_security && step == 1" :step="1" @verifySuccess="verifySuccess"></verify-security> -->
    <!-- 没有邮箱，没有密保，直接绑定邮箱 -->
    <bind-email v-else :step="step" :key="0"></bind-email>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import securityQuestionBar from '@/components/security-question-bar';

// ================================
import bindEmail from './components/bind-email.vue';
import verifySecurity from './components/verify-security.vue';
export default {
  name: 'ChangePassword',
  components: {
    securityQuestionBar,
    bindEmail,
    verifySecurity,
  },
  data() {
    return {
      email: '',
      hadBind: false,
      step: 0,
      // ========================================
      phone: '',
      authCode: '',
      password: '',
      countdown: 60,
      ifCount: false,
      open: false, //是否显示密码
      captcha: null,

      username: '',
      selectedQuestion: {
        title: '请选择问题',
      },
      answer: '',

      show_phone: false, // 显示手机号和验证码

      show_username: true, // 显示账号

      show_security: false, // 显示密保
    };
  },
  computed: {
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
    pageTitle() {
      return this.email ? '更换绑定邮箱' : '绑定邮箱';
    },
    is_security() {
      return this.userInfo.is_security;
    },
    token() {
      return this.userInfo.token;
    },
  },
  watch: {
    authCode() {
      if (String(this.authCode).length > 6)
        this.authCode = String(this.authCode).substring(0, 5);
    },
  },
  created() {
    this.hadBind = this.userInfo.email ? true : false;
    this.email = this.userInfo.email;
    if (this.userInfo.is_security == 1 || this.userInfo.email) {
      this.step = 1;
    }
  },
  methods: {
    verifySuccess() {
      // 第一步认证成功
      this.step = 2;
    },
  },
};
</script>

<style lang="less" scoped>
.change-email {
  background: #f6f6f9;
}
</style>
