<template>
  <div class="game-item-components" @click="toDetail(gameInfo)">
    <div
      class="game-icon"
      :style="{
        width: `${iconSize * remNumberLess}rem !important`,
        height: `${iconSize * remNumberLess}rem !important`,
      }"
    >
      <img :src="gameInfo.titlepic" alt="" />
    </div>
    <div class="game-info">
      <div class="game-name" :class="{ big: iconSize >= 80 }">
        {{ gameInfo.main_title }}
        <span class="game-subtitle" v-if="gameInfo.subtitle">{{
          gameInfo.subtitle
        }}</span>
      </div>
      <div class="info-center">
        <slot>
          <template>
            <div v-for="(item, index) in gameInfo.row_1" :key="index">
              <div
                v-if="item.label == 'rank' && item.rank?.title"
                class="discount-ranking"
              >
                <img
                  class="ranking-icon"
                  src="@/assets/images/games/ranking-icon.png"
                />
                <div class="ranking-title">{{ item.rank?.title }}</div>
              </div>
              <div
                v-else-if="item.label == 'heat_degree' && item.heat_degree"
                class="game-hot"
              >
                <img
                  class="hot-icon"
                  src="@/assets/images/games/hot-icon.png"
                />
                <div class="hot-num">{{ item.heat_degree }}</div>
              </div>
              <div v-else-if="item.label == 'tag'" class="types">
                <template v-for="(type, typeIndex) in item.tag">
                  <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                    type.title
                  }}</span>
                </template>
              </div>
            </div>
          </template>
        </slot>
      </div>
      <div class="info-bottom">
        <template>
          <div v-for="(item, index) in gameInfo.row_2" :key="index">
            <div class="discount-tag" v-if="item.label == 'discount_show'">
              <img
                class="discount-icon"
                :class="{
                  'discount-01': item.discount_show.title.includes('0.1'),
                }"
                :src="item.discount_show.img"
              />
              <div class="discount-text"
                ><span>{{ item.discount_show.title }}</span></div
              >
            </div>
            <div class="tags" v-if="item.label == 'extra_tag'">
              <div
                class="tag"
                v-for="(tag, tagIndex) in item.extra_tag"
                :key="tagIndex"
              >
                <div class="tag-name">{{ tag.title }}</div>
              </div>
            </div>
            <div class="yxftitle" v-if="item.label == 'yxftitle'">
              {{ item.yxftitle }}
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="game-right">
      <div class="onekey-btn" v-if="showBtnDirect">
        <div>一键直达</div>
        <div class="arrow-icon">
          <img src="~@/assets/images/search/search-right-arrow.png" alt="" />
        </div>
      </div>
      <div class="download-btn" v-if="showBtnDownload"> 下载 </div>
    </div>
  </div>
</template>

<script>
import { navigateToGameDetail } from '@/utils/function';
import { remNumberLess } from '@/common/styles/_variable.less';

export default {
  name: 'GameItem',
  props: {
    gameInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 游戏图标大小
    iconSize: {
      type: Number,
      default: 72,
    },
    // 是否显示下载按钮
    showBtnDownload: {
      type: Boolean,
      default: false,
    },
    // 是否显示一键直达按钮
    showBtnDirect: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      remNumberLess,
      loading: false,
    };
  },
  computed: {},
  methods: {
    // 打开详情页
    toDetail(gameInfo) {
      this.CLICK_EVENT(gameInfo.click_id);
      this.$emit('child-click', gameInfo); // 给搜索页使用 搜索历史
      navigateToGameDetail(gameInfo);
    },
  },
};
</script>

<style lang="less" scoped>
.game-item-components {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  padding: 10 * @rem 0;
  flex: 1;
  min-width: 0;
  .game-icon {
    width: 84 * @rem;
    height: 84 * @rem;
    border-radius: 16 * @rem;
    background-color: #eeeeee;
    overflow: hidden;
  }
  .game-info {
    margin-left: 10 * @rem;
    flex: 1;
    min-width: 0;
    // height: 65 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .game-name {
      display: flex;
      align-items: center;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #111111;
      overflow: hidden;
      white-space: nowrap;
      &.big {
        font-size: 16 * @rem;
      }
      .game-subtitle {
        box-sizing: border-box;
        border: 1 * @rem solid #e0e0e0;
        border-radius: 3 * @rem;
        font-size: 11 * @rem;
        padding: 2 * @rem 3 * @rem;
        color: #808080;
        margin-left: 8 * @rem;
        vertical-align: middle;
        line-height: 1;
      }
      .modify_subtitle {
        border: 1 * @rem solid #e5e1ea;
        color: #888888;
      }
      .game-title {
        font-size: 14 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 5em;
        font-weight: 600;
      }
    }
  }

  .info-center {
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 14 * @rem;
    margin-top: 2 * @rem;
    width: 185 * @rem;
    > div {
      height: 100%;
    }
    .hot-num,
    .game-subscribe {
      font-size: 11 * @rem;
      color: #999999;
      display: flex;
      align-items: center;
      height: 14 * @rem;
      margin-right: 6 * @rem;
    }
    .game-hot,
    .discount-ranking {
      color: #666666;
      display: flex;
      align-items: center;
      height: 14 * @rem;
      line-height: 14 * @rem;
      .hot-icon,
      .ranking-icon {
        background-size: 10 * @rem 10 * @rem;
        width: 10 * @rem;
        height: 10 * @rem;
        margin-right: 2 * @rem;
      }
      .ranking-title {
        font-size: 11 * @rem;
        color: #999999;
        line-height: 14 * @rem;
        height: 14 * @rem;
        margin-right: 10 * @rem;
      }
    }
    .types {
      display: flex;
      align-items: center;
      height: 14 * @rem;
      line-height: 14 * @rem;
      .type {
        padding: 0 5 * @rem;
        position: relative;
        display: flex;
        align-items: center;
        color: #999999;
        line-height: 14 * @rem;
        font-size: 11 * @rem;
        &:first-of-type {
          padding-left: 0;
        }
        &:not(:first-child) {
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1 * @rem;
            height: 10 * @rem;
            background-color: #999999;
          }
        }
      }
    }

    .server-date {
      font-size: 12 * @rem;
      color: #ffa16c;
      margin-left: 5 * @rem;
      height: 17 * @rem;
      display: flex;
      align-items: center;
      line-height: 17 * @rem;
      span {
        line-height: 17 * @rem;
      }
    }
  }
  .info-bottom {
    display: flex;
    align-items: center;
    margin-top: 2 * @rem;
    .discount-tag {
      display: flex;
      align-items: center;
      width: fit-content;
      margin-right: 8 * @rem;
      flex-shrink: 0;

      .discount-icon {
        width: 30 * @rem;
        height: 18 * @rem;
        position: relative;
        z-index: 1;
        &.discount-01 {
          width: 49 * @rem;
        }
      }
      .discount-text {
        display: flex;
        align-items: center;
        height: 18 * @rem;
        padding-right: 4 * @rem;
        flex: 1;
        min-width: 0;
        font-size: 11 * @rem;
        color: #ff6649;
        white-space: nowrap;
        background-color: #fff5ed;
        border-radius: 0 2 * @rem 2 * @rem 0;
        margin-left: -5 * @rem;
        padding-left: 5 * @rem;
      }
    }
    .is-accelerate {
      width: 73 * @rem;
      height: 18 * @rem;
      background: url('~@/assets/images/games/accelerate-tag.png');
      background-position: left center;
      background-size: 73 * @rem 18 * @rem;
      margin-right: 8 * @rem;
      flex-shrink: 0;
    }
    .tags {
      display: flex;
      height: 18 * @rem;
      overflow: hidden;
      flex-wrap: wrap;
      .tag {
        height: 18 * @rem;
        margin-right: 8 * @rem;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        color: #9a9a9a;
        background-color: #ecfbf4;
        border-radius: 2 * @rem;
        padding: 0 4 * @rem;
        margin-bottom: 1 * @rem;
        .tag-icon {
          width: 13 * @rem;
          height: 13 * @rem;
          margin-right: 2 * @rem;
        }
        .tag-name {
          font-size: 11 * @rem;
          white-space: nowrap;
          color: #21b98a;
        }
      }
      .modify-tag {
        background-color: #fff;
      }
    }
    .yxftitle {
      width: 185 * @rem;
      font-weight: 400;
      font-size: 11 * @rem;
      color: #868686;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .gold-discount-text {
      color: #21b98a;
      white-space: nowrap;
      width: 139 * @rem;
      height: 15 * @rem;
      font-weight: 400;
      font-size: 11 * @rem;
      color: #21b98a;
      line-height: 15 * @rem;
    }
  }
  .game-right {
    position: relative;
    display: flex;
    align-items: center;
    .download-btn {
      width: 64 * @rem;
      height: 30 * @rem;
      background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
      border-radius: 28 * @rem;
      font-weight: 500;
      font-size: 14 * @rem;
      color: #ffffff;
      text-align: center;
      line-height: 30 * @rem;
    }
    .onekey-btn {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #999999;
      flex-shrink: 0;
      .arrow-icon {
        margin-left: 2 * @rem;
        width: 8 * @rem;
        height: 8 * @rem;
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
