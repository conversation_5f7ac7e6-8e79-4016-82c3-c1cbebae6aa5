<template>
  <div class="manage-by-xiaohao">
    <div class="search-container">
      <div class="search-bar">
        <div class="search-icon"></div>
        <form>
          <input
            type="text"
            v-model="keyword"
            :placeholder="$t('请输入小号id、小号昵称、游戏名')"
          />
        </form>
      </div>
    </div>
    <content-empty
      v-if="empty"
      :tips="$t('咦，什么都没找到哦~')"
    ></content-empty>
    <yy-list
      v-else
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="xiaohao-list">
        <div
          class="xiaohao-item"
          v-for="(item, xiaohaoIndex) in xiaohaoList"
          :key="`${item.id}${xiaohaoIndex}`"
        >
          <xiaohao-item :info="item"></xiaohao-item>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import xiaohaoItem from '../components/xiaohao-item';
import { ApiXiaohaoManage } from '@/api/views/xiaohao.js';
export default {
  name: 'ManageByXiaohao',
  components: {
    xiaohaoItem,
  },
  data() {
    return {
      themeColorLess,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      gameInfo: {},
      empty: false,
      xiaohaoList: [],
      keyword: '',
      timer: null,
    };
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      this.timer = setTimeout(async () => {
        this.finished = false;
        await this.getXiaohaoList();
      }, 300);
    },
  },
  async activated() {
    this.finished = false;
    await this.getXiaohaoList();
  },
  methods: {
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoManage({
        keyword: this.keyword,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
      }
      this.xiaohaoList.push(...res.data.list);
      if (this.xiaohaoList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getXiaohaoList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getXiaohaoList(2);

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.manage-by-xiaohao {
  position: fixed;
  width: 100%;
  height: calc(100vh - 94 * @rem - @safeAreaTop);
  height: calc(100vh - 94 * @rem - @safeAreaTopEnv);
  top: calc(44 * @rem);
  display: flex;
  flex-direction: column;
  .search-container {
    width: 100%;
    height: 60 * @rem;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    .search-bar {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 347 * @rem;
      height: 38 * @rem;
      background: #f4f4f4;
      border-radius: 19 * @rem;
      padding: 0 18 * @rem;
      margin: 0 auto;
      .search-icon {
        width: 17 * @rem;
        height: 17 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat;
        background-size: 17 * @rem 17 * @rem;
      }
      form {
        flex: 1;
        min-width: 0;
        display: flex;
        background-color: transparent;
        margin-left: 7 * @rem;
        input {
          display: block;
          flex: 1;
          min-width: 0;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
    }
  }
  .yy-list {
    flex: 1;
    overflow-y: auto;
  }
  .xiaohao-list {
    flex: 1;
    min-height: 0;
    margin-top: 10 * @rem;
    .xiaohao-item {
      margin-top: 20 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
    }
  }
}
</style>
