<template>
  <div class="change-birthday-page page">
    <nav-bar-2 :border="true" :title="$t('生日时间设置')"></nav-bar-2>
    <van-datetime-picker
      v-model="currentDate"
      type="date"
      :min-date="minDate"
      :max-date="maxDate"
      :item-height="`${53 * remNumberLess}rem`"
    >
      <template #default>
        <div></div>
      </template>
    </van-datetime-picker>
    <div class="save btn" @click="commit()">{{ $t('确认') }}</div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { ApiChangeInfoEx } from '@/api/views/users';
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'ChangeBirthday',
  data() {
    return {
      remNumberLess,
      minDate: new Date(1949, 0, 1),
      maxDate: new Date(),
      currentDate: new Date(),
    };
  },
  computed: {
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
    }),
  },
  created() {
    if (this.userInfoEx.birthday != 0) {
      let temp = this.handleTimestamp(this.userInfoEx.birthday);
      this.currentDate = new Date(temp.y, temp.m, temp.d);
    } else {
      this.currentDate = new Date(2000, 4, 5);
    }
  },
  methods: {
    ...mapMutations({
      setUserInfoEx: 'user/setUserInfoEx',
    }),
    commit() {
      ApiChangeInfoEx({ birthday: this.currentDate }).then(res => {
        this.setUserInfoEx(res.data);
        this.$toast(res.msg);
        this.$router.go(-1);
      });
    },
    handleTimestamp(ts) {
      let temp = new Date(ts * 1000),
        y = temp.getFullYear(),
        m = temp.getMonth(),
        d = temp.getDate();
      return { y, m, d };
    },
  },
};
</script>

<style lang="less" scoped>
.change-birthday-page {
  /deep/ .van-picker__toolbar {
    height: 0;
  }
  /deep/ .van-picker-column__item {
    color: #47a83a;
    font-size: 15 * @rem;
    color: #c2c2c2;
  }
  /deep/ .van-picker-column__item--selected {
    color: #47a83a;
    font-size: 15 * @rem;
    font-weight: bold;
  }
  .save {
    width: 347 * @rem;
    height: 45 * @rem;
    border-radius: 23 * @rem;
    background: @themeBg;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 16 * @rem;
    color: #ffffff;
  }
}
</style>
