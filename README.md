# box3733

> 安卓原生sdk的from是3；
>
> ios原生sdk的from是103；

港澳台新增字段

> 在初始化接口新增 `login_type` 和 `is_hw` 字段：

0.国内用户，逻辑和之前一样
1.海外用户，默认密码登录，用户点击 验证码登录时给出以下弹窗（弹窗内容由服务端给出）
2.海外用户，默认密码登录，点击验证码登录，不再跳转，而是直接给出一个弹窗（弹窗内容由服务端给出）
3.海外用户，默认密码登录，隐藏验证码登录

## 初始化接口需要的字段：

access_tracking_io 热云

access_medium_h5_io

is_hw 是否海外

configs.flavor 是否是up版

app_name 游戏盒

default_avatar 默认头像

configs.hide_jfq 是否隐藏福利中心

configs.show_suspension 是否显示引导

configs.show_my_game 是否显示我的游戏

nav_angle 底部导航栏角标

jump_game 跳转游戏

## 用户行为统计 部分click_id：

    // M1=首页、M2=排行榜、M3=辅助空间、M4=福利中心、M5=我的 M6=分类（webapp） M7=我的游戏（webapp）
    // 搜索=SEARCH  消息=MSG  游戏列表=GAMELIST
    // 我的页面中
    // 设置=SETTING 个人资料=SELF 经验等级=LEVEL 财富等级=MONEY

    // 我的-右上角客服=M5_KF    我的-右上角消息=M5_MSG
    // 每日签到=SIGNIN 充值平台币=PAYPTB 平台币明细=PTBDTL
    // 代金券=COUPON  金币详情=GOLDDTL  使用金币=USEGOLD
    // 礼金=CASHGIFT SVIP会员=SVIP 省钱卡=SAVING
    // 金币广告= GOLDADV
    // 点击福利中心  click_id  = M4
    // 首页的搜索click_id  = M1_SEARCH
    // 首页的消息click_id  = M1_MSG
    // 我的页面的每日签到click_id  = M5_SIGNIN

## 友盟统计备份

```js
export const youMeng = {
  fun() {
    window._czc = [];
    const script = document.createElement('script');
    script.src =
      'https://v1.cnzz.com/z_stat.php?id=1280372883&web_id=1280372883';
    script.language = 'JavaScript';
    document.body.appendChild(script);
  },
  point(a, b, c = '') {
    try {
      window._czc.push(['_trackEvent', a, b, c]);
    } catch { }
  },
};

Vue.prototype.$point = youMeng.point;
```
