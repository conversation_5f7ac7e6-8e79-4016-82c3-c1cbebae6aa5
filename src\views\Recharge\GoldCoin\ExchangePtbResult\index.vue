<template>
  <div class="page exchange-result-page">
    <nav-bar-2
      :border="false"
      title="兑换结果"
      :azShow="true"
      :placeholder="false"
      bgStyle="transparent-white"
    >
    </nav-bar-2>
    <div class="main">
      <div class="top-container">
        <div class="top-bg"></div>
        <div class="result-info">
          <div class="result-icon">
            <img src="@/assets/images/recharge/exchange-success.png" />
          </div>
          <div class="result-text">兑换成功</div>
        </div>
      </div>
      <div class="gray-line"></div>
      <div class="card-container">
        <div class="ptb">
          获得<span>{{ info.exchange_ptb }}</span
          >平台币
        </div>
        <div class="card-list">
          <div class="card-item">
            <div class="title">消耗金币：</div>
            <div class="desc">{{ info.buy_gold }}</div>
          </div>
          <div class="card-item">
            <div class="title">订单编号：</div>
            <div class="desc">{{ info.order_id }}</div>
          </div>
          <div class="card-item">
            <div class="title">兑换时间：</div>
            <div class="desc">{{ info.buy_time }}</div>
          </div>
          <div class="card-item">
            <div class="title">特别说明：</div>
            <div class="desc">{{ info.desc }}</div>
          </div>
        </div>
      </div>
      <div class="conform-btn btn" @click="goToGoldCoinExchange"> 完成 </div>
    </div>
    <bottom-safe-area></bottom-safe-area>
  </div>
</template>
<script>
import {
  BOX_openInNewWindow,
  BOX_showActivityByAction,
} from '@/utils/box.uni.js';
export default {
  name: 'ExchangePtb',
  data() {
    return {
      info: {},
    };
  },
  created() {
    this.info = this.$route.params.info;
    console.log(this.info);
  },
  methods: {
    goToGoldCoinExchange() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },
  },
};
</script>

<style lang="less" scoped>
.exchange-result-page {
  background: #f8f8f9;
  .main {
    .top-container {
      width: 100%;
      height: 248 * @rem;
      overflow: hidden;
      position: relative;
      .top-bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 248 * @rem;
        background: #32b768;
        border-radius: 0 0 100% 100%;
        transform: scale(3);
        transform-origin: center bottom;
      }
      .result-info {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 105 * @rem auto;
        .result-icon {
          width: 56 * @rem;
          height: 41 * @rem;
        }
        .result-text {
          font-size: 20 * @rem;
          color: #ffffff;
          margin-left: 6 * @rem;
          font-weight: 500;
        }
      }
    }
    .gray-line {
      position: relative;
      z-index: 1;
      margin: -62 * @rem auto 0;
      width: 349 * @rem;
      height: 10 * @rem;
      background: rgba(7, 136, 87, 0.7);
      border-radius: 13 * @rem 13 * @rem 13 * @rem 13 * @rem;
    }
    .card-container {
      margin: -10 * @rem auto 0;
      box-sizing: border-box;
      width: 349 * @rem;
      height: 277 * @rem;
      background: url(~@/assets/images/recharge/exchange-card-bg.png) no-repeat;
      background-size: 349 * @rem 277 * @rem;
      position: relative;
      padding: 35 * @rem 27 * @rem;
      z-index: 2;
      .ptb {
        font-size: 12 * @rem;
        color: #888888;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 35 * @rem;
        span {
          font-size: 28 * @rem;
          color: #111111;
          font-weight: bold;
          margin: 0 8 * @rem;
        }
      }
      .card-list {
        margin-top: 28 * @rem;
        .card-item {
          display: flex;
          align-items: center;
          padding: 10 * @rem 0;
          .title {
            font-size: 14 * @rem;
            color: #888888;
          }
          .desc {
            font-size: 14 * @rem;
            color: #111111;
          }
        }
      }
    }
    .conform-btn {
      width: 247 * @rem;
      height: 44 * @rem;
      border-radius: 22 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      margin: 38 * @rem auto 0;
      font-size: 15 * @rem;
      color: #ffffff;
    }
  }
}
</style>
