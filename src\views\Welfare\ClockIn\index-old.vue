<template>
  <rubber-band topColor="#FFE2BE" bottomColor="#FFE2BE">
    <div class="clock-in-page">
      <nav-bar-2
        bgStyle="transparent"
        :border="false"
        :placeholder="false"
      ></nav-bar-2>
      <div class="top-banner"></div>
      <div class="top-bar-container">
        <div class="top-reward">
          <!-- <div class="main-title" v-html="mainTitle"></div> -->
          <div class="reward-list">
            <!-- state 0:不可领取 1:可领取 2:已领取 -->
            <div
              class="reward-item"
              :class="{ last: index === goldBoxList.length - 1 }"
              v-for="(item, index) in goldBoxList"
              :key="item.gold"
              @click="handleGet(item)"
            >
              <div class="days">{{ item.box_num }}{{ $t('天') }}</div>
              <div class="reward-icon"></div>

              <div class="coins can" v-if="item.state == 1">
                {{ $t('可领取') }}
              </div>
              <div class="coins had" v-else-if="item.state == 2">
                {{ $t('已领取') }}
              </div>
              <div class="coins" v-else>
                {{ item.gold }}<span>{{ $t('金币') }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="clock-in-info">
          <div class="operate-info">
            <div class="total-days">
              <span>{{ continousDay }}</span
              >本月已连续签到天数
            </div>
            <div
              class="clock-in-btn btn"
              :class="{ had: isSignIn(currentDay) }"
              @click="clockIn"
            >
              {{ clockInBtnText }}
            </div>
          </div>
          <div class="record-info">
            <div class="record-item">
              {{ $t('本月签到总天数') }}：<span>{{ clockInDate.length }}</span>
            </div>
            <div class="record-item">
              {{ $t('剩余补签次数') }}：<span>{{ surplusRepairNum }}</span>
            </div>
          </div>
        </div>
        <!-- 日历 -->
        <div class="calendar-container">
          <div class="title">
            {{ currentYear }}{{ $t('年') }}{{ currentMonth + 1 }}{{ $t('月') }}
          </div>
          <div class="content">
            <div class="calendar-top" v-if="calendarOpen">
              <div
                class="top-item"
                v-for="(item, index) in calendarTop"
                :key="index"
              >
                {{ item }}
              </div>
            </div>
            <div class="day-list">
              <template v-for="item in prevLeftDays">
                <div
                  class="day-item day-pre"
                  :key="'pre' + item"
                  v-if="
                    thisWeekList.some(
                      v =>
                        v.day == prevDays + item - prevLeftDays &&
                        v.month != currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div class="day-btn">
                    <div class="num">{{ prevDays + item - prevLeftDays }}</div>
                  </div>
                </div>
              </template>
              <template v-for="item in currentDays">
                <div
                  class="day-item"
                  :key="'cur' + item"
                  v-if="
                    thisWeekList.some(
                      v => v.day == item && v.month == currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div
                    class="day-btn btn"
                    @click="repairSign(item)"
                    :class="{
                      had: isSignIn(item),
                      can: canSignIn(item),
                      today: item == currentDay,
                    }"
                  >
                    <div class="num">
                      {{ item }}
                    </div>

                    <div class="text" v-if="canSignIn(item) || isSignIn(item)">
                      {{ isSignIn(item) ? '' : $t('补签') }}
                    </div>
                    <div class="text" v-else></div>
                  </div>
                </div>
              </template>
              <template v-for="item in nextLeftDays">
                <div
                  class="day-item day-next"
                  :key="'next' + item"
                  v-if="
                    thisWeekList.some(
                      v => v.day == item && v.month != currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div class="day-btn">
                    <div class="num">{{ item }}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div
            class="calendar-open"
            :class="{ close: calendarOpen }"
            @click="calendarOpen = !calendarOpen"
          ></div>
        </div>
      </div>

      <!-- 今日游戏福利 -->
      <div class="today-game-container">
        <div class="today-title">今日游戏福利</div>
        <div class="game-swiper">
          <van-swipe
            class="my-swipe"
            :autoplay="3000"
            indicator-color="#FE6600"
            :loop="true"
          >
            <van-swipe-item v-for="(item, index) in benefitsList" :key="index">
              <div class="swiper-item">
                <div class="game-line">
                  <game-item-2
                    :type="5"
                    :gameInfo="item"
                    class="game-item"
                    :showRight="false"
                  ></game-item-2>
                </div>
                <div
                  class="reward-line"
                  @click="goToGameCoupon(item)"
                  v-if="item.coupon"
                >
                  <div class="reward-icon">
                    <img :src="benefitsIcon.coupon" alt="" />
                  </div>
                  <div class="reward-content">
                    满<span
                      >{{ item.coupon.reach_money }}-{{
                        item.coupon.money
                      }}</span
                    >专属代金券
                  </div>
                  <div class="btn reward-btn">领取</div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <!-- 签到规则 -->
      <div class="sign-rule-container">
        <div class="rule-title">
          <div class="rule-title-icon"></div>
          <div class="rule-title-text">{{ $t('签到规则') }}</div>
        </div>
        <div class="rule-content" v-html="rules" @click="clickRules"></div>
      </div>

      <!-- 领取宝箱成功弹窗 -->
      <van-dialog
        v-model="boxSuccessShow"
        :show-confirm-button="false"
        :lock-scroll="false"
      >
        <div class="box-success">
          <div class="box-icon"></div>
          <div class="info-content">
            <div class="info-text" v-for="item in boxPoints" :key="item.id">
              {{ item.rule_name }}{{ item.text }}{{ item.num }}
            </div>
            <div class="info-text red small" v-if="!userInfo.is_svip">
              SVIP尊享每日签到双倍金币，宝箱奖励翻倍！
            </div>
          </div>
          <div class="btn-bar">
            <div class="success-conform" @click="boxSuccessShow = false">
              收下
            </div>
            <div class="vip-conform" v-if="!userInfo.is_svip" @click="toSvip">
              立即开通
            </div>
          </div>
        </div>
      </van-dialog>

      <!-- 签到成功弹窗 -->
      <van-dialog
        v-model="signSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        @closed="openYindaoPopup()"
      >
        <div class="sign-success">
          <div class="dialog-close" @click="signSuccessShow = false">
            <img src="@/assets/images/clock-in/sign-success-close.png" alt="" />
          </div>
          <div class="success-info">
            <div class="info-content">
              <div class="info-text" v-for="item in points" :key="item.id">
                {{ item.rule_name }}{{ item.text }}{{ item.num }}
              </div>
              <div class="info-text red" v-if="!userInfo.is_svip">
                {{ $t('开通SVIP【额外】金币') }}+{{ svip_extra_gold }}
              </div>
              <div class="info-text red small" v-if="!userInfo.is_svip">
                还可金币兑换平台币哦~
              </div>
            </div>
            <div
              class="success-conform"
              v-if="!userInfo.is_svip"
              @click="toSvip"
            >
              {{ $t('立即开通') }}
            </div>
            <div
              class="success-conform"
              v-else
              @click="signSuccessShow = false"
            >
              {{ $t('开心收下') }}
            </div>
          </div>
        </div>
      </van-dialog>
      <!-- 引导添加到桌面弹窗 -->
      <van-dialog
        v-model="yindaoPopup"
        :showConfirmButton="false"
        :showCancelButton="false"
        :lock-scroll="false"
        :close-on-click-overlay="true"
        class="yindao-popup"
      >
        <div class="close" @click="yindaoPopup = false"></div>
        <div class="text">{{ $t('为了更方便的连续签到领宝箱') }}</div>
        <div class="text">{{ $t('请添加到主屏幕哦') }}</div>
        <div class="down-arrow"></div>
      </van-dialog>

      <!-- 非svip点击签到弹窗 -->
      <van-dialog
        class="no-svip-sign-dislog"
        v-model="noSvipSignShow"
        :show-confirm-button="false"
        :lock-scroll="false"
        :close-on-click-overlay="true"
      >
        <div class="top-icon"></div>
        <div class="no-svip-tip">
          <div class="line">{{ $t('开通SVIP') }}</div>
          <div class="line">
            {{ $t('每天签到额外')
            }}<span>+{{ svip_extra_gold }}{{ $t('金币') }}</span>
          </div>
          <div class="line">
            <span class="small">还可金币兑换平台币哦~</span>
          </div>
        </div>
        <div class="no-notice" :class="{ remember: remember }">
          <div class="content" @click.stop="remember = !remember">
            <div class="gou"></div>
            {{ $t('不再提醒') }}
          </div>
        </div>
        <div class="operation">
          <div class="clock-btn btn" @click="clickNormalSign">
            {{ $t('普通签到') }}
          </div>
          <div class="clock-btn svip btn" @click="clickSvipSign">
            {{ $t('SVIP签到') }}
          </div>
        </div>
      </van-dialog>

      <!-- 非svip点击补签弹窗 -->
      <van-dialog
        v-model="repairSignTipShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
        class="repair-sign-tip-popup"
      >
        <div class="popup-icon"></div>
        <div class="repair-sign-content">
          <div class="tip-text">
            {{ noSvipRepairSignTip }}
          </div>
          <div class="operate">
            <div class="cancel-btn btn" @click="repairSignTipShow = false">
              {{ $t('取消') }}
            </div>
            <div class="svip-btn btn" @click="toBuySvip">
              {{ $t('立即开通') }}
            </div>
          </div>
        </div>
      </van-dialog>

      <!-- svip补签提示弹窗 -->
      <van-dialog
        v-model="svipRepairSignTipShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
        class="svip-repair-sign-tip-popup"
      >
        <div class="popup-icon"></div>
        <div class="repair-sign-content">
          <div class="tip-text">
            {{ repairSignTip }}
          </div>
          <div class="operate">
            <div class="repair-btn" @click="repairSigning">
              {{ $t('补签') }}
            </div>
          </div>
        </div>
      </van-dialog>
      <!-- 补签成功弹窗 -->
      <van-dialog
        v-model="repairSignSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
      >
        <div class="repair-sign-success">
          <div class="repair-num">
            {{ $t('恭喜获得') }}<span>{{ repairGoldNum }}</span
            >{{ $t('金币') }}
          </div>
          <img
            class="coin-icon"
            src="@/assets/images/clock-in/coin-icon.png"
            alt=""
          />
          <div class="text">
            {{ $t('补签成功') }}<br />{{ $t('再接再厉哦') }}
          </div>
        </div>
        <div class="dialog-close" @click="repairSignSuccessShow = false">
          <img src="@/assets/images/clock-in/dialog-close.png" alt="" />
        </div>
      </van-dialog>

      <!-- 连续签到领宝箱弹窗(全局弹窗了，这个弹窗样式暂时不用了) -->
      <!-- <van-dialog
      v-model="goldBoxSuccessShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="gold-box-success">
        <div class="success-text">
          {{ goldPoints.rule_name }}，{{ goldPoints.text
          }}<span>{{ goldPoints.num }}</span>
        </div>
        <div class="confirm" @click="goldBoxSuccessShow = false">确定</div>
      </div>
    </van-dialog> -->

      <!-- 宝箱分享弹窗 -->
      <van-dialog
        v-model="goldBoxShareShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="title">
            {{ $t('即将获得') }}<span>{{ goldBoxShareData.gold }}</span
            >{{ $t('金币') }}
          </div>
          <div class="tip">{{ goldBoxShareData.showText }}</div>
          <div class="sub-tip">{{ $t('新老用户皆可') }}</div>
          <div class="avatar">
            <img src="@/assets/images/clock-in/no-invite.png" alt="" />
          </div>
          <div class="operate">
            <div class="common-invite btn" @click="handleShare">
              {{ $t('立即邀请') }}
            </div>
            <div class="svip-invite btn" @click="toPage('Svip')">
              {{ $t('SVIP免邀请') }}
            </div>
          </div>
        </div>
      </van-dialog>

      <!-- 宝箱分享成功弹窗 -->
      <!-- 2021年12月13日18:39:42 新需求：最后两个宝箱取消分享直接领取。 -->
      <van-dialog
        v-model="goldBoxShareSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareSuccessShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="get-gold-num">
            {{ $t('获得') }}{{ goldBoxShareSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="sub-tip help-man">{{ $t('助力好友') }}</div>
          <div class="avatar avatar-icon">
            <img :src="goldBoxShareSuccessData.avatar" alt="" />
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxShareSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>

      <!-- svip直接领取最后两个宝箱 -->
      <van-dialog
        v-model="goldBoxSvipSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxSvipSuccessShow = false"></div>
          <div class="top-text orange">{{ $t('恭喜你') }}</div>
          <div class="sub-tip help-svip">
            {{ $t('SVIP无需好友助力即可领取') }}
          </div>
          <div class="get-gold-num svip-num">
            {{ goldBoxSvipSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxSvipSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>
  </rubber-band>
</template>

<script>
import rubberBand from '@/components/rubber-band';
import {
  ApiUserClockIn,
  ApiUserActiveSign,
  ApiUserGetGoldBox,
  ApiUserRepairDeductGold,
  ApiUserRepairSign,
} from '@/api/views/users.js';
import { ApiGameGetGameBenefitsList } from '@/api/views/game.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { needGuide } from '@/utils/userAgent';
let defaultDate = new Date().getTime();
export default {
  name: 'ClockIn',
  components: {
    rubberBand,
  },
  data() {
    return {
      themeColorLess,
      currentToken: 0,
      mainTitle: '',
      goldBoxList: [],
      clockInDate: [], // 已签到的日期
      rules: '',
      maxDays: 0, // 本月最长连续签到
      continousDay: 0, // 目前连续签到天数
      surplusRepairNum: 0, // 剩余补签次数
      repairDate: [], // 可补签的日期
      calendarTop: [
        this.$t('周日'),
        this.$t('周一'),
        this.$t('周二'),
        this.$t('周三'),
        this.$t('周四'),
        this.$t('周五'),
        this.$t('周六'),
      ],
      currentDay: new Date(defaultDate).getDate(),
      currentMonth: new Date(defaultDate).getMonth(),
      currentYear: new Date(defaultDate).getFullYear(),
      signSuccessShow: false, // 签到成功弹窗
      points: [], // 签到成功信息
      repairSignTipShow: false, // 补签提示弹窗
      svipRepairSignTipShow: false, // svip补签提示弹窗(svip才可补签了)
      noSvipRepairSignTip: '', // 非svip补签提示信息
      repairSignTip: '', // 补签提示信息
      repairSignSuccessShow: false, // 补签成功弹窗
      repairSignDay: '', // 要补签的是哪一天
      // goldPoints: [], // 领取宝箱成功信息
      goldBoxSuccessShow: false, // 连续签到领宝箱弹窗
      goldBoxShareShow: false, // 宝箱分享弹窗
      goldBoxShareData: {}, // 宝箱分享信息
      shareInfo: {}, // 分享给好友的内容
      goldBoxShareSuccessShow: false, // 宝箱分享成功弹窗
      goldBoxShareSuccessData: {}, // 宝箱分享成功弹窗
      repairGoldNum: 0,
      goldBoxSvipSuccessShow: false, // svip免邀请直接领取宝箱的弹窗
      goldBoxSvipSuccessData: {}, // svip免邀请直接领取宝箱的信息
      yindaoPopup: false, //引导到添加主屏幕的弹窗
      noSvipSignShow: false, // 非svip签到弹窗
      remember: false, // 是否记住不再提示
      popup_msg_show: true, //是否同意打开消息提示弹窗（用于规则弹窗只弹一次）
      is_open_new_role: false, //是否打开锁功能
      svip_extra_gold: 0,

      boxSuccessShow: false, // 领取宝箱弹窗
      boxPoints: [], // 领取宝箱信息

      calendarOpen: false, // 日历是否展开
      thisWeekList: [], // 今日这周的数组

      benefitsList: [], // 今日游戏福利
      benefitsIcon: {}, // 今日福利的icons
    };
  },
  computed: {
    clockInBtnText() {
      let clockDate =
        this.currentDay < 10
          ? '0' + this.currentDay
          : this.currentDay.toString();
      return this.clockInDate.includes(clockDate)
        ? this.$t('已签到')
        : this.$t('立即签到');
    },
    // 这个月天数
    currentDays() {
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },
    // 上个月天数
    prevDays() {
      return new Date(this.currentYear, this.currentMonth, 0).getDate();
    },
    // 上个月剩余天数
    prevLeftDays() {
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },
    // 下个月显示的天数
    nextLeftDays() {
      return 7 - new Date(this.currentYear, this.currentMonth + 1, 1).getDay();
    },
  },
  async created() {
    this.getThisWeekList();
    this.currentToken = this.userInfo.token;
    this.currentDay = new Date(defaultDate).getDate();
    this.currentMonth = new Date(defaultDate).getMonth();
    this.currentYear = new Date(defaultDate).getFullYear();
    await this.getSignInfo();
    await this.getGameBenefitsList();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    goToGameCoupon(item) {
      this.toPage('GameCoupon', {
        game_id: item.id,
      });
    },
    async getGameBenefitsList() {
      const res = await ApiGameGetGameBenefitsList();
      this.benefitsList = res.data.list;
      this.benefitsIcon = res.data.icon;
    },
    getThisWeekList() {
      let date = new Date(defaultDate);
      let now = date.getTime();
      let day = date.getDay();
      let oneDayTime = 24 * 60 * 60 * 1000;
      let MondayTime = now - day * oneDayTime;
      let SundayTime = now + (7 - day - 1) * oneDayTime;
      let thisWeekList = [];
      for (let i = 0; i < 7; i++) {
        let d = new Date(MondayTime + i * oneDayTime);
        thisWeekList.push(d);
      }
      this.thisWeekList = thisWeekList.map(item => {
        return {
          day: item.getDate(),
          month: item.getMonth() + 1,
        };
      });
    },
    // 签到成功跳转svip
    toSvip() {
      this.signSuccessShow = false;
      this.boxSuccessShow = false;
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
    // 补签跳转svip
    toBuySvip() {
      this.repairSignTipShow = false;
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
    // 是否可补签
    async repairSign(item) {
      if (this.canSignIn(item)) {
        this.$toast.loading({
          message: this.$t('加载中'),
        });
        const res = await ApiUserRepairDeductGold();
        this.$toast.clear();
        if (res.code == -14) {
          // 非svip点击补签
          this.noSvipRepairSignTip = res.msg;
          this.repairSignTipShow = true;
        }
        if (res.code > 0) {
          this.repairSignTip = res.data.gold_num;
          this.repairSignDay = item;
          this.svipRepairSignTipShow = true;
        }
      }
    },
    // 补签中
    async repairSigning() {
      this.svipRepairSignTipShow = false;
      let date = `${this.currentYear}-${this.currentMonth + 1}-${
        this.repairSignDay
      }`;
      const res = await ApiUserRepairSign({
        signDate: date,
      });
      this.repairGoldNum = res.data.gold_num;
      this.repairSignSuccessShow = true;
      await this.getSignInfo();
    },
    // 是否是签到过的
    isSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.clockInDate.length > 0 && this.clockInDate.includes(str)) {
        return true;
      }
      return false;
    },
    // 是否是可以补签的
    canSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.repairDate.length > 0 && this.repairDate.includes(str)) {
        return true;
      }
      return false;
    },
    // 领取宝箱
    async handleGet(item) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      // 文案由后端返回，所以前端没有判断是否已领取过或者还不能领取，所有宝箱均可点击
      await this.fetchGetGoldBox(item);
    },
    async fetchGetGoldBox(item) {
      try {
        const res = await ApiUserGetGoldBox({
          box_num: item.box_num,
        });
        let {
          need_share,
          gold_box_list,
          clock_in_date,
          is_svip,
          get_box,
          show_text,
          share_info,
          list,
        } = res.data;
        if (res.code > 0) {
          this.$toast.clear();
          if (res.points) {
            // 弹出签到成功弹窗
            this.boxPoints = res.points;
            this.boxSuccessShow = true;
          }
          await this.getSignInfo();
        }

        // 以下两个if为刷新宝箱数据
        if (gold_box_list && gold_box_list.length) {
          this.goldBoxList = gold_box_list;
        }
        if (clock_in_date && clock_in_date.length) {
          this.clockInDate = clock_in_date;
        }

        if (need_share == true) {
          // 需要分享的
          if (is_svip == false) {
            // 非svip才需要助力
            if (get_box == false) {
              // 还需要助力
              this.goldBoxShareData = {
                gold: item.gold,
                showText: show_text,
              };
              if (share_info) {
                this.shareInfo = share_info;
              }
              this.goldBoxShareShow = true;
            } else {
              // 助力成功
              this.goldBoxShareSuccessData = list[0];
              this.goldBoxShareSuccessData.gold = item.gold;
              this.goldBoxShareSuccessShow = true;
            }
          } else {
            // svip直接领取的弹窗
            this.goldBoxSvipSuccessData = {
              gold: item.gold,
            };
            this.goldBoxSvipSuccessShow = true;
          }
        }
      } catch (e) {}
    },
    async getSignInfo() {
      const res = await ApiUserClockIn();
      let {
        gold_box_list,
        clock_in_date,
        max_days,
        repair_date,
        surplus_repair_num,
        title,
        text1,
        continuous_day,
        is_open_new_role,
        svip_extra_gold,
      } = res.data;
      this.goldBoxList = gold_box_list;
      this.mainTitle = title;
      this.rules = text1;
      this.clockInDate = clock_in_date;
      this.maxDays = max_days;
      this.continousDay = continuous_day;
      this.surplusRepairNum = surplus_repair_num;
      this.repairDate = repair_date;
      this.is_open_new_role = is_open_new_role;
      this.svip_extra_gold = svip_extra_gold;
      if (res.data.update_msg && this.popup_msg_show) {
        this.popup_msg_show = false;
        this.$dialog.alert({
          message: res.data.update_msg,
          lockScroll: false,
        });
      }
    },
    clickRules(event) {
      event.preventDefault();
      let target = event.target;
      if (target.tagName.toLowerCase() == 'a') {
        let routeName = target.getAttribute('href');
        this.$router.push({ name: routeName });
      }
    },
    // 点击普通签到
    clickSvipSign() {
      this.noSvipSignShow = false;
      this.$nextTick(() => {
        this.handlenotice();
        this.toPage('Svip');
      });
    },
    // 点击svip签到
    async clickNormalSign() {
      this.noSvipSignShow = false;
      this.$nextTick(async () => {
        this.handlenotice();
        await this.handleClockIn();
      });
    },
    handlenotice() {
      if (this.remember) {
        localStorage.setItem('NO_SVIP_SIGN_DIALOG_HIDE', true);
      }
    },
    // 点击签到
    async clockIn() {
      let temDay =
        this.currentDay < 10
          ? `0${this.currentDay}`
          : this.currentDay.toString();
      if (!this.clockInDate.includes(temDay)) {
        // 还没签到的情况
        if (!this.userInfo.is_svip) {
          let no_svip_sign_dialog_hide = localStorage.getItem(
            'NO_SVIP_SIGN_DIALOG_HIDE',
          );
          if (no_svip_sign_dialog_hide) {
            // 不再提示svip直接签到
            await this.handleClockIn();
            return false;
          }
          this.remember = false;
          this.noSvipSignShow = true;
        } else {
          // 是svip的情况
          await this.handleClockIn();
        }
        return false;
      }
      this.$toast(this.$t('今日已签到'));
    },
    async handleClockIn() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiUserActiveSign();
      this.$toast.clear();
      if (res.points) {
        // 弹出签到成功弹窗
        this.points = res.points;
        this.signSuccessShow = true;
      }
      await this.getSignInfo();
    },
    handleShare() {
      this.$copyText(this.shareInfo.title_url).then(res => {
        this.$toast(this.$t('复制成功，快去粘贴给好友助力吧~'));
      });
    },
    openYindaoPopup() {
      if (needGuide) {
        this.yindaoPopup = true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.clock-in-page {
  width: 100%;
  background: #ffe2be url(~@/assets/images/clock-in/clock-in-bg-new.png) center
    top no-repeat;
  background-size: 100% auto;
  overflow: hidden;
  .top-banner {
    width: 255 * @rem;
    height: 103 * @rem;
    .image-bg('~@/assets/images/clock-in/top-banner.png');
    margin: 69 * @rem auto 0;
    margin-top: calc(30 * @rem + @safeAreaTop);
    margin-top: calc(30 * @rem + @safeAreaTopEnv);
  }
  .top-bar-container {
    box-sizing: border-box;
    width: 335 * @rem;
    .image-bg('~@/assets/images/clock-in/top-bar-bg-1.png');
    background-size: 100% auto;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 0 0 12 * @rem 12 * @rem;
    .top-reward {
      box-sizing: border-box;
      padding: 49 * @rem 0 0;
      .reward-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10 * @rem 13 * @rem;
        padding: 0 * @rem 23 * @rem 0;
        height: 206 * @rem;
        .reward-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 96 * @rem;
          background: rgba(255, 247, 238, 0.75);
          border-radius: 12 * @rem;
          border: 1 * @rem solid #ffb670;
          &.last {
            grid-column: ~'2 / 4';
            padding: 0 18 * @rem;
            .days {
              margin-right: auto;
            }
            .reward-icon {
              margin-left: auto;
              transform: scale(75 / 42);
              transform-origin: right center;
            }
            .coins {
              margin-right: auto;
            }
          }
          .days {
            font-family:
              PingFangSC-Regular,
              PingFang SC;
            margin-top: 8 * @rem;
            font-size: 15 * @rem;
            color: #8d3f3f;
            font-weight: 500;
            line-height: 21 * @rem;
            margin-top: 10 * @rem;
          }
          .reward-icon {
            width: 42 * @rem;
            height: 23 * @rem;
            .image-bg('~@/assets/images/clock-in/coin-new.png');
            background-size: 42 * @rem 23 * @rem;
            margin-top: 7 * @rem;
          }
          .coins {
            width: 50 * @rem;
            height: 18 * @rem;
            line-height: 18 * @rem;
            border-radius: 9 * @rem;
            background-color: #ffffff;
            text-align: center;
            font-size: 10 * @rem;
            font-weight: 500;
            color: #ff7554;
            white-space: nowrap;
            margin-top: 7 * @rem;
            &.can {
              color: #fff1d7;
              background-color: #ff7554;
            }
            &.had {
              color: #a6603c;
              background-color: #ffdbb2;
            }
          }
        }
      }
    }

    .clock-in-info {
      padding: 25 * @rem 20 * @rem 0;
      .operate-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .total-days {
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-size: 12 * @rem;
          color: #000000;
          font-weight: 400;
          height: 48 * @rem;
          span {
            font-family: DINAlternate-Bold, DINAlternate;
            font-size: 42 * @rem;
            color: #000000;
            font-weight: bold;
            line-height: 48 * @rem;
            margin-right: 7 * @rem;
          }
        }
        .clock-in-btn {
          width: 85 * @rem;
          height: 35 * @rem;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
          font-size: 14 * @rem;
          font-weight: 500;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          .image-bg('~@/assets/images/clock-in/clock-in.png');

          &.had {
            .image-bg('~@/assets/images/clock-in/clock-in-had.png');
          }
        }
      }
      .record-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15 * @rem;
        .record-item {
          font-size: 13 * @rem;
          font-weight: 400;
          color: #000000;
          line-height: 18 * @rem;
          span {
            color: #ff7554;
          }
        }
      }
    }
  }

  .calendar-container {
    width: 335 * @rem;
    overflow: hidden;
    background-color: #fff;
    padding-top: 23 * @rem;
    .title {
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #000000;
      height: 20 * @rem;
      line-height: 20 * @rem;
      margin: 0 14 * @rem;
      text-align: center;
      position: relative;
      &:before {
        content: '';
        width: 100 * @rem;
        height: 0.5 * @rem;
        background-color: #d8d8d8;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      &:after {
        content: '';
        width: 100 * @rem;
        height: 0.5 * @rem;
        background-color: #d8d8d8;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .content {
      padding: 0 7 * @rem;
      .calendar-top {
        display: flex;
        flex-wrap: wrap;
        padding-top: 24 * @rem;
        .top-item {
          width: 14.28%;
          text-align: center;
          font-size: 12 * @rem;
          line-height: 17 * @rem;
          color: #797979;
        }
      }
      .day-list {
        display: flex;
        flex-wrap: wrap;
        padding: 10 * @rem 0 15 * @rem;
        .day-item {
          width: 14.28%;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 5 * @rem;
          &.day-pre,
          &.day-next {
            .day-btn {
              .num {
                color: #c1c1c1;
                font-size: 16 * @rem;
              }
            }
          }
          .day-btn {
            box-sizing: border-box;
            width: 34 * @rem;
            height: 42 * @rem;
            border-radius: 8 * @rem;
            .num {
              display: flex;
              align-items: center;
              justify-content: center;
              font-family:
                PingFangSC-Regular,
                PingFang SC;
              font-size: 16 * @rem;
              line-height: 20 * @rem;
              color: #000000;
              font-weight: 400;
              width: 34 * @rem;
              margin-top: 4 * @rem;
            }
            .text {
              width: 34 * @rem;
              height: 14 * @rem;
              font-family:
                PingFangSC-Regular,
                PingFang SC;
              font-size: 12 * @rem;
              line-height: 14 * @rem;
              font-weight: 400;
              color: @themeColor;
              transform: scale(0.8333);
              transform-origin: center top;
            }

            &.had {
              background-color: #f5f5f6;
              .num {
                color: #000000;
              }
              .text {
                transform: unset;
                background: url(~@/assets/images/clock-in/clock-had.png) center
                  center no-repeat;
                background-size: 15 * @rem 10 * @rem;
              }
            }
            &.can {
              background-color: #f5f5f6;
            }
            &.today {
              border: 1 * @rem solid @themeColor;
              background: rgba(254, 102, 0, 0.1);
              &:after {
                content: '';
                width: 8 * @rem;
                height: 5 * @rem;
                background: url(~@/assets/images/clock-in/current-arrow.png);
                background-size: 8 * @rem 5 * @rem;
                position: absolute;
                bottom: -6 * @rem;
                left: 50%;
                transform: translateX(-50%);
              }
              .num {
                color: @themeColor;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
    .calendar-open {
      width: 56 * @rem;
      height: 18 * @rem;
      background: url(~@/assets/images/clock-in/calendar-open.png) left center
        no-repeat;
      background-size: 56 * @rem 18 * @rem;
      margin: 0 auto;
      &.close {
        background-image: url(~@/assets/images/clock-in/calendar-close.png);
      }
    }
  }

  .today-game-container {
    box-sizing: border-box;
    width: 335 * @rem;
    height: 251 * @rem;
    border-radius: 12 * @rem;
    background-color: #ffffff;
    margin: 10 * @rem auto 0;
    padding: 20 * @rem 10 * @rem 14 * @rem;
    .today-title {
      font-size: 18 * @rem;
      font-weight: 600;
      color: #000000;
      padding: 0 8 * @rem;
      line-height: 25 * @rem;
    }
  }

  .sign-rule-container {
    box-sizing: border-box;
    padding: 20 * @rem 20 * @rem;
    margin-bottom: 30 * @rem;
    .rule-title {
      display: flex;
      align-items: center;
      justify-content: center;
      .rule-title-icon {
        width: 16 * @rem;
        height: 16 * @rem;
        background: url(~@/assets/images/clock-in/rule-title-icon.png) center
          center no-repeat;
        background-size: 16 * @rem 16 * @rem;
      }
      .rule-title-text {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        margin-left: 6 * @rem;
        line-height: 22 * @rem;
      }
    }
    /deep/ .rule-content {
      font-size: 13 * @rem;
      line-height: 22 * @rem;
      color: #000000;
      margin-top: 12 * @rem;
      a {
        text-decoration: underline;
        color: #ff7554;
      }
    }
  }
}

/deep/ .van-dialog {
  background-color: unset;
  width: unset;
  border-radius: unset;
}
.dialog-close {
  width: 50 * @rem;
  height: 50 * @rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10 * @rem auto 0;
  img {
    display: block;
    width: 30 * @rem;
    height: 30 * @rem;
  }
}

.box-success {
  box-sizing: border-box;
  width: 275 * @rem;
  background: #fff3fc;
  border-radius: 12 * @rem;
  padding-top: 20 * @rem;
  padding-bottom: 18 * @rem;
  .box-icon {
    width: 126 * @rem;
    height: 130 * @rem;
    background: url(~@/assets/images/clock-in/get-box-coin.png) center center
      no-repeat;
    background-size: 126 * @rem 130 * @rem;
    margin: 0 * @rem auto;
  }
  .info-content {
    .info-text {
      font-size: 14 * @rem;
      color: #333333;
      text-align: center;
      margin-top: 0 * @rem;
      line-height: 18 * @rem;
      width: 220 * @rem;
      margin: 0 auto;
      &.red {
        color: @themeColor;
        font-weight: 400;
      }
      &.small {
        width: unset;
        font-size: 11 * @rem;
        line-height: 14 * @rem;
        margin-top: 11 * @rem;
      }
    }
  }
  .btn-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 18 * @rem auto 0;
    padding: 0 25 * @rem;
    .success-conform,
    .vip-conform {
      flex: 1;
      height: 36 * @rem;
      border-radius: 19 * @rem;
      background-color: #ff7554;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      font-weight: 600;
      color: #ffffff;
      margin: 0 5 * @rem;
    }
    .success-conform {
      background-color: #ffdbd2;
      color: #ff5b34;
    }
  }
}
/* 签到成功弹窗 */
.sign-success {
  width: 282 * @rem;
  height: 358 * @rem;
  .image-bg('~@/assets/images/clock-in/sign-success-bg.png');
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .dialog-close {
    width: 14 * @rem;
    height: 14 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    position: absolute;
    right: 12 * @rem;
    top: 74 * @rem;
    img {
      display: block;
      width: 14 * @rem;
      height: 14 * @rem;
    }
  }
  .success-info {
    box-sizing: border-box;
    padding-top: 17 * @rem;
    width: 252 * @rem;
    height: 184 * @rem;
    margin: 158 * @rem auto 0;
    border-radius: 8 * @rem;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
    .info-content {
      .info-text {
        font-size: 15 * @rem;
        color: #000000;
        text-align: center;
        margin-top: 8 * @rem;
        line-height: 21 * @rem;
        &.red {
          color: #ff7554;
          font-weight: 600;
        }
        &.small {
          font-size: 12 * @rem;
          line-height: 12 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
    .success-conform {
      width: 188 * @rem;
      height: 38 * @rem;
      border-radius: 19 * @rem;
      background-color: #ff7554;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #ffffff;
      position: absolute;
      bottom: 16 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
/*非svip点击补签弹窗*/
.repair-sign-tip-popup {
  width: 244 * @rem;
  .popup-icon {
    width: 98 * @rem;
    height: 75 * @rem;
    .image-bg('~@/assets/images/clock-in/svip-repair-popup-icon.png');
    margin: 0 auto;
    position: relative;
  }
  .repair-sign-content {
    margin: -38 * @rem auto 0;
    padding-top: 38 * @rem;
    background: #ffffff;
    border-radius: 12 * @rem;
    .tip-text {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
      line-height: 22 * @rem;
      text-align: center;
      padding: 0 30 * @rem;
    }
    .operate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16 * @rem;
      .svip-btn {
        width: 122 * @rem;
        height: 36 * @rem;
        background: @themeBg;
        border-radius: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: 600;
      }
      .cancel-btn {
        width: 78 * @rem;
        height: 36 * @rem;
        background: #f2f2f2;
        border-radius: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-size: 14 * @rem;
        color: #7d7d7d;
        font-weight: 600;
      }
    }
  }
}
/* svip补签提示弹窗 */
.svip-repair-sign-tip-popup {
  width: 244 * @rem;
  .popup-icon {
    width: 98 * @rem;
    height: 75 * @rem;
    .image-bg('~@/assets/images/clock-in/repair-popup-icon.png');
    margin: 0 auto;
    position: relative;
  }
  .repair-sign-content {
    margin: -38 * @rem auto 0;
    padding-top: 38 * @rem;
    background: #ffffff;
    border-radius: 12 * @rem;
    .tip-text {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
      line-height: 22 * @rem;
      text-align: center;
      padding: 0 42 * @rem;
    }
    .operate {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16 * @rem;
      .repair-btn {
        width: 124 * @rem;
        height: 36 * @rem;
        background: @themeBg;
        border-radius: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}
.repair-sign-tip {
  box-sizing: border-box;
  width: 290 * @rem;
  height: 170 * @rem;
  border-radius: 10 * @rem;
  background-color: #fff;
  padding: 28 * @rem 42 * @rem;
  .tip-text {
    font-size: 15 * @rem;
    color: #333333;
    line-height: 24 * @rem;
    text-align: center;
    margin-top: 6 * @rem;
    letter-spacing: 2 * @rem;
    span {
      font-weight: bold;
      color: #ffb400;
    }
  }
  .repair-btn {
    width: 180 * @rem;
    height: 38 * @rem;
    border-radius: 6 * @rem;
    background-color: #47a83a;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 15 * @rem;
    margin: 25 * @rem auto 0;
  }
}

/* 补签成功弹窗 */
.repair-sign-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  .repair-num {
    font-size: 20 * @rem;
    color: #fff;
    font-weight: bold;
    span {
      color: #ffc900;
    }
  }
  .coin-icon {
    width: 123 * @rem;
    height: 124 * @rem;
    margin-top: 18 * @rem;
  }
  .text {
    margin-top: 10 * @rem;
    text-align: center;
    font-size: 15 * @rem;
    color: #ffffff;
    line-height: 24 * @rem;
  }
}
/* 连续签到领宝箱弹窗 */
.gold-box-success {
  box-sizing: border-box;
  width: 290 * @rem;
  height: 150 * @rem;
  border-radius: 10 * @rem;
  background-color: #fff;
  padding: 28 * @rem 10 * @rem;
  overflow: hidden;
  .success-text {
    font-size: 15 * @rem;
    color: #333333;
    text-align: center;
    margin-top: 20 * @rem;
    span {
      color: #ffb400;
      font-weight: bold;
    }
  }
  .confirm {
    display: flex;
    justify-content: flex-end;
    font-size: 16 * @rem;
    color: #40b640;
    margin-top: 30 * @rem;
    padding: 10 * @rem;
    float: right;
    letter-spacing: 2 * @rem;
  }
}
/* 宝箱分享弹窗 */
.gold-box-share {
  box-sizing: border-box;
  position: relative;
  width: 300 * @rem;
  background-color: #fff;
  border-radius: 10 * @rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24 * @rem 13 * @rem 30 * @rem;
  .close {
    width: 40 * @rem;
    height: 40 * @rem;
    background: url(~@/assets/images/clock-in/close-share.png) center center
      no-repeat;
    background-size: 21 * @rem 21 * @rem;
    position: absolute;
    right: 0;
    top: 0;
  }
  .top-text {
    font-size: 20 * @rem;
    color: #333333;
    &.orange {
      color: #ff641d;
    }
  }
  .title {
    font-size: 25 * @rem;
    font-weight: bold;
    color: #000000;
    margin-top: 10 * @rem;
    span {
      color: #ff641d;
      font-weight: bold;
    }
  }
  .tip {
    font-size: 16 * @rem;
    color: #ff8549;
    margin-top: 10 * @rem;
  }
  .sub-tip {
    font-size: 14 * @rem;
    color: #999999;
    margin-top: 10 * @rem;
  }
  .help-man {
    margin-top: 40 * @rem;
  }
  .help-svip {
    font-size: 18 * @rem;
    color: #000000;
    margin-top: 20 * @rem;
  }
  .avatar {
    width: 50 * @rem;
    height: 50 * @rem;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 35 * @rem;
  }
  .avatar-icon {
    margin-top: 15 * @rem;
  }
  .operate {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 48 * @rem;
    .common-invite {
      width: 130 * @rem;
      height: 43 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16 * @rem;
      border-radius: 5 * @rem;
      border: 1px solid #bfbfbf;
      color: #666666;
    }
    .svip-invite {
      width: 130 * @rem;
      height: 43 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16 * @rem;
      border-radius: 5 * @rem;
      background-color: #ffa820;
      color: #fff;
    }
    .confirm {
      width: 200 * @rem;
      height: 43 * @rem;
      border-radius: 5 * @rem;
      background-color: #ffa820;
      color: #fff;
      font-size: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
  }
  .get-gold-num {
    font-size: 28 * @rem;
    color: #ff8a00;
    font-weight: bold;
    margin-top: 15 * @rem;
  }
  .svip-num {
    font-size: 28 * @rem;
    color: #ff8a00;
    margin-top: 60 * @rem;
  }
}
.yindao-popup {
  width: 250 * @rem;
  padding: 40 * @rem 35 * @rem 20 * @rem;
  background-color: #fff;
  border-radius: 10px;
  line-height: 28 * @rem;
  .close {
    position: absolute;
    top: 15 * @rem;
    right: 15 * @rem;
    width: 16 * @rem;
    height: 16 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .text {
    text-align: center;
    font-size: 15 * @rem;
  }
  .down-arrow {
    margin: 15 * @rem auto 0;
    width: 16 * @rem;
    height: 20 * @rem;
    background-image: url(~@/assets/images/clock-in/down-arrow.png);
    background-size: 100%;
    background-repeat: no-repeat;
    -webkit-animation: downward 0.8s ease-in-out infinite;
    animation: downward 0.8s ease-in-out infinite;
  }
}

.no-svip-sign-dislog {
  background-color: #fff;
  width: 274 * @rem;
  border-radius: 14 * @rem;
  padding-top: 44 * @rem;
  overflow: unset;
  .top-icon {
    width: 88 * @rem;
    height: 68 * @rem;
    .image-bg('~@/assets/images/clock-in/clock-in-dialog-top.png');
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -30 * @rem;
  }
  .no-svip-tip {
    .line {
      font-size: 16 * @rem;
      color: #000000;
      text-align: center;
      line-height: 24 * @rem;
      span {
        color: #fe4a55;
        &.small {
          font-size: 12 * @rem;
          font-weight: bold;
        }
      }
    }
  }
  .no-notice {
    text-align: center;
    color: #b1b1b1;
    font-size: 12 * @rem;
    margin-top: 3 * @rem;
    padding: 10 * @rem 0 10 * @rem;
    line-height: 17 * @rem;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 17 * @rem;
      .gou {
        width: 8 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/select-no.png) center center no-repeat;
        background-size: 8 * @rem 8 * @rem;
        margin-right: 4 * @rem;
      }
    }
    &.remember {
      .content {
        .gou {
          background-image: url(~@/assets/images/select-yes.png);
        }
      }
    }
  }
  .operation {
    display: flex;
    align-items: center;
    padding: 0 22 * @rem;
    justify-content: space-between;
    margin: 2 * @rem auto 20 * @rem;
    .clock-btn {
      width: 110 * @rem;
      height: 34 * @rem;
      background: #f2f2f2;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 17 * @rem;
      font-size: 13 * @rem;
      color: #7d7d7d;
      &.svip {
        background: @themeBg;
        color: #fff;
      }
    }
  }
}
@keyframes downward {
  0% {
  }
  50% {
    transform: translate(0, 10px);
  }
  to {
    transform: translate(0, 0);
  }
}
.my-swipe {
  height: 197 * @rem;
  /deep/ .van-swipe__indicators {
    bottom: 8 * @rem;
    .van-swipe__indicator {
      width: 6 * @rem;
      height: 6 * @rem;
      border-radius: 3 * @rem;
      background: #d8d8d8;
      transition: 0.3;
    }
    .van-swipe__indicator--active {
      width: 16 * @rem;
      border-radius: 3 * @rem;
    }
  }
}
.swiper-item {
  width: 315 * @rem;
  height: 164 * @rem;
  margin: 8 * @rem auto 0;
  border-radius: 8 * @rem;
  overflow: hidden;
  background-color: #f8f8fa;
  .game-line {
    box-sizing: border-box;
    height: 108 * @rem;
    padding: 4 * @rem 10 * @rem;
  }
  .reward-line {
    box-sizing: border-box;
    height: 56 * @rem;
    padding: 7 * @rem 10 * @rem;
    background-color: #f1f2fa;
    display: flex;
    align-items: center;
    .reward-icon {
      width: 42 * @rem;
      height: 42 * @rem;
    }
    .reward-content {
      flex: 1;
      min-width: 0;
      margin: 0 8 * @rem;
      font-size: 12 * @rem;
      color: #666666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        color: @themeColor;
      }
    }
    .reward-btn {
      width: 50 * @rem;
      height: 22 * @rem;
      text-align: center;
      line-height: 22 * @rem;
      font-size: 11 * @rem;
      color: #fff1d7;
      border-radius: 11 * @rem;
      background-color: #ff7554;
    }
  }
  /deep/ .game-item-components {
    padding: 8 * @rem 0;
    .game-icon {
      width: 76 * @rem;
      height: 76 * @rem;
      flex: 0 0 76 * @rem;
    }
    .game-info {
      height: 70 * @rem;
      .game-name {
        font-size: 14 * @rem;
        font-weight: bold;
        line-height: 20 * @rem;
        .game-subtitle {
          border: none;
          background: #ffffff;
          font-size: 12 * @rem;
          color: #808080;
          padding: 3 * @rem 5 * @rem;
          border: 1 * @rem solid #f5f5f6;
          border-radius: 5 * @rem;
        }
      }
      .game-bottom {
        font-size: 11 * @rem;
        line-height: 16 * @rem;
        font-size: 12 * @rem;
        .score {
          font-weight: 600;
          color: @themeColor;
          &::before {
            content: '';
            display: block;
            width: 8 * @rem;
            height: 8 * @rem;
            margin-right: 4 * @rem;
            .image-bg('~@/assets/images/category/star.png');
          }
        }
      }
      .tags {
        height: unset;
        overflow: unset;
        .tag {
          background: #ffefee;
          border: 1px solid #ff471f;
          color: #ff471f;
          font-size: 12 * @rem;
          padding: 2 * @rem 4 * @rem;
        }
        .tag:nth-of-type(2n) {
          background: #fff8f4;
          color: #ff8a00;
          border: 1px solid #ff471f;
        }
        .tag:nth-of-type(n + 3) {
          display: none;
        }
      }
    }
  }
}
</style>
