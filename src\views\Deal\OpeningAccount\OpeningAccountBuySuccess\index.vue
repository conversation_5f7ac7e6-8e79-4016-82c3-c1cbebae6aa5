<template>
  <div class="page">
    <nav-bar-2 title="开局号" :border="true">
      <template #right>
        <div class="rule-btn btn" @click="clickRule">规则</div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="success-bar">
        <div class="success-icon">
          <img
            src="@/assets/images/deal/opening-account-success-icon.png"
            alt=""
          />
        </div>
        <div class="success-text">交易成功</div>
      </div>

      <div class="tip-container">
        <div class="tip-content">
          {{ tips }}
        </div>
      </div>
      <div class="bottom-absolute">
        <div class="close-btn btn" @click="goToGameDetail">关闭</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      id: 0,
      tips: '请在游戏中登录您的账号查收角色，如果看不到角色，请在游戏登录界面切换到所购买的小号',
    };
  },
  created() {
    this.id = this.$route.params.id;
    if (this.$route.params.msg) {
      this.tips = this.$route.params.msg;
    }
  },
  methods: {
    clickRule() {
      this.toPage('OpeningAccountExplain');
    },
    goToGameDetail() {
      this.toPage(
        'GameDetail',
        {
          id: this.id,
        },
        1,
      );
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .rule-btn {
    color: #32b768;
  }
  .main {
    padding: 0 18 * @rem;
    flex: 1;
    position: relative;
    .success-bar {
      padding-top: 82 * @rem;
      .success-icon {
        width: 57 * @rem;
        height: 42 * @rem;
        margin: 0 auto;
      }
      .success-text {
        font-size: 20 * @rem;
        color: #333333;
        line-height: 25 * @rem;
        margin-top: 18 * @rem;
        text-align: center;
      }
    }
    .tip-container {
      margin-top: 56 * @rem;
      padding: 0 40 * @rem;
      .tip-content {
        font-size: 12 * @rem;
        line-height: 20 * @rem;
        color: #999999;
        text-align: center;
      }
    }
  }
  .bottom-absolute {
    padding-bottom: 20 * @rem;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    .close-btn {
      width: 295 * @rem;
      height: 40 * @rem;
      border-radius: 20 * @rem;
      margin: 20 * @rem auto 0;
      background: linear-gradient(39deg, #ff4646 0%, #ff6a6a 100%);
      font-size: 15 * @rem;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
