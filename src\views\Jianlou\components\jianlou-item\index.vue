<template>
  <div class="jianlou-item-component">
    <div class="jianlou-item" @click="toJianlouDetail">
      <div class="top-info">
        <div class="top-left">
          <div class="game-icon">
            <img :src="jianlouInfo.game.titlepic" />
          </div>
        </div>
        <div class="top-center">
          <div class="game-name">
            {{ jianlouInfo.game.main_title
            }}<span class="game-subtitle" v-if="jianlouInfo.game.subtitle">{{
              jianlouInfo.game.subtitle
            }}</span>
          </div>
          <div class="game-area">
            {{ $t('区服') }}:{{ jianlouInfo.game_area }}
          </div>
          <div class="game-tag">
            <div class="platforms">
              <div
                class="plat"
                v-for="(item, index) in jianlouInfo.platforms"
                :key="index"
              >
                <img :src="item.icon" />
              </div>
            </div>

            <!-- <div class="can-sell btn" @click.stop="canSellDialog">可出售</div> -->
          </div>
        </div>
        <div class="top-right">
          ¥
          <span class="price">{{ Number(jianlouInfo.rmb).toFixed(0) }}</span>
        </div>
      </div>
      <div class="bottom-info">
        <div class="desc" v-if="!isSell">
          {{ $t('该小号创建') }}<span>{{ jianlouInfo.xh_days }}</span
          >{{ $t('天，实际充值') }}<span>{{ jianlouInfo.pay_sum_num }}</span
          >元
        </div>
        <div class="deal-time" v-else>
          {{ $t('成交时间') }}：{{ jianlouInfo.update_time | formatTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'JianlouItemComponent',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    isSell: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      jianlouInfo: this.info,
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  watch: {
    info(val) {
      this.jianlouInfo = val;
    },
  },
  methods: {
    toJianlouDetail() {
      this.toPage('JianlouDetail', {
        id: this.jianlouInfo.id,
      });
    },
    canSellDialog() {
      this.$dialog.alert({
        message: this.$t('该游戏支持小号出售服务'),
        confirmButtonText: this.$t('知道了'),
        confirmButtonColor: '#FE6600',
        lockScroll: false,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-item-component {
  .jianlou-item {
    padding: 10 * @rem;
    background: #ffffff;
    border-radius: 12 * @rem;
    .top-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .top-left {
        display: flex;
        align-items: center;
        .game-icon {
          width: 70 * @rem;
          height: 70 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
        }
      }
      .top-center {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .game-name {
          font-size: 16 * @rem;
          line-height: 22 * @rem;
          color: #000000;
          font-weight: 600;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          overflow: hidden;
          display: flex;
          align-items: center;
          .game-subtitle {
            box-sizing: border-box;
            border: 1 * @rem solid fade(@themeColor, 80);
            border-radius: 3 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 3 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            vertical-align: middle;
            line-height: 1;
          }
        }
        .game-area {
          font-size: 13 * @rem;
          line-height: 18 * @rem;
          color: #797979;
          margin-top: 4 * @rem;
        }
        .game-tag {
          display: flex;
          align-items: center;
          margin-top: 10 * @rem;
          .can-sell {
            box-sizing: border-box;
            height: 16 * @rem;
            padding: 0 4 * @rem;
            border: 0.5 * @rem solid @themeColor;
            font-size: 10 * @rem;
            color: @themeColor;
            border-radius: 4 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .platforms {
            display: flex;
            align-items: center;
            .plat {
              width: 16 * @rem;
              height: 16 * @rem;
              margin-right: 6 * @rem;
            }
          }
        }
      }
      .top-right {
        font-size: 12 * @rem;
        color: @themeColor;
        font-weight: 600;
        .price {
          font-size: 20 * @rem;
          font-weight: 600;
          color: @themeColor;
        }
      }
    }
    .bottom-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 13 * @rem;
      .desc {
        font-size: 12 * @rem;
        color: #929292;
        span {
          font-size: 12 * @rem;
          color: @themeColor;
        }
      }
      .deal-time {
        font-size: 13 * @rem;
        color: #999999;
      }
    }
  }
}
</style>
