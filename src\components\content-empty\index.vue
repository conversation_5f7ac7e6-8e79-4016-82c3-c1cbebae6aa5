<template>
  <div class="empty">
    <van-empty :description="tips" class="van-empty" :image="emptyImg">
      <div class="retry" @click="retry" v-if="showRetry">
        {{ $t('重试') }}
      </div>
    </van-empty>
  </div>
</template>

<script>
import emptyImg from '@/assets/images/empty.png';
export default {
  name: 'ContentEmpty',
  props: {
    tips: {
      type: String,
      default: '暂无数据',
    },
    showRetry: {
      type: Boolean,
      default: false,
    },
    emptyImg: {
      type: String,
      default: emptyImg,
    },
  },
  methods: {
    retry() {
      this.$emit('retry');
    },
  },
};
</script>

<style lang="less" scoped>
.empty {
  display: flex;
  justify-content: center;
  .van-empty {
    align-self: center;
    padding: 0;
    /deep/ .van-empty__description {
      margin-top: 5 * @rem;
    }
    /deep/ .van-empty__image img {
      object-fit: contain;
    }
  }
  .retry {
    font-size: 14 * @rem;
    text-decoration: underline;
    color: @themeColor;
  }
}
</style>
