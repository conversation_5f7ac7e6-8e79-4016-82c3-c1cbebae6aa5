import Vue from 'vue';
import cardpassCopyPopup from '@/components/cardpass-copy-popup/index.vue';

const CardpassConstructor = Vue.extend(cardpassCopyPopup);

function useCardpassCopyPopup(info, authXh = 0) {
  const cardpass = new CardpassConstructor();

  cardpass.$mount(document.createElement('div'));
  document.body.appendChild(cardpass.$el);

  cardpass.info = info;
  cardpass.authXh = authXh;
  cardpass.show = true;
}

Vue.prototype.$cardpassCopyPopup = useCardpassCopyPopup;

export default useCardpassCopyPopup;
