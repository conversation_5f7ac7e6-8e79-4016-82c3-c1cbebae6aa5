<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item">
      <div class="game-info">
        <game-item-2
          :showRight="false"
          :gameInfo="couponItem"
          class="conpon-game-item"
        ></game-item-2>
      </div>
      <div class="game-quan-get">
        <div class="money">
          ¥
          <span>{{ Number(couponItem.sum_money) }}</span>
        </div>
        <div class="total">
          {{ $t('共') }}{{ couponItem.count }}{{ $t('张') }}
        </div>
        <div
          class="get btn"
          @click="
            $router.push({
              name: 'GameCoupon',
              params: { game_id: couponItem.id },
            })
          "
        >
          {{ $t('领取') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameQuanItem',
  props: ['couponItem'],
  data() {
    return {
      item: {},
    };
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  margin-bottom: 10 * @rem;
  .game-quan-item {
    width: 347 * @rem;
    height: 94 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 12 * @rem;
    .game-info {
      padding-left: 12 * @rem;
      flex: 1;
      min-width: 0;

      /deep/ .conpon-game-item {
        .game-info .game-name .game-subtitle {
          border: 1 * @rem solid #e5e1ea;
          color: #888888;
        }
        .tags .tag {
          background-color: #fff;
        }
        .game-bottom .score {
          color: #ff5d5d;
          font-size: 13 * @rem;
          font-weight: bold;
        }    
      }
    }
    .game-quan-get {
      width: 90 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .money {
        font-size: 11 * @rem;
        color: #fe4a26;
        font-weight: 600;
        display: flex;
        align-items: center;
        span {
          font-size: 22 * @rem;
          font-weight: 600;
        }
      }
      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
      .get {
        font-size: 13 * @rem;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        background: @themeBg;
        width: 52 * @rem;
        height: 26 * @rem;
        border-radius: 6 * @rem;
        margin-top: 4 * @rem;
      }
    }
  }
}
</style>
