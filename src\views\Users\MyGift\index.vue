<template>
  <div class="page my-gift-page">
    <nav-bar-2 title="我的礼包" :border="true">
      <template #right>
        <div class="recovery-btn" @click="toPage('GiftRecovery')">回收站</div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="search-bar">
        <div class="search-container">
          <div class="search-icon"></div>
          <form @submit="handleSearch" class="form">
            <input
              class="input"
              type="text"
              v-model="searchInput"
              placeholder="请输入关键词查找"
            />
          </form>
        </div>
      </div>
      <yy-list
        class="my-gift-content"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
        :empty="empty"
      >
        <div class="gift-list">
          <van-swipe-cell
            v-for="(item, index) in giftList"
            :key="item.belong_id"
            class="gift-item-swipe"
            ref="giftItemRef"
          >
            <div
              class="gift-item"
              @click="
                toPage('GiftDetail', {
                  game_id: item.game_id,
                  gift_id: item.id,
                  xh_id: item.xh_id,
                })
              "
            >
              <div class="game-info">
                <div class="gift-icon">
                  <img :src="item.titlepic" :alt="item.titlegame" />
                </div>
                <div class="center">
                  <div class="gift-title">
                    <div class="main-title">{{ item.game_title }}</div>
                    <span>{{ item.subtitle }}</span>
                  </div>
                  <div class="gift-name">{{ item.title }}</div>
                  <div class="desc">{{ item.endtime | formatTime }}</div>
                </div>
              </div>
              <div class="gift-cardpass">
                <div class="cardpass">
                  礼包码：<span>{{ item.cardpass }}</span>
                </div>
                <div class="copy-btn btn" @click.stop="copy(item)">
                  复制
                </div>
              </div>
            </div>
            <template #right>
              <div
                class="delete-button"
                @click.stop="handleDelete(item, index)"
              >
                移入<br />回收站
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </yy-list>
      <!-- 复制成功弹窗 -->
      <cardpass-copy-success-popup
        :info="dialogInfo"
        :show.sync="copySuccessPopupShow"
      ></cardpass-copy-success-popup>
    </div>
  </div>
</template>

<script>
import { ApiCardMine, ApiCardLogToRecovery } from '@/api/views/card.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'MyGift',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      giftList: [],
      empty: false,
      searchInput: '', // 搜索关键词
      dialogInfo:{},
      copySuccessPopupShow:false,
    };
  },
  filters: {
    formatTime(val) {
      let { detail_time } = handleTimestamp(val);
      return `有效期至${detail_time}`;
    },
  },
  async created() {
    await this.getMyGiftList();
  },
  methods: {
    async handleSearch(e) {
      e.preventDefault();
      await this.getMyGiftList(1);
    },
    async getMyGiftList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      let res = await ApiCardMine({
        listRows: this.listRows,
        page: this.page,
        isDel: 0,
        name: this.searchInput,
      });
      this.loadingObj.loading = false;
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.giftList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.giftList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getMyGiftList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getMyGiftList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },

    copy(info) {
      this.$copyText(info.cardpass).then(
        res => {
          // this.$toast(this.$t('复制成功'));
          this.dialogInfo = info;
          this.copySuccessPopupShow = true;
        },
        err => {
          this.$toast(this.$t('复制失败，请手动复制'));
        },
      );
    },
    async handleDelete(item, index) {
      this.$toast.loading();
      try {
        const res = await ApiCardLogToRecovery({
          id: item.belong_id,
        });
        this.giftList.splice(index, 1);
      } finally {
      }
    },
  },
};
</script>

<style lang="less" scoped>
.my-gift-page {
  .recovery-btn {
    color: rgba(153, 153, 153, 1);
  }
  .main {
    flex: 1;
    background-color: #f5f5f6;
  }
  .search-bar {
    width: 100%;
    height: 56 * @rem;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    .search-container {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 0 6 * @rem;
      width: 339 * @rem;
      height: 32 * @rem;
      border-radius: 16 * @rem;
      background-color: rgba(102, 102, 102, 0.05);
      .search-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat center
          center;
        background-size: 14 * @rem 14 * @rem;
      }
      .form {
        flex: 1;
        min-width: 0;
        margin-left: 5 * @rem;
        .input {
          display: block;
          background: transparent;
          height: 32 * @rem;
          width: 100%;
          font-size: 14 * @rem;
          color: #000;
        }
      }
    }
  }
  .my-gift-content {
    .gift-list {
      padding: 12 * @rem 0;
      /deep/ .delete-button {
        height: 100% !important;
        background: @themeColor;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100 * @rem;
        text-align: center;
        font-size: 15 * @rem;
        color: #fff;
      }
      .gift-item-swipe {
        box-sizing: border-box;
        width: 339 * @rem;
        margin: 12 * @rem auto 0;
        background-color: #fff;
        border-radius: 10 * @rem;
        border: 1 * @rem solid rgba(238, 238, 238, 1);
        &:first-of-type {
          margin-top: 0;
        }
      }
      .gift-item {
        box-sizing: border-box;
        padding: 0 14 * @rem;
        .game-info {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          padding: 10 * @rem 0;
          border-bottom: 1 * @rem dashed rgba(204, 204, 204, 1);
          .gift-icon {
            width: 58 * @rem;
            height: 58 * @rem;
            border-radius: 12 * @rem;
            overflow: hidden;
          }
          .center {
            flex: 1;
            min-width: 0;
            margin-left: 8 * @rem;
            margin-right: 20 * @rem;
            .gift-title {
              display: flex;
              align-items: center;
              .main-title {
                font-size: 14 * @rem;
                color: #111111;
                line-height: 22 * @rem;
                font-weight: 600;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              span {
                white-space: nowrap;
                color: #808080;
                font-size: 10 * @rem;
                padding: 2 * @rem 4 * @rem;
                background-color: #f5f5f6;
                border-radius: 4 * @rem;
                border: 0.5px solid rgba(0, 0, 0, 0.1);
                margin-left: 5 * @rem;
              }
            }
            .gift-name {
              font-size: 12 * @rem;
              color: #777777;
              line-height: 15 * @rem;
              margin-top: 4 * @rem;
              font-weight: 500;
            }
            .desc {
              font-size: 10 * @rem;
              color: #999999;
              line-height: 13 * @rem;
              margin-top: 6 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
        .gift-cardpass {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10 * @rem 0;

          .cardpass {
            font-size: 13 * @rem;
            color: #333333;
            font-weight: 600;
            line-height: 14 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
            min-width: 0;
            span {
              color: #ff7b01;
              font-weight: 600;
            }
          }
          .copy-btn {
            width: 46 * @rem;
            height: 24 * @rem;
            background: @themeBg;
            border-radius: 4 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
