<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item">
      <div class="game-info">
        <game-item-2 :showRight="false" :gameInfo="couponItem"></game-item-2>
      </div>
      <div class="game-quan-get">
        <div class="money">
          ¥
          <span>{{ couponItem.coupon_money }}</span>
        </div>
        <div class="total">
          {{ $t('共') }}{{ couponItem.coupon_total }}{{ $t('张') }}
        </div>
        <div
          class="get btn"
          @click="
            $router.push({
              name: 'GameCoupon',
              params: { game_id: couponItem.id },
            })
          "
        >
          {{ $t('领券') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameQuanItem',
  props: ['couponItem'],
  data() {
    return {
      item: {},
    };
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  margin-top: 17 * @rem;
  .game-quan-item {
    width: 347 * @rem;
    height: 90 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 5 * @rem;
    .game-info {
      padding-left: 12 * @rem;
      flex: 1;
      min-width: 0;
    }
    .game-quan-get {
      width: 90 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .money {
        font-size: 12 * @rem;
        color: #ff3c3c;
        span {
          font-size: 16 * @rem;
        }
      }
      .total {
        font-size: 11 * @rem;
        color: #b3b3b3;
        margin-top: 8 * @rem;
      }
      .get {
        font-size: 13 * @rem;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(60deg, #ff4c39, #ff9c34);
        width: 65 * @rem;
        height: 25 * @rem;
        border-radius: 13 * @rem;
        margin-top: 5 * @rem;
      }
    }
  }
}
</style>
