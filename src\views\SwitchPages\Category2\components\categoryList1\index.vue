<template>
  <div class="tab-type1">
    <div class="container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
        <content-empty v-if="empty"></content-empty>
        <van-list
          v-else
          v-model="loading"
          :finished="finished"
          :finished-text="$t('没有更多了')"
          @load="loadMore()"
        >
          <div
            v-for="(item, index) in game_list"
            :key="index"
            class="item-container"
          >
            <game-item-2
              :gameInfo="item"
              class="game-item"
              :showRight="false"
            ></game-item-2>
            <!-- <div class="video-container" v-if="item.video_thumb">
              <video
                :src="item.video_url"
                :poster="item.video_thumb"
                :webkit-playsinline="true"
                :playsinline="true"
                :x5-playsinline="true"
                x-webkit-airplay="allow"
              >
                {{ $t("您的手机不支持该视频文件！！！") }}
              </video>
              <div
                v-if="item.video_url"
                @click="play(index)"
                class="play"
              ></div>
            </div> -->
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import { ApiGameRecommend } from '@/api/views/game.js';
import gameItem from '@/components/game-item';
export default {
  props: {},
  data() {
    return {
      game_list: [],
      finished: false,
      loading: false,
      reloading: false,
      refreshing: false,
      page: 1,
      player: null,
      empty: false,
    };
  },
  methods: {
    async getGameList() {
      const res = await ApiGameRecommend({ listRows: 10, page: this.page });
      if (this.page === 1) {
        this.game_list = [];
        if (!res.data.list.length) {
          this.empty = true;
        }
      }
      this.game_list.push(...res.data.list);
      if (res.data.list.length < 10) {
        this.finished = true;
      } else {
        this.finished = false;
      }
    },
    onChange() {
      this.getGameList();
    },
    async onRefresh() {
      this.page = 1;
      await this.getGameList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getGameList();
      this.loading = false;
      this.page++;
    },
    // play(index) {
    //   this.pauseAll();
    //   let $video = document.querySelector(
    //     `.item-container:nth-of-type(${index + 1}) video`
    //   );
    //   let $play = document.querySelector(
    //     `.item-container:nth-of-type(${index + 1}) .play`
    //   );
    //   $play.style.display = "none";
    //   $video.play();
    //   $video.setAttribute("controls", "true");
    // },
    // pauseAll() {
    //   let $videos = document.querySelectorAll(".item-container video");
    //   let $plays = document.querySelectorAll(".item-container .play");
    //   $videos.forEach((item) => {
    //     item.pause();
    //     item.removeAttribute("controls");
    //   });
    //   $plays.forEach((item) => {
    //     item.style.display = "block";
    //   });
    // },
  },
  components: {
    gameItem,
  },
};
</script>
<style lang="less" scoped>
.tab-type1 {
  display: flex;
  height: calc(100vh - 105 * @rem - @safeAreaTop - @safeAreaBottom);
  height: calc(100vh - 105 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
}
.container {
  display: flex;
  flex-flow: column;
  flex: 1;
  overflow: hidden;
  /deep/ .load-more {
    overflow-y: scroll;
  }
}
.list {
  flex: 1;
  overflow-y: scroll;
  // -webkit-overflow-scrolling: touch;
  ::-webkit-scrollbar {
    display: none;
  }
}
.item-container {
  margin: 14 * @rem;
  padding: 0 10 * @rem;
  border-radius: 12 * @rem;
  box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
  /deep/ .game-item-components {
    .game-icon {
      width: 58 * @rem;
      height: 58 * @rem;
      flex: 0 0 58 * @rem;
    }
    .game-info {
      height: unset;
      .game-name {
        font-size: 13 * @rem;
        font-weight: bold;
        line-height: 18 * @rem;
      }
      .game-bottom {
        font-size: 10 * @rem;
        margin-top: 3 * @rem;
      }
      .tags {
        margin-top: 3 * @rem;
      }
    }
  }
  video {
    width: 100%;
    border-radius: 14 * @rem;
    object-fit: cover;
  }
}
.video-container {
  position: relative;
}
.play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 38 * @rem;
  height: 38 * @rem;
  background-image: url(~@/assets/images/video-play.png);
  background-size: 100%;
}
</style>
