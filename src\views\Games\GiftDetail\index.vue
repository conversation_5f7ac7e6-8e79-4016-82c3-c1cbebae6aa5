<template>
  <div class="gift-detail-page page">
    <nav-bar-2 :title="$t('福利详情')" :border="true"></nav-bar-2>
    <div class="gift-container" v-if="giftInfo.game && giftInfo.title">
      <div class="gift-bar" @click="goToGame">
        <div class="game-pic">
          <img
            :src="giftInfo.game && giftInfo.game.titlepic"
            :alt="giftInfo.titlegame"
          />
        </div>
        <div class="right">
          <div class="gift-name">{{ giftInfo.title }}</div>
          <div class="game-name">{{ giftInfo.titlegame }}</div>
        </div>
      </div>

      <!-- 礼包码 -->
      <div class="gift-code" v-if="giftInfo.cardpass">
        <div class="code-text">
          <span></span>{{ $t('礼包码') }}：{{ giftInfo.cardpass }}
        </div>
        <div class="copy-btn btn" @click="copy(giftInfo.cardpass)">
          {{ $t('复制') }}
        </div>
      </div>

      <div class="info-content">
        <div class="info-item">
          <div class="name">{{ $t('礼包内容') }}：</div>
          <div class="text">
            <p>
              {{ giftInfo.cardbody }}
            </p>
          </div>
        </div>
        <div class="info-item">
          <div class="name">{{ $t('使用方法') }}：</div>
          <div class="text">
            <p>
              {{ giftInfo.cardtext }}
            </p>
          </div>
        </div>
        <div class="info-item">
          <div class="name">{{ $t('注意事项') }}：</div>
          <div class="text red">
            <p v-html="giftNotes"></p>
          </div>
        </div>
        <div class="info-item">
          <div class="name">{{ $t('礼包时间') }}：</div>
          <div class="text">
            <p>
              {{ $t('开始时间') }}：{{ getFullDateTime(giftInfo.starttime) }}
            </p>
            <p>{{ $t('结束时间') }}：{{ getFullDateTime(giftInfo.endtime) }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-container">
      <div class="get-bar">
        <div class="get-content">
          <div
            class="btn get-btn"
            @click="getGift()"
            v-if="giftInfo.id"
            :class="{
              had:
                giftInfo.cardpass ||
                (!giftInfo.cardpass && giftInfo.remain == 0),
            }"
          >
            {{
              giftInfo.cardpass
                ? $t('已领取')
                : giftInfo.remain != 0
                  ? [28].includes(giftInfo.classid)
                    ? '兑换'
                    : '领取'
                  : '被抢光'
            }}
          </div>
        </div>
        <bottom-safe-area></bottom-safe-area>
      </div>
    </div>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="false"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-text">礼包已领取，您的兑换码</div>
      </div>
      <div class="cardpass">{{ giftInfo.cardpass }}</div>
      <div class="desc" v-html="introduction"></div>
      <div class="copy-btn btn" @click="copy(giftInfo.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
      <div class="close-btn" @click="copyDialogShow = false"></div>
    </van-dialog>
    <!-- 复制成功弹窗 -->
    <cardpass-copy-success-popup
      :info="giftInfo"
      :show.sync="copySuccessPopupShow"
    ></cardpass-copy-success-popup>
    <!-- 小号选择弹窗 -->
    <xh-select-dialog
      :show.sync="xhDialogShow"
      :id="Number(gameId)"
      @handleGet="getGift"
    ></xh-select-dialog>
  </div>
</template>

<script>
import { ApiCardRead, ApiCardGet } from '@/api/views/gift.js';
import { ApiXiaohaoMyListByCard } from '@/api/views/xiaohao.js';
import { mapGetters } from 'vuex';

import xhSelectDialog from '@/components/xh-select-dialog/index.vue';

export default {
  name: 'GiftDetail',
  components: {
    xhSelectDialog,
  },
  data() {
    return {
      gameId: 0,
      giftId: 0,
      xhId: 0,
      giftInfo: {},
      xhDialogShow: false, //小号选择弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      currentXiaohao: {}, //当前选择小号
      copySuccessPopupShow: false,
      copyDialogShow: false,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    giftNotes() {
      if (this.giftInfo && this.giftInfo.notes) {
        return this.giftInfo.notes.replace(/\r\n/g, '<br/>');
      }
    },
    introduction() {
      return `${this.$t('使用说明')}：${this.giftInfo.cardtext}`;
    },
  },
  created() {
    this.giftId = this.$route.params.gift_id;
    this.gameId = this.$route.params.game_id;
  },
  async activated() {
    if (this.$route.params.xh_id || this.xiaohaoMap[this.gameId]?.id) {
      this.xhId =
        this.$route.params.xh_id || this.xiaohaoMap[this.gameId]?.id || '';
    }
    await this.getGiftInfo();

    if ([28].includes(this.giftInfo.classid)) {
      //神策埋点
      this.$sensorsTrack('benefitsPackage_pageView');
    }
  },
  methods: {
    async getGiftInfo() {
      const res = await ApiCardRead({
        cardId: this.giftId,
        xhId: this.xhId || '',
      });
      this.giftInfo = res.data;
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          // this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
          this.copySuccessPopupShow = true;
        },
        err => {
          this.$toast(this.$t('复制失败，请手动复制'));
        },
      );
    },
    getGift(id) {
      if([28].includes(this.giftInfo.classid)) {
        this.$sensorsTrack('gameSwitching_detailsPayment_submit', {
          game_id: `${this.giftInfo.game_id}`,
          game_name: this.giftInfo.titlegame,
          game_type: `${this.giftInfo.classid}`,
          package_name: this.giftInfo.title,
          Package_id: `${this.giftInfo.id}`,
        });
      }
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (this.giftInfo.cardpass) {
          this.$toast(this.$t('亲，当前账号已经领取过该礼包了~'));
          return false;
        }
        if (this.giftInfo.remain == 0) {
          this.$toast(this.$t('礼包已被抢光啦~'));
          return false;
        }
        if (!this.xhId && ![28, 30].includes(this.giftInfo.classid)) {
          this.$dialog
            .confirm({
              title: this.$t('提示'),
              message: this.$t('请先下载游戏并登录创建游戏账号哦！'),
              confirmButtonText: this.$t('下载游戏'),
              cancelButtonText: this.$t('稍后领取'),
              lockScroll: false,
            })
            .then(() => {
              this.toPage('GameDetail', {
                id: this.gameId,
              });
            });
          return false;
        }
        if (this.giftInfo.is_main_account == 0 && !this.xhId && !id) {
          // 没有小号的话在这个页面弹窗小号选择框
          this.xhDialogShow = true;
          return false;
        }

        this.$toast.loading({
          message: this.$t('加载中...'),
        });

        ApiCardGet({
          cardId: this.giftId,
          xhId: this.xhId || id || '',
        })
          .then(() => {
            this.$toast.clear();

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${this.giftInfo.game_id}`,
              adv_id: '暂无',
              game_name: this.giftInfo.titlegame,
              game_type: `${this.giftInfo.classid}`,
              game_size: '暂无',
              reward_type: this.giftInfo.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });
          })
          .catch(e => {
            this.$toast.clear();
            this.$dialog
              .confirm({
                title: '提示',
                message: e.msg,
                confirmButtonText: '打开游戏',
              })
              .then(() => {
                this.toPage('GameDetail', { id: this.giftInfo.game_id });
              });
          })
          .finally(async () => {
            await this.getGiftInfo();
            if (this.giftInfo.cardpass) {
              this.copyDialogShow = true;
            }
          });
      }
    },
    getFullDateTime(timeStemp) {
      let date = new Date(timeStemp * 1000);
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      return `${year}-${this.addZero(month)}-${this.addZero(
        day,
      )}  ${this.addZero(hour)}${this.$t('时')}${this.addZero(minute)}${this.$t(
        '分',
      )}`;
    },
    addZero(num) {
      return num < 10 ? '0' + num : num;
    },
    getChargeGift() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        // this.$toast.loading({
        //   message: "拼命加载中...",
        //   forbidClick: true,
        //   lockScroll: false,
        // });
        ApiXiaohaoMyListByCard({
          cardId: this.giftInfo.id,
        }).then(
          res => {
            // this.$toast.clear();
            this.xiaohaoList = res.data.list;
            this.currentXiaohao = this.xiaohaoList[0];
            this.xhDialogShow = true;
            this.currentGiftId = this.giftInfo.id;
          },
          () => {},
        );
      }
    },
    xiaoHaoListClick(item) {
      this.currentXiaohao = item;
      this.xiaohaoListShow = false;
    },
    handleXhGift() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        ApiCardGet({
          cardId: this.currentGiftId,
          xhId: this.currentXiaohao.id,
        }).then(
          res => {
            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${this.giftInfo.game_id}`,
              adv_id: '暂无',
              game_name: this.giftInfo.titlegame,
              game_type: `${this.giftInfo.classid}`,
              game_size: '暂无',
              reward_type: this.giftInfo.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });
          },
          () => {},
        );
      }
    },
    goToGame() {
      this.toPage('GameDetail', {
        id: this.gameId,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.gift-detail-page {
  .gift-container {
    padding: 14 * @rem 18 * @rem;
    .gift-bar {
      display: flex;
      padding: 5 * @rem 0;
      .game-pic {
        width: 64 * @rem;
        height: 64 * @rem;
        border-radius: 10 * @rem;
        background-color: #eeeeee;
        overflow: hidden;
      }
      .right {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 15 * @rem;
        .gift-name {
          font-size: 16 * @rem;
          line-height: 18 * @rem;
          color: #000000;
          font-weight: 600;
          max-height: 36 * @rem;
        }
        .game-name {
          font-size: 13 * @rem;
          color: #757575;
          margin-top: 10 * @rem;
        }
      }
    }

    .gift-code {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8 * @rem 12 * @rem;
      background-color: #f2f2f2;
      margin-top: 17 * @rem;
      border-radius: 6 * @rem;
      .code-text {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        font-size: 13 * @rem;
        color: #000;
        font-weight: 400;
        word-break: break-all;
        span {
          flex: 0 0 18 * @rem;
          display: block;
          width: 18 * @rem;
          height: 18 * @rem;
          .image-bg('~@/assets/images/games/gift-title-icon.png');
          margin-right: 5 * @rem;
        }
      }
      .copy-btn {
        width: 52 * @rem;
        height: 25 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        background-color: #ffbf13;
        background: @themeBg;
        border-radius: 13 * @rem;
      }
    }
    .info-content {
      padding: 20 * @rem 0 10 * @rem;
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20 * @rem;
        .name {
          width: 80 * @rem;
          font-size: 14 * @rem;
          color: #999999;
          line-height: 20 * @rem;
        }
        .text {
          flex: 1;
          min-width: 0;
          font-size: 14 * @rem;
          color: #000000;
          line-height: 20 * @rem;
          &.red {
            color: @themeColor;
          }
          p {
            margin-bottom: 2 * @rem;
          }
        }
      }
    }
  }

  .bottom-container {
    height: calc(65 * @rem + @safeAreaBottom);
    height: calc(65 * @rem + @safeAreaBottomEnv);
  }
  .get-bar {
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 2000;

    .get-content {
      height: 65 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      .get-btn {
        width: 230 * @rem;
        height: 48 * @rem;
        background: @themeColor;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        font-size: 16 * @rem;
        font-weight: bold;
        border-radius: 24 * @rem;
        &.had {
          background: #c1c1c1;
        }
      }
    }
  }
  /deep/ .van-button {
    border-radius: 16px;
  }
  .copy-dialog {
    box-sizing: border-box;
    width: 300 * @rem;
    border-radius: 12 * @rem;
    background-color: #fff;
    padding: 30 * @rem;
    overflow: unset;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;

      .title-text {
        line-height: 20 * @rem;
        font-size: 16 * @rem;
        color: #333333;
        font-weight: 600;
      }
    }
    .cardpass {
      box-sizing: border-box;
      width: 100%;
      height: 70 * @rem;
      background-color: #f5f5f5;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 0 10 * @rem;
      margin-top: 27 * @rem;
      font-size: 16 * @rem;
      line-height: 22 * @rem;
      color: #333333;
      font-weight: 600;
      word-break: break-all;
    }
    .desc {
      font-size: 11 * @rem;
      line-height: 15 * @rem;
      color: #777777;
      font-weight: 400;
      margin-top: 10 * @rem;
      text-align: center;
      word-break: break-all;
    }
    .copy-btn {
      width: 238 * @rem;
      height: 44 * @rem;
      margin: 21 * @rem auto 0;
      background: @themeBg;
      border-radius: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #ffffff;
    }
    .close-btn {
      width: 32 * @rem;
      height: 32 * @rem;
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translateX(-50%);
      z-index: 2;
      .image-bg('~@/assets/images/games/get-gift-popup-close-btn.png');
    }
  }
  .xh-dialog {
    overflow: unset;
    .center {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 30 * @rem 0;
    }
    .left,
    .right {
      position: relative;
      text-align: center;
      flex: 0 0 50%;
      line-height: 30 * @rem;
    }
    .right {
      .text {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #999999;
        span {
          display: block;
        }
      }
    }
    .xiaohao-list {
      display: none;
      position: absolute;
      top: 30 * @rem;
      left: 5%;
      z-index: 2000;
      width: 90%;
      border-radius: 10 * @rem;
      background: #fff;
      box-shadow: 0 0 3 * @rem #6e6e6e;
      &.on {
        display: block;
      }
      .xiaohao-item {
        text-align: center;
        line-height: 30 * @rem;
      }
    }
    .more-text-icon {
      width: 10 * @rem;
      height: 6 * @rem;
      background: url(~@/assets/images/games/bottom-arrow.png) center center
        no-repeat;
      background-size: 10 * @rem 6 * @rem;
      margin-left: 4 * @rem;
      transition: 0.3s;
      &.on {
        transform: rotateZ(180deg);
      }
    }
  }
}
</style>
