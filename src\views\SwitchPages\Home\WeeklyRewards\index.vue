<template>
  <div class="weekly-rewards-page">
    <rubber-band :topColor="bgc" bottomColor="#ffffff">
      <div class="placeholder" :style="{ background: bgc }"></div>
      <div class="game-panel" @click="tapBanner(info)">
        <div
          class="top-shadow"
          :style="{
            background: topShadow,
          }"
        ></div>
        <img
          class="game-banner"
          src="@/assets/images/weekly-rewards/weekly-rewards-bg.png"
          alt=""
        />
      </div>
      <div class="main" v-if="weekList.length">
        <div class="title-container">
          <div class="title">{{ weekList[current].desc }}</div>
        </div>
        <div class="content-container">
          <div class="day-list">
            <div
              class="day-item"
              :class="{ current: current == index }"
              v-for="(item, index) in weekList"
              :key="index"
              @click="clickDayItem(index)"
            >
              <div class="tag">{{ item.title }}</div>
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="desc">{{ item.desc }}</div>
            </div>
          </div>
          <div class="welfare-list">
            <div
              class="welfare-item"
              v-for="(item, index) in welfareList"
              :key="index"
            >
              <div class="welfare-tag">{{ item.small_title }}</div>
              <div class="welfare-title">{{ item.title }}</div>
              <div class="welfare-content">
                <div class="line">
                  <span>参与对象：</span>
                  <div class="line-desc">{{ item.user_desc }}</div>
                </div>
                <div class="line" v-if="item.privilege">
                  <span>福利特权：</span>
                  <div class="line-desc">{{ item.privilege }}</div>
                </div>
                <div class="line">
                  <span>福利时间：</span>
                  <div class="line-desc">{{ item.time_desc }}</div>
                </div>
              </div>
              <div
                class="welfare-btn"
                :class="{ cant: item.status == 0 }"
                @click="takeWelfare(item)"
              >
                {{ item.submit_title }}
              </div>
            </div>
          </div>
          <div class="welfare-introduction">
            <div class="intro-title">特权说明</div>
            <div class="intro-content" v-html="weekList[current].content"></div>
          </div>
        </div>
      </div>
    </rubber-band>
  </div>
</template>

<script>
import {
  ApiWeekWelfareIndex,
  ApiWeekWelfareItem,
  ApiWeekWelfareTake,
} from '@/api/views/weekWelfare.js';

export default {
  data() {
    return {
      bgc: '',
      weekList: [],
      welfareList: [],
      current: 0, // 当前选中的项
    };
  },
  computed: {
    topShadow() {
      return `linear-gradient(360deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    hexToRgba() {
      return `rgba(${parseInt('0x' + this.bgc.slice(1, 3))},${parseInt(
        '0x' + this.bgc.slice(3, 5),
      )},${parseInt('0x' + this.bgc.slice(5, 7))},0)`;
    },
  },

  async created() {
    this.bgc = '#A975EB';
  },
  async activated() {
    await this.getIndex();
    this.getTodayCurrent();
    await this.getWelfare();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async clickDayItem(index) {
      if (index != this.current) {
        this.current = index;
        await this.getWelfare();
      }
    },
    async getIndex() {
      const res = await ApiWeekWelfareIndex();
      this.weekList = res.data.week_list;
    },
    async getWelfare() {
      const res = await ApiWeekWelfareItem({
        type: this.current + 1,
      });
      this.welfareList = res.data.info;
    },
    getTodayCurrent() {
      let day = new Date().getDay();
      this.current = this.weekList.findIndex(item => {
        return item.default_check.includes(day);
      });
    },
    async takeWelfare(item) {
      switch (item.action_code) {
        case 1: // 领取福利
          try {
            const res = await ApiWeekWelfareTake({ type: item.type });
          } finally {
            await this.getWelfare();
          }
          break;
        case 2: // 前往财富等级
          this.toPage('PayHelp');
          break;
        default:
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.weekly-rewards-page {
  min-height: 100vh;
  padding-bottom: 40 * @rem;
  background-color: #fff;
  .placeholder {
    position: relative;
    display: block;
    width: 100%;
    height: calc(56 * @rem + @safeAreaTop);
    height: calc(56 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .game-panel {
    position: relative;
    width: 100%;
    height: 308 * @rem;
    padding-top: 45 * @rem;
    z-index: 2;
    .top-shadow {
      width: 100%;
      height: 150 * @rem;
      position: absolute;
      left: 0;
      top: -1 * @rem;
      z-index: 1;
    }
    .game-banner {
      position: relative;
      z-index: 2;
    }
  }
}
.main {
  margin-top: -120 * @rem;
  position: relative;
  z-index: 3;
  .title-container {
    width: 100%;
    height: 55 * @rem;
    background: url(~@/assets/images/weekly-rewards/title-bg.png) center center
      no-repeat;
    background-size: 100% 55 * @rem;
  }
  .title {
    text-align: center;
    line-height: 40 * @rem;
    font-size: 18 * @rem;
    font-weight: bold;
    background: linear-gradient(180deg, #a96fcc 2%, #713496 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .content-container {
    background-color: #fff;
  }
  .day-list {
    padding: 0 18 * @rem;
    display: flex;
    justify-content: space-between;
    .day-item {
      box-sizing: border-box;
      position: relative;
      width: 80 * @rem;
      border-radius: 8 * @rem;
      border: 1 * @rem solid #e7d4ff;
      background: linear-gradient(
        180deg,
        #fdeeff 0%,
        rgba(253, 238, 255, 0) 100%
      );
      &.current {
        border-color: #ffa194;
        background: linear-gradient(
          180deg,
          #ffdfcd 0%,
          rgba(255, 223, 205, 0.08) 92%,
          rgba(255, 223, 205, 0) 100%
        );
      }
      .tag {
        width: 54 * @rem;
        height: 18 * @rem;
        background: linear-gradient(93deg, #865ca0 0%, #e25fba 93%);
        border-radius: 8 * @rem 0 8 * @rem 0;
        position: absolute;
        left: -1 * @rem;
        top: -1 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #ffffff;
        font-weight: bold;
      }
      .icon {
        height: 32 * @rem;
        width: 100%;
        margin-top: 28 * @rem;
        img {
          height: 32 * @rem;
          width: auto;
          margin: 0 auto;
        }
      }
      .desc {
        font-size: 12 * @rem;
        font-weight: bold;
        line-height: 15 * @rem;
        color: #724d89;
        text-align: center;
        margin-top: 8 * @rem;
        margin-bottom: 10 * @rem;
      }
    }
  }
  .welfare-list {
    padding: 0 18 * @rem;
    .welfare-item {
      position: relative;
      border: 2 * @rem solid #e7d5ff;
      border-radius: 8 * @rem;
      margin-top: 25 * @rem;
      padding: 30 * @rem 14 * @rem 16 * @rem;
      .welfare-tag {
        box-sizing: border-box;
        padding-right: 4 * @rem;
        position: absolute;
        left: 11 * @rem;
        top: -11 * @rem;
        width: 58 * @rem;
        height: 23 * @rem;
        background: url(~@/assets/images/weekly-rewards/welfare-tag-bg.png);
        background-size: 58 * @rem 23 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: bold;
      }
      .welfare-title {
        font-size: 16 * @rem;
        color: #724d89;
        font-weight: bold;
      }
      .welfare-content {
        padding: 0 9 * @rem 11 * @rem;
        overflow: hidden;
        margin-top: 15 * @rem;
        background: linear-gradient(
          133deg,
          rgba(255, 241, 238, 0) 0%,
          #fff1ee 100%
        );
        border-radius: 8 * @rem;
        .line {
          margin-top: 11 * @rem;
          font-size: 13 * @rem;
          line-height: 16 * @rem;
          color: #724d89;
          display: flex;
          span {
            line-height: 16 * @rem;
          }
          .line-desc {
            flex: 1;
            min-width: 0;
            line-height: 16 * @rem;
          }
        }
      }
      .welfare-btn {
        width: 200 * @rem;
        height: 36 * @rem;
        border-radius: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        font-weight: bold;
        color: #c1422b;
        margin: 14 * @rem auto 0;
        background: linear-gradient(90deg, #fedf6c 0%, #fe9800 100%);
        &.cant {
          background: #eeecee;
          color: #d8d8d8;
        }
      }
    }
  }
  .welfare-introduction {
    margin-top: 20 * @rem;
    .intro-title {
      text-align: center;
      font-size: 16 * @rem;
      color: #724d89;
      font-weight: bold;
      line-height: 20 * @rem;
      background: url(~@/assets/images/weekly-rewards/intro-title-bg.png) center
        center no-repeat;
      background-size: 188 * @rem 3 * @rem;
    }
    .intro-content {
      line-height: 19 * @rem;
      font-size: 15 * @rem;
      color: #724d89;
      padding: 19 * @rem 18 * @rem;
    }
  }
}
</style>
