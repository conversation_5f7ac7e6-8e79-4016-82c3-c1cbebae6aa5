import Vue from 'vue';
import toastComponent from './index.vue';

// 返回一个 扩展实例构造器
const ToastConstructor = Vue.extend(toastComponent);

// 用于存储当前的 toast 实例
let currentToast = null;

// 定义弹出组件的函数 接收2个参数, 要显示的集合 和 显示时间
function useTaskToast(options, duration = 1500) {
  // 如果当前有一个 toast 显示中，则先移除它
  if (currentToast) {
    currentToast.showWrap = false;

    // 销毁之前，确保 DOM 节点存在
    if (currentToast.$el && currentToast.$el.parentNode) {
      currentToast.$el.parentNode.removeChild(currentToast.$el);
    }

    currentToast.$destroy();
    currentToast = null;
  }

  // 实例化一个 toast.vue
  const toastDom = new ToastConstructor({
    el: document.createElement('div'),
    data() {
      return {
        msg: options.msg || '',
        showWrap: true,
        showContent: true,
      };
    },
  });

  // 把 实例化的 toast.vue 添加到 body 里
  document.body.appendChild(toastDom.$el);

  // 设置当前的 toast 实例
  currentToast = toastDom;

  setTimeout(() => {
    toastDom.showContent = false;
  }, duration - 250);

  // 过了 duration 时间后隐藏并移除整个组件
  setTimeout(() => {
    toastDom.showWrap = false;

    // 销毁之前，确保 DOM 节点存在
    if (toastDom.$el && toastDom.$el.parentNode) {
      toastDom.$el.parentNode.removeChild(toastDom.$el);
    }

    toastDom.$destroy();
    currentToast = null; // 清空当前的 toast 实例
  }, duration);
}

export default useTaskToast;
