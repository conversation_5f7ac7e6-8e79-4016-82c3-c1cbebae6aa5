<template>
  <div class="container">
    <div class="content-container">
      <div class="top-bar">
        <p></p>
        <span>{{ my_subtitle }}</span>
      </div>
      <div class="empty-container" v-if="empty">
        <img
          class="empty-pic"
          src="@/assets/images/welfare/bounty-empty.png"
          alt=""
        />
        <div class="empty-desc">
          当前尚未领取任务,请前往 <span>任务列表</span> 领取
        </div>
        <div class="empty-btn" @click="$emit('toTaskList')">立即领取</div>
      </div>
      <div class="loading" v-if="!taskList.length && !empty"
        ><van-loading vertical>加载中...</van-loading></div
      >
      <yy-list
        class="task-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="false"
        :check="false"
        v-else-if="taskList.length"
      >
        <template v-for="(item, index) in taskList">
          <bounty-task-item
            class="task-item"
            :info="item"
            :key="index"
          ></bounty-task-item>
        </template>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiBountyTaskGetMyTask } from '@/api/views/bounty.js';
import bountyTaskItem from '@/components/bounty-task-item';
export default {
  components: {
    bountyTaskItem,
  },
  props: {
    my_subtitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 任务列表
      taskList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  async created() {
    await this.getTaskList();
  },
  methods: {
    async getTaskList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }

      const res = await ApiBountyTaskGetMyTask({
        page: this.page,
        listRows: this.listRows,
      });

      if (action == 1 || this.page == 1) {
        this.taskList = [];
      }

      this.taskList.push(...res.data.taskList);
      if (!this.taskList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (res.data.taskList.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished == true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getTaskList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.taskList.length) {
        await this.getTaskList();
      } else {
        await this.getTaskList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  padding-bottom: 20 * @rem;
  .content-container {
    box-sizing: border-box;
    border-radius: 12 * @rem;
    width: 355 * @rem;
    min-height: 385 * @rem;
    background: #fff;
    margin: 0 auto 0;
    position: relative;    
    overflow: hidden;
    .top-bar {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;

      p {
        background: url(~@/assets/images/welfare/bounty-mission-bg.png)
          no-repeat center 0;
        width: 100%;
        height: 38 * @rem;
      }

      span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -65%);
        font-weight: 600;
        font-size: 15 * @rem;
        color: #284f86;
      }
    }
    .empty-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 42 * @rem;
      padding-bottom: 20 * @rem;
      .empty-pic {
        width: 159 * @rem;
        height: 105 * @rem;
      }
      .empty-desc {
        margin-top: 18 * @rem;
        font-size: 13 * @rem;
        color: #999999;
        line-height: 18 * @rem;
        span {
          color: #456fab;
        }
      }
      .empty-btn {
        width: 140 * @rem;
        height: 38 * @rem;
        line-height: 38 * @rem;
        border-radius: 6 * @rem;
        font-size: 15 * @rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        background-color: @themeColor;
        background: linear-gradient(235deg, #6ddc8c 0%, #21b98a 100%);
        border-radius: 24 * @rem;
        border: 0px solid;
        border-image: linear-gradient(
            180deg,
            rgba(255, 248, 183, 1),
            rgba(255, 255, 255, 0)
          )
          0 0;
        margin-top: 41 * @rem;
      }
    }
    .task-title-container {
      height: 20 * @rem;
      width: 244 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin: 0 * @rem auto 0;
      font-size: 16 * @rem;
      color: #e85230;
      font-weight: 600;
      &::before {
        content: '';
        width: 80 * @rem;
        height: 3 * @rem;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url(~@/assets/images/welfare/bounty-section-title-left-bg.png)
          no-repeat;
        background-size: 80 * @rem 3 * @rem;
      }
      &::after {
        content: '';
        width: 80 * @rem;
        height: 3 * @rem;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: url(~@/assets/images/welfare/bounty-section-title-right-bg.png)
          no-repeat;
        background-size: 80 * @rem 3 * @rem;
      }
    }
    .loading {
      display: flex;
      align-content: center;
      justify-content: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .task-list {
      padding-top: 6 * @rem;
      background-color: #fff;
      margin: 0 10 * @rem 0;
      border-radius: 8 * @rem;    
    }
  }
}
</style>
