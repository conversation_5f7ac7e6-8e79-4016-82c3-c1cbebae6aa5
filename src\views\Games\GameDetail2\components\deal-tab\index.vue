<template>
  <div class="deal-tab" :class="{ centered: empty }">
    <content-empty
      v-if="empty"
      :tips="tips"
      :emptyImg="emptyImg"
    ></content-empty>
    <load-more
      v-else
      class="list-container"
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="deal-list">
        <div
          class="deal-item"
          v-for="(deal, dealIndex) in dealList"
          :key="dealIndex"
        >
          <deal-item :info="deal"></deal-item>
        </div>
      </div>
    </load-more>
  </div>
</template>

<script>
import { ApiXiaohaoTradeList } from '@/api/views/xiaohao.js';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
export default {
  name: 'dealTab',
  props: {
    gameId: {
      required: true,
    },
  },
  data() {
    return {
      dealList: [],
      finished: false,
      loading: false,
      empty: false,
      page: 1,
      listRows: 10,
      tips: '暂无交易动态',
      emptyImg,
    };
  },
  async created() {
    await this.getDealList();
  },
  methods: {
    async getDealList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiXiaohaoTradeList({
        isDone: 0,
        deviceFrom: 0,
        gameId: this.gameId,
        page: this.page,
        listRows: this.listRows,
      });
      let { count, list } = res.data;
      if (action === 1 || this.page === 1) {
        this.dealList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.dealList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getDealList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.deal-tab {
  height: 100%;
  background: #f7f8fa;
  &.centered {
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    background: #f7f8fa;
  }
  .list-container {
    .deal-list {
      padding-top: 12 * @rem;
      background: #f7f8fa;
      .deal-item {
        margin: 0 12 * @rem;
        background: #ffffff;
        border-radius: 8 * @rem;
        &:not(:first-child) {
          margin-top: 12 * @rem;
        }
      }
    }
  }
}
</style>
