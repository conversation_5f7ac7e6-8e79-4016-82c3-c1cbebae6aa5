<template>
  <div ref="saveDom" class="change-phone-page page">
    <nav-bar-2 :border="true" :title="title"></nav-bar-2>
    <div class="change-content">
      <div class="field">
        <input
          type="number"
          v-model="phone"
          :disabled="disabled"
          :placeholder="$t('请输入手机号码')"
          :class="{ pdr115: !userInfo.mobile }"
        />
        <div class="tel-right" v-if="!userInfo.mobile">
          <div
            class="clear"
            :class="{ transparent: phone == '' }"
            @click="phone = ''"
          ></div>
          <div class="country-code" @click="toPage('AreaCode')">
            <div class="country-code-text">+{{ areaCode }}</div>
            <div class="arrow-down"></div>
          </div>
        </div>
      </div>
      <div class="field">
        <input
          type="number"
          v-model="authCode"
          :placeholder="$t('请输入验证码')"
        />
        <div class="text" v-if="!ifCount" @click="captchaClick()">
          {{ $t('获取验证码') }}
        </div>
        <div class="text" v-else>
          {{ `${$t('重新获取')}${countdown}s` }}
        </div>
      </div>
    </div>
    <div class="save btn" @click="save">
      {{ userInfo.mobile ? $t('解除绑定') : $t('绑定') }}
    </div>
    <van-dialog
      v-model="show"
      :title="$t('重要提醒')"
      show-cancel-button
      :cancel-button-text="$t('再想想')"
      show-confirm-button
      :confirm-button-text="$t('确定解绑')"
      :confirm-button-color="themeColorLess"
      :lockScroll="false"
      @confirm="unbind()"
      class="unbind-popup"
    >
      <div class="text">
        {{
          $t('解绑后手机将不能使用手机号码登录APP和游戏，请一定牢记您的用户名')
        }}:{{ userInfo.username }}
      </div>
      <!-- <div class="small-text" @click="savePic()">保存到相册</div> -->
    </van-dialog>
  </div>
</template>

<script>
import { ApiUnBindPhone, ApibindPhone, ApiAuthCode } from '@/api/views/users';
import { mapActions } from 'vuex';
import { themeColorLess } from '@/common/styles/_variable.less';
import html2Canvas from 'html2canvas';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'ChangePhone',
  data() {
    return {
      phone: '', //手机号码
      authCode: '', //验证码
      countdown: 60, //倒计时
      ifCount: false, //倒计时开关
      disabled: false, //手机栏是否可以编辑
      show: false, //确定解绑的弹窗是否打开
      themeColorLess, //主题色
      imgUrl: '', //生成的图片地址
      captcha: null, //生成验证
    };
  },
  computed: {
    title() {
      if (this.userInfo.mobile) {
        return this.$t('解绑手机');
      } else {
        return this.$t('绑定手机');
      }
    },
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
  },
  watch: {
    authCode() {
      if (this.authCode > 1000000)
        this.authCode = Math.floor(this.authCode / 10);
    },
  },
  created() {
    if (this.userInfo.mobile) {
      this.phone = this.userInfo.mobile;
      this.disabled = true;
      this.setAreaCode(this.userInfo.country_code);
    }
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    async save() {
      if (!this.authCode) {
        this.$toast(this.$t('请输入验证码'));
        return false;
      }
      if (!!this.userInfo.mobile) {
        this.show = true;
      } else {
        let res = await ApibindPhone({
          phone: this.phone,
          code: this.authCode,
          countryCode: this.areaCode,
        });
        this.$toast(res.msg);
        this.disabled = !this.disabled;
        this.SET_USER_INFO();
        this.$router.go(-1);
      }
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    async getAuthCode(captcha) {
      // 获取验证码
      let params = {
        phone: this.phone,
      };
      params.type = !!this.userInfo.mobile ? 6 : 5;
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      if (this.areaCode) {
        params.countryCode = this.areaCode;
      }
      let res = await ApiAuthCode(params);
      this.$toast(res.msg);
      // 出现倒计时，颜色变暗
      this.ifCount = !this.ifCount;
      let fun = setInterval(() => {
        this.countdown--;
        if (this.countdown === -1) {
          clearInterval(fun);
          this.countdown = 60;
          this.ifCount = !this.ifCount;
        }
      }, 1000);
    },
    async unbind() {
      await ApiUnBindPhone({
        phone: this.phone,
        code: this.authCode,
        countryCode: this.areaCode,
      });
      this.disabled = !this.disabled;
      this.SET_USER_INFO();
      this.$router.go(-1);
    },
    savePic() {
      html2Canvas(this.$refs.saveDom).then(canvas => {
        // 转成图片，生成图片地址
        this.imgUrl = canvas.toDataURL('image/png');
        // 创建隐藏的可下载链接
        let eleLink = document.createElement('a');
        eleLink.href = this.imgUrl; // 转换后的图片地址
        eleLink.download = 'pictureName';
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.change-phone-page {
  .change-content {
    padding: 0 17 * @rem;
    margin-top: 30 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      height: 44 * @rem;
      border-radius: 22 * @rem;
      overflow: hidden;
      position: relative;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      .tel-right {
        width: 113 * @rem;
        height: 42 * @rem;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        .clear {
          width: 16 * @rem;
          height: 42 * @rem;
          padding: 0 10 * @rem;
          background-image: url(~@/assets/images/users/keyword-clear.png);
          background-size: 14 * @rem 14 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.transparent {
            opacity: 0;
          }
        }
        .country-code {
          display: flex;
          height: 42 * @rem;
          align-items: center;
          padding-left: 9 * @rem;
          position: relative;
          &::before {
            content: '';
            width: 1 * @rem;
            height: 11 * @rem;
            background-color: #dadada;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
          .country-code-text {
            font-size: 16 * @rem;
            color: #000000;
          }
          .arrow-down {
            width: 10 * @rem;
            height: 6 * @rem;
            .image-bg('~@/assets/images/users/arrow-down.png');
            margin-left: 5 * @rem;
            margin-top: 2 * @rem;
          }
        }
      }
      input {
        box-sizing: border-box;
        flex: 1;
        min-width: 0;
        height: 100%;
        padding: 0 5 * @rem;
        line-height: 44 * @rem;
        font-size: 14 * @rem;
        letter-spacing: 1 * @rem;
        background-color: #f4f4f4;
        padding: 0 20 * @rem;
        border-radius: 22 * @rem;
        &.pdr115 {
          padding-right: 115 * @rem;
        }
        &.pdr40 {
          padding-right: 40 * @rem;
        }
        &[disabled] {
          color: #000;
          opacity: 1;
        }
      }
      .text {
        box-sizing: border-box;
        border: 1 * @rem solid @themeColor;
        font-size: 14 * @rem;
        height: 42 * @rem;
        width: 116 * @rem;
        border-radius: 22 * @rem;
        color: @themeColor;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 10 * @rem;
        margin-right: 1 * @rem;
        &.text2 {
          color: #a4a4a4;
        }
      }
      .eyes {
        width: 18 * @rem;
        height: 44 * @rem;
        background-image: url(~@/assets/images/users/no-look.png);
        background-size: 18 * @rem 7 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        position: absolute;
        right: 20 * @rem;
        top: 50%;
        transform: translateY(-50%);
        &.open {
          background-image: url(~@/assets/images/users/look.png);
          background-size: 18 * @rem 12 * @rem;
        }
      }
    }
  }
  .save {
    width: 341 * @rem;
    height: 50 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25 * @rem;
    margin: 114 * @rem auto 0;
    background: @themeBg;
  }
}
.unbind-popup {
  .text {
    margin: 20 * @rem;
    font-size: 14 * @rem;
    line-height: 24 * @rem;
  }
  .small-text {
    margin: 0 auto 20 * @rem;
    font-size: 13 * @rem;
    color: #fff;
    text-align: center;
    padding: 5 * @rem;
    width: 100 * @rem;
    border-radius: 20 * @rem;
    background: @themeBg;
  }
}
</style>
