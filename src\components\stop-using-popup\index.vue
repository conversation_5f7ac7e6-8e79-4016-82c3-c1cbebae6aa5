<template>
  <div v-if="!!initData.ios_mconfig && platform == 'iosBox'">
    <!-- <div v-if="!!initData.ios_mconfig"> -->
    <van-popup
      v-model="popupShow"
      position="center"
      :lock-scroll="false"
      round
      class="stop-using-popup"
      :close-on-popstate="true"
      :transition-appear="true"
      :overlay-style="{ zIndex: 10000 }"
    >
      <div class="close" @click="closePopup"></div>
      <div class="stop-using-content">
        <div class="fast-add" @click="fastAdd"></div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { downloadH5Game } from '@/utils/function.js';
import { mapGetters } from 'vuex';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'stopUsingPopup',
  props: {
    show: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      platform,
      popupShow: this.show,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  watch: {
    show(val) {
      this.popupShow = val;
    },
  },
  methods: {
    fastAdd() {
      downloadH5Game(this.initData.ios_mconfig, true);
    },
    closePopup() {
      this.popupShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.stop-using-popup {
  width: 334 * @rem;
  height: 382 * @rem;
  background: transparent;
  margin-top: -30 * @rem;
  z-index: 10001 !important;
  .close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/home/<USER>
    background-size: 28 * @rem 28 * @rem;
    margin-left: auto;
  }
  .stop-using-content {
    width: 334 * @rem;
    height: 328 * @rem;
    background: url(~@/assets/images/home/<USER>
    background-size: 334 * @rem 328 * @rem;
    margin-top: 10 * @rem;
    overflow: hidden;
    .fast-add {
      width: 205 * @rem;
      height: 48 * @rem;
      margin: 239 * @rem auto 0;
    }
  }
}
</style>
