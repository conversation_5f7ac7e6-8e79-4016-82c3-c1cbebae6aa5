<template>
  <div class="feedback-item">
    <div class="item">
      <div class="title">{{ $t('反馈类型') }}：</div>
      <div class="desc weight">{{ info.type_name }}</div>
    </div>
    <div class="item" v-if="info.type == 1">
      <div class="title">{{ $t('游戏名称') }}：</div>
      <div class="desc">{{ info.game_name }}</div>
    </div>
    <div class="item">
      <div class="title">{{ $t('详细说明') }}：</div>
      <div class="desc">{{ info.content }}</div>
    </div>
    <div class="item">
      <div class="title hightlight">{{ $t('官方回复') }}：</div>
      <div class="desc">{{ info.push_content }}</div>
    </div>
    <div class="dot" v-if="info.read == 0"></div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
.feedback-item {
  box-sizing: border-box;
  padding: 20 * @rem 14 * @rem;
  width: 351 * @rem;
  border-radius: 12 * @rem;
  margin: 12 * @rem auto 0;
  background-color: #fff;
  position: relative;
  .dot {
    position: absolute;
    width: 5 * @rem;
    height: 5 * @rem;
    border-radius: 50%;
    background-color: #fe4a55;
    right: 14 * @rem;
    top: 20 * @rem;
  }
  .item {
    display: flex;
    &:not(:first-of-type) {
      margin-top: 10 * @rem;
    }
    .title {
      font-size: 14 * @rem;
      color: #797979;
      line-height: 22 * @rem;
      &.hightlight {
        color: @themeColor;
      }
    }
    .desc {
      flex: 1;
      min-width: 0;
      font-size: 14 * @rem;
      color: #000000;
      line-height: 22 * @rem;
      word-wrap: break-word;
      &.weight {
        font-weight: bold;
      }
    }
  }
}
</style>
