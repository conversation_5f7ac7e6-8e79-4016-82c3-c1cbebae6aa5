<template>
  <div class="page xiaohao-detail-page">
    <nav-bar-2 :border="true" :title="$t('捡漏角色详情')">
      <template #right>
        <div class="kefu-btn btn" @click="toPage('Kefu')"></div>
        <div class="share-btn btn" @click="handleShare"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="acount-info" v-if="xiaohaoInfo.game">
        <div
          class="top"
          @click="toPage('GameDetail', { id: xiaohaoInfo.game.id })"
        >
          <div class="game-icon">
            <img :src="xiaohaoInfo.game.titlepic" alt="" />
          </div>
          <div class="top-info">
            <div class="game-name">
              {{ xiaohaoInfo.game.main_title
              }}<span class="game-subtitle" v-if="xiaohaoInfo.game.subtitle">{{
                xiaohaoInfo.game.subtitle
              }}</span>
            </div>
            <div class="platform">
              <div
                class="plat-icon"
                v-for="(plat, platIndex) in xiaohaoInfo.platforms"
                :key="platIndex"
              >
                <img :src="plat.icon" alt="" />
              </div>
            </div>
          </div>
          <div class="top-right">
            <div class="price">
              ¥<span>{{ Number(xiaohaoInfo.rmb).toFixed(0) }}</span>
            </div>
            <div
              class="record-btn btn"
              @click.stop="historyShow = true"
              v-if="trade_list.length || jl_list.length"
            >
              {{ $t('小号历史') }}
            </div>
          </div>
        </div>
        <div class="center">
          <div class="main-info" v-html="info_detail"></div>
        </div>
      </div>
      <!-- 主要充值 -->
      <div class="servers-info" v-if="servers.length">
        <div class="section-title">
          <div class="title-text">
            {{ $t('主要充值')
            }}<span>{{ $t('（仅展示充值最多的3个区服）') }}</span>
          </div>
        </div>
        <div class="server-list">
          <div
            class="server-item"
            v-for="(item, index) in servers"
            :key="index"
          >
            <div class="server-id">{{ $t('区服ID') }}：{{ item.server }}</div>
            <div class="server-money">{{ item.suma }}</div>
          </div>
        </div>
        <div class="server-tips">
          {{
            $t('区服ID为所充值区服代码，购买后找不到区服右上角撩客服协助查询')
          }}
        </div>
      </div>
      <!-- 小号关联的游戏 -->
      <div class="container game-info" v-if="xiaohaoInfo.game">
        <game-item-2 :gameInfo="xiaohaoInfo.game"></game-item-2>
      </div>
      <!-- 相关商品 -->
      <div class="container related" v-if="relatedList.length">
        <div class="section-title">
          <div class="title-icon"></div>
          <div class="title-text">{{ $t('相关商品') }}</div>
          <div class="more btn" @click="hanleMore">
            <div class="more-text">{{ $t('更多') }}</div>
          </div>
        </div>
        <div class="related-list">
          <template v-for="(item, index) in relatedList">
            <div class="related-item" :key="index" v-if="index < 3">
              <jianlou-item :info="item"></jianlou-item>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="bottom-bar fixed-center" v-if="xiaohaoInfo.status_jl">
      <div
        class="deal-btn btn"
        :style="{ background: dealStatusInfo.buttonColor }"
        @click="handleDeal"
      >
        {{ dealStatusInfo.buttonText }}
      </div>
    </div>
    <!-- 小号历史弹窗 -->
    <van-dialog
      v-model="historyShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="history-container">
        <div class="close" @click="historyShow = false"></div>
        <div class="title">{{ $t('小号历史') }}</div>
        <content-empty
          v-if="historyEmpty"
          :tips="$t('暂无小号历史记录')"
        ></content-empty>
        <div class="cates" v-else>
          <div class="cate" v-if="trade_list.length">
            <div class="cate-title">{{ $t('近期交易成交历史') }}</div>
            <div class="hao-list">
              <div
                class="hao-item"
                v-for="(item, index) in trade_list"
                :key="index"
              >
                <div class="avatar">
                  <img :src="item.avatar" v-if="item.avatar" />
                  <img
                    src="@/assets/images/avatar/img_user_default.png"
                    v-else
                  />
                </div>
                <div class="nickname">{{ item.nickname }}</div>
                <div class="rmb">¥{{ Number(item.rmb).toFixed(1) }}</div>
                <div class="date">{{ item.update_time | formatTime }}</div>
              </div>
            </div>
          </div>
          <div class="cate" v-if="jl_list.length">
            <div class="cate-title">{{ $t('近期捡漏成交历史') }}</div>
            <div class="hao-list">
              <div
                class="hao-item"
                v-for="(item, index) in jl_list"
                :key="index"
              >
                <div class="avatar">
                  <img :src="item.avatar" v-if="item.avatar" />
                  <img
                    src="@/assets/images/avatar/img_user_default.png"
                    v-else
                  />
                </div>
                <div class="nickname">{{ item.nickname }}</div>
                <div class="rmb">¥{{ Number(item.rmb).toFixed(1) }}</div>
                <div class="date">{{ item.update_time | formatTime }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 买家须知 -->
    <van-dialog
      v-model="buyTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="buy-tip-container">
        <div class="title">{{ $t('买家须知') }}</div>
        <div class="content">
          <img class="banner" src="@/assets/images/deal/buy-tip-banner-2.png" />
          <div class="content-text">
            <p>{{ $t('1、角色信息已通过官方核验。') }}</p>
            <p>
              {{
                $t(
                  '2、购买后角色直接转入您的账号，登录游戏【选择小号】处即可查收角色。',
                )
              }}
            </p>
            <p>
              {{
                $t(
                  '3、因时间因素造成的角色数据变化（如排行榜，称号，装备到期等）不视为信息失实，买家需理解并接受。',
                )
              }}
            </p>
            <p>
              4、<span
                >{{ $t('该账号仅限') }}{{ platformText
                }}{{ $t('平台可玩，购买后不可退货！') }}</span
              >
            </p>
            <p>
              {{
                $t(
                  '5、交易过程仅限小号转移，不涉及账号交易或换绑操作，无需担心购买的号被找回。',
                )
              }}
            </p>
          </div>
          <div class="agree" @click="buyTipAgree = !buyTipAgree">
            <div class="agree-btn" :class="{ yes: buyTipAgree }"></div>
            <div class="agree-text">{{ $t('我已认真阅读买家须知') }}</div>
          </div>
          <div class="confirm-btn btn" @click="handleBuy">
            {{ $t('我知道了') }}
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiXiaohaoJianlouItem,
  ApiXiaohaoJianlouList,
  ApiXiaohaoTradeHistory,
  ApiXiaohaoCreateOrderJL,
} from '@/api/views/xiaohao.js';

import { handleTimestamp } from '@/utils/datetime.js';
import jianlouItem from '../components/jianlou-item';
export default {
  name: 'XiaohaoDetail',
  components: {
    jianlouItem,
  },
  data() {
    return {
      xiaohaoInfo: {},
      recycleId: 0,
      servers: [],
      info_detail: '', // html内容文案
      relatedList: [], // 相关商品
      historyShow: false,
      trade_list: [],
      jl_list: [],
      historyEmpty: false,
      buyTipShow: false, //买家须知
      buyTipAgree: false,
    };
  },
  computed: {
    dealStatusInfo() {
      let buttonText = '',
        buttonColor = '';
      switch (Number(this.xiaohaoInfo.status_jl)) {
        case 1:
          // 已上架
          buttonText = this.$t('立即购买');
          buttonColor = '#FE6600';
          break;
        case 3:
          buttonText = this.$t('小号正在交易中');
          buttonColor = '#FE6600';
          break;
        case 4:
          buttonText = this.$t('角色已卖出');
          buttonColor = '#a0a0a0';
          break;
        default:
          break;
      }
      return {
        buttonText,
        buttonColor,
      };
    },
    platformText() {
      if (this.xiaohaoInfo.platforms) {
        let platformArr = this.xiaohaoInfo.platforms.map(item => {
          return item.name;
        });
        return platformArr.join('/');
      }
    },
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  async created() {
    this.recycleId = this.$route.params.id;
    await this.getXiaohaoInfo(this.recycleId);
    await this.getRelated(this.recycleId);
    await this.getHistory();
  },
  methods: {
    hanleMore() {
      this.toPage('Jianlou', { info: this.xiaohaoInfo.game, active: 0 });
    },
    // 底下按钮逻辑
    handleDeal() {
      switch (Number(this.xiaohaoInfo.status_jl)) {
        case 1:
          // 立即购买
          this.buyTipShow = true;
          break;
        default:
          break;
      }
    },
    // 买家须知按钮逻辑
    async handleBuy() {
      if (!this.buyTipAgree) {
        this.$toast(this.$t('请认真阅读买家须知，并勾选'));
        return false;
      }
      this.buyTipShow = false;
      let orderInfo = await this.createOrder();
      //跳转收银台
      this.toPage('XiaohaoOrderRecharge', {
        info: orderInfo,
        back: 'MyJianlou',
        xhInfo: this.xiaohaoInfo,
        order_type: 202, // 小号捡漏
      });
    },
    async createOrder() {
      const res = await ApiXiaohaoCreateOrderJL({
        recycleId: this.xiaohaoInfo.id,
        rmb: this.xiaohaoInfo.rmb,
      });
      return res.data;
    },
    async getHistory() {
      const res = await ApiXiaohaoTradeHistory({
        xh_id: this.xiaohaoInfo.xh_id,
      });
      this.trade_list = res.data.trade_list;
      this.jl_list = res.data.jl_list;
      if (!this.trade_list.length && !this.jl_list.length) {
        this.historyEmpty = true;
      }
    },
    async getXiaohaoInfo(recycleId) {
      const res = await ApiXiaohaoJianlouItem({ recycleId });
      this.xiaohaoInfo = res.data.recycle;
      if (res.data.servers) {
        this.servers = res.data.servers;
      }

      this.info_detail = res.data.info_detail;
    },
    async getRelated(recycleId) {
      const res = await ApiXiaohaoJianlouList({
        recycleId,
        gameId: this.xiaohaoInfo.game.id,
      });
      this.relatedList = res.data.list;
    },
    handleShare() {
      let href = window.location.href;
      this.$copyText(href).then(
        res => {
          this.$toast(this.$t('本页链接复制成功，快去粘贴给好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-detail-page {
  background-color: #f6f6f6;
  .kefu-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/mine/icon_kefu_black.png) right center
      no-repeat;
    background-size: 20 * @rem 20 * @rem;
  }
  .share-btn {
    width: 22 * @rem;
    height: 22 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/jianlou/share-icon.png) right center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    margin-left: 10 * @rem;
  }
  .main {
    box-sizing: border-box;
    background-color: #f6f6f6;
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
    .container {
      box-sizing: border-box;
      width: 351 * @rem;
      margin: 12 * @rem auto 0;
      background: #ffffff;
      border-radius: 10 * @rem;
      padding: 16 * @rem 16 * @rem;
    }
    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        .image-bg('~@/assets/images/welfare/section-title-icon.png');
        margin-right: 5 * @rem;
      }
      .title-text {
        font-size: 18 * @rem;
        color: #000000;
        font-weight: 600;
        flex: 1;
        min-width: 0;
        span {
          font-size: 14 * @rem;
          color: #797979;
        }
      }
      .more {
        display: flex;
        .more-text {
          font-size: 14 * @rem;
          color: #797979;
        }
        .more-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          background: url(~@/assets/images/right-icon.png) right center
            no-repeat;
          background-size: 6 * @rem 10 * @rem;
        }
      }
    }
    .acount-info {
      background-color: #ffffff;
      padding: 14 * @rem 18 * @rem;
      .top {
        display: flex;
        align-items: center;
        .game-icon {
          width: 60 * @rem;
          height: 60 * @rem;
          border-radius: 8 * @rem;
          background-color: #bfbfbf;
          overflow: hidden;
        }
        .top-info {
          flex: 1;
          min-width: 0;
          margin-left: 12 * @rem;
          .game-name {
            font-size: 18 * @rem;
            color: #383838;
            line-height: 22 * @rem;
            font-weight: 600;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1 * @rem solid fade(@themeColor, 80);
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              color: @themeColor;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .platform {
            display: flex;
            align-items: center;
            margin-top: 11 * @rem;
            .plat-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              margin-right: 6 * @rem;
            }
          }
        }
        .top-right {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .price {
            font-size: 12 * @rem;
            font-weight: 600;
            color: @themeColor;
            span {
              font-size: 24 * @rem;
              color: @themeColor;
              font-weight: 600;
            }
          }
          .gold {
            font-size: 11 * @rem;
            color: #ff395e;
            background-color: #ff395d10;
            border-radius: 10 * @rem;
            height: 20 * @rem;
            padding: 0 7 * @rem;
            display: flex;
            align-items: center;
            margin: 0 6 * @rem;
            white-space: nowrap;
            overflow: hidden;
          }
          .record-btn {
            height: 22 * @rem;
            border: 1px solid #c1c1c1;
            border-radius: 13 * @rem;
            font-size: 10 * @rem;
            padding: 0 7 * @rem;
            color: #797979;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5 * @rem;
          }
        }
      }
      .center {
        margin-top: 8 * @rem;
        .main-info {
          font-size: 13 * @rem;
          color: #000000;
          line-height: 25 * @rem;
        }
      }
    }
    .servers-info {
      background-color: #ffffff;
      padding: 15 * @rem 18 * @rem;
      margin-top: 10 * @rem;
      .server-list {
        padding: 2 * @rem 0 5 * @rem;
        .server-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 37 * @rem;
          &:not(:first-of-type) {
            border-top: 0.5 * @rem solid #f3f3f8;
          }
          .server-id {
            font-size: 13 * @rem;
            color: #000000;
          }
          .server-money {
            font-size: 13 * @rem;
            color: @themeColor;
            font-weight: 600;
          }
        }
      }
      .server-tips {
        font-size: 12 * @rem;
        color: #9a9a9a;
        line-height: 17 * @rem;
      }
    }
    .goods-info {
      .content {
        font-size: 13 * @rem;
        color: #666666;
        line-height: 20 * @rem;
        margin-top: 8 * @rem;
      }
      .img-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 12 * @rem;
        .img-item {
          width: 100 * @rem;
          height: 60 * @rem;
          border-radius: 4 * @rem;
          background-color: #ffffff;
          overflow: hidden;
          margin-top: 10 * @rem;
          &:nth-of-type(-n + 3) {
            margin-top: 0;
          }
          img {
            object-fit: cover;
          }
        }
      }
    }
    .game-info {
      padding: 5 * @rem 12 * @rem;
    }
    .related {
      padding-bottom: 0;
      .related-list {
        padding: 5 * @rem 0 10 * @rem;
        .related-item {
          border-top: 0.5 * @rem solid #f3f3f8;
          &:nth-of-type(1) {
            border-top: 0;
          }
        }
        /deep/ .jianlou-item {
          box-shadow: unset;
          padding-left: 0;
          padding-right: 0;
          padding: 15 * @rem 0;
        }
      }
    }
  }

  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .collect {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 15 * @rem;
      .collect-icon {
        width: 20 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/collect.png) center center no-repeat;
        background-size: 20 * @rem 20 * @rem;
        &.had {
          background-image: url(~@/assets/images/collect-success.png);
        }
      }
      .collect-text {
        font-size: 11 * @rem;
        color: #999999;
        margin-top: 5 * @rem;
      }
    }
    .operate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      .publish-btn {
        flex: 1;
        min-width: 0;
        border-radius: 5px;
        height: 44 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14 * @rem;
        font-weight: 600;
        color: #ffffff;
        background-color: #ff9834;
      }
      .cancel-btn {
        flex: 1;
        min-width: 0;
        border-radius: 5px;
        height: 44 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14 * @rem;
        font-weight: 600;
        color: #ffffff;
        background-color: #a0a0a0;
        margin-left: 10 * @rem;
      }
    }
    .deal-btn {
      flex: 1;
      min-width: 0;
      background: @themeBg;
      border-radius: 5px;
      height: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #ffffff;
    }
  }
}
.history-container {
  padding: 20 * @rem 13 * @rem;
  position: relative;
  .close {
    width: 18 * @rem;
    height: 18 * @rem;
    background: url(~@/assets/images/close-dialog.png) center center no-repeat;
    background-size: 18 * @rem 18 * @rem;
    padding: 16 * @rem;
    position: absolute;
    right: 0;
    top: 0;
  }
  .title {
    font-size: 20 * @rem;
    color: #000000;
    font-weight: bold;
    text-align: center;
  }
  .cates {
    .cate {
      padding: 12 * @rem 0;
      border-top: 1px solid #e5e5e5;
      &:nth-of-type(1) {
        border-top: 0;
      }
      .cate-title {
        font-size: 16 * @rem;
        color: #000000;
        padding: 8 * @rem 0;
      }
      .hao-list {
        .hao-item {
          display: flex;
          align-items: center;
          padding: 7 * @rem 0;
          .avatar {
            display: block;
            width: 24 * @rem;
            height: 24 * @rem;
            border-radius: 50%;
            overflow: hidden;
            img {
              object-fit: cover;
            }
          }
          .nickname {
            font-size: 14 * @rem;
            color: #333333;
            margin-left: 12 * @rem;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .rmb {
            font-size: 14 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            width: 60 * @rem;
          }
          .date {
            font-size: 13 * @rem;
            color: #999999;
            margin-left: 5 * @rem;
          }
        }
      }
    }
  }
}
/deep/ .van-dialog {
  width: 315 * @rem;
}
.buy-tip-container {
  box-sizing: border-box;
  padding: 20 * @rem 18 * @rem;
  .title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: 600;
    text-align: center;
  }
  .content {
    margin-top: 15 * @rem;
    .banner {
      width: 281 * @rem;
      height: 106 * @rem;
      margin: 15 * @rem auto 0;
    }
    .content-text {
      margin-top: 15 * @rem;
      p {
        margin-top: 8 * @rem;
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        span {
          color: @themeColor;
        }
      }
    }
    .agree {
      display: flex;
      align-items: center;
      margin-top: 25 * @rem;
      .agree-btn {
        width: 19 * @rem;
        height: 19 * @rem;
        background: url(~@/assets/images/deal/buy-tip-no.png) center center
          no-repeat;
        background-size: 12 * @rem 12 * @rem;
        &.yes {
          background-image: url(~@/assets/images/deal/buy-tip-yes.png);
        }
      }
      .agree-text {
        font-size: 13 * @rem;
        color: #757575;
        margin-left: 5 * @rem;
      }
    }
    .confirm-btn {
      width: 204 * @rem;
      height: 40 * @rem;
      margin: 15 * @rem auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #ffffff;
      background: @themeBg;
      border-radius: 20 * @rem;
    }
  }
}
</style>
