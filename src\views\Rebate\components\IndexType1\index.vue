<template>
  <div v-if="loading" class="index-type1">
    <div v-if="list.length > 0" class="content">
      <div class="search-container">
        <div class="search">
          <div class="search-icon"></div>
          <input
            v-model="key"
            type="text"
            :placeholder="$t('请输入返利的游戏名')"
          />
        </div>
      </div>
      <game-list :gameList="list"></game-list>
    </div>
    <content-empty
      v-if="list.length === 0"
      @retry="init()"
      :showRetry="true"
      :tips="$t('您暂无可申请返利的游戏哦')"
    ></content-empty>
  </div>
</template>
<script>
import GameList from '../GameList';
import { ApiRebateGameList } from '@/api/views/rebate';

export default {
  name: 'IndexType1',
  data() {
    return {
      list: [],
      loading: false,
      key: '',
    };
  },
  watch: {
    async key() {
      const res = await ApiRebateGameList({ keyword: this.key });
      this.list = res.data.list;
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const toast1 = this.$toast.loading({
        message: this.$t('加载中...'),
        forbidClick: true,
      });
      try {
        const res = await ApiRebateGameList();
        this.list = res.data.list;
      } finally {
        this.loading = true;
      }
    },
  },
  components: {
    GameList,
  },
};
</script>
<style lang="less" scoped>
.index-type1 {
  .min-height-safa-top(94 * @rem);
  display: flex;
  justify-content: center;
  .content {
    width: 100%;
    .search-container {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 55 * @rem;
      background-color: #f5f5f5;
      .search {
        display: flex;
        align-items: center;
        width: 330 * @rem;
        height: 33 * @rem;
        background: #fff;
        border-radius: 16.5 * @rem;
        background: #fff;
        overflow: hidden;
        .search-icon {
          width: 17 * @rem;
          height: 17 * @rem;
          margin-left: 18 * @rem;
          background-image: url(~@/assets/images/search-icon.png);
          background-size: 100%;
        }
        input {
          flex: 1;
          margin-left: 5 * @rem;
          height: 33 * @rem;
          line-height: 33 * @rem;
          font-size: 14 * @rem;
        }
      }
    }
  }
  .empty {
    // align-self: center;
    display: block;
    .text {
      text-align: center;
      font-size: 15 * @rem;
      &.color {
        margin-top: 5 * @rem;
        color: @themeColor;
      }
    }
  }
}
</style>
