<template>
  <div class="marquee-content" ref="marqueeContainer">
    <div class="getWidth" ref="marqueeText">
      <slot></slot>
    </div>
    <div
      v-if="scroll"
      class="scroll-content"
      :style="{ animationDuration: time + 's' }"
    >
      <div class="scroll-item"><slot></slot></div>
      <div class="scroll-item"><slot></slot></div>
    </div>
    <template v-else>
      <slot></slot>
    </template>
  </div>
</template>

<script>
export default {
  name: 'MarqueeContent',
  props: {
    time: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      scroll: false,
    };
  },
  created() {
    this.$nextTick(() => {
      let width = this.$refs.marqueeText.scrollWidth;
      let maxWidth = this.$refs.marqueeContainer.clientWidth;
      this.scroll = width > maxWidth;
    });
  },
};
</script>

<style lang="less" scoped>
.marquee-content {
  display: flex;
  width: 100%;
  overflow: hidden;
  position: relative;

  .scroll-content {
    flex: 1;
    display: flex;
    align-items: center;
    animation: marquee 4s linear infinite;

    @keyframes marquee {
      0% {
        transform: translateX(0);
      }
      20% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(-50%);
      }
    }
  }

  .scroll-item {
    flex-shrink: 0;
    margin-right: 30 * @rem;
  }

  .getWidth {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: -1;
    white-space: nowrap;
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
