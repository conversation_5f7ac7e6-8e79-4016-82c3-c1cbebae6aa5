export default [
  {
    path: '/feedback',
    name: 'Feedback',
    component: () =>
      import(/* webpackChunkName: "feedback" */ '@/views/Feedback'),
    meta: {
      requiresAuth: true,
      pageTitle: '投诉建议',
    },
  },
  {
    path: '/feedback_complaint',
    name: 'FeedbackComplaint',
    component: () =>
      import(/* webpackChunkName: "feedback" */ '@/views/Feedback/Complaint'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/feedback_game',
    name: 'FeedbackGame',
    component: () =>
      import(/* webpackChunkName: "feedback" */ '@/views/Feedback/Game'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/new_opinion',
    name: 'NewOpinion',
    component: () =>
      import(/* webpackChunkName: "feedback" */ '@/views/Feedback/NewOpinion'),
    meta: {
      requiresAuth: true,
    },
  },
];
