<template>
  <div class="h5game">
    <rubber-band :topColor="'#000'" :bottomColor="'#000'">
      <iframe id="main" :src="url" frameborder="0"></iframe>
    </rubber-band>
  </div>
</template>
<script>
import BASEPARAMS from '@/utils/baseParams';
import rubberBand from '@/components/rubber-band';
import { mapGetters } from 'vuex';
import { getQueryVariable } from '@/utils/function.js';
import { BOX_setScreenOrientation } from '@/utils/box.uni.js';
import { BOX_openInBrowser } from '@/utils/box.uni.js';

export default {
  name: 'H5Game',
  data() {
    return {
      url: '',
    };
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    if (this.gameInfo.screen_orientation) {
      BOX_setScreenOrientation(0);
    }
    next(true);
  },
  computed: {
    ...mapGetters({
      gameInfo: 'game/gameInfo',
      h5GameUrl: 'game/h5GameUrl',
    }),
  },
  mounted() {
    window.addEventListener('message', this.handleMessage, false);
    this.postMessage();

    // 2022年8月29日11:28:37 该页面取消keepAlive,防止无法修改账号
    if (this.gameInfo.screen_orientation) {
      BOX_setScreenOrientation(1);
    }
    this.url = `${this.h5GameUrl}`;
    // this.url = `http://localhost:8081/h5g?appid=71813&w=1`;
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage, false);
  },
  methods: {
    handleMessage(e) {
      if (e.data.from === 'h5') {
        switch (e.data.type) {
          case 'exit':
            this.toPage('MyGame', {}, 1);
            break;
          case 'openurl':
            BOX_openInBrowser({
              ...e.data.data,
              ...{ h5_url: e.data.data.url },
            });
            break;
          case 'openInNewWindow':
            let temp = {};
            temp.name = e.data.data.name;
            if (e.data.data.params) {
              temp.params = e.data.data.params;
            }
            this.$router.push(temp);
            break;
        }
      }
    },
    postMessage() {
      let data = {
        from: 'webapp',
        type: 'init',
        data: {
          f: BASEPARAMS.from,
          t: this.userInfo.token ? this.userInfo.token : '',
          u: BASEPARAMS.uuid,
          c: BASEPARAMS.channel,
        },
      };

      // ios媒体上报
      let medium_key = getQueryVariable('medium_key');
      let medium_from = getQueryVariable('medium_from');
      if (medium_key) {
        data.data.medium_key = medium_key;
      }
      if (medium_from) {
        data.data.medium_from = medium_from;
      }

      let iframe = document.getElementById('main');
      iframe.onload = function () {
        iframe.contentWindow.postMessage(data, '*');
      };
    },
  },
  components: {
    rubberBand,
  },
};
</script>
<style lang="less" scoped>
.h5game {
  width: 100%;
  height: 100vh;
  background: #000;
  #main {
    position: fixed;
    top: constant(safe-area-inset-top);
    top: env(safe-area-inset-top);
    left: constant(safe-area-inset-left);
    left: env(safe-area-inset-left);
    width: calc(
      100% - constant(safe-area-inset-left) - constant(safe-area-inset-right)
    );
    width: calc(100% - env(safe-area-inset-left) - env(safe-area-inset-right));
    height: calc(
      100vh - constant(safe-area-inset-bottom) - constant(safe-area-inset-top)
    );
    height: calc(
      100vh - env(safe-area-inset-bottom) - env(safe-area-inset-top)
    );
    // margin-top: @safeAreaTop;
    // margin-top: @safeAreaTopEnv;
  }
}
</style>
