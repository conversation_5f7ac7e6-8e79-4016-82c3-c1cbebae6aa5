<template>
  <rubber-band topColor="#f8f8f9" bottomColor="#f8f8f9">
    <div class="my-gamble-page">
      <nav-bar-2
        :title="$t('我的夺宝')"
        :border="true"
        :azShow="true"
      ></nav-bar-2>
      <div class="main" :class="{ centered: empty }">
        <div class="nav-list">
          <div
            class="nav-item"
            :class="{ current: current == navIndex }"
            v-for="(nav, navIndex) in navList"
            :key="navIndex"
            @click="tapNav(navIndex)"
          >
            {{ nav.title }}
          </div>
          <div
            class="line"
            :style="{ left: `${current * 93.75 * remNumberLess}rem` }"
          ></div>
        </div>
        <yy-list
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="empty"
          :check="false"
          :tips="$t('快来参与金币夺宝吧')"
          :emptyImg="emptyImg"
        >
          <div class="gamble-list">
            <gamble-item
              :info="item"
              v-for="item in gambleList"
              :key="item.id"
            ></gamble-item>
          </div>
        </yy-list>
      </div>
    </div>
  </rubber-band>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import gambleItem from '../components/gamble-item/index.vue';
import { ApiGoldDuobaoUserRecord } from '@/api/views/goldGamble.js';
import emptyImg from '@/assets/images/welfare/gold-gamble/my-empty.png';
export default {
  name: 'MyGamble',
  components: {
    gambleItem,
  },
  data() {
    return {
      remNumberLess,
      emptyImg,
      navList: [
        {
          title: this.$t('全部'),
          status: 0,
        },
        {
          title: this.$t('进行中'),
          status: 1,
        },
        {
          title: this.$t('已中奖'),
          status: 2,
        },
        {
          title: this.$t('未中奖'),
          status: 3,
        },
      ],
      current: 0,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      gambleList: [],
    };
  },
  async activated() {
    await this.getGambleList();
  },
  methods: {
    async tapNav(index) {
      this.current = index;
      this.gambleList = [];
      await this.getGambleList();
    },
    async getGambleList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGoldDuobaoUserRecord({
        status: this.navList[this.current].status,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page == 1) {
        this.gambleList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gambleList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGambleList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGambleList(2);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.nav-title {
  font-weight: 600 !important;
  font-size: 18 * @rem !important;
  color: #191b1f !important;
}
.my-gamble-page {
  background-color: #f8f8f9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .main {
    padding-top: 38 * @rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    .nav-list {
      display: flex;
      align-items: center;
      height: 40 * @rem;
      background-color: #fff;
      position: fixed;
      z-index: 1;
      .fixed-center;
      top: calc(50 * @rem + @safeAreaTop);
      top: calc(50 * @rem + @safeAreaTopEnv);
      .nav-item {
        flex: 1;
        height: 40 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 15 * @rem;
        color: #60666c;
        &.current {
          font-weight: 600;
          color: #191b1f;
        }
      }
      .line {
        width: 16 * @rem;
        height: 3 * @rem;
        background-color: #fd6a53;
        border-radius: 2 * @rem;
        position: absolute;
        bottom: 0;
        left: 0;
        transform: translateX(38.875 * @rem);
        transition: 0.3s;
      }
    }
    /deep/.empty {
      height: calc(100vh - 38 * @rem - 50 * @rem - 40 * @rem);
    }
    /deep/.van-empty__image {
      width: 128 * @rem;
      height: 128 * @rem;
    }
    /deep/.van-empty__description {
      margin-top: 6 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
}
</style>
