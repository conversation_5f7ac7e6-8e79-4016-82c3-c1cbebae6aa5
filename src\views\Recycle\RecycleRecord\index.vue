<template>
  <div class="page recycle-record-page">
    <nav-bar-2 :border="true" :title="$t('我的回收记录')"></nav-bar-2>
    <div class="main">
      <div class="top-content">
        <div class="num-bar">
          <div class="num-item">
            <div class="num-title">{{ $t('共回收小号') }}</div>
            <div class="num-text">{{ recycle_count }}</div>
          </div>
          <div class="num-item">
            <div class="num-title">{{ $t('获得金币') }}</div>
            <div class="num-text">
              {{ recycle_gold_sum }}
            </div>
          </div>
        </div>
        <div class="tip-content">
          <p>{{ text1 }}</p>
        </div>
      </div>
      <div class="record-container">
        <yy-list
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="empty"
        >
          <div class="record-list">
            <div
              class="record-item"
              v-for="(item, index) in recordList"
              :key="index"
            >
              <div class="xiaohao-info">
                <div class="content">
                  <div class="game-icon">
                    <img :src="item.game.titlepic" alt="" />
                  </div>
                  <div class="game-right">
                    <div class="game-name">{{ item.game.title }}</div>
                    <div class="role-info">
                      <div class="role-name">
                        {{ $t('区服') }}：{{ item.game_area }}
                      </div>
                      <div class="recharge-money">
                        {{ $t('充值') }}：<span>{{ item.pay_sum }}</span>
                      </div>
                    </div>
                    <div class="recycle-time">
                      {{ $t('回收时间') }}：{{ item.create_time | formatTime }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="record-bottom">
                <div class="gold">
                  {{ $t('金币') }}：<span>{{ item.recycle_gold }}</span>
                </div>
                <div class="operate">
                  <div
                    class="operate-btn redeem btn"
                    @click="handleRedeem(item)"
                    v-if="item.status == 1"
                  >
                    {{ $t('购回小号') }}
                  </div>
                  <div class="operate-btn no btn" v-if="item.status == 2">
                    {{ $t('已购回') }}
                  </div>
                  <div
                    class="operate-btn delete btn"
                    v-if="item.status == 2"
                    @click="handleDelete(item)"
                  >
                    {{ $t('删除') }}
                  </div>
                  <div class="operate-btn no btn" v-if="item.status == 13">
                    {{ $t('不可购回') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </div>
    <van-dialog
      v-model="buyShow"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="buy-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">
          {{ $t('是否花费') }}<span>{{ buyInfo.redeem_price_gold }}</span
          >{{ $t('个金币购回当前小号?') }}
        </div>
        <div class="message">{{ text1 }}</div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="close">{{ $t('取消') }}</div>
          <div class="confirm btn" @click="confirm">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiXiaohaoMyRecycleList,
  ApiXiaohaoRedeem,
  ApiXiaohaoChangeRecycleStatus,
} from '@/api/views/xiaohao.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'RecycleRecord',
  data() {
    return {
      recycle_count: 0, // 回收数量
      recycle_gold_sum: 0, // 回收金币数
      page: 1,
      listRows: 10,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      empty: false,
      recordList: [],
      text1: '',
      buyShow: false,
      buyInfo: 0, // 要购回的小号信息
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  methods: {
    close() {
      this.buyShow = false;
    },
    async confirm() {
      this.buyShow = false;
      const res = await ApiXiaohaoRedeem({
        redeemPrice: this.buyInfo.redeem_price,
        recycleId: this.buyInfo.id,
      });
      if (res.code == 1) {
        this.$toast(this.$t('已购回'));
      }
      await this.getRecordList();
    },
    handleRedeem(item) {
      this.buyInfo = item;
      this.buyShow = true;
    },
    handleDelete(item) {
      this.$dialog
        .confirm({
          message: this.$t('确认删除？'),
          lockScroll: false,
        })
        .then(async () => {
          const res = await ApiXiaohaoChangeRecycleStatus({
            status: 100,
            recycleId: item.id,
          });
          if (res.code == 1) {
            this.$toast(this.$t('已删除'));
          }
          await this.getRecordList();
        });
    },
    async getRecordList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoMyRecycleList({
        page: this.page,
        listRows: this.listRows,
      });
      let { text1, recycle_count, recycle_gold_sum, list } = res.data;
      if (action === 1 || this.page === 1) {
        this.recordList = [];
      }
      if (!this.text) {
        this.text1 = text1;
      }
      if (recycle_count) {
        this.recycle_count = recycle_count;
      }
      if (recycle_gold_sum) {
        this.recycle_gold_sum = recycle_gold_sum;
      }
      this.recordList.push(...list);
      if (!this.recordList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getRecordList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.recordList.length) {
        await this.getRecordList();
      } else {
        await this.getRecordList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.recycle-record-page {
  background-color: #f5f5f6;
  .main {
    display: flex;
    flex-direction: column;
    min-height: calc(100% - 50 * @rem - @safeAreaTop);
    min-height: calc(100% - 50 * @rem - @safeAreaTopEnv);
    .top-content {
      .num-bar {
        display: flex;
        align-items: center;
        height: 94 * @rem;
        width: 339 * @rem;
        background-color: #fff;
        margin: 14 * @rem auto 0;
        border-radius: 12 * @rem;
        .num-item {
          width: 50%;
          height: 60 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;
          &:not(:first-of-type) {
            &::before {
              content: '';
              width: 1 * @rem;
              height: 26 * @rem;
              background-color: #ededed;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }
          .num-title {
            font-size: 13 * @rem;
            color: #000000;
          }
          .num-text {
            font-size: 26 * @rem;
            color: @themeColor;
            font-weight: 600;
            margin-top: 12 * @rem;
          }
        }
      }
      .tip-content {
        font-size: 11 * @rem;
        color: #909090;
        text-align: center;
        overflow: hidden;
        padding: 12 * @rem 0;
      }
    }

    .record-container {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      flex-grow: 1;
      min-height: 0;
      .record-list {
        .record-item {
          width: 339 * @rem;
          margin: 10 * @rem auto 0;
          background-color: #fff;
          border-radius: 12 * @rem;
          .xiaohao-info {
            padding: 0 15 * @rem;
            .content {
              display: flex;
              align-items: center;
              height: 91 * @rem;
              .game-icon {
                width: 67 * @rem;
                height: 67 * @rem;
                border-radius: 12 * @rem;
                overflow: hidden;
                background-color: #dcdcdc;
              }
              .game-right {
                flex: 1;
                min-width: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-left: 8 * @rem;
                .game-name {
                  font-size: 16 * @rem;
                  color: #000000;
                  font-weight: 500;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                .role-info {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-top: 8 * @rem;
                  .role-name {
                    font-size: 11 * @rem;
                    color: #757575;
                    flex: 1;
                    min-width: 0;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                  .recharge-money {
                    font-size: 11 * @rem;
                    color: #757575;
                    span {
                      color: @themeColor;
                    }
                  }
                }
                .recycle-time {
                  font-size: 11 * @rem;
                  color: #757575;
                  margin-top: 5 * @rem;
                }
              }
            }
          }
          .record-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 0.5 * @rem solid #ebebeb;
            margin: 0 15 * @rem;
            height: 45 * @rem;
            .gold {
              font-size: 13 * @rem;
              color: #757575;
              span {
                font-size: 13 * @rem;
                color: @themeColor;
              }
            }
            .operate {
              display: flex;
              .operate-btn {
                box-sizing: border-box;
                width: 60 * @rem;
                height: 28 * @rem;
                padding: 0 7 * @rem;
                border: 1px solid #bfbfbf;
                border-radius: 6 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 11 * @rem;
                color: #999999;
                margin-left: 9 * @rem;
                &.redeem {
                  color: #fff;
                  border-color: @themeColor;
                  background-color: @themeColor;
                }
                &.delete {
                  color: @themeColor;
                  border-color: @themeColor;
                }
                &.no {
                  color: #fff;
                  border-color: #cfcfcf;
                  background-color: #cfcfcf;
                }
              }
            }
          }
        }
      }
    }
  }
}
.buy-dialog {
  width: 244 * @rem;
  background: transparent;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/dialog-coin-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 11 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 15 * @rem 15 * @rem;
    .title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
      text-align: center;
      line-height: 22 * @rem;
      padding: 0 30 * @rem;
      span {
        color: @themeColor;
      }
    }
    .message {
      font-size: 9 * @rem;
      color: #b1b1b1;
      font-weight: 400;
      text-align: center;
      margin-top: 11 * @rem;
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10 * @rem;
      .cancel {
        width: 96 * @rem;
        height: 32 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 96 * @rem;
        height: 32 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
