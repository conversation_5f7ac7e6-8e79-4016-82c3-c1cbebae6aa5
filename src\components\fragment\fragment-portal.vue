<template>
  <div class="fragment-tab">
    <div
      class="tab-item"
      v-for="(item, index) in info.tab_action"
      :key="index"
      @click="goToPage(item, index)"
    >
      <img class="tab-icon" v-sensors-exposure="iconExposure(item, index)" :src="item.icon_url" alt="" />
      <div class="tab-title">{{ item.text1 }}</div>
    </div>
  </div>
</template>

<script>
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'FragmentPortal',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    iconExposure(item, index) {
      return {
        'event-name': 'icon_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-icon_index': `${index}`,
        'property-icon_name': item.text1,
      }
    },
    goToPage(item, index) {
      // 神策埋点
      this.$sensorsTrack('icon_click', {
        page_name: this.$sensorsPageGet(),
        icon_index: `${index}`,
        icon_name: item.text1,
      });
      const info = {
        ...item,
        header_title: item.text1,
      };
      
      this.$sensorsModuleSet(that.info.header_title || '金刚区')
      handleActionCode(info);
    },
  },
};
</script>

<style lang="less" scoped>
.fragment-tab {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 6 * @rem;
  padding: 4 * @rem 12 * @rem;
  &::-webkit-scrollbar {
    display: none;
  }
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80 * @rem;
    height: 48 * @rem;
    position: relative;
    .tab-icon {
      display: block;
      width: 80 * @rem;
      height: 48 * @rem;
    }
    .tab-title {
      box-sizing: border-box;
      width: 100%;
      position: absolute;
      top: 8 * @rem;
      left: 0;
      padding-left: 8 * @rem;
      overflow: hidden;
      font-size: 13 * @rem;
      font-weight: bold;
      color: #ffffff;
      line-height: 15 * @rem;
      white-space: nowrap;
      overflow: hidden;
      height: 15 * @rem;
    }
  }
}
</style>
