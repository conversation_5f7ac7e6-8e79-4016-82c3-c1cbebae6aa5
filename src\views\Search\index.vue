<template>
  <div class="search-page page">
    <nav-bar-2 :border="true">
      <template #left>
        <slot name="left">
          <div class="back" @click="searchBack"></div>
        </slot>
      </template>
      <template #center>
        <div class="search-bar" ref="searchBar">
          <div class="search-input" @click="handleSearchBarClick()">
            <form @submit.prevent="submitSearch(inputText)">
              <div class="input-wrapper">
                <input
                  v-model.trim="inputText"
                  type="text"
                  @focus="onFocus"
                  @blur="onBlur"
                  @input="inputListener"
                  :maxlength="15"
                  placeholderClass="input-placeholder"
                />
              </div>
              <div class="input-wrapper1" v-if="!inputText && !inputStatus">
                <span class="search-input-text">
                  {{ placeholder }}
                </span>
                <div
                  v-if="placeholder && search_hot_icon"
                  class="search-hot-icon"
                >
                  <img :src="search_hot_icon" alt="" />
                </div>
              </div>
            </form>
          </div>

          <div
            class="input-clear"
            v-if="inputClearShow"
            @click="clearInput"
          ></div>
          <!-- <div class="search-icon"></div> -->
        </div>
      </template>
      <template #right>
        <div class="right-btn" @click="clickSearch">
          {{ $t('搜索') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="search-container" v-if="!showSug">
      <template v-if="!isResult">
        <!-- <div class="search-list">
          <div
            class="search-item"
            v-for="(item, index) in searchGuideList"
            :key="index"
            @click="clickSearchItem(item)"
          >
            <div class="search-icon">
              <div class="icon">
                <img :src="item.iconPath" alt="" />
              </div>
              <div class="title">{{ item.title }}</div>
            </div>
            <div class="search-text"> {{ item.text }} </div>
          </div>
        </div> -->
        <div class="search-index">
          <!-- 搜索历史 -->
          <div class="search-history" v-if="historyList.length">
            <div class="section-title">
              <div class="title-text">
                <span>{{ $t('搜索历史') }}</span>
              </div>
              <div class="clear-history btn" @click="clearHistory">
                <i></i>
              </div>
            </div>
            <div class="word-list">
              <div
                class="word-item btn"
                v-for="(item, index) in historyList"
                :key="index"
                @click="handleRecordSearch(item)"
              >
                <img
                  v-if="item.type == 1"
                  src="~@/assets/images/search/btn_search_game.png"
                  alt=""
                />
                <img
                  v-if="item.type == 901"
                  src="~@/assets/images/search/btn_search_classify.png"
                  alt=""
                />
                <img
                  v-if="item.type == 701"
                  src="~@/assets/images/search/btn_search_collection.png"
                  alt=""
                />
                <img
                  v-if="item.type == 402"
                  src="~@/assets/images/search/btn_search_activity.png"
                  alt=""
                />
                <span>
                  {{ item.title || item.main_title }}
                </span>
              </div>
              <div
                v-if="hasMoreBtn"
                class="word-item-btn search-arrow-down"
                id="wordItemDown"
                :class="{ rotate_180_degrees: isDeleteShow }"
                @click="open"
              >
                <img
                  src="~@/assets/images/search/search-arrow-down.png"
                  alt=""
                />
              </div>
            </div>
          </div>
          <!-- 猜你喜欢 -->
          <div
            class="search-youLike"
            v-if="guessYouLike && guessYouLike.length"
          >
            <div class="section-title">
              <div class="title-text">
                <!-- <i></i> -->
                <span>{{ $t('猜你喜欢') }}</span>
              </div>
              <div class="change-batch btn" @click="getGuessYouLike()">
                <i
                  :class="{ 'rotate-icon': isRotated }"
                  @animationend="animationEnd"
                ></i>
                <span>换一换</span>
              </div>
            </div>
            <div class="section-box">
              <div
                class="section-list"
                v-for="(item, index) in guessYouLike.slice(0, 8)"
                :key="index"
              >
                <div class="section-item" @click="handleGuessYouLikeIt(item)">
                  <span>
                    {{ item.title || item.main_title }}
                  </span>
                  <img v-if="item.img" :src="item.img" alt="" />
                </div>
              </div>
            </div>
          </div>
          <!-- 热门模块 -->
          <van-loading
            class="hot-module-loading"
            v-if="!hotModuleLoadSuccess"
            size="24*@rem"
            >{{ $t('加载中...') }}</van-loading
          >
          <div
            class="tap-search-ranking"
            v-if="hotModuleLoadSuccess && isHasHotModuleList"
            ref="tapContainer"
          >
            <div class="tap-slide">
              <div class="tap-slide-wrap">
                <div class="tap-slide-content">
                  <span
                    class="tap-search-ranking-slide-item"
                    v-for="(tap, index) in tap_list"
                    :key="tap.id"
                    ref="tabItems"
                    :class="{ tapActive: tabCurrent === tap.id }"
                    @click="clickTab(tap.id, index)"
                  >
                    {{ tap.title }}</span
                  >

                  <span
                    class="tap-slide-line"
                    :style="{
                      left: `${lineLeft}px`,
                    }"
                  >
                  </span>
                </div>
              </div>
            </div>
            <div class="hot-module-swiper">
              <template>
                <swiper :options="hotModuleSwiperOption" ref="hotModuleSwiper">
                  <swiper-slide
                    class="swiper-slide"
                    v-for="(hotItem, index) in hotModuleListEntries"
                    :key="index"
                  >
                    <div
                      class="list"
                      :class="{ 'border-1': hotItem.value.length }"
                    >
                      <div class="hot-module-box">
                        <div
                          v-for="(item, index1) in hotItem.value"
                          :key="index1"
                          class="hot-module-item"
                          @click="jumpDifferentPage(item, hotItem.key)"
                        >
                          <div
                            class="hot-module-num"
                            :class="{
                              no1: index1 === 0,
                              no2: index1 === 1,
                              no3: index1 === 2,
                            }"
                            v-if="index1 < 3"
                          >
                          </div>
                          <div class="hot-module-num" v-else>
                            {{ index1 + 1 }}
                          </div>
                          <div
                            class="hot-module-img"
                            v-if="hotItem.key == 'game'"
                          >
                            <img :src="item.titlepic" alt="" />
                          </div>
                          <div
                            class="hot-module-title"
                            :class="{
                              mar_left_6: hotItem.key != 'game',
                            }"
                            >{{ item.title }}</div
                          >
                          <div class="hot-module-icon" v-if="item.img">
                            <img :src="item.img" alt="" />
                          </div>
                          <div
                            class="hot-module-icon"
                            :class="{ 'activity-tag': item.ad_type == 402 }"
                            v-if="item.ad_type == 402 && item.tag"
                          >
                            {{ item.tag }}
                          </div>
                        </div>
                      </div>
                      <div class="hot-bg-h94"></div>
                    </div>
                  </swiper-slide>
                </swiper>
              </template>
            </div>
          </div>

          <!-- 热门搜索 -->
          <!-- <div class="hot-search">
          <div class="section-title">
            <div class="hot-search-title"></div>
          </div>
          <div class="rank-list">
            <div
              class="rank-item btn"
              v-for="(item, index) in initData.configs
                ? initData.configs.hot_search_game
                : []"
              :key="index"
              @click="handleHotGameSearch(item)"
            >
              <div class="num" :class="`num${index + 1}`">{{ index + 1 }}</div>
              <div class="game-icon">
                <img :src="item.titlepic" :alt="item.title" />
              </div>
              <div class="game-name">
                {{ item.main_title
                }}<span class="game-subtitle" v-if="item.subtitle">{{
                  item.subtitle
                }}</span>
              </div>
            </div>
          </div>
        </div> -->
          <!-- 热门游戏分类 -->
          <!-- <div class="hot-word" v-if="categoryList.length">
            <div class="section-title">
              <div class="title-text">{{ $t('热门游戏分类') }}</div>
            </div>
            <div class="word-list1">
              <div
                class="word-item1 btn"
                v-for="(item, index) in categoryList"
                :key="index"
                @click="toPage('Category', { info: item })"
              >
                {{ item.title }}
              </div>
            </div>
          </div> -->
        </div>
      </template>
      <div class="search-result" v-if="isResult">
        <!-- <div class="tab-bar">
          <div
            class="tab-item btn"
            v-for="(tab, tabIndex) in tabList"
            :key="tabIndex"
            :class="{ on: current === tabIndex }"
            @click="tapNav(tabIndex)"
          >
            {{ tab.name }}
          </div>
          <p class="empty-msg" v-if="this.emptyMsgInfo">
            {{ this.emptyMsgInfo }}
          </p>
        </div> -->

        <!-- <content-empty
          v-if="empty"
          class="empty-box"
          :tips="$t('咦，什么都没找到哦~')"
        ></content-empty> -->
        <!-- 加载中 -->
        <van-loading
          v-if="!gameLoadSugSuccess && !defaultPageShow"
          class="sug-loading"
          vertical
          size="24*@rem"
          >正在加载...</van-loading
        >
        <!-- 网络异常 -->
        <div
          class="sug-loading"
          v-else-if="!gameLoadSugSuccess && defaultPageShow"
        >
          <default-error-page
            errorTitle="网络异常"
            errorBtn="重试"
            @callback="parentErrorCallback1"
          />
        </div>
        <!-- 暂无搜索结果 -->
        <div
          class="sug-loading"
          v-else-if="
            !loadingObj.loading &&
            gameLoadSugSuccess &&
            !defaultPageShow &&
            !resultList.length
          "
        >
          <default-not-found-page />
        </div>
        <template v-else>
          <yy-list
            class="game-list-box"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh()"
            @loadMore="loadMore()"
          >
            <div
              class="game-list"
              v-if="resultList.length"
              :class="{ 'aggregate-cart': tabList[current].type == 701 }"
            >
              <div
                class="empty-msg"
                v-if="emptyMsgInfo && empty_position === 0"
              >
                <span> {{ emptyMsgInfo }}</span>
              </div>
              <div
                class="game-item"
                :class="{
                  'coupon-item': tabList[current].type == 701,
                  'btn': tabList[current].type == 1,
                  'padding-10':
                    tabList[current].type == 101 || tabList[current].type == 4,
                }"
                v-for="(item, index) in resultList"
                :key="index"
                @click="clickGame(item)"
              >
                <div class="game-item-box" v-if="tabList[current].type == 1">
                  <template
                    v-if="!item.empty_position && item.empty_position !== 0"
                  >
                    <game-item-5
                      :gameInfo="item"
                      :showBtnDownload="true"
                      @child-click="clickGame(item)"
                    ></game-item-5>
                  </template>
                  <template
                    v-if="item.empty_position && item.empty_position > 0"
                  >
                    <div class="empty-msg1">
                      <span> {{ item.empty_msgInfo }}</span>
                    </div>
                  </template>
                </div>

                <div class="gift-pack-list" v-if="tabList[current].type == 101">
                  <gift-pack-item
                    :couponItem="item"
                    :isShowTitlePic="isShowTitlePic"
                  ></gift-pack-item>
                </div>
                <div
                  class="aggregate-list"
                  @click="toGameCollect(item)"
                  v-if="tabList[current].type == 701"
                >
                  <div class="aggregate-item">
                    <div class="aggregate-img">
                      <img :src="item.list_banner" alt="" />
                    </div>
                    <title class="aggregate-title">{{ item.title }}</title>
                  </div>
                </div>
                <div class="other-list" v-if="tabList[current].type == 4">
                  <div class="coupon-item1">
                    <coupon-item
                      :key="item.id"
                      :couponItem="item"
                    ></coupon-item>
                  </div>
                </div>
              </div>
            </div>
          </yy-list>
        </template>
      </div>
    </div>
    <div class="sug-container" v-else>
      <div class="sug-list">
        <!-- 加载中 -->
        <van-loading
          v-if="!loadSugSuccess && !defaultPageShow"
          class="sug-loading"
          vertical
          size="24*@rem"
          >正在加载...</van-loading
        >
        <!-- 网络异常 -->
        <div class="sug-loading" v-else-if="!loadSugSuccess && defaultPageShow">
          <default-error-page
            errorTitle="网络异常"
            errorBtn="重试"
            @callback="parentErrorCallback2"
          />
        </div>
        <!-- 暂无搜索结果 -->
        <div
          class="sug-loading"
          v-else-if="loadSugSuccess && !defaultPageShow && !sugList.length"
        >
          <default-not-found-page />
        </div>
        <!-- 搜索结果 -->
        <div class="sug-info" v-else>
          <SugInfoItem
            :sugList="sugList"
            @oneKeyBtn="oneKeyBtn"
            @addHistoryItem="addHistoryItem"
          ></SugInfoItem>
        </div>
      </div>
    </div>
    <div
      class="return-top"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>
  </div>
</template>

<script>
import {
  ApiSearchIndex,
  ApiCouponCouponList,
  ApiSearchGetHotKey,
  ApiCwbIndexSearch,
  ApiCwbIndexGetSearchHotData,
  ApiV2024SearchIndex,
  ApiV2024SearchSug,
  ApiV2024SearchYourLike,
  ApiV2024SearchHotList,
} from '@/api/views/search.js';
import { ApiGameCate } from '@/api/views/game.js';
import { mapGetters } from 'vuex';
import { remNumberLess } from '@/common/styles/_variable.less';
import CouponItem from './components/coupon-item/index.vue';
import GameItem from './components/game-item/index.vue';
import GiftPackItem from './components/gift-pack-item/index.vue';
import SugInfoItem from './components/sug-info-item/index.vue';
import { BOX_goToGame } from '@/utils/box.uni.js';
import { navigateToGameDetail } from '@/utils/function';
import { handleActionCode } from '@/utils/actionCode.js';
import search_zyx_icon from '@/assets/images/search/search_zyx_icon.png';
import search_lfl_icon from '@/assets/images/search/search_lfl_icon.png';
import search_gbd_icon from '@/assets/images/search/search_gbd_icon.png';
export default {
  name: 'Search',
  components: {
    CouponItem,
    GameItem,
    GiftPackItem,
    SugInfoItem,
  },
  data() {
    let that = this;
    return {
      remNumberLess,
      isResult: false,
      inputText: '',
      inputStatus: false,
      resultList: [], // 结果列表
      keyword: this.$t('游戏'),
      fromAction: 1,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      search_ver: 2, //6.5.1版本时为2
      empty: false,
      timer: null,
      historyList: [],
      historyListShow: [],
      categoryList: [
        {
          title: this.$t('精选'),
        },
      ],
      fallbackLists: {
        1: {
          dataKey: 'game_list',
          emptyKey: 'empty_game_list',
          emptyMsg: 'empty_msg',
          emptyPosition: 'empty_position',
        },
        101: {
          dataKey: 'card_list',
          emptyKey: 'empty_card_list',
          emptyMsg: 'empty_msg',
        },
        701: {
          dataKey: 'collect_list',
          emptyKey: 'empty_collect_list',
          emptyMsg: 'empty_msg',
        },
        4: {
          dataKey: 'list',
          emptyKey: 'empty_coupon_list',
          emptyMsg: 'empty_msg',
        },
      },
      guessYouLike: [], // 猜你喜欢
      isRotated: false, // 换一批
      isShowTitlePic: false, // 是否显示titlePic
      hotModuleList: [], // 热门模块
      activeModule: 0,
      tabList: [
        { name: this.$t('游戏'), type: 1 },
        { name: this.$t('礼包'), type: 101 },
        // { name: this.$t('分类'), type: 901 },
        { name: this.$t('合集'), type: 701 },
        // { name: this.$t('综合'), type: 1000 },
        { name: this.$t('其他'), type: 4 },
      ],
      current: 0,
      emptyMsgInfo: '',
      empty_position: 0,
      placeholder: '搜你想玩的游戏',
      search_hot_icon: '',
      searchGuideList: [
        {
          id: 1,
          iconPath: search_zyx_icon,
          title: '找游戏',
          text: '入口一键直达',
        },
        {
          id: 2,
          iconPath: search_lfl_icon,
          title: '领福利',
          text: '超值游戏福利',
        },
        {
          id: 3,
          iconPath: search_gbd_icon,
          title: '逛榜单',
          text: 'get热门游戏',
        },
      ],
      lineLeft: 0,
      tabCurrent: 0,
      resizeObserver: null, // ResizeObserver 实例
      tap_list: [
        {
          id: 0,
          title: '热门游戏',
        },
        {
          id: 1,
          title: '热门分类',
        },
        // {
        //   id: 2,
        //   title: '热门合集',
        // },
      ], // 热门模块名称    // 2024年12月31日17:16:47 产品说来不及配置 先隐藏 合集页的 tab
      hotModuleLoadSuccess: false,
      isHasHotModuleList: false,
      hotModuleListEntries: [],
      hotModuleSwiper: null,
      hotModuleSwiperOption: {
        observer: true,
        slidesPerView: 1,
        observerSlideChildren: true,
        observerParents: true,
        on: {
          slideChange: function (e) {
            setTimeout(() => {
              that.tabCurrent = this.activeIndex;
              that.handleResize();
            }, 0);
          },
        },
      },

      line: 0, //搜索历史占行
      hideIndex: 0,
      isDeleteShow: true,
      hasMoreBtn: false, // 是否超出两行
      showSug: false, // 显示搜索建议
      loadSugSuccess: false, // 搜索建议loading
      gameLoadSugSuccess: false, // 游戏搜建议loading
      defaultPageShow: false, // 网络错误占位符
      sugList: [], // 搜索建议信息
      isShowInput: false, // 输入框是否显示
      isFocused: false, //input是否失去焦点
      showReturnTop: false, // 返回顶部按钮
      yourLikePage: 0, // 猜你页面页码
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      searchHotText: 'system/searchHotText',
    }),
    inputClearShow() {
      return this.inputText?.length ? true : false;
    },
  },
  directives: {
    focus: {
      inserted(el) {
        el.focus();
      },
    },
  },
  watch: {
    inputText(value) {
      if (!value) {
        this.tabCurrent = 0;
        this.lineLeft = 0;
        this.inputStatus = false;
        this.$nextTick(() => {
          this.toggleHistoryData();
        });
      }
    },
  },
  async created() {
    // this.placeholder = this.searchHotText.keyword || '搜一搜';
    // this.search_hot_icon = this.searchHotText.img;
    this.updateHistory();
    await this.getHotKey();
    await this.getGuessYouLike(false);
    await this.getHotModuleList();
    window.addEventListener('scroll', this.handleScroll);
  },
  mounted() {
    this.$nextTick(() => {
      this.toggleHistoryData();
    });
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    searchBack() {
      if (this.inputText) {
        window.scrollTo(0, 0);
        this.inputText = '';
        this.showSug = false;
        this.isResult = false;
        this.defaultPageShow = false;
        this.isDeleteShow = true;
        this.resultList = [];
      } else {
        this.$router.back();
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleSearchBarClick() {
      // 输入框已聚焦
      if (this.isFocused) return;

      // 神策埋点
      this.$sensorsTrack('search_click');

      // 输入框未聚焦并且输入框内容为空 或输入框未显示 切换显示状态并聚焦
      if (!this.isShowInput || !this.inputText) {
        this.isShowInput = true;
        this.$nextTick(() => {
          document.querySelector('.search-input input')?.focus();
        });
      }
    },

    oneKeyBtn(item) {
      this.showSug = false;
      this.$nextTick(() => {
        document.querySelector('.search-input input')?.focus();
      });
      this.inputText = `${item.main_title}-${item.subtitle}`;
      this.submitSearch(item.main_title);
      return;
    },
    jumpDifferentPage(item, key) {
      switch (key) {
        case 'game':
          if (item?.action_code && item.action_code == 13) {
            handleActionCode(item);
          } else {
            navigateToGameDetail(item);
          }
          break;
        case 'type':
          this.toPage('Category', { info: item });
          break;
        case 'collect':
          this.toPage('ExternalGameCollect', { id: item.id, info: item });
          break;

        default:
          break;
      }
    },
    clickSearchItem(item) {
      switch (item.id) {
        case 1:
          break;
        case 2:
          this.toPage('Welfare');
          break;
        case 3:
          this.$router.push({
            path: '/home/<USER>',
          });
          break;
        default:
          break;
      }
    },
    async parentErrorCallback1() {
      this.gameLoadSugSuccess = false;
      this.defaultPageShow = false;
      await this.submitSearch(this.inputText);
    },
    async parentErrorCallback2() {
      this.loadSugSuccess = false;
      this.defaultPageShow = false;
      this.getSearchSug();
    },
    toggleHistoryData(first = true) {
      this.line = 0;
      let offsetLeft = 0;
      const tags = document.querySelectorAll('.word-list > .word-item');
      // 计算行数 往后的行数第一个offsetLeft跟第一个标签的offsetLeft一样
      tags.forEach((item, index) => {
        if (index === 0) {
          this.line = 1;
          offsetLeft = item?.offsetLeft;
        } else if (item?.offsetLeft === offsetLeft) {
          this.line++;
        }
        if (this.line === 3 && first) {
          // 第3行的时候 最后一个的下标
          // this.hideIndex = index - 1;
          this.hideIndex = index;
          this.hasMoreBtn = true;
        }
        if (this.line > 2) {
          item.style.display = 'none';
          if (item.id === 'wordItemDown') {
            item.style.display = 'flex';
            tags[this.hideIndex].style.display = 'none';
          }
        }
      });
    },
    openAll() {
      const tags = document.querySelectorAll('.word-list > .word-item');
      tags.forEach(item => {
        item.style.display = 'flex';
      });
    },
    open() {
      this.isDeleteShow ? this.openAll() : this.toggleHistoryData();
      this.isDeleteShow = !this.isDeleteShow;
    },
    async getSearchSug() {
      try {
        const res = await ApiV2024SearchSug({
          keyword: this.inputText,
        });
        if (res.data) {
          const { list, top } = res.data;
          this.sugList = [top, ...list];
        } else {
          this.sugList = [];
        }
        this.loadSugSuccess = true;
        this.defaultPageShow = false;
      } catch (error) {
        if (
          error.toString().includes('code 500') ||
          error.toString().includes('code 404')
        ) {
          this.loadSugSuccess = false;
          this.defaultPageShow = true;
        }
        if (!navigator.onLine) {
          this.loadSugSuccess = false;
          this.defaultPageShow = true;
        }
      }
    },
    clickTab(id, index) {
      this.tabCurrent = id;
      this.$nextTick(() => {
        this.updateLinePosition(index);
      });
      this.hotModuleSwiper = this.$refs?.hotModuleSwiper?.$swiper;
      if (this.hotModuleSwiper) {
        this.hotModuleSwiper.slideTo(index);
      }
    },
    handleResize() {
      this.$nextTick(() => {
        this.updateLinePosition();
      });
    },
    updateLinePosition(index = this.tabCurrent) {
      // 获取所有 Tab 元素
      const tabItems = this.$refs.tabItems;
      if (!tabItems || !tabItems[index]) return;

      const selectedTab = tabItems[index].getBoundingClientRect();
      const container =
        this.$refs.tabItems[0].parentElement.getBoundingClientRect();

      this.lineLeft = selectedTab.left - container.left; // 相对父容器的偏移
    },
    async getHotKey() {
      const res = await ApiSearchGetHotKey();
      this.categoryList = res.data.key_list;
    },
    clearInput() {
      window.scrollTo(0, 0);
      this.inputText = '';
      this.showSug = false;
      this.isResult = false;
      this.defaultPageShow = false;
      this.isDeleteShow = true;
      this.resultList = [];
      this.$nextTick(() => {
        document.querySelector('.search-input input')?.focus();
        this.toggleHistoryData();
      });
    },
    clearHistory() {
      this.$dialog
        .confirm({
          message: this.$t('确认删除？'),
          lockScroll: false,
        })
        .then(() => {
          this.hasMoreBtn = false;
          localStorage.setItem('NEW_HISTORY_LIST', '');
          this.updateHistory();
          this.$toast(this.$t('历史记录已删除'));
        });
    },
    addHistoryItem(keywordObj) {
      if (!keywordObj) return;
      let index = this.historyList.findIndex(
        item => JSON.stringify(item) === JSON.stringify(keywordObj),
      );
      if (index != -1) {
        this.historyList.splice(index, 1);
      }
      this.historyList.unshift(keywordObj);
      if (this.historyList.length > 10) {
        this.historyList.pop();
      }
      const uniqueData = [];
      const seenIds = new Set();

      this.historyList.forEach(item => {
        const itemId = item.id !== undefined ? parseInt(item.id) : null;

        if (itemId === null || !seenIds.has(itemId)) {
          seenIds.add(itemId);
          uniqueData.push(item);
        }
      });

      this.updateHistory(uniqueData);
    },
    updateHistory(historyList) {
      if (historyList) {
        localStorage.setItem('NEW_HISTORY_LIST', JSON.stringify(historyList));
      }

      let historyListStr = localStorage.getItem('NEW_HISTORY_LIST');
      if (historyListStr) {
        this.historyList = JSON.parse(historyListStr);
      } else {
        this.historyList = [];
      }
      this.$nextTick(() => {
        this.toggleHistoryData();
      });
    },
    async onRefresh() {
      this.finished = false;
      await this.handleSearch(this.inputText, 1, 4);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.resultList.length) {
        await this.handleSearch(this.inputText, 1, 5);
      } else {
        await this.handleSearch(this.inputText, 2, 5);
      }
    },
    // 搜索
    clickSearch() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.showSug = false;
          this.isDeleteShow = true;
          if (!this.isResult) {
            this.isShowInput = true;
          } else {
            return;
          }
          this.$nextTick(() => {
            if (!this.inputText) {
              document.querySelector('.search-input input')?.focus();
            } else {
              document.querySelector('.search-input input')?.blur();
            }
          });
          if (!this.inputText) {
            this.inputText = this.searchHotText?.keyword || '';
          }
          this.submitSearch(this.inputText);
        }, 200);
      });
    },
    submitSearch(keyword) {
      this.loadingObj.loading = true;
      if (keyword) {
        this.addHistoryItem({ title: keyword });
      }
      this.handleSearch(keyword);
    },
    // 监听输入框获得焦点
    onFocus() {
      this.isFocused = true;
      this.isShowInput = true;
    },
    // 监听输入框失去焦点
    onBlur() {
      this.isFocused = false;
      if (!this.inputText) {
        this.isShowInput = false;
      }
    },
    inputListener() {
      this.inputStatus = true;
      if (!this.inputText) {
        this.loadSugSuccess = false;
        this.showSug = false;
        this.isResult = false;
        this.resultList = [];
        this.isDeleteShow = true;
      }
      if (this.timer || !this.inputText) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      if (this.inputText) {
        this.showSug = true;
        this.isResult = false;
        this.loadSugSuccess = false;
        this.defaultPageShow = false;
        this.timer = setTimeout(() => {
          this.sugList = [];
          this.resultList = [];
          this.getSearchSug();
        }, 500);
      }
    },
    handleSearch(keyword, action = 1, from = 1, type = 1) {
      this.loadingObj.loading = true;
      this.isShowTitlePic = false;
      clearTimeout(this.timer);
      this.timer = null;
      keyword = keyword.trim();
      if (!keyword) {
        this.$toast(this.$t('请输入搜索词'));
        return;
      }

      if (action === 1) {
        this.resetSearchState();
      } else {
        this.page++;
      }

      type = this.tabList[this.current].type;

      const apiFunction =
        type === 4 ? ApiCouponCouponList : ApiV2024SearchIndex;
      const params = {
        keyword,
        type,
        fromAction: from,
        page: this.page,
        listRows: this.listRows,
        search_ver: this.search_ver,
      };
      if (type === 4) {
        delete params.type;
      }
      apiFunction(params)
        .then(res => {
          let dataList = [];
          if (this.fallbackLists[type]) {
            const { dataKey, emptyKey, emptyMsg, emptyPosition } =
              this.fallbackLists[type];
            dataList = res.data[dataKey]?.length
              ? res.data[dataKey]
              : res.data[emptyKey] || [];
          }

          if (type === 101 && res.data['empty_card_list']) {
            this.isShowTitlePic = true;
          }
          if (action === 1 || this.page === 1) {
            this.emptyMsgInfo = res.data.empty_msg;
            this.empty_position = res.data.empty_position;
            this.resultList = [];
            this.empty = dataList.length === 0;
          }
          this.resultList.push(...dataList);
          if (
            this.page === 1 &&
            dataList.length !== 0 &&
            this.empty_position !== -1
          ) {
            const newObject = {
              empty_position: this.empty_position,
              empty_msgInfo: this.emptyMsgInfo,
            };
            this.resultList.splice(this.empty_position, 0, newObject);
          }
          this.loadingObj.loading = false;
          this.finished = dataList.length < this.listRows;
          this.gameLoadSugSuccess = true;
          this.defaultPageShow = false;
          this.showSug = false;
          this.isDeleteShow = true;
        })
        .catch(e => {
          if (
            error.toString().includes('code 500') ||
            error.toString().includes('code 404')
          ) {
            this.gameLoadSugSuccess = false;
            this.defaultPageShow = true;
          }
          if (!navigator.onLine) {
            this.gameLoadSugSuccess = false;
            this.defaultPageShow = true;
          }
          if (e.code == 500) {
            this.empty = true;
          }
        });
    },
    resetSearchState() {
      this.isResult = true;
      this.empty = false;
      this.finished = false;
      this.page = 1;
    },

    handleRecordSearch(item) {
      this.handleGuessYouLikeIt(item);
    },
    handleGuessYouLikeIt(item) {
      // type = -1 跳转游戏搜索tab
      // type = 1 游戏详情
      // type = 701  合集详情
      // type = 901 分类详情
      // type = 402 活动
      const type = Number(item.type) || '';
      this.gameLoadSugSuccess = false;
      this.addHistoryItem(item);
      switch (type) {
        case -1:
          this.resultList = [];
          this.inputText = item.keyword || item.title || item.main_title;
          this.isShowInput = true;
          this.handleSearch(this.inputText, 1, 3);
          break;
        case 1:
          navigateToGameDetail(item);
          break;
        case 402:
          handleActionCode(item);
          break;
        case 701:
          this.toPage('ExternalGameCollect', { id: item.id, info: item });
          break;
        case 901:
          this.toPage('Category', { info: item });
          break;
        default:
          this.resultList = [];
          this.inputText = item.keyword || item.title;
          this.isShowInput = true;
          this.handleSearch(this.inputText, 1, 3);
          break;
      }
    },
    // handleHotKeywordSearch(keyword) {
    //   this.resultList = [];
    //   this.inputText = keyword;
    //   this.addHistoryItem(this.inputText);
    //   this.handleSearch(this.inputText, 1, 2);
    // },
    // handleHotGameSearch(item) {
    //   this.addHistoryItem(item.main_title);
    //   this.$router.push({
    //     name: 'GameDetail',
    //     params: {
    //       id: item.id,
    //     },
    //   });
    // },
    clickGame(item) {
      const filteredData = {
        id: item.id,
        type: 1,
        classid: item.classid,
        main_title: item.main_title,
      };
      this.addHistoryItem(filteredData);
    },
    // 猜你喜欢
    async getGuessYouLike(isRotated = true) {
      try {
        this.isRotated = isRotated;
        this.yourLikePage++;
        const res = await ApiV2024SearchYourLike({
          page: this.yourLikePage,
        });
        this.guessYouLike = res.data.guess || [];
      } catch (error) {}
    },
    animationEnd() {
      this.isRotated = false;
    },
    mergeHotSearchAndGame(data) {
      const hotSearch = data.hot_search || [];
      const hotSearchGame = data.hot_search_game || [];
      return [...hotSearch, ...hotSearchGame];
    },
    randomSortArray(arr) {
      var stack = [];
      while (arr.length) {
        var index = parseInt(Math.random() * arr.length);
        stack.push(arr[index]);
        arr.splice(index, 1);
      }
      return stack;
    },
    // 热门模块
    async getHotModuleList() {
      try {
        // const res = await ApiCwbIndexGetSearchHotData();
        const res = await ApiV2024SearchHotList();
        const hotModuleList = res.data;
        // 2024年12月31日17:16:47 产品说来不及配置 先隐藏 合集页的 tab
        this.hotModuleList = {
          game: hotModuleList.game,
          type: hotModuleList.type,
        };
        // console.log(this.hotModuleList);
        this.hotModuleListEntries = Object.entries(this.hotModuleList).map(
          ([key, value]) => ({
            key,
            value,
          }),
        );
      } catch (error) {
      } finally {
        this.hotModuleLoadSuccess = true;
        this.isHasHotModuleList = Object.values(this.hotModuleList).some(
          array => array.length > 0,
        );
        if (!this.isHasHotModuleList) return;
        this.$nextTick(() => {
          const container = this.$refs.tapContainer;
          if (container) {
            this.resizeObserver = new ResizeObserver(() => {
              this.handleResize();
            });
            this.resizeObserver.observe(container);
          }
          this.hotModuleSwiper = this.$refs?.hotModuleSwiper?.$swiper;
        });
      }
    },
    async tapNav(index) {
      if (this.current != index) {
        this.current = index;
        this.resultList = [];
        this.emptyMsgInfo = '';
        await this.handleSearch(
          this.inputText,
          1,
          4,
          this.tabList[this.current].type,
        );
      }
    },
    // 打开合集页
    // toGameCollect(item) {
    //   this.addHistoryItem(item.title);
    //   const { id } = item;
    //   this.toPage('ExternalGameCollect', { id, info: item });
    // },
  },
  async activated() {
    this.isDeleteShow = true;
    this.placeholder = this.searchHotText.keyword || '搜一搜';
    this.search_hot_icon = this.searchHotText.img;
    this.updateHistory();
    // await this.getHotKey();
    // await this.getGuessYouLike(false);
    // await this.getHotModuleList();
    window.addEventListener('scroll', this.handleScroll);
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-nav-bar__title {
  max-width: 237 * @rem;
  margin: 0 90 * @rem 0 auto;
}
/deep/.nav-bar-component {
  z-index: 999;
}
/deep/.van-popup {
  &.sug-loading {
    background-color: unset;
  }
}
.search-page {
  .search-bar {
    box-sizing: border-box;
    padding: 0 14 * @rem;
    width: 237 * @rem;
    height: 36 * @rem;
    border-radius: 49 * @rem;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    // border: 1 * @rem solid #0fb089;
    .search-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/search/search_ss.png) center center
        no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    .search-input {
      flex: 1;
      height: 33 * @rem;
      // margin-left: 7 * @rem;
      form {
        border: 0;
        outline: 0;
        display: block;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        position: relative;
        .input-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          width: 100%;
          input {
            border: 0;
            outline: 0;
            display: block;
            width: 100%;
            background-color: transparent;
            font-size: 14 * @rem;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &::placeholder {
              color: #999999;
              opacity: 0.8;
            }
          }
        }
        .input-wrapper1 {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          .search-input-text {
            padding: 0;
            border: 0;
            outline: 0;
            display: block;
            min-width: 0;
            max-width: 174 * @rem;
            background-color: transparent;
            font-size: 14 * @rem;
            color: #999999;
            opacity: 0.8;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .search-hot-icon {
            margin-left: 4 * @rem;
            width: 14 * @rem;
            height: 14 * @rem;
          }
        }
      }
    }

    .input-clear {
      width: 16 * @rem;
      height: 16 * @rem;
      .image-bg('~@/assets/images/search/search_xx.png');
      // margin: 0 12 * @rem;
    }
  }
  .right-btn {
    width: 60 * @rem;
    text-align: center;
    height: 32 * @rem;
    line-height: 32 * @rem;
    background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
    border-radius: 28 * @rem;
    font-weight: 500;
    font-size: 14 * @rem;
    color: #ffffff;
  }
  .search-list {
    margin-top: 16 * @rem;
    padding: 0 18 * @rem;
    display: flex;
    align-items: center;
    .search-item {
      // flex: 1;
      width: 108 * @rem;
      height: 54 * @rem;
      line-height: 54 * @rem;
      background: #eefff5;
      border-radius: 8 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(33, 185, 138, 0.12);
      .search-icon {
        display: flex;
        align-items: center;
        .icon {
          width: 16 * @rem;
          height: 14 * @rem;
        }
        .title {
          margin-left: 4 * @rem;
          height: 16 * @rem;
          font-weight: 600;
          font-size: 13 * @rem;
          color: #222222;
          line-height: 16 * @rem;
        }
      }
      .search-text {
        margin-top: 4 * @rem;
        height: 14 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #999999;
        line-height: 14 * @rem;
      }
      &:not(:first-child) {
        margin-left: 8 * @rem;
      }
    }
  }
  .search-index {
    padding-bottom: 24 * @rem;
    .section-title {
      padding: 10 * @rem 18 * @rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-text {
        display: flex;
        align-items: center;
        > i {
          width: 18 * @rem;
          height: 18 * @rem;
          background: url(~@/assets/images/search/search_ssls.png) center center
            no-repeat;
          background-size: 13 * @rem auto;
        }
        span {
          // margin-left: 4 * @rem;
          font-size: 17 * @rem;
          color: #333333;
          font-weight: bold;
        }
      }
      .clear-history {
        display: flex;
        align-items: center;
        > i {
          width: 16 * @rem;
          height: 16 * @rem;
          background: url(~@/assets/images/search/search_clear.png) center
            center no-repeat;
          background-size: 16 * @rem auto;
        }
      }
      .hot-search-title {
        width: 79 * @rem;
        height: 20 * @rem;
        .image-bg('~@/assets/images/hot-search-title.png');
      }
    }
    .search-history {
      padding: 15 * @rem 0 5 * @rem;
      .section-title {
        padding: 10 * @rem 18 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-text {
          display: flex;
          align-items: center;
          > i {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/search/search_ssls.png) center
              center no-repeat;
            background-size: 14 * @rem auto;
          }
          span {
            // margin-left: 4 * @rem;
            font-size: 17 * @rem;
            color: #333333;
            font-weight: bold;
          }
        }
        .clear-history {
          display: flex;
          align-items: center;
          > i {
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/search/search_clear.png) center
              center no-repeat;
            background-size: 16 * @rem auto;
          }
        }
        .hot-search-title {
          width: 79 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/hot-search-title.png');
        }
      }
    }
    .search-youLike {
      // padding: 15 * @rem 0 5 * @rem;
      .section-title {
        padding: 10 * @rem 18 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-text {
          display: flex;
          align-items: center;
          > i {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/search/search_cnxh.png) center
              center no-repeat;
            background-size: 13 * @rem auto;
          }
          span {
            // margin-left: 4 * @rem;
            font-size: 17 * @rem;
            color: #333333;
            font-weight: bold;
          }
        }
        .change-batch {
          display: flex;
          align-items: center;
          > i {
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/search/search_sync.png) center
              center no-repeat;
            background-size: 12 * @rem 12 * @rem;
            transition: transform 0.3s ease;
            &.rotate-icon {
              animation: rotateAnimation 0.3s linear forwards;
            }
          }
          span {
            margin-left: 5 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #adafb8;
          }
        }
        @keyframes rotateAnimation {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .hot-search-title {
          width: 79 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/hot-search-title.png');
        }
      }
      .section-box {
        padding: 4 * @rem 18 * @rem 8 * @rem 18 * @rem;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 339 * @rem;
        .section-list {
          width: 50%;
          height: 16 * @rem;
          line-height: 16 * @rem;
          margin-bottom: 16 * @rem;
          .section-item {
            width: 150 * @rem;
            background: #ffffff;
            font-weight: 400;
            font-size: 13 * @rem;
            color: #333333;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            span {
              display: block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
            }
            img {
              margin-left: 4 * @rem;
              width: 14 * @rem;
              height: 14 * @rem;
            }
          }
        }
      }
    }
    .hot-module-loading {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tap-search-ranking {
      padding: 0 18 * @rem;
      overflow-x: hidden;
      .tap-slide {
        .tap-slide-wrap {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          overflow-x: hidden;
          overflow-y: visible;
          height: 100%;
          display: -webkit-box;
          display: flex;
          -webkit-box-align: center;
          align-items: center;
          .tap-slide-content {
            display: inline-flex;
            position: relative;
            .tap-search-ranking-slide-item {
              position: relative;
              z-index: 1;
              display: -webkit-box;
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              -webkit-box-pack: center;
              justify-content: center;
              font-size: 14 * @rem;
              line-height: 32 * @rem;
              color: #666666;
              &:not(:first-child) {
                margin-left: 26 * @rem;
              }
              &.tapActive {
                font-size: 16 * @rem;
                font-weight: 600;
                color: #222222;
              }
            }
            .tap-slide-line {
              position: absolute;
              bottom: 4 * @rem;
              width: 64 * @rem;
              height: 12 * @rem;
              background: linear-gradient(
                89deg,
                rgba(109, 255, 148, 0.47) 0%,
                rgba(147, 255, 109, 0.06) 99%
              );
              border-radius: 4 * @rem;
              -webkit-transform: translateX(-50%);
              transform: translate(-50%);
              -webkit-transition-duration: 0.3s;
              transition-duration: 0.3s;
              transform: translate3d(calc(-50% + 32 * @rem), 0px, 0px);
            }
          }
        }
      }
      .tap-box {
      }
      .hot-module-swiper {
        padding: 16 * @rem 0 0 0;
        position: relative;
        width: auto;
        /deep/.swiper-container {
          width: 260 * @rem;
          margin-left: 0;
          margin-right: 0;
          position: relative;
          overflow: inherit;
          list-style: none;
          padding: 0;
          z-index: 1;
        }
        .list {
          width: 260 * @rem;
          height: auto;
          border-radius: 10 * @rem;
          padding: 16 * @rem 15 * @rem;
          box-sizing: border-box;

          .hot-module-box {
            border-radius: 17 * @rem;
            .hot-module-item {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              &:not(:first-child) {
                margin-top: 14 * @rem;
              }
              height: 36 * @rem;
              .hot-module-num {
                width: 18 * @rem;
                height: 18 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #999999;
                text-transform: none;
                &.no1 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no1.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
                &.no2 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no2.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
                &.no3 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no3.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
              }
              .hot-module-img {
                margin: 0 8 * @rem 0 13 * @rem;
                img {
                  width: 36 * @rem;
                  height: 36 * @rem;
                  border-radius: 6 * @rem;
                }
              }
              .hot-module-title {
                height: 16 * @rem;
                line-height: 16 * @rem;
                font-weight: 400;
                font-size: 13 * @rem;
                color: #111111;
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &.mar_left_6 {
                  margin-left: 6 * @rem;
                }
              }
              .hot-module-icon {
                margin-left: 4 * @rem;
                img {
                  width: 15 * @rem;
                  height: 15 * @rem;
                }
                &.activity-tag {
                  height: 15 * @rem;
                  line-height: 15 * @rem;
                  color: #21b98a;
                  padding: 0 3 * @rem;
                  border-radius: 4 * @rem;
                  font-size: 10 * @rem;
                  border: 1px solid #21b98a;
                  background: #ecfbf4;
                  vertical-align: middle;
                }
              }
              .hot-module-title-left {
                margin-left: 13 * @rem;
              }
            }
          }
          .hot-module-box1 {
            background: linear-gradient(
              180deg,
              #fae7d8 0%,
              #fcf7f5 16%,
              #fcf7f5 99%
            );
          }
          .hot-module-box2 {
            background: linear-gradient(
              180deg,
              #e7f1f7 0%,
              #f5f8fa 16%,
              #f5f8fa 99%
            );
          }
          .hot-module-box3 {
            background: linear-gradient(
              180deg,
              #e8e7f7 0%,
              #f6f5fa 16%,
              #f6f5fa 99%
            );
          }
          .hot-bg-h94 {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            z-index: -99999;
            height: 94 * @rem;
            background: linear-gradient(
              173deg,
              rgba(147, 255, 109, 0.12) 0%,
              rgba(147, 255, 109, 0) 100%
            );
          }
          &.border-1 {
            border: 1px solid rgba(60, 210, 121, 0.1);
          }
        }
        .swiper-slide {
          width: 260 * @rem !important;
          &:not(:last-child) {
            margin-right: 18 * @rem;
          }
        }
      }
    }
    .hot-module {
      padding: 0 15 * @rem;

      .vant-tabs {
        /deep/.van-tabs__line {
          border-radius: 3 * @rem;
          width: 12 * @rem;
          height: 6 * @rem;
          background: #32b768;
          border-radius: 16 * @rem;
        }
        /deep/.van-tab__text--ellipsis {
          width: 64 * @rem;
          height: 20 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #111111;
          line-height: 20 * @rem;
        }
      }
    }
    .hot-search {
      padding: 10 * @rem 0 5 * @rem;
    }
    .hot-word {
      padding: 15 * @rem 0 5 * @rem;
    }
    .rank-list {
      padding: 0 18 * @rem;
      .rank-item {
        display: flex;
        align-items: center;
        padding: 8 * @rem 0;
        .num {
          width: 15 * @rem;
          height: 15 * @rem;
          display: flex;
          align-items: center;
          font-size: 14 * @rem;
          font-weight: 500;
          color: #c4c4c4;
          &.num1 {
            color: #fe2b1d;
          }
          &.num2 {
            color: #f8860d;
          }
          &.num3 {
            color: #fec243;
          }
        }
        .game-icon {
          width: 30 * @rem;
          height: 30 * @rem;
          border-radius: 7 * @rem;
          overflow: hidden;
          margin-left: 5 * @rem;
        }
        .game-name {
          font-size: 14 * @rem;
          color: #000000;
          margin-left: 7 * @rem;
          display: flex;
          align-items: center;
          .game-subtitle {
            box-sizing: border-box;
            border: 1 * @rem solid fade(@themeColor, 80);
            border-radius: 3 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 3 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            vertical-align: middle;
            line-height: 1;
          }
        }
      }
    }
    .word-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 18 * @rem;
      margin-top: 10 * @rem;
      .word-item,
      .word-item-btn {
        height: 33 * @rem;
        line-height: 33 * @rem;
        padding: 0 12 * @rem;
        border-radius: 16 * @rem;
        background: rgba(250, 250, 253, 1);
        display: flex;
        // justify-content: center;
        align-items: center;
        font-weight: 400;
        margin-right: 12 * @rem;
        margin-bottom: 12 * @rem;
        font-size: 12 * @rem;
        color: #222222;
        max-width: 339 * @rem;
        box-sizing: border-box;
        img {
          margin-right: 4 * @rem;
          width: 15 * @rem;
          height: 15 * @rem;
        }
        span {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: left;
        }
      }

      .search-arrow-down {
        padding: 0;
        img {
          margin-right: 0;
          width: 29 * @rem;
          height: 29 * @rem;
        }
      }
    }
    .word-list1 {
      display: flex;
      flex-wrap: wrap;
      padding: 0 18 * @rem;
      margin-top: 10 * @rem;
      .word-item1 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 32 * @rem;
        padding: 0 11 * @rem;
        border-radius: 16 * @rem;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12 * @rem;
        margin-bottom: 12 * @rem;
        font-size: 12 * @rem;
        color: #333333;
      }
    }
  }

  .search-result {
    box-sizing: border-box;
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    display: flex;
    flex-direction: column;

    .tab-bar {
      position: fixed;
      width: 100%;
      max-width: 450px;
      height: 50 * @rem;
      z-index: 200;
      top: calc(50 * @rem + @safeAreaTop);
      top: calc(50 * @rem + @safeAreaTopEnv);
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .tab-item {
        // width: 78 * @rem;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        font-weight: 400;
        color: #797979;
        &.on {
          font-weight: 600;
          color: #000000;
          &::after {
            content: '';
            width: 14 * @rem;
            height: 4 * @rem;
            border-radius: 2 * @rem;
            background-color: #32b768;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
          }
        }
      }
      .empty-msg {
        position: absolute;
        width: 100%;
        top: 50 * @rem;
        height: 20 * @rem;
        line-height: 20 * @rem;
        background: #fff;
        left: 18 * @rem;
        color: #ff9000;
        font-size: 13 * @rem;
        padding: 4 * @rem 0 0;
      }
    }
    // .empty-msg {
    //   position: fixed;
    // }
    .empty-msg,
    .empty-msg1 {
      height: 53 * @rem;
      box-sizing: border-box;
      background: #fff;
      z-index: 9;
      width: 100%;
      padding: 20 * @rem 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 13 * @rem;
      color: #999999;
    }
    .game-list-box {
      // margin-top: 50 * @rem;
      padding: 0 18 * @rem;
      // &.margin-top-53 {
      //   margin-top: 53 * @rem;
      // }
      .game-list {
        // padding: 6 * @rem 0 0;
        .game-item {
          .game-item-box {
            display: flex;
            align-items: center;
            .game-btn {
              position: relative;
              .download-btn {
                width: 64 * @rem;
                height: 30 * @rem;
                background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
                border-radius: 28 * @rem;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #ffffff;
                text-align: center;
                line-height: 30 * @rem;
              }
            }
          }
          .gift-pack-list {
          }
          .aggregate-list {
            height: 121 * @rem;

            .aggregate-item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              .aggregate-img {
                width: 160 * @rem;
                height: 80 * @rem;
                border-radius: 6 * @rem;
                background-color: #eeeeee;
                img {
                  border-radius: 6 * @rem;
                }
              }
              .aggregate-title {
                margin: 5 * @rem 4 * @rem;
                width: 149 * @rem;
                font-weight: 400;
                font-size: 14 * @rem;
                color: #333333;
                line-height: 18 * @rem;
                text-align: left;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
              }
            }
          }
        }
        .padding-10 {
          padding: 10 * @rem 0;
        }
        .coupon-item {
          padding: 14 * @rem 0 0 0;
        }
        .coupon-item1 {
          padding: 0;
        }
        &.aggregate-cart {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 0 19 * @rem;
        }
      }
    }

    .empty-box {
      margin-top: 50 * @rem;
    }
  }
  .sug-container {
    // margin-top: 50 * @rem;
    height: 100%;
    .sug-list {
      height: 100%;

      .sug-info {
        height: 100%;
        overflow: hidden;
      }
    }
  }
  .rotate_180_degrees {
    transform: rotate(180deg);
    transform-origin: center;
  }
  .sug-loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .return-top {
    position: fixed;
    right: 5 * @rem;
    bottom: 100 * @rem;
    width: 50 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/exclusive-activity/return-top.png) center
      center no-repeat;
    background-size: 50 * @rem 50 * @rem;
    z-index: 100;
  }
}
</style>
