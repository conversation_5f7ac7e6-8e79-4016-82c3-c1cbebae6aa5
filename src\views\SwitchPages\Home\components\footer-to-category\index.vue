<template>
  <div class="footer btn" @click="goToCate">
    <span>{{ $t('查看更多游戏') }}</span>
    <span class="right-icon"></span>
  </div>
</template>

<script>
export default {
  methods: {
    goToCate() {
      this.CLICK_EVENT('M1_MORE');
      this.toPage('Category');
    },
  },
};
</script>

<style lang="less" scoped>
.footer {
  display: flex;
  justify-content: center;
  align-items: center;  
  width: 339*@rem;
  height: 28*@rem;
  // padding: 10 * @rem 0 18 * @rem;
  background-color: #F9F9F9;
  border-radius: 4*@rem;
  margin: 0 auto 24*@rem;
  span {
    font-size: 12 * @rem;
    color: #666666;
  }
  .right-icon {
    width: 6 * @rem;
    height: 9 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 6 * @rem 9 * @rem;
    margin-left: 5 * @rem;
  }
}
</style>
