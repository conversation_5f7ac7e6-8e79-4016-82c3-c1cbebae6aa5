<template>
  <div class="game-tab">
    <template v-if="empty">
      <div v-if="!self" class="empty">
        <div class="text">该用户没有上传分享任何UP资源哦~</div>
      </div>
      <div v-if="self" class="empty">
        <div class="text">您没有上传分享任何UP资源哦~</div>
        <div @click="toPage('UpGame')" class="bottom-button">去上传UP资源</div>
      </div>
    </template>
    <template v-else>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="$t('没有更多了')"
          :offset="50"
          @load="loadMore()"
          class="list"
        >
          <div
            v-for="(item, index) in list"
            :key="index"
            @click="toPage('UpDetail', { gameInfo: item, id: item.id })"
            class="item"
          >
            <img :src="item.titlepic" class="game-img" />
            <div class="center">
              <div class="top">{{ item.title }}</div>
              <div class="bottom">
                <div class="text">{{ item.version }}</div>
                <div class="text">{{ item.size_a }}</div>
              </div>
            </div>
            <div
              @click="
                toPage('UpDetail', {
                  gameInfo: item,
                  id: item.id,
                  download: true,
                })
              "
              class="right"
            >
              下载
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </template>
  </div>
</template>
<script>
import { ApiUserGetUpGameList } from '@/api/views/users';

export default {
  props: {
    mem_id: {
      type: Number,
      default: 0,
    },
    self: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: 1, //up动态的页码
      list: [], //动态列表
      refreshing: false, //是否刷新中
      loading: false, //是否加载更多中
      finished: false, //防抖
      empty: false, //是否为空
    };
  },
  methods: {
    async getList() {
      try {
        const res = await ApiUserGetUpGameList({
          memId: this.mem_id,
          page: this.page,
        });
        if (this.page === 1) this.list = [];
        this.list.push(...res.data.game_list);
        if (!this.list.length) {
          this.empty = true;
        }
        if (res.data.game_list.length < 10) {
          this.finished = true;
        } else {
          this.finished = false;
        }
      } finally {
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getList();
      this.loading = false;
      this.page++;
    },
  },
};
</script>
<style lang="less" scoped>
.game-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ .van-loading {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .empty {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    line-height: 21 * @rem;
    color: #999999;
  }
  .bottom-button {
    margin-top: 22 * @rem;
    width: 120 * @rem;
    height: 30 * @rem;
    background: linear-gradient(221deg, #9df4ff 0%, #4f6cff 87%);
    border-radius: 40 * @rem 40 * @rem 40 * @rem 40 * @rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12 * @rem;
  }
  .list {
    flex: 1;
    padding: 10 * @rem 0;
    .item {
      display: flex;
      height: 56 * @rem;
      justify-content: space-between;
      align-items: center;
      padding: 0 18 * @rem;
      margin-bottom: 20 * @rem;
      .game-img {
        width: 56 * @rem;
        height: 56 * @rem;
      }
      .center {
        flex: 1;
        display: flex;
        height: 100%;
        padding: 4 * @rem 0;
        flex-direction: column;
        justify-content: space-between;
        padding: 0 8 * @rem;
        .top {
          font-size: 14 * @rem;
          font-family:
            PingFang SC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #333333;
        }
        .bottom {
          display: flex;
          font-size: 12 * @rem;
          color: #999999;
          .text {
            margin-right: 10 * @rem;
          }
        }
      }
      .right {
        width: 56 * @rem;
        height: 24 * @rem;
        background: @themeColor;
        border-radius: 30 * @rem 30 * @rem 30 * @rem 30 * @rem;
        color: #fff;
        font-size: 12 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
