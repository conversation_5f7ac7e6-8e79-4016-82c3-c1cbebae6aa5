<template>
  <div class="page game-down-activity">
    <nav-bar-2
      title=""
      :azShow="true"
      :placeholder="false"
      :bgStyle="'transparent-white'"
    >
    </nav-bar-2>

    <div
      class="main"
      :style="{ background: pageData.info.bg_color }"
      v-if="pageData.info"
    >
      <div class="fixed-right">
        <div
          class="btn-fixed"
          @click="openMyResult"
          v-if="activity_status == 2"
        >
          我的奖品
        </div>
        <div class="btn-fixed" @click="goToApplyRecord" v-if="userInfo.token">
          申请记录
        </div>
        <div class="btn-fixed" @click="rulePopup = true">活动规则</div>
      </div>
      <div class="game-banner-container">
        <img :src="pageData.info.bg_img" alt="" />
        <div class="belt"></div>
      </div>
      <div
        class="activity-time-container"
        :class="{ no: activity_status == 0, had: activity_status == 2 }"
      >
        <div class="time-title">活动时间：</div>
        <div class="time-num">
          {{ formatDate(pageData.info.start_time) }}-{{
            formatDate(pageData.info.end_time)
          }}
        </div>
        <div v-if="activity_status == 0" class="time-right"> 活动未开始 </div>
        <div v-if="activity_status == 1" class="time-right"> 火爆进行中 </div>
        <div v-if="activity_status == 2" class="time-right"> 活动已结束 </div>
      </div>
      <!-- 1 -->
      <div class="section-title section-title-1"></div>
      <div class="section-container" v-if="pageData.new_area">
        <div class="section-content section-content-1">
          <div class="new-area-steps">
            <div
              class="step-item"
              v-for="(item, index) in pageData.new_area.list"
              :key="index"
            >
              <div class="step-num">步骤{{ index + 1 }}</div>
              <template v-if="index + 1 == 1">
                <div class="step-content">
                  <div class="step-text">{{ item.title }}</div>
                  <div
                    class="btn step-btn"
                    :class="{ no: item.status == 1 }"
                    v-if="item.status == 1"
                  >
                    已创建
                  </div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: activity_status == 2 || activity_status == 0,
                    }"
                    v-else
                    @click="goToCreate"
                  >
                    去创建
                  </div>
                </div>
              </template>
              <template v-if="index + 1 == 2">
                <div class="step-content">
                  <div class="step-text">{{ item.title }}</div>
                  <div
                    class="btn step-btn"
                    v-if="item.apply_status == 2"
                    :class="{ no: item.apply_status == 2 }"
                    @click="goToApplyRecord"
                  >
                    已上传
                  </div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: item.apply_status == 0,
                    }"
                    v-else-if="item.apply_status == 0"
                    @click="noUpload"
                  >
                    去上传
                  </div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: activity_status == 2 || activity_status == 0,
                    }"
                    v-else
                    @click="goToUpload(1)"
                  >
                    去上传
                  </div>
                </div>
              </template>
              <template v-if="index + 1 == 3">
                <gift-pool
                  :info="item"
                  @takeGift="handleTakeGift(1)"
                ></gift-pool>
              </template>
            </div>
          </div>
          <div class="new-area-tips">
            <div class="tip-icon"></div>
            <div class="tips-text">{{ pageData.new_area.desc }}</div>
          </div>
        </div>
      </div>
      <!-- 2 -->
      <div class="section-title section-title-2"></div>
      <div class="section-container" v-if="pageData.game_sign">
        <div class="section-content">
          <div class="content-title">签到目标</div>
          <div class="sign-task">
            <div class="sign-title">{{ pageData.game_sign.sub_title }}</div>
            <div class="user-sign-container">
              <div class="sign-steps">
                <div class="line-bg">
                  <div
                    class="line-on"
                    :style="{
                      width: `${
                        (user_max_login_day /
                          pageData.game_sign.need_login_day) *
                        100
                      }%`,
                    }"
                  ></div>
                </div>
                <div
                  class="sign-step"
                  v-for="(item, index) in pageData.game_sign.need_login_day + 1"
                  :key="index"
                  :class="{
                    on: index <= user_max_login_day && activity_status > 0,
                  }"
                >
                  <div class="sign-step-dot"></div>
                  <div class="sign-step-text">
                    {{ index == 0 ? '开始' : `${index}天` }}
                  </div>
                </div>
              </div>
              <div class="sign-btn had" v-if="pageData.game_sign.status == 2">
                已签到
              </div>
              <div
                class="sign-btn"
                :class="{ no: activity_status == 2 || activity_status == 0 }"
                @click="clickSign"
                v-else
              >
                签到
              </div>
            </div>
          </div>
          <div class="content-title mt-18">签到奖励</div>
          <div class="sign-reward-table">
            <div class="sign-reward-title">
              {{ pageData.game_sign.reward_title }}
            </div>
            <div class="sign-reward-content">
              <div
                class="reward-content-item"
                v-for="(item, index) in pageData.game_sign.reward"
                :key="index"
              >
                <div class="reward-title">{{ item.title }}</div>
                <div class="reward-num">{{ item.desc }}</div>
              </div>
            </div>
          </div>
          <div class="content-title mt-18">
            中奖信息
            <div class="title-right-btn" @click="openSignResultPopup">
              虚席以待，活动结束公布
            </div>
          </div>
        </div>
      </div>
      <!-- 3 -->
      <div class="section-title section-title-3"></div>
      <div class="section-container" v-if="pageData.share">
        <div class="section-content section-content-1">
          <div class="new-area-steps">
            <div
              class="step-item"
              v-for="(item, index) in pageData.share.list"
              :key="index"
            >
              <div class="step-num">步骤{{ index + 1 }}</div>
              <template v-if="index + 1 == 1">
                <div class="step-content">
                  <div class="step-text">{{ item.title }}</div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: activity_status == 2 || activity_status == 0,
                    }"
                    @click="handleShare"
                  >
                    分享
                  </div>
                </div>
              </template>
              <template v-if="index + 1 == 2">
                <div class="step-content">
                  <div class="step-text">{{ item.title }}</div>
                  <div
                    class="btn step-btn"
                    v-if="item.apply_status == 2"
                    :class="{ no: item.apply_status == 2 }"
                    @click="goToApplyRecord"
                  >
                    已上传
                  </div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: item.apply_status == 0,
                    }"
                    v-else-if="item.apply_status == 0"
                    @click="noUpload"
                  >
                    去上传
                  </div>
                  <div
                    class="btn step-btn"
                    :class="{
                      no: activity_status == 2 || activity_status == 0,
                    }"
                    v-else
                    @click="goToUpload(2)"
                  >
                    去上传
                  </div>
                </div>
              </template>
              <template v-if="index + 1 == 3">
                <gift-pool
                  :info="item"
                  @takeGift="handleTakeGift(2)"
                ></gift-pool>
              </template>
            </div>
          </div>
          <div class="new-area-tips">
            <div class="tip-icon"></div>
            <div class="tips-text">{{ pageData.share.desc }}</div>
          </div>
        </div>
      </div>
      <!-- 4 -->
      <div class="section-title section-title-4"></div>
      <div class="section-container" v-if="pageData.exchange_list">
        <div class="section-content">
          <div class="content-title">我的积分</div>
          <div class="score-container">
            <div class="my-score">{{ pageData.exchange_list.my_integral }}</div>
            <div class="score-menus">
              <div class="score-menu" @click="clickGetScores">
                <img
                  class="menu-icon"
                  src="@/assets/images/game-down-activity/score-menu-1.png"
                  alt=""
                />
                <div class="menu-title">获取积分</div>
              </div>
              <div class="score-menu" @click="openScoreDetailPopup">
                <img
                  class="menu-icon"
                  src="@/assets/images/game-down-activity/score-menu-2.png"
                  alt=""
                />
                <div class="menu-title">积分明细</div>
              </div>
              <div class="score-menu" @click="openExchangeRecordPopup">
                <img
                  class="menu-icon"
                  src="@/assets/images/game-down-activity/score-menu-3.png"
                  alt=""
                />
                <div class="menu-title">兑奖记录</div>
              </div>
            </div>
            <div class="score-tips" v-html="pageData.exchange_list.desc"> </div>
          </div>
          <div class="content-title mt-18">兑换区</div>
          <div class="exchange-list">
            <template v-for="(item, index) in pageData.exchange_list.list">
              <div
                class="exchange-item"
                :key="index"
                v-if="!(index > 2 && !isExpand)"
              >
                <div class="exchange-limit">
                  {{ item.max_num > 0 ? `限${item.max_num}次` : '不限' }}
                </div>
                <div class="exchange-item-img">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="exchange-item-center">
                  <div class="exchange-title">{{ item.title }}</div>
                  <div class="exchange-desc">{{ item.desc }}</div>
                </div>
                <div class="exchange-item-right">
                  <div
                    class="exchange-btn"
                    :class="{ can: activity_status == 1 }"
                    @click="handleExchange(item)"
                  >
                    立即兑换
                  </div>
                  <div class="exchange-cost">
                    消耗{{ item.need_integral }}积分
                  </div>
                </div>
              </div>
            </template>
            <template v-if="pageData.exchange_list.list.length > 3">
              <div
                class="exchange-down"
                v-if="!isExpand"
                @click="isExpand = !isExpand"
              >
                查看更多<i></i>
              </div>
              <div
                class="exchange-down up"
                v-else
                @click="isExpand = !isExpand"
              >
                收起<i></i>
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- 5 幸运锦鲤 -->
      <div class="section-title section-title-5"></div>
      <div class="section-container" v-if="pageData.lucky">
        <div class="section-content">
          <div class="content-title">参与条件</div>
          <div class="condition">
            <div class="condition-title">{{ pageData.lucky.desc }}</div>
            <div
              class="condition-btn"
              :class="{ no: activity_status == 2 || activity_status == 0 }"
              @click="goToRecharge"
            >
              {{ pageData.lucky.status == 1 ? '已达成' : '去充值' }}
            </div>
          </div>

          <div
            class="content-title mt-18"
            v-if="pageData.lucky.join_reward.length"
          >
            参与礼包
          </div>

          <gift-pool
            :info="luckyGiftPoolData"
            class="ml-1"
            v-if="pageData.lucky.join_reward.length"
            @takeGift="handleTakeGift(3)"
          ></gift-pool>
          <div class="content-title mt-18">锦鲤大奖</div>

          <div class="sign-reward-table">
            <div class="sign-reward-title">
              {{ pageData.lucky.reward_title }}
            </div>
            <div class="sign-reward-content">
              <div
                class="reward-content-item"
                v-for="(item, index) in pageData.lucky.reward"
                :key="index"
              >
                <div class="reward-title">{{ item.title }}</div>
                <div class="reward-num">{{ item.desc }}</div>
              </div>
            </div>
          </div>

          <div class="content-title mt-18">
            中奖信息
            <div class="title-right-btn" @click="openLuckyResultPopup">
              虚席以待，活动结束公布
            </div>
          </div>
        </div>
      </div>
      <div class="no-more">没有更多了</div>
    </div>

    <!-- 普通提示信息弹窗 -->
    <van-popup
      v-model="tipPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="tip-popup"
    >
      <div class="tip-title">提示</div>
      <div class="tip-content">
        {{ tipContent }}
      </div>
      <div class="operation-bar">
        <div class="btn operation-btn confirm" @click="tipPopup = false">
          知道了
        </div>
      </div>
    </van-popup>

    <!-- 积分明细弹窗 scoreDetailPopup -->
    <van-popup
      v-model="scoreDetailPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container score-detail-popup"
    >
      <div class="popup-close" @click="scoreDetailPopup = false"></div>
      <div class="popup-title score-detail-title"></div>
      <div class="popup-content-bg">
        <div class="popup-content">
          <div class="score-list">
            <div
              class="score-item"
              v-for="(item, index) in scoreDetailList"
              :key="index"
            >
              <div class="score-left">
                <div class="score-title">{{ item.title }}</div>
                <div class="score-time">{{ item.create_time }}</div>
              </div>
              <div class="score-num">{{ item.desc }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 兑换记录弹窗 exchangeRecordPopup -->
    <van-popup
      v-model="exchangeRecordPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container exchange-record-popup"
    >
      <div class="popup-close" @click="exchangeRecordPopup = false"></div>
      <div class="popup-title exchange-record-title"></div>
      <div class="popup-content-bg">
        <div class="popup-content">
          <div class="record-tips">
            注意：绑定平台币及金币奖励领取后直接发送至平台账户，兑换奖励礼包需至游戏中输入礼包码进行兑换
          </div>
          <div class="record-list">
            <div
              class="record-item"
              v-for="(item, index) in exchangeRecordList"
              :key="index"
            >
              <div class="record-left">
                <div class="record-title">{{ item.title }}</div>
                <div class="record-cardpass" v-if="item.is_card">
                  礼包码：<span>{{ item.extra }}</span>
                </div>
                <div class="record-time">{{ item.create_time }}</div>
              </div>
              <div class="record-right" v-if="item.is_card">
                <div class="copy-btn" @click="copy(item.extra)">复制</div>
              </div>
              <div class="record-right" v-else>
                {{ item.desc }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 幸运锦鲤中奖名单弹窗 luckyResultPopup -->
    <van-popup
      v-model="luckyResultPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container lucky-result-popup"
    >
      <div class="popup-close" @click="luckyResultPopup = false"></div>
      <div class="popup-title lucky-result-title"></div>
      <div class="popup-content-bg">
        <div class="popup-content">
          <div class="lucky-result-list">
            <div class="result-title">
              <div class="title">玩家信息</div>
              <div class="desc">获取奖励</div>
            </div>
            <template v-if="luckyResult.list && luckyResult.list.length">
              <div class="result-content">
                <div
                  class="result-content-item"
                  v-for="(item, index) in luckyResult.list"
                  :key="index"
                >
                  <div class="title">{{ item.nickname }}</div>
                  <div class="desc">{{ item.desc }}</div>
                </div>
              </div>
              <div class="result-more" v-if="luckyResult.list.length > 8">
                上下滑动查看更多
              </div>
            </template>
            <content-empty v-else></content-empty>
          </div>
          <div class="lucky-reward-list">
            <div
              class="lucky-reward-item"
              v-for="(item, index) in luckyResult.prize_info"
              :key="index"
            >
              <div class="item-card">
                <img class="icon" :src="item.img" alt="" />
                <div class="num">{{ item.desc }}</div>
              </div>
              <div class="item-content">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 签到奖励中奖名单弹窗 signResultPopup -->
    <van-popup
      v-model="signResultPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container lucky-result-popup"
    >
      <div class="popup-close" @click="signResultPopup = false"></div>
      <div class="popup-title sign-result-title"></div>
      <div class="popup-content-bg">
        <div class="popup-content">
          <div class="lucky-result-list">
            <div class="result-title">
              <div class="title">玩家信息</div>
              <div class="desc">获取奖励</div>
            </div>
            <template v-if="signResult.list && signResult.list.length">
              <div class="result-content">
                <div
                  class="result-content-item"
                  v-for="(item, index) in signResult.list"
                  :key="index"
                >
                  <div class="title">{{ item.nickname }}</div>
                  <div class="desc">{{ item.desc }}</div>
                </div>
              </div>
              <div class="result-more" v-if="signResult.list.length > 8">
                上下滑动查看更多
              </div>
            </template>
            <content-empty v-else></content-empty>
          </div>
          <div class="lucky-reward-list">
            <div
              class="lucky-reward-item"
              v-for="(item, index) in signResult.prize_info"
              :key="index"
            >
              <div class="item-card">
                <img class="icon" :src="item.img" alt="" />
                <div class="num">{{ item.desc }}</div>
              </div>
              <div class="item-content">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 中奖结果弹窗 resultPopup -->
    <van-popup
      v-model="resultPopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container result-popup"
    >
      <div class="popup-close" @click="resultPopup = false"></div>
      <!-- 是否中奖 -->
      <template v-if="resultList.length">
        <div class="popup-title result-title"></div>
        <div class="popup-content-bg">
          <div class="popup-content">
            <template v-for="(item, index) in resultList">
              <div :key="'result' + index">
                <div
                  class="popup-section-title ml-20"
                  :class="{ 'mt-18': index > 0 }"
                >
                  {{ item.type }}
                </div>
                <div class="gift-pool-result">
                  <div class="gift-content">
                    <div class="gift-pool-title">{{ item.title }}</div>
                    <div class="gift-pool-desc">
                      {{ item.desc }}
                    </div>
                  </div>

                  <div class="gift-pool-reward" v-if="item.card_pass">
                    <div class="reward-icon"></div>
                    <div class="reward-text">礼包码：{{ item.card_pass }}</div>
                    <div class="reward-btn copy" @click="copy(item.card_pass)">
                      复制
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <div class="result-tips">
              <div class="tips-title">温馨提示</div>
              <div class="tips-content">
                实物奖励发送至对应账号 <br />
                游戏礼包请凭礼包码至游戏中兑换 <br />
                您也可在我的—我的礼包查看礼包码 <br />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="popup-title result-no-title"></div>
        <div class="popup-content-bg">
          <div class="popup-content">
            <content-empty
              tips="很遗憾您未中奖"
              :emptyImg="emptyImg"
            ></content-empty>
          </div>
        </div>
        <div class="result-no-tips">请及时领取您的活动奖励</div>
      </template>
    </van-popup>

    <!-- 活动规则弹窗 -->
    <van-popup
      v-model="rulePopup"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="popup-container rule-popup"
    >
      <div class="popup-close" @click="rulePopup = false"></div>
      <div class="popup-title rule-title"></div>
      <div class="popup-content-bg">
        <div class="popup-content rule-content">
          <div class="popup-section-title">活动时间</div>
          <div class="p">2024年3月1日至2024年3月3日</div>
          <div class="popup-section-title">活动范围</div>
          <div class="p">部分专服</div>
          <div class="popup-section-title">活动规则</div>
          <div
            class="rule-content-html"
            v-if="pageData.info"
            v-html="pageData.info.rule_content"
          ></div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  platform,
  BOX_goToGame,
  BOX_checkInstall,
  BOX_openApp,
  BOX_downloadGame,
} from '@/utils/box.uni.js';
import { ApiIndexGetDownActivityInfo } from '@/api/views/home.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import {
  ApiDownActivityExchange,
  ApiDownActivityPointLog,
  ApiDownActivityExchangeLog,
  ApiDownActivityGameSign,
  ApiDownActivityLuckyReceive,
  ApiDownActivityRebateReceive,
  ApiDownActivityLotteryLog,
  ApiDownActivityCheckLottery,
} from '@/api/views/downActivity.js';
import giftPool from './components/gift-pool/index.vue';
import emptyImg from '@/assets/images/game-down-activity/empty-bg.png';
import useActivityDialog from './components/activity-dialog/index.js';
import { BOX_login } from '@/utils/box.uni.js';

import { mapGetters } from 'vuex';
// // 函数式调用弹窗
// useActivityDialog({
//   titie: "提示",
//   desc: "恭喜你获得XXXXXXX",
//   content: "",
//   tips: "",
//   showConfirm: true,
//   showCancel: true,
//   confirmText: "我知道了",
//   cancelText: "我是取消",
//   onConfirm: () => {},
//   onCancel: () => {},
// });
export default {
  name: 'GameDownActivity',
  components: {
    giftPool,
  },
  data() {
    return {
      emptyImg,
      pageId: 44,
      pageData: {},
      rulePopup: false,

      tipPopup: false,
      tipContent: '当前活动尚未开始', // 提示弹窗内容

      isExpand: false, //兑换列表是否展开

      scoreDetailPopup: false, // 积分明细弹窗
      scoreDetailList: [],

      exchangeRecordPopup: false, // 兑换记录弹窗
      exchangeRecordList: [],

      resultPopup: false, // 中奖结果弹窗
      resultList: [], // 中奖数据

      luckyResultPopup: false, // 幸运锦鲤中奖名单弹窗
      luckyResult: {},
      signResultPopup: false, // 签到奖励中奖名单弹窗
      signResult: {},
    };
  },

  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    // 活动状态 0=未开始 1=进行中 2=已结束
    activity_status() {
      return this.pageData?.info?.activity_status ?? 0;
    },

    gameInfo() {
      return this.pageData?.game ?? {};
    },

    luckyGiftPoolData() {
      return {
        ...this.pageData.lucky,
        title: '达成参与条件可领取',
        reward_desc: this.pageData.lucky.join_reward,
        receive_status: this.pageData.lucky.status,
      };
    },
    user_max_login_day() {
      let day = this.pageData?.game_sign?.user_login_day;
      if (!day) {
        day = 0;
      }
      if (day && day > 3) {
        day = 3;
      }
      return day;
    },
  },
  async created() {
    this.pageId = this.$route.params.id;
    await this.init();
    if (this.activity_status === 2) {
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      await this.openMyResult();
    }
  },
  methods: {
    async init() {
      await this.getPageData();
    },
    async refresh() {
      await this.getPageData();
    },
    async getPageData() {
      const res = await ApiIndexGetDownActivityInfo({ id: this.pageId });
      this.pageData = res.data.list;
    },

    async getLotteryLog(type = 1) {
      const res = await ApiDownActivityLotteryLog({
        act_id: this.pageData.info.id,
        lottery_type: type,
      });
      if (type == 1) {
        // 签到中奖结果
        this.signResult = res.data;
      } else {
        // 锦鲤中奖结果
        this.luckyResult = res.data;
      }
    },

    async handleTakeGift(type) {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false,
        duration: 0,
      });
      let res = null;
      try {
        switch (type) {
          case 1:
            res = await ApiDownActivityRebateReceive({
              act_id: this.pageData.info.id,
              game_id: this.pageData.game.id,
              reward_type: type,
              prize_id: this.pageData.new_area.prize_id,
            });
            break;
          case 2:
            res = await ApiDownActivityRebateReceive({
              act_id: this.pageData.info.id,
              game_id: this.pageData.game.id,
              reward_type: type,
              prize_id: this.pageData.share.prize_id,
            });
            break;
          case 3:
            res = await ApiDownActivityLuckyReceive({
              act_id: this.pageData.info.id,
              prize_id: this.pageData.lucky.prize_id,
            });
            break;
        }
        this.openGiftSuccessPopup(res.data);
        this.$toast.clear();
      } finally {
        this.refresh();
      }
    },
    /**
     * 打开礼包/奖励领取成功的弹窗
     */
    openGiftSuccessPopup(info) {
      if (info.card_pass) {
        useActivityDialog({
          title: this.gameInfo.title,
          desc: info.title,
          content: info.content,
          cardpass: info.card_pass,
          tips: info.desc,
          showCancel: true,
          cancelText: '知道了',
          confirmText: '复制',
          onConfirm: () => {
            this.copy(info.card_pass);
          },
        });
      } else if (info.is_card && !info.card_pass) {
        useActivityDialog({
          title: this.gameInfo.title,
          desc: info.title,
          content: info.content,
          tips: info.desc,
          showCancel: true,
          cancelText: '知道了',
          confirmText: '复制',
          onConfirm: () => {
            this.copy(info.content);
          },
        });
      } else {
        useActivityDialog({
          title: this.gameInfo.title,
          desc: info.title,
          content: info.content,
          tips: info.desc,
          confirmText: '知道了',
        });
      }
    },
    noUpload() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      this.$toast('请先创建账号');
    },
    goToUpload(type = 1) {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      this.toPage('GameDownRebateApply', {
        type: type,
        gameId: this.gameInfo.id,
        actId: this.pageData.info.id,
      });
    },
    /**
     * 去充值
     */
    goToRecharge() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      this.openCurrentGame();
    },
    /**
     * 去创建
     */
    goToCreate() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      this.openCurrentGame();
    },
    async getShareInfo() {
      const res = await ApiCommonShareInfo({
        type: 1,
        id: this.gameInfo.id,
      });
      this.shareInfo = res.data;
    },
    /**
     * 分享
     */
    async handleShare() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      if (this.initData?.share_info?.length) {
        window.BOX.mobShare(1, this.gameInfo.id);
        return false;
      }
      await this.getShareInfo();

      this.$copyText(this.shareInfo.title + this.shareInfo.title_url).then(
        res => {
          this.$toast(this.$t('链接已复制到剪贴板，快去邀请好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请联系客服'),
            lockScroll: false,
          });
        },
      );
    },
    /**
     * 我的中奖情况
     */
    async openMyResult() {
      try {
        const res = await ApiDownActivityCheckLottery({
          act_id: this.pageData.info.id,
        });
        this.resultList = res.data.list;
        this.resultPopup = true;
      } finally {
      }
    },
    // 跳转申请记录
    goToApplyRecord() {
      this.toPage('GameDownRebateRecord');
    },
    /**
     * 打开签到中奖名单
     */
    async openSignResultPopup() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      } else if (this.activity_status == 1) {
        useActivityDialog({
          title: '提示',
          desc: '中奖名单于活动结束后公布<br>当前活动正在火热进行中',
          confirmText: '我知道了',
        });
        return;
      }
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false,
        duration: 0,
      });
      await this.getLotteryLog(1);
      this.$toast.clear();
      this.signResultPopup = true;
    },
    /**
     * 打开锦鲤中奖名单
     */
    async openLuckyResultPopup() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      } else if (this.activity_status == 1) {
        useActivityDialog({
          title: '提示',
          desc: '中奖名单于活动结束后公布<br>当前活动正在火热进行中',
          confirmText: '我知道了',
        });
        return;
      }
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false,
        duration: 0,
      });
      await this.getLotteryLog(2);
      this.$toast.clear();
      this.luckyResultPopup = true;
    },

    /**
     * 打开活动尚未开始弹窗
     */
    openActivityNotStartPopup() {
      useActivityDialog({
        title: '提示',
        desc: '当前活动尚未开始',
        confirmText: '我知道了',
      });
    },
    /**
     * 打开活动结束弹窗
     */
    openActivityOverPopup() {
      useActivityDialog({
        titie: '提示',
        desc: '当前活动已结束',
        showCancel: true,
        confirmText: '打开游戏',
        cancelText: '知道了',
        onConfirm: () => {
          this.openCurrentGame();
        },
      });
    },
    /**
     * 打开游戏
     */
    openCurrentGame() {
      // 未登录过游戏
      // 打开游戏/下载游戏
      if (platform == 'android') {
        // 1.判断是否已下载游戏
        const isDownload = BOX_checkInstall(this.pageData.game.package_name);
        if (isDownload) {
          BOX_openApp(this.pageData.game.package_name);
        } else {
          useActivityDialog({
            title: '提示',
            desc: '请先下载游戏并登录创建游戏账号哦！',
            showCancel: true,
            cancelText: '取消',
            confirmText: '下载游戏',
            onConfirm: () => {
              BOX_downloadGame(this.pageData.game);
            },
          });
        }
      } else {
        // 打开游戏详情页
        BOX_goToGame(
          {
            params: {
              id: this.pageData.info.game_id,
            },
          },
          { id: this.pageData.info.game_id },
        );
      }
    },

    /**
     * 点击签到
     */
    async clickSign() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      // 未登录游戏
      if (this.pageData.game_sign.status == 0) {
        this.openCurrentGame();
      } else if (this.pageData.game_sign.status == 1) {
        // 已登录过游戏，可以签到
        try {
          this.$toast.loading({
            message: '加载中...',
            forbidClick: false,
            duration: 0,
          });
          const res = await ApiDownActivityGameSign({
            prize_id: this.pageData.game_sign.prize_id,
            act_id: this.pageData.info.id,
          });
        } catch (e) {
          this.$toast.clear();
        } finally {
          await this.refresh();
        }
      }
    },
    /**
     * 打开积分明细弹窗
     */
    async openScoreDetailPopup() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      try {
        this.$toast.loading({
          duration: 0,
          message: '加载中...',
          forbidClick: false,
        });
        const res = await ApiDownActivityPointLog({
          act_id: this.pageData.info.id,
        });
        if (!res.data.list?.length) {
          useActivityDialog({
            title: '提示',
            desc: '当前暂无积分明细信息',
            showCancel: true,
            cancelText: '知道了',
            confirmText: '获取积分',
            onConfirm: () => {
              this.openCurrentGame();
            },
          });
          return;
        }
        this.scoreDetailList = res.data.list;
        this.scoreDetailPopup = true;
      } finally {
        this.$toast.clear();
      }
    },
    /**
     * 打开兑换记录弹窗
     */
    async openExchangeRecordPopup() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      try {
        this.$toast.loading({
          duration: 0,
          message: '加载中...',
          forbidClick: false,
        });
        const res = await ApiDownActivityExchangeLog({
          act_id: this.pageData.info.id,
        });
        if (!res.data.list?.length) {
          useActivityDialog({
            title: '提示',
            desc: '当前暂无兑奖记录',
            confirmText: '我知道了',
          });
          return;
        }
        this.exchangeRecordList = res.data.list;
        this.exchangeRecordPopup = true;
      } finally {
        this.$toast.clear();
      }
    },

    /**
     * 点击获取积分
     */
    clickGetScores() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      this.openCurrentGame();
    },
    /**
     * 复制
     */
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },

    formatDate(val) {
      let { year, month, day, time } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },

    /**
     * 点击立即兑换
     */
    async handleExchange(item) {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始
      if (this.activity_status == 0) {
        this.openActivityNotStartPopup();
        return;
      }
      // 活动已结束
      if (this.activity_status == 2) {
        this.openActivityOverPopup();
        return;
      }
      if (this.pageData.exchange_list.my_integral < item.need_integral) {
        // 积分不足，兑换失败
        useActivityDialog({
          title: '提示',
          desc: '兑换失败，当前积分不足',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '获取积分',
          onConfirm: () => {
            this.openCurrentGame();
          },
        });
        return;
      }
      try {
        this.$toast.loading({
          message: '加载中...',
          forbidClick: false,
          duration: 0,
        });
        const res = await ApiDownActivityExchange({
          prize_id: item.prize_id,
          act_id: this.pageData.info.id,
        });
        // const { card_pass, desc, title, content } = res.data;
        this.openGiftSuccessPopup(res.data);
        this.$toast.clear();
      } finally {
        await this.refresh();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.main {
  background: #000000;
  .fixed-right {
    box-sizing: border-box;
    width: 34 * @rem;
    position: fixed;
    right: 0;
    top: 94 * @rem;
    z-index: 10;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10 * @rem 0 0 10 * @rem;
    border: 0.5 * @rem solid rgba(255, 255, 255, 0.3);
    border-right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 10 * @rem;
  }
  .btn-fixed {
    width: 18 * @rem;
    height: 92 * @rem;
    font-size: 14 * @rem;
    color: #fff;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    &:not(:first-of-type) {
      border-top: 0.5px solid rgba(255, 255, 255, 0.3);
    }
  }
  .game-banner-container {
    position: relative;
    width: 100%;
    height: 564 * @rem;
    // height: auto;
    img {
      // border-radius: 0 0 100 * @rem 50 * @rem/0 0 100 * @rem 50 * @rem;
      -webkit-clip-path: circle(72% at 50% 38%);
      clip-path: circle(72% at 50% 38%);
    }
    .belt {
      width: 100%;
      height: 76 * @rem;
      position: absolute;
      left: 0;
      bottom: 0;
      background: url('~@/assets/images/game-down-activity/belt.png') no-repeat;
      background-size: 100% 76 * @rem;
    }
  }
  .activity-time-container {
    position: relative;
    box-sizing: border-box;
    width: 349 * @rem;
    height: 36 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8 * @rem;
    margin: -33 * @rem 10 * @rem 0;
    padding: 0 10 * @rem;
    background: url(~@/assets/images/game-down-activity/time-bg-now.png)
      no-repeat;
    background-size: 349 * @rem 36 * @rem;
    color: #fff;
    font-size: 12 * @rem;
    font-weight: 600;
    &.no {
      background-image: url(~@/assets/images/game-down-activity/time-bg-no.png);
    }
    &.had {
      background-image: url(~@/assets/images/game-down-activity/time-bg-had.png);
      color: #313131;
    }
    .time-title {
      font-weight: 600;
    }
    .time-num {
      margin-left: 0 * @rem;
      font-weight: 600;
    }
    .time-right {
      margin-left: 15 * @rem;
      font-weight: 600;
    }
  }

  .section-title {
    margin: 24 * @rem auto 0;
    &.section-title-1 {
      width: 355 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/game-down-activity/section-title-1.png)
        no-repeat;
      background-size: 355 * @rem 72 * @rem;
    }
    &.section-title-2 {
      width: 355 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/game-down-activity/section-title-2.png)
        no-repeat;
      background-size: 355 * @rem 72 * @rem;
    }
    &.section-title-3 {
      width: 355 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/game-down-activity/section-title-3.png)
        no-repeat;
      background-size: 355 * @rem 72 * @rem;
    }
    &.section-title-4 {
      width: 305 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/game-down-activity/section-title-4.png)
        no-repeat;
      background-size: 305 * @rem 72 * @rem;
    }
    &.section-title-5 {
      width: 193 * @rem;
      height: 72 * @rem;
      background: url(~@/assets/images/game-down-activity/section-title-5.png)
        no-repeat;
      background-size: 193 * @rem 72 * @rem;
    }
  }

  .section-container {
    position: relative;
    width: 347 * @rem;
    margin: 18 * @rem auto 0;
    &::before {
      content: '';
      display: block;
      width: 347 * @rem;
      height: 29 * @rem;
      background: url(~@/assets/images/game-down-activity/section-bg-1.png)
        no-repeat;
      background-size: 347 * @rem 29 * @rem;
      margin-bottom: -1 * @rem;
    }
    &::after {
      content: '';
      display: block;
      width: 347 * @rem;
      height: 29 * @rem;
      background: url(~@/assets/images/game-down-activity/section-bg-3.png)
        no-repeat;
      background-size: 347 * @rem 29 * @rem;
      margin-top: -1 * @rem;
    }
    .section-content {
      box-sizing: border-box;
      background: url(~@/assets/images/game-down-activity/section-bg-2.png)
        no-repeat;
      background-size: 100% 100%;
      min-height: 100 * @rem;
      padding: 0 14 * @rem;
      overflow: hidden;
      &.section-content-1 {
        .new-area-steps {
          box-sizing: border-box;
          position: relative;
          padding-left: 15 * @rem;
          &::before {
            content: '';
            display: block;
            width: 0 * @rem;
            height: 120 * @rem;
            border-left: 2 * @rem dashed #ffcb44;
            position: absolute;
            left: 1 * @rem;
            top: 10 * @rem;
          }
          .step-item {
            .step-num {
              font-size: 15 * @rem;
              color: #313131;
              font-weight: 600;
              position: relative;
              line-height: 16 * @rem;
              padding-top: 10 * @rem;
              &::before {
                content: '';
                width: 15 * @rem;
                height: 18 * @rem;
                position: absolute;
                left: -17 * @rem;
                top: 8 * @rem;
                background: #fff
                  url(~@/assets/images/game-down-activity/title-star.png)
                  no-repeat;
                background-size: 15 * @rem 15 * @rem;
              }
            }
            .step-content {
              font-size: 13 * @rem;
              color: #393838;
              line-height: 14 * @rem;
              margin-top: 10 * @rem;
              display: flex;
              align-items: center;

              .step-text {
                font-size: 13 * @rem;
                color: #393838;
                line-height: 14 * @rem;
                flex: 1;
                min-width: 0;
                margin-right: 10 * @rem;
              }
              .step-btn {
                width: 58 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 13 * @rem;
                color: #ffffff;
                font-weight: 500;
                background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
                &.no {
                  background: linear-gradient(180deg, #d1d1d1 0%, #858585 99%);
                  color: #ffffff;
                }
              }
            }
          }
        }
        .new-area-tips {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          border-radius: 14 * @rem;
          background-color: #fafafa;
          margin-top: 12 * @rem;
          padding: 7 * @rem 15 * @rem;
          .tip-icon {
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/game-down-activity/tip-icon.png)
              no-repeat;
            background-size: 16 * @rem 16 * @rem;
          }
          .tips-text {
            flex: 1;
            min-width: 0;
            margin-left: 6 * @rem;
            font-size: 12 * @rem;
            color: #757575;
            line-height: 17 * @rem;
          }
        }
      }

      .content-title {
        font-size: 15 * @rem;
        color: #313131;
        line-height: 17 * @rem;
        position: relative;
        margin-left: 20 * @rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &::before {
          content: '';
          width: 15 * @rem;
          height: 16 * @rem;
          position: absolute;
          left: -20 * @rem;
          top: -2 * @rem;
          background: url(~@/assets/images/game-down-activity/title-star.png)
            no-repeat;
          background-size: 15 * @rem 15 * @rem;
        }
        .title-right-btn {
          font-size: 11 * @rem;
          color: #757575;
          padding-right: 14 * @rem;
          background: url(~@/assets/images/game-down-activity/right-icon-square.png)
            right center no-repeat;
          background-size: 12 * @rem 12 * @rem;
        }
        &.mt-18 {
          margin-top: 18 * @rem;
        }
      }
    }
  }

  .sign-task {
    box-sizing: border-box;
    background: #fafafa;
    height: 88 * @rem;
    border-radius: 8 * @rem;
    padding: 15 * @rem;
    margin-top: 10 * @rem;
    .sign-title {
      font-size: 13 * @rem;
      color: #313131;
      line-height: 14 * @rem;
      font-weight: 600;
    }
    .user-sign-container {
      margin-top: 13 * @rem;
      display: flex;
      align-items: center;
      .sign-steps {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        .line-bg {
          width: 174 * @rem;
          height: 2 * @rem;
          border-radius: 1 * @rem;
          background: #d9d9d9;
          position: absolute;
          left: 50%;
          top: 3 * @rem;
          transform: translateX(-50%);
          .line-on {
            height: 2 * @rem;
            border-radius: 1 * @rem;
            background: linear-gradient(90deg, #ff3f4a 1%, #ffb26b 100%);
          }
        }
        .sign-step {
          .sign-step-dot {
            border-radius: 50%;
            width: 8 * @rem;
            height: 8 * @rem;
            background-color: rgba(217, 217, 217, 1);
            outline: 3 * @rem solid rgba(217, 217, 217, 0.33);
            margin: 0 auto;
            position: relative;
          }
          .sign-step-text {
            text-align: center;
            margin-top: 9 * @rem;
            font-size: 11 * @rem;
            color: #757575;
            line-height: 12 * @rem;
            margin-top: 9 * @rem;
          }
          &.on {
            .sign-step-dot {
              background-color: #ff6957;
              outline: 3 * @rem solid rgba(255, 178, 108, 0.41);
            }
            .sign-step-text {
              color: #ff6957;
            }
          }
        }
      }
      .sign-btn {
        margin-left: 40 * @rem;
        width: 56 * @rem;
        height: 24 * @rem;
        border-radius: 12 * @rem;
        background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        color: #ffffff;
        font-weight: 500;
        &.had,
        &.no {
          background: linear-gradient(180deg, #d1d1d1 0%, #858585 99%);
        }
      }
    }
  }
  .sign-reward-table {
    width: 319 * @rem;
    margin: 10 * @rem auto 0;
    border-radius: 8 * @rem;
    background-color: #fafafa;
    overflow: hidden;
    .sign-reward-title {
      box-sizing: border-box;
      height: 42 * @rem;
      background: #f2f2f2;
      line-height: 42 * @rem;
      font-size: 13 * @rem;
      color: #313131;
      font-weight: 600;
      padding: 0 15 * @rem;
    }
    .sign-reward-content {
      .reward-content-item {
        font-size: 11 * @rem;
        text-align: center;
        color: #757575;
        display: flex;
        align-items: center;
        height: 36 * @rem;
        .reward-title {
          width: 55%;
          border-top: 1 * @rem solid #f2f2f2;
          height: 36 * @rem;
          line-height: 36 * @rem;
        }
        .reward-num {
          flex: 1;
          min-width: 0;
          border-top: 1 * @rem solid #f2f2f2;
          border-left: 1 * @rem solid #f2f2f2;
          height: 36 * @rem;
          line-height: 36 * @rem;
        }
      }
    }
  }

  .score-container {
    .my-score {
      text-align: center;
      font-size: 28 * @rem;
      color: #ff5350;
      font-weight: 600;
      line-height: 35 * @rem;
      margin-top: 10 * @rem;
    }
    .score-menus {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20 * @rem;
      .score-menu {
        margin: 0 auto;
        display: flex;
        align-items: center;
        .menu-icon {
          width: 16 * @rem;
          height: 16 * @rem;
          margin: 0 auto;
        }
        .menu-title {
          margin-left: 2 * @rem;
          font-size: 14 * @rem;
          font-weight: 500;
          color: #313131;
        }
      }
    }
    .score-tips {
      box-sizing: border-box;
      background: #fafafa;
      border-radius: 8 * @rem;
      font-size: 11 * @rem;
      color: #757575;
      line-height: 16 * @rem;
      padding: 7 * @rem 11 * @rem;
      margin-top: 20 * @rem;
    }
  }
  .exchange-list {
    .exchange-item {
      position: relative;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      background: #fafafa;
      border-radius: 8 * @rem;
      margin-top: 10 * @rem;
      padding: 12 * @rem 12 * @rem;
      overflow: hidden;
      .exchange-limit {
        position: absolute;
        left: 0;
        top: 0;
        width: 51 * @rem;
        height: 16 * @rem;
        border-radius: 0 0 8 * @rem 0;
        background: #f4dfc6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10 * @rem;
        color: #a55001;
      }
      .exchange-item-img {
        width: 53 * @rem;
        height: 53 * @rem;
        border-radius: 6 * @rem;
        overflow: hidden;
        background: #fff;
        img {
          object-fit: contain;
        }
      }
      .exchange-item-center {
        flex: 1;
        min-width: 0;
        margin-left: 12 * @rem;
        .exchange-title {
          font-size: 16 * @rem;
          line-height: 22 * @rem;
          color: #121212;
          font-weight: 500;
        }
        .exchange-desc {
          margin-top: 10 * @rem;
          font-size: 11 * @rem;
          line-height: 11 * @rem;
          color: #757575;
        }
      }
      .exchange-item-right {
        margin-left: 10 * @rem;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .exchange-btn {
          width: 65 * @rem;
          height: 24 * @rem;
          color: #fff;
          background: linear-gradient(180deg, #d1d1d1 0%, #858585 99%);
          border-radius: 12 * @rem;
          line-height: 24 * @rem;
          font-size: 11 * @rem;
          display: flex;
          justify-content: center;
          &.can {
            background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
          }
        }
        .exchange-cost {
          font-size: 10 * @rem;
          color: #757575;
          margin-top: 8 * @rem;
          line-height: 10 * @rem;
          text-align: center;
        }
      }
    }
    .exchange-down {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: #777777;
      padding: 10 * @rem 0 0;
      i {
        display: block;
        width: 6 * @rem;
        height: 7 * @rem;
        margin-left: 5 * @rem;
        background: url(~@/assets/images/game-down-activity/more-exchange.png)
          center center no-repeat;
        background-size: 6 * @rem 7 * @rem;
      }
      &.up {
        i {
          transform: rotate(-90deg);
        }
      }
    }
  }

  .no-more {
    text-align: center;
    line-height: 13 * @rem;
    padding: 26 * @rem 0;
    font-size: 13 * @rem;
    color: #f2f2f2;
  }
}

.condition {
  display: flex;
  align-items: center;
  margin-left: 20 * @rem;
  margin-top: 5 * @rem;
  .condition-title {
    font-size: 12 * @rem;
    color: #393838;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .condition-btn {
    width: 58 * @rem;
    height: 24 * @rem;
    border-radius: 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12 * @rem;
    color: #ffffff;
    margin-left: 10 * @rem;
    background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
    &.no {
      background: linear-gradient(180deg, #d1d1d1 0%, #858585 99%);
    }
  }
}
.tip-popup {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 300 * @rem;
  padding: 20 * @rem 31 * @rem;
  .tip-title {
    font-size: 16 * @rem;
    color: #333333;
    text-align: center;
    font-weight: 600;
    line-height: 40 * @rem;
  }
  .tip-content {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    text-align: center;
    padding: 0 15 * @rem;
    margin-top: 10 * @rem;
  }
  .operation-bar {
    display: flex;
    align-items: center;
    margin: 34 * @rem 0 0;
    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 40 * @rem;
      border-radius: 20 * @rem;
      &.confirm {
        background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
        color: #fff;
      }
      &.cancel {
        background: #f5f5f5;
        color: #333;
      }
    }
  }
}
.popup-container {
  box-sizing: border-box;
  border-radius: 12 * @rem;
  background: transparent;
  .popup-title {
    margin: 0 auto;
  }
  .popup-close {
    width: 24 * @rem;
    height: 24 * @rem;
    position: absolute;
    background: url(~@/assets/images/game-down-activity/popup-close.png)
      no-repeat;
    background-size: 24 * @rem 24 * @rem;
    right: 10 * @rem;
    top: 36 * @rem;
  }
  .popup-content-bg {
    position: relative;
    width: 347 * @rem;
    margin: 18 * @rem auto 0;
    &::before {
      content: '';
      display: block;
      width: 347 * @rem;
      height: 29 * @rem;
      background: url(~@/assets/images/game-down-activity/section-bg-1.png)
        no-repeat;
      background-size: 347 * @rem 29 * @rem;
      margin-bottom: -1 * @rem;
    }
    &::after {
      content: '';
      display: block;
      width: 347 * @rem;
      height: 29 * @rem;
      background: url(~@/assets/images/game-down-activity/section-bg-3.png)
        no-repeat;
      background-size: 347 * @rem 29 * @rem;
      margin-top: -2 * @rem;
    }
    .popup-content {
      box-sizing: border-box;
      background: url(~@/assets/images/game-down-activity/section-bg-2.png)
        no-repeat;
      background-size: 100% 100%;
      min-height: 100 * @rem;
      padding: 0 14 * @rem;
      overflow: hidden;
      .popup-section-title {
        position: relative;
        font-size: 15 * @rem;
        color: #313131;
        line-height: 17 * @rem;
        font-weight: 600;
        &::before {
          content: '';
          width: 15 * @rem;
          height: 16 * @rem;
          position: absolute;
          left: -20 * @rem;
          top: -2 * @rem;
          background: url(~@/assets/images/game-down-activity/title-star.png)
            no-repeat;
          background-size: 15 * @rem 15 * @rem;
        }
      }
    }
  }
}
.score-detail-popup {
  .score-detail-title {
    width: 193 * @rem;
    height: 72 * @rem;
    background: url(~@/assets/images/game-down-activity/score-detail-title.png)
      no-repeat;
    background-size: 193 * @rem 72 * @rem;
  }
  .score-list {
    padding: 0 15 * @rem;
    max-height: 400 * @rem;
    overflow-y: auto;
    .score-item {
      box-sizing: border-box;
      padding: 16 * @rem 0 10 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:not(:last-of-type) {
        border-bottom: 1 * @rem solid #f2f2f2;
      }
      .score-left {
        .score-title {
          font-size: 14 * @rem;
          color: #121212;
          line-height: 20 * @rem;
        }
        .score-time {
          font-size: 12 * @rem;
          color: #7d7d7d;
          line-height: 17 * @rem;
          margin-top: 2 * @rem;
        }
      }
      .score-num {
        font-size: 15 * @rem;
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }
}
.exchange-record-popup {
  .exchange-record-title {
    width: 193 * @rem;
    height: 72 * @rem;
    background: url(~@/assets/images/game-down-activity/exchange-record-title.png)
      no-repeat;
    background-size: 193 * @rem 72 * @rem;
  }
  .record-tips {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    padding: 0 15 * @rem;
  }
  .record-list {
    box-sizing: border-box;
    padding: 0 15 * @rem;
    max-height: 400 * @rem;
    overflow-y: auto;
    .record-item {
      box-sizing: border-box;
      padding: 16 * @rem 0 10 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:not(:last-of-type) {
        border-bottom: 1 * @rem solid #f2f2f2;
      }
      .record-left {
        .record-title {
          font-size: 16 * @rem;
          color: #121212;
          line-height: 22 * @rem;
        }
        .record-cardpass {
          margin-top: 2 * @rem;
          font-size: 14 * @rem;
          color: #121212;
          line-height: 20 * @rem;
          span {
            color: #ff4d4f;
          }
        }
        .record-time {
          font-size: 12 * @rem;
          color: #7d7d7d;
          line-height: 17 * @rem;
          margin-top: 2 * @rem;
        }
      }
      .record-right {
        font-size: 14 * @rem;
        color: #ff4d4f;
        .copy-btn {
          width: 46 * @rem;
          color: #ff4d4f;
          height: 25 * @rem;
          border-radius: 8 * @rem;
          border: 1 * @rem solid #ff4d4f;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
.result-popup {
  overflow-y: hidden;
  .result-title {
    width: 237 * @rem;
    height: 94 * @rem;
    background: url(~@/assets/images/game-down-activity/result-title.png)
      no-repeat;
    background-size: 237 * @rem 94 * @rem;
  }
  .result-no-title {
    width: 193 * @rem;
    height: 94 * @rem;
    background: url(~@/assets/images/game-down-activity/result-no-title.png)
      no-repeat;
    background-size: 193 * @rem 94 * @rem;
  }
  .result-tips {
    box-sizing: border-box;
    width: 319 * @rem;
    border-radius: 8 * @rem;
    background: #fafafa;
    margin: 16 * @rem auto 0;
    padding: 12 * @rem 14 * @rem;
    .tips-title {
      font-size: 13 * @rem;
      color: #121212;
      line-height: 16 * @rem;
      padding-left: 21 * @rem;
      background: url(~@/assets/images/game-down-activity/tip-icon.png) left
        center no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    .tips-content {
      font-size: 12 * @rem;
      color: #757575;
      line-height: 17 * @rem;
      margin-top: 5 * @rem;
    }
  }
  .empty {
    margin-bottom: 30 * @rem;
  }
  /deep/ .van-empty__image {
    width: 159 * @rem;
    height: 106 * @rem;
  }
  .result-no-tips {
    text-align: center;
    font-size: 15 * @rem;
    color: #ffecd5;
    font-weight: 500;
    margin: 10 * @rem auto 0;
  }
}
.rule-popup {
  .rule-title {
    width: 193 * @rem;
    height: 72 * @rem;
    background: url(~@/assets/images/game-down-activity/rule-title.png)
      no-repeat;
    background-size: 193 * @rem 72 * @rem;
  }
  .popup-section-title {
    margin-bottom: 5 * @rem;
  }
  .popup-content-bg {
    .rule-content {
      font-size: 12 * @rem;
      line-height: 18 * @rem;
      padding: 0 21 * @rem 0 32 * @rem;
      .rule-content-html {
        line-height: 18 * @rem;
        font-size: 13 * @rem;
        color: #757575;
      }
      .p {
        margin-bottom: 10 * @rem;
        line-height: 18 * @rem;
        font-size: 13 * @rem;
        color: #757575;
        span {
          color: #ff4d4f;
        }
      }
    }
  }
}

.lucky-result-popup {
  .lucky-result-title {
    width: 281 * @rem;
    height: 72 * @rem;
    background: url(~@/assets/images/game-down-activity/lucky-result-title.png)
      no-repeat;
    background-size: 281 * @rem 72 * @rem;
  }
  .sign-result-title {
    width: 281 * @rem;
    height: 72 * @rem;
    background: url(~@/assets/images/game-down-activity/sign-result-title.png)
      no-repeat;
    background-size: 281 * @rem 72 * @rem;
  }
  .lucky-result-list {
    border-radius: 8 * @rem;
    overflow: hidden;
    .result-title {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      background: #ffedd7;
      height: 42 * @rem;
      .title,
      .desc {
        box-sizing: border-box;
        padding-left: 20 * @rem;
        font-weight: 500;
        width: 50%;
        font-size: 13 * @rem;
        color: #e77624;
      }
    }
    .result-content {
      max-height: 9 * 36 * @rem;
      overflow-y: auto;
      .result-content-item {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        min-height: 42 * @rem;
        padding: 10 * @rem 0;
        &:nth-of-type(odd) {
          background: #fafafa;
        }
        .title,
        .desc {
          box-sizing: border-box;
          padding-left: 20 * @rem;
          padding-right: 10 * @rem;
          width: 50%;
          font-size: 11 * @rem;
          color: #757575;
        }
      }
    }
    .result-more {
      font-size: 11 * @rem;
      color: #757575;
      background: #fafafa;
      height: 42 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .lucky-reward-list {
    display: flex;
    justify-content: center;
    gap: 16 * @rem;
    margin: 24 * @rem auto 0;
    .lucky-reward-item {
      .item-card {
        width: 94 * @rem;
        height: 75 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #fafafa;
        .icon {
          height: 36 * @rem;
          width: auto;
          display: block;
          margin: 0 auto;
        }
        .num {
          font-size: 11 * @rem;
          color: #e77624;
          line-height: 12 * @rem;
          margin-top: 8 * @rem;
          text-align: center;
        }
      }
      .item-content {
        font-size: 12 * @rem;
        color: #313131;
        line-height: 13 * @rem;
        margin-top: 10 * @rem;
        text-align: center;
      }
    }
  }
}

.gift-pool-result {
  box-sizing: border-box;
  background-color: #fafafa;
  border-radius: 8 * @rem;
  margin-top: 10 * @rem;
  .gift-content {
    box-sizing: border-box;
    flex: 1;
    min-width: 0;
    padding: 14 * @rem 15 * @rem;
  }
  .gift-pool-title {
    font-size: 14 * @rem;
    line-height: 20 * @rem;
    font-weight: 600;
    color: #121212;
  }
  .gift-pool-desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    margin-top: 4 * @rem;
    flex: 1;
    min-width: 0;
    span {
      &:not(:last-of-type) {
        &::after {
          content: '、';
        }
      }
    }
  }

  .reward-btn {
    width: 46 * @rem;
    height: 25 * @rem;
    border-radius: 6 * @rem;
    background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12 * @rem;
    color: #ffffff;
    margin-left: 20 * @rem;
    &.copy {
      border: 1 * @rem solid #ff4d4f;
      color: #ff4d4f;
      background: transparent;
    }
  }
  .gift-pool-reward {
    display: flex;
    height: 45 * @rem;
    align-items: center;
    border-top: 0.5px solid #ebebeb;
    margin: 0 14 * @rem;
    .reward-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      background: url(~@/assets/images/game-down-activity/reward-icon.png)
        no-repeat;
      background-size: 18 * @rem 18 * @rem;
      margin-right: 4 * @rem;
    }
    .reward-text {
      flex: 1;
      min-width: 0;
      font-size: 13 * @rem;
      color: #757575;
    }
  }
}
</style>
