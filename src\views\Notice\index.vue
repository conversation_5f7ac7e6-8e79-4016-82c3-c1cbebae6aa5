<template>
  <div class="notice-page">
    <nav-bar-2 :title="title" :border="true">
      <template #right>
        <div class="read-all" @click="readAll"></div>
      </template>
    </nav-bar-2>
    <div class="tab-bar">
      <div
        class="tab-item btn"
        v-for="(tab, tabIndex) in tabList"
        :key="tabIndex"
        :class="{ on: current === tabIndex }"
        @click="tapNav(tabIndex)"
      >
        <span :class="{ dot: hasDot(tabIndex) }"
          >{{ tab.name
          }}{{
            tabIndex > 2
              ? ''
              : `(${parseInt(tab.notice) > 99 ? '99+' : tab.notice})`
          }}</span
        >
      </div>
      <div
        class="line"
        :style="{ left: `${current * 75 * remNumberLess}rem` }"
      ></div>
    </div>
    <div class="notice-container" ref="notice">
      <template v-for="(list, listIndex) in noticeList">
        <yy-list
          v-model="list.loadingObj"
          :finished="list.finished"
          @refresh="onRefresh(listIndex)"
          @loadMore="loadMore(listIndex)"
          :check="false"
          :key="listIndex"
          class="notice-content"
          v-show="listIndex == current"
          :empty="list.empty"
          :tips="$t('暂时没有新的消息哦')"
          :emptyImg="emptyImg"
        >
          <div class="notice-list">
            <template v-if="listIndex == 0">
              <comment-item
                class="comment-item"
                v-for="(item, index) in list.data"
                :key="index"
                :info="item"
              ></comment-item>
            </template>
            <template v-if="listIndex == 1">
              <comment-item
                class="comment-item"
                v-for="(item, index) in list.data"
                :key="index"
                :info="item"
                :self="true"
              ></comment-item>
            </template>
            <template v-if="listIndex == 2">
              <feedback-item
                v-for="(item, index) in list.data"
                :key="index"
                :info="item"
              ></feedback-item>
            </template>
            <template v-if="listIndex == 3">
              <notice-item
                v-for="(item, index) in list.data"
                :key="index"
                :info="item"
              ></notice-item>
            </template>
            <template v-if="listIndex == 4">
              <remind-item
                v-for="item in list.data"
                :key="item.id"
                :info="item"
              ></remind-item>
            </template>
          </div>
        </yy-list>
      </template>
    </div>
  </div>
</template>

<script>
import emptyImg from '@/assets/images/notice/notice-empty.png';
import { remNumberLess } from '@/common/styles/_variable.less';
// 反馈通知
import { ApiFeedbackInform, ApiFeedbackRead } from '@/api/views/feedback.js';
import { ApiCommentSetCommentRead } from '@/api/views/comment.js';
import {
  ApiCommentReplyMe,
  ApiCommentMine,
  ApiCommentCommentRead,
} from '@/api/views/comment.js';
import { ApiUserInform, ApiServerMyList } from '@/api/views/users.js';
import CommentItem from './components/comment-item/index.vue';
import FeedbackItem from './components/feedback-item/index.vue';
import NoticeItem from './components/notice-item/index.vue';
import RemindItem from './components/remind-item/index.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'notice',
  components: {
    CommentItem,
    FeedbackItem,
    NoticeItem,
    RemindItem,
  },
  data() {
    return {
      emptyImg,
      remNumberLess,
      title: this.$t('消息中心'),
      current: 3,
      tabList: [
        {
          name: this.$t('回复'),
          index: 0,
          dot: false,
          notice: 0,
          api: ApiCommentReplyMe,
        },
        {
          name: this.$t('评论'),
          index: 1,
          dot: false,
          notice: 0,
          api: ApiCommentMine,
        },
        {
          name: this.$t('反馈'),
          index: 2,
          dot: false,
          notice: 0,
          api: ApiFeedbackInform,
        },
        {
          name: this.$t('通知'),
          index: 3,
          dot: false,
          notice: 0,
          api: ApiUserInform,
        },
        {
          name: this.$t('提醒'),
          index: 4,
          dot: false,
          notice: 0,
          api: ApiServerMyList,
        },
      ],
      noticeList: [],
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
    }),
  },
  watch: {
    async current(val) {
      switch (parseInt(val)) {
        case 1: // 消除评论小红点
          await ApiCommentCommentRead({ type: 1 });
          this.SET_UNREAD_COUNT();
          break;
        case 2: // 消除反馈小红点
          await ApiFeedbackRead();
          this.SET_UNREAD_COUNT();
          break;
        default:
          break;
      }
    },
  },
  created() {
    this.tabList.forEach(nav => {
      this.noticeList.push({
        data: [],
        finished: false,
        loadingObj: {
          loading: false,
          reloading: false,
        },
        empty: false,
        params: {
          page: 1,
          listRows: 10,
        },
      });
    });
  },
  activated() {
    if (this.$route.params.index) {
      this.current = this.$route.params.index;
    }
    this.tabList.forEach(nav => {
      this.getNoticeList(nav.index);
    });
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name == 'Mine') {
        vm.tabList.forEach(nav => {
          vm.getNoticeList(nav.index);
        });
      }
    });
  },
  methods: {
    ...mapActions({
      SET_UNREAD_COUNT: 'system/SET_UNREAD_COUNT',
    }),
    async readAll() {
      const res = await ApiCommentSetCommentRead();
      this.$toast(res.msg);
      this.SET_UNREAD_COUNT();
      await this.getNoticeList();
    },
    async getNoticeList(index = this.current, action = 1) {
      let active = this.noticeList[index];
      if (action === 1) {
        active.params.page = 1;
      } else {
        if (active.finished) {
          return;
        }
        active.params.page++;
      }
      active.loadingObj.loading = true;
      const res = await this.tabList[index].api(active.params);
      active.loadingObj.loading = false;
      // 获取反馈的列表数据
      let list = [];
      switch (parseInt(index)) {
        case 0:
        case 1:
          list = res.data?.comments ?? [];
          break;
        case 2: // 反馈
          list = res.data.feedbackList;
          break;
        case 3: // 通知
        case 4: // 提醒
          list = res.data;
          break;
        default:
          break;
      }
      if (action === 1 || active.params.page === 1) {
        active.data = [];
      }
      this.tabList[index].notice = res.data?.cmt_sum ?? 0;
      active.data.push(...list);
      if (!active.data.length) {
        active.empty = true;
      } else {
        active.empty = false;
      }
      if (list.length < active.params.listRows) {
        active.finished = true;
      } else {
        if (active.finished === true) {
          active.finished = false;
        }
      }
    },
    tapNav(index) {
      if (this.current != index) {
        this.current = index;
        this.$nextTick(() => {
          window.scrollTo(0, 0);
        });
      }
    },
    async onRefresh(index) {
      let active = this.noticeList[index];
      active.finished = false;
      await this.getNoticeList(index);
      active.loadingObj.reloading = false;
    },
    async loadMore(index) {
      let active = this.noticeList[index];
      await this.getNoticeList(index, 2);
      active.loadingObj.loading = false;
    },
    hasDot(index) {
      let result = false;
      switch (parseInt(index)) {
        case 1:
          result = this.unreadCount?.hf > 0;
          break;
        case 2:
          result = this.unreadCount?.fk > 0;
          break;
        case 3:
          result = this.unreadCount?.tz > 0;
          break;
        default:
          result = false;
          break;
      }
      return result;
    },
  },
};
</script>

<style lang="less" scoped>
.notice-page {
  background-color: #f5f5f6;
  min-height: 100vh;
  .read-all {
    width: 20 * @rem;
    height: 40 * @rem;
    background: url('~@/assets/images/read-all.png') center center no-repeat;
    background-size: 20 * @rem 20 * @rem;
  }
  .tab-bar {
    display: flex;
    align-items: center;
    background-color: #fff;
    position: fixed;
    width: 100%;
    height: 48 * @rem;
    z-index: 200;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    .tab-item {
      width: 20%;
      height: 48 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #797979;
      &.on {
        color: #000000;
      }
      span {
        &.dot {
          position: relative;
          &::before {
            content: '';
            width: 5 * @rem;
            height: 5 * @rem;
            border-radius: 50%;
            position: absolute;
            right: -5 * @rem;
            top: -5 * @rem;
            background-color: #fe4a55;
          }
        }
      }
    }
    .line {
      width: 14 * @rem;
      height: 4 * @rem;
      border-radius: 2 * @rem;
      background-color: @themeColor;
      position: absolute;
      bottom: 0 * @rem;
      transform: translateX(30 * @rem);
      transition: 0.3s;
    }
  }
  .notice-container {
    box-sizing: border-box;
    padding-top: 48 * @rem;
    min-height: calc(100vh - 50 * @rem - @safeAreaTop);
    min-height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    display: flex;
    flex-direction: column;
  }
}

.comment-item {
  box-sizing: border-box;
  width: 351 * @rem;
  border-radius: 12 * @rem;
  margin: 12 * @rem auto;
  background-color: #fff;
  padding: 20 * @rem 14 * @rem;
}
</style>
