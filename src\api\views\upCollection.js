import { request } from '../index';
import h5Page from '@/utils/h5Page.js';

/**
 * up合集 - 获取up合集列表
 */
export function ApiCollectGetUpCollectList(params = {}) {
  return request('/api/collect/getUpCollectList', params);
}

/**
 * @function up合集 - 获取up合集信息
 * @param {number} id
 * @param {number} page
 * @param {number} listRows
 */
export function ApiCollectGetUpCollectInfo(params = {}) {
  return request('/api/collect/getUpCollectInfo', params);
}

/**
 * @function up合集 - 获取合集的游戏列表
 * @param {number} id
 * @param {number} page
 * @param {number} listRows
 */
export function ApiCollectGetUpGameList(params = {}) {
  return request('/api/collect/getGameList', params);
}
