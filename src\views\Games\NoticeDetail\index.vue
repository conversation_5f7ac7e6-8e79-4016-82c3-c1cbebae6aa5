<template>
  <div class="notice-detail">
    <nav-bar-2 :title="title" :border="true" :azShow="true"></nav-bar-2>
    <main>
      <div class="wrapper1">
        <img src="~@/assets/images/app-icon2.png" class="avatar" />
        <div class="wrapper1-content">
          <div class="title">三七三三兔耳娘<i class="icon"></i>官方GM</div>
          <div class="time">发布时间：{{ newstime }}</div>
        </div>
      </div>
      <div class="wrapper2">
        <div class="title">{{ title }}</div>
        <div class="article" v-html="article_text"></div>
        <div v-if="game" class="game-info">
          <img :src="game.titlepic" class="game-icon" />
          <div class="game-content">
            <div class="game-title">{{ game.title }}</div>
            <div class="game-tag">
              <div
                v-for="(item, index) in game.app_tag"
                :key="index"
                class="tag-item"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
          <div @click="toGameDetail(game)" class="game-button btn">详情</div>
        </div>
      </div>
      <div class="wrapper3">
        <div class="tab-container">
          <div class="tab-left">
            <div
              :class="{ current: tab_current == 0 }"
              @click="changeTabCurrent(0)"
              class="tab-item"
            >
              评论<span>{{ cmt_sum }}条</span>
            </div>
            <div
              :class="{ current: tab_current == 1 }"
              @click="changeTabCurrent(1)"
              class="tab-item"
            >
              相关
            </div>
          </div>
          <div @click="changeOrder" v-if="tab_current == 0" class="tab-right">
            <template v-if="order == 0">热度<i class="icon hot"></i></template>
            <template v-if="order == 1">最新<i class="icon new"></i></template>
          </div>
        </div>
        <div class="tab-detail">
          <div v-if="tab_current == 0" class="comment-section">
            <template v-if="comment_list.length > 0">
              <CommentItem
                v-for="(item, index) in comment_list"
                :key="index"
                :info="item"
                :classId="101"
              />
            </template>
            <ContentEmpty v-else />
          </div>
          <div v-if="tab_current == 1" class="about-section">
            <template v-if="about_list.length > 0">
              <div
                v-for="(item, index) in about_list"
                :key="index"
                @click="toNoticeDetail(item.id)"
                class="about-item"
              >
                <img :src="item.titlepic" class="about-left" />
                <div class="about-right">
                  <div class="about-title">{{ item.title }}</div>
                  <div class="about-bottom">{{ item.like_count }}赞</div>
                </div>
              </div>
            </template>
            <ContentEmpty v-else />
          </div>
        </div>
      </div>
    </main>
    <div class="page-bottom">
      <div
        class="comment-input"
        @click="toPage('CommentEditor', { source_id: game.id, class_id: 101 })"
      >
        说点什么...
      </div>
      <div class="icon-list">
        <div @click="returnTop" class="item">
          <div class="icon return-top"></div>
          <div class="text">顶部</div>
        </div>
        <div @click="changeLike" class="item">
          <div :class="{ current: like.status == 1 }" class="icon good"></div>
          <div class="text">{{ like.count }}</div>
        </div>
        <div @click="changeCollection" class="item">
          <div
            :class="{ current: collection.status == 1 }"
            class="icon star"
          ></div>
          <div class="text">{{ collection.count }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  ApiGameNewsRead,
  ApiGameNewstGetTagInformation,
  ApiGameNewsCollection,
  ApiGameNewsLike,
} from '@/api/views/game.js';
import { ApiCommentComments } from '@/api/views/comment.js';
import CommentItem from '@/components/comment-item';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';

export default {
  data() {
    return {
      title: '',
      article_text: '',
      news_id: 0,
      news_time: 0,
      game: {},
      tab_current: 0, //当前选中tab
      order: 0, //0按热度，1按最新
      comment_list: [], //评论列表
      cmt_sum: 0, //评论数
      about_list: [], //相关资讯
      collection: {
        status: 0, //0未收藏，1已收藏
        count: 0, //计数
      },
      like: {
        status: 0, //0未点赞，1已点赞,
        count: 0, //计数
      },
    };
  },
  computed: {
    newstime() {
      let time = this.$handleTimestamp(this.news_time);
      return `${time.year}-${time.date} ${time.time}`;
    },
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      this.news_id = this.$route.params.id;
      await this.getPageMain();
      await this.getCommentList();
      await this.getLikeList();
    },
    async changeLike() {
      const res = await ApiGameNewsLike({
        sourceId: this.news_id,
        status: this.like.status == -1 ? 1 : -1,
        // status: 0
      });
      await this.getPageMain();
    },
    async changeCollection() {
      const res = await ApiGameNewsCollection({
        sourceId: this.news_id,
        status: this.collection.status == -1 ? 1 : -1,
      });
      await this.getPageMain();
    },
    changeTabCurrent(item) {
      this.tab_current = item;
    },
    async changeOrder() {
      this.order = !this.order;
      this.tab_current == 0
        ? await this.getCommentList()
        : await this.getLikeList();
    },
    async getPageMain() {
      const res = await ApiGameNewsRead({ id: this.news_id });
      this.title = res.data.title;
      this.news_time = res.data.newstime;
      this.article_text = res.data.newstext;
      this.game = res.data.game_info;
      this.like = { status: res.data.is_like, count: res.data.like_count };
      this.collection = {
        status: res.data.is_collection,
        count: res.data.collection_count,
      };
    },
    async getCommentList() {
      const res2 = await ApiCommentComments({
        page: 1,
        classId: 101,
        sourceId: 245597,
        order: this.order == 0 ? 3 : 2,
      });
      let { comments, cmt_sum } = res2.data;
      this.comment_list = [];
      if (comments && comments.length) {
        this.comment_list.push(...comments);
      }
      this.cmt_sum = cmt_sum;
    },
    async getLikeList() {
      const res3 = await ApiGameNewstGetTagInformation({
        game_id: this.game?.game_id,
      });
      this.about_list = res3.data;
    },
    toGameDetail(game) {
      BOX_goToGame(
        {
          params: {
            id: game.id,
            gameInfo: game,
          },
        },
        { id: game.id },
      );
    },
    toNoticeDetail(id) {
      BOX_openInNewWindow(
        {
          name: 'NoticeDetail',
          params: {
            id: id,
          },
        },
        {
          url: `https://${this.$h5Page.env}game.3733.com/#/notice_detail/${id}`,
        },
      );
    },
    returnTop() {
      window.scrollTo(0, 0);
    },
  },
  components: {
    CommentItem,
  },
};
</script>
<style lang="less" scoped>
.notice-detail {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}
main {
  padding-bottom: 90 * @rem;
}
.wrapper1 {
  display: flex;
  height: 88 * @rem;
  padding: 0 20 * @rem;
  align-items: center;
  box-shadow: 0px 1px 1px 0px rgba(5, 0, 37, 0.04);
  .avatar {
    width: 52 * @rem;
    height: 52 * @rem;
    margin-right: 10 * @rem;
    border-radius: 50%;
  }
  .wrapper1-content {
    flex: 1;
    .title {
      display: flex;
      font-size: 14 * @rem;
      font-weight: 600;
      .icon {
        display: block;
        width: 16 * @rem;
        height: 16 * @rem;
        margin: 0 5 * @rem;
        .image-bg(
          '~@/assets/images/game-notice-detail/notice-detail-icon1.png'
        );
      }
    }
    .time {
      margin-top: 10 * @rem;
      font-size: 11 * @rem;
      color: #999;
    }
  }
}
.wrapper2 {
  border-bottom: 12 * @rem solid #f6f6f6;
  .title {
    margin: 20 * @rem 20 * @rem 22 * @rem;
    font-size: 20 * @rem;
    font-weight: bolder;
  }
  .article {
    font-size: 13 * @rem;
    line-height: 21 * @rem;
    padding: 0 20 * @rem;
    /deep/ img {
      max-width: 100%;
      margin: 5 * @rem auto;
    }
  }
  .game-info {
    background: #f8f8f8;
    border-radius: 12 * @rem;
    display: flex;
    margin: 20 * @rem;
    padding: 12 * @rem;
    align-items: center;
    .game-icon {
      flex: 0 0 64 * @rem;
      width: 64 * @rem;
      height: 64 * @rem;
    }
    .game-content {
      flex: 1;
      margin-left: 8 * @rem;
      overflow: hidden;
      .game-title {
        margin-bottom: 10 * @rem;
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #111111;
        line-height: 18 * @rem;
      }
      .game-tag {
        display: flex;
        white-space: nowrap;
        .tag-item {
          margin-right: 5 * @rem;
          color: #888888;
          line-height: 15 * @rem;
        }
      }
    }
    .game-button {
      flex: 0 0 70 * @rem;
      margin-left: 5 * @rem;
      width: 70 * @rem;
      height: 32 * @rem;
      background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      border-radius: 29 * @rem;
      color: #fff;
      font-size: 14 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.wrapper3 {
  .tab-container {
    display: flex;
    justify-content: space-between;
    padding: 24 * @rem 20 * @rem;
    .tab-left {
      display: flex;
      .tab-item {
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #86888f;
        line-height: 23 * @rem;
        font-size: 18 * @rem;
        margin-right: 15 * @rem;
        &.current {
          color: #333438;
        }
        span {
          margin-left: 5 * @rem;
          font-size: 11 * @rem;
          color: #adafb8;
          font-weight: 600;
          line-height: 14 * @rem;
        }
      }
    }
    .tab-right {
      display: flex;
      align-items: center;
      line-height: 16 * @rem;
      color: #5d5d5d;
      font-size: 13 * @rem;
      .icon {
        width: 20 * @rem;
        height: 20 * @rem;
        margin-left: 5 * @rem;
        .image-bg('~@/assets/images/game-notice-detail/order_icon.png');
        &.hot {
          transform: rotateX(180deg);
        }
      }
    }
  }
  .comment-section {
    padding: 0 15 * @rem;
  }
  .about-section {
    .about-item {
      display: flex;
      margin: 0 20 * @rem 20 * @rem;
      .about-left {
        flex: 0 0 120 * @rem;
        width: 120 * @rem;
        height: 70 * @rem;
        border-radius: 5 * @rem;
      }
      .about-right {
        flex: 1;
        margin-left: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .about-title {
          line-height: 19 * @rem;
          color: #111111;
          font-size: 15 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          font-weight: 600;
        }
        .about-bottom {
          color: #999;
        }
      }
    }
  }
}
.page-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 99;
  height: 70 * @rem;
  background: #ffffff;
  display: flex;
  align-items: center;
  box-shadow: 0 -5 * @rem 6 * @rem 0 rgba(0, 36, 69, 0.03);
  padding: 0 20 * @rem;
  .comment-input {
    flex: 1;
    height: 38 * @rem;
    box-sizing: border-box;
    padding: 12 * @rem;
    background: #f8f8f8;
    border-radius: 20 * @rem;
    margin-right: 20 * @rem;
    color: #999;
  }
  .icon-list {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      .icon {
        display: block;
        width: 22 * @rem;
        height: 22 * @rem;
        &.return-top {
          .image-bg(
            '~@/assets/images/game-notice-detail/notice-detail-return-top.png'
          );
        }
        &.good {
          .image-bg(
            '~@/assets/images/game-notice-detail/notice-detail-good.png'
          );
          &.current {
            .image-bg(
              '~@/assets/images/game-notice-detail/notice-detail-good-current.png'
            );
          }
        }
        &.star {
          .image-bg(
            '~@/assets/images/game-notice-detail/notice-detail-star.png'
          );
          &.current {
            .image-bg(
              '~@/assets/images/game-notice-detail/notice-detail-star-current.png'
            );
          }
        }
      }
      .text {
        font-size: 12 * @rem;
        font-weight: 500;
        color: #9897a4;
        line-height: 15 * @rem;
      }
    }
  }
}
</style>
