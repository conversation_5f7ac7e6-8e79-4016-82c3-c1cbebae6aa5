<template>
  <div class="pay-help-page">
    <nav-bar-2
      :title="pageTitle"
      :placeholder="false"
      bgStyle="transparent"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <section class="first-section">
      <div class="background">
        <div class="level">
          <div class="content" id="now_rank">{{ userPay.pay_level_name }}</div>
        </div>
      </div>
      <div class="container-wrapper">
        <div class="title">
          <span id="curr_rank">{{ userPay.pay_level_name }}</span>
          <span id="next_rank">{{ userPay.next_level_name }}</span>
        </div>
        <div class="container">
          <div class="max-bar">
            <div
              v-if="userPay.pay_sum"
              class="now-bar"
              :style="{ width: `${percentage}%` }"
            ></div>
          </div>
          <div class="content">
            <div class="left">
              {{ $t('财富值') }}:<span id="now_sum">{{ userPay.pay_sum }}</span>
            </div>
            <div class="right">
              {{ $t('还差') }}<span id="lack_sum">{{ userPay.pay_next }}</span
              >{{ $t('财富值升级') }}
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="second-section">
      <div class="title">{{ $t('如何获取财富值') }}</div>
      <ul class="list">
        <li class="item">
          {{ $t('购买游戏盒SVIP') }}
          <div @click="toSvip" class="right-btn btn">{{ $t('购买') }}</div>
        </li>
        <li class="item">
          {{ $t('充值平台币') }}
          <div @click="toPlatformCoin" class="right-btn btn">
            {{ $t('充值') }}
          </div>
        </li>
        <li class="item">{{ $t('游戏内消费') }}</li>
        <li class="item">{{ $t('购买GM权限') }}</li>
        <li class="item">{{ $t('GM转区消费') }}</li>
        <li class="tips">
          {{ $t('注：财富增长值等价于平台币的购买与消费额') }}
        </li>
      </ul>
    </section>
    <section class="third-section">
      <div class="title">{{ $t('财富等级说明') }}</div>
      <div class="table">
        <div class="row">
          <div class="th">{{ $t('财富等级') }}</div>
          <div class="th">{{ $t('财富名称') }}</div>
          <div class="th">{{ $t('财富值') }}</div>
        </div>
        <div class="row" v-for="(item, index) in payList" :key="index">
          <div class="td">{{ item.level_id }}</div>
          <div class="td">{{ item.level_name }}</div>
          <div class="td">{{ item.require_pay }}</div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import { ApiUserPayRank } from '@/api/views/users';
import { BOX_openInNewNavWindow, platform } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      pageTitle: this.$t('财富等级'),
      userPay: {},
      payList: [],
      navbarOpacity: 0,
      vipUrl: '',
      ptbUrl: '',
    };
  },
  computed: {
    percentage() {
      return Math.floor(
        (parseInt(this.userPay.pay_sum) /
          (parseInt(this.userPay.pay_sum) + parseInt(this.userPay.pay_next))) *
          100,
      );
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
    this.vipUrl = `https://${this.$h5Page.env}api.a3733.com/h5/vip/index`;
    this.ptbUrl = `https://${this.$h5Page.env}h5.a3733.com/h5/platcoin/index`;
    window.addEventListener('scroll', this.handleScroll);
    const res = await ApiUserPayRank();
    this.payList = res.data.pay_rank;
    this.userPay = res.data.user_pay;
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    toSvip() {
      BOX_openInNewNavWindow({ name: 'Svip' }, { url: this.vipUrl });
    },
    toPlatformCoin() {
      BOX_openInNewNavWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.pay-help-page {
  .first-section {
    position: relative;
    height: 320 * @rem;
    width: 100%;
    .background {
      width: 100%;
      height: 320 * @rem;
      background-size: 100% 263 * @rem;
      background-repeat: no-repeat;
      overflow: hidden;
      background-image: url(~@/assets/images/users/pay_help_bg_new.png);
      .level {
        position: relative;
        width: 240 * @rem;
        height: 205 * @rem;
        margin: 62 * @rem auto 0;
        background-size: 240 * @rem 205 * @rem;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/users/pay_help_level_new.png);
        position: relative;
        .content {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 128 * @rem;
          width: 100 * @rem;
          height: 20 * @rem;
          text-align: center;
          line-height: 20 * @rem;
          font-size: 14 * @rem;
          font-weight: bold;
          color: #6a73bf;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .container-wrapper {
      box-sizing: border-box;
      position: absolute;
      right: 0;
      bottom: 0 * @rem;
      left: 0;
      width: 339 * @rem;
      height: 90 * @rem;
      margin: auto;
      background-color: #ffffff;
      border-radius: 12 * @rem;
      box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
      padding: 0 28 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16 * @rem;
        height: 22 * @rem;
        line-height: 22 * @rem;
        color: #000000;
        font-weight: bold;
        span {
          font-weight: bold;
        }
      }
      .container {
        width: 280 * @rem;
        margin: 12 * @rem auto 0;
        .max-bar {
          width: 100%;
          height: 8 * @rem;
          background-color: #f3efe7;
          border-radius: 4 * @rem;
          .now-bar {
            width: 0%;
            height: 8 * @rem;
            background-color: @themeColor;
            border-radius: 4 * @rem;
          }
        }
        .content {
          margin-top: 7 * @rem;
          color: #666666;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 11 * @rem;
          line-height: 16 * @rem;
          color: #9a9a9a;
        }
      }
    }
  }
  .second-section {
    padding: 17 * @rem 18 * @rem 6 * @rem;
    border-bottom: 8 * @rem solid #f5f5f6;
    background: #ffffff;

    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .list {
      margin-top: 7 * @rem;
      .item {
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 47 * @rem;
        padding-left: 9 * @rem;
        font-size: 14 * @rem;
        color: #000000;
        border-bottom: 0.5 * @rem solid #e8e8e8;
        &::before {
          content: '';
          width: 4 * @rem;
          height: 4 * @rem;
          border-radius: 50%;
          background-color: @themeColor;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        span {
          color: @themeColor;
        }
        .right-btn {
          height: 25 * @rem;
          padding: 0 14 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: @themeColor;
          font-size: 12 * @rem;
          color: #ffffff;
          border-radius: 13 * @rem;
        }
      }
      .tips {
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 47 * @rem;
        font-size: 14 * @rem;
        color: #9a9a9a;
      }
    }
  }
  .third-section {
    padding: 19 * @rem 18 * @rem 32 * @rem;
    background: #ffffff;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .table {
      box-sizing: border-box;
      width: 100%;
      margin-top: 20 * @rem;
      border-radius: 12 * @rem;
      border: 0.5 * @rem solid #cba597;
      overflow: hidden;
      .row {
        display: flex;
        &:nth-of-type(n + 2) {
          border-top: 0.5 * @rem solid #cba597;
        }
        &:nth-of-type(2n + 1) {
          background-color: #f5f5f6;
        }
      }
      .th,
      .td {
        width: 50%;
        text-align: center;
        font-size: 12 * @rem;
        color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 11 * @rem 5 * @rem;
        &:nth-of-type(n + 2) {
          border-left: 0.5 * @rem solid #cba597;
        }
      }
      .th {
        background-color: #f5e2ce;
        font-size: 14 * @rem;
        color: #7a532a;
        font-weight: normal;
      }
    }
  }
}
</style>
