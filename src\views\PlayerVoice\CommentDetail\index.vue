<template>
  <div class="list-page">
    <nav-bar-2 title="评论详情" :azShow="true"></nav-bar-2>
    <div class="main-comment" v-if="info.comment_id">
      <CommentItem
        :comment="info"
        :showChildren="false"
        :showReplyCount="false"
      ></CommentItem>
    </div>
    <div class="list-container">
      <div class="title">
        <span class="text">全部回复</span>
        <div class="order">
          <span :class="{ active: order == 2 }" @click="changeOrder(2)"
            >正序</span
          >
          <span :class="{ active: order == 1 }" @click="changeOrder(1)"
            >倒序</span
          >
        </div>
      </div>

      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
        tips="暂无数据"
      >
        <div class="comment-list">
          <div class="item" v-for="(item, index) in list" :key="index">
            <CommentItem :comment="item" :showReplyCount="false" :noJump="true" :level="2"></CommentItem>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiCommentReplies } from '@/api/views/comment.js';
import CommentItem from '../components/comment-item';
export default {
  name: 'PastList',
  data() {
    return {
      id: 0,
      sourceId: 0,
      info: {},
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      order: 2, //排序 1倒序 2正序
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  components: {
    CommentItem,
  },
  async created() {
    this.id = this.$route.params.id || 0;
    this.sourceId = this.$route.params.sourceId || 0;
    this.loadingObj.loading = true;
    await this.getList(2);
    this.loadingObj.loading = false;
  },
  methods: {
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
      }
      try {
        const res = await ApiCommentReplies({
          page: this.page,
          listRows: this.listRows,
          classId: 106,
          sourceId: this.sourceId,
          commentId: this.id,
          order: this.order,
        });

        this.info = res.data;
        let list = this.info.replies ?? [];
        if (list.length < this.listRows) {
          this.finished = true;
        }
        this.list.push(...list);
        if (this.list.length == 0) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      } catch {
        this.$toast.clear();
        this.empty = true;
      }
    },
    async onRefresh() {
      await this.getList(2);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
    async changeOrder(order) {
      if (order == this.order) {
        return false;
      }
      this.order = order;
      this.loadingObj.loading = true;
      await this.getList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.list-page {
  .main-comment {
    padding: 20 * @rem 18 * @rem;
    border-bottom: 8 * @rem solid #f8f8f8;
  }

  .list-container {
    padding: 20 * @rem 18 * @rem 0;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        display: flex;
        align-items: center;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #333333;
        line-height: 20 * @rem;

        &::before {
          content: '';
          display: block;
          width: 5 * @rem;
          height: 12 * @rem;
          border-radius: 16 * @rem;
          background-color: @themeColor;
          margin-right: 6 * @rem;
        }
      }
    }

    .order {
      display: flex;
      align-items: center;

      span {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #666666;
        line-height: 15 * @rem;
        text-align: left;
        margin-right: 8 * @rem;
        padding-right: 8 * @rem;
        border-right: 1 * @rem solid #d9d9d9;

        &:last-of-type {
          margin-right: 0;
          padding-right: 0;
          border-right: none;
        }

        &.active {
          color: @themeColor;
        }
      }
    }
  }

  .comment-list {
    margin-top: 25 * @rem;
    .item {
      background-color: #fff;
      border-radius: 12 * @rem;
      margin-bottom: 37 * @rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      .feedback {
        border: 1 * @rem solid #f4efe9;
        border-radius: 6 * @rem;
        background: #fcfaf9;
        padding: 10 * @rem;
        margin-left: 34 * @rem;
        margin-top: 15 * @rem;

        .kefu-info {
          display: flex;
          align-items: center;

          img {
            width: 28 * @rem;
            height: 28 * @rem;
            margin-right: 6 * @rem;
            border-radius: 50%;
          }

          .name {
            flex: 1;
            min-width: 0;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            color: #111111;
            line-height: 15 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .feedback-content {
          width: 100%;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #93734d;
          line-height: 15 * @rem;
          text-align: left;
          margin-top: 16 * @rem;
        }
      }
    }
  }
}
</style>
