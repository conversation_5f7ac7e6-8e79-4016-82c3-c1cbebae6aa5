export default [
  {
    path: '/player_voice/:id',
    name: 'PlayerVoice',
    component: () =>
      import(/* webpackChunkName: "notice" */ '@/views/PlayerVoice'),
    meta: {
      keepAlive: true,
      pageTitle: '玩家之声'
    },
  },
  {
    path: '/past_list',
    name: 'PastList',
    component: () =>
      import(/* webpackChunkName: "notice" */ '@/views/PlayerVoice/PastList'),
  },
  {
    path: '/add_comment',
    name: 'AddComment',
    component: () =>
      import(/* webpackChunkName: "notice" */ '@/views/PlayerVoice/AddComment'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/player_voice_detail/:sourceId/:id',
    name: 'PlayerVoiceDetail',
    component: () =>
      import(
        /* webpackChunkName: "notice" */ '@/views/PlayerVoice/PlayerVoiceDetail'
      ),
  },
  {
    path: '/comment_detail/:sourceId/:id',
    name: 'CommentDetail',
    component: () =>
      import(
        /* webpackChunkName: "notice" */ '@/views/PlayerVoice/CommentDetail'
      ),
  },
];
