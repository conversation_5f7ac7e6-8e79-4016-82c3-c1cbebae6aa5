<template>
  <div class="game-try-detail-page">
    <nav-bar-2
      title="任务详情"
      :bgStyle="navbarOpacity < 0.5 ? 'transparent-white' : 'transparent'"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
    </nav-bar-2>
    <div class="top-container">
      <div class="game-bg">
        <img :src="game.video_thumb" alt="" />
      </div>
      <div
        class="game-container"
        @click="toPage('GameDetail', { id: game.id })"
      >
        <div class="game-icon">
          <img :src="game.titlepic" alt="" />
        </div>
        <div class="game-info">
          <div class="game-title">请体验游戏：{{ game.title }}</div>
          <div class="task-time">【任务时间】{{ trial_info.task_time }}</div>
        </div>
      </div>
    </div>
    <div class="main" v-if="trial_info.id">
      <div class="section">
        <div class="tag">
          <p>任 务 {{ trial_info.take_trial_num }}</p>
        </div>
        <div class="section-title">本任务分为1个阶段</div>
        <div class="content">
          <p>【任务要求】{{ trial_info.task_content }}</p>
          <p>【任务奖励】{{ trial_info.task_reward }}</p>
          <p>【任务说明】{{ trial_info.desc }}</p>
        </div>
        <div class="banner" v-if="game.video_thumb">
          <img :src="game.video_thumb" alt="" />
        </div>
      </div>
      <div class="section">
        <div class="section-title">任务规则</div>
        <div class="content" v-html="rule_desc"></div>
      </div>
    </div>
    <div class="bottom-container" v-if="trial_info.id">
      <div class="bottom-fixed">
        <div
          class="operation-btn btn"
          :class="{ empty: ![0, 4].includes(Number(trial_info.trial_status)) }"
          @click="handleClick"
        >
          {{ trial_status_text }}
        </div>
        <div
          class="operation-btn blue btn"
          @click="toPage('GameDetail', { id: game.id })"
        >
          前往下载
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiTrialPlayDetail,
  ApiTrialPlayTake,
  ApiTrialPlayTakeReward,
} from '@/api/views/gameTry.js';
export default {
  data() {
    return {
      navbarOpacity: 0,
      id: 0,
      game: {},
      task_num: 0,
      trial_info: {},
      rule_desc: '',
    };
  },
  computed: {
    trial_status_text() {
      let trial_status = Number(this.trial_info.trial_status) ?? 3;
      let text = '';
      switch (trial_status) {
        case 0:
          text = '领取任务';
          break;
        case 1:
          text = '进行中';
          break;
        case 2:
          text = '已完成';
          break;
        case 3:
          text = '人数已满';
          break;
        case 4:
          text = '领取奖励';
          break;
        case 5:
          text = '已结束';
          break;
      }
      return text;
    },
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);

    this.id = this.$route.params.id;
    await this.getDetail();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async handleClick() {
      let trial_status = Number(this.trial_info.trial_status) ?? 3;
      switch (trial_status) {
        case 0: // 领取任务
          await ApiTrialPlayTake({ trial_id: this.trial_info.id });
          await this.getDetail();
          break;
        case 1: // 进行中
          this.$toast('任务进行中');
          break;
        case 2: // 已完成
          this.$toast('任务已完成');
          break;
        case 3: // 人数已满
          this.$toast('任务人数已满');
          break;
        case 4: // 领取奖励
          await ApiTrialPlayTakeReward({ trial_id: this.trial_info.id });
          await this.getDetail();
          break;
        case 5: // 已结束
          this.$toast('任务已结束');
          break;
      }
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async getDetail() {
      const res = await ApiTrialPlayDetail({ trial_id: this.id });
      let { game, task_num, trial_info, rule_desc } = res.data;
      this.game = game;
      this.task_num = task_num;
      this.trial_info = trial_info;
      this.rule_desc = rule_desc;
    },
  },
};
</script>

<style lang="less" scoped>
.game-try-detail-page {
  background-color: #f5f6f6;
  .top-container {
    .game-bg {
      width: 100%;
      height: 180 * @rem;
    }
    .game-container {
      display: flex;
      padding: 0 18 * @rem;
      margin-top: -24 * @rem;
      .game-icon {
        width: 80 * @rem;
        height: 80 * @rem;
      }
      .game-info {
        flex: 1;
        min-width: 0;
        padding-top: 24 * @rem;
        padding-left: 4 * @rem;
        background: url('~@/assets/images/right-icon.png') right 42 * @rem
          no-repeat;
        background-size: 10 * @rem 18 * @rem;
        .game-title {
          font-size: 14 * @rem;
          font-weight: bold;
          color: #222222;
          line-height: 18 * @rem;
          margin-top: 9 * @rem;
          padding-left: 6 * @rem;
          padding-right: 16 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .task-time {
          font-size: 12 * @rem;
          color: #888888;
          line-height: 15 * @rem;
          margin-top: 9 * @rem;
        }
      }
    }
  }
  .main {
    margin-top: 10 * @rem;
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
  }
  .section {
    box-sizing: border-box;
    width: 339 * @rem;
    padding: 16 * @rem;
    background-color: #fff;
    border-radius: 8 * @rem;
    margin: 0 auto 14 * @rem;
    position: relative;
    .tag {
      position: absolute;
      right: 10 * @rem;
      top: 0;
      width: 30 * @rem;
      height: 63 * @rem;
      .image-bg('~@/assets/images/welfare/game-try/task-num-tag.png');
      background-size: 30 * @rem 63 * @rem;
      text-align: center;
      p {
        font-size: 14 * @rem;
        color: #fff;
        width: 1em;
        text-align: center;
        margin: 5 * @rem auto 0;
      }
    }
    .section-title {
      padding: 4 * @rem;
      font-size: 15 * @rem;
      color: #222222;
      line-height: 18 * @rem;
      font-weight: bold;
    }
    .content {
      line-height: 21 * @rem;
      font-size: 12 * @rem;
      color: #444444;
      margin-top: 6 * @rem;
    }
    .banner {
      width: 307 * @rem;
      height: 140 * @rem;
      border-radius: 8 * @rem;
      overflow: hidden;
      margin-top: 10 * @rem;
    }
  }
}
.bottom-container {
  flex-shrink: 0;
  width: 100%;
  height: calc(70 * @rem + @safeAreaBottom);
  height: calc(70 * @rem + @safeAreaBottomEnv);
  .bottom-fixed {
    box-sizing: border-box;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    .fixed-center;
    width: 100%;
    z-index: 2000;
    box-shadow: 0px -2px 8px 0px rgba(0, 74, 217, 0.08);
    padding: 0 18 * @rem;
    height: calc(70 * @rem + @safeAreaBottom);
    height: calc(70 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .operation-btn {
      flex: 1;
      height: 40 * @rem;
      border-radius: 20 * @rem;
      background-color: #ff7a00;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      &:not(:first-of-type) {
        margin-left: 18 * @rem;
      }
      &.empty {
        background-color: #e1e1e1;
      }
      &.blue {
        background-color: #628dff;
      }
    }
  }
}
</style>
