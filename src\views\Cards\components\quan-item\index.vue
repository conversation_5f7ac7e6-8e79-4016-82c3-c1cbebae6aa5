<template>
  <div class="quan-item-component">
    <div class="quan-model">
      <div class="left">
        <div v-if="couponItem.type && couponItem.type != 0" class="subscript">
          {{ subscript() }}
        </div>
        <div class="left-top">
          <div class="amount">
            ¥
            <span :class="{ 'big-size': parseInt(couponInfo.money) < 10000 }">{{
              couponInfo.money
            }}</span>
          </div>
          <div class="info">
            <div class="game-name">{{ couponInfo.title }}</div>
            <div class="tip">
              {{ $t('满') }}{{ couponInfo.reach_money }}{{ $t('元可用') }}
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="quan-total">
            <div
              class="quan-left"
              :style="{ width: couponInfo.remain_percent + '%' }"
            ></div>
          </div>
          <div class="left-num">
            {{ $t('剩余') }}
            <span>{{ couponInfo.remain_percent }}%</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div
          class="get btn"
          :class="{
            had: couponInfo.take_status != 0 || couponInfo.remain_percent == 0,
          }"
          @click="getCoupon(couponInfo)"
        >
          {{
            couponInfo.take_status != 0
              ? $t('已领取')
              : couponInfo.remain_percent != 0
                ? $t('领取')
                : $t('已抢光')
          }}
        </div>
        <div class="date">
          {{ $t('有限期') }}:{{ couponInfo.period }}{{ $t('天') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiCouponTake } from '@/api/views/coupon.js';
import { mapActions } from 'vuex';
import { parse } from 'path';
export default {
  name: 'QuanItem',
  props: ['couponItem'],
  data() {
    return {
      couponInfo: {},
    };
  },
  computed: {},
  created() {
    this.couponInfo = this.couponItem;
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    getCoupon(item) {
      ApiCouponTake({
        couponId: item.id,
      }).then(
        res => {
          this.$toast(res.msg);
          this.couponInfo.take_status = true;
          this.SET_USER_INFO();

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${item.id}`,
            voucher_name: `${item.title}`,
            voucher_amount: `${item.money}`,
          });
        },
        err => {
          if (err.code == 0) {
            this.couponInfo.remain_percent = 0;
          }
        },
      );
    },
    subscript() {
      switch (parseInt(this.couponItem.type)) {
        case 1:
          return 'svip';
        case 2:
          return this.$t('无门槛');
        default:
          return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.quan-item-component {
  width: 100%;
  margin: 18 * @rem 0;
  .quan-model {
    box-sizing: border-box;
    width: 347 * @rem;
    height: 90 * @rem;
    display: flex;
    position: relative;
    background-color: #fff;
    margin: 0 auto;
    padding: 10 * @rem 0;
    &:before {
      content: '';
      width: 15 * @rem;
      height: 15 * @rem;
      border-radius: 50%;
      background-color: #f8f8f8;
      position: absolute;
      left: 248 * @rem;
      top: -9 * @rem;
    }
    &:after {
      content: '';
      width: 15 * @rem;
      height: 15 * @rem;
      border-radius: 50%;
      background-color: #f8f8f8;
      position: absolute;
      left: 248 * @rem;
      bottom: -9 * @rem;
    }
    .left {
      box-sizing: border-box;
      width: 256 * @rem;
      padding-left: 11 * @rem;
      position: relative;
      &:after {
        content: '';
        width: 0;
        height: 71 * @rem;
        border-left: 1 * @rem dashed #e0e0e0;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .subscript {
        position: absolute;
        top: -20 * @rem;
        left: 0;
        padding: 3 * @rem 5 * @rem;
        background: rgba(255, 117, 84, 1);
        color: #fff;
        border-radius: 10 * @rem 0 10 * @rem 0;
      }
      .left-top {
        display: flex;
        align-items: center;
        .amount {
          width: 60 * @rem;
          text-align: center;
          font-size: 17 * @rem;
          color: #ff3c3c;
          white-space: nowrap;
          span {
            font-size: 18 * @rem;
            font-weight: bold;
            &.big-size {
              font-size: 23 * @rem;
            }
          }
        }
        .info {
          margin-left: 15 * @rem;
          flex: 1;
          min-width: 0;
          .game-name {
            font-size: 15 * @rem;
            font-weight: bold;
            color: #010101;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .tip {
            font-size: 11 * @rem;
            color: #999999;
            margin-top: 5 * @rem;
          }
        }
      }
      .left-bottom {
        display: flex;
        align-items: center;
        margin-top: 10 * @rem;
        .quan-total {
          background-color: #fff6e3;
          width: 150 * @rem;
          border-radius: 3 * @rem;
          height: 6 * @rem;
          .quan-left {
            background: linear-gradient(90deg, #ff6e5e, #ffa74a);
            width: 50%;
            height: 6 * @rem;
            border-radius: 3 * @rem;
          }
        }
        .left-num {
          margin-left: 10 * @rem;
          font-size: 12 * @rem;
          color: #666666;
          span {
            color: #ff604b;
          }
        }
      }
    }
    .right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .get {
        font-size: 13 * @rem;
        color: #ffffff;
        width: 65 * @rem;
        height: 25 * @rem;
        background: linear-gradient(60deg, #ff4c39, #ff9c34);
        border-radius: 13 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        &.had {
          background: #d6d6d6;
        }
      }
      .date {
        color: #b5b5b5;
        font-size: 11 * @rem;
        margin-top: 15 * @rem;
      }
    }
  }
}
</style>
