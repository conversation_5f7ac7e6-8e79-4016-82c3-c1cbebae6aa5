<template>
  <div class="new-first-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="game-container">
        <div
          class="game-section"
          ref="today_container"
          v-if="today_list.length"
        >
          <div class="section-title">
            <div class="title-icon"></div>
            <div class="time-bar">{{ $t('今日首发') }}</div>
          </div>
          <div class="game-list">
            <game-item-new
              v-for="(item, index) in today_list"
              :key="item.id"
              :info="item"
              :index="index"
            ></game-item-new>
          </div>
        </div>
        <div
          class="game-section"
          ref="yestoday_container"
          v-if="yesterday_list.length"
        >
          <div class="section-title">
            <div class="title-icon"></div>
            <div class="time-bar">{{ $t('近期新游') }}</div>
          </div>
          <div class="game-list">
            <game-item-new
              v-for="(item, index) in yesterday_list"
              :key="item.id"
              :info="item"
              :index="index"
            ></game-item-new>
          </div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiGameNewGameListV2 } from '@/api/views/game.js';
export default {
  name: 'NewFirst',
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      today_list: [],
      yesterday_list: [],
      gameList: [],
      page: 1,
      listRows: 15,
    };
  },
  activated() {
    this.getGameList();
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      let res = await ApiGameNewGameListV2({
        type: 1,
        page: this.page,
        listRows: this.listRows,
      });
      let { list, today_list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        this.today_list = [];
        this.yesterday_list = [];
      }
      if (today_list) {
        this.today_list = today_list;
      }
      if (list) {
        this.yesterday_list = list;
      }
      this.finished = true;
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGameList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.new-first-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }

  .game-container {
    .game-section {
      background-color: #fff;
      box-sizing: border-box;
      background-color: #fff;
      width: 100%;
      margin: 0 auto;
      padding: 15 * @rem 18 * @rem 6 * @rem;
      .section-title {
        display: flex;
        align-items: center;
        .title-icon {
          width: 4 * @rem;
          height: 12 * @rem;
          background-color: @themeColor;
          border-radius: 12 * @rem;
        }
        .time-bar {
          height: 30 * @rem;
          display: flex;
          align-items: center;
          background-color: #fff;
          font-size: 18 * @rem;
          font-weight: 600;
          color: #000000;
          margin-left: 10 * @rem;
        }
      }
    }
    .game-list {
      .game-item {
        box-sizing: border-box;
        padding: 4 * @rem 0;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
      }
    }
  }
}
</style>
