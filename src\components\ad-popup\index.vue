<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    class="ad-and-coupon-popup"
    :overlay-style="{ 'z-index': '3000' }"
    :closeOnPopstate="false"
    @closed="closePopup"
  >
    <img
      v-if="data.act_info"
      :src="data.act_info.bg_img_url"
      @click="toDetail()"
    />
    <div @click="popup = false" class="close"></div>
  </van-dialog>
</template>

<script>
import { PageName, handleActionCode } from '@/utils/actionCode.js';
import { ApiV2024IndexPopcloseLog } from '@/api/views/system.js';

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  methods: {
    toDetail() {
      this.popup = false;
      if (this.data.click_id) {
        this.CLICK_EVENT(this.data.click_id);
      }
      handleActionCode(this.data.act_info);
    },
    closePopup() {
      this.popup = false;
      this.popupCloseLog(this.data);
      this.$emit('closed');
    },
    async popupCloseLog(info) {
      const res = await ApiV2024IndexPopcloseLog({
        type: info.type,
        pop_id: info.id,
        pop_type: info.pop_type,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ad-and-coupon-popup {
  z-index: 3000 !important;
  background: none;
  overflow: unset;
  padding-bottom: 30 * @rem;
}
.close {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 26 * @rem;
  height: 26 * @rem;
  background-image: url(~@/assets/images/close.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
</style>
