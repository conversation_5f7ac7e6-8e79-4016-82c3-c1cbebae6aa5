<template>
  <div class="deal-item-component">
    <div class="deal-item" v-if="xiaohaoInfo.xh_id">
      <div class="top-content">
        <div class="top-left">
          <div v-if="isDetail" class="current-status">
            {{ $t('当前状态') }}:
          </div>
          <div
            class="status"
            :style="{ color: `${xiaohaoInfo.status_info.color}` }"
          >
            {{ xiaohaoInfo.status_info.str || '' }}
          </div>
          <div
            class="countdown"
            v-if="xiaohaoInfo.countdown && xiaohaoInfo.status == 0"
          >
            ({{ $t('将于') }}{{ xiaohaoInfo.countdown | formatCountDown
            }}{{ $t('后关闭') }})
          </div>
        </div>

        <div class="top-right" v-if="isShowPaySum">
          <div class="game-money"
            >{{ $t('实充￥') }}{{ xiaohaoInfo.trade_snapshot.pay_sum }}</div
          >
        </div>
      </div>
      <div
        class="bottom-content"
        @click="toPage('XiaohaoOrderDetail', { info: xiaohaoInfo })"
      >
        <div class="xiaohao-info">
          <div class="game-icon">
            <img :src="xiaohaoInfo.game.titlepic" alt="" />
          </div>
          <div class="center">
            <div class="game-name">
              {{ xiaohaoInfo.game.main_title
              }}<span class="game-subtitle" v-if="xiaohaoInfo.game.subtitle">{{
                xiaohaoInfo.game.subtitle
              }}</span>
            </div>
            <div class="center-info">
              <div class="server">
                {{ $t('区服') }}：{{ xiaohaoInfo.trade_snapshot.game_area }}
              </div>
              <div class="platforms">
                <div
                  class="plat"
                  v-for="(item, index) in xiaohaoInfo.platforms"
                  :key="index"
                >
                  <img :src="item.icon" />
                </div>
              </div>
            </div>
            <div class="xh-id">{{ $t('小号ID') }}：{{ xiaohaoInfo.xh_id }}</div>
          </div>
          <div class="right">
            <div class="rmb-num">
              ¥ <span>{{ Number(xiaohaoInfo.rmb).toFixed(0) }}</span>
            </div>
          </div>
        </div>
        <div class="specify-man" v-if="xiaohaoInfo.specify_username">
          {{ $t('指定玩家') }}：{{ xiaohaoInfo.specify_username }}
        </div>
      </div>
      <div class="bottom-btn-group">
        <div
          class="operate-btn cancel-btn"
          v-if="statusIn([0])"
          @click="handleCancel()"
        >
          {{ $t('取消') }}
        </div>
        <div
          class="operate-btn delete-btn"
          v-if="statusIn([1, 2])"
          @click="handleDelete()"
        >
          {{ $t('删除') }}
        </div>
        <div
          class="operate-btn help-btn"
          v-if="statusIn([1])"
          @click="handleHelp()"
        >
          {{ $t('如何登录') }}
        </div>
        <div
          class="operate-btn recharge-btn"
          v-if="statusIn([0])"
          @click="handlePay()"
        >
          {{ $t('支付') }}
        </div>
      </div>
    </div>
    <!-- 如何登录 -->
    <van-dialog
      v-model="isHelpShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      get-container="body"
    >
      <div class="help-container">
        <div class="title">{{ $t('登录说明') }}</div>
        <div class="help-content">
          <div class="help-p">
            {{ $t('1、直接使用您当前登录APP的账号进入游戏即可。') }}
          </div>
          <div class="help-p">{{ $t('2、登录游戏后选择您购买的小号。') }}</div>
          <img
            class="help-img"
            src="@/assets/images/deal/login-help.png"
            alt=""
          />
          <div class="help-p">
            {{ $t('3、如有任何疑问，可联系') }}<span>{{ $t('客服咨询') }}</span
            >！
          </div>
        </div>
        <div class="confirm-btn btn" @click="isHelpShow = false">
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiXiaohaoChangeOrderStatus } from '@/api/views/xiaohao.js';
import { mapGetters, mapMutations } from 'vuex';
let _this;
export default {
  name: 'BuyItemComponent',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
    isShowPaySum: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      xiaohaoInfo: this.info,
      isHelpShow: false,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  watch: {
    info(val) {
      this.xiaohaoInfo = val;
    },
  },
  filters: {
    formatCountDown(val) {
      if (val >= 60) {
        let minute = parseInt(val) / 60;
        return `${Math.floor(minute)}${_this.$t('分钟')}`;
      } else {
        return `${val}${_this.$t('秒钟')}`;
      }
    },
  },
  beforeCreate() {
    _this = this;
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    statusIn(statusArr) {
      let index = statusArr.findIndex(item => {
        return Number(this.xiaohaoInfo.status) == item;
      });
      return index == -1 ? false : true;
    },
    handleHelp() {
      this.isHelpShow = true;
    },
    handleCancel() {
      this.$dialog
        .confirm({
          message: this.$t(
            '取消支付后，该角色可能会被抢走哦~是否确认取消支付?',
          ),
          confirmButtonText: this.$t('立即付款'),
          cancelButtonText: this.$t('取消支付'),
          closeOnClickOverlay: false,
          lockScroll: false,
        })
        .then(async () => {
          // 立即付款
          this.handlePay();
        })
        .catch(async () => {
          // 取消支付
          await this.changeOrderStatus(2);
          this.xiaohaoInfo.status = 2;
          this.$set(this.xiaohaoInfo, 'status_info', {
            str: this.$t('已取消'),
            color: 'rgb(136, 136, 136)',
          });
        });
    },
    handleDelete() {
      this.$dialog
        .confirm({
          message: this.$t('删除后无法恢复，确认删除？'),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeOrderStatus(100);
          this.xiaohaoInfo = {};
          this.$emit('afterDelete');
        });
    },
    handlePay() {
      let xhInfo = {
        ...this.xiaohaoInfo.trade_snapshot,
        game: this.xiaohaoInfo.game,
        xh_id: this.xiaohaoInfo.xh_id,
      };
      this.toPage('XiaohaoOrderRecharge', {
        info: this.xiaohaoInfo,
        xhInfo: xhInfo,
        back: 'MyBuyList',
      });
    },
    async changeOrderStatus(status) {
      try {
        this.$toast({
          type: 'loading',
          duration: 0,
          message: this.$t('加载中...'),
        });
        // status   2 = 已取消 100 删除
        const res = await ApiXiaohaoChangeOrderStatus({
          status: status,
          orderId: this.xiaohaoInfo.order_id,
        });
      } finally {
        this.$toast.clear();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.deal-item-component {
  &:not(:first-child) {
    margin-top: 10 * @rem;
  }
  .deal-item {
    box-sizing: border-box;
    background: #ffffff;
    padding: 14 * @rem 12 * @rem;
    width: 351 * @rem;
    margin: 0 auto;
    border-radius: 12 * @rem;
    .top-content {
      display: flex;
      justify-content: space-between;
      .top-left {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        .current-status {
          font-size: 14 * @rem;
          color: #000000;
        }
        .status {
          font-size: 14 * @rem;
          line-height: 15 * @rem;
        }
        .remark {
          font-size: 11 * @rem;
          color: #999;
          margin-top: 5 * @rem;
          margin-bottom: 8 * @rem;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .countdown {
          margin-left: 8 * @rem;
          font-size: 12 * @rem;
          color: #9a9a9a;
        }
      }
      .top-right {
        display: flex;
        height: 15 * @rem;
        line-height: 15 * @rem;
        align-items: center;
        .game-money {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #fe6600;
        }
      }
    }
    .bottom-content {
      .xiaohao-info {
        display: flex;
        align-items: flex-end;
        padding: 12 * @rem 0 0 * @rem;
        .game-icon {
          width: 72 * @rem;
          height: 72 * @rem;
          border-radius: 8 * @rem;
          background-color: #a0a0a0;
          overflow: hidden;
        }
        .center {
          height: 72 * @rem;
          flex: 1;
          min-width: 0;
          margin-left: 8 * @rem;
          .game-name {
            font-size: 15 * @rem;
            color: #30343b;
            font-weight: 600;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1px solid #e3e5e8;
              border-radius: 4 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 4 * @rem;
              color: #93999f;
              margin-left: 6 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .center-info {
            margin-top: 15 * @rem;
            display: flex;
            align-items: center;
            .server {
              white-space: nowrap;
              font-size: 12 * @rem;
              color: @themeColor;
            }
            .platforms {
              display: flex;
              align-items: center;
              .plat {
                width: 16 * @rem;
                height: 16 * @rem;
                margin-left: 6 * @rem;
              }
            }
          }

          .xh-id {
            font-size: 11 * @rem;
            margin-top: 8 * @rem;
            color: #93999f;
          }
        }
        .right {
          width: 70 * @rem;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          margin-left: 5 * @rem;
          .ptb-num {
            font-size: 14 * @rem;
            color: #ee3f3f;
            white-space: nowrap;
          }
          .ptb-text {
            font-size: 12 * @rem;
            color: #999999;
            margin-top: 4 * @rem;
          }
          .rmb-num {
            color: @themeColor;
            white-space: nowrap;
            font-weight: 600;
            span {
              font-size: 20 * @rem;
            }
          }
        }
      }
      .specify-man {
        padding-bottom: 10 * @rem;
        font-size: 14 * @rem;
        color: #666;
      }
    }
    .bottom-btn-group {
      margin-top: 20 * @rem;
      display: flex;
      height: 28 * @rem;
      align-items: center;
      justify-content: flex-end;
      .operate-btn {
        box-sizing: border-box;
        padding: 0 17 * @rem;
        height: 28 * @rem;
        line-height: 28 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        border-radius: 14 * @rem;
        margin-left: 10 * @rem;
        border: 1px solid#C1C1C1;
        color: #9a9a9a;
      }
      .edit-btn {
        border: 1px solid #2486b3;
        color: #2486b3;
      }
      .publish-btn {
        border: 1px solid @themeColor;
        color: @themeColor;
      }
      .unpublish-btn {
        border: 1px solid @themeColor;
        color: @themeColor;
      }
      .cancel-btn {
        border: 0;
        color: #93999f;
        background: #f7f8fa;
      }
      .delete-btn {
        border: 0;
        color: #93999f;
        background: #f7f8fa;
      }
      .recharge-btn {
        border: 0;
        color: #fff;
        background: #ff6649;
      }
      .help-btn {
        border: 0;
        color: #2bbe88;
        background: #ecfbf4;
      }
    }
  }
}
/deep/ .van-dialog {
  width: 300 * @rem !important;
}
.price-editor {
  box-sizing: border-box;
  padding: 20 * @rem 15 * @rem;
  .edit-price {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding: 11 * @rem 0;
    line-height: 30 * @rem;
    .title {
      font-size: 15 * @rem;
      color: #000000;
    }
    .input-text {
      display: block;
      flex: 1;
      min-width: 0;
      text-align: right;
      font-size: 15 * @rem;
    }
  }
  .edit-code {
    display: flex;
    align-items: center;
    padding: 10 * @rem 0;
    margin-top: 20 * @rem;
    border-bottom: 1px solid #e5e5e5;
    .input-code {
      flex: 1;
      min-width: 0;
    }
    .get-code {
      font-size: 13 * @rem;
      color: #60c055;
      width: 75 * @rem;
      height: 31 * @rem;
      border: 1px solid #60c055;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4 * @rem;
    }
  }
  .change-btn {
    background: @themeBg;
    border-radius: 8 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    margin-top: 20 * @rem;
  }
}
.help-container {
  box-sizing: border-box;
  padding: 20 * @rem 15 * @rem;
  .title {
    font-size: 20 * @rem;
    color: #000000;
    text-align: center;
  }
  .help-content {
    font-size: 15 * @rem;
    color: #000000;
    line-height: 20 * @rem;
    .help-p {
      margin-top: 15 * @rem;
      span {
        color: #1795ff;
      }
    }
    .help-img {
      width: 260 * @rem;
      height: 168 * @rem;
      margin: 10 * @rem auto 0;
    }
  }
  .confirm-btn {
    height: 45 * @rem;
    background: @themeBg;
    border-radius: 5 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16 * @rem;
    color: #ffffff;
    margin-top: 20 * @rem;
  }
}
</style>
