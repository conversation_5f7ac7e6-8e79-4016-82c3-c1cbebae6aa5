<template>
  <div class="page zhuanyou-page">
    <nav-bar-2
      :title="$t('转游中心')"
      bgStyle="transparent"
      :placeholder="false"
      :bgColor="`rgba(255,255,255, ${navbarOpacity})`"
      :border="navbarOpacity ? true : false"
    >
      <template #right>
        <div class="zhuanyou-explain" @click="toExplain">
          {{ $t('转游说明') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-bar">
        <div class="info">
          <div class="top-title">{{ $t('我的转游点') }}</div>
          <div class="zhuanyou-info" v-if="!!userInfo.token">
            <div class="zhuanyou-num">{{ bal }}</div>
          </div>
          <div
            class="to-login btn"
            @click="toPage('PhoneLogin')"
            v-if="!userInfo.token"
          >
            {{ $t('登录查看转游点') }}<span class="right-icon"></span>
          </div>
        </div>
        <div class="top-tips"> 游戏停运可用转游点兑换新游礼包哦~ </div>
        <div class="right-top-box">
          <div class="to-detail btn" @click="toPage('ZhuanyoudianDetail')">
          </div>
        </div>
      </div>
      <div class="operation">
        <div class="cate-title">平台推荐转入游戏</div>
        <div class="cate-list" v-if="cateList.length">
          <div
            class="cate-item"
            :class="{ active: item.id == cate }"
            v-for="(item, index) in cateList"
            :key="index"
            @click="cateChange(item)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="content">
        <div class="search-bar">
          <input
            type="text"
            class="input-text"
            :placeholder="$t('搜索游戏兑换转游福利')"
            v-model="keyword"
          />
          <div class="search-btn btn" @click="handleSearch">
            <div class="search-icon">
              <img src="@/assets/images/zhuanyou/search-icon.png" alt="" />
            </div>
          </div>
        </div>
        <content-empty v-if="empty"></content-empty>
        <yy-list
          v-else
          class="yy-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
        >
          <div class="coupon-list">
            <div
              class="coupon-item"
              v-for="(item, index) in couponList"
              :key="index"
              @click="toExchange(item, $event)"
            >
              <div class="game-info">
                <game-item-2 :gameInfo="item" :isToDetail="false"></game-item-2>
              </div>
              <div class="coupon-info">
                <div class="title">{{ item.zhuanyou_text }}</div>
                <template v-if="item.zhuanyou_type == 1">
                  <div class="number">
                    {{ $t('共') }}<span>{{ item.card_count }}</span
                    >个礼包
                  </div>
                  <div class="money">
                    {{ $t('总价值') }}<span>¥</span
                    ><span>{{ item.sum_worth }}</span>
                  </div>
                </template>
                <div
                  class="kefu"
                  v-if="item.zhuanyou_type == 2"
                  @click.stop="toKefu"
                  >联系客服</div
                >
              </div>
            </div>
          </div>
        </yy-list>
        <!-- <load-more
          v-else
          v-model="loading"
          :finished="finished"
          @loadMore="loadMore"
          :check="false"
        >
          <div class="coupon-list">
            <div
              class="coupon-item"
              v-for="(item, index) in couponList"
              :key="index"
              @click="toExchange(item, $event)"
            >
              <div class="game-info">
                <game-item-2 :gameInfo="item" :isToDetail="false"></game-item-2>
              </div>
              <div class="coupon-info">
                <div class="title">{{ item.zhuanyou_text }}</div>
                <template v-if="item.zhuanyou_type == 1">
                  <div class="number">
                    {{ $t('共') }}<span>{{ item.card_count }}</span
                    >个礼包
                  </div>
                  <div class="money">
                    {{ $t('总价值') }}<span>¥</span
                    ><span>{{ item.sum_worth }}</span>
                  </div>
                </template>
                <div
                  class="kefu"
                  v-if="item.zhuanyou_type == 2"
                  @click.stop="toKefu"
                  >联系客服</div
                >
              </div>
            </div>
          </div>
        </load-more> -->
      </div>
    </div>
  </div>
</template>

<script>
import { ApiZhuanyouIndex, ApiZhuanyouGetBal } from '@/api/views/zhuanyou.js';
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'Zhuanyou',
  data() {
    return {
      keyword: '',
      couponList: [],
      cateList: [],
      cate: 0,
      page: 1,
      listRows: 10,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      bal: 0,
      timer: null,
      empty: false,
      navbarOpacity: 0,
    };
  },
  watch: {
    keyword() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this.getCouponList();
      }, 300);
    },
  },
  async activated() {
    await this.getZhuanyouGetBal();

    // 神策埋点
    this.$sensorsTrack('gameSwitching_benefits_pageView');
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async getZhuanyouGetBal() {
      const res = await ApiZhuanyouGetBal();
      let { bal } = res.data;
      this.bal = bal;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 50) {
        this.navbarOpacity = 1;
      } else {
        this.navbarOpacity = 0;
      }
    },
    toExplain() {
      this.toPage('Iframe', {
        url: this.$h5Page.zhuanyoushuoming,
        title: this.$t('转游说明'),
      });
    },
    toExchange(item, e) {
      e.stopPropagation();
      if (item.zhuanyou_type == 2) {
        BOX_goToGame(
          {
            params: {
              id: item.id,
              gameInfo: item,
            },
          },
          { id: item.id },
        );
        return false;
      }
      this.toPage('ExchangeWelfare', { id: item.id, gameInfo: item });
    },
    cateChange(item) {
      this.cate = item.id;
      this.getCouponList();
    },
    handleSearch() {
      this.getCouponList();
    },
    async getCouponList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiZhuanyouIndex({
        page: this.page,
        listRows: this.listRows,
        keyword: this.keyword,
        cate: this.cate,
      });
      let { list } = res.data;
      if (res.data.cate) {
        this.cateList = [{ id: 0, title: '全部' }, ...res.data.cate];
      }
      if (action === 1 || this.page === 1) {
        this.couponList = [];
      }
      this.couponList.push(...list);
      this.loadingObj.loading = false;
      if (!this.couponList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getCouponList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.couponList.length) {
        await this.getCouponList();
      } else {
        await this.getCouponList(2);
      }
      this.loadingObj.loading = false;
    },
    toKefu() {
      // this.toPage('Kefu');
      this.openKefu();
    },
  },
};
</script>

<style lang="less" scoped>
.zhuanyou-page {
  .zhuanyou-explain {
    font-size: 14 * @rem;
    color: #60666c;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main {
    width: 100%;
    flex: 1;
    background: #f7f8fa url(~@/assets/images/zhuanyou/main-bg.png) no-repeat;
    background-size: 100% auto;
    padding-top: 50 * @rem;
    padding-top: calc(50 * @rem + @safeAreaTop);
    padding-top: calc(50 * @rem + @safeAreaTopEnv);
    .top-bar {
      display: flex;
      align-items: center;
      height: 96 * @rem;
      margin: 12 * @rem;
      background-color: #fff;
      border-radius: 14 * @rem;
      position: relative;
      padding: 0 12 * @rem 0 16 * @rem;

      .info {
        flex: 1;
        min-width: 0;
        .top-title {
          height: 20 * @rem;
          font-weight: bold;
          font-size: 14 * @rem;
          color: #191b1f;
          line-height: 20 * @rem;
        }
        .zhuanyou-info {
          width: 100%;
          display: flex;
          flex-direction: column;
          margin-top: 9 * @rem;
          .zhuanyou-num {
            font-size: 28 * @rem;
            color: #191b1f;
            font-weight: bold;
          }
        }
        .to-login {
          box-sizing: border-box;
          width: 118 * @rem;
          height: 25 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 15 * @rem;
          font-size: 13 * @rem;
          color: @themeColor;
          margin-top: 8 * @rem;
          background: #ecfbf4;
          font-weight: bold;
          .right-icon {
            width: 4 * @rem;
            height: 8 * @rem;
            background: url(~@/assets/images/zhuanyou/right-icon.png) no-repeat;
            background-size: 4 * @rem 8 * @rem;
            margin-left: 5 * @rem;
          }
        }
      }

      .top-tips {
        height: 15 * @rem;
        font-size: 11 * @rem;
        color: #93999f;
        line-height: 15 * @rem;
        text-align: right;
        position: absolute;
        bottom: 9 * @rem;
        right: 12 * @rem;
      }
      .right-top-box {
        width: 106 * @rem;
        height: 64 * @rem;
        background: url(~@/assets/images/zhuanyou/to-detail.png) no-repeat;
        background-size: 106 * @rem 64 * @rem;
        position: absolute;
        top: 0;
        right: 0;
        .to-detail {
          display: block;
          width: 72 * @rem;
          height: 29 * @rem;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
    .operation {
      padding: 0 12 * @rem;

      .cate-title {
        font-weight: bold;
        font-size: 16 * @rem;
        color: #191b1f;
        line-height: 22 * @rem;
        margin-top: 1 * @rem;
      }
      .cate-list {
        display: flex;
        overflow-x: auto;
        margin-top: 8 * @rem;

        &::-webkit-scrollbar {
          display: none;
        }

        .cate-item {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 25 * @rem;
          background: #f0f1f5;
          border-radius: 6 * @rem;
          padding: 0 10 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #60666c;
          line-height: 15 * @rem;
          text-align: center;
          margin-right: 10 * @rem;

          &:last-of-type {
            margin-right: 0;
          }

          &.active {
            background: #1cce94;
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }
    .content {
      overflow: hidden;
      .search-bar {
        box-sizing: border-box;
        width: 351 * @rem;
        margin: 9 * @rem auto 12 * @rem;
        display: flex;
        align-items: center;
        padding-left: 14 * @rem;
        padding-right: 11 * @rem;
        background-color: #ffffff;
        border-radius: 35 * @rem;
        height: 38 * @rem;
        // box-shadow: 0*@rem 6*@rem 15*@rem 0*@rem rgba(150, 150, 150, 0.15);
        .search-btn {
          // width: 38 * @rem;
          // height: 22 * @rem;
          // border-radius: 11 * @rem;
          // background: @themeBg;
          display: flex;
          align-items: center;
          justify-content: center;
          .search-icon {
            width: 14 * @rem;
            height: 14 * @rem;
          }
        }
        .input-text {
          flex: 1;
          min-width: 0;
          font-size: 12 * @rem;
          color: #333;

          &::placeholder {
            color: #93999f;
          }
        }
      }
      .coupon-list {
        .coupon-item {
          box-sizing: border-box;
          width: 351 * @rem;
          background-color: #fff;
          margin: 0 auto 13 * @rem;
          padding: 0 12 * @rem;
          border-radius: 12 * @rem;
          .coupon-info {
            display: flex;
            align-items: center;
            height: 43 * @rem;
            .title {
              font-size: 11 * @rem;
              color: #93999f;
              flex: 1;
              line-height: 43 * @rem;
            }
            .money {
              font-size: 14 * @rem;
              color: #191b1f;
              margin-left: 7 * @rem;
              line-height: 43 * @rem;
              span {
                font-weight: 600;
                font-size: 10 * @rem;
                color: #ff6649;
                margin-left: 2 * @rem;
                &:nth-of-type(2) {
                  font-size: 16 * @rem;
                }
              }
            }
            .number {
              font-size: 11 * @rem;
              color: #191b1f;
              line-height: 43 * @rem;
              span {
                color: #2bbe88;
                font-weight: bold;
              }
            }

            .kefu {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 65 * @rem;
              height: 24 * @rem;
              background: #f0f1f5;
              border-radius: 30 * @rem;
              font-size: 11 * @rem;
              color: #60666c;
              line-height: 14 * @rem;
              text-align: center;
            }
          }
          .game-info {
            flex: 1;
            min-width: 0;

            /deep/ .game-item-components {
              padding: 14 * @rem 0 0 0;
              .game-icon {
                flex: 0 0 64 * @rem;
                width: 64 * @rem;
                height: 64 * @rem;

                .subscript {
                  display: none;
                }
              }
              .game-info {
                .game-name {
                  color: #191b1f;
                  font-size: 14 * @rem;
                  line-height: 18 * @rem;
                  font-weight: bold;
                  .game-subtitle {
                    border-radius: 4 * @rem;
                    border-color: #e3e5e8;
                    padding: 2 * @rem 4 * @rem;
                    color: #93999f;
                    font-size: 11 * @rem;
                    line-height: 14 * @rem;
                  }
                }

                .tags {
                  margin-top: 6 * @rem;
                  .tag {
                    border-radius: 4 * @rem;
                    background-color: #f7f8fa;
                    padding: 0 5 * @rem;
                    height: 16 * @rem;
                    margin-right: 8 * @rem;

                    .tag-name {
                      color: #93999f;
                      font-size: 10 * @rem;
                      line-height: 10 * @rem;
                      margin-left: 0;
                    }
                  }
                }

                .game-bottom {
                  .score {
                    color: @themeColor;
                  }
                  .types {
                    .type {
                      color: #93999f;
                      font-size: 11 * @rem;
                      line-height: 14%;
                    }
                  }
                }
              }
            }
          }
          .right-info {
            .game-icon {
              width: 45 * @rem;
              height: 45 * @rem;
              border-radius: 50%;
              overflow: hidden;
              margin: 0 auto;
              position: relative;
            }
            .coupon-btn {
              position: relative;
              width: 58 * @rem;
              height: 21 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              background: @themeBg;
              border-radius: 4 * @rem;
              font-size: 13 * @rem;
              color: #ffffff;
              margin-top: -5 * @rem;
            }
          }
        }
      }
    }
  }
}
</style>
