<template>
  <div
    class="award-record-page"
    :class="{
      centered: !isLoading,
    }"
  >
    <rubber-band :topColor="'#151412'" :bottomColor="'#151412'">
      <nav-bar-2
        class="nav-bar"
        :placeholder="false"
        :bgStyle="bgStyle"
        :bgColor="`rgba(28, 23, 19, ${navbarOpacity})`"
        :azShow="true"
      >
        <template #right>
          <div class="share-btn" @click="shareBadge()"></div>
        </template>
      </nav-bar-2>
      <loading-indicator v-if="!isLoading" />
      <template v-else>
        <div class="empty-box" v-if="badgeList && !badgeList?.length">
          <default-not-found-page notFoundText="没有该徽章哦～" />
        </div>
        <div class="container" v-else>
          <div class="top-bg">
            <div class="layer-box" :style="{ opacity: layerBoxOpacity }">
              <canvas id="myParticleCanvas" class="myParticleCanvas"></canvas>
            </div>
          </div>
          <div class="main">
            <div class="badge-swiper">
              <swiper
                class="badge-content"
                :options="swiperBannerOptions"
                @slideChange="onSlideBannerChange()"
                @touchStart="onTouchStart()"
                @touchEnd="onTouchEnd()"
                @transitionEnd="onTransitionEnd()"
                ref="badgeSwiper"
              >
                <swiper-slide
                  class="swiper-slide"
                  v-for="item in badgeList"
                  :key="item.id"
                  :class="{ sliding: isSliding }"
                >
                  <div class="swiper-item">
                    <img :src="item.icon_url" alt="" />
                  </div>
                </swiper-slide>
              </swiper>
            </div>
            <div class="badge-info">
              <div class="content">
                <div class="content-info">
                  <div class="title"
                    >{{ currentBadge.name }}
                    <div class="wear-icon" v-if="currentBadge.is_wear == 1">
                    </div>
                  </div>
                  <div class="number"
                    >已有{{ currentBadge.have_num }}人 获得</div
                  >
                </div>
              </div>
              <div class="require" v-if="currentBadge.arrive_text">
                <div
                  class="require-text"
                  v-html="currentBadge.arrive_text"
                ></div>
              </div>
              <div
                class="create-time"
                v-if="
                  currentBadge.is_did !== 0 ||
                  (isValid == 1 && currentBadge.id == id)
                "
              >
                <div class="time" v-if="currentBadge.create_time"
                  >{{ formatDate(currentBadge.create_time) }} 获得该徽章</div
                >
              </div>
              <div
                class="badge-list"
                :class="{
                  'scrollable': badgeList.length > 5,
                  'non-scrollable': badgeList.length <= 5,
                }"
              >
                <template v-for="(item, index) in badgeList">
                  <div
                    class="badge-item"
                    :key="index"
                    :class="{
                      'active': currentIndex === index,
                      'not-obtained': item.is_did == 0,
                    }"
                    @click="slideTo(index)"
                  >
                    <img :src="item.icon_url" alt="" />
                  </div>
                  <span
                    :key="`line-${item.id}-${index}`"
                    class="line"
                    v-if="index !== badgeList.length - 1"
                  ></span>
                </template>
              </div>
            </div>
            <div class="bottom-btn">
              <div
                class="wear-btn btn"
                :class="{ default: currentBadge.is_did == 0 }"
                @click="wearBadge()"
              >
                <span>{{
                  currentBadge.is_wear == 1 ? '取消佩戴' : '佩戴徽章'
                }}</span>
              </div>
              <div class="share-btn btn" @click="shareBadge()">
                <span>炫耀一下</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </rubber-band>
  </div>
</template>

<script>
import LoadingIndicator from '@/components/loading-Indicator';
import {
  ApiUserBadgeBadgeInfo,
  ApiUserBadgeWear,
} from '@/api/views/badgeCollection.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import {
  createStarCanvas,
  GentleParticleView,
  GentleParticle,
  Random,
  ParticlePool,
} from './index';
import { mapMutations } from 'vuex';
import { throttle } from '@/utils/tools.js';
export default {
  name: 'BadgeDetail',
  props: {},
  components: {
    LoadingIndicator,
  },
  data() {
    let _self = this;
    return {
      title: '',
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      layerBoxOpacity: 1,
      isSliding: false, // 滑动状态标志
      swiperBannerOptions: {
        slidesPerView: 2,
        centeredSlides: true,
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerGroup: 1, // 一次滑动一个
        slideToClickedSlide: true,
        spaceBetween: 15,
        loop: false,
        resistanceRatio: 0, // 不允许边界回弹
        watchSlidesProgress: true,
        on: {
          progress: function () {
            for (let i = 0; i < this.slides.length; i++) {
              let slide = this.slides[i];
              const maxScale = 1; // 即 170px 大图
              const minScale = 100 / 170; // 即缩到 100px = 约 0.588
              const slideProgress = Math.abs(this.slides[i].progress);
              const progressClamped = Math.min(slideProgress, 1); // 限制最大 1
              const scale = maxScale - progressClamped * (maxScale - minScale);
              slide.style.transform = `scale(${scale})`;
              // console.log(scale);
              const maxOpacity = 1;
              const minOpacity = 0.15;
              const easeProgress =
                1 - (1 - progressClamped) * (1 - progressClamped);
              const opacity =
                maxOpacity - easeProgress * (maxOpacity - minOpacity);
              slide.style.opacity = opacity;

              if (Math.abs(slideProgress) > 3) {
                slide.style.opacity = 0;
              }
            }
          },
          transitionEnd: function () {
            _self.currentIndex = this.activeIndex;
            _self.currentBadge = _self.badgeList[_self.currentIndex];
          },
          setTransition: function (duration) {
            for (let i = 0; i < this.slides.length; i++) {
              this.slides[i].style.transitionDuration = `${duration}ms`;
            }
          },
        },
      },
      badgeList: [],
      currentBadge: {},
      currentIndex: 0,
      type_id: '',
      record_id: '',
      isLoading: false,
      id: '',
      isValid: 0, // 是否显示详情页过期徽章 0隐藏 1显示
      isCurrenSelect: false, // 是否是当前选中徽章
      // 节流函数
      wearBadgeThrottled: null,
      shareBadgeThrottled: null,
    };
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  mounted() {},
  methods: {
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
    // 佩戴徽章
    async _wearBadge() {
      if (!this.currentBadge.is_did) {
        return;
      }
      const res = await ApiUserBadgeWear({
        record_id: this.currentBadge.record_id,
        type: this.currentBadge.is_wear == 1 ? 2 : 1,
      });
      if (res.code == 1) {
        let badge = res.data?.badge;
        if (badge && Object.keys(badge).length) {
          this.badgeList = this.badgeList.map(item => {
            if (item.id == badge.id) {
              item.is_wear = badge.is_wear;
            }
            return item;
          });
        } else {
          this.currentBadge.is_wear = 0;
        }
        this.setWearBadgeInfo(badge);
      }
    },

    wearBadge() {
      if (!this.wearBadgeThrottled) {
        this.wearBadgeThrottled = throttle(this._wearBadge, 1500);
      }
      this.wearBadgeThrottled();
    },
    // 分享徽章
    async _shareBadge() {
      if (!this.badgeList.length) {
        this.$toast('没有该徽章哦～');
        return;
      }
      const res = await ApiCommonShareInfo({
        type: 14,
        id: this.currentBadge.id,
        source_id: this.currentBadge.record_id,
      });
      this.$nextTick(() => {
        setTimeout(() => {
          this.$copyText(res.data.share_text + ' ' + res.data.url).then(
            res => {
              this.$toast('链接已复制');
            },
            err => {
              this.$dialog.alert({
                message: '复制失败，请稍后重试',
                lockScroll: false,
              });
            },
          );
        }, 100);
      });
    },

    // 分享徽章
    shareBadge() {
      if (!this.shareBadgeThrottled) {
        this.shareBadgeThrottled = throttle(this._shareBadge, 1500);
      }
      this.shareBadgeThrottled();
    },
    async getUserBadgeBadgeInfo() {
      try {
        const res = await ApiUserBadgeBadgeInfo({
          type_id: Number(this.type_id),
          record_id: Number(this.record_id),
        });

        this.badgeList = res.data.list;
        if (this.id && res.data.list.length > 0) {
          this.currentBadge =
            res.data.list.find(item => item.id == this.id) || {};
          this.currentBadge.arrive_text = this.currentBadge.arrive_text.replace(
            /\\n|\n/g,
            '<br>',
          );
        }
        if (this.badgeList && this.badgeList.length > 0) {
          // 初始化粒子效果
          this.$nextTick(() => {
            setTimeout(() => {
              this.currentIndex =
                res.data.list.findIndex(item => item.id == this.id) || 0;
              this.initParticle();
              this.scrollActiveBadgeToView();
              this.slideTo(Number(this.currentIndex));
            }, 100);
          });
        }
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
    initParticle() {
      const particleView = new GentleParticleView('myParticleCanvas');

      const starCanvas = createStarCanvas(10, '#FFF', '#FFF', 8); // size=30, color=white, glowColor=淡蓝色, glowBlur=8
      // 设置粒子图片
      particleView.setParticleImage(starCanvas);

      // 开始动画
      particleView.startAnimation();

      // 保存particleView实例，以便在组件销毁时清理
      this.particleView = particleView;
    },
    onTouchStart() {
      // 用户触摸开始时隐藏粒子效果
      this.layerBoxOpacity = 0;
    },
    onTouchEnd() {
      // 手指放开后恢复显示粒子效果
      const swiper = this.$refs.badgeSwiper.$swiper;
      this.currentIndex = swiper.activeIndex;
      this.currentBadge = this.badgeList[this.currentIndex];
      this.layerBoxOpacity = 1;
    },
    onTransitionEnd() {
      // 过渡动画结束后恢复显示粒子效果
      this.layerBoxOpacity = 1;
    },
    onSlideBannerChange() {
      const swiper = this.$refs.badgeSwiper.$swiper;
      if (swiper) {
        // this.currentIndex = swiper.activeIndex;
        // this.currentBadge = this.badgeList[this.currentIndex];
        this.$nextTick(() => {
          this.scrollActiveBadgeToView();
        });
      }
    },
    scrollActiveBadgeToView() {
      if (this.badgeList.length <= 5) return;
      const badgeListEl = document.querySelector('.badge-list');
      const activeBadgeEl = document.querySelector(
        '.badge-list .badge-item.active',
      );

      if (badgeListEl && activeBadgeEl) {
        const badgeListRect = badgeListEl.getBoundingClientRect();
        const activeBadgeRect = activeBadgeEl.getBoundingClientRect();

        const scrollLeft =
          activeBadgeEl.offsetLeft -
          badgeListRect.width / 2 +
          activeBadgeRect.width / 2;
        badgeListEl.scrollTo({
          left: scrollLeft,
          behavior: 'smooth',
        });
      }
    },
    slideTo(index) {
      this.currentIndex = index;
      this.currentBadge = this.badgeList[this.currentIndex];
      if (this.$refs.badgeSwiper?.$swiper) {
        this.$refs.badgeSwiper.$swiper.slideTo(index);
      }
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.title = '';
        // this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.title = '';
        // this.bgStyle = 'transparent-white';
      }
    },
    ...mapMutations({
      setWearBadgeInfo: 'badge_collection/setWearBadgeInfo',
    }),
  },
  activated() {
    this.type_id = this.$route.params.type_id;
    this.id = Number(this.$route.params.id);
    this.record_id = Number(this.$route.params.record_id);
    this.isValid = Number(this.$route.params.is_valid);
    this.getUserBadgeBadgeInfo();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    // 销毁粒子效果，避免内存泄漏
    if (this.particleView) {
      this.particleView.destroy();
      this.particleView = null;
    }
  },
};
</script>

<style lang="less" scoped>
.award-record-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(180deg, #151412 0%, #151412 100%);
  overflow: hidden;
  &.centered {
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
  }
  @keyframes float-swiper {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-6px);
    }
    100% {
      transform: translateY(0);
    }
  }
  .nav-bar {
    /deep/ .transparent-white {
      .nav-title {
        color: #fffcec;
        font-size: 17 * @rem;
        font-weight: normal;
      }
      .back {
        background-image: url(~@/assets/images/badge-collection/back-btn.png);
      }
    }
    .share-btn {
      width: 22 * @rem;
      height: 22 * @rem;
      background: url(~@/assets/images/badge-collection/badge-share-btn.png)
        no-repeat;
      background-size: 100% 100%;
    }
  }
  .empty-box {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .container {
    .top-bg {
      width: 100%;
      height: 358 * @rem;
      background: url(~@/assets/images/badge-collection/badge-collection-bg2.png)
        no-repeat top right;
      background-size: 100% auto;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      .layer-box {
        position: relative;
        z-index: 5;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 127 * @rem;
        height: calc(127 * @rem + @safeAreaTop);
        height: calc(127 * @rem + @safeAreaTopEnv);
        transition: opacity 0.3s ease;
        .myParticleCanvas {
          width: 100%;
          height: 100%;
        }
      }
    }
    .main {
      padding-bottom: 30 * @rem;
      .badge-swiper {
        padding-top: calc(44 * @rem + @safeAreaTop);
        padding-top: calc(44 * @rem + @safeAreaTopEnv);
        height: 249 * @rem;
        .badge-content {
          margin-top: 50 * @rem;
          .swiper-slide {
            // width: 170 * @rem;
            // height: 170 * @rem;
            // transition: all 0.3s;
            opacity: 0.5;
            // &:not(:last-child) {
            //   margin-right: 20 * @rem;
            // }
            .swiper-item {
              width: 100%;
              height: 100%;
              img {
                width: 100%;
                height: 100%;
                object-fit: scale-down;
              }
            }
          }
          /deep/.swiper-slide-active {
            .swiper-item {
              // animation: float-swiper 4s ease-in-out infinite;
            }
          }

          /deep/.sliding {
            .swiper-item {
              animation-play-state: paused !important;
            }
          }
          // .is-active-slide {
          //   width: 170 * @rem !important;
          //   height: 170 * @rem !important;
          //   opacity: 1 !important;
          //   transition: all 0.3s;
          // }
        }
      }
      .badge-info {
        position: relative;
        margin-top: 29 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .content {
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: block;
            width: 30 * @rem;
            height: 44 * @rem;
            margin-right: 16 * @rem;
            margin-top: 7 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon5.png)
              no-repeat;
            background-size: 30 * @rem 44 * @rem;
          }
          &::after {
            content: '';
            display: block;
            width: 30 * @rem;
            height: 44 * @rem;
            margin-left: 16 * @rem;
            margin-top: 7 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon6.png)
              no-repeat;
            background-size: 30 * @rem 44 * @rem;
          }
          .content-info {
            max-width: 200 * @rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            .title {
              position: relative;
              line-height: 31 * @rem;
              font-weight: 600;
              font-size: 22 * @rem;
              color: #ffffff;
              .wear-icon {
                position: absolute;
                top: -8 * @rem;
                right: -37 * @rem;
                width: 37 * @rem;
                height: 17 * @rem;
                background: url('~@/assets/images/badge-collection/wear-icon.png')
                  no-repeat center center;
                background-size: 37 * @rem 17 * @rem;
              }
            }
            .number {
              margin-top: 3 * @rem;
              font-weight: 400;
              font-size: 13 * @rem;
              color: rgba(255, 255, 255, 0.3);
            }
          }
        }
        .require {
          width: 280 * @rem;
          line-height: 21 * @rem;
          margin-top: 15 * @rem;
          font-weight: 400;
          font-size: 15 * @rem;
          color: rgba(255, 255, 255, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          &::before {
            flex-shrink: 0;
            content: '';
            display: block;
            width: 16 * @rem;
            height: 12 * @rem;
            margin-right: 6 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon1.png)
              no-repeat;
            background-size: 16 * @rem 12 * @rem;
          }
          &::after {
            flex-shrink: 0;
            content: '';
            display: block;
            width: 16 * @rem;
            height: 12 * @rem;
            margin-left: 6 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon2.png)
              no-repeat;
            background-size: 16 * @rem 12 * @rem;
          }
          .require-text {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .create-time {
          margin-top: 5 * @rem;
          height: 17 * @rem;
          line-height: 17 * @rem;
          .time {
            font-weight: 400;
            font-size: 12 * @rem;
            color: rgba(255, 255, 255, 0.3);
          }
        }
        .badge-list {
          position: absolute;
          top: 199 * @rem;
          flex: 1;
          min-width: 0;
          max-width: 315 * @rem;
          margin: 0 auto;
          display: flex;
          align-items: center;
          &::-webkit-scrollbar {
            display: none;
          }
          &.scrollable {
            overflow: scroll;
            justify-content: flex-start;
          }
          &.non-scrollable {
            overflow: hidden;
            justify-content: center;
          }
          .badge-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 4 * @rem;
            img {
              flex-shrink: 0;
              width: 38 * @rem;
              height: 38 * @rem;
              object-fit: scale-down;
            }
            &.active {
              flex-shrink: 0;
              width: 50 * @rem;
              height: 50 * @rem;
              background: url(~@/assets/images/badge-collection/badge-collection-icon3.png)
                no-repeat center center;
              background-size: 50 * @rem 50 * @rem;
              img {
                flex-shrink: 0;
                width: 50 * @rem;
                height: 50 * @rem;
                object-fit: scale-down;
              }
            }
            &.not-obtained {
              opacity: 0.15;
            }
          }
          .line {
            flex-shrink: 0;
            width: 20 * @rem;
            height: 2 * @rem;
            background: url(~@/assets/images/badge-collection/badge-collection-icon4.png)
              no-repeat;
            background-size: 20 * @rem 2 * @rem;
          }
        }
      }
      .bottom-btn {
        margin: 0 auto;
        position: fixed;
        bottom: 30 * @rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        .wear-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #ffffff;
          width: 148 * @rem;
          height: 46 * @rem;
          border-radius: 23 * @rem;
          border: 1px solid #ffffff;
          box-sizing: border-box;
          &.default {
            opacity: 0.15;
          }
        }
        .share-btn {
          margin-left: 31 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #151412;
          width: 148 * @rem;
          height: 46 * @rem;
          background: linear-gradient(90deg, #fcf7d1 0%, #f7d7b0 100%);
          border-radius: 23 * @rem;
        }
      }
    }
  }
}
</style>
