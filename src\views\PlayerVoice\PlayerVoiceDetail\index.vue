<template>
  <div class="list-page">
    <nav-bar-2 title="精选建议回复" :azShow="true"></nav-bar-2>
    <yy-list
      class="list-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
      tips="暂无数据"
    >
      <div class="comment-list">
        <div class="item" v-for="(item, index) in list" :key="index">
          <CommentItem
            :comment="item"
            :showBottom="false"
            :showChildren="false"
          ></CommentItem>
          <div class="feedback" v-if="item.suggest_content">
            <div class="official-info">
              <img :src="kefuInfo.icon" alt="" />
              <div class="name">{{ kefuInfo.name }}</div>
            </div>
            <div class="feedback-content"> {{ item.suggest_content }} </div>
          </div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiCommentComments } from '@/api/views/comment.js';
import CommentItem from '../components/comment-item';
export default {
  name: 'PastList',
  data() {
    return {
      id: 0,
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
      kefuInfo: {},
    };
  },
  components: {
    CommentItem,
  },
  async created() {
    this.id = this.$route.params.id || 0;
    this.loadingObj.loading = true;
    await this.getList(2);
    this.loadingObj.loading = false;
  },
  methods: {
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
      }
      const res = await ApiCommentComments({
        page: this.page,
        listRows: this.listRows,
        classId: 106,
        sourceId: this.id,
        order: 0,
        is_suggest: 1,
      });

      let list = res.data.comments;
      this.kefuInfo = res.data.kefu_info ?? {};
      if (list.length < this.listRows) {
        this.finished = true;
      }
      this.list.push(...list);
      if (this.list.length == 0) {
        this.empty = true;
      } else {
        this.empty = false;
      }
    },
    async onRefresh() {
      await this.getList(2);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.list-page {
  min-height: 100vh;
  background-color: #f5f5f6;
  .comment-list {
    padding: 20 * @rem 12 * @rem 0;

    .item {
      background-color: #fff;
      border-radius: 12 * @rem;
      margin-bottom: 12 * @rem;
      padding: 21 * @rem 18 * @rem 18 * @rem 13 * @rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      .feedback {
        border: 1 * @rem solid #f4efe9;
        border-radius: 6 * @rem;
        background: #fcfaf9;
        padding: 10 * @rem;
        margin-left: 34 * @rem;
        margin-top: 15 * @rem;

        .official-info {
          display: flex;
          align-items: center;

          img {
            width: 28 * @rem;
            height: 28 * @rem;
            margin-right: 6 * @rem;
            border-radius: 50%;
          }

          .name {
            flex: 1;
            min-width: 0;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            color: #111111;
            line-height: 15 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .feedback-content {
          width: 100%;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #93734d;
          line-height: 15 * @rem;
          text-align: left;
          margin-top: 16 * @rem;
        }
      }
    }
  }
}
</style>
