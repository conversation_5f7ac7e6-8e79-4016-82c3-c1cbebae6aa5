<template>
  <div class="special-topic-page">
    <nav-bar-2
      v-if="!isInFragment"
      bgStyle="white"
      :placeholder="true"
      :azShow="true"
      :title="topicTitle"
    >
      <template #right>
        <div class="search-icon" @click="toPage('Search')"> </div>
        <div class="my-game-icon" @click="toPage('MyGame')"> </div>
      </template>
    </nav-bar-2>
    <div
      v-if="isInFragment && platform != 'android' && isEmbedded"
      class="placeholder"
    ></div>
    <van-loading class="loading-box" v-if="pageLoading">{{
      $t('加载中...')
    }}</van-loading>
    <div v-else class="main" :class="{ 'is-in-fragment': isInFragment }">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh()"
        @loadMore="loadMore()"
        :empty="empty"
        :check="false"
      >
        <template v-for="(fragment, index) in fragmentData">
          <div
            class="fragment-container"
            :key="index"
            v-if="fragmentComponentMap[fragment.view_type]"
          >
            <!-- {{ fragment.view_type}} -->
            <component
              :is="fragmentComponentMap[fragment.view_type]"
              :info="fragment"
            ></component>
          </div>
        </template>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiV2024IndexGetTopic } from '@/api/views/specialTopic.js';
import {
  BOX_goToGame,
  BOX_login,
  platform,
  BOX_isInFragment,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
import { fragmentComponentMap } from '@/utils/componentMap.js';
export default {
  data() {
    return {
      topicId: 0,
      topicTitle: '',
      fragmentComponentMap,
      isInFragment: false, // 安卓 是否在碎片里
      isEmbedded: 1, // ios 从接口返回是否是嵌入式的

      fragmentData: [], // 碎片数据

      platform,
      pageLoading: true,

      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      empty: false,
    };
  },
  computed: {
    ...mapGetters({
      homeNav: 'system/homeNav',
    }),
  },
  async created() {
    this.topicId = Number(this.$route.params.id);
    if (this.$route.params.title) {
      this.topicTitle = this.$route.params.title;
    }
    if (this.platform == 'android') {
      this.isInFragment = BOX_isInFragment();
    } else {
      if (this.$route.name == 'HomeSpecialTopic') {
        this.isInFragment = true;
      }
    }
    this.pageLoading = true;

    try {
      this.activity_id = Number(this.$route.params.id);

      await this.getPageData();
    } finally {
      this.pageLoading = false;
    }
  },
  activated() {
    if (this.$route.path.indexOf('/home') != -1) {
      const homeNavName = this.homeNav.find(
        item => item.url == this.$route.path,
      ).name;
      this.$route.meta.pageTitle = `首页-${homeNavName}`;
      this.$sensorsPageSet(`首页-${homeNavName}`);
    } else {
      this.$route.meta.pageTitle = `专题页-${this.$route.params.title}`;
      this.$sensorsPageSet(`专题页-${this.$route.params.title}`);
    }
  },
  methods: {
    async getPageData(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiV2024IndexGetTopic({
        topic_id: this.topicId,
        page: this.page,
        listRows: 10,
      });
      if (action === 1 || this.page === 1) {
        this.fragmentData = [];
      }
      this.fragmentData.push(...res.data);
      if (this.fragmentData.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }

      if (!res.data.length) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },

    async onRefresh() {
      await this.getPageData();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.fragmentData.length) {
        await this.getPageData();
      } else {
        await this.getPageData(2);
      }

      this.loadingObj.loading = false;
    },
    goToGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.special-topic-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .search-icon {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url('~@/assets/images/games/search-icon.png') center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
  }
  .my-game-icon {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 10 * @rem;
    background-image: url(~@/assets/images/my-game.png);
    background-size: 28 * @rem;
    background-repeat: no-repeat;
  }
  .placeholder {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(80 * @rem + @safeAreaTop);
    height: calc(80 * @rem + @safeAreaTopEnv);
    background: #fff;
    flex-shrink: 0;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .main {
    flex: 1;
    flex-grow: 1;
    position: relative;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    /deep/ .pull-refresh {
      background: linear-gradient(
        to bottom,
        #fff 100 * @rem,
        #f6f8fa 200 * @rem
      );
    }
    &.is-in-fragment {
      /deep/ .pull-refresh {
        padding-bottom: calc(50 * @rem + @safeAreaBottom);
        padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
      }
    }
    .fragment-container {
      flex: 1;
      padding: 6 * @rem 0;
    }
  }
}
</style>
