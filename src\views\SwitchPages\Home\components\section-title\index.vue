<template>
  <div class="section-title-component">
    <div class="title">
      <div class="main-title">{{ mainTitle }}</div>
      <div class="small-title">{{ smallText }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionTitle',
  data() {
    return {};
  },
  props: {
    mainTitle: {
      type: String,
      required: true,
    },
    smallText: {
      type: String,
      default: '',
    },
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.section-title-component {
  padding: 10 * @rem 22 * @rem;
  .title {
    .main-title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 600;
    }
    .small-title {
      font-size: 13 * @rem;
      color: #c1c1c1;
      font-weight: 400;
      margin-top: 5 * @rem;
    }
  }
}
</style>
