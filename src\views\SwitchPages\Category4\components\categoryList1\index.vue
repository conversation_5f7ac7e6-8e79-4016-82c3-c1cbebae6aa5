<template>
  <div class="category-list">
    <div class="container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
        <content-empty v-if="empty"></content-empty>
        <van-list
          v-else
          v-model="loading"
          :finished="finished"
          :finished-text="$t('没有更多了')"
          @load="loadMore()"
        >
          <div
            v-for="(item, index) in game_list"
            :key="index"
            class="item-container"
            :class="`item-${index + 1}`"
          >
            <div class="item-content">
              <div class="game-index">{{ index + 1 }}</div>
              <game-item-4 :gameInfo="item" class="game-item"></game-item-4>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import GameItem3 from '@/components/game-item-3';
import { ApiGameRecommend } from '@/api/views/game.js';
export default {
  props: {
    classId: {
      type: Number,
    },
  },
  data() {
    return {
      game_list: [],
      finished: false,
      loading: false,
      reloading: false,
      refreshing: false,
      page: 1,
      empty: false,
    };
  },
  methods: {
    async getGameList() {
      const res = await ApiGameRecommend({
        listRows: 10,
        page: this.page,
        classId: this.classId,
      });
      if (this.page === 1) {
        this.game_list = [];
        if (!res.data.list.length) {
          this.empty = true;
        }
      }
      this.game_list.push(...res.data.list);
      if (res.data.list.length < 10) {
        this.finished = true;
      } else {
        this.finished = false;
      }
    },
    onChange() {
      this.getGameList();
    },
    async onRefresh() {
      this.page = 1;
      await this.getGameList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getGameList();
      this.loading = false;
      this.page++;
    },
  },
  components: {
    GameItem3,
  },
};
</script>
<style lang="less" scoped>
.category-list {
  display: flex;
  height: 100%;
}
.container {
  display: flex;
  flex-flow: column;
  flex: 1;
  overflow: hidden;
  /deep/ .load-more {
    // overflow-y: scroll;
  }
}
.list {
  flex: 1;
  // overflow-y: scroll;
  // -webkit-overflow-scrolling: touch;
  ::-webkit-scrollbar {
    display: none;
  }
}
.item-container {
  margin: 10 * @rem 5 * @rem 10 * @rem 21 * @rem;
  border-radius: 16 * @rem;
  &.item-1 {
    background: linear-gradient(270deg, #ff7156 0%, #ffb053 100%);
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-1.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        .game-icon {
          box-sizing: border-box;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
            .game-subtitle {
              border-width: 0.5 * @rem;
              color: #fff;
              border-color: #fff;
            }
          }
          .info-line {
            .item-info {
              color: #fff;
              &.main-color {
                color: #fff;
              }
              &.cate-type:not(:first-of-type)::before {
                color: #fff;
              }
              &.discount-tag {
                .discount-text {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
              &.tag {
                background-color: rgba(255, 255, 255, 0.85);

                &.blue-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
                &.orange-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
            }
          }
          .info-center {
            .game-hot {
              color: #fff;
            }

            .types .type {
              color: #fff;

              &:not(:first-child):before {
                background-color: #fff;
              }
            }
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
            .tags {
              .tag {
                .tag-name {
                  font-weight: bold;
                }
              }
            }
            .score::before {
              .image-bg('~@/assets/images/category/star-2.png');
            }
          }
          .info-bottom {
            .discount-text {
              background-color: #fff;
            }
          }
        }
      }
    }
  }
  &.item-2 {
    background: linear-gradient(270deg, #738ac0 0%, #74a4cd 100%);
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-2.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          box-sizing: border-box;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
            .game-subtitle {
              border-width: 0.5 * @rem;
              color: #fff;
              border-color: #fff;
            }
          }
          .info-line {
            .item-info {
              color: #fff;
              &.main-color {
                color: #fff;
              }
              &.cate-type:not(:first-of-type)::before {
                color: #fff;
              }
              &.discount-tag {
                .discount-text {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
              &.tag {
                background-color: rgba(255, 255, 255, 0.85);

                &.blue-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
                &.orange-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
            }
          }
          .info-center {
            .game-hot {
              color: #fff;
            }

            .types .type {
              color: #fff;

              &:not(:first-child):before {
                background-color: #fff;
              }
            }
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
            .tags {
              .tag {
                .tag-name {
                  font-weight: bold;
                }
              }
            }
          }
          .score::before {
            .image-bg('~@/assets/images/category/star-2.png');
          }
        }
        .info-bottom {
          .discount-text {
            background-color: #fff;
          }
        }
      }
    }
  }
  &.item-3 {
    background: linear-gradient(270deg, #d99d64 0%, #f0ca9e 100%);
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-3.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          box-sizing: border-box;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
            .game-subtitle {
              border-width: 0.5 * @rem;
              color: #fff;
              border-color: #fff;
            }
          }
          .info-line {
            .item-info {
              color: #fff;
              &.main-color {
                color: #fff;
              }
              &.cate-type:not(:first-of-type)::before {
                color: #fff;
              }
              &.discount-tag {
                .discount-text {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
              &.tag {
                background-color: rgba(255, 255, 255, 0.85);

                &.blue-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
                &.orange-tag {
                  background-color: rgba(255, 255, 255, 0.85);
                }
              }
            }
          }
          .info-center {
            .game-hot {
              color: #fff;
            }

            .types .type {
              color: #fff;

              &:not(:first-child):before {
                background-color: #fff;
              }
            }
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
            .tags {
              .tag {
                .tag-name {
                  font-weight: bold;
                }
              }
            }
            .score::before {
              .image-bg('~@/assets/images/category/star-2.png');
            }
          }
          .info-bottom {
            .discount-text {
              background-color: #fff;
            }
          }
        }
      }
    }
  }
  .item-content {
    display: flex;
    align-items: center;
    margin-left: -16 * @rem;
    .game-index {
      width: 22 * @rem;
      height: 22 * @rem;
      margin: 5 * @rem;
      font-size: 14 * @rem;
      color: #797979;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    /deep/ .game-item-components {
      padding: 8 * @rem 0;
      .game-icon {
        width: 64 * @rem;
        height: 64 * @rem;
      }
      .game-info {
        height: unset;
        .game-name {
          font-size: 14 * @rem;
          font-weight: bold;
          line-height: 20 * @rem;
        }
        .game-bottom {
          margin-top: 0;
          font-size: 11 * @rem;
          line-height: 16 * @rem;
        }
        .tags {
          margin: 4 * @rem 0;
        }
        .score {
          font-size: 12 * @rem;
          font-weight: 600;
          &::before {
            content: '';
            display: block;
            width: 8 * @rem;
            height: 8 * @rem;
            margin-right: 4 * @rem;
            .image-bg('~@/assets/images/category/star-3.png');
          }
        }
        .types {
          .type {
            font-size: 11 * @rem;
          }
        }
      }
    }
  }
}
</style>
