<template>
  <div class="comment-item">
    <div class="user-info">
      <div class="user-avatar">
        <img :src="comment.user.avatar" alt="" />
      </div>
      <div class="info">
        <div class="user-bar">
          <div class="name">
            {{ comment.user.nickname }}
          </div>
          <div
            class="badge-icon"
            v-if="
              comment?.user_badge && Object.keys(comment?.user_badge).length
            "
            @click.stop="showAwardRecord(comment.user_badge)"
          >
            <img :src="comment?.user_badge?.icon_url" alt="" />
          </div>
          <!-- 2025年3月24日17:21:33 原型没了sj说先隐藏等级 级别 置顶 -->
          <!-- <div class="exp-level" v-if="comment.user.exp_level_name">
            {{ comment.user.exp_level_name }}
          </div>
          <div
            v-if="comment.user.pay_level_name"
            class="pay-level"
            :style="{ backgroundColor: comment.user.pay_level_color }"
          >
            {{ comment.user.pay_level_name }}
          </div>
          <div v-if="comment.top == 1" class="top-level">
            <div class="top-icon"></div>
            <div class="top-text">{{ $t('置顶') }}</div>
          </div> -->
        </div>
        <div class="score" v-if="showScoreTime && comment?.playtime">
          <!-- <div class="score-num">
            <van-rate
              v-model="comment.rating"
              color="#1CCE94"
              void-icon="star"
              void-color="#F0F1F5"
              :size="14"
            />
          </div> -->
          <div class="score-time">
            <!-- <img src="~@/assets/images/games/score-time-icon.png" alt="" /> -->
            <!-- <span class="text">游戏时长</span> -->
            <span class="time">{{ comment.playtime }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="showMyCommentRevise"
        class="update-btn"
        @click="reviseComment(comment)"
      >
        <div></div>
        <div>修改</div>
      </div>
    </div>
    <!-- <div class="selected" v-if="comment.suggest_type_img">
      <img :src="comment.suggest_type_img" alt="" />
    </div> -->
    <div class="comment-content">
      <div class="content" v-if="noJump">
        <template v-if="comment.reply_user">
          回复
          <span>{{ comment.reply_user.nickname }} </span>
        </template>
        {{ formatEmoji(comment.content) }}</div
      >
      <div class="content" @click="toCommentDetailGameDetail(comment)" v-else>
        <template v-if="comment.reply_user">
          回复
          <span>{{ reply.reply_user.nickname }}： </span>
        </template>
        <text-overflow
          class="ellipsis-content"
          @click-btn.stop="hideMore(false)"
          :content="formatEmoji(comment.content.replace(/\n/g, '<br>'))"
          :rows="rows"
          :btnText="btnText"
          :ellipsisText="ellipsisText"
          v-if="ellipsis && showOverflow"
          :btnShow="btnShow"
        >
        </text-overflow>
        <div
          class="ellipsis-content"
          v-html="formatEmoji(comment.content.replace(/\n/g, '<br>'))"
          v-else
        >
        </div>
      </div>

      <div
        class="images1"
        v-if="comment.images && comment.images.length && showTypesettingImg1"
      >
        <div
          class="tap-image-wrapper"
          v-for="(image, imageIndex) in comment.images"
          :key="imageIndex"
          @click="showBigImage(comment.images, imageIndex)"
        >
          <img :src="image" alt="" />
        </div>
      </div>
      <div
        class="images2"
        v-if="comment.images && comment.images.length && showTypesettingImg2"
      >
        <img
          v-for="(image, imageIndex) in comment.images"
          :key="imageIndex"
          :src="image"
          alt=""
          @click="showBigImage(comment.images, imageIndex)"
        />
      </div>
      <div class="children-comment-list" v-if="showChildren && comment.replies">
        <div
          class="children-comment-item"
          v-for="(reply, replyIndex) in comment.replies"
          :key="replyIndex"
        >
          <template v-if="replyIndex < 1">
            <!-- <div class="nickname">{{ reply.user.nickname }}：</div> -->
            <div class="content">
              <span> {{ reply.user.nickname }}</span
              ><span v-if="!reply.reply_user">：</span>
              <template v-if="reply.reply_user">
                回复
                <span>{{ reply.reply_user.nickname }}</span>
              </template>
              {{ formatEmoji(reply.content) }}
            </div>
            <!-- <div class="operation">
              <div class="time">{{ $getDateDiff(reply.create_time) }}</div>
              <div
                class="like"
                :class="{ active: reply.is_support == 1 }"
                @click="handleLike(reply)"
              >
                <span class="like-icon"></span>
                <span>{{ reply.support_count }}</span>
              </div>
              <div class="reply" @click="handleReply(reply, 2)">
                <span class="reply-icon"></span>
                <span>回复</span>
              </div>
            </div> -->
          </template>
        </div>
        <!-- <div
          class="more-comment"
          v-if="comment.replies.length > 1"
          @click="toCommentDetailGameDetail(comment)"
        >
          <span>全部{{ comment.reply_count }}条回复</span>
          <span class="icon"></span>
        </div> -->
      </div>
      <div class="bottom-info" v-if="showBottom">
        <div class="left-box">
          <div class="time">{{
            comment?.time || $getDateDiff(comment.create_time)
          }}</div>
          <!-- <div class="phone" v-if="showPhone && comment.is_show_model">
            <span class="phone-icon"></span>
            <span>{{ comment.model || '小米14' }}</span>
          </div> -->
          <div
            class="detail-reply"
            v-if="showLeftReply"
            @click="handleReply(comment, level)"
            >回复</div
          >
        </div>
        <div class="operation" v-if="showOperation">
          <div
            class="like"
            :class="{ active: comment.is_support == 1 }"
            @click="handleLike(comment)"
          >
            <span class="like-icon"></span>
            <span>{{ comment.support_count }}</span>
          </div>
          <div
            class="reply"
            v-if="showRightReply"
            @click="handleReply(comment, level)"
          >
            <span class="reply-icon"></span>
            <span v-if="showReplyCount">{{ comment.reply_count }}</span>
            <span v-else>回复</span>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="isReplying" :lock-scroll="false" position="bottom">
      <div class="reply-editor" v-if="isReplying">
        <div class="input-container">
          <input
            id="inputText"
            type="text"
            v-model.trim="replyInput"
            placeholder="回复"
            v-if="replyInfo.level == 1"
          />
          <input
            id="inputText"
            type="text"
            v-model.trim="replyInput"
            :placeholder="`回复@${replyNickname}`"
            v-else
          />
        </div>
        <div
          class="send-btn"
          :class="{ on: replyInput.length > 0 }"
          @click="sendReply"
        >
          回复
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiCommentSubmit, ApiResourceSupport } from '@/api/views/comment.js';
import TextOverflow from '@/components/text-overflow';
import { ImagePreview } from 'vant';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'CommentItem2',
  components: {
    TextOverflow,
  },
  props: {
    comment: {
      type: Object,
      required: true,
    },
    showBottom: {
      type: Boolean,
      default: true,
    },
    showChildren: {
      type: Boolean,
      default: true,
    },
    showReplyCount: {
      type: Boolean,
      default: true,
    },
    noJump: {
      type: Boolean,
      default: false,
    },
    level: {
      type: Number,
      default: 1,
    },
    showOverflow: {
      //是否显示展开评论
      type: Boolean,
      default: true,
    },
    showOperation: {
      // 是否显示点赞评论操作按钮
      type: Boolean,
      default: true,
    },
    showPhone: {
      // 是否显示手机
      type: Boolean,
      default: true,
    },

    showLeftReply: {
      // 是否显示左侧点赞回复
      type: Boolean,
      default: false,
    },
    showRightReply: {
      // 是否显示右侧点赞回复
      type: Boolean,
      default: true,
    },
    showTypesettingImg1: {
      // 是否显示图片排版1
      type: Boolean,
      default: true,
    },
    showTypesettingImg2: {
      // 是否显示图片排版2
      type: Boolean,
      default: false,
    },
    showScoreTime: {
      // 是否评分时长
      type: Boolean,
      default: false,
    },
    showMyCommentRevise: {
      // 是否显示我的评论修改按钮
      type: Boolean,
      default: false,
    },
    gameId: {
      // 游戏id
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isReplying: false,
      replyNickname: '',
      replyInput: '',
      replyInfo: {},
      rows: 4,
      btnText: '展开',
      ellipsisText: '...',
      ellipsis: true,
      btnShow: false,
      score: 4,
    };
  },
  watch: {
    replyInput(newVal, oldVal) {
      this.replyInput = newVal.slice(0, 100);
    },
  },
  computed: {
    formatReplyInput() {
      return this.replyInput.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  methods: {
    showAwardRecord(item) {
      if (!item) return;
      this.$nextTick(() => {
        this.$router.push({
          name: 'CommentBadgeDetail',
          params: {
            id: item.id,
            mem_id: item.mem_id,
            record_id: item.record_id ? item.record_id : 0,
          },
        });
      });
    },
    // 修改我的评论
    reviseComment(comment) {
      this.toPage('CommentEditor', {
        source_id: this.gameId,
        class_id: 103,
        comment_id: comment.comment_id,
        is_update: 0,
      });
    },
    hideMore(ellipsis) {
      this.ellipsis = ellipsis;
    },
    async handleLike(item) {
      if (!item.is_support) {
        item.is_support = -1;
      }
      const res = await ApiResourceSupport({
        classId: 103,
        sourceId: item.comment_id,
        status: item.is_support == 1 ? -1 : 1,
      });
      if (item.is_support == -1) {
        if (!item.support_count) {
          item.support_count = res.data.count;
        } else {
          item.support_count++;
        }
        item.is_support = 1;
      } else {
        if (!item.support_count) {
          item.support_count = res.data.count;
        } else {
          item.support_count--;
        }
        item.is_support = -1;
      }
    },
    handleReply(item, level = 1) {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.replyNickname = item.user.nickname;
      this.replyInfo = item;
      this.isReplying = true;
      this.replyInput = '';
      this.replyInfo.level = level;
      this.$nextTick(() => {
        document.querySelector('#inputText').focus();
      });
    },
    async sendReply() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.replyInput.length < 1) {
        this.$toast('输入的内容不能为空');
        this.isReplying = false;
        return false;
      }
      if (this.replyInput.length > 100) {
        this.$toast('输入的内容不能超过100字符');
        this.isReplying = false;
        return false;
      }
      this.$toast.loading({
        message: this.$t('发送中...'),
      });

      let params = {
        sourceId: this.comment.source_id,
        classId: 103,
        model: 'iPhone',
        content: this.formatReplyInput,
      };
      if (this.replyInfo.level == 1) {
        params.replyOuterId = this.replyInfo.comment_id;
      } else {
        params.replyCommentId = this.replyInfo.reply_outer_id;
        params.replyOuterId = this.replyInfo.comment_id;
      }

      if (platform == 'android') {
        params.model = this.modelName ? this.modelName : '安卓';
      }
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
      } finally {
        this.isReplying = false;
      }
    },
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    formatEmoji(str) {
      var pattern = /\[emoji\:(.+?)\]/g;
      var result = str.replace(pattern, a => {
        let code = a.substring(1, a.length - 1).split(':')[1];
        let strs;
        if (code.length > 4) {
          strs =
            unescape('%u' + code.substr(0, 4)) +
            unescape('%u' + code.substr(4));
        } else {
          strs = unescape('%u' + code.substr(0, 4));
        }

        return strs;
      });
      return result;
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
    formatDate(val) {
      let { year, date, time } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
    toCommentDetailGameDetail(item) {
      this.toPage('CommentDetailGameDetail', {
        id: item.comment_id,
        sourceId: this.comment.source_id,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.comment-item {
  position: relative;

  .user-info {
    height: 38 * @rem;
    display: flex;
    align-items: center;
    .user-avatar {
      width: 38 * @rem;
      height: 38 * @rem;
      margin-right: 8 * @rem;
      border-radius: 50%;
      overflow: hidden;
    }

    .info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: flex-start;
      .name {
        font-weight: 600;
        font-size: 14 * @rem;
        color: #111111;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .user-bar {
        display: flex;
        align-items: center;
        height: 24 * @rem;
        line-height: 24 * @rem;
        .nickname {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: 600;
          &.gold {
            color: #f8ab31;
          }
          &.orange {
            color: @themeColor;
          }
        }
        .exp-level {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 17 * @rem;
          line-height: 17 * @rem;
          border-radius: 2 * @rem;
          background-color: #309af8;
          padding: 0 5 * @rem;
          font-size: 10 * @rem;
          font-weight: 500;
          color: #ffffff;
          margin-left: 8 * @rem;
        }
        .pay-level {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 17 * @rem;
          line-height: 17 * @rem;
          border-radius: 2 * @rem;
          background-color: #ed9239;
          padding: 0 5 * @rem;
          font-size: 10 * @rem;
          font-weight: 500;
          color: #ffffff;
          margin-left: 6 * @rem;
        }
        .top-level {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 17 * @rem;
          line-height: 17 * @rem;
          border-radius: 2 * @rem;
          background-color: #f34a4a;
          padding: 0 5 * @rem;
          margin-left: 6 * @rem;
          .top-icon {
            width: 8 * @rem;
            height: 8 * @rem;
            .image-bg('~@/assets/images/games/top-icon.png');
          }
          .top-text {
            font-size: 10 * @rem;
            color: #ffffff;
            font-weight: 500;
            margin-left: 2 * @rem;
          }
        }
        .badge-icon {
          margin-left: 2 * @rem;
          width: 24 * @rem;
          height: 24 * @rem;
        }
      }
      .score {
        margin-top: 4 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 15 * @rem;
        line-height: 15 * @rem;
        .score-num {
          display: flex;
          align-items: center;
        }
        .score-time {
          display: flex;
          align-items: center;
          justify-content: center;
          // margin-left: 8 * @rem;
          height: 15 * @rem;
          line-height: 15 * @rem;
          img {
            width: 13 * @rem;
            height: 13 * @rem;
          }
          span {
            margin-left: 1 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #9a9a9a;
          }
          .time {
            // margin-left: 5 * @rem;
          }
        }
      }
    }
    .update-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56 * @rem;
      height: 28 * @rem;
      border-radius: 26 * @rem;
      border: 1 * @rem solid #e3e5e8;
      div {
        &:first-child {
          width: 12 * @rem;
          height: 12 * @rem;
          background: url('~@/assets/images/games/comment-update-icon.png')
            no-repeat center center;
          background-size: 12 * @rem 12 * @rem;
        }
        &:last-child {
          margin-left: 4 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #191b1f;
        }
      }
    }
  }
  .selected {
    width: 60 * @rem;
    height: 45 * @rem;
    position: absolute;
    top: -10 * @rem;
    right: -5 * @rem;
  }
  .comment-content {
    // padding-left: 33 * @rem;

    .content {
      display: block;
      width: 100%;
      font-weight: 400;
      font-size: 13 * @rem;
      color: #30343b;
      // line-height: 16 * @rem;
      text-align: justify;
      word-spacing: 1px;
      position: relative;
      line-height: 1.5;
      margin-top: 9 * @rem;
      word-break: break-all;
      .ellipsis-content {
        text-align: justify;
        word-spacing: 2px;
        position: relative;
        line-height: 1.5;
      }
      span {
        color: #999999;
      }
    }
    .images1 {
      padding-top: 5 * @rem;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-column-gap: 5 * @rem;
      .tap-image-wrapper {
        display: block;
        margin-top: 5 * @rem;
        border-radius: 6 * @rem;
      }
    }

    .images2 {
      display: flex;
      flex-direction: column;
      img {
        display: block;
        margin-top: 10 * @rem;
      }
    }

    .bottom-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding-top: 14 * @rem;
      .left-box {
        display: flex;
        align-items: center;
        .time {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #bababa;
        }
        .phone {
          margin-left: 6 * @rem;
          flex-shrink: 0;
          display: flex;
          align-items: center;

          span {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #999999;
            line-height: 15 * @rem;
          }

          .phone-icon {
            display: block;
            width: 12 * @rem;
            height: 14 * @rem;
            background: url(~@/assets/images/player-voice/phone-icon.png)
              no-repeat;
            background-size: 12 * @rem 14 * @rem;
            margin-right: 4 * @rem;
          }
        }
        .detail-reply {
          width: 40 * @rem;
          height: 21 * @rem;
          line-height: 21 * @rem;
          background: #f9f9f9;
          border-radius: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #000000;
          text-align: center;
          margin-left: 6 * @rem;
        }
      }

      .operation {
        display: flex;
        align-items: center;

        .like {
          display: flex;
          align-items: center;

          span {
            height: 18 * @rem;
            font-weight: 400;
            font-size: 14 * @rem;
            color: #666666;
            line-height: 18 * @rem;
          }

          .like-icon {
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/player-voice/like-icon.png)
              no-repeat;
            background-size: 16 * @rem 16 * @rem;
            margin-right: 4 * @rem;
          }

          &.active {
            .like-icon {
              background-image: url(~@/assets/images/player-voice/like-icon-active.png);
            }
            // span {
            //   color: #21b98a;
            // }
          }
        }
        .reply {
          display: flex;
          align-items: center;
          margin-left: 12 * @rem;

          span {
            height: 18 * @rem;
            font-weight: 400;
            font-size: 14 * @rem;
            color: #666666;
            line-height: 18 * @rem;
          }

          .reply-icon {
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/player-voice/reply-icon1.png)
              no-repeat;
            background-size: 16 * @rem 16 * @rem;
            margin-right: 4 * @rem;
          }
        }
      }
    }

    .children-comment-list {
      background-color: #f8f8fa;
      border-radius: 8 * @rem;
      margin-top: 6 * @rem;
      padding: 12 * @rem;

      .children-comment-item {
        // margin-top: 17 * @rem;

        &:first-of-type {
          margin-top: 0;
        }
        .nickname {
          width: 100%;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #fe6600;
          line-height: 14 * @rem;
          text-align: left;
        }

        .content {
          width: 100%;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #555555;
          text-align: justify;
          word-spacing: 2px;
          position: relative;
          line-height: 1.5;
          margin-top: 0 * @rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          span {
            color: #999999;
          }
        }

        .operation {
          display: flex;
          align-items: center;
          margin-top: 9 * @rem;

          .time {
            flex: 1;
            min-width: 0;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #9a9a9a;
            line-height: 14 * @rem;
            text-align: left;
          }

          .like {
            display: flex;
            align-items: center;

            span {
              height: 14 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #666666;
              line-height: 14 * @rem;
            }

            .like-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/player-voice/like-icon.png)
                no-repeat;
              background-size: 16 * @rem 16 * @rem;
              margin-right: 4 * @rem;
            }

            &.active {
              .like-icon {
                background-image: url(~@/assets/images/player-voice/like-icon-active.png);
              }
              span {
                color: #21b98a;
              }
            }
          }
          .reply {
            display: flex;
            align-items: center;
            margin-left: 12 * @rem;

            span {
              height: 14 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #666666;
              line-height: 14 * @rem;
            }

            .reply-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/player-voice/reply-icon.png)
                no-repeat;
              background-size: 16 * @rem 16 * @rem;
              margin-right: 4 * @rem;
            }
          }
        }
      }

      .more-comment {
        display: flex;
        align-items: center;
        margin-top: 17 * @rem;

        span {
          height: 15 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: @themeColor;
          line-height: 15 * @rem;
          text-align: left;
        }

        .icon {
          display: block;
          width: 12 * @rem;
          height: 12 * @rem;
          background: url(~@/assets/images/player-voice/double-arrow.png)
            no-repeat;
          background-size: 12 * @rem 12 * @rem;
          transform: rotate(90deg);
          margin-left: 4 * @rem;
        }
      }
    }
  }
  .reply-editor {
    width: 100%;
    height: 56 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12 * @rem 0 16 * @rem;
    box-sizing: border-box;

    .input-container {
      width: 212 * @rem;
      height: 38 * @rem;
      line-height: 38 * @rem;
      color: #999;

      input {
        width: 100%;
        height: 100%;
        background-color: #f8f8f8;
        border: 1 * @rem solid #f2f2f2;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
        padding: 0 12 * @rem;
        box-sizing: border-box;
      }
    }

    .send-btn {
      width: 120 * @rem;
      height: 38 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 38 * @rem;
      text-align: center;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      background: @themeBg;
      border-radius: 29 * @rem;
    }
  }
}
</style>
