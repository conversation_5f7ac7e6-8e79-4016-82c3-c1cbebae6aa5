<template>
  <div
    class="up-game-item"
    @click.stop="toPage('UpDetail', { id: info.id, gameInfo: info })"
  >
    <div class="game-icon">
      <img :src="info.titlepic" :alt="info.title" />
    </div>
    <div class="game-info">
      <div class="game-name">{{ info.title }}</div>
      <div class="game-desc">
        <span v-if="info.rating">{{ info.rating.rating }}分&nbsp;&nbsp;</span>
        <span v-for="(type, typeIndex) in info.type" :key="typeIndex">
          {{ typeIndex != 0 ? '|' : '' }} {{ type }}
        </span>
      </div>
    </div>
    <slot>
      <div class="play-btn" v-if="showRight">试玩</div>
    </slot>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
    showRight: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
.up-game-item {
  padding: 8 * @rem 10 * @rem;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  .game-icon {
    width: 60 * @rem;
    height: 60 * @rem;
  }
  .game-info {
    flex: 1;
    min-width: 0;
    margin-left: 8 * @rem;
    height: 60 * @rem;
    .game-name {
      font-size: 16 * @rem;
      color: #333333;
      line-height: 19 * @rem;
      font-weight: bold;
      margin-top: 4 * @rem;
    }
    .game-desc {
      flex: 1;
      min-width: 0;
      margin-top: 10 * @rem;
      font-size: 12 * @rem;
      line-height: 14 * @rem;
      height: 14 * @rem;
      overflow: hidden;
      color: #929292;
    }
  }
  .play-btn {
    width: 58 * @rem;
    height: 26 * @rem;
    border-radius: 20 * @rem;
    border: 1 * @rem solid @themeColor;
    font-size: 12 * @rem;
    color: @themeColor;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8 * @rem;
  }
}
</style>
