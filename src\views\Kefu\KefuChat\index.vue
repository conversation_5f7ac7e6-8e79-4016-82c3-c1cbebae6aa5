<template>
  <div class="kf-chat">
    <div :class="{ zx: is_zx }" class="nav-bar">
      <div @click="back()" class="back"></div>
      <div class="title">{{ is_zx ? '尊享VIP客服' : '客服' }}</div>
    </div>
    <iframe :src="url" frameborder="0" class="chat"></iframe>
  </div>
</template>
<script>
import { mapMutations } from 'vuex';
export default {
  data() {
    return {
      is_zx: 0,
      url: '',
    };
  },
  created() {
    this.is_zx = this.$route.params.is_zx;
    ysf('onready', () => {
      this.url = ysf('url');
      this.setUnReadMesNumber(0);
    });
  },
  methods: {
    ...mapMutations({
      setUnReadMesNumber: 'user/setUnReadMesNumber',
    }),
  },
};
</script>
<style lang="less" scoped>
// 这个页面px不能换rem
.kf-chat {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: calc(@safeAreaTop);
  padding-top: calc(@safeAreaTopEnv);
  .nav-bar {
    width: 100%;
    height: 48px;
    position: absolute;
    top: calc(@safeAreaTop);
    top: calc(@safeAreaTopEnv);
    left: 0;
    background: #fff;
    .back {
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 50px;
      background: url(~@/assets/images/nav-bar-back-black.png) center center
        no-repeat;
      background-size: 10px 18px;
    }
    .title {
      font-size: 18px;
      color: #333;
      font-weight: 600;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }
    &.zx {
      background: url(~@/assets/images/kefu/zx_bg.png) center right no-repeat;
      background-size: auto 50px;
    }
  }
  .chat {
    width: 100%;
    flex: 1;
  }
}
</style>
