<template>
  <div class="page select-game-page">
    <nav-bar-2 :title="$t('选择游戏')" :border="true"></nav-bar-2>
    <div class="main">
      <div class="search-bar">
        <div class="search-icon"></div>
        <input
          class="search-text"
          type="text"
          :placeholder="$t('搜索您玩过的游戏')"
          v-model="keyword"
        />
      </div>
      <yy-list
        class="yy-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="game-list">
          <div
            class="game-item"
            v-for="(item, index) in gameList"
            :key="index"
            @click="selectGame(item)"
          >
            <div class="game-icon">
              <img :src="item.game_icon" alt="" />
            </div>
            <div class="info">
              <div class="game-name">{{ item.game_name }}</div>
              <div class="xh-count">
                {{ $t('有') }}{{ item.xh_count }}{{ $t('个小号') }}
              </div>
            </div>
            <div class="right-icon"></div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiXiaohaoGetGameList } from '@/api/views/xiaohao.js';
import { mapMutations, mapGetters } from 'vuex';
export default {
  name: 'SelectGame',
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      keyword: '',
      gameList: [],
      page: 1,
      listRows: 10,
      timer: null,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  watch: {
    keyword() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this.getGameList();
      }, 500);
    },
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    selectGame(item) {
      let gameInfo = {
        game_icon: item.game_icon,
        game_name: item.game_name,
        app_id: item.app_id,
        is_h5: item.is_h5,
      };
      this.setXiaohaoSellInfo({ ...gameInfo });
      this.$router.go(-1);
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiXiaohaoGetGameList({
        keyword: this.keyword,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      this.gameList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.select-game-page {
  .main {
    .search-bar {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 347 * @rem;
      height: 35 * @rem;
      border-radius: 18 * @rem;
      background-color: #f6f6f6;
      margin: 14 * @rem auto;
      padding: 0 17 * @rem;
      .search-icon {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/search-icon.png) center center
          no-repeat;
        background-size: 15 * @rem 15 * @rem;
      }
      .search-text {
        display: block;
        padding: 0 7 * @rem;
        width: 100%;
        height: 100%;
        font-size: 15 * @rem;
        color: #333;
        background-color: transparent;
      }
    }
    .game-list {
      margin-top: 5 * @rem;
      .game-item {
        box-sizing: border-box;
        width: 347 * @rem;
        height: 90 * @rem;
        margin: 24 * @rem auto 0;
        background: #ffffff;
        box-shadow: 0 * @rem 1 * @rem 10 * @rem 0 * @rem rgba(0, 0, 0, 0.1);
        border-radius: 10 * @rem;
        display: flex;
        align-items: center;
        padding: 0 14 * @rem;
        &:nth-of-type(1) {
          margin-top: 0;
        }
        .game-icon {
          width: 60 * @rem;
          height: 60 * @rem;
          border-radius: 12 * @rem;
          overflow: hidden;
        }
        .info {
          flex: 1;
          min-width: 0;
          margin-left: 12 * @rem;
          .game-name {
            font-size: 16 * @rem;
            color: #333333;
            font-weight: bold;
          }
          .xh-count {
            font-size: 13 * @rem;
            color: #999999;
            margin-top: 10 * @rem;
          }
        }
        .right-icon {
          width: 9 * @rem;
          height: 17 * @rem;
          background: url(~@/assets/images/deal/right-icon.png) center center
            no-repeat;
          background-size: 9 * @rem 17 * @rem;
        }
      }
    }
  }
}
</style>
