<template>
  <div class="zhuanyekefu">
    <div class="content">
      <img src="@/assets/images/kefu/zhuanyekefu.png" class="left" />
      <div class="right">
        <div class="big-text">1V1专人答疑，贴心全程服务</div>
        <div class="small-text">仅限财富值达到郡王及以上的用户开通专属特权</div>
        <div
          :class="{ empty: empty, btn: !empty }"
          @click="handlePopup"
          class="button"
        >
          郡王享客服经理1对1在线服务
        </div>
      </div>
    </div>
    <van-popup v-model="popup" round class="zhuanyekefu-popup">
      <div class="title">专属SVIP客服微信号</div>
      <div v-for="(item, index) in vipWxList" :key="index" class="item">
        <div class="left">{{ item }}</div>
        <div @click="copy(item)" class="button">复制</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  data() {
    return {
      popup: false,
    };
  },
  computed: {
    empty() {
      if (!this.userInfo.token) {
        return true;
      } else {
        return this.vipWxList.length > 0 ? false : true;
      }
    },
    ...mapGetters({
      vipWxList: 'user/vipWxList',
    }),
  },
  async created() {
    // 获取vip客服列表
    await this.GET_VIP_WX();
  },
  methods: {
    handlePopup() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.empty) {
        this.$toast('仅限财富值达到郡王及以上的用户开通专属特权');
        return false;
      }
      if (this.vipWxList.length == 0) {
        this.$toast('暂无列表');
        return false;
      }
      this.popup = true;
    },
    // copy
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    ...mapActions({
      GET_VIP_WX: 'user/GET_VIP_WX',
    }),
  },
};
</script>
<style lang="less" scoped>
.content {
  display: flex;
  box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
  opacity: 1;
  border: 1 * @rem solid #f9f9f9;
  padding: 14 * @rem;
  border-radius: 15 * @rem;
  .left {
    width: 96 * @rem;
    height: 82 * @rem;
  }
  .right {
    margin-left: 10 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .big-text {
      font-size: 14 * @rem;
      line-height: 16 * @rem;
      color: #333333;
    }
    .small-text {
      font-size: 10 * @rem;
      color: #666666;
      line-height: 12 * @rem;
    }
    .button {
      width: 180 * @rem;
      height: 28 * @rem;
      background: linear-gradient(135deg, #ff7a00 0%, #ffb03a 100%);
      border-radius: 14 * @rem 14 * @rem 14 * @rem 14 * @rem;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      &.empty {
        background: #cccccc;
      }
    }
  }
}
.zhuanyekefu-popup {
  width: 279 * @rem;
  padding: 12 * @rem 24 * @rem;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
  .title {
    margin-bottom: 20 * @rem;
    font-size: 16 * @rem;
    font-family:
      PingFang SC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #3e4688;
    line-height: 15 * @rem;
    text-align: center;
  }
  .item {
    display: flex;
    height: 40 * @rem;
    justify-content: space-between;
    align-items: center;
    .left {
      color: #444444;
      line-height: 14 * @rem;
    }
    .button {
      width: 52 * @rem;
      height: 24 * @rem;
      background: @themeColor;
      border-radius: 14 * @rem 14 * @rem 14 * @rem 14 * @rem;
      opacity: 1;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
