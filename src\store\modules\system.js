import { ApiIndexExtra, ApiIndexExtra2, ApiClickClickEvent } from '@/api/views/system.js';
import {
  ApiCommentCommentReadStatus,
  ApiCommentGetUnreadCount,
} from '@/api/views/comment.js';
import { ApiIndexGetShowList } from '@/api/views/home.js';
import appIcon from '@/assets/images/app-icon2.png';
import { getDayZeroTimestamp, setLanguage, devLog } from '@/utils/function.js';
import { loadSDK } from '@/utils/tools.js'

export default {
  state: {
    initData: {},
    // 正在请求的接口，解决重复请求同一接口的问题
    loadingUrl: '',
    // 小红点信息 true:有小红点，false:没有小红点
    dotInfo: {
      feedback_read: false,
      reply_read: false,
      inform_read: false,
    },
    // 未读消息数量
    unreadCount: {
      fk: 0, // 反馈
      hf: 0, // 回复
      tz: 0, // 通知
      sum: 0, // 总数
    },
    homeNav: [],
    homeNavBg: 'rgba(255,255,255,0)',
    clickedTabBarAngle: [],

    showRookieGuidePopup: false, //新手引导弹窗Popup
    showWelfareGuidePopup: 0, //福利中心引导弹窗Popup 0:未显示 1:显示步骤一  2：显示步骤二

    mediumDeviceId: '',
    searchHotText: {}, // 搜索热词
    searchCurrentSlideIndex: 0, // 搜索热词索引
    postError: false, //请求报错
    onActionData: {}, //接受安卓传递的数据
    cloud_type: 0, // 0-百度云 1-移动云 2-臂云,
  },
  getters: {
    initData(state) {
      return state.initData;
    },
    // 是否是海外
    isHw(state, getters, rootState) {
      return rootState?.user?.userInfo?.is_hw ?? !!state?.initData?.is_hw;
    },
    configs(state) {
      return state.initData?.configs;
    },
    // 是否需要上报用户行为统计
    isClickEvent(state) {
      return state.initData?.click_event;
    },
    // 是否up版
    isUpApp(state) {
      return state.initData?.configs?.flavor == 3;
    },
    appName(state) {
      return state.initData?.app_name ?? '3733游戏盒';
    },
    defaultAvatar(state) {
      return state.initData?.default_avatar ?? appIcon;
    },
    // 渠道下载配置 0=正常显示，1=隐藏，2=敬请期待(暂不处理)
    dlConfig(state) {
      return state.initData?.configs?.dl_config ?? 1;
    },
    // 福利中心显示隐藏 0=正常显示，1=隐藏
    hideJfq(state) {
      return state.initData?.configs?.hide_jfq ?? 0;
    },
    // 显示tabbar“我的游戏” 1是展示 0是隐藏
    showMyGame(state) {
      return state.initData?.configs?.show_my_game ?? 1;
    },
    // 某些渠道需要隐藏引导弹窗和右下角icon
    showSuspension(state) {
      return state.initData?.configs?.show_suspension ?? 1;
    },
    // 客服qq号
    kefuQQNumber(state) {
      return state.initData?.configs?.kefu?.qq || '';
    },
    // 客服qq跳转链接
    kefuQQLink(state) {
      return state.initData?.configs?.kefu?.qq_url || '';
    },
    // 网易客服
    kefuWyLink(state) {
      return `https://qiyukf.com/script/6374f68354737e31410ef7a1b0e1828d.js?sdkTemplateId=6669676`;
    },
    // 是否接trackingIo统计
    accessTrackingIO(state) {
      return state.initData?.access_tracking_io ?? false;
    },
    // 是否接MediumH5IO统计
    accessMediumH5IO(state) {
      return state.initData?.access_medium_h5_io ?? false;
    },
    // 是否接小程序过来的统计
    accessMediumWechatIO(state) {
      return state.initData?.access_medium_wechat_io ?? false;
    },
    // 是否接自家上报
    accessMediumIO(state) {
      return state.initData?.access_medium_io ?? false;
    },
    // 自家上报参数
    mediumDeviceId(state) {
      return state.mediumDeviceId;
    },
    loadingUrl(state) {
      return state.loadingUrl;
    },
    dotInfo(state) {
      return state.dotInfo;
    },
    // 未读消息数量
    unreadCount(state) {
      return state.unreadCount;
    },
    homeNav(state) {
      return state.homeNav;
    },
    homeNavBg(state) {
      return state.homeNavBg;
    },
    tabBarAngle(state) {
      return state.initData?.nav_angle || [];
    },
    clickedTabBarAngle(state) {
      return state.clickedTabBarAngle;
    },
    jumpGame(state) {
      return state.initData?.jump_game || {};
    },
    // 是否显示交易模块
    showTransaction(state) {
      return state.initData?.configs?.show_transaction || true;
    },

    showRookieGuidePopup(state) {
      return state.showRookieGuidePopup;
    },
    showWelfareGuidePopup(state) {
      return state.showWelfareGuidePopup;
    },
    // 搜索热词
    searchHotText(state) {
      return state.searchHotText;
    },
    searchCurrentSlideIndex(state) {
      return state.searchCurrentSlideIndex;
    },
    postMessageData(state) {
      return state.postMessageData;
    },
    postError(state) {
      return state.postError;
    },
    onActionData(state) {
      return state.onActionData;
    },
    cloud_type(state) {
      return state.cloud_type;
    },
  },
  mutations: {
    setInitData(state, payload) {
      if (payload) {
        state.initData = Object.assign({}, payload);
      } else {
        state.initData = {
          feedback_read: false,
          reply_read: false,
          inform_read: false,
        };
      }
    },
    setLoadingUrl(state, payload) {
      state.loadingUrl = payload || '';
    },
    setDotInfo(state, payload) {
      state.dotInfo = Object.assign({}, payload);
    },
    setUnreadCount(state, payload) {
      state.unreadCount = Object.assign({}, payload);
    },
    setHomeNav(state, payload) {
      if (payload) {
        state.homeNav = payload;
      } else {
        state.homeNav = [];
      }
    },
    setHomeNavBg(state, payload) {
      state.homeNavBg = payload || 'rgba(255,255,255,0)';
    },
    setClickedTabBarAngle(state, payload) {
      if (payload) {
        let findResult = state.clickedTabBarAngle.findIndex((item, index) => {
          return item.navigation == payload;
        });
        if (findResult != -1) {
          state.clickedTabBarAngle.splice(findResult, 1);
        }
        state.clickedTabBarAngle.push({
          navigation: payload,
          dateTimeStamp: getDayZeroTimestamp(new Date().getTime() / 1000),
        });
      } else {
        state.clickedTabBarAngle = [];
      }
    },

    setShowRookieGuidePopup(state, payload) {
      state.showRookieGuidePopup = payload;
    },
    setShowWelfareGuidePopup(state, payload) {
      state.showWelfareGuidePopup = payload;
    },
    setMediumDeviceId(state, payload) {
      state.mediumDeviceId = payload;
    },
    setSearchHotText(state, payload) {
      state.searchHotText = payload;
    },
    setSearchCurrentSlideIndex(state, payload) {
      state.searchCurrentSlideIndex = payload;
    },
    setPostMessageData(state, payload) {
      state.postMessageData = payload;
    },
    setPostError(state, payload) {
      state.postError = payload;
    },
    setOnActionData(state, payload) {
      state.onActionData = Object.assign({}, payload);
    },
    setCloudType(state, payload) {
      state.cloud_type = payload;
      // 设置cloud_type时动态加载对应SDK
      loadSDK(payload);
    },
  },
  actions: {
    async SET_INIT_DATA({ commit }) {
      const res = await ApiIndexExtra();
      commit('setInitData', res.data);
      // 设置语言
      setLanguage();
      ApiIndexExtra2().then(res2 => {
        const result = { ...res2.data };
        for (const key in res.data) {
          result[key] = res.data[key] ?? res2.data[key] ?? res.data[key]; // 优先取a的值，如果a为null/undefined则取b的值
        }
        // 设置云类型并加载相应的SDK 默认值给0 0百度云 2臂云
        const cloudType = res2.data?.cloud_type !== undefined ? res2.data.cloud_type : 0;
        commit('setCloudType', cloudType);
        commit('setInitData', result);
      })

    },
    async SET_DOT_INFO({ commit }) {
      const res = await ApiCommentCommentReadStatus();
      commit('setDotInfo', res.data);
    },
    async SET_UNREAD_COUNT({ commit }) {
      const res = await ApiCommentGetUnreadCount();
      commit('setUnreadCount', res.data);
    },
    async SET_CLICKED_TAB_BAR_ANGLE({ commit, rootState }, index) {
      commit('setClickedTabBarAngle', index);
      localStorage.setItem('STORE', JSON.stringify(rootState));
    },
    // 用户行为上报
    CLICK_EVENT({ getters, rootState }, id) {
      if (getters['isClickEvent'] && id) {
        try {
          devLog(`触发行为上报=>click_id:${id}`);
          ApiClickClickEvent({
            click_time: Math.floor(Date.now() / 1000),
            click_id: id,
          });
        } catch (e) { }
      }
    },

    async SET_SHOW_ROOKIE_GUIDE_POPUP({ commit }, payload) {
      // rookieGuideStep => 1 | 2
      const stepStorage = localStorage.getItem('rookieGuideStep');
      if (!stepStorage && payload == 1) {
        commit('setShowRookieGuidePopup', true);
        localStorage.setItem('rookieGuideStep', payload);
        return;
      }
      if (stepStorage && stepStorage == '1' && payload == 2) {
        commit('setShowRookieGuidePopup', true);
        localStorage.setItem('rookieGuideStep', payload);
      }
    },

    async SET_SHOW_WELFARE_GUIDE_POPUP({ commit }, payload) {
      const stepStorage = localStorage.getItem('welfareGuideStep');
      if (stepStorage) commit('setShowWelfareGuidePopup', stepStorage);

      if (!stepStorage && payload == 1) {
        commit('setShowWelfareGuidePopup', 1);
        localStorage.setItem('welfareGuideStep', payload);
        return;
      }
      if (stepStorage && stepStorage == '1' && payload == 2) {
        commit('setShowWelfareGuidePopup', 2);
        localStorage.setItem('welfareGuideStep', payload);
      }
    },

    async SET_PAGE_NAV_LIST({ commit }) {
      const res = await ApiIndexGetShowList();
      if (res.data && res.data.show_info && res.data.show_info.length) {
        //top-bar组件要用到，所以存到store里
        commit('setHomeNav', res.data.show_info);
      } else {
        commit('setHomeNav');
      }
    },
    SET_POSTERROR_INIT({ commit }) {
      commit('setPostError', false);
    },
  },
};
