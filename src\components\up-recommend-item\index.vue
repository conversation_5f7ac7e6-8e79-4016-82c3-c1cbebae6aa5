<template>
  <div class="user-recommend-item" @click="goToPopularizeDetail">
    <div class="recommend-game">
      <img :src="info.game.titlepic" class="game-img" />
      <div class="content">
        <div class="gamename">{{ info.game.title }}</div>
        <div class="detail">
          <!-- <span class="score">{{info.game.rating ? info.game.rating.rating : "10" }}分</span> -->
          <template v-for="(type, typeIndex) in info.game.type">
            <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
              type
            }}</span>
          </template>
        </div>
      </div>
    </div>
    <div class="recommend-content">
      {{ info.content }}
    </div>
    <div v-if="info.game.up_info" class="user-bar">
      <user-avatar
        class="avatar"
        :src="info.game.up_info.avatar"
        :self="false"
      />
      <div class="nickname">
        {{ info.game.up_info.nickname }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  methods: {
    goToPopularizeDetail() {
      this.CLICK_EVENT(this.info.click_id);
      this.toPage('PopularizeDetail', {
        id: this.info.game.id,
        gameInfo: this.info.game,
        content: this.info.content,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.user-recommend-item {
  width: 290 * @rem;
  height: 195 * @rem;
  box-sizing: border-box;
  padding: 0 18 * @rem;
  background: #f5f5f6;
  border-radius: 10 * @rem 10 * @rem 10 * @rem 10 * @rem;
  margin-left: 10 * @rem;
  margin-top: 18 * @rem;
  .user-bar {
    display: flex;
    align-items: center;
    .avatar {
      width: 24 * @rem;
      height: 24 * @rem;
      background-color: #ccc;
      flex-shrink: 0;
    }
    .nickname {
      font-size: 14 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 15 * @rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.orange {
        color: @themeColor;
      }
    }
  }
  .recommend-content {
    overflow: hidden;
    height: 75 * @rem;
    color: #797979;
    font-size: 13 * @rem;
    line-height: 18 * @rem;
    margin-top: 8 * @rem;
    margin-bottom: 5 * @rem;
  }
  .recommend-game {
    box-sizing: border-box;
    background: #f5f5f6;
    border-radius: 12 * @rem;
    margin-top: 8 * @rem;
    display: flex;
    .game-img {
      position: relative;
      top: -11 * @rem;
      width: 70 * @rem;
      height: 70 * @rem;
      border-radius: 10 * @rem;
    }
    .content {
      flex: 1;
      min-width: 0;
      margin-left: 10 * @rem;
      display: flex;
      height: 50 * @rem;
      flex-direction: column;
      justify-content: center;
      margin-top: 8 * @rem;
      .gamename {
        font-size: 16 * @rem;
        font-weight: bold;
        line-height: 22 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .detail {
        flex: 1;
        min-width: 0;
        font-size: 11 * @rem;
        color: #999999;
        display: flex;
        align-items: center;
        margin: 5 * @rem 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .type {
          padding: 0 5 * @rem;
          position: relative;
          &:not(:first-child) {
            &:before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1 * @rem;
              height: 10 * @rem;
              background-color: #999999;
            }
          }
          &:first-of-type {
            padding-left: 0 * @rem;
          }
        }
      }
    }
  }
}
</style>
