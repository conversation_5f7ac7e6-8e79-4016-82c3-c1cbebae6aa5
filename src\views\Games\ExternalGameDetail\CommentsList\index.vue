<template>
  <div class="comment-tab">
    <div class="comment-scores" v-if="rating.rating">
      <div class="score-left">
        <div class="score-num">
          <span>{{ rating.rating }}</span
          >{{ $t('分') }}
        </div>
        <div class="total">{{ $t('满分10分') }}</div>
      </div>
      <div class="score-right">
        <div class="star-list">
          <div class="star-item" v-for="(item, key) in starList" :key="key">
            <div class="star-row">
              <div
                class="star-current"
                :style="{
                  width: `${(5 - key.substring(key.length - 1)) * 20}%`,
                }"
              ></div>
            </div>
            <div class="score-progress">
              <div
                class="progress-current"
                :style="{ width: `${item}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="total-score-star" v-if="rating.rating">
      {{ rating.user_num }}{{ $t('个评分') }}
    </div>
    <div class="order-list">
      <div
        class="order-item"
        :class="{ current: order == item.order }"
        v-for="(item, index) in orderList"
        :key="index"
        @click="clickOrder(item.order)"
      >
        {{ item.name }}
      </div>
    </div>
    <content-empty v-if="empty"></content-empty>
    <load-more
      v-else
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="comment-list">
        <template v-for="item in topList">
          <comment-item
            :info="item"
            :key="`${item.comment_id}-top`"
            v-if="order == 0"
          ></comment-item
        ></template>
        <template v-for="item in hotList">
          <comment-item
            :info="item"
            :key="`${item.comment_id}-hot`"
            v-if="order == 0"
          ></comment-item
        ></template>
        <template v-for="item in commentList">
          <comment-item :info="item" :key="item.comment_id"></comment-item
        ></template>
      </div>
    </load-more>
  </div>
</template>
<script>
import { ApiCommentComments } from '@/api/views/comment.js';
import CommentItem from '@/components/comment-item';
import { mapGetters } from 'vuex';
export default {
  name: 'CommentList',
  components: {
    CommentItem,
  },
  data() {
    return {
      topList: [],
      hotList: [],
      commentList: [],
      page: 1,
      listRows: 10,
      gameId: '',
      rating: {},
      loading: false,
      finished: false,
      empty: false,
      order: 0,
      orderList: [
        {
          name: this.$t('默认'),
          order: 0,
        },
        {
          name: this.$t('最新'),
          order: 2,
        },
      ],
    };
  },
  computed: {
    starList() {
      let percentName = Object.keys(this.rating).filter(key =>
        /percent/.test(key),
      );
      let obj = {};
      percentName.forEach(item => {
        obj[item] = this.rating[item];
      });
      return obj;
    },
    ...mapGetters({
      gameInfo: 'game/gameInfo',
    }),
  },
  async created() {
    this.gameId = this.$route.params.id;
    this.rating = this.gameInfo.rating;
    this.loading = true;
    await this.getCommentList();
  },
  methods: {
    async clickOrder(order) {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.order = order;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getCommentList();
      this.$toast.clear();
    },
    async getCommentList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCommentComments({
        page: this.page,
        listRows: this.listRows,
        classId: 103,
        sourceId: this.gameId,
        order: this.order,
      });
      let { hots, comments, rating, tops, cmt_sum } = res.data;
      this.rating = rating;
      if (cmt_sum) {
        this.$emit('updateCommentSum', cmt_sum);
      }
      if (action === 1 || this.page === 1) {
        this.commentList = [];
        if (tops && tops.length) {
          this.topList = tops;
        }
        if (hots && hots.length) {
          this.hotList = hots;
        }
        if (!comments.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.commentList.push(...comments);
      if (comments.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.$nextTick(() => {
        this.loading = false;
      });
    },
    async loadMore() {
      await this.getCommentList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.comment-tab {
  .order-list {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    width: 148 * @rem;
    .order-item {
      width: 74 * @rem;
      height: 42 * @rem;
      font-size: 16 * @rem;
      color: #9a9a9a;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &:not(:first-of-type) {
        &::before {
          content: '';
          width: 1 * @rem;
          height: 7 * @rem;
          background-color: #c1c1c1;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      &.current {
        color: #000000;
        font-weight: 500;
        &::after {
          content: '';
          width: 10 * @rem;
          height: 3 * @rem;
          background-color: @themeColor;
          border-radius: 2 * @rem;
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
        }
      }
    }
  }
  .comment-scores {
    display: flex;
    padding-top: 25 * @rem;
    padding-right: 43 * @rem;
    .score-left {
      width: 136 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .score-num {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        span {
          font-size: 30 * @rem;
          color: #000000;
          font-weight: 600;
        }
      }
      .total {
        font-size: 11 * @rem;
        color: #9a9a9a;
        font-weight: 400;
        margin-top: 4 * @rem;
      }
    }
    .score-right {
      .star-list {
        .star-item {
          display: flex;
          align-items: center;
          &:not(:first-of-type) {
            margin-top: 5 * @rem;
          }
          .star-row {
            width: 60 * @rem;
            height: 8 * @rem;
            background: url(~@/assets/images/games/star-row.png) center center
              no-repeat;
            background-size: 56 * @rem 8 * @rem;
            .star-current {
              width: 20%;
              height: 100%;
              background-color: #fff;
            }
          }
          .score-progress {
            width: 132 * @rem;
            height: 4 * @rem;
            border-radius: 3 * @rem;
            background-color: #d8d8d8;
            margin-left: 8 * @rem;
            .progress-current {
              width: 100%;
              height: 100%;
              background-color: @themeColor;
              border-radius: 3 * @rem;
            }
          }
        }
      }
    }
  }
  .total-score-star {
    padding-right: 43 * @rem;
    text-align: right;
    font-size: 10 * @rem;
    font-weight: 400;
    color: #797979;
    height: 30 * @rem;
    line-height: 30 * @rem;
  }
  .comment-list {
    padding: 0 18 * @rem;
  }
}
</style>
