<template>
  <!-- 二维码支付弹窗 -->
  <van-popup
    v-model="show"
    :close-on-click-overlay="false"
    :lock-scroll="false"
    position="bottom"
    round
    class="ewm-pay"
  >
    <div class="pay-type">{{ payInfo.alias }}支付</div>
    <div class="qrcode-content" v-if="ewmSrc">
      <div class="money" v-if="payInfo.amount"
        ><span>{{ payInfo.currencySymbol }}</span
        >{{ payInfo.amount }}</div
      >
      <div id="qrcode" class="qrcode" ref="qrcode"></div>
    </div>
    <div class="network-error" v-else>
      <div class="network-error-img"></div>
      <div class="tips">{{ $t("网络异常，请重试") }}</div>
      <div class="retry btn" @click="retry">{{ $t("重试") }}</div>
    </div>
    <div class="close-btn btn" @click="handleRefresh"></div>
    <div @click="handleRefresh" class="button btn" v-if="ewmSrc">{{ $t("我已支付") }}</div>
  </van-popup>
</template>

<script>
import { ApiGetOrderStatus } from '@/api/views/recharge.js';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import QRCode from 'qrcodejs2';
export default {
  name: 'ewmPopup',
  data() {
    return {
      show: false,
      ewmSrc:"",
      payInfo:{},
    };
  },
  watch: {
    ewmSrc() {
      this.$nextTick(() => {
        this.$refs.qrcode.innerHTML = '';
        this.createQrCode();
      });
    },
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    createQrCode() {
      new QRCode(this.$refs.qrcode, {
        text: this.ewmSrc, // 需要转换为二维码的内容
        width: 180,
        height: 180,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    onClose(){

    },
    async handleRefresh() {
      this.onClose();
      this.show = false;
      this.$toast.loading('加载中');
      this.$emit('success'); // 支付完成的操作
      this.SET_USER_INFO();
    },
    retry() {
      this.$nextTick(() => {
        this.$refs.qrcode.innerHTML = '';
        this.createQrCode();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ewm-pay {
  width: 100%;
  padding: 16 * @rem;
  background-color: #f3f5f9;
  box-sizing: border-box;

  .pay-type {
    width: 100%;
    height: 25 * @rem;
    font-weight: 500;
    font-size: 18 * @rem;
    color: #000000;
    line-height: 25 * @rem;
    text-align: center;
  }
  .qrcode-content {
    width: 100%;
    background: #ffffff;
    border-radius: 19 * @rem;
    margin-top: 20 * @rem;
    padding: 16 * @rem 20 * @rem 20 * @rem;
    box-sizing: border-box;

    .money {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 45 * @rem;
      font-weight: 500;
      font-size: 32 * @rem;
      color: #222222;
      line-height: 45 * @rem;

      span {
        height: 22 * @rem;
        font-weight: 500;
        font-size: 16 * @rem;
        color: #222222;
        line-height: 22 * @rem;
        margin-right: 2 * @rem;
        margin-top: 7 * @rem;
      }
    }

    .last-time {
      height: 15 * @rem;
      font-weight: 500;
      font-size: 11 * @rem;
      color: #fc3a3b;
      line-height: 15 * @rem;
      text-align: center;
    }
    .qrcode {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 220 * @rem;
      height: 220 * @rem;
      margin: 16 * @rem auto 0;
      canvas,
      img {
        display: block;
        width: 220 * @rem;
        height: 220 * @rem;
        margin: 0 auto;
      }
    }
  }
  .text {
    margin-top: 20 * @rem;
    font-size: 17 * @rem;
    text-align: center;
    color: #333;
  }
  .button {
    width: 100%;
    height: 44 * @rem;
    margin: 15 * @rem auto 0;
    background: @themeBg;
    font-size: 16 * @rem;
    color: #fff;
    line-height: 44 * @rem;
    text-align: center;
    border-radius: 6 * @rem;
  }
  .close-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    background: url(~@/assets/images/recharge/recharge-popup-close.png)
      no-repeat;
    background-size: 20 * @rem 20 * @rem;
    position: absolute;
    top: 20 * @rem;
    right: 14 * @rem;
  }
}
.network-error {
  display: block;
  margin: 45 * @rem auto 0;
  .network-error-img {
    width: 128 * @rem;
    height: 128 * @rem;
    background: url(~@/assets/images/network_error.png) no-repeat;
    background-size: 128 * @rem 128 * @rem;
    margin: 0 auto;
  }

  .tips {
    height: 20 * @rem;
    font-weight: 400;
    font-size: 14 * @rem;
    color: #999999;
    line-height: 20 * @rem;
    text-align: center;
    margin-top: 15 * @rem;
  }

  .retry {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 119 * @rem;
    height: 40 * @rem;
    border-radius: 24 * @rem;
    font-weight: 500;
    font-size: 16 * @rem;
    color: #3cd279;
    text-align: center;
    border: 1 * @rem solid #3cd279;
    margin: 37 * @rem auto 40 * @rem;
  }
}
</style>
