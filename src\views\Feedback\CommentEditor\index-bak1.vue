<template>
  <div class="comment-editor">
    <nav-bar-2 :title="$t('发表评论')" :border="true">
      <template #left>
        <slot name="left">
          <div class="back" @click="back"></div>
        </slot>
      </template>
    </nav-bar-2>
    <!-- 评分 -->
    <!-- <div v-if="class_id != 101" class="rate-container">
      <div class="rate-click">
        <van-rate
          v-model="score"
          color="#1CCE94"
          void-icon="star"
          void-color="#E3E5E8"
          :size="28"
        />
      </div>
      <div class="tips">轻点星星打个分</div>
    </div> -->
    <div class="editor-container">
      <div class="input-container">
        <textarea
          id="inputText"
          class="input-text"
          v-model.trim="inputText"
          rows="10"
          :placeholder="placeholder"
        ></textarea>
      </div>
      <div class="img-container">
        <van-uploader
          v-model="imageFileList"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="9"
          :preview-size="92"
          accept="image/*"
          :before-read="beforeRead"
          :multiple="true"
          class="uploader"
        >
          <div class="upload-btn"> </div>
          <div class="text">上传</div>
        </van-uploader>
      </div>
      <div class="check-box">
        <span class="check-box-time">
          <van-checkbox
            v-model="is_show_playtime"
            :icon-size="`${14 * remNumberLess}rem`"
            checked-color="#1CCE94"
          >
            显示游戏时长</van-checkbox
          >
        </span>
        <!-- <span class="check-box-phone">
          <van-checkbox
            v-model="is_show_model"
            :icon-size="`${14 * remNumberLess}rem`"
            checked-color="#1CCE94"
          >
            显示机型</van-checkbox
          ></span
        > -->
      </div>
    </div>
    <!-- <div v-if="class_id != 101" class="score-container">
      <div class="score-title">{{ $t('评分') }}：</div>
      <van-rate
        v-model="score"
        color="#FE6600"
        void-icon="star"
        void-color="#E0E0E0"
        :size="14"
      />
    </div> -->
    <div
      class="quick-comment"
      v-if="quick_comment_list && quick_comment_list.length && class_id != 101"
    >
      <div class="title">快速点评<span>（请点击下面条目）</span></div>
      <div class="list">
        <div
          v-for="(item, index) in quick_comment_list"
          :key="index"
          @click="handleQuickComment(index, item.text)"
          :class="{ current: comment_current === index }"
          class="item"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
    <!-- 评论按钮 -->
    <div class="bottom-container">
      <div class="bottom-fixed">
        <div class="btn" :class="{ on: canSend }" @click="handleSend">
          {{ $t('发表') }}</div
        >
      </div>
    </div>
    <!-- 退出弹窗 -->
    <van-popup
      v-model="isExitPopupShow"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="exit-popup"
    >
      <div class="exit-container">
        <div class="title">确定退出评价吗？</div>
        <div class="msg">已编辑的内容将不会保存噢~</div>
        <div class="btn-list">
          <div class="exit" @click="confirmExit">退出评价</div>
          <div class="continue" @click="isExitPopupShow = false">继续评价</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiCommentSubmit, ApiCommentQuick } from '@/api/views/comment.js';
import md5 from 'js-md5';
import { remNumberLess } from '@/common/styles/_variable.less';
export default {
  name: 'CommentEditor',
  data() {
    return {
      score: 5,
      inputText: '', // 评论内容
      imageFileList: [], // 本地图片列表
      images: [], // 上传后的图片URL列表
      quick_comment_list: [], //快速点评列表
      isSending: false,
      comment_current: -1, //当前选中快速评论
      class_id: 0,
      source_id: 0,
      model: 'iPhone X',
      comment_id: null, //评论id
      is_update: 1, //是否修改 1-修改 0-获取数据
      is_show_playtime: false,
      is_show_model: false,
      isExitPopupShow: false,
      remNumberLess,
      params: {},
    };
  },
  computed: {
    placeholder() {
      return this.class_id == 101
        ? '说点什么...'
        : '写出你对游戏玩法、操作、攻略等方面的可观评价吧，优质评论将给予金币奖励与前排展示~';
    },
    canSend() {
      if (this.inputText.length < 15) return false;
      if (this.score < 1) return false;
      return true;
    },
    formatContent() {
      return this.inputText.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  watch: {
    inputText() {
      if (
        this.comment_current !== -1 &&
        this.inputText !== this.quick_comment_list[this.comment_current].text
      ) {
        this.comment_current = -1;
      }
    },
  },
  async created() {
    this.class_id = this.$route.params.class_id;
    this.source_id = this.$route.params.source_id;
    this.comment_id = this.$route.params.comment_id || 0;
    this.totaldown = this.$route.params.totaldown || 0;
    this.is_update = this.$route.params.is_update;

    // 获取设备信息 没有详细写 部分获取设备信息不准
    this.model = this.getUserDevice();
    if (!this.is_update && this.comment_id) {
      this.getCommentInfo();
    }
    const res = await ApiCommentQuick();
    this.quick_comment_list = res.data.list;
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelector('#inputText').focus();
    });
  },
  methods: {
    // 获取当前评论
    async getCommentInfo() {
      const res = await ApiCommentSubmit({
        sourceId: this.source_id,
        classId: this.class_id,
        comment_id: this.comment_id,
        is_update: this.is_update,
      });
      this.sourceId = res.data.source_id;
      this.inputText = res.data.content;
      this.score = res.data.rating;
      this.is_show_playtime = res.data.is_show_playtime;
      this.is_show_model = res.data.is_show_model;
      this.is_update = 1;
      // 确保 images 是一个包含 URL 的数组
      if (res.data.images && Array.isArray(res.data.images)) {
        this.images = res.data.images;
        this.imageFileList = res.data.images.map(image => ({
          url: image, // 确保这里是正确的 URL
        }));
      }
    },
    back() {
      this.isExitPopupShow = true;
    },
    confirmExit() {
      this.$router.go(-1);
    },
    async handleSend() {
      if (this.isSending) {
        return false;
      }
      if (this.inputText.length < 15) {
        this.$toast(this.$t('评论的字数不得小于15个字'));
        return false;
      }
      if (this.score < 1) {
        this.$toast(this.$t('请选择你的评分'));
        return false;
      }
      this.isSending = true;
      this.params = {
        sourceId: this.source_id,
        classId: this.class_id,
        model: this.model,
        content: this.formatContent,
        rating: this.score,
        is_show_playtime: this.is_show_playtime ? 1 : 0,
        is_show_model: this.is_show_model ? 1 : 0,
        is_update: this.is_update,
      };
      if (this.images.length) {
        this.params.images = JSON.stringify(this.images);
      }
      this.$toast.loading({
        message: this.$t('正在发布评论...'),
      });
      try {
        const res = await ApiCommentSubmit({ ...this.params });
        this.$toast(res.msg);
        setTimeout(() => {
          this.$router.go(-1);
        }, 500);
      } finally {
        this.isSending = false;
      }
    },
    handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      ApiUploadImage(data).then(
        res => {
          this.images.push(res.data.url);
          file.status = 'done';
          file.message = this.$t('上传成功');
        },
        err => {
          file.status = 'failed';
          file.message = this.$t('上传失败');
        },
      );
    },
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
    // 点击快速评论
    handleQuickComment(index, text) {
      this.comment_current = index;
      this.inputText = text;
    },
    // 获取用户设备信息
    getUserDevice() {
      const ua = navigator.userAgent;
      let deviceInfo = '';

      // 检测设备类型
      if (/iPhone|iPad|iPod/i.test(ua)) {
        // iOS 设备
        const match = ua.match(/iPhone OS (\d+)_(\d+)/i);
        if (match) {
          const model = this.getIOSModel();
          const version = `${match[1]}.${match[2]}`;
          deviceInfo = model || `iPhone (iOS ${version})`;
        } else {
          deviceInfo = 'iPhone';
        }
      } else if (/Android/i.test(ua)) {
        // Android 设备
        const match = ua.match(/Android (\d+)\.(\d+)/i);
        const modelMatch = ua.match(/\((.+?)\)/);
        let model = modelMatch ? modelMatch[1].split(';')[1] : '';
        model = model ? model.trim() : 'Android';

        if (match) {
          const version = `${match[1]}.${match[2]}`;
          deviceInfo = `${model} (Android ${version})`;
        } else {
          deviceInfo = model;
        }
      } else {
        // 其他设备
        deviceInfo = '其他设备';
      }

      this.model = deviceInfo;
      return deviceInfo;
    },

    // 获取 iOS 设备型号（基于屏幕尺寸和像素密度的估计）
    getIOSModel() {
      const width = window.screen.width;
      const height = window.screen.height;
      const ratio = window.devicePixelRatio;

      // 根据屏幕尺寸和像素密度估计设备型号
      if (width === 375 && height === 812 && ratio === 3) return 'iPhone X/XS';
      if (width === 414 && height === 896 && ratio === 2) return 'iPhone XR';
      if (width === 414 && height === 896 && ratio === 3)
        return 'iPhone XS Max';
      if (width === 375 && height === 667 && ratio === 2) return 'iPhone 6/7/8';
      if (width === 414 && height === 736 && ratio === 3)
        return 'iPhone 6/7/8 Plus';
      if (width === 320 && height === 568 && ratio === 2)
        return 'iPhone 5/5S/SE';
      if (width === 390 && height === 844 && ratio === 3)
        return 'iPhone 12/12 Pro/13/13 Pro';
      if (width === 428 && height === 926 && ratio === 3)
        return 'iPhone 12/13 Pro Max';
      if (width === 360 && height === 780 && ratio === 3)
        return 'iPhone 12/13 mini';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 14 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 14 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 14 Pro';
      if (width === 393 && height === 852 && ratio === 2) return 'iPhone 15';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 15 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 15 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 15 Pro';
      if (width === 430 && height === 932 && ratio === 3)
        return 'iPhone 16 Pro Max';
      if (width === 430 && height === 932 && ratio === 2)
        return 'iPhone 16 Plus';
      if (width === 393 && height === 852 && ratio === 3)
        return 'iPhone 16 Pro';
      if (width === 430 && height === 926 && ratio === 3) return 'iPhone 16';

      return 'iPhone';
    },
  },
};
</script>

<style lang="less" scoped>
.comment-editor {
  .send {
    width: 52 * @rem;
    height: 26 * @rem;
    border-radius: 13 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    color: #ffffff;
    font-weight: 600;
    background-color: #c1c1c1;
    &.on {
      background-color: @themeColor;
    }
  }
  .rate-container {
    margin-top: 24 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .tips {
      margin-top: 16 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
  .editor-container {
    margin-top: 26 * @rem;
    padding-bottom: 20 * @rem;
    width: 336 * @rem;
    margin: 26 * @rem auto 20 * @rem;
    height: 486 * @rem;
    background: #f7f8fa;
    border-radius: 6 * @rem;
    position: relative;
    .input-container {
      box-sizing: border-box;
      width: 100%;
      height: 162 * @rem;
      padding-bottom: 20 * @rem;
      .input-text {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 22 * @rem 18 * @rem;
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        line-height: 20 * @rem;
        border: 0;
        outline: none;
        background: #f7f8fa;
      }
    }
    .img-container {
      box-sizing: border-box;
      width: 100%;
      min-height: 92 * @rem;
      padding: 0 16 * @rem;
      .uploader {
        width: 100%;
        /deep/ .van-uploader__wrapper {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12 * @rem;
        }
        /deep/.van-uploader__input-wrapper {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        /deep/ .van-uploader__preview-image {
          border-radius: 6 * @rem;
          background: #f0f1f5;
          width: 92 * @rem;
          height: 92 * @rem;
        }
        /deep/.van-uploader__preview-delete {
          top: -5 * @rem;
          right: -2 * @rem;
          height: 14 * @rem;
          width: 14 * @rem;
          background: url('~@/assets/images/deal/upload-icon-delete.png')
            #f0f1f5 no-repeat center center;
          background-size: 14 * @rem 14 * @rem;
          border-radius: 0;
        }
        /deep/.van-icon {
          display: none;
        }
      }
      .upload-btn {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url('~@/assets/images/deal/upload-icon-1.png') #f0f1f5
          no-repeat center center;
        background-size: 24 * @rem 24 * @rem;
      }
      .text {
        margin-top: 8 * @rem;
        font-weight: 500;
        font-size: 12 * @rem;
        color: #93999f;
      }
    }
    .check-box {
      position: absolute;
      left: 12 * @rem;
      bottom: 12 * @rem;
      display: flex;
      align-items: center;
      .check-box-time,
      .check-box-phone {
        margin-left: 12 * @rem;
        /deep/.van-checkbox {
          display: flex;
          flex-direction: row-reverse;
        }
        /deep/.van-checkbox__label {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #93999f;
          margin-left: 0;
          margin-right: 4 * @rem;
        }
      }
    }
  }
  .score-container {
    display: flex;
    align-items: center;
    padding: 14 * @rem 18 * @rem;
    border-bottom: 0.5 * @rem solid #e8e8e8;
    .score-title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
    }
  }
  .quick-comment {
    border-top: 10 * @rem solid #f5f5f6;
    padding: 18 * @rem;
    .title {
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      span {
        font-size: 14 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .list {
      margin-top: 22 * @rem;
      .item {
        display: table;
        margin-bottom: 10 * @rem;
        padding: 8 * @rem 15 * @rem;
        background: #fff6e9;
        border-radius: 20 * @rem;
        color: @themeColor;
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        box-sizing: border-box;
        border: 1px solid #fff6e9;
        &.current {
          border: 1px solid @themeColor;
          color: @themeColor;
          font-weight: 600;
        }
      }
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      // box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: calc(@safeAreaBottom + 20 * @rem);
      padding-bottom: calc(@safeAreaBottomEnv + 20 * @rem);
      .btn {
        width: 336 * @rem;
        height: 44 * @rem;
        border-radius: 29 * @rem;
        text-align: center;
        line-height: 44 * @rem;
        color: #fff;
        font-weight: 500;
        font-size: 16 * @rem;
        background-color: #c1c1c1;
        &.on {
          background-color: @themeColor;
        }
      }
    }
  }
  .exit-popup {
    width: 300 * @rem;
    height: 200 * @rem;
    background: #fff;
    .exit-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-top: 30 * @rem;
        font-size: 16 * @rem;
        font-weight: bold;
        color: #191b1f;
      }
      .msg {
        margin-top: 25 * @rem;
        width: 196 * @rem;
        text-align: center;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #191b1f;
        line-height: 18 * @rem;
      }
      .btn-list {
        position: absolute;
        left: 25 * @rem;
        bottom: 20 * @rem;
        display: flex;
        align-items: center;
        .exit,
        .continue {
          width: 120 * @rem;
          height: 36 * @rem;
          background: #f7f8fa;
          border-radius: 22 * @rem;
          color: #93999f;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .continue {
          margin-left: 10 * @rem;
          background: #1cce94;
          color: #fff;
        }
      }
    }
  }
}
</style>
