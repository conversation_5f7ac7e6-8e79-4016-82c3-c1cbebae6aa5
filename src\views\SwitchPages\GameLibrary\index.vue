<template>
  <div class="game-library">
    <!-- 顶部 -->
    <div class="fixed">
      <status-bar></status-bar>
      <SearchContainer :white="false" />
    </div>
    <template v-for="item in data_list">
      <!-- banner -->
      <template v-if="item.view_type == 1">
        <YyBanner :bannerList="item.banner_list" />
      </template>
      <!-- 类型 -->
      <template v-if="item.view_type == 2">
        <div class="section game-cate">
          <div class="title">{{ item.header_title }}</div>
          <van-swipe class="cate-swiper" :loop="false" v-if="cate_list.length">
            <template v-for="cate in cate_list">
              <van-swipe-item>
                <div class="cate-list">
                  <div
                    v-for="item3 in cate"
                    @click="
                      toPage('Category', {
                        classId: 40,
                        info: { up_category_id: item3.id },
                      })
                    "
                    class="cate-item"
                  >
                    {{ item3.title }}
                  </div>
                </div>
              </van-swipe-item>
            </template>
          </van-swipe>
        </div>
      </template>
      <!-- 游戏列表 -->
      <template v-if="item.view_type == 3">
        <div class="section game">
          <div class="title">
            {{ item.header_title }}
            <span class="more" @click="toPage('UpGameList', { id: item.id })"
              >更多
              <img src="../../../assets/images/more-right-arrow.png" alt=""
            /></span>
          </div>
          <div class="game-list">
            <div
              v-for="game in item.game_list"
              @click="toPageDetail(game)"
              class="game-item"
            >
              <div class="game-img">
                <img :src="game.titlepic" />
              </div>
              <div class="game-name">{{ game.title }}</div>
            </div>
          </div>
        </div>
      </template>
      <!-- 大图游戏列表 -->
      <template v-if="item.view_type == 4">
        <div class="section big-game">
          <div class="title">
            {{ item.header_title }}
            <span class="more" @click="toPage('UpGameList', { id: item.id })"
              >更多
              <img src="../../../assets/images/more-right-arrow.png" alt=""
            /></span>
          </div>
          <div class="big-game-list">
            <div
              v-for="game in item.game_list"
              @click="toPageDetail(game)"
              class="big-game-item"
            >
              <img :src="game.video_thumb" class="big-game-img" />
              <div class="big-game-cont">
                <img :src="game.titlepic" alt="" class="big-game-pic" />
                <div class="big-game-info">
                  <div class="big-game-name">{{ game.title }}</div>
                  <div class="big-game-desc">
                    <span class="score">{{ game.score }}分</span>
                    <template v-if="game.type">
                      <span class="type" v-for="type in game.type">
                        {{ type }}
                      </span>
                    </template>
                  </div>
                </div>
                <div class="play-btn">试玩</div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <!-- 合集 -->
      <template v-if="item.view_type == 5">
        <div class="section heji">
          <div class="title">
            {{ item.header_title }}
            <span class="more" @click="toPage('UpCollection')"
              >更多
              <img src="../../../assets/images/more-right-arrow.png" alt=""
            /></span>
          </div>
          <div class="collection-list">
            <up-collection-item
              v-for="(collection, index) in item.heji_list"
              :key="index"
              :info="collection"
              class="collection-item"
            ></up-collection-item>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>
<script>
import StatusBar from '@/components/status-bar';
import SearchContainer from '@/components/search-container';
import YyBanner from '@/components/yy-banner';
import { ApiUpGameIndex } from '@/api/views/game';
import upCollectionItem from '@/components/up-collection-item';

export default {
  components: {
    YyBanner,
    StatusBar,
    SearchContainer,
    upCollectionItem,
  },
  data() {
    return {
      data_list: [],
      cate_list: [],
    };
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      const res = await ApiUpGameIndex();
      this.data_list = res.data;
    },
    toPageDetail(item) {
      if (item.classid === 40) {
        this.toPage('UpDetail', { id: item.id, gameInfo: item });
      } else {
        this.toPage('GameDetail', { id: item.id, gameInfo: item });
      }
    },
  },
  watch: {
    data_list(newValue) {
      let cate_list = [];
      newValue.forEach(item => {
        if (item.view_type === 2) {
          cate_list = item.cate_list;
        }
      });
      if (cate_list.length <= 8) {
        this.cate_list = [...cate_list];
      }
      let list = [];
      let allList = [];
      cate_list.forEach((item, index) => {
        if (
          index !== 0 &&
          (index % 7 === 0 || index === cate_list.length - 1)
        ) {
          list.push(item);
          allList.push(list);
          list = [];
        } else {
          list.push(item);
        }
      });
      this.cate_list = allList;
    },
  },
};
</script>
<style lang="less" scoped>
.fixed {
  background: #fff;
  box-sizing: border-box;
  height: calc(55 * @rem + @safeAreaTop);
  height: calc(55 * @rem + @safeAreaTopEnv);
  position: fixed;
  left: 0;
  .fixed-center;
  top: 0;
  width: 100%;
  z-index: 2000;
}
.game-library {
  padding-top: calc(55 * @rem + @safeAreaTop);
  padding-top: calc(55 * @rem + @safeAreaTopEnv);
  .swipe {
    width: 335 * @rem;
    height: 160 * @rem;
    margin: 10 * @rem 20 * @rem;
    border-radius: 6 * @rem;
  }
}
.section {
  margin: 30 * @rem 17 * @rem 30 * @rem 18 * @rem;
  .title {
    margin-bottom: 12 * @rem;
    font-size: 18 * @rem;
    line-height: 21 * @rem;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .more {
      display: flex;
      align-items: center;
      color: #999;
      height: 20 * @rem;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #999999;
      line-height: 16 * @rem;

      img {
        display: block;
        width: 16 * @rem;
        height: 16 * @rem;
      }
    }
  }
  .cate-list {
    overflow: hidden;
    .cate-item {
      float: left;
      width: 75 * @rem;
      height: 30 * @rem;
      margin: 0 12 * @rem 12 * @rem 0;
      background: #efefef;
      display: flex;
      justify-content: center;
      // align-items: center;
      border-radius: 15 * @rem;
      padding: 0 8 * @rem;
      box-sizing: border-box;
      line-height: 30 * @rem;
      overflow: hidden;
      text-align: center;

      &:nth-of-type(4n) {
        margin-right: 0;
      }
    }
  }
  .game-list {
    display: flex;
    overflow-x: auto;
    padding-bottom: 10 * @rem;

    .game-item {
      flex: 0 0 64 * @rem;
      margin-right: 20 * @rem;

      &:last-of-type {
        margin-right: 0;
      }

      .game-img {
        display: block;
        margin: 0 auto 8 * @rem;
        width: 64 * @rem;
        height: 64 * @rem;
        border-radius: 14 * @rem;
        overflow: hidden;
      }
      .game-name {
        font-size: 12 * @rem;
        line-height: 15 * @rem;
        width: 100%;
        margin: 0 auto;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-align: center;
      }
    }
  }
  .big-game-list {
    display: flex;
    overflow-x: auto;
    padding-bottom: 10 * @rem;

    .big-game-item {
      flex: 0 0 300 * @rem;
      height: 140 * @rem;
      position: relative;
      margin-right: 20 * @rem;
      border-radius: 10 * @rem;
      overflow: hidden;

      &:last-of-type {
        margin-right: 0;
      }

      .big-game-cont {
        width: 100%;
        height: 55 * @rem;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #020116 100%);
        box-sizing: border-box;
        padding: 0 10 * @rem;
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;

        .big-game-pic {
          display: block;
          width: 44 * @rem;
          height: 44 * @rem;
          border-radius: 10 * @rem;
          border: 1 * @rem solid #fff;
          flex-shrink: 0;
        }

        .big-game-info {
          display: block;
          flex: 1;
          min-width: 0;
          margin: 0 18 * @rem 0 8 * @rem;

          .big-game-name {
            display: block;
            height: 18 * @rem;
            font-size: 14 * @rem;
            font-weight: 500;
            color: #ffffff;
            line-height: 18 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-top: 2 * @rem;
          }

          .big-game-desc {
            display: flex;
            align-items: center;
            width: 100%;
            height: 15 * @rem;
            font-size: 12 * @rem;
            font-weight: 400;
            color: #929292;
            line-height: 15 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-top: 6 * @rem;
            flex-wrap: wrap;

            .score {
              margin-right: 4 * @rem;
              flex-shrink: 0;
            }

            .type {
              padding: 0 4 * @rem;
              border-left: 1 * @rem solid #929292;

              &:nth-of-type(2) {
                border-left: none;
              }
            }
          }
        }

        .play-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 58 * @rem;
          height: 28 * @rem;
          border-radius: 28 * @rem;
          background-color: @themeColor;
          color: #fff;
          font-size: 13 * @rem;
          margin-top: 9 * @rem;
        }
      }
    }
  }
  .collection-list {
    padding-bottom: 10 * @rem;
    display: flex;
    overflow-x: auto;

    .collection-item {
      width: 306 * @rem;
      flex-shrink: 0;
      margin-top: 0;
      margin-right: 12 * @rem;
      box-shadow: 0px 0px 8 * @rem 0px rgba(5, 0, 255, 0.08);

      &:last-of-type {
        margin-right: 0;
      }
    }

    .heji-item {
      flex: 0 0 340 * @rem;
      padding: 20 * @rem 13 * @rem 15 * @rem;
      margin-bottom: 15 * @rem;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        #eff3ff 0%,
        #ffffff 24%,
        #ffffff 100%
      );
      box-shadow: 0 * @rem 0 * @rem 8 * @rem 0 * @rem rgba(5, 0, 255, 0.08);
      border-radius: 10 * @rem;

      .heji_main_title {
        font-size: 18 * @rem;
        font-weight: bold;
        color: #333333;
        line-height: 23 * @rem;
      }

      .heji_vice_title {
        font-size: 13;
        font-weight: 400;
        color: #9a9a9a;
        line-height: 17 * @rem;
        margin-top: 5 * @rem;
      }

      .up_info {
        margin-top: 20 * @rem;
        display: flex;
        align-items: center;

        .avatar {
          width: 24 * @rem;
          height: 24 * @rem;
          overflow: hidden;
          border-radius: 50%;
        }

        .up_nickname {
          font-size: 14 * @rem;
          font-weight: 400;
          color: #333333;
          margin-left: 8 * @rem;
          flex: 1;
          min-width: 0;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .heji-game-list {
        margin-top: 15 * @rem;
        background-color: #f6f9fc;
        border-radius: 10 * @rem;
        padding: 10 * @rem;

        .heji-game-item {
          display: flex;
          align-items: center;
          margin-bottom: 16 * @rem;

          &:last-of-type {
            margin-bottom: 0;
          }

          .heji-game-img {
            flex-shrink: 0;
            width: 60 * @rem;
            height: 60 * @rem;
            border-radius: 10 * @rem;
            overflow: hidden;
          }

          .heji-game-info {
            flex: 1;
            min-width: 0;
            margin: 0 8 * @rem;

            .heji-game-name {
              width: 100%;
              height: 22 * @rem;
              font-size: 16 * @rem;
              font-weight: 500;
              color: #333333;
              line-height: 22 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .heji-game-desc {
              width: 100%;
              height: 17 * @rem;
              font-size: 12 * @rem;
              font-weight: 400;
              color: #929292;
              line-height: 17 * @rem;
              margin-top: 10 * @rem;
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              overflow: hidden;

              .score {
                margin-right: 4 * @rem;
                flex-shrink: 0;
              }

              .type {
                padding: 0 4 * @rem;
                border-left: 1 * @rem solid #929292;

                &:nth-of-type(2) {
                  border-left: none;
                }
              }
            }
          }

          .play-btn {
            width: 58 * @rem;
            height: 28 * @rem;
            line-height: 28 * @rem;
            border-radius: 20 * @rem;
            border: 1 * @rem solid @themeColor;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            font-weight: 400;
            color: @themeColor;
          }
        }
      }

      .list_banner {
        width: 313 * @rem;
        height: 176 * @rem;
        border-radius: 10 * @rem;
        overflow: hidden;
        margin-top: 15 * @rem;
      }
    }
  }
}
.cate-swiper {
  padding-bottom: 21 * @rem;

  /deep/ .van-swipe__indicator {
    width: 5 * @rem;
    height: 5 * @rem;
    background-color: #e5e5e5;
    opacity: 1;
    border-radius: 3 * @rem;
  }
  /deep/ .van-swipe__indicator--active {
    width: 13 * @rem;
    height: 5 * @rem;
    background-color: @themeColor;
  }
  /deep/ .van-swipe__indicator:not(:last-child) {
    margin-right: 3 * @rem;
  }
}
</style>
