<template>
  <div class="game-list">
    <div v-for="(item, index) in gameList" :key="index" class="game-item">
      <img :src="item.titlepic" class="left" />
      <div class="center">{{ item.title }}</div>
      <div class="right" @click="toRebate(item)">{{ $t('申请') }}</div>
    </div>
  </div>
</template>
<script>
import { mapMutations } from 'vuex';

export default {
  name: 'GameList',
  props: {
    gameList: {
      type: Array,
      default: [],
    },
  },
  methods: {
    toRebate(item) {
      this.setRebateInit(item);
      this.$router.push({ name: 'RebateFirst', params: { game_id: item.id } });
    },
    ...mapMutations({
      setRebateInit: 'rebate/setInit',
    }),
  },
};
</script>
<style lang="less" scoped>
.game-list {
  .game-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20 * @rem 14 * @rem;
    border-bottom: 1px solid #e5e5e5;
    .left {
      flex: 0 0 40 * @rem;
      width: 40 * @rem;
      height: 40 * @rem;
      border-radius: 8 * @rem;
    }
    .center {
      flex: 1;
      margin-left: 8 * @rem;
      text-align: left;
      font-size: 16 * @rem;
    }
    .right {
      width: 60 * @rem;
      height: 28 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      border-radius: 6 * @rem;
      background: @themeBg;
      font-size: 15 * @rem;
      color: #fff;
    }
  }
}
</style>
