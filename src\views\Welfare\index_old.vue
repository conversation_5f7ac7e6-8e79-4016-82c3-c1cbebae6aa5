<template>
  <div class="welfare-page">
    <nav-bar-2
      :placeholder="false"
      title="福利中心"
      :backShow="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :bgStyle="bgStyle"
    ></nav-bar-2>
    <div class="top-bar"></div>
    <van-loading class="loading-box" v-if="!loadSuccess" />
    <!-- 福利中心碎片-------------5.8新 -->
    <template v-for="(section, sectionIndex) in pageData" v-else>
      <!-- 轮播碎片 -->
      <van-swipe
        class="banner-fragment"
        indicator-color="#FF7348"
        :autoplay="5000"
        v-if="section.view_type == 35"
        :key="sectionIndex"
      >
        <van-swipe-item
          class="swiper-slide"
          v-for="(item, index) in section.banner"
          :key="index"
          @click="CLICK_EVENT(item.click_id)"
        >
          <img class="banner" :src="item.titleimg" @click="clickBanner(item)" />
        </van-swipe-item>
      </van-swipe>
      <!-- 金刚区 -->
      <div
        class="section gold-container"
        :key="sectionIndex"
        v-if="section.view_type == 24"
      >
        <swiper
          class="select-list"
          :options="swiperOptions"
          :auto-update="true"
          style="width: 100%; margin: 0 auto"
          v-if="section.tab_action.length > 0"
        >
          <swiper-slide
            class="select-item btn"
            v-for="(item, index) in section.tab_action"
            :key="index"
          >
            <div class="select-icon" v-if="item.icon_lottie">
              <yy-lottie
                class="lottie-icon"
                :width="48"
                :height="48"
                :options="{
                  autoplay: true,
                  loop: true,
                  path: item.icon_lottie,
                }"
                v-if="item.icon_lottie"
              ></yy-lottie>
            </div>
            <div class="select-icon" v-else>
              <img :src="item.icon_url" alt="" />
            </div>
            <div class="select-text">
              <div class="select-name">{{ item.text1 }}</div>
            </div>
          </swiper-slide>
        </swiper>
        <div class="swiper-scrollbar"></div>
      </div>
      <!-- 平台福利 -->
      <div
        class="fuli-container section"
        :key="sectionIndex"
        v-if="section.view_type == 36"
        :class="{ 'border-radius-select': section.view_type == 36 }"
      >
        <div class="section-img-title">
          <img :src="section.head_img" alt="" />
        </div>
        <div class="welfare-bar">
          <div
            class="welfare-banner"
            v-for="(item, index) in section.tab_action"
            :key="index"
            @click="clickViewType36Item(item)"
          >
            <img :src="item.icon_url" alt="" />
          </div>
        </div>
      </div>
      <!-- <div v-if="section.view_type == 36" :key="'extra_' + sectionIndex">
          <div
            @click="toGameTester(game_tester)"
            class="game-tester-banner btn"
            v-if="game_tester.type"
          >
            <img :src="game_tester.titleimg" alt="" />
          </div>

          <div
            @click="toGameTry(trial_play)"
            class="game-demo-banner btn"
            v-if="trial_play.type"
          >
            <img :src="trial_play.titleimg" alt="" />
          </div>
          <div
            class="section btn"
            @click="toIdCard"
            v-if="userInfo.auth_status != 2"
          >
            <img src="@/assets/images/welfare/shimingrenzheng.png" alt="" />
          </div>
        </div> -->
      <!-- 648暴爽 -->
      <div
        class="section gift-648-container"
        :key="sectionIndex"
        v-if="section.view_type == 44"
      >
        <div class="section-img-title">
          <img :src="section.head_img" :alt="section.header_title" />
        </div>
        <div class="gift-list">
          <div
            class="gift-item"
            v-for="(item, index) in section.card648_list"
            :key="index"
            @click="toPage('GameDetail', { id: item.game.id })"
          >
            <div class="tag">剩余{{ item.remain }}%</div>
            <div class="game-icon">
              <img :src="item.game.titlepic" :alt="item.game.title" />
            </div>
            <div class="game-title">{{ item.game.title }}</div>
            <div class="game-cate">
              <span
                v-for="(type, typeIndex) in item.game.type"
                :key="typeIndex"
              >
                {{ type
                }}{{
                  typeIndex == item.game.type.length - 1 ? '' : '&nbsp;·&nbsp;'
                }}
              </span>
            </div>
            <div class="get-btn btn" @click.stop="takeGift(item)">
              {{ item.cardpass ? '复制' : '领取' }}
            </div>
          </div>
        </div>
        <div class="btn section-more-btn" @click="goToWelfare648(section)">
          <div class="more-text">{{ section.bottom_text }}</div>
          <div class="more-right-icon"></div>
        </div>
      </div>

      <!-- 赏金任务 -->
      <div
        class="section bounty-container"
        :key="sectionIndex"
        v-if="section.view_type == 46"
      >
        <div class="section-img-title">
          <img :src="section.head_img" alt="" />
        </div>
        <div class="bounty-list">
          <div v-for="(item, index) in section.bounty_task_list" :key="index">
            <bounty-task-item
              :info="item"
              :key="componentKey"
              :isDisabled="true"
            ></bounty-task-item>
          </div>
        </div>
        <div class="section-more-btn" @click="goToBountyTask(section)">
          <div class="more-text">{{ section.bottom_text }}</div>
          <div class="more-right-icon"></div>
        </div>
      </div>

      <!-- 游戏签到 -->
      <div
        class="section sign-container"
        :key="sectionIndex"
        v-if="section.view_type == 45"
      >
        <div class="section-img-title">
          <img :src="section.head_img" alt="" />
        </div>
        <div class="game-bar">
          <div
            class="game-pic"
            :class="{ current: item.id == signSelected.id }"
            v-for="(item, index) in section.sign_in_list"
            :key="index"
            @click="signSelected = item"
          >
            <img :src="item.game_titlepic" alt="" />
          </div>
        </div>
        <div class="game-info">
          <div class="left-content">
            <div class="title-line">
              <div
                class="game-title"
                @click="toPage('GameDetail', { id: signSelected.game_id })"
              >
                {{ signSelected.game_title }}
              </div>
              <div class="tag">{{ signSelected.game_subtitle }}</div>
            </div>
            <div class="desc">{{ signSelected.desc }}</div>
          </div>
          <div class="sign-btn" @click="goToGameSignInDetail(signSelected)">
            签到
          </div>
        </div>
        <div class="section-more-btn" @click="goToGameSignInList(section)">
          <div class="more-text">{{ section.bottom_text }}</div>
          <div class="more-right-icon"></div>
        </div>
      </div>
      <!-- 金币加油站 -->
      <div
        class="task-container section"
        :key="sectionIndex"
        v-if="section.view_type == 37"
      >
        <div class="section-img-title">
          <img :src="section.head_img" alt="" />
        </div>
        <div class="task-list">
          <div
            class="task-item"
            v-for="task in section.tab_action"
            :key="task.name"
            @click="CLICK_EVENT(task.click_id)"
          >
            <div class="task-icon">
              <img :src="task.icon_url" alt="" />
            </div>
            <div class="task-info">
              <div class="task-center">
                <div class="title">{{ task.text1 }}</div>
                <div class="desc">{{ task.text2 }}</div>
              </div>
              <div class="task-btn btn" @click="actionToPage(task)">
                去完成
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 0元畅玩 -->
    <!-- <div class="zero-container section" v-if="discountGameList.length">
        <div class="section-title jingxuan">
          <div class="section-icon"></div>
          <div class="title-text">充值0.1折</div>
          <div class="section-more btn" @click="to01Discount">
            {{ $t("更多") }}
          </div>
        </div>
        <div class="zero-game">
          <div class="zero-list">
            <div
              class="zero-item"
              v-for="zero in discountGameList"
              :key="zero.id"
            >
              <game-item-2 :gameInfo="zero"></game-item-2>
            </div>
          </div>
        </div>
      </div> -->
    <!-- 热门活动 -->
    <!-- <div class="activity-container section" v-if="activityList.length">
        <div class="section-title jingxuan">
          <div class="section-icon"></div>
          <div class="title-text">{{ $t("热门活动") }}</div>
        </div>
        <yy-banner :bannerList="activityList" :width="325"></yy-banner>
      </div> -->

    <!-- 官包手动签到转自动签到的提示框 -->
    <van-dialog
      class="az-sign-dislog"
      v-model="azSignDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
    >
      <div class="az-sign-title">{{ $t('重要提醒') }}</div>
      <div class="az-sign-tip">
        <div class="line">{{ $t('福利中心已进行升级') }}</div>
        <div class="line">{{ $t('请您前往【签到】页面进行手动签到') }}</div>
      </div>
      <div class="no-notice" :class="{ remember: remember }">
        <div class="content" @click.stop="remember = !remember">
          <div class="gou"></div>
          {{ $t('不再提醒') }}
        </div>
      </div>
      <div class="operation">
        <div class="clock-btn btn" @click="clickAzKnow">
          {{ $t('我知道了') }}
        </div>
        <div class="clock-btn svip btn" @click="clickAzClockIn">
          {{ $t('去签到') }}
        </div>
      </div>
    </van-dialog>
    <cardpass-copy-popup
      :show.sync="giftCopyPopupShow"
      :info="giftSelected"
    ></cardpass-copy-popup>

    <!-- 省钱卡限时倒计时弹窗 -->

    <van-popup
      v-model="welfareSavingsPopup"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      round
      class="savings-countdown-popup"
    >
      <div class="popup-close" @click="closeWelfareSavingsPopup"></div>
      <div
        class="content"
        :style="{ backgroundImage: `url(${countdown.bg_img})` }"
      >
        <div class="countdown-clock">
          <span>{{ countdownObj.day }}</span
          >天 <span>{{ countdownObj.hour }}</span
          >时 <span>{{ countdownObj.minute }}</span
          >分
          <span>{{ countdownObj.second }}</span>
        </div>
        <div
          class="remember-container"
          @click="rememberSavingsCountdown = !rememberSavingsCountdown"
        >
          <div
            class="gou"
            :class="{ remember: rememberSavingsCountdown }"
          ></div>
          <div class="tips-text">不再提示</div>
        </div>
      </div>
      <div class="popup-btn" @click="goToSavingsCard"></div>
    </van-popup>
  </div>
</template>

<script>
import { ApiGame01DiscountList } from '@/api/views/game.js';
import { ApiIndexBannerList } from '@/api/views/system.js';
import { ApiUserWelfareIndex } from '@/api/views/users.js';
import xinshourenwu from '@/assets/images/welfare/xinshourenwu.png';
import chengjiurenwu from '@/assets/images/welfare/chengjiurenwu.png';
import meirirenwu from '@/assets/images/welfare/meirirenwu.png';
import jinbiduobao from '@/assets/images/welfare/duobao-bg.png';
import jinbizhuanpan from '@/assets/images/welfare/zhuanpan-bg.png';
import mingrentang from '@/assets/images/welfare/mingrentang.png';
import welfareIcon1 from '@/assets/images/welfare/welfare_icon1.png';
import welfareIcon2 from '@/assets/images/welfare/welfare_icon2.png';
import welfareIcon3 from '@/assets/images/welfare/welfare_icon3.png';
import welfareIcon4 from '@/assets/images/welfare/welfare_icon4.png';
import welfareIcon5 from '@/assets/images/welfare/welfare_icon5.png';
import welfareIcon6 from '@/assets/images/welfare/welfare_icon6.png';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import h5Page from '@/utils/h5Page.js';
import { isIos, isWebApp } from '@/utils/userAgent';
import welfare_bg1 from '@/assets/images/welfare/welfare_bg1.png';
import welfare_bg2 from '@/assets/images/welfare/welfare_bg2.png';

import { PageName, handleActionCode } from '@/utils/actionCode.js';

import { clickBanner } from '@/utils/function.js';

import {
  platform,
  BOX_showActivity,
  BOX_openInNewNavWindow,
  BOX_memAuth,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';

import { ApiWelfareCenter } from '@/api/views/welfare.js';
import bountyTaskItem from '@/components/bounty-task-item';
import { ApiCardRead, ApiCardGet } from '@/api/views/gift.js';
export default {
  name: 'Welfare',
  data() {
    let that = this;
    return {
      isIos,
      isWebApp,

      navbarOpacity: 0,
      bgStyle: 'transparent-white',

      loadSuccess: false,

      discountGameList: [],
      activityList: [],
      azSignDialogShow: false, // 安卓官包手动签到提醒是否显示
      remember: false, // 是否记住不再提示
      game_tester: {}, // 测试员
      trial_play: {}, // 游戏试玩
      spread_user: {}, // 邀请好友

      // 页面碎片数据
      pageData: [],
      giftCopyPopupShow: false, // 648礼包领取弹窗
      giftSelected: {},
      signSelected: {},
      swiperOptions: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.swiper-scrollbar',
        },
        on: {
          click: function () {
            let list = that.pageData.find(item => item.view_type == 24);
            that.CLICK_EVENT(list.tab_action[this.clickedIndex]?.click_id);
            that.actionToPage(list.tab_action[this.clickedIndex]);
          },
        },
      },

      welfareSavingsPopup: false, //省钱卡限时弹窗
      countdown: {}, // 显示倒计时对象
      timeClock: null, // 定时器
      rememberSavingsCountdown: false, // 是否记住省钱卡倒计时
      componentKey: 0,
    };
  },
  computed: {
    countdownObj() {
      if (this.countdown && this.countdown.endTime) {
        return this.formatTime(this.countdown.endTime - this.countdown.nowTime);
      } else {
        return {};
      }
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('福利中心');
      let as_sign_dialog_hide = localStorage.getItem('AZ_SIGN_DIALOG_HIDE');
      if (
        !as_sign_dialog_hide &&
        !this.userInfo.is_sign &&
        this.isBeforeDate('2022/4/1')
      ) {
        this.azSignDialogShow = true;
      }
    }
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  async activated() {
    // this.SET_USER_INFO(true);
    try {
      await this.getWelfareCenter();
      // 新手引导 如果有新手引导，就不弹省钱卡倒计时弹窗了
      await this.SET_SHOW_WELFARE_GUIDE_POPUP(2);
    } catch (e) {}
    this.loadSuccess = true;
    await this.getIndex();
    await this.getDiscountGame();
    // await this.getActivity();
  },
  deactivated() {
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  methods: {
    // 组件丢失响应式 刷新组件
    forceRerender() {
      this.componentKey += 1;
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
      SET_SHOW_WELFARE_GUIDE_POPUP: 'system/SET_SHOW_WELFARE_GUIDE_POPUP',
    }),
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    clickBanner,
    clickEvent(click_id) {
      if (click_id) {
        this.CLICK_EVENT(click_id);
      }
    },
    async getIndex() {
      const res = await ApiUserWelfareIndex();
      this.game_tester = res.data.game_tester;
      this.trial_play = res.data.trial_play;
      this.spread_user = res.data.spread_user;
      this.countdown = res.data.countdown; // 倒计时

      if (!this.userInfo.is_sqk_member) {
        // 判断是否有弹窗显示的缓存
        const WELFARE_SAVINGS_COUNTDOWN_SHOW = localStorage.getItem(
          'WELFARE_SAVINGS_COUNTDOWN_SHOW',
        );
        // 永久不再提醒
        if (WELFARE_SAVINGS_COUNTDOWN_SHOW == 99) {
          this.welfareSavingsPopup = false;
          return;
        }
        if (
          WELFARE_SAVINGS_COUNTDOWN_SHOW &&
          WELFARE_SAVINGS_COUNTDOWN_SHOW == new Date().getDate()
        ) {
          // 有缓存，并且是当天
          this.welfareSavingsPopup = false;
        } else {
          // 需要弹窗和定时器
          this.welfareSavingsPopup = true;
          // 清除定时器
          clearInterval(this.timeClock);
          this.timeClock = null;
          this.timeClock = setInterval(() => {
            this.countdown.nowTime += 1;
            if (this.countdown.endTime - this.countdown.nowTime <= 0) {
              clearInterval(this.timeClock);
              this.timeClock = null;
              this.welfareSavingsPopup = false;
            }
          }, 1000);
        }
      } else {
        this.welfareSavingsPopup = false;
      }
    },
    toClockIn() {
      BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
    },
    clickAzClockIn() {
      this.azSignDialogShow = false;
      this.handlenotice();
      this.$nextTick(() => {
        BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
      });
    },
    clickAzKnow() {
      this.azSignDialogShow = false;
      this.handlenotice();
    },
    toTxRechargeArea() {
      this.toPage('TxRechargeArea');
    },
    toFreePlay() {
      BOX_showActivity({ name: 'FreePlay' }, { page: 'lycw' });
    },
    to01Discount() {
      BOX_showActivity({ name: '01Discount' });
    },
    toInvite() {
      BOX_openInNewWindow(
        { name: 'Invite' },
        { url: `${window.location.origin}/#/invite` },
      );
    },
    toGameTester() {
      BOX_showActivity({ name: 'GameTester' }, { page: 'yxncy' });
    },
    toGameTry() {
      BOX_showActivity({ name: 'GameTry' }, { page: 'yxsw' });
    },
    toPayHelp() {
      BOX_openInNewWindow({ name: 'PayHelp' }, { url: h5Page.caifudengji });
    },

    toIdCard() {
      BOX_memAuth();
    },
    toPtbExchange() {
      this.toPage('GoldCoinExchange');
    },
    toSvip() {
      BOX_openInNewNavWindow({ name: 'Svip' }, { url: h5Page.svip_url });
    },
    toChangwan() {
      BOX_openInNewWindow(
        { name: 'SavingsCard' },
        { url: `${window.location.origin}/#/savings_card` },
      );
    },
    clickViewType36Item(item) {
      this.CLICK_EVENT(item.click_id);
      this.actionToPage(item);
    },

    // 前往赏金任务
    goToBountyTask(section) {
      this.CLICK_EVENT(section.click_id);
      BOX_openInNewWindow(
        { name: 'BountyTask' },
        { url: `${window.location.origin}/#/bounty_task` },
      );
    },
    goToGameSignInDetail(signSelected) {
      this.CLICK_EVENT(signSelected.click_id);
      this.toPage('GameSignInDetail', { id: signSelected.game_id });
    },
    goToGameSignInList(section) {
      this.CLICK_EVENT(section.click_id);
      this.toPage('GameSignInList');
    },
    goToWelfare648(section) {
      this.CLICK_EVENT(section.click_id);
      this.toPage('Welfare648');
    },

    handlenotice() {
      if (this.remember) {
        localStorage.setItem('AZ_SIGN_DIALOG_HIDE', true);
      }
    },
    isBeforeDate(dateStr) {
      let curDate = new Date(),
        endDate = new Date(dateStr);
      if (curDate <= endDate) {
        return true;
      }
      return false;
    },
    async getDiscountGame() {
      const res = await ApiGame01DiscountList({ id: 151, listRows: 5 });
      this.discountGameList = res.data.game_list;
    },
    async getActivity() {
      const res = await ApiIndexBannerList({ showType: 2 });
      this.activityList = res.data.list;
    },
    actionToPage(item) {
      handleActionCode(item);
      // if (item.web_page) {
      //   this.toPage(item.web_page);
      //   return;
      // }
      // if (PageName[item.action_code]) {
      //   this.toPage(PageName[item.action_code]);
      //   return;
      // }
    },
    handleToPage(select, nav = 0) {
      if (
        (platform != 'android' && !select.component) ||
        (platform == 'android' && !select.url && !select.page)
      ) {
        this.$toast(this.$t('敬请期待'));
        return;
      }
      if (select.url) {
        // 安卓打开新的webview;
        if (nav == 1) {
          BOX_openInNewNavWindow(
            { name: select.component },
            { url: select.url },
          );
        } else {
          BOX_openInNewWindow({ name: select.component }, { url: select.url });
        }
      } else {
        // 安卓打开原生页面
        BOX_showActivity({ name: select.component }, { page: select.page });
      }
    },

    async getWelfareCenter() {
      const res = await ApiWelfareCenter();
      this.pageData = res.data;
      this.signSelected = this.pageData.find(item => {
        return item.view_type == 45;
      })?.sign_in_list[0];
      this.forceRerender();
    },
    async takeGift(item) {
      this.CLICK_EVENT(item.click_id);
      this.$toast.loading('加载中');
      if (!item.cardpass) {
        const res = await ApiCardGet({
          cardId: item.id,
          autoXh: 1,
        });
        this.giftSelected = res.data;

        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${item.game_id}`,
          adv_id: '暂无',
          game_name: item.titlegame,
          game_type: `${item.classid}`,
          game_size: '暂无',
          reward_type: item.title, // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });

        await this.getWelfareCenter();
      } else {
        this.giftSelected = item;
      }
      this.$toast.clear();
      this.$nextTick(() => {
        this.giftCopyPopupShow = true;
      });
    },

    // 关闭省钱卡限时弹窗
    closeWelfareSavingsPopup() {
      if (!this.rememberSavingsCountdown) {
        localStorage.setItem(
          'WELFARE_SAVINGS_COUNTDOWN_SHOW',
          new Date().getDate(),
        );
      } else {
        localStorage.setItem('WELFARE_SAVINGS_COUNTDOWN_SHOW', 99);
      }
      this.welfareSavingsPopup = false;
    },
    goToSavingsCard() {
      this.closeWelfareSavingsPopup();
      this.toPage('SavingsCard');
    },

    // 格式化时间戳为日时分秒
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let day = this.addZero(Math.floor(timeStamp / 3600 / 24));
      let hour = this.addZero(Math.floor(timeStamp / 3600) % 24);
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return {
        day,
        hour,
        minute,
        second,
      };
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
  },
  components: {
    bountyTaskItem,
  },
};
</script>

<style lang="less" scoped>
.welfare-page {
  font-size: 14 * @rem;
  background-color: #def5f7;
  .top-bar {
    width: 100%;
    height: 148 * @rem;
    .image-bg('~@/assets/images/welfare/top-bg-new.png');
  }
  .loading-box {
    display: flex;
    justify-content: center;
    margin-top: 20 * @rem;
  }
  .section {
    width: 355 * @rem;
    background-color: #fff;
    border-radius: 12px;
    margin: 0 auto 10 * @rem;
    padding-bottom: 1 * @rem;

    .section-img-title {
      width: 355 * @rem;
      height: 48 * @rem;
    }
    .section-more-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 328 * @rem;
      height: 36 * @rem;
      border-radius: 8 * @rem;
      background: #f8f8fa;
      margin: 18 * @rem auto 20 * @rem;
      .more-text {
        font-size: 14 * @rem;
        color: #333333;
      }
      .more-right-icon {
        width: 12 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/welfare/more-right-icon.png) no-repeat;
        background-size: 12 * @rem 12 * @rem;
        margin-left: 5 * @rem;
      }
    }
  }
  .user-bar {
    display: flex;
    align-items: center;
    margin-top: -55 * @rem;
    .user-info {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      .avatar {
        width: 35 * @rem;
        height: 35 * @rem;
        border-radius: 50%;
        overflow: hidden;
        margin-left: 23 * @rem;
      }
      .user-info-detail {
        flex: 1;
        min-width: 0;
        margin-left: 9 * @rem;
        .nickname {
          font-size: 16 * @rem;
          color: #111111;
          font-weight: 600;
        }
        .username {
          font-size: 13 * @rem;
          color: #797979;
          font-weight: 400;
          margin-top: 3 * @rem;
        }
      }
    }
    .clock-btn {
      width: 122 * @rem;
      height: 67 * @rem;
      .image-bg('~@/assets/images/welfare/clock-in-btn.png');
      &.had {
        background-image: url(~@/assets/images/welfare/clock-in-btn-had.png);
        background-size: 108 * @rem 53 * @rem;
        background-position: center 10 * @rem;
      }
    }
  }
  .banner-fragment {
    margin: 10 * @rem;
    margin-top: -44 * @rem;
    width: 355 * @rem;
    height: 100 * @rem;
    border-radius: 10 * @rem;
    /deep/ .van-swipe__indicators {
      left: unset;
      right: 20 * @rem;
      bottom: 3 * @rem;
    }
    /deep/ .van-swipe__indicator {
      width: 6 * @rem;
      height: 6 * @rem;
      color: rgba(27, 27, 27, 0.2);
      background-color: rgba(27, 27, 27, 0.2);
      opacity: 1;
      border-radius: 3 * @rem;
    }
    /deep/ .van-swipe__indicator--active {
      width: 10 * @rem;
      height: 6 * @rem;
      background-color: #faae86;
    }
    /deep/ .van-swipe__indicator:not(:last-child) {
      margin-right: 3 * @rem;
    }
  }
  .section-title {
    display: flex;
    align-items: center;
    height: 31 * @rem;
    background-size: auto 31 * @rem;
    background-repeat: no-repeat;
    background-position: 30 * @rem bottom;
    padding-bottom: 5 * @rem;
    margin-left: 20 * @rem;
    margin-right: 10 * @rem;
    .section-more {
      padding: 0 5 * @rem;
      height: 100%;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #797979;
    }
    &.pingtaifuli {
      background-image: url(~@/assets/images/welfare/pingtaifuli-bg.png);
    }
    &.jingxuan {
      background-image: url(~@/assets/images/welfare/jingxuan-bg.png);
    }
    .section-icon {
      width: 22 * @rem;
      height: 22 * @rem;
      .image-bg('~@/assets/images/welfare/section-title-icon.png');
    }
    .title-text {
      flex: 1;
      min-width: 0;
      font-size: 18 * @rem;
      font-weight: 600;
      color: #000000;
      margin-left: 5 * @rem;
    }
  }
  .game-tester-banner {
    width: 355 * @rem;
    height: 79 * @rem;
    margin: 0 auto;
    border-radius: 12 * @rem;
    overflow: hidden;
  }
  .game-demo-banner {
    width: 355 * @rem;
    height: 79 * @rem;
    margin: 10 * @rem auto;
    border-radius: 12 * @rem;
    overflow: hidden;
  }
  .fuli-container {
    box-sizing: border-box;
    padding: 0 0 8 * @rem;
    .welfare-bar {
      display: flex;
      justify-content: space-between;
      margin-top: 10 * @rem;
      padding: 0 14 * @rem;
      .welfare-banner {
        width: 158 * @rem;
        height: 115 * @rem;
        img {
          object-fit: contain;
        }
      }
    }
    .caifu-banner {
      width: 327 * @rem;
      height: 78 * @rem;
      margin: 10 * @rem auto 0;
    }
    .invite-banner {
      width: 327 * @rem;
      height: 86 * @rem;
      margin: 10 * @rem auto 0;
    }
  }
  .border-radius-select {
    border-radius: 12 * @rem 16 * @rem 12 * @rem 12 * @rem;
  }
  .gold-container {
    background-color: transparent;
    width: 355 * @rem;
    overflow-x: auto;
    border-radius: 0;
    margin: 15 * @rem 10 * @rem 10 * @rem;
    &::-webkit-scrollbar {
      display: none;
    }
    .select-list {
      background: #fff;
      border-radius: 10 * @rem;
      .select-item {
        width: 70 * @rem;
        box-sizing: border-box;
        position: relative;
        height: 100 * @rem;
        border-radius: 12 * @rem;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .select-icon {
          img {
            width: auto;
            height: 48 * @rem;
            display: block;
            margin: 0 auto;
          }
        }
        .select-text {
          box-sizing: border-box;
          .select-name {
            font-size: 13 * @rem;
            line-height: 22 * @rem;
            margin-top: 5 * @rem;
            font-weight: 500;
            color: #000000;
            text-align: center;
          }
        }
      }
    }
    .swiper-scrollbar {
      margin: 13 * @rem auto 0;
      width: 24 * @rem;
      height: 6 * @rem;
    }
    /deep/ .swiper-scrollbar-drag {
      background: @themeColor;
    }
  }
  .task-container {
    box-sizing: border-box;
    padding: 0 0 8 * @rem;
    .task-list {
      padding: 0 15 * @rem;
      .task-item {
        display: flex;
        align-items: center;
        height: 79 * @rem;
        &:last-child {
          .task-info {
            border-bottom: 0;
          }
        }
        .task-icon {
          width: 32 * @rem;
          height: 32 * @rem;
        }
        .task-info {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;
          border-bottom: 0.5px solid #e8e8e8;
          margin-left: 10 * @rem;
          height: 100%;
          .task-center {
            flex: 1;
            min-width: 0;
            margin-right: 20 * @rem;
            .title {
              font-size: 17 * @rem;
              font-weight: 500;
              color: #111111;
            }
            .desc {
              font-size: 12 * @rem;
              font-weight: 400;
              color: #999999;
              margin-top: 3 * @rem;
              line-height: 17 * @rem;
            }
          }
          .task-btn {
            width: 60 * @rem;
            height: 30 * @rem;
            background: @themeBg;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
    }
  }
  .zero-container {
    box-sizing: border-box;
    padding: 15 * @rem 0 15 * @rem;
    .zero-game {
      margin-top: 10 * @rem;
      .zero-list {
        display: flex;
        width: 100%;
        overflow-x: auto;
        &::-webkit-scrollbar {
          /*隐藏滚轮*/
          display: none;
        }
        .zero-item {
          box-sizing: border-box;
          padding: 0 14 * @rem;
          width: 275 * @rem;
          height: 98 * @rem;
          background: #f5f5f6;
          border-radius: 12 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 8 * @rem;
          &:nth-of-type(1) {
            margin-left: 10 * @rem;
          }
          /deep/ .game-item-components .tags .tag {
            background-color: #ebebeb;
          }
        }
      }
    }
  }
  .activity-container {
    box-sizing: border-box;
    padding: 15 * @rem 0 8 * @rem;
    .activity-list {
      width: 355 * @rem;

      margin: 10 * @rem 0;
      .activity-item {
        box-sizing: border-box;
        width: 355 * @rem;
        height: 182 * @rem;
        padding: 0 15 * @rem;
        height: auto;
        overflow: hidden;
        img {
          background: #d8d8d8;
          border-radius: 10px;
        }
      }
    }
  }

  // 碎片
  // 648暴爽
  .gift-648-container {
    .gift-list {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      padding-left: 15 * @rem;
      overflow: hidden;
      overflow-x: auto;
      margin-top: 8 * @rem;
      &::-webkit-scrollbar {
        display: none;
      }
      .gift-item {
        box-sizing: border-box;
        padding: 12 * @rem 8 * @rem 0;
        width: 114 * @rem;
        height: 160 * @rem;
        flex-shrink: 0;
        flex-grow: 0;
        position: relative;
        background: url('~@/assets/images/welfare/gift-648-item-bg.png');
        background-size: 114 * @rem 160 * @rem;
        margin-right: 12 * @rem;
        .tag {
          width: 48 * @rem;
          height: 18 * @rem;
          position: absolute;
          right: 6 * @rem;
          top: 4 * @rem;
          background: url(~@/assets/images/welfare/remain-648-bg.png);
          background-size: 48 * @rem 18 * @rem;
          font-size: 10 * @rem;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
        }
        .game-icon {
          width: 60 * @rem;
          height: 60 * @rem;
          margin: 0 auto;
        }
        .game-title {
          font-size: 12 * @rem;
          color: #000000;
          line-height: 15 * @rem;
          margin-top: 5 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: center;
        }
        .game-cate {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #999999;
          line-height: 15 * @rem;
          margin-top: 3 * @rem;
          text-align: center;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .get-btn {
          width: 58 * @rem;
          height: 28 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 20 * @rem;
          background: @themeBg;
          font-size: 13 * @rem;
          color: #ffffff;
          margin: 10 * @rem auto 0;
        }
      }
    }
  }

  .sign-container {
    .game-bar {
      display: flex;
      align-items: center;
      height: 60 * @rem;
      margin-top: 8 * @rem;
      padding: 0 6 * @rem;
      .game-pic {
        box-sizing: border-box;
        width: 48 * @rem;
        height: 48 * @rem;
        border-radius: 12 * @rem;
        overflow: hidden;
        transition: 0.2s;
        margin: 0 9 * @rem;
        &.current {
          width: 60 * @rem;
          height: 60 * @rem;
          border: 3 * @rem solid #ff5c39;
        }
      }
    }
    .game-info {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 329 * @rem;
      height: 91 * @rem;
      background: url(~@/assets/images/welfare/sign-item-bg.png) no-repeat;
      background-size: 329 * @rem 91 * @rem;
      margin: 15 * @rem auto 0;
      flex: 1;
      min-width: 0;
      padding: 0 12 * @rem;
      .left-content {
        flex: 1;
        min-width: 0;
        .title-line {
          display: flex;
          align-items: center;
          width: 100%;
          .game-title {
            font-size: 15 * @rem;
            font-weight: 500;
            color: #3c506f;
            line-height: 18 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .tag {
            box-sizing: border-box;
            height: 20 * @rem;
            padding: 0 9 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            color: #95a2c2;
            border-radius: 4 * @rem;
            box-shadow: inset 0px -2px 4px 0px rgba(255, 241, 241, 0.2);
            background: #ffffff;
            border: 1px solid #b7c6eb;
            margin-left: 8 * @rem;
            flex-shrink: 0;
          }
        }
        .desc {
          font-size: 12 * @rem;
          color: #493c6f;
          line-height: 17 * @rem;
          margin-top: 14 * @rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .sign-btn {
        width: 58 * @rem;
        height: 28 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6 * @rem;
        font-size: 13 * @rem;
        color: #fff;
        background: @themeBg;
        margin-left: 20 * @rem;
      }
    }
  }
}
.az-sign-dislog {
  background-color: #fff;
  width: 278 * @rem;
  border-radius: 16 * @rem;
  padding-top: 21 * @rem;
  .top-icon {
    width: 88 * @rem;
    height: 68 * @rem;
    .image-bg('~@/assets/images/clock-in/clock-in-dialog-top.png');
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -30 * @rem;
  }
  .az-sign-title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: bold;
    line-height: 25 * @rem;
    text-align: center;
  }
  .az-sign-tip {
    margin-top: 11 * @rem;
    .line {
      font-size: 15 * @rem;
      color: #000000;
      text-align: center;
      line-height: 21 * @rem;
    }
  }
  .no-notice {
    text-align: center;
    color: #b1b1b1;
    font-size: 12 * @rem;
    margin-top: 3 * @rem;
    padding: 10 * @rem 0 10 * @rem;
    line-height: 17 * @rem;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 17 * @rem;
      .gou {
        width: 8 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/select-no.png) center center no-repeat;
        background-size: 8 * @rem 8 * @rem;
        margin-right: 4 * @rem;
      }
    }
    &.remember {
      .content {
        .gou {
          background-image: url(~@/assets/images/select-yes.png);
        }
      }
    }
  }
  .operation {
    display: flex;
    align-items: center;
    padding: 0 22 * @rem;
    justify-content: space-between;
    margin: 2 * @rem auto 20 * @rem;
    .clock-btn {
      width: 110 * @rem;
      height: 34 * @rem;
      background: #f2f2f2;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 17 * @rem;
      font-size: 13 * @rem;
      color: #7d7d7d;
      &.svip {
        background: @themeBg;
        color: #fff;
      }
    }
  }
}
.savings-countdown-popup {
  width: 309 * @rem;
  background: transparent;
  overflow: hidden;
  .popup-close {
    position: absolute;
    right: 7 * @rem;
    top: 0 * @rem;
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/recharge/savings-card/savings-popup-close.png)
      center center no-repeat;
    background-size: 28 * @rem 28 * @rem;
  }
  .content {
    box-sizing: border-box;
    padding-top: 166 * @rem;
    margin-top: 40 * @rem;
    width: 309 * @rem;
    height: 316 * @rem;
    background-size: 309 * @rem auto;
    background-position: center center;
    background-repeat: no-repeat;

    .countdown-clock {
      display: flex;
      margin-left: 112 * @rem;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #ffffff;
      height: 30 * @rem;
      line-height: 30 * @rem;
      span {
        box-sizing: border-box;
        display: block;
        width: 24 * @rem;
        height: 30 * @rem;
        background: linear-gradient(
          180deg,
          #ffe6eb 0%,
          #ffffff 74%,
          #ffe6eb 100%
        );
        border-radius: 5 * @rem;
        line-height: 30 * @rem;
        text-align: center;
        font-size: 15 * @rem;
        color: #ff1b3f;
        font-weight: 600;
        margin: 0 2 * @rem;
      }
    }
    .remember-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 27 * @rem auto 0;
      height: 15 * @rem;
      .gou {
        width: 14 * @rem;
        height: 14 * @rem;
        background: url(~@/assets/images/recharge/savings-card/welfare-savings-gou-no.png)
          center center no-repeat;
        background-size: 14 * @rem 14 * @rem;
        &.remember {
          background-image: url(~@/assets/images/recharge/savings-card/welfare-savings-gou.png);
        }
      }
      .tips-text {
        font-size: 12 * @rem;
        color: #9a1a0d;
        margin-left: 5 * @rem;
      }
    }
  }
  .popup-btn {
    width: 221 * @rem;
    height: 68 * @rem;
    background: url(~@/assets/images/recharge/savings-card/welfare-savings-get.png)
      center top no-repeat;
    background-size: 221 * @rem 68 * @rem;
    margin: -35 * @rem auto 0;
  }
}
</style>
