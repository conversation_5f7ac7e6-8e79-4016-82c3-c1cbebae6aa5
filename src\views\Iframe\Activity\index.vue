<template>
  <div class="iframe">
    <iframe
      id="activity-iframe"
      :src="url"
      frameborder="0"
      allow="clipboard-read; clipboard-write"
    ></iframe>
  </div>
</template>

<script>
import BASEPARAMS from '@/utils/baseParams.js';
import { mapMutations } from 'vuex';
import { BOX_openInBrowser } from '@/utils/box.uni.js';
export default {
  name: 'Activity',
  data() {
    return {
      url: '',
    };
  },
  created() {
    this.$toast.loading({
      message: this.$t('加载中...'),
    });
    // 获取活动页是否存在url链接 拿到iframe的url链接
    // const storedUrl = sessionStorage.getItem('FROM_ACTIVITY_SESSION_URL');
    // this.url = storedUrl || this.$route.params.url;
    this.url = this.$route.params.url;
  },
  mounted() {
    // sessionStorage.removeItem('FROM_ACTIVITY_SESSION_URL');
    let iframe = document.querySelector('#activity-iframe');
    iframe.onload = () => {
      this.$toast.clear();
    };
    window.addEventListener('message', this.handleMessage, false);
    this.postMessage();
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage, false);
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
    handleMessage(e) {
      let { data, type, from } = e.data;
      if (from === 'activity') {
        switch (type) {
          case 'login':
            this.$router.push({ name: 'PhoneLogin' });
            break;
          case 'goToGame':
            this.$router.push({
              name: 'GameDetail',
              params: { id: data.id },
            });
            break;
          case 'goToGameCoupon':
            this.$router.push({
              name: 'GameCoupon',
              params: { game_id: data.game_id },
            });
            break;
          case 'goToGameFanli':
            this.setGameInfo(data.game_info);
            this.$router.push({
              name: 'GameNews',
              params: { game_id: data.game_id },
            });
            break;
          case 'goToFanliDetails':
            this.$router.push({
              name: 'Iframe',
              params: { url: data.url, title: data.title },
            });
            break;
          case 'openInBrowser':
            BOX_openInBrowser({ h5_url: data.url, open_type: data.open_type });
            break;
          case 'close':
            this.$router.go(-1);
            break;
          case 'memAuth':
            this.$router.push({
              name: 'IdCard',
            });
            break;
          case 'openInNewWindow':
            let temp = {};
            temp.name = data.name;
            if (data.params) {
              temp.params = data.params;
            }
            this.$router.push(temp);
            break;

          case 'showActivity':
            console.log('接收showActivity', data);
            this.$router.push({
              name: data.name,
              params: data.params,
            });
            break;
          case 'copy':
            this.$copyText(data.text).then(
              async res => {
                // successMsg 默认值: 链接已复制到剪贴板，快去邀请好友吧~
                this.$toast(data.successMsg);
              },
              err => {
                this.$dialog.alert({
                  message: '复制失败',
                  lockScroll: false,
                });
              },
            );
            break;
          // case 'selectUrl':
          //   sessionStorage.setItem('FROM_ACTIVITY_SESSION_URL', data.url);
          //   break;
          default:
            break;
        }
      }
    },
    postMessage() {
      let data = {
        from: 'webapp',
        type: 'init',
        data: {
          f: BASEPARAMS.from,
          t: this.userInfo.token ? this.userInfo.token : '',
          u: BASEPARAMS.uuid,
        },
      };
      let iframe = document.getElementById('activity-iframe');
      iframe.onload = function () {
        iframe.contentWindow.postMessage(data, '*');
      };
    },
  },
};
</script>

<style lang="less" scoped>
.iframe {
  height: 100vh;
  overflow: hidden;
  &::-webkit-scrollbar {
    display: none !important;
  }
  iframe {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    &::-webkit-scrollbar {
      display: none !important;
    }
  }
}
</style>
