<template>
  <div class="game-page">
    <div class="placeholder"></div>

    <van-loading class="loading-box" v-if="!loadSuccess" size="24* @rem">{{
      $t('加载中...')
    }}</van-loading>
    <!-- @touchmove.self="" -->
    <div class="now-hot-container" v-if="nowHotGames.length && loadSuccess">
      <div class="now-hot-title">{{ nowHotTitle }}</div>
      <div class="now-hot-list">
        <div
          class="now-hot-item"
          v-for="(item, index) in nowHotGames"
          :key="index"
          @click="goToGame(item)"
        >
          <div class="game-icon">
            <img :src="item.titlepic" alt="" />
          </div>
          <div class="game-title">{{ item.title }}</div>
          <div class="hot">{{ item.totaldown }}热度</div>
          <div class="tags">
            <div class="tag first" v-if="discountTag"
              >{{ discountTag(item) }}折</div
            >
            <div class="tag" v-if="item.is_libao">游戏礼包</div>
          </div>
        </div>
      </div>
    </div>
    <div class="new-game-filter" v-if="loadSuccess">
      <van-sticky :offset-top="stickyOffsetTop">
        <div class="nav-container">
          <div class="nav-list" v-if="tabList.length">
            <div
              class="nav-item"
              v-for="(nav, index) in tabList"
              :key="index"
              :class="{ active: tabIndex == nav.id }"
              @click="navTabClick(nav.id)"
            >
              <div class="nav-title">
                {{ nav.txt }}
              </div>
            </div>
          </div>
        </div>
      </van-sticky>
      <yy-list
        class="game-container"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="onLoadMore"
        :empty="empty"
        :check="true"
      >
        <div class="new-game-list">
          <div class="list-item" v-for="(item, index) in gameList" :key="index">
            <div class="title">
              {{ formatDate(item[0].newstime) }}
              <span>{{ formatDate1(item[0].newstime) }}</span>
            </div>
            <div class="list">
              <div
                class="game-item"
                v-for="(game, index) in item"
                :key="game.id + '' + index"
              >
                <game-item4 :gameInfo="game" :iconSize="72"></game-item4>
                <yy-download-btn :gameInfo="game"></yy-download-btn>
              </div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiCwbIndexGameScreenList } from '@/api/views/home.js';
import { ApiV2024GameNewGame } from '@/api/views/game.js';
export default {
  data() {
    const that = this;
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
      gameList: [],
      tabIndex: 1,
      tabList: [],
      popupReloadFinished: false,
      nowTime: 0,
      firstTime: 0,

      stickyOffsetTop: '0px', //顶部导航栏的高度

      nowHotGames: [],
      nowHotTitle: '',

      loadSuccess: false,
    };
  },
  async created() {
    this.loadSuccess = false;
    Promise.all([this.getGameScreenList(), this.getNowHotGames()])
      .then(() => {
        this.loadSuccess = true;
      })
      .catch(err => {
        this.loadSuccess = true;
      });
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.game-page .placeholder').offsetHeight - 2 + 'px';
  },
  methods: {
    async getGameScreenList(action = 1, isDown = true) {
      //isDown true上滑 false下拉
      if (action == 1) {
        this.nowTime = 0;
        this.firstTime = 0;
        this.finished = false;
      }
      if (this.finished) {
        return;
      }
      let params = {
        type: this.tabIndex,
        list_rows: this.listRows,
        now_time: this.nowTime,
        first_time: this.firstTime,
        new_game: 1,
      };
      let res = await ApiCwbIndexGameScreenList(params);
      if (action == 1) {
        this.gameList = [];
      }
      this.tabList = res.data.types;

      if (res.data.list.length == 0) {
        this.finished = true;
      } else {
        let formatArr = this.classify(res.data.list, 'newstime', isDown);
        if (action != 1 && res.data.list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
        this.gameList.push(...formatArr);
      }
      if (this.gameList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      this.loadingObj.loading = false;
      this.loadingObj.reloading = false;
    },
    async onRefresh() {
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getGameScreenList();
      this.loadingObj.loading = false;
    },
    async onLoadMore() {
      let list = this.gameList[this.gameList.length - 1];
      this.nowTime = list[list.length - 1].newstime;
      this.firstTime = 0;
      await this.getGameScreenList(2, true);
      this.loadingObj.loading = false;
    },
    async navTabClick(id) {
      if (this.loadingObj.loading || this.loadingObj.reloading) return;
      if (this.tabIndex === id) return;
      this.tabIndex = id;
      this.gameList = [];
      this.loadingObj.loading = true;
      await this.getGameScreenList();
      this.loadingObj.loading = false;
    },
    formatDate(timestamp) {
      let date = new Date(this.getDayZeroTimestamp(timestamp));
      let isYear = date.getFullYear() != new Date().getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      switch (this.tabIndex) {
        case 1:
          if (this.isToday(timestamp)) {
            return '今天首发';
          } else if (this.isYesterday(timestamp)) {
            return '昨天首发';
          }
          break;
        case 2:
          if (this.isTomorrow(timestamp)) {
            return '明日';
          }
          break;
        case 3:
          if (this.isToday(timestamp)) {
            return '今日';
          } else if (this.isYesterday(timestamp)) {
            return '昨日';
          } else if (this.isTomorrow(timestamp)) {
            return '明日';
          }
          break;
      }
      return isYear
        ? `${date.getFullYear()}年${month}月${day}日`
        : `${month}月${day}日`;
    },
    formatDate1(timestamp) {
      let date = new Date(this.getDayZeroTimestamp(timestamp));
      let isYear = date.getFullYear() != new Date().getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let str = '';
      switch (this.tabIndex) {
        case 1:
          if (this.isToday(timestamp)) {
            return `${month}月${day}日`;
          } else if (this.isYesterday(timestamp)) {
            return `${month}月${day}日`;
          }
          break;
        case 2:
          if (this.isTomorrow(timestamp)) {
            str = `${month}月${day}日`;
          }
          break;
        case 3:
          if (
            this.isToday(timestamp) ||
            this.isYesterday(timestamp) ||
            this.isTomorrow(timestamp)
          ) {
            str = `${month}月${day}日`;
          }
          break;
        default:
          break;
      }
      return str ? str + ' ' + this.week(timestamp) : this.week(timestamp);
    },
    formatTime(timestamp) {
      let date = new Date(timestamp * 1000);
      let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      return `${hour}:${minute}`;
    },
    classify(arr, key, isDown) {
      let obj = {};
      arr.forEach(e => {
        if (typeof obj[this.getDayZeroTimestamp(e[key])] == 'undefined') {
          obj[this.getDayZeroTimestamp(e[key])] = [];
        }
        if (isDown) {
          if (
            this.gameList.length &&
            this.getDayZeroTimestamp(
              this.gameList[this.gameList.length - 1][0][key],
            ) == this.getDayZeroTimestamp(e[key])
          ) {
            // 如果该项的时间和gameList最后一项的时间一样，则push到gameList的最后一项
            this.gameList[this.gameList.length - 1].push(e);
            delete obj[this.getDayZeroTimestamp(e[key])];
          } else {
            obj[this.getDayZeroTimestamp(e[key])].push(e);
          }
        } else {
          if (
            this.gameList.length &&
            this.getDayZeroTimestamp(this.gameList[0][0][key]) ==
              this.getDayZeroTimestamp(e[key])
          ) {
            // 如果该项的时间和gameList最后一项的时间一样，则push到gameList的最后一项
            this.gameList[0].unshift(e);
            delete obj[this.getDayZeroTimestamp(e[key])];
          } else {
            obj[this.getDayZeroTimestamp(e[key])].unshift(e);
          }
        }
      });

      return Object.values(obj);
    },
    // 获取当天0点的时间戳
    getDayZeroTimestamp(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return new Date(new Date(timestamp).toLocaleDateString()).getTime();
    },
    isToday(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date().toLocaleDateString()
      );
    },
    isYesterday(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date(
          new Date().getTime() - 24 * 60 * 60 * 1000,
        ).toLocaleDateString()
      );
    },
    isTomorrow(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date(
          new Date().getTime() + 24 * 60 * 60 * 1000,
        ).toLocaleDateString()
      );
    },
    week(timestamp) {
      let day = new Date(Number(timestamp) * 1000).getDay();
      switch (day) {
        case 0:
          return '星期日';
          break;
        case 1:
          return '星期一';
          break;
        case 2:
          return '星期二';
          break;
        case 3:
          return '星期三';
          break;
        case 4:
          return '星期四';
          break;
        case 5:
          return '星期五';
          break;
        case 6:
          return '星期六';
          break;
      }
    },
    async getNowHotGames() {
      const res = await ApiV2024GameNewGame();
      this.nowHotGames = res.data.list;
      this.nowHotTitle = res.data.title;
    },

    // 是否显示折扣tag
    discountTag(gameInfo) {
      if (gameInfo.classid != 107) {
        return '';
      }
      if (gameInfo.f_pay_rebate && gameInfo.f_pay_rebate != 100) {
        return `${parseFloat(gameInfo.f_pay_rebate) / 10}`;
      } else if (gameInfo.pay_rebate && gameInfo.pay_rebate != 100) {
        return `${parseFloat(gameInfo.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
    goToGame(item) {
      this.toPage('GameDetail', {
        id: item.id,
        gameInfo: item,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.game-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  min-height: 80vh;
  .placeholder {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(100 * @rem + @safeAreaTop);
    height: calc(100 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .now-hot-container {
    padding: 10 * @rem 0 10 * @rem;
    .now-hot-title {
      font-size: 18 * @rem;
      color: #111111;
      font-weight: bold;
      padding: 0 18 * @rem;
    }
    .now-hot-list {
      // flex横向排列，超出可滚动
      padding: 16 * @rem 10 * @rem 0;
      margin-top: 13 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      .now-hot-item {
        border-radius: 10 * @rem;
        flex-shrink: 0;
        width: 108 * @rem;
        height: 132 * @rem;
        padding-top: 1 * @rem;
        margin: 0 4 * @rem;
        background: linear-gradient(
          180deg,
          #ffe2d8 0%,
          rgba(255, 226, 216, 0) 80%
        );
        border-radius: 16 * @rem;
        &:nth-of-type(n + 4) {
          margin-top: 20 * @rem;
        }
        &:nth-of-type(even) {
          background: linear-gradient(
            180deg,
            #ffead0 0%,
            rgba(255, 234, 208, 0) 80%
          );
        }
        .game-icon {
          width: 64 * @rem;
          height: 64 * @rem;
          margin: -16 * @rem auto 0;
          img {
            width: 100%;
            height: 100%;
            border-radius: 14 * @rem;
            background: #eeeeee;
          }
        }
        .game-title {
          font-size: 13 * @rem;
          color: #222222;
          margin-top: 12 * @rem;
          text-align: center;
          line-height: 16 * @rem;
          font-weight: bold;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          padding: 0 10 * @rem;
        }
        .hot {
          height: 13 * @rem;
          padding-left: 11 * @rem;
          margin-top: 8 * @rem;
          background: url('~@/assets/images/games/fire-hot.png') no-repeat left
            center;
          background-size: 8 * @rem 8 * @rem;
          font-size: 10 * @rem;
          color: #444444;
          margin: 0 auto;
          width: fit-content;
          margin-top: 6 * @rem;
          line-height: 14 * @rem;
        }
        .tags {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          margin-top: 6 * @rem;
          height: 18 * @rem;
          overflow: hidden;

          .tag {
            height: 18 * @rem;
            border-radius: 4 * @rem;
            background: #f9f9f9;
            padding: 0 4 * @rem;
            font-size: 10 * @rem;
            margin: 0 3 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #979797;
            &.first {
              background: #fff5ed;
              color: #ff6649;
            }
          }
        }
      }
    }
  }

  .game-list {
    flex: 1;
    overflow-y: scroll;
    padding: 0 18 * @rem;
  }

  .filter-container {
    box-sizing: border-box;
    height: 500 * @rem;

    /deep/ .van-icon {
      position: absolute;
    }
    /deep/ .van-popup__close-icon::before {
      font-size: 14px;
    }
  }
  .new-game-filter {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;

    .popup-name {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 23 * @rem 0;
      span {
        position: relative;
        height: 25 * @rem;
        font-weight: bold;
        font-size: 20 * @rem;
        color: #333333;
        line-height: 25 * @rem;
        text-align: center;
        &::before {
          content: '';
          display: block;
          width: 18 * @rem;
          height: 18 * @rem;
          border-radius: 50%;
          background-color: rgba(33, 185, 138, 0.23);
          position: absolute;
          top: 50%;
          left: -7 * @rem;
          transform: translateY(-50%);
        }
      }
    }
    .nav-container {
      width: 100%;
      height: 32 * @rem;
      background: #fff;
      padding: 8 * @rem 0;
    }
    .nav-list {
      margin: 0 18 * @rem;
      display: flex;
      justify-content: space-between;
      width: max-content;
      height: 32 * @rem;
      background-color: #f9fafb;
      border-radius: 16 * @rem;

      .nav-item {
        height: 32 * @rem;
        padding: 0 14 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #333333;
        line-height: 32 * @rem;
        text-align: center;
        border-radius: 16 * @rem;
        .nav-title {
          font-weight: 500;
        }

        &.active {
          background-color: @themeColor;
          color: #fff;
        }
      }
    }
    .game-container {
      flex: 1;
      min-height: 0;
      padding-right: 5 * @rem;
      margin-right: -10 * @rem;
      .new-game-list {
        padding: 0 18 * @rem 0;
      }
      .list-item {
        .title {
          display: flex;
          align-items: center;
          padding: 10 * @rem 0;
          height: 20 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #333333;
          line-height: 20 * @rem;

          &::before {
            content: '';
            display: block;
            width: 4 * @rem;
            height: 10 * @rem;
            border-radius: 16 * @rem;
            background-color: @themeColor;
            margin-right: 6 * @rem;
          }

          span {
            display: block;
            height: 18 * @rem;
            line-height: 18 * @rem;
            padding: 0 6 * @rem;
            background: rgba(231, 231, 231, 0.3);
            border-radius: 2 * @rem;
            font-size: 11 * @rem;
            color: #333333;
            margin-left: 5 * @rem;
          }
        }

        .game-item {
          display: flex;
          align-items: center;
          margin-bottom: 20 * @rem;
          /deep/ .game-item-components {
            padding: 0;
          }

          .server-info {
            margin-right: 8 * @rem;
            font-size: 11px;
            line-height: 14px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
