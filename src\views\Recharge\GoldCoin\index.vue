<template>
  <div class="page gold-coin-page">
    <nav-bar-2
      :border="true"
      title="任务大厅"
      :azShow="true"
      :placeholder="true"
    >
      <!-- <template #right>
        <div
          class="coin-tips"
          :class="{ black: bgStyle == 'transparent' }"
          @click="toPage('GoldCoinTips')"
        >
          金币小贴士
        </div>
      </template> -->
    </nav-bar-2>
    <div class="main">
      <!-- <div class="top-bg">
        <img src="@/assets/images/recharge/gold-coin-top-bg.png" alt="" />
        <div class="gold-bar">
          <user-avatar class="avatar"></user-avatar>
          <div class="gold-info">
            <div class="title">当前金币</div>
            <div class="gold-balance">
              <div class="gold-num">{{ userInfo.gold }}</div>
              <div class="gold-detail" @click="toPage('GoldCoinDetail')">
                明细
              </div>
            </div>
          </div>
          <div class="gold-mall" @click="goToGoldCoinExchange">
            <div class="title">金币商城</div>
            <div class="icon"></div>
          </div>
        </div>
      </div> -->

      <!-- 每日任务 -->
      <div class="task-container" v-if="taskList1 && Object.keys(taskList1).length">
        <div class="task-title">
          <img src="@/assets/images/recharge/task-title-1.png" alt="" />
        </div>
        <div class="task-list">
          <div class="task-item" v-for="(item, key) in taskList1" :key="key">
            <div class="icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="task-info">
              <div class="title">{{ item.title }}</div>
              <div class="line">
                完成得
                <div v-if="item.gold_num">
                  <span>金币</span>
                  <span class="reward-info">+{{ item.gold_num }}</span>
                  <span v-if="item.coupon || item.exp_num">、</span>
                </div>
                <div v-if="item.coupon">
                  <span class="reward-info">{{ item.coupon }}</span>
                  <span v-if="item.exp_num">、</span>
                </div>
                <div v-if="item.exp_num">
                  <span>经验</span>
                  <span class="reward-info">+{{ item.exp_num }}</span>
                </div>
                <!-- <div class="gold" v-if="item.gold">{{ item.gold }}</div>
                <div class="coupon" v-if="item.coupon">{{ item.coupon }}</div>
                <div class="exp" v-if="item.exp">{{ item.exp }}</div> -->
              </div>
              <!-- <div class="extra" v-if="item.extra_exp">
                会员加成：{{ item.extra_exp }}
              </div> -->
            </div>
            <div
              class="task-btn btn"
              v-if="item.is_finish == 0"
              @click.stop="handleGo1(item, key)"
            >
              {{ $t('去完成') }}
            </div>
            <div
              class="task-btn btn get"
              v-if="item.is_finish == 1"
              @click.stop="handleGet1(item, key)"
            >
              {{ $t('领取') }}
            </div>

            <div class="task-btn btn had" v-else-if="item.is_finish == 2">
              {{ $t('已领取') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 成就任务 -->
      <div class="task-container" v-if="taskList2 && Object.keys(taskList2).length">
        <div class="task-title">
          <img src="@/assets/images/recharge/task-title-2.png" alt="" />
        </div>
        <div class="task-list">
          <!-- 签到 -->
          <template v-if="taskList2.sign">
            <div class="task-item">
              <div class="icon">
                <img :src="taskList2.sign.mission_info.icon" alt="" />
              </div>
              <div class="task-info">
                <div class="title">
                  累计签到 {{ taskList2.sign.mission_info.need_value }}次 ({{
                    taskList2.sign.total
                  }}/{{ taskList2.sign.mission_info.need_value }})
                </div>
                <!-- <div class="subtitle">
                  ({{ taskList2.sign.total }}/{{
                    taskList2.sign.mission_info.need_value
                  }})
                </div> -->
                <div class="line">
                  <!-- <div class="gold" v-if="taskList2.sign.mission_info.reward">
                    {{ taskList2.sign.mission_info.reward }}金币
                  </div> -->
                  <div v-if="taskList2.sign.mission_info.reward">
                    完成得<span>金币</span>
                    <span class="reward-info"
                      >+{{ taskList2.sign.mission_info.reward }}</span
                    >
                  </div>
                </div>
              </div>

              <template v-if="!Boolean(taskList2.sign.mission_info.all_finish)">
                <div
                  class="task-btn btn get"
                  v-if="
                    taskList2.sign.total >=
                    taskList2.sign.mission_info.need_value
                  "
                  @click="handleGet2(taskList2.sign.mission_info, 'sign')"
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="task-btn btn"
                  v-else
                  @click.stop="handleGo2(taskList2.sign, 'sign')"
                >
                  {{ $t('去完成') }}
                </div>
              </template>
              <div class="task-btn btn had" v-else>{{ $t('已领取') }}</div>
            </div>
          </template>
          <!-- 充值 -->
          <template v-if="taskList2.pay">
            <template v-for="(item, index) in taskList2.pay.mission_info">
              <div
                class="task-item"
                :key="index"
                v-if="!(index > 1 && !isExpand)"
              >
                <div class="icon">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="task-info">
                  <div class="title">
                    {{ item.name }} ({{ taskList2.pay.total }}/{{
                      item.need_value
                    }})
                  </div>
                  <!-- <div class="subtitle">
                    ({{ taskList2.pay.total }}/{{ item.need_value }})
                  </div> -->
                  <div class="line">
                    <!-- <div class="gold" v-if="item.reward">
                      {{ item.reward }}金币
                    </div> -->
                    <div v-if="item.reward">
                      完成得<span>金币</span>
                      <span class="reward-info">+{{ item.reward }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="task-btn btn get"
                  v-if="item.is_finish == 1"
                  @click="handleGet2(item, 'pay')"
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="task-btn btn"
                  v-else
                  @click.stop="handleGo2(item, 'pay')"
                >
                  {{ $t('去完成') }}
                </div>
              </div>
            </template>
          </template>
          <template v-if="taskList2.pay.mission_info.length + 1 > 3">
            <div
              class="exchange-down"
              v-if="!isExpand"
              @click="isExpand = !isExpand"
            >
              更多任务<i></i>
            </div>
            <div class="exchange-down up" v-else @click="isExpand = !isExpand">
              收起<i></i>
            </div>
          </template>
        </div>
      </div>

      <!-- 新手任务 -->
      <div class="task-container" v-if="taskList3 && Object.keys(taskList3).length">
        <div class="task-title">
          <img src="@/assets/images/recharge/task-title-3.png" alt="" />
        </div>
        <div class="task-list">
          <div class="task-item" v-for="(item, key) in taskList3" :key="key">
            <div class="icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="task-info">
              <div class="title">{{ item.title }}</div>
              <div class="line">
                完成得
                <div v-if="item.gold_num">
                  <span>金币</span>
                  <span class="reward-info">+{{ item.gold_num }}</span>
                  <span v-if="item.coupon || item.exp_num">、</span>
                </div>
                <div v-if="item.coupon">
                  <span class="reward-info">{{ item.coupon }}</span>
                  <span v-if="item.exp_num">、</span>
                </div>
                <div v-if="item.exp_num">
                  <span>经验</span>
                  <span class="reward-info">+{{ item.exp_num }}</span>
                </div>
                <!-- <div class="gold" v-if="item.gold">{{ item.gold }}</div>
                <div class="coupon" v-if="item.coupon">{{ item.coupon }}</div>
                <div class="exp" v-if="item.exp">{{ item.exp }}</div> -->
              </div>
              <!-- <div class="extra" v-if="item.extra_exp">
                会员加成：{{ item.extra_exp }}
              </div> -->
            </div>
            <div
              class="task-btn btn"
              v-if="item.is_finish == 0"
              @click.stop="handleGo3(item, key)"
            >
              {{ $t('去完成') }}
            </div>
            <div
              class="task-btn btn get"
              v-if="item.is_finish == 1"
              @click.stop="handleGet3(item, key)"
            >
              {{ $t('领取') }}
            </div>

            <div class="task-btn btn had" v-else-if="item.is_finish == 2">
              {{ $t('已领取') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      :tip="popupTip"
      :confirmText="popupConfirmText"
      @confirm="popupConfirm"
    ></task-popup>
  </div>
</template>
<script>
import {
  ApiMissionGetDaily,
  ApiMissionGetAchievement,
  ApiMissionGetAchievementReward,
  ApiMissionGetNewcomer,
  ApiMissionGetMissionReward,
  ApiMissionCheckToComplete,
} from '@/api/views/mission.js';
import { mapActions } from 'vuex';
import {
  platform,
  BOX_showActivity,
  BOX_openInBrowser,
  BOX_showActivityByAction,
  BOX_openInNewWindow,
  BOX_goToGame,
} from '@/utils/box.uni.js';
import { navigateToGameDetail } from '@/utils/function';
import h5Page from '@/utils/h5Page';
import TaskPopup from '@/components/task-popup';
import useCollectToast from '@/components/yy-collect-toast/index.js';
export default {
  name: 'GoldCoin',
  components: {
    TaskPopup,
  },
  data() {
    return {
      taskPopup: false, // 任务弹窗是否显示
      popupContent: '', // 任务弹窗内容
      popupTip: '', // 任务弹窗提示
      popupConfirmText: '', // 任务弹窗确认按钮文案
      isSvip: false,
      taskList1: null,
      taskList2: null,
      taskList3: null,
      ptbUrl: '',
      vipUrl: '',
      isLoading: false,

      bgStyle: 'transparent',
      navbarOpacity: 1,

      isExpand: false,
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = '任务大厅';
    }
    // window.addEventListener('scroll', this.handleScroll);
    await this.getDailyData();
    await this.getAchievementData();
    await this.getNewData();
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  beforeDestroy() {
    // window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async onResume() {
      this.SET_USER_INFO(true);
      await this.getDailyData();
      await this.getAchievementData();
      await this.getNewData();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    async getDailyData() {
      const res = await ApiMissionGetDaily();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList1 = list;
    },
    async getAchievementData() {
      const res = await ApiMissionGetAchievement();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList2 = list;
    },
    async getNewData() {
      const res = await ApiMissionGetNewcomer();
      let { isSvip, list, vipUrl } = res.data;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList3 = list;
    },

    handleGo1(item, key) {
      switch (key) {
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'pay':
          this.popupContent =
            item.task_prompt || '每日充值一次平台币即可完成任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'pay100':
        case 'pay500':
          this.popupContent =
            item.task_prompt ||
            `每日充值平台币满${item.need_value}元即可完成任务哦~`;
          this.popupTip = '注：每日累计充值时间为0点至24点';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'play_time':
          this.popupContent = '成功登录并畅玩任意游戏30分钟即可完成当前任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '我知道了';
          this.taskPopup = true;
          break;
        default:
          break;
      }
    },
    handleGo2(item, key) {
      switch (key) {
        case 'pay':
          this.popupContent = `累计充值平台币满${item.need_value}元即可完成任务`;
          this.popupConfirmText = this.$t('充值平台币');
          this.taskPopup = true;
          break;
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        default:
          break;
      }
    },
    handleGo3(item, key) {
      switch (key) {
        case 'band_email': // 绑定邮箱
          BOX_showActivity(
            { name: 'ChangeEmail' },
            { page: 'com.a3733.cwbgamebox.ui.mine.BindEmailActivity' },
          );
          break;
        case 'add_assistant':
          BOX_openInNewWindow(
            { name: 'AddAssistant' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/add_assistant` },
          );
          break;
        case 'bind_wx': // 绑定微信
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'follow_gzh': // 关注公众号
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'down_game': // 下载游戏
          BOX_showActivity({ name: 'Category' }, { page: 'qbyx' });
          break;
        case 'first_pay': // 首充
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满6元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'mem_info': // 实名认证
        case 'mobile': // 绑定手机
          let set_temp =
            platform == 'android'
              ? 'com.a3733.gamebox.ui.etc.AccountSafeActivity'
              : 'YYAccountAndSecurityViewController';
          BOX_showActivity({ name: 'UserInfo' }, { page: set_temp });
          break;
        case 'pay100': // 充值100元
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满100元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'bookmark': //IOS商店版添加IOS书签版
          if (item.is_diff_users) {
            this.$toast('该设备已完成过任务');
          } else {
            ApiMissionCheckToComplete().then(res => {
              BOX_openInBrowser(
                {
                  h5_url: `https://${
                    this.$h5Page.env
                  }game.3733.com${window.location.search.substring(0)}`,
                },
                {},
              );
            });
          }
          break;
        case 'down_zasq': //下载追爱神器
          if (platform == 'android') {
            BOX_goToGame(
              {
                params: {
                  id: item.jump_game_id,
                },
              },
              { id: item.jump_game_id },
            );
            return false;
          }
          navigateToGameDetail({ id: item.jump_game_id });
          break;
      }
    },

    async handleGet1(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getDailyData();
      this.SET_USER_INFO();
    },
    async handleGet2(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetAchievementReward({
        mission: key,
        mission_level: item.mission_level,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getAchievementData();
      this.SET_USER_INFO();
    },
    async handleGet3(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getNewData();
      this.SET_USER_INFO();
    },
    formatRewards(item) {
      let rewards = [];
      if (item?.gold_num) {
        rewards.push('金币+' + item.gold_num);
      }
      if (item?.reward) {
        rewards.push('金币+' + item.reward);
      }
      if (item.coupon) {
        rewards.push(item.coupon);
      }
      if (item.exp_num) {
        rewards.push('经验+' + item.exp_num);
      }
      return rewards.join('、');
    },
    popupConfirm() {
      this.taskPopup = false;
      if (this.popupConfirmText == this.$t('我知道了')) {
        return false;
      }
      BOX_openInNewWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },

    goToGoldCoin() {
      BOX_openInNewWindow({ name: 'TaskDaily' }, { url: h5Page.meirirenwu });
    },
    goToGoldCoinExchange() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },

    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
};
</script>

<style lang="less" scoped>
.gold-coin-page {
  background: #f5f5f6;

  .coin-tips {
    color: #fff;
    font-size: 14 * @rem;

    &.black {
      color: #000000;
    }
  }

  .main {
    padding: 0 0 30 * @rem;
    background: #f5f5f6;
    flex: 1;

    .top-bg {
      width: 100%;
      height: 227 * @rem;

      .gold-bar {
        box-sizing: border-box;
        position: absolute;
        top: 107 * @rem;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        max-width: 375 * @rem;
        height: 48 * @rem;
        display: flex;
        align-items: center;
        padding: 0 20 * @rem;

        .avatar {
          width: 48 * @rem;
          height: 48 * @rem;
          border: 1 * @rem solid rgba(0, 0, 0, 0.1);
        }

        .gold-info {
          flex: 1;
          min-width: 0;
          margin-left: 8 * @rem;

          .title {
            font-size: 12 * @rem;
            color: #ffffff;
            line-height: 15 * @rem;
          }

          .gold-balance {
            display: flex;
            align-items: center;
            margin-top: 7 * @rem;

            .gold-num {
              font-size: 20 * @rem;
              color: #ffffff;
              line-height: 25 * @rem;
              font-weight: 600;
            }

            .gold-detail {
              margin-left: 8 * @rem;
              font-size: 11 * @rem;
              color: #ffffff;
              padding-right: 10 * @rem;
              background: url(~@/assets/images/recharge/right-icon-white.png)
                right center no-repeat;
              background-size: 6 * @rem 10 * @rem;
            }
          }
        }

        .gold-mall {
          width: 100 * @rem;
          height: 40 * @rem;
          border-radius: 22 * @rem;
          border: 1 * @rem solid rgba(255, 255, 255, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(255, 255, 255, 0.1);

          .title {
            font-size: 15 * @rem;
            color: #ffffff;
          }

          .icon {
            width: 6 * @rem;
            height: 10 * @rem;
            background: url(~@/assets/images/recharge/right-icon-white.png)
              no-repeat;
            background-size: 6 * @rem 10 * @rem;
            margin-left: 6 * @rem;
          }
        }
      }
    }

    .task-container {
      position: relative;
      margin: 16 * @rem auto 0;
      width: 355 * @rem;
      border-radius: 12px 12px 12px 12px;
      background: #ffffff;

      &.first {
        margin-top: -41 * @rem;
      }

      .task-title {
        width: 355 * @rem;
        height: 48 * @rem;
      }

      .task-list {
        // padding: 1 * @rem 0 25 * @rem;
        .task-item {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          // width: 331 * @rem;
          height: 71 * @rem;
          /* background: #f5f9ff; */
          /* border-radius: 0.2136rem; */
          // margin: 0 auto;
          padding: 0 16 * @rem;

          &:not(:last-of-type) {
            border-bottom: 0.5px solid #f4f4f4;
          }

          .icon {
            width: 40 * @rem;
            height: 40 * @rem;
            background-color: #f5f5f5;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
              width: 24 * @rem;
              height: 24 * @rem;
            }
          }

          .task-info {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;

            .title {
              font-size: 13 * @rem;
              color: #222222;
              height: 18px;
              line-height: 18 * @rem;
              font-weight: 600;
              text-align: left;
              word-break: break-all;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .subtitle {
              font-size: 10 * @rem;
              color: #7a7a7a;
              line-height: 18 * @rem;
              font-weight: 500;
              text-align: left;
              word-break: break-all;
              margin-top: 2 * @rem;
            }

            .line {
              display: flex;
              align-items: center;
              margin-top: 6 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 181 * @rem;
              flex-shrink: 0;

              .gold {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-gold.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }

              .exp {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-exp.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }

              .coupon {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-coupon.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }

              .reward-info {
                color: #f05f29;
              }
            }

            .extra {
              font-size: 11 * @rem;
              color: #777777;
              line-height: 14 * @rem;
              margin-top: 6 * @rem;
            }
          }

          .task-btn {
            width: 72 * @rem;
            height: 28 * @rem;
            border-radius: 25 * @rem;
            border: 1px solid #cccccc;
            font-size: 12 * @rem;
            color: #333333;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;

            &.had {
              color: #cccccc;
              border: 1px solid rgba(204, 204, 204, 0.5);
            }

            &.get {
              color: #fff;
              background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
              box-shadow: inset 0 0 10 * @rem 0 rgba(255, 255, 255, 0.7);
              border: 0;
            }
          }
        }

        .exchange-down {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #777777;
          padding: 10 * @rem 0 10 * @rem;

          i {
            display: block;
            width: 10 * @rem;
            height: 7 * @rem;
            margin-left: 4 * @rem;
            background: url(~@/assets/images/down-icon.png) center center
              no-repeat;
            background-size: 10 * @rem 7 * @rem;
          }

          &.up {
            i {
              transform: rotate(180deg);
            }
          }
        }
      }
    }

    .section {
      box-sizing: border-box;
      width: 355 * @rem;
      padding: 18 * @rem 18 * @rem;
      background: #ffffff;
      border-radius: 12 * @rem;
      margin: 20 * @rem auto 0;
      position: relative;

      .section-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          display: flex;
          align-items: center;

          .title {
            font-size: 18 * @rem;
            color: #000000;
            font-weight: 600;
          }

          .icon {
            width: 20 * @rem;
            height: 20 * @rem;
            background-size: 20 * @rem 20 * @rem;
            margin-left: 5 * @rem;

            &.gold-coin-coupon {
              background-image: url(~@/assets/images/recharge/gold-coin-coupon.png);
            }

            &.gold-coin-welfare {
              background-image: url(~@/assets/images/recharge/gold-coin-welfare.png);
            }

            &.gold-coin-help {
              background-image: url(~@/assets/images/recharge/gold-coin-help.png);
            }
          }
        }

        .right {
          display: flex;
          align-items: center;

          .right-title {
            font-size: 13 * @rem;
            color: #000000;
          }

          .right-icon-white {
            width: 12 * @rem;
            height: 12 * @rem;
            background-size: 12 * @rem 12 * @rem;
            margin-left: 4 * @rem;

            &.gold-coin-rule {
              background-image: url(~@/assets/images/recharge/gold-coin-rule.png);
            }
          }
        }
      }
    }

    .help-list {
      .help-item {
        padding-top: 15 * @rem;
        margin-top: 12 * @rem;

        .jinbidikou-pic {
          width: 100%;
          background-color: #f5f5f5;
          margin-top: 10 * @rem;

          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }

        &:not(:first-of-type) {
          border-top: 0.5 * @rem solid #f3f3f8;
        }

        .sub-title {
          font-size: 15 * @rem;
          color: #000000;
          font-weight: 600;
          padding-left: 20 * @rem;
          position: relative;

          &::before {
            content: '';
            width: 8 * @rem;
            height: 8 * @rem;
            background-color: @themeColor;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        .desc {
          font-size: 13 * @rem;
          color: #797979;
          line-height: 22 * @rem;
          margin-top: 10 * @rem;

          i {
            font-size: 13 * @rem;
            color: @themeColor;
          }

          span {
            color: @themeColor;
          }
        }
      }
    }
  }
}
</style>
