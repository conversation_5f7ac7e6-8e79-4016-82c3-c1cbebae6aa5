import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import {
  platform
} from '@/utils/box.uni.js';
import {
  sensorsChainSet,
  sensorsChainGet,
  sensorsPageSet,
  sensorsPageGet,
} from '@/utils/sensors.js';

// 重写push和replace,拦截跳转同一路由时报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => err);
};

Vue.use(VueRouter);

// require.context() webpack的方法，文件工程化导入
let routerList = [];

function importAll(r) {
  r.keys().forEach(fileName => {
    routerList.push(...r(fileName).default);
  });
}
importAll(require.context('./', true, /\.router\.js/));

const routes = [...routerList];

const router = new VueRouter({
  // history模式好像不太行
  // 记录各个滚动条位置
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return {
        x: 0,
        y: 0,
      };
    }
  },
  routes,
});

router.beforeEach(async (to, from, next) => {
  store.commit('system/setPostError', false);
  // 首次打开localstorage没有initData的情况下首页会出现白屏，延迟异步
  if (
    to.name === 'QualitySelect' &&
    !store.getters['system/initData'].configs
  ) {
    await setTimeout(() => {
      next('/');
    }, 0);
    return false;
  }
  // 任务大厅改为/gold_coin_task，旧/gold_coin重定向金币中心 2024年9月30日14:34:48
  if (to.path === '/gold_coin') {
    next({
      name: 'GoldCoinCenter',
    });
  }
  const routeExists = router.getRoutes().some(route => {
    return route.name === to.name;
  });

  if (routeExists) {
    next();
  } else {
    next({
      name: 'NotFoundPage',
    });
  }
});

router.afterEach((to, from) => {
  // console.log('------------after', to, from);
  if (!from.name) {
    sensorsChainSet();
    sensorsPageSet();
  }
  sensorsChainSet(sensorsPageGet());
  // console.log('------------after--------', sensorsPageGet(), sensorsChainGet());
  if (to.meta?.pageTitle) {
    sensorsPageSet(to.meta.pageTitle);
    // console.log('------------内', sensorsChainGet(), '--', sensorsPageGet());
  }
  // console.log('------------外', sensorsChainGet(), '--', sensorsPageGet());

  // 记录第一次进入的页面url
  // 当页面为打开的第一个页面时，url添加进sessionStorage，用来给官包判断是返回还是关闭窗口
  if (
    ['android', 'ios', 'androidBox', 'iosBox'].includes(platform) &&
    !window.sessionStorage.firstUrl
  ) {
    window.sessionStorage.firstUrl = window.location.href;
  }
});

export default router;