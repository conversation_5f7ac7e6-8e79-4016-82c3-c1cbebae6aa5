<template>
  <div class="news-fanli-history-page">
    <nav-bar-2 :title="$t('往期活动')" :border="true"></nav-bar-2>
    <content-empty v-if="empty"></content-empty>
    <load-more
      v-else
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <template v-for="(news, newsIndex) in newsList">
        <div class="news-type" :key="newsIndex" v-if="news.data.length">
          <div class="type-title">
            <div class="type-icon"></div>
            <div class="type-title-text">{{ formatType(news.type) }}</div>
          </div>
          <div class="news-list">
            <div class="news-item" v-for="item in news.data" :key="item.id">
              <div class="left" @click="toNewsDetail(item)">
                <div class="news-title">{{ item.title }}</div>
                <div class="news-desc">{{ $t('已结束') }}</div>
              </div>
              <div class="right" v-if="item.grant_type == 1">
                {{ $t('自动到账') }}
              </div>
              <div class="right kefu" @click="toPage('Rebate')" v-else>
                {{ $t('申请返利') }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </load-more>
  </div>
</template>

<script>
import { ApiNewsGameNewsList } from '@/api/views/news.js';
export default {
  data() {
    return {
      page: 1,
      listRows: 20,
      id: 0,
      newsList: [
        {
          type: 1, // 长期
          data: [],
        },
        {
          type: 2,
          data: [], // 限时
        },
        {
          type: 3,
          data: [], // 货币
        },
      ],
      loading: false,
      finished: false,
      empty: false,
    };
  },
  async created() {
    this.id = this.$route.params.game_id;
    await this.getNewsList();
  },
  methods: {
    formatType(type) {
      let typeStr = '';
      switch (type) {
        case 1:
          typeStr = this.$t('长期活动');
          break;
        case 2:
          typeStr = this.$t('限时活动');
          break;
        case 3:
          typeStr = this.$t('货币返利');
          break;
        default:
          typeStr = this.$t('长期活动');
          break;
      }
      return typeStr;
    },
    async getNewsList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiNewsGameNewsList({
        gameId: this.id,
        type: 1,
        isOverdue: 1,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.newsList = [
          {
            type: 1,
            data: [],
          },
          {
            type: 2,
            data: [],
          },
          {
            type: 3,
            data: [],
          },
        ];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      list.forEach(item => {
        let _index = this.newsList.findIndex(_news => {
          return item.time_type == _news.type;
        });
        this.newsList[_index].data.push(item);
      });
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getNewsList(2);
    },
    toNewsDetail(item) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: item.titleurl,
          title: item.title,
          showKefu: item.apply_type.type == 3 ? 1 : 0,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.news-fanli-history-page {
  background-color: #fff;
  .news-type {
    width: 347 * @rem;
    margin: 20 * @rem auto 0;
    background: #ffffff;
    box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
    border-radius: 12 * @rem;
    overflow: hidden;
    .type-title {
      display: flex;
      align-items: center;
      height: 38 * @rem;
      padding: 0 12 * @rem 0 19 * @rem;
      background-color: #f5f5f6;
      .type-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        .image-bg('~@/assets/images/games/sun-icon.png');
      }
      .type-title-text {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: bold;
        margin-left: 6 * @rem;
      }
    }
    .news-list {
      padding: 0 12 * @rem 0 19 * @rem;
      .news-item {
        display: flex;
        align-items: center;
        padding: 15 * @rem 0;
        &:not(:first-of-type) {
          border-top: 0.5 * @rem solid #ebebeb;
        }
        .left {
          flex: 1;
          min-width: 0;
          .news-title {
            font-size: 15 * @rem;
            color: #000000;
            line-height: 22 * @rem;
            max-height: 44 * @rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .news-desc {
            font-size: 13 * @rem;
            color: #909090;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-top: 5 * @rem;
          }
        }
        .right {
          width: 70 * @rem;
          height: 30 * @rem;
          background-color: #ebebeb;
          border: 1 * @rem solid #ebebeb;
          border-radius: 15 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 13 * @rem;
          color: #797979;
          margin-left: 10 * @rem;
          &.kefu {
            border: 1 * @rem solid @themeColor;
            background-color: #fff;
            color: @themeColor;
          }
        }
      }
    }
  }
}
</style>
