export default {
  state: {
    showEwmPopup: false, //二维码弹窗显示
    ewmSrc: '', // 需要生成二维码的地址
    payInfo: {}, // 支付信息
    closeEwmPopupFun: null, // 关闭二维码弹窗的回调函数
  },
  mutations: {
    setShowEwmPopup(state, showEwmPopup) {
      state.showEwmPopup = showEwmPopup ?? false;
    },
    setEwmSrc(state, ewmSrc) {
      state.ewmSrc = ewmSrc ? ewmSrc : '';
    },
    setPayInfo(state, payInfo) {
      state.payInfo = payInfo.orderId ? payInfo : {};
    },
    setCloseEwmPopupFun(state, payInfo) {
      state.closeEwmPopupFun = payInfo ? payInfo : ()=>{};
    },
  },
  getters: {
    showEwmPopup(state) {
      return state.showEwmPopup;
    },
    ewmSrc(state) {
      return state.ewmSrc;
    },
    payInfo(state) {
      return state.payInfo;
    },
    closeEwmPopupFun(state) {
      return state.closeEwmPopupFun;
    }
  },
};
