<template>
  <div class="category-list2">
    <div class="category-nav">
      <div
        class="nav-item"
        v-for="(nav, navIndex) in order_list"
        :key="navIndex"
        :class="{ active: active == navIndex }"
        @click="active = navIndex"
      >
        {{ nav.title }}
      </div>
      <div
        class="line"
        :style="{ left: `${active * 114 * remNumberLess}rem` }"
      ></div>
    </div>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
      <content-empty v-if="empty"></content-empty>
      <van-list
        v-else
        v-model="loading"
        :finished="finished"
        :finished-text="$t('没有更多了')"
        :offset="50"
        @load="loadMore()"
      >
        <div class="game-item" v-for="(item, index) in game_list" :key="index">
          <game-item-2 :gameInfo="item" :showRight="false"></game-item-2>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { themeColorLess, remNumberLess } from '@/common/styles/_variable.less';
import { ApiGameIndex } from '@/api/views/game.js';
import gameItem from '@/components/game-item';

export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      themeColorLess,
      remNumberLess,
      active: 0,
      game_list: [],
      finished: false,
      loading: false,
      reloading: false,
      refreshing: false,
      page: 1,
      empty: false,
      order_list: [
        {
          title: this.$t('热门游戏'),
          order: 1,
        },
        {
          title: this.$t('新游上架'),
          order: 3,
        },
      ],
    };
  },
  computed: {},
  watch: {
    async info(val, oldVal) {
      if (val != oldVal) {
        this.finished = false;
        this.page = 1;
        this.game_list = [];
        this.loading = true;
        await this.getGameList();
        this.loading = false;
      }
    },
    async active(val, oldVal) {
      if (val != oldVal) {
        this.finished = false;
        this.page = 1;
        this.game_list = [];
        this.loading = true;
        await this.getGameList();
        this.loading = false;
      }
    },
  },
  methods: {
    async getGameList() {
      this.empty = false;
      let data = {
        order: this.order_list[this.active].order,
        page: this.page,
        listRows: 10,
      };
      if (this.info.type) {
        data.type = this.info.type;
      }
      if (this.info.theme) {
        data.theme = this.info.theme;
      }

      const res = await ApiGameIndex(data);

      if (this.page === 1) this.game_list = [];
      this.game_list.push(...res.data.list);
      if (!this.game_list.length) {
        this.empty = true;
      }

      if (res.data.list.length < 10) {
        this.finished = true;
      } else {
        this.finished = false;
      }
    },
    async onChange() {
      this.game_list = [];
      this.page = 1;
      this.finished = false;
      this.loading = true;
      await this.getGameList();
      this.loading = false;
      this.$toast.clear();
    },
    async onRefresh() {
      this.page = 1;
      await this.getGameList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getGameList();
      this.loading = false;
      this.page++;
    },
  },
  components: {
    gameItem,
  },
};
</script>
<style lang="less" scoped>
::-webkit-scrollbar {
  display: none;
}
.category-list2 {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 105 * @rem - @safeAreaTop - @safeAreaBottom);
  height: calc(100vh - 105 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
  /deep/ .van-sidebar {
    flex: 0 0 95px;
    width: 95px;
    height: 100%;
    .van-sidebar-item {
      padding: 12px;
    }
    .van-sidebar-item--select::before {
      background-color: @themeColor;
    }
  }
  .category-nav {
    box-sizing: border-box;
    width: 228 * @rem;
    display: flex;
    height: 38 * @rem;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    .nav-item {
      width: 114 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: 0.3s;
      height: 38 * @rem;
      font-size: 16 * @rem;
      color: #797979;
      &.active {
        border-radius: 20 * @rem 20 * @rem 0 0;
        color: #000;
        font-weight: 600;
        transition: 0.2s;
      }
    }
    .line {
      width: 12 * @rem;
      height: 4 * @rem;
      background-color: @themeColor;
      position: absolute;
      bottom: 0 * @rem;
      transition: 0.2s;
      transform: translateX(51 * @rem);
      border-radius: 2 * @rem;
    }
  }
  .container {
    display: flex;
    flex-flow: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
    /deep/ .load-more {
      overflow-y: scroll;
    }
    /deep/ .van-dropdown-menu {
      position: relative;
      .van-dropdown-menu__bar {
        height: 44px;
        box-shadow: none;
        .van-dropdown-menu__title--active {
          color: @themeColor;
        }
      }
      .van-dropdown-item {
        position: absolute;
        top: 44px !important;
        height: calc(100vh - 193 * @rem - @safeAreaTop);
        height: calc(100vh - 193 * @rem - @safeAreaTopEnv);
      }
    }
  }
  .list {
    flex: 1;
    overflow-y: scroll;
    padding: 0 15 * @rem;
    ::-webkit-scrollbar {
      display: none;
    }
    .game-item {
      background: #ffffff;
      box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
      border-radius: 12 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10 * @rem;
      padding: 0 10 * @rem;
      /deep/ .game-item-components {
        .game-icon {
          width: 58 * @rem;
          height: 58 * @rem;
          flex: 0 0 58 * @rem;
        }
        .game-info {
          height: unset;
          .game-name {
            font-size: 13 * @rem;
            font-weight: bold;
            line-height: 18 * @rem;
          }
          .game-bottom {
            font-size: 10 * @rem;
            line-height: 14 * @rem;
            margin-top: 3 * @rem;
          }
          .tags {
            margin-top: 3 * @rem;
          }
        }
      }
    }
  }
}
</style>
