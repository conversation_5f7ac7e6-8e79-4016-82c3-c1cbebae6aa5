<template>
  <div class="exchange-gold">
    <div class="header">
      <nav-bar-2
        bgStyle="transparent-white"
        :azShow="true"
        :title="'提现'"
        class="nav"
      >
        <template #right>
          <div
            @click="toPage('InviteCostRecord', { type: 2 })"
            class="btn right"
          >
            提现记录
          </div>
        </template>
      </nav-bar-2>
      <div class="bottom">
        <div class="left">
          <div class="small-text">当前现金</div>
          <div class="big-text"><span>￥</span>{{ money }}</div>
        </div>
      </div>
    </div>
    <main>
      <div class="title">
        <div class="left">选择提现金额</div>
        <div class="right">提现比例：1现金:1元</div>
      </div>
      <div class="list">
        <div
          v-for="item in list"
          :key="item.id"
          :class="{
            current: current == item.id && !item.is_ban,
            disable: item.is_ban,
          }"
          @click="changeCurrent(item.id, item.is_ban)"
          class="item"
        >
          <div class="left">{{ item.title }}</div>
          <div class="right">{{ item.subtitle }}</div>
        </div>
      </div>
      <div @click="submit" class="btn big-button">立即提现</div>
      <div class="tips">
        <div class="tips-title">重要提示</div>
        <div class="text">
          1.绑定手机号、微信号以及实名认证后才可以进行提现<br />
          2.每个手机号、手机设备、身份证、微信号、3733游戏盒子账号一经绑定即为唯一提现凭证，请确认好常用设备与微信号进行操作，绑定后不更改<br />
          <span class="color"
            >3.提现金额将通过“3733游戏”公众号进行发放，您需关注公众号后才可自行领取</span
          >
        </div>
      </div>
    </main>
    <van-popup v-model="popup1" :close-on-click-overlay="false" class="content">
      <div class="title">提现申请已提交</div>
      <div class="item">
        提现金额：<span>{{ text }}</span
        >元
      </div>
      <div class="text">
        本次提现将通过“<span class="color">3733游戏</span
        >”公众号发放，预计在1—2个工作日内打款，客服正在加急处理中，请耐心等候。（<span
          class="color"
          >提现红包发放后需在公众号内自行领取</span
        >）
      </div>
      <div @click="popup1 = false" class="button">知道了</div>
    </van-popup>
  </div>
</template>
<script>
import {
  ApiInviteExchangeMoney,
  ApiInviteExchangeMoneyPost,
  ApiInviteExchangeWx,
} from '@/api/views/weekWelfare.js';
import { Box_wxOAuth2, packageName } from '@/utils/box.uni';
import { themeColorLess } from '@/common/styles/_variable.less';

export default {
  data() {
    return {
      popup1: false, //弹窗
      money: 0,
      list: [],
      current: -1,
      text: '', //用来存储弹窗文案
    };
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      const res = await ApiInviteExchangeMoney();
      this.money = res.data.money;
      this.list = res.data.money_reward;
      for (let key in this.list) {
        if (!this.list[key].is_ban) {
          this.current = this.list[key].id;
          return false;
        }
      }
    },
    async submit() {
      const res2 = await ApiInviteExchangeWx();
      if (!res2.data.is_bind) {
        if (
          packageName === 'com.a3733.gamebox' ||
          packageName === 'com.a3733.gameboxwww'
        ) {
          this.$dialog
            .confirm({
              message: '您好，提现需要绑定微信方可到账，是否前往绑定？',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            })
            .then(() => {
              Box_wxOAuth2();
            });
        } else {
          this.$dialog
            .confirm({
              message: '您好，提现需要绑定微信方可到账，是否前往绑定？',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            })
            .then(() => {
              this.toPage('BindWeChat');
            });
        }
        return false;
      }
      if (!res2.data.is_gz) {
        this.$dialog
          .confirm({
            message: '您还未关注公众号，是否前往关注？',
            confirmButtonColor: themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            this.toPage('BindWeChat');
          });
        return false;
      }
      const res = await ApiInviteExchangeMoneyPost({
        id: this.list[this.current].id,
      });
      this.text = this.list[this.current].consume;
      this.popup1 = true;
      await this.init();
    },
    changeCurrent(index, is_ban) {
      if (is_ban) {
        return false;
      }
      this.current = index;
    },
  },
};
</script>
<style lang="less" scoped>
.exchange-gold {
  .header {
    background: linear-gradient(100.18deg, #ffc875 1.05%, #ff7e55 98.1%);
    overflow: hidden;
    .nav {
      .right {
        color: #fff;
      }
    }
    .bottom {
      margin: 30 * @rem auto;
      color: #fff;
      display: flex;
      .big-text {
        margin-top: 5 * @rem;
        font-size: 32 * @rem;
        font-weight: 600;
        line-height: 38 * @rem;
        span {
          font-size: 20 * @rem;
        }
      }
      .left,
      .right {
        flex: 0 0 50%;
      }
      .left {
        padding-left: 18 * @rem;
        box-sizing: border-box;
      }
      .right {
        border-left: 1 * @rem solid rgba(255, 255, 255, 0.23);
        padding-left: 28 * @rem;
        box-sizing: border-box;
      }
    }
  }
  main {
    padding: 0 18 * @rem;
    .title {
      margin: 15 * @rem 0;
      display: flex;
      justify-content: space-between;
      .left {
        font-size: 15 * @rem;
        font-weight: 600;
        color: #333438;
      }
      .right {
        font-size: 12 * @rem;
        color: #ff7144;
        font-weight: 400;
      }
    }
    .list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .item {
      flex: 0 0 30%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      align-items: center;
      width: 105 * @rem;
      margin-bottom: 10 * @rem;
      height: 70 * @rem;
      padding: 0 14 * @rem;
      box-sizing: border-box;
      background: #fff9f1;
      border: 1 * @rem solid #fff9f1;
      border-radius: 8 * @rem;
      &.current {
        border: 1 * @rem solid #ff7f56;
        &::after {
          position: absolute;
          top: -1 * @rem;
          right: -1 * @rem;
          content: '';
          width: 14 * @rem;
          height: 14 * @rem;
          .image-bg('~@/assets/images/welfare/invite/invite_icon1.png');
        }
      }
      &.disable {
        background: #f7f7f7;
        .left,
        .right {
          color: #cccccc;
        }
      }
      .left {
        font-size: 14 * @rem;
        font-weight: 600;
        color: #333333;
      }
      .right {
        margin-top: 8 * @rem;
        font-size: 12 * @rem;
        font-weight: 400;
        color: #888888;
      }
    }
    .big-button {
      width: 200 * @rem;
      height: 40 * @rem;
      margin: 28 * @rem auto;
      background: @themeColor;
      border-radius: 40 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .tips {
      margin: 60 * @rem auto 30 * @rem;
      .tips-title {
        margin-bottom: 8 * @rem;
        font-size: 14 * @rem;
        font-weight: 600;
        color: #4a4a4a;
      }
      .text {
        line-height: 18 * @rem;
        color: #666666;
        .color {
          color: @themeColor;
        }
      }
    }
  }
  .content {
    width: 300 * @rem;
    border-radius: 10 * @rem;
    box-sizing: border-box;
    padding: 20 * @rem;
    .title {
      margin-bottom: 20 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      text-align: center;
    }
    .item {
      margin-bottom: 10 * @rem;
      font-size: 14 * @rem;
      color: #333333;
      span {
        color: @themeColor;
      }
    }
    .text {
      margin-top: 20 * @rem;
      font-size: 14 * @rem;
      color: #777777;
      span {
        color: @themeColor;
      }
    }
    .button {
      width: 110 * @rem;
      height: 32 * @rem;
      background: @themeColor;
      border-radius: 18 * @rem;
      color: #fff;
      text-align: center;
      line-height: 32 * @rem;
      margin: 20 * @rem auto 0;
    }
  }
}
</style>
