<template>
  <div class="user-recommend-detail-page">
    <nav-bar-2 :border="true">
      <template #center>
        <div class="title-bar" v-if="info.user">
          <user-avatar
            class="avatar"
            :src="info.user.avatar"
            :self="false"
          ></user-avatar>
          <div class="nickname">{{ info.user.nickname }}</div>
        </div>
      </template>
    </nav-bar-2>
    <div class="content" v-if="info.game">
      <div class="recommend-content" v-html="info.content"></div>
      <div class="recommend-game" @click.stop="">
        <game-item-2 :gameInfo="info.game"></game-item-2>
      </div>
    </div>

    <div class="comment-container">
      <div class="comment-title">
        <div class="title">{{ $t('玩家评论') }}</div>
      </div>
      <content-empty v-if="empty"></content-empty>
      <load-more
        v-else
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="comment-list">
          <template v-for="item in commentList">
            <comment-item
              :info="item"
              :key="item.comment_id"
              :classId="104"
            ></comment-item
          ></template>
        </div>
      </load-more>
    </div>
    <div class="bottom-container">
      <div class="operate-bar">
        <div class="comment-entry" @click="clickComment">
          {{ $t('写评论...') }}
        </div>
        <div class="operate-list">
          <!-- <div class="operate-item comment-btn"></div> -->
          <div
            class="operate-item collect-btn"
            :class="{ on: collected }"
            @click="setCollectStatus"
          ></div>
          <div
            class="operate-item like-btn"
            :class="{ on: this.liked }"
            @click="setLikeStatus"
          ></div>
        </div>
      </div>
      <bottom-safe-area></bottom-safe-area>
    </div>
    <van-popup v-model="isReplying" :lock-scroll="false" position="bottom">
      <div class="reply-editor" v-if="isReplying">
        <div class="reply-title"></div>
        <div class="input-container">
          <textarea
            id="inputText"
            class="input-text"
            v-model="replyInput"
            rows="10"
            :placeholder="$t('写两句你想说的话...')"
          ></textarea>
        </div>
        <div class="reply-operation">
          <div
            class="send btn"
            :class="{ on: replyInput.length > 0 }"
            @click="sendReply"
          >
            {{ $t('发送') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiCommentComments, ApiCommentSubmit } from '@/api/views/comment.js';
import {
  ApiGameGetRecommendInfo,
  ApiResourceCollectStatus,
  ApiResourceCollect,
  ApiGameRecommendThumb,
} from '@/api/views/game.js';
import commentItem from '@/components/comment-item';
export default {
  components: {
    commentItem,
  },
  data() {
    return {
      id: '',
      info: {},
      commentList: [],
      page: 1,
      listRows: 10,
      loading: false,
      finished: false,
      empty: false,
      collected: false,
      liked: false,
      isReplying: false,
      replyInput: '',
    };
  },
  computed: {
    formatReplyInput() {
      return this.replyInput.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getDetail();
    this.getCollectStatus();
    await this.getCommentList();
  },
  methods: {
    async getDetail() {
      const res = await ApiGameGetRecommendInfo({
        id: this.id,
      });
      this.info = res.data.info;
      this.liked = this.info.thumb;
    },
    // 获取收藏状态 （注意：这里是收藏游戏的状态）
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 3,
        sourceId: this.info.game.id,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    // 设置收藏 （注意：这里收藏的是游戏）
    setCollectStatus() {
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;
      }
      ApiResourceCollect({
        classId: 3,
        sourceId: this.info.game.id,
        status: status,
      }).then(res => {
        this.collected = res.data.status;
      });
    },
    // 点赞
    setLikeStatus() {
      let type;
      if (this.liked) {
        type = 0;
      } else {
        type = 1;
      }
      ApiGameRecommendThumb({
        reId: this.id,
        type: type,
      }).then(res => {
        this.$toast(res.msg);
        this.liked = !this.liked;
      });
    },
    clickComment() {
      this.isReplying = true;
      this.$nextTick(() => {
        document.querySelector('#inputText').focus();
      });
    },
    // 发送回复
    async sendReply() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.replyInput.length < 1) {
        this.$toast(this.$t('输入的内容太少'));
        this.isReplying = false;
        return false;
      }
      this.$toast.loading({
        message: this.$t('发送中...'),
      });
      let params = {
        sourceId: this.id,
        classId: 104,
        model: 'iPhone X',
        content: this.formatReplyInput,
        rating: 0,
      };
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
      } finally {
        this.isReplying = false;
      }
    },
    async getCommentList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCommentComments({
        page: this.page,
        listRows: this.listRows,
        classId: 104,
        sourceId: this.id,
      });
      let { hots, comments, rating, tops, cmt_sum } = res.data;
      this.rating = rating;
      if (cmt_sum) {
        this.$emit('updateCommentSum', cmt_sum);
      }
      if (action === 1 || this.page === 1) {
        this.commentList = [];
        if (tops && tops.length) {
          this.topList = tops;
        }
        if (hots && hots.length) {
          this.hotList = hots;
        }
        if (!comments.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.commentList.push(...comments);
      if (comments.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.$nextTick(() => {
        this.loading = false;
      });
    },
    loadMore() {
      this.getCommentList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.user-recommend-detail-page {
  box-sizing: border-box;
  background-color: #fff;
  padding-bottom: 90 * @rem;
  min-height: 100vh;
  /deep/ .title-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    .avatar {
      width: 22 * @rem;
      height: 22 * @rem;
      border-radius: 50%;
      background-color: #ccc;
    }
    .nickname {
      font-size: 16 * @rem;
      color: #000000;
      line-height: 22 * @rem;
      font-weight: 500;
      margin-left: 6 * @rem;
      flex: 1;
      min-width: 0;
    }
  }
  .content {
    padding: 18 * @rem;
    border-bottom: 10 * @rem solid #f5f5f6;
    .recommend-content {
      padding: 0 7 * @rem;
      font-size: 14 * @rem;
      color: #000000;
      line-height: 22 * @rem;
    }
    .recommend-game {
      background-color: #f5f5f6;
      border-radius: 12 * @rem;
      padding: 0 10 * @rem;
      margin-top: 12 * @rem;
    }
  }
  .comment-container {
    padding: 20 * @rem 0;
    .comment-title {
      padding: 0 18 * @rem;
      .title {
        font-size: 18 * @rem;
        color: #000000;
        font-weight: 600;
        line-height: 25 * @rem;
        position: relative;
        padding-left: 14 * @rem;
        &::before {
          content: '';
          width: 4 * @rem;
          height: 12 * @rem;
          background-color: @themeColor;
          border-radius: 2 * @rem;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .comment-list {
      padding: 0 18 * @rem;
    }
  }
  .bottom-container {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    background: #ffffff;
    box-shadow: 0 * @rem -2 * @rem 6 * @rem 0 * @rem rgba(162, 156, 149, 0.15);
    border-radius: 18 * @rem 18 * @rem 0 * @rem 0 * @rem;
    .operate-bar {
      box-sizing: border-box;
      width: 100%;
      height: 60 * @rem;
      display: flex;
      align-items: center;
      padding: 0 15 * @rem;
      .comment-entry {
        flex: 1;
        height: 38 * @rem;
        display: flex;
        align-items: center;
        border-radius: 23 * @rem;
        background-color: #f2f2f2;
        padding-left: 24 * @rem;
        font-size: 15 * @rem;
        color: #747474;
      }
      .operate-list {
        display: flex;
        align-items: center;
        .operate-item {
          width: 38 * @rem;
          height: 38 * @rem;
          background-size: 38 * @rem 38 * @rem;
          margin-left: 10 * @rem;
          &.comment-btn {
            background-image: url(~@/assets/images/games/recommend-comment.png);
          }
          &.collect-btn {
            background-image: url(~@/assets/images/games/recommend-collect.png);
            &.on {
              background-image: url(~@/assets/images/games/recommend-collect-on.png);
            }
          }
          &.like-btn {
            background-image: url(~@/assets/images/games/recommend-like.png);
            &.on {
              background-image: url(~@/assets/images/games/recommend-like-on.png);
            }
          }
        }
      }
    }
  }
}
.reply-editor {
  width: 100%;
  height: 160 * @rem;
  background-color: #fff;
  .reply-title {
    box-sizing: border-box;
    height: 40 * @rem;
    background-color: #f5f5f6;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    font-size: 13 * @rem;
    color: #797979;
    font-weight: 400;
    .reply-user {
      font-size: 13 * @rem;
      color: #000000;
      font-weight: 500;
    }
  }
  .input-container {
    box-sizing: border-box;
    width: 100%;
    height: 82 * @rem;
    .input-text {
      box-sizing: border-box;
      display: block;
      width: 100%;
      height: 100%;
      padding: 12 * @rem;
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
      line-height: 20 * @rem;
      border: 0;
      outline: none;
    }
  }
  .reply-operation {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 12 * @rem;
    .send {
      width: 56 * @rem;
      height: 28 * @rem;
      border-radius: 14 * @rem;
      background-color: #c1c1c1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #ffffff;
      font-weight: 400;
      &.on {
        background-color: @themeColor;
      }
    }
  }
}
</style>
