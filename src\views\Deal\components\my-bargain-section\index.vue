<template>
  <div class="bargain-section">
    <div class="game-container" @click="toXiaohaoDetail">
      <div class="game-icon">
        <img :src="info.titlepic" alt="" />
      </div>
      <div class="game-right">
        <div class="right-line">
          <div class="game-name" :class="{ short: info.trade_status == 7 }">
            {{ info.title }}
          </div>
          <div
            class="pay-btn"
            v-if="info.order"
            @click.stop="goToPay(info.order)"
          >
            {{ $t('立即支付') }}
          </div>
        </div>
        <div class="game-info">
          <div class="info-item">{{ $t('区服') }}：{{ info.game_area }}</div>
          <div class="info-item">{{ $t('小号id') }}：{{ info.xh_id }}</div>
        </div>
      </div>
      <div class="had-deal" v-if="info.trade_status == 7"></div>
    </div>
    <div
      class="game-bargain-list"
      :class="{ auto: open || info.list.length <= 3 }"
    >
      <div class="bargain-item" v-for="item in info.list" :key="item.id">
        <bargain-item
          @refresh="$emit('refresh')"
          :info="item"
          :trade_status="info.trade_status"
        ></bargain-item>
      </div>
    </div>
    <div class="toggle" v-if="info.list.length > 3" @click="toggle">
      <div class="toggle-text">{{ open ? $t('收起') : $t('展开') }}</div>
      <div class="toggle-icon" :class="{ open: open }"></div>
    </div>
  </div>
</template>

<script>
import bargainItem from '../bargain-item/index.vue';
export default {
  components: {
    bargainItem,
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      open: false,
    };
  },
  methods: {
    toggle() {
      this.open = !this.open;
    },
    goToPay(order) {
      this.toPage('XiaohaoOrderRecharge', {
        info: order,
        back: 'MyBargainList',
      });
    },
    toXiaohaoDetail() {
      this.toPage('XiaohaoDetail', {
        id: this.info.trade_id,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.bargain-section {
  padding: 14 * @rem 18 * @rem;
  &:not(:last-of-type) {
    border-bottom: 10 * @rem solid #f5f5f6;
  }
  .game-container {
    box-sizing: border-box;
    padding: 16 * @rem;
    width: 339 * @rem;
    height: 86 * @rem;
    border-radius: 8 * @rem;
    background-color: #f5f5f6;
    display: flex;
    align-items: center;
    margin: 0 auto;
    position: relative;
    .had-deal {
      width: 76 * @rem;
      height: 76 * @rem;
      .image-bg('~@/assets/images/deal/had-deal-icon.png');
      position: absolute;
      right: -12 * @rem;
      top: -14 * @rem;
    }
    .game-icon {
      width: 54 * @rem;
      height: 54 * @rem;
    }
    .game-right {
      flex: 1;
      min-width: 0;
      margin-left: 12 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .right-line {
        display: flex;
        justify-content: space-between;
        .game-name {
          font-size: 16 * @rem;
          color: #000000;
          line-height: 22 * @rem;
          font-weight: bold;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 5 * @rem;
          &.short {
            margin-right: 50 * @rem;
          }
        }
        .pay-btn {
          width: 60 * @rem;
          height: 26 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(90deg, #ffa06c 0%, #ff5050 100%);
          border-radius: 6 * @rem;
          color: #ffffff;
          font-size: 12 * @rem;
          margin-top: -5 * @rem;
          margin-right: -4 * @rem;
        }
      }

      .game-info {
        display: flex;
        align-items: center;
        margin-top: 6 * @rem;
        .info-item {
          font-size: 13 * @rem;
          color: #9a9a9a;
          line-height: 18 * @rem;
          &:not(:last-of-type) {
            margin-right: 14 * @rem;
          }
        }
      }
    }
  }
  .game-bargain-list {
    height: 146 * @rem; //219
    transition: height 0.3s;
    overflow: hidden;
    &.auto {
      height: auto;
    }
    .bargain-item {
      &:not(:last-of-type) {
        border-bottom: 1 * @rem solid #f3f3f8;
      }
    }
  }
  .toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    .toggle-text {
      font-size: 12 * @rem;
      color: #9a9a9a;
      line-height: 17 * @rem;
    }
    .toggle-icon {
      width: 10 * @rem;
      height: 6 * @rem;
      margin-left: 5 * @rem;
      .image-bg('~@/assets/images/deal/down-arrow.png');
      &.open {
        transform: rotateZ(180deg);
      }
    }
  }
}
</style>
