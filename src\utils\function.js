import { Toast, Dialog } from 'vant';
import store from '@/store';
import { request } from '@/api';
import router from '@/router';

import useAddToDeskPopup from '@/components/add-to-desk-popup/index.js';
import useConfirmDialog from '@/components/yy-confirm-dialog/index.js';
import useEwmPopup from '@/components/ewm-popup/index.js';
import { handleActionCode } from '@/utils/actionCode.js';
import { toPage } from '@/utils/tools.js';
import { sensors } from '@/utils/sensors.js';

import {
  ApiUserInfoEx,
  ApiNewUser,
  ApiUserSubmitInstallInfo,
  ApiUserSubmitLoginInfo,
  ApiUserSubmitRegisterInfo,
  ApiUserSubmitUserInfo,
} from '@/api/views/users';
import { ApiMediumSubmitWechatRegister } from '@/api/views/system.js';
import {
  ApiGameCheckDown,
  ApiGameDownloadAdded,
  ApiSimulatorSaveCheatCode,
  ApiSimulatorDelCheatCode,
  ApiUserMemIsSvip,
  ApiPlayingRecodePlayTime,
  ApiResourceCollect,
  ApiCloudSdkReportPlay,
  ApiCloudSdkGetPriorityList,
  ApiCloudUserInfo,
  ApiCloudSdkInit,
} from '@/api/views/game.js';
import {
  BOX_goToGame,
  BOX_openInBrowser,
  BOX_openInNewWindow,
  BOX_showActivity,
  BOX_showActivityByAction,
  BOX_getPackageName,
  BOX_openInNewNavWindow,
  BOX_openInBrowser_H5Download,
  BOX_checkInstall,
  BOX_openApp,
} from '@/utils/box.uni.js';
import BASEPARAMS from '@/utils/baseParams';
import i18n from '@/i18n';
import { authInfo, platform, isSdk } from './box.uni';
import {
  isIos,
  isIosBox,
  isWebApp,
  isSafari,
  needGuide,
  isAndroid,
  isAndroidBox,
  iosSystem,
} from '@/utils/userAgent';
import h5Page from '@/utils/h5Page';
import {
  ApiPersonalSign,
  ApiCheckUdid,
  ApiUserCheckGrqAuthStatus,
} from '@/api/views/users.js';

export function loginSuccess(res) {
  Toast.success(i18n.t('登录成功'));
  store.commit('user/setUserInfo', res.data);
  sensors.login(`${store.getters['user/userInfo'].user_id}`);
  store.commit('gift/setXiaohaoMap');
  store.dispatch('system/SET_PAGE_NAV_LIST');

  // 消息推送
  if (platform == 'iosBox' && window.messagePost) {
    let postMessageData = store.getters['system/postMessageData'];
    window.messagePost(postMessageData);
  }
  // 首次登录保存记录，后续登录不再跳转注册页 2023年12月12日14:06:51
  if (!localStorage.getItem('HAD_LOGIN_HW')) {
    localStorage.setItem('HAD_LOGIN_HW', 1);
  }

  // 如果渠道号是804判断是不是第一次登录，是的话上报，不是的话不处理
  if (BASEPARAMS.channel === 'cps804') {
    if (!localStorage.getItem('804first')) {
      ApiNewUser({ packageName: getQueryVariable('packageName') });
      localStorage.setItem('804first', 1);
    }
  }

  // TrackingIO统计(上报服务端)
  if (
    (!!store.getters['system/accessTrackingIO'] ||
      !!store.getters['system/accessMediumH5IO'] ||
      !!store.getters['system/accessMediumWechatIO']) &&
    ['iosBox', 'androidBox', 'h5'].includes(platform)
  ) {
    // 特定渠道才要上报 从store里拿

    let plat = 'ios';
    if (platform == 'androidBox') {
      plat = 'android';
    }
    if (!res.data.new_user) {
      // 登录上报
      ApiUserSubmitLoginInfo({ plat });
    } else {
      // 注册上报
      ApiUserSubmitRegisterInfo({ plat });
      // 小程序广告注册 上报
      let urlQuery = getQueryVariable();
      if (
        urlQuery &&
        ((urlQuery.open_id && urlQuery.clue_token) || platform === 'iosBox') &&
        !!store.getters['system/accessMediumWechatIO']
      ) {
        let temp_data = {
          ...urlQuery,
        };
        if (urlQuery.clue_token) temp_data.medium_key = urlQuery.clue_token;
        ApiMediumSubmitWechatRegister(temp_data);
      }
    }

    // 登录之后上报玩家信息
    if (getQueryVariable('medium_code')) {
      ApiUserSubmitUserInfo({
        plat,
        medium_code: getQueryVariable('medium_code'),
      });
    } else {
      ApiUserSubmitUserInfo({ plat });
    }
  }

  ApiUserInfoEx().then(async res2 => {
    store.commit('user/setUserInfoEx', res2.data);
    await store.dispatch('system/SET_INIT_DATA');
    await store.dispatch('user/SET_USER_INFO');

    // 如果是海外用户，且没有邮箱，则强制绑定邮箱
    if (
      store.getters['system/initData'].bang_email &&
      !store.getters['user/userInfo'].email
    ) {
      router.replace({ name: 'ForceEmail' });
      localStorage.setItem('STORE', JSON.stringify(store.state));
      return false;
    }

    router.go(-1);
    await store.dispatch('user/SET_REGRESSION_POPUP'); // 回归用户处理
    await store.dispatch('user/SET_FIRST_COUPON_ICON'); // 首充条件
    if (!res2.data || parseInt(res2.data.auth_status) != 2) {
      setTimeout(() => {
        store.commit('user/setShowLCPopup', true);
      }, 0);
    }
    localStorage.setItem('STORE', JSON.stringify(store.state));
  });
}
// 下载按钮防沉迷check
export const checkDownload = async () => {
  const res = await ApiGameCheckDown();
  if (res.code > 0) {
    return true;
  }
  return false;
};

// 游戏上报（点击游戏下载的时候调用一下） 2023年9月13日14:02:29
export const downloadAdd = async ({ gameId, classId }) => {
  try {
    const res = await ApiGameDownloadAdded({
      gameId: gameId,
      classId: classId,
    });
  } finally {
  }
};

// 个人签弹窗处理
export const handleGrqDownload = async detail => {
  // 循环调用接口，更改各个状态
  const res = await ApigrqGameList({ game_id: detail.id });
  switch (parseInt(res.data.list[0].grq_status)) {
    case 0:
      store.commit('game/setGrqStatus', 0);
      setTimeout(handleGrqDownload(detail), 4000);
      break;
    case 1:
      store.commit('game/setGrqStatus', 1);
      setTimeout(handleGrqDownload(detail), 4000);
      break;
    case 2:
      store.commit('game/setDownloadGrqDialogShow', false);
      // 下载游戏
      BOX_openInBrowser({ h5_url: res.data.list[0].grq_down_ip });
      store.commit('game/setGrqStatus', 2);
      break;
    case 3:
    case 4:
      store.commit('game/setDownloadGrqDialogShow', false);
      Toast(i18n.t('签名失败'));
      store.commit('game/setGrqStatus', -1);
      break;
  }
};

// 下载游戏
export const downloadGame = async (detail, checkDownloadStatus = true) => {
  // 判断webapp未添加桌面版时，点击下载均弹出添加桌面引导弹窗
  if (isIos && needGuide) {
    document.querySelector('.fix-button.yindao-button').click();
    return false;
  }
  if (checkDownloadStatus) {
    const checkRes = await checkDownload();
    if (!checkRes) {
      return false;
    }
    downloadAdd({
      gameId: detail.id,
      classId: detail.classid,
    });
  }
  // 安卓下载
  if (!isIos) {
    if (platform == 'android' || platform == 'androidBox') {
      // 安卓官包 安卓马甲包
      let isDownload = false;
      try {
        isDownload = BOX.checkInstall(detail.package_name);
      } catch (error) {}
      if (isDownload) {
        BOX_openApp(detail.package_name);
      } else {
        window.BOX.startDownload(JSON.stringify(detail));
        setTimeout(() => {
          BOX_showActivity({ isAndroidBoxToNative: true }, { page: 'yygl' });
        }, 200);
      }
    } else {
      window.location.href = detail.down_a;
    }
  } else {
    // 判断登录
    if (store.getters['user/userInfo'].token) {
      // ios马甲包 下载鲸云漫游 特殊字段
      if (detail?.jy_ios_url) {
        BOX_openInBrowser({ h5_url: detail?.jy_ios_url });
        return false;
      }
      // 如果是个人签
      if (detail.repair) {
        // 走我们自己的下载方法
        if (parseInt(detail.grq_status) === 1) {
          let userAgent = isIosBox ? 2 : isWebApp ? 0 : 1;
          ApiPersonalSign({
            game_id: detail.id,
            from_plat: userAgent,
            packageName: userAgent === 2 ? BOX_getPackageName() : '',
          }).then(res => {
            switch (parseInt(res.data.status)) {
              // 不是svip
              case 2:
                router.push({ name: 'Svip' });
                break;
              // 没有绑定udid
              case 3:
                store.commit('game/setGetUdidPopupShow', true);
                // 能走到这里的只有ios的马甲包和webapp
                setTimeout(() => {
                  let { jump_url, mobileconfig_url, mobileprovision_url } =
                    res.data;
                  if (isIosBox) {
                    // 下载配置文件,双url才能跳转到配置文件
                    BOX_openInBrowser({
                      h5_url: `${jump_url}?url1=${mobileconfig_url}&url2=${mobileprovision_url}`,
                    });
                  } else {
                    window.location.href = mobileconfig_url;
                    setTimeout(() => {
                      // 非套壳情况需要第二遍让其自动跳转
                      window.location.href = mobileprovision_url;
                    }, 1000);
                  }
                }, 200);
                break;
              // 已经签完
              case 5:
                store.commit('game/setDownloadPopupShow', true);
                // 下载游戏
                BOX_openInBrowser({ h5_url: res.data.grq_down_ip });
                break;
              default:
                store.commit('game/setGetUdidPopupShow', false);
                store.commit('game/setDownloadGrqDialogShow', true);
                this.handleGrqDownload(detail);
            }
          });
        }
        // 走纸片内测
        else if (parseInt(detail.grq_status) === 2) {
          ApiUserCheckGrqAuthStatus({
            grq_status: detail.grq_status,
            game_id: detail.id,
          }).then(res => {
            // 第三方个人签，纸片内测
            BOX_openInBrowser({ h5_url: res.data.zp_grq_url });
          });
        }
      }
      // 如果是企业签
      else {
        store.commit('game/setDownloadPopupShow', true);
        // 下载游戏
        BOX_openInBrowser({ h5_url: detail.down_ip });
      }
    }
    // 如果没登录
    else {
      router.push({ name: 'PhoneLogin' });
    }
  }
};

// h5游戏下载
export async function downloadH5Game(appid, isConfigUrl = false) {
  if (!!isIos) {
    setTimeout(() => {
      // 下载配置文件,双url才能跳转到配置文件
      let jump_url = 'https://grq.3733.com/index/out/url'; // 中转页
      let mobileconfig_url = isConfigUrl
        ? appid
        : `https://d2.xz3733.com/h5/${appid}.mobileconfig`; // 下载链接 拼appid
      // let mobileconfig_url = `https://d2.xz3733.com/h5/65177.mobileconfig` // 测试游戏链接
      let mobileprovision_url = 'https://grq.3733.com/app.mobileprovision'; // 跳转系统设置页
      if (platform == 'iosBox') {
        BOX_openInBrowser({
          h5_url: `${jump_url}?url1=${mobileconfig_url}&url2=${mobileprovision_url}`,
        });
      } else {
        window.location.href = mobileconfig_url;
        if (iosSystem == 17) {
          // ios系统17版本无法自动跳转，所以要弹窗提示，让用户手动跳转授权配置文件
          setTimeout(() => {
            // 非套壳情况需要第二遍让其自动跳转
            useAddToDeskPopup(true);
          }, 1000);
        } else {
          setTimeout(() => {
            // 非套壳情况需要第二遍让其自动跳转
            window.location.href = mobileprovision_url;
          }, 1000);
        }
      }
    }, 200);
  }
}

export async function startH5Game(h5Url, appid) {
  // title-游戏名
  // 验证token是否过期
  await store.dispatch('user/SET_USER_INFO');
  if (!store.getters['user/userInfo'].token) {
    router.push({ name: 'PhoneLogin' });
  } else {
    store.commit('game/setH5GameUrl', h5Url);
    // store.commit('game/setH5GameUrl', `http://localhost:8081/h5g/?appid=${appid}`)
    store.commit('game/setH5gamePopup', 1);
    // router.push({ name: 'H5Game', params: { h5Url: h5Url } },);
  }
}

export function startCloudGame(id) {
  router.push({ name: 'CloudGame', params: { game_id: id } });
}

// 判断是否有安装支付方式
export function isInstallPayWay(payway) {
  if (
    (platform === 'android' || platform === 'androidBox') &&
    !isSdk &&
    authInfo.versionCode < 4190
  ) {
    if (
      payway == 'zfb_dmf' &&
      !BOX_checkInstall('com.eg.android.AlipayGphone')
    ) {
      Dialog.alert({
        message: '您尚未安装支付宝应用，请先安装支付宝才能进行支付。',
        lockScroll: false,
        confirmButtonText: i18n.t('我知道了'),
      });
      return false;
    }
    if (payway == 'wx' && !BOX_checkInstall('com.tencent.mm')) {
      Dialog.alert({
        message: '您尚未安装微信应用，请先安装微信才能进行支付。',
        lockScroll: false,
        confirmButtonText: i18n.t('我知道了'),
      });
      return false;
    }
  }
  return true;
}

export function pay() {
  const payWay = arguments[1].payWay;

  let isInstall = isInstallPayWay(payWay);
  if (!isInstall) {
    return false;
  }

  // 打开loading窗口
  const toast1 = Toast.loading({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });

  return new Promise((resolve, reject) => {
    // 请求支付接口
    request(...arguments)
      .then(res => {
        toast1.clear();
        let { param_url, alipay_url, url, html, qrSrcUrl, pay_info } = res.data;
        // 做支付接口成功处理
        switch (payWay) {
          case 'wx': // 微信支付 ----------------------------------------
          case 'quick_wxpay': // 微信快支付 ----------------------------------------
            if (param_url) {
              let msg = '您尚未安装微信应用，请先安装微信才能进行支付。';
              // window.location.href = param_url;
              let can_open = BOX_openInBrowser(
                { h5_url: param_url, msg: msg },
                { url: param_url, msg: msg },
              );
              if (can_open) {
                setTimeout(() => {
                  Dialog.confirm({
                    message: i18n.t('是否支付成功'),
                    confirmButtonText: i18n.t('是'),
                    cancelButtonText: i18n.t('否'),
                    lockScroll: false,
                  })
                    .then(() => {
                      // on confirm
                      resolve(res);
                    })
                    .catch(() => {
                      resolve(res);
                    })
                    .finally(() => {
                      store.dispatch('user/SET_USER_INFO');
                    });
                }, 3000);
              } else {
                reject(res);
              }
            } else if (url) {
              if (url) {
                // on confirm
                if (
                  (platform === 'android' || platform === 'androidBox') &&
                  !isSdk
                ) {
                  BOX_openInNewWindow(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                } else {
                  Dialog.confirm({
                    message: '微信需要跳转到外部支付，是否打开',
                    confirmButtonText: i18n.t('是'),
                    cancelButtonText: i18n.t('否'),
                    lockScroll: false,
                  })
                    .then(() => {
                      BOX_openInBrowser(
                        { h5_url: url, open_type: 1 },
                        { url: url },
                      );
                    })
                    .catch(() => {
                      resolve(res);
                    });
                }
                setTimeout(() => {
                  Dialog.confirm({
                    message: i18n.t('是否支付成功'),
                    confirmButtonText: i18n.t('是'),
                    cancelButtonText: i18n.t('否'),
                    lockScroll: false,
                  }).finally(() => {
                    resolve(res);
                    store.dispatch('user/SET_USER_INFO');
                  });
                }, 3000);
              }
              // window.location.href = url;
            }
            break;
          case 'zfb': // 支付宝支付 ------------------------------------------
            if (alipay_url) {
              useConfirmDialog({
                title: '温馨提示',
                desc: '即将前往充值页面完成充值',
                confirmText: '确定',
                onConfirm: () => {
                  BOX_openInBrowser(
                    { h5_url: alipay_url, open_type: 1 },
                    { url: alipay_url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    })
                      .then(() => {
                        // on confirm
                        resolve(res);
                      })
                      .catch(() => {
                        resolve(res);
                      })
                      .finally(() => {
                        store.dispatch('user/SET_USER_INFO');
                      });
                  }, 3000);
                },
              });
            }
            break;
          case 'zfb_dmf': // 支付宝支付当面付 ------------------------------------------
          case 'quick_alipay': //支付宝快支付 --------------------------------------------
            if (alipay_url) {
              let msg = '您尚未安装支付宝应用，请先安装支付宝才能进行支付。';
              let can_open = BOX_openInBrowser(
                { h5_url: alipay_url, msg: msg },
                { url: alipay_url, msg: msg },
              );
              if (can_open) {
                setTimeout(() => {
                  Dialog.confirm({
                    message: i18n.t('是否支付成功'),
                    confirmButtonText: i18n.t('是'),
                    cancelButtonText: i18n.t('否'),
                    lockScroll: false,
                  })
                    .then(() => {
                      // on confirm
                      resolve(res);
                    })
                    .catch(() => {
                      resolve(res);
                    })
                    .finally(() => {
                      store.dispatch('user/SET_USER_INFO');
                    });
                }, 3000);
              } else {
                reject(res);
              }
            } else if (html) {
              // 如果是有html（即支付宝h5支付类型）
              /* 此处form就是后台返回接收到的数据 */
              const div = document.createElement('div');
              div.setAttribute('class', 'yy-alipay');
              div.innerHTML = res.data.html;
              document.body.appendChild(div);
              document.forms[0].submit();
              setTimeout(() => {
                Dialog.confirm({
                  message: i18n.t('是否支付成功'),
                  confirmButtonText: i18n.t('是'),
                  cancelButtonText: i18n.t('否'),
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                    document.getElementsByClassName('yy-alipay')[0].remove();
                  });
              }, 3000);
            }
            break;
          case 'wx_ewm': // 微信二维码支付 ---------------------------------------
          case 'zfb_dmf_ewm': // 支付宝二维码支付 ------------------------------------
          case 'quick_ewm': // 微信/支付宝/云闪付二维码快支付 ----------------------------------------
            if (!qrSrcUrl) {
              Toast(i18n.t('暂不支持扫码支付'));
              return false;
            }
            setTimeout(() => {
              // 延时器保证二维码在下面的确认框上层(不然会被遮住)
              useEwmPopup({
                ewmSrc: qrSrcUrl,
                payInfo: pay_info,
                onClose: () => {
                  resolve(res);
                },
              });
            }, 0);
            break;
          case 'gold': // 金币抵扣 ---------------------------------------
          case 'ptb': // 平台币支付 ------------------------------------
            // 支付方式是平台币或者金币的话直接显示支付结果(isPay字段有值)并跳转支付成功页面
            Toast.success(i18n.t('支付成功'));
            resolve(res);
            break;
          case 'paypal': // paypal支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'paypal需要跳转到外部支付，是否打开',
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          case 'mycard': // mycard支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'mycard需要跳转到外部支付，是否打开',
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          case 'ysf':
            if (url) {
              Dialog.confirm({
                message: '云闪付需要跳转到外部支付，是否打开',
                confirmButtonText: i18n.t('是'),
                cancelButtonText: i18n.t('否'),
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: i18n.t('是否支付成功'),
                      confirmButtonText: i18n.t('是'),
                      cancelButtonText: i18n.t('否'),
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          default:
            if (url) {
              BOX_openInBrowser({ h5_url: url }, { url: url });
              setTimeout(() => {
                Dialog.confirm({
                  message: i18n.t('是否支付成功'),
                  confirmButtonText: i18n.t('是'),
                  cancelButtonText: i18n.t('否'),
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                  });
              }, 3000);
            }
            break;
        }
        return false;
      })
      .catch(res => {
        // 做支付接口失败处理
        reject(res);
        toast1.clear();
      });
  });
}

// 获取链接所带参数(根据key获取value)
/**
 *
 * @param {string | undefined} variable 获取查询字符串的key，可不传，不传即获取参数对象
 * @returns {string | object | false} 返回对应key的value, 或者返回整个对象, false表示没有查询字符串
 */
export function getQueryVariable(variable) {
  let query = window.location.search.substring(1);
  let vars = query.split('&');
  let obj = {};
  if (!vars.length) {
    return false;
  }
  // 没有参数的情况
  if (!variable) {
    for (let i = 0; i < vars.length; i++) {
      let pair = vars[i].split('=');
      obj[pair[0]] = pair[1];
    }
    return obj;
  } else {
    // 有参数，获取特定key的value
    for (let i = 0; i < vars.length; i++) {
      let pair = vars[i].split('=');
      if (pair[0] == variable) {
        return pair[1];
      }
    }
    return false;
  }
}
// 判断url是否含有参数
export function hasUrlQuery(url) {
  if (!url) {
    let query = window.location.search.substring(1);
    if (query) return true;
  } else {
    let index = url.indexOf('?');
    if (index != -1) return true;
  }
  return false;
}
// 日志打印
export function devLog() {
  if (
    process.env.NODE_ENV == 'development' ||
    h5Page.env == 'aa' ||
    h5Page.env == 'cc'
  ) {
    window.console.log(...arguments);
  }
}

// 一天中0时的时间戳
export function getDayZeroTimestamp(timestamp) {
  // 秒
  timestamp = Number(timestamp) * 1000;
  return new Date(new Date(timestamp).toLocaleDateString()).getTime();
}

// 点击banner的一项
export function clickBanner(banner) {
  switch (Number(banner.type)) {
    case 1:
      if (banner.game.is_up) {
        toPage('UpDetail', {
          id: banner.game.id,
          gameInfo: banner.game,
        });
      } else {
        BOX_goToGame(
          {
            params: {
              id: banner.game.id,
              gameInfo: banner.game,
            },
          },
          { id: banner.game.id },
        );
      }
      break;
    case 4:
      if (banner.extra.indexOf('activity.3733.com') > -1) {
        BOX_openInNewWindow(
          { name: 'Activity', params: { url: banner.extra } },
          { url: banner.extra },
        );
      } else if (banner.page_name) {
        BOX_openInNewNavWindow(
          { name: banner.page_name },
          { url: banner.extra },
        );
      } else {
        BOX_openInNewNavWindow({ h5_url: banner.extra }, { url: banner.extra });
      }
      break;
    case 30: // 捡漏
      BOX_showActivity({ name: 'Jianlou' }, { page: 'jl' });
      break;
    case 21: // 金币商城
      BOX_showActivity({ name: 'GoldMall' }, { page: 'jbsc' });
      break;
    case 9: // 签到
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'Welfare',
          type: 1,
        });
      } catch (e) {
        BOX_showActivity(
          { name: 'Welfare', params: { type: 1 } },
          { page: 'qd' },
        );
      }
      // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
      break;
    case 8: // 返利申请
      BOX_showActivity({ name: 'Rebate' }, { page: 'fl' });
      break;
    case 22: // 小号回收
      BOX_showActivity({ name: 'Recycle' }, { page: 'xhhs' });
      break;
    case 23: // 转游中心
      BOX_showActivity({ name: 'Zhuanyou' }, { page: 'zyzx' });
      break;
    case 13: // 金币转盘
      BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
      break;
    case 15: // 邀请赚佣金
      BOX_openInNewWindow(
        { name: 'Invite' },
        { url: `${window.location.origin}/#/invite` },
      );
      break;
    case 17: // 排行榜
      BOX_showActivity({ name: 'Rank' }, { page: 'phb' });
      break;
    case 18: // 新游首发
      BOX_showActivity({ name: 'NewGame' }, { page: 'xysf' });
      break;
    case 31: // 合集
      toPage('GameCollect', { id: banner.heji_id });
      break;
    case 32: // 游戏试玩
      BOX_showActivity({ name: 'GameTry' }, { page: 'yxsw' });
      break;
    case 33: // 游戏内测员
      BOX_showActivity({ name: 'GameTester' }, { page: 'yxncy' });
      break;
    default:
      break;
  }
}

/**
 * 设置App语言
 */

export function setLanguage() {
  let lang = store.getters['system/isHw'] ? 'zh_t' : 'zh_s';
  localStorage.setItem('lang', lang);
  i18n.locale = lang;
}

// h5处理消息
export function h5HandleMessage(e) {
  if (e.data.from === 'h5') {
    switch (e.data.type) {
      case 'exit':
        store.commit('game/setH5gamePopup', 0);
        break;
      case 'openurl':
        BOX_openInBrowser(
          {
            ...e.data.data,
            ...{ h5_url: e.data.data.url },
          },
          e.data.data,
        );
        break;
      case 'openInNewWindow':
        let temp = {};
        temp.name = e.data.data.name;
        if (e.data.data.params) {
          temp.params = e.data.data.params;
        }
        router.push(temp);
        break;
      case 'minimization':
        store.commit('game/setH5gamePopup', 2);
        break;
    }
  }
  if (e.data.from === 'webapp') {
    switch (e.data.type) {
      case 'showAdActivity':
        handleActionCode(e.data.data);
        break;
      case 'savePopupConfigSdk':
        h5PostAdPopupConfigMessage(e.data.data);
        break;
      case 'saveNowPopupConfigSdk':
        h5PostNowAdPopupConfigMessage(e.data.data);
        break;
      case 'removePopupConfig':
        localStorage.removeItem('POPUP_CONFIG_SDK');
        break;
      case 'removeNowPopupConfig':
        localStorage.removeItem('NOW_POPUP_CONFIG_SDK');
        break;
    }
  }
  if (e.data.from === 'ios') {
    switch (e.data.type) {
      case 'showAdActivity':
        handleActionCode(e.data.data);
        break;
      case 'savePopupConfigSdk':
        h5PostAdPopupConfigMessage(e.data.data);
        break;
      case 'saveNowPopupConfigSdk':
        h5PostNowAdPopupConfigMessage(e.data.data);
        break;
      case 'removePopupConfig':
        localStorage.removeItem('POPUP_CONFIG_SDK');
        break;
      case 'removeNowPopupConfig':
        localStorage.removeItem('NOW_POPUP_CONFIG_SDK');
        break;
    }
  }
  if (e.data.from === 'iosBox') {
    switch (e.data.type) {
      case 'showAdActivity':
        handleActionCode(e.data.data);
        break;
      case 'savePopupConfigSdk':
        h5PostAdPopupConfigMessage(e.data.data);
        break;
      case 'saveNowPopupConfigSdk':
        h5PostNowAdPopupConfigMessage(e.data.data);
        break;
      case 'removePopupConfig':
        localStorage.removeItem('POPUP_CONFIG_SDK');
        break;
      case 'removeNowPopupConfig':
        localStorage.removeItem('NOW_POPUP_CONFIG_SDK');
        break;
    }
  }
}

// H5初始化
export function h5PostMessage() {
  let data = {
    from: 'webapp',
    type: 'init',
    data: {
      f: BASEPARAMS.from,
      t: store.getters['user/userInfo'].token
        ? store.getters['user/userInfo'].token
        : '',
      u: BASEPARAMS.uuid,
      c: BASEPARAMS.channel,
    },
  };

  // ios媒体上报
  let medium_key = getQueryVariable('medium_key');
  let medium_from = getQueryVariable('medium_from');
  if (medium_key) {
    data.data.medium_key = medium_key;
  }
  if (medium_from) {
    data.data.medium_from = medium_from;
  }
  setTimeout(() => {
    let iframe = document.getElementById('h5-iframe');
    iframe.onload = function () {
      iframe.contentWindow.postMessage(data, '*');
    };
  }, 200);
}

// h5处理ad弹出信息
export function h5PostAdPopupConfigMessage(savaData) {
  if (savaData) {
    localStorage.setItem('POPUP_CONFIG_SDK', JSON.stringify(savaData));
  }
  let data = {
    from: 'webapp',
    type: 'postPopupConfig',
    data: localStorage.getItem('POPUP_CONFIG_SDK')
      ? JSON.parse(localStorage.getItem('POPUP_CONFIG_SDK'))
      : [],
  };
  setTimeout(() => {
    let iframe = document.getElementById('h5-iframe');
    iframe.contentWindow.postMessage(data, '*');
  }, 200);
}
// h5处理ad弹出信息
export function h5PostNowAdPopupConfigMessage(savaData) {
  if (savaData) {
    localStorage.setItem('NOW_POPUP_CONFIG_SDK', JSON.stringify(savaData));
  }
  let data = {
    from: 'webapp',
    type: 'postNowPopupConfig',
    data: localStorage.getItem('NOW_POPUP_CONFIG_SDK')
      ? JSON.parse(localStorage.getItem('NOW_POPUP_CONFIG_SDK'))
      : [],
  };
  setTimeout(() => {
    let iframe = document.getElementById('h5-iframe');
    iframe.contentWindow.postMessage(data, '*');
  }, 200);
}
// 公告跳转
export function jumpAnnouncement(item) {
  switch (item.type) {
    case 0:
      let id = item.other_id || item.id;
      BOX_openInNewWindow(
        {
          name: 'NoticeDetail',
          params: {
            id: id,
          },
        },
        {
          url: `https://${h5Page.env}game.3733.com/#/notice_detail/${id}`,
        },
      );
      break;
    case 1:
      let upId = item.other_id || item.id;
      BOX_openInNewWindow(
        {
          name: 'UpCollectionDetail',
          params: {
            id: upId,
          },
        },
        {
          url: `https://${h5Page.env}game.3733.com/#/up_collection_detail/${upId}`,
        },
      );
      break;
    case 2:
      let gameId = item.game_id || item.id;
      BOX_openInNewWindow(
        {
          name: 'GameExchangeCode',
          params: {
            game_id: gameId,
          },
        },
        {
          url: `https://${h5Page.env}game.3733.com/#/game_exchange_code/${gameId}`,
        },
      );
      break;
  }
}
// 添加、修改作弊码
export async function saveCheatCodeCallback(e) {
  try {
    let params = JSON.parse(e);
    const res = await ApiSimulatorSaveCheatCode({
      id: params.id,
      game_id: params.game_id,
      cheat_code: params.cheat_code,
      title: params.title,
    });
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'saveCheatCode',
      code: res.code,
      data: res.data,
    });
  } catch (error) {
    handleError('saveCheatCode', error);
  }
}

// 删除自定义作弊码
export async function delCheatCodeCallback(e) {
  try {
    const res = await ApiSimulatorDelCheatCode({ id: JSON.parse(e).id });
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'delCheatCode',
      code: res.code,
      data: JSON.parse(e),
    });
  } catch (error) {
    handleError('delCheatCode', error);
  }
}

// 查询用户是否 svip
export async function checkUserHasAnSvipCallback() {
  try {
    const res = await ApiUserMemIsSvip();
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'userMemIsSvip',
      code: res.code,
      data: res.data,
    });
  } catch (error) {
    handleError('userMemIsSvip', error);
  }
}

// 获取模拟器区域信息回调
export async function getSimulatorZoneInfoCallback(e) {
  try {
    const res = await ApiSimulatorInit({ id: JSON.parse(e).game_id });
    const data = { ...res.data };
    delete data.simulator_info.other_info;
    const simulatorInitInfo = { ...data, ...data.simulator_info };
    delete simulatorInitInfo.simulator_info;
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'simulatorInitInfo',
      code: res.code,
      data: { ...simulatorInitInfo },
    });
  } catch (error) {
    handleError('getSimulatorZoneInfo', error);
  }
}

// 改变模拟器游戏收藏状态
export async function updateEmulatorCollectStatusCallback(e) {
  try {
    const res = await ApiResourceCollect({
      classId: 3,
      sourceId: JSON.parse(e).game_id,
      status: JSON.parse(e).status,
    });
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'collectionStatus',
      code: res.code,
      data: res.data,
      msg: res.msg,
    });
  } catch (error) {
    handleError('collectionStatus', error);
  }
}

// 模拟器游戏上报时长
export async function simulatorGameReportTimeCallback(e) {
  try {
    const res = await ApiPlayingRecodePlayTime({
      game_id: JSON.parse(e).game_id,
      play_time: JSON.parse(e).play_time,
    });
    window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
      type: 'simulatorReportTimeStatus',
      code: res.code,
      data: res.data,
      msg: res.msg,
    });
  } catch (error) {
    handleError('simulatorReportTimeStatus', error);
  }
}

// 通用错误处理函数-模拟器游戏
function handleError(type, error) {
  const errorMsg = navigator.onLine
    ? error.msg || error.toString()
    : '网络已断开，请稍后再试噢~';

  window.webkit.messageHandlers.getAddCheatCodeRusult.postMessage({
    type,
    code: 0,
    msg: errorMsg,
    data: error,
  });
}

// 获取PC云游戏初始化信息（马甲包主动获取）
export async function getCloudSdkInitCallback(e) {
  try {
    const res = await ApiCloudSdkInit({ game_id: JSON.parse(e).game_id });
    if (res.code === -40) {
      // 时长不足
      store.commit('game/setDurationOverShow', true);
      return;
    }

    window.webkit.messageHandlers.goToPcCloudGame.postMessage({
      cloudSdkInit: { game_id: JSON.parse(e).game_id, ...res.data },
      userInfo: store.getters['user/userInfo'],
      cloud_member_url: h5Page.cloud_member_url,
      cloud_game_use_url: h5Page.cloud_game_use_url,
      cloud_member_scb_url: h5Page.cloud_member_scb_url,
      kefu_url: h5Page.kefu_url,
    });
  } catch (error) {
    pcGameHandleError(type, error);
  }
}

// 获取PC云游戏用户信息回调
export async function getPcCloudGameInfoCallback(isActive = false) {
  let type = !isActive ? 'pcCloudUserInfo' : 'activePcCloudUserInfo';
  try {
    const res = await ApiCloudUserInfo();
    window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
      type,
      code: res.code,
      data: { ...res.data },
    });
  } catch (error) {
    pcGameHandleError(type, error);
  }
}

// 改变游戏收藏状态
export async function updateGameCollectStatusCallback(e) {
  try {
    const res = await ApiResourceCollect({
      classId: 3,
      sourceId: JSON.parse(e).game_id,
      status: JSON.parse(e).status,
    });
    window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
      type: 'collectStatus',
      code: res.code,
      data: res.data,
      msg: res.msg,
    });
  } catch (error) {
    pcGameHandleError('collectStatus', error);
  }
}

/**
 * @function 云游戏游戏时长上报
 * @param {int} game_id 游戏id
 * @param {string} once_token 初始化接口返回的token
 * @param {string} game_name 游戏名称
 * @param {int} play_time 扣除时长
 * @param {int} report_type 游戏状态 0 正常游戏 1 关闭游戏 2 启动游戏
 */

export async function createPlayTimeReporterCallback(e) {
  try {
    const parsedData = JSON.parse(e);
    const playTime = parsedData.play_time || 0;
    const params = {
      game_id: parsedData.game_id,
      once_token: parsedData.once_token,
      game_name: parsedData.game_name,
      report_type: Number(parsedData.report_type),
    };

    if (playTime) {
      params.play_time = playTime;
    }

    const res = await ApiCloudSdkReportPlay(params);
    window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
      type: 'cloudSdkReportPlay',
      code: res.code,
      data: res.data,
      msg: res.msg,
    });
  } catch (error) {
    pcGameHandleError('cloudSdkReportPlay', error);
  }
}

// 云游戏-服务器列表
export async function getPriorityListInfoCallback(e) {
  try {
    const res = await ApiCloudSdkGetPriorityList({
      game_id: JSON.parse(e).game_id,
    });
    window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
      type: 'priorityList',
      code: res.code,
      data: res.data,
      msg: res.msg,
    });
  } catch (error) {
    pcGameHandleError('priorityList', error);
  }
}

// 云游戏-激励视频
export async function triggerIncentiveVideo(item) {
  try {
    window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
      type: 'incentiveVideo',
      data: item,
    });
  } catch (error) {
    pcGameHandleError('incentiveVideo', error);
  }
}

// 获取登录状态结果 token-登录失效
export async function tokenInvalidity(item) {
  if (isIosBox) {
    window.webkit.messageHandlers.getLoginStateResult.postMessage({
      type: 'tokenInvalidity',
      msg: item.msg,
      code: item.code,
    });
  }
}

// 通用错误处理函数-PC云游戏
function pcGameHandleError(type, error) {
  const errorMsg = navigator.onLine
    ? error.msg || error.toString()
    : '网络已断开，请稍后再试噢~';

  window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
    type,
    code: 0,
    msg: errorMsg,
    data: error,
  });
}

// 下载鲸鱼漫游
export const downJymyBtnCallback = item => {
  downloadGame(item);
};

/**
 * @function 根据游戏信息跳转到不同的详情页
 * @param {Object} gameInfo 游戏信息对象
 */
export function navigateToGameDetail(gameInfo) {
  if (
    gameInfo?.detailid &&
    [1, 2].includes(gameInfo?.detailid) &&
    platform == 'iosBox'
  ) {
    // 跳转到 PC云游 详情页
    // toPage('PcCloudGameDetail', {
    //   id: gameInfo.id,
    // });
    // 2025年3月25日17:20:34 整合pc云游戏详情页到常规游戏详情页
    BOX_goToGame(
      {
        params: {
          id: gameInfo.id,
          gameInfo: gameInfo,
        },
      },
      { id: gameInfo.id },
    );
  } else if (gameInfo.classid == 40) {
    // 跳转到 Up 详情页
    toPage('UpDetail', {
      id: gameInfo.id,
      gameInfo: gameInfo,
    });
  } else if (gameInfo.classid == 140 && platform == 'iosBox') {
    // 跳转到 模拟器游戏 详情页
    toPage('EmulatorGameDetail', {
      id: gameInfo.id,
    });
  } else {
    // 默认跳转到游戏详情页
    BOX_goToGame(
      {
        params: {
          id: gameInfo.id,
          gameInfo: gameInfo,
        },
      },
      { id: gameInfo.id },
    );
  }
}
