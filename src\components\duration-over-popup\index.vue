<template>
  <div class="duration-over-popup">
    <van-popup
      v-model="durationOverPopupShow"
      position="bottom"
      :lock-scroll="false"
      round
      @click-overlay="closePopup"
      class="duration-container-popup"
    >
      <div class="duration-container">
        <div class="exhibition-content">
          <div class="head">
            <div class="title">时长已耗尽...</div>
            <div class="close" @click="closePopup">
              <img
                src="~@/assets/images/cloud-game/time-arrow-left.png"
                alt=""
              />
            </div>
          </div>
          <div class="icon">
            <img src="~@/assets/images/games/time-over-img.png" alt="" />
            <div class="text">时长耗尽</div>
          </div>
        </div>
        <div class="open-content">
          <div class="desc">
            <div class="title1">开通会员赠海量时长</div>
            <div class="title2">开通会员排队优先，送海量会员时长</div>
          </div>
          <div class="btn" @click="toPage('CloudGameBuy')">立即开通</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'durationOverPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showAll: false,
    };
  },
  computed: {
    durationOverPopupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },

  watch: {},
  methods: {
    closePopup() {
      this.durationOverPopupShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.duration-container {
  display: flex;
  flex-direction: column;
  min-height: 303 * @rem;
  box-sizing: border-box;
  position: relative;
  background: #f3f5f9;
  justify-content: space-between;
  .exhibition-content {
    flex: 1;
    padding: 20 * @rem 20 * @rem 10 * @rem;
    .head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        height: 25 * @rem;
        font-weight: 500;
        font-size: 18 * @rem;
        color: #222222;
        line-height: 25 * @rem;
      }
      .close {
        width: 20 * @rem;
        height: 20 * @rem;
      }
    }
    .icon {
      height: 190 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 80 * @rem;
        height: 80 * @rem;
      }
      .text {
        margin-top: 16 * @rem;
        height: 18 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #666666;
      }
    }
  }

  .open-content {
    padding: 15 * @rem 14 * @rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 68 * @rem;
    background: #242526;
    background: linear-gradient(
      92deg,
      #fde9bf 0%,
      #fef7db 37%,
      #ffd69c 91%,
      #ffe6c2 98%
    );
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;

    border-image: linear-gradient(
        108deg,
        rgba(251, 221, 161, 1),
        rgba(251, 243, 193, 1),
        rgba(255, 201, 119, 1)
      )
      1 1;
    .desc {
      display: flex;
      flex-direction: column;
      .title1 {
        height: 19 * @rem;
        font-weight: 600;
        font-size: 15 * @rem;
        color: #772b21;
        line-height: 19 * @rem;
      }
      .title2 {
        margin-top: 5 * @rem;
        height: 14 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #772b21;
        line-height: 14 * @rem;
      }
    }
    .btn {
      height: 32 * @rem;
      background: linear-gradient(137deg, #98432b 0%, #772b21 100%);
      border-radius: 16 * @rem;
      font-weight: 500;
      font-size: 14 * @rem;
      color: #ffffff;
      padding: 7 * @rem 16 * @rem;
      border: 2px solid rgba(248, 189, 112, 0.55);
      box-sizing: border-box;
    }
  }
}
</style>
