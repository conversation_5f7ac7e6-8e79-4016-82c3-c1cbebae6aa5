<template>
  <div class="page search-game-page">
    <nav-bar-2 :title="$t('选择游戏')" :border="true"></nav-bar-2>
    <div class="search-container">
      <div class="search-bar">
        <div class="search-icon"></div>
        <form @submit.prevent="submitSearch(keyword)">
          <input
            type="text"
            class="input-text"
            v-model.trim="keyword"
            :placeholder="$t('请输入游戏名称进行查找')"
            :maxlength="15"
          />
        </form>
      </div>
    </div>
    <div class="main">
      <!-- 历史记录 -->
      <div class="history-container" v-if="!isResult">
        <content-empty
          v-if="!historyList.length"
          :tips="$t('请输入游戏名称进行查找')"
        ></content-empty>
        <template v-else>
          <div class="section-title">
            <div class="title-text">{{ $t('搜索历史') }}</div>
            <div class="clear-history btn" @click="clearHistory"></div>
          </div>

          <div class="history-list">
            <div class="history-item" v-for="item in historyList" :key="item">
              <div class="time-icon"></div>
              <div class="history-text btn" @click="submitSearch(item.trim())">
                {{ item }}
              </div>
              <div class="clear btn" @click="removeHistoryItem(item)"></div>
            </div>
          </div>
        </template>
      </div>

      <template v-else>
        <content-empty
          v-if="empty"
          :tips="$t('咦，什么也都找到哦~')"
        ></content-empty>
        <yy-list
          v-else
          class="yy-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
        >
          <div class="game-list">
            <div
              class="game-item btn"
              v-for="item in gameList"
              :key="item.id"
              @click="selectGame(item)"
            >
              <div class="game-icon">
                <img :src="item.titlepic" />
              </div>
              <div class="game-name">{{ item.title }}</div>
            </div>
          </div>
        </yy-list>
      </template>
    </div>
  </div>
</template>

<script>
import { ApiGameTitleHints } from '@/api/views/xiaohao.js';

export default {
  name: 'SearchGame',
  data() {
    return {
      keyword: '',
      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 30,
      timer: null,
      isResult: false,
      historyList: [],
    };
  },
  computed: {
    empty() {
      return !this.loadingObj.loading && this.finished && !this.gameList.length;
    },
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      if (val == '') {
        this.isResult = false;
        return;
      }

      this.timer = setTimeout(async () => {
        this.finished = false;
        this.gameList = [];
        this.loadingObj.loading = true;
        this.isResult = true;
        await this.getGameList();
        this.loadingObj.loading = false;
      }, 300);
    },
  },
  created() {
    this.updateHistory();
  },
  methods: {
    async submitSearch(keyword) {
      this.addHistoryItem(keyword);
      this.gameList = [];
      this.keyword = keyword;
      // if (this.keyword == keyword) {
      //   await this.getGameList();
      // }
    },
    clearHistory() {
      this.$dialog
        .confirm({
          message: this.$t('确认删除所有搜索记录吗？'),
          lockScroll: false,
        })
        .then(() => {
          localStorage.setItem('DEAL_HISTORY_LIST', '');
          this.updateHistory();
          this.$toast(this.$t('历史记录已删除'));
        });
    },
    removeHistoryItem(keyword) {
      if (!keyword) return;
      let index = this.historyList.findIndex(item => item == keyword);
      if (index != -1) {
        this.historyList.splice(index, 1);
        this.updateHistory(this.historyList);
      }
    },
    addHistoryItem(keyword) {
      if (!keyword) return;
      let index = this.historyList.findIndex(item => item == keyword);
      if (index != -1) {
        this.historyList.splice(index, 1);
      }
      this.historyList.unshift(keyword);
      if (this.historyList.length > 10) {
        this.historyList.pop();
      }
      this.updateHistory(this.historyList);
    },
    updateHistory(historyList) {
      if (historyList) {
        localStorage.setItem('DEAL_HISTORY_LIST', JSON.stringify(historyList));
      }

      let historyListStr = localStorage.getItem('DEAL_HISTORY_LIST');
      if (historyListStr) {
        this.historyList = JSON.parse(historyListStr);
      } else {
        this.historyList = [];
      }
    },
    selectGame(item) {
      this.toPage(this.$route.params.from, { info: item }, true);
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameTitleHints({
        keyword: this.keyword,
        page: this.page,
        listRows: this.listRows,
        all: 1,
      });
      this.$toast.clear();
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      this.gameList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.search-game-page {
  .search-container {
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 65 * @rem;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    .search-bar {
      box-sizing: border-box;
      width: 347 * @rem;
      height: 35 * @rem;
      background-color: #f6f6f6;
      border-radius: 18 * @rem;
      display: flex;
      align-items: center;
      padding: 0 17 * @rem;
      .search-icon {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat;
        background-size: 15 * @rem 15 * @rem;
      }
      form {
        border: 0;
        outline: 0;
        display: block;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .input-text {
        font-size: 15 * @rem;
        color: #333;
        flex: 1;
        min-width: 0;
        margin-left: 8 * @rem;
        background-color: transparent;
      }
    }
  }
  .main {
    margin-top: 65 * @rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    .history-container {
      .section-title {
        padding: 10 * @rem 14 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-text {
          font-size: 16 * @rem;
          color: #333333;
        }
        .clear-history {
          width: 30 * @rem;
          height: 30 * @rem;
          background: url(~@/assets/images/clear.png) center center no-repeat;
          background-size: 15 * @rem auto;
        }
      }
      .history-list {
        padding: 0 14 * @rem;
        .history-item {
          height: 30 * @rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10 * @rem;
          .time-icon {
            width: 15 * @rem;
            height: 15 * @rem;
            background: url(~@/assets/images/time-icon.png) center center
              no-repeat;
            background-size: 15 * @rem 15 * @rem;
          }
          .history-text {
            font-size: 14 * @rem;
            color: #666;
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
          }
          .clear {
            width: 30 * @rem;
            height: 30 * @rem;
            background: url(~@/assets/images/close-black.png) center center
              no-repeat;
            background-size: 10 * @rem auto;
            margin-left: 20 * @rem;
          }
        }
      }
    }
    .game-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 14 * @rem;
      .game-item {
        width: 48%;
        display: flex;
        align-items: center;
        margin-top: 20 * @rem;
        margin-left: 2%;
        &:nth-of-type(2n + 1) {
          margin-left: 0;
        }
        &:nth-of-type(-n + 2) {
          margin-top: 0;
        }
        .game-icon {
          width: 46 * @rem;
          height: 46 * @rem;
          border-radius: 12 * @rem;
          overflow: hidden;
        }
        .game-name {
          font-size: 13 * @rem;
          margin-left: 12 * @rem;
          color: #333333;
          flex: 1;
          min-width: 0;
          line-height: 16 * @rem;
        }
      }
    }
  }
}
</style>
