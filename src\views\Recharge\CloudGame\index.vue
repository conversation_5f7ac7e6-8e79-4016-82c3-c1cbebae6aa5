<template>
  <div class="cloud-game-buy-page">
    <nav-bar-2
      :title="$t('云会员')"
      :azShow="true"
      :placeholder="false"
      bgStyle="transparent"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
      <template #right>
        <div class="introduction" @click="goToCloudGameBuyList()">
          {{ $t('开通记录') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="user-info">
        <user-avatar class="avatar"></user-avatar>
        <div class="info">
          <div class="user-nickname">{{ userCloudInfo.nickname }}</div>
          <div class="vip-overtime" v-if="userCloudInfo.is_hmy_vip"
            >{{ $t('将于') }}{{ userCloudInfo.end_date }}到期</div
          >
          <div class="vip-overtime" v-else>{{
            $t('开通会员排队优先，快人一步')
          }}</div>
        </div>
        <div class="cloud-game-remain">
          <span>{{ $t('剩余时长') }}</span>
          <div class="remain-time">{{
            formatDuration(userCloudInfo.duration)
          }}</div>
        </div>
      </div>
      <div class="tab-btns">
        <div class="tab-btn" :class="{ active: tab == 0 }" @click="tab = 0">{{
          $t('会员卡')
        }}</div>
        <div class="tab-btn" :class="{ active: tab == 1 }" @click="tab = 1">{{
          $t('时长包')
        }}</div>
      </div>
      <div class="tab-content">
        <div class="vip-box section" v-if="tab == 0">
          <div class="vip-list">
            <div
              class="vip-item"
              :class="{ active: selectedVipMeal.id == vip.id }"
              v-for="(vip, index) in vipList"
              :key="index"
              @click="selectedVipMeal = vip"
            >
              <div class="vip-tips" v-if="vip.tips"
                ><span>{{ vip.tips }}</span></div
              >
              <div class="vip-title">{{ vip.title }}</div>
              <div class="vip-subtitle">{{ vip.subtitle }}</div>
              <div class="vip-price">
                <div
                  class="vip-amount"
                  :class="{ smallText: String(vip.amount).length > 5 }"
                  ><span>{{ unit }}</span
                  >{{ vip.amount }}</div
                >
                <div class="vip-show-amount" v-if="vip.show_amount"
                  ><span>{{ unit }}</span
                  >{{ vip.show_amount }}</div
                >
              </div>
              <div class="vip-text">{{ vip.text }}</div>
            </div>
          </div>
          <div class="tips">*{{ $t('重复购买将延长会员时间') }}</div>
        </div>
        <div class="duration-box section" v-if="tab == 1">
          <div class="duration-list">
            <div
              class="duration-item"
              :class="{ active: selectedDurationMeal.id == item.id }"
              v-for="(item, index) in durationList"
              :key="index"
              @click="selectedDurationMeal = item"
            >
              <div class="duration-tips" v-if="item.tips"
                ><span>{{ item.tips }}</span></div
              >
              <div class="duration-title">{{ item.title }}</div>
              <div class="duration-price">
                <div
                  class="duration-amount"
                  :class="{ smallText: String(item.amount).length > 5 }"
                  ><span>{{ unit }}</span
                  >{{ item.amount }}</div
                >
                <div class="duration-show-amount" v-if="item.show_amount"
                  ><span>{{ unit }}</span
                  >{{ item.show_amount }}</div
                >
              </div>
            </div>
          </div>
          <div class="tips">*{{ $t('重复购买将延长云玩时间') }}</div>
        </div>
      </div>
      <div class="welfare-box section">
        <div class="title">{{ $t('云会员特权') }}</div>
        <div class="welfare-list">
          <div
            class="welfare-item"
            v-for="(welfare, index) in welfareList"
            :key="index"
          >
            <img :src="welfare.icon" alt="" />
            <div class="text">
              <div class="welfare-title">{{ welfare.title }}</div>
              <div class="welfare-desc">{{ welfare.desc }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="buy-tips section">
        <div class="title">{{ $t('购买须知') }}</div>
        <div class="tips-content">
          <div
            class="tips-item"
            v-for="(tips, index) in text_list"
            :key="index"
          >
            {{ index + 1 }}.{{ tips }}
          </div>
        </div>
      </div>
      <div class="fixed-bottom" v-if="selectedMeal.amount">
        <div class="bg">
          <div class="pay-btn btn" @click="clickRechargeBtn"
            >支付{{ unit }} <span>{{ selectedMeal.amount }}</span></div
          >
        </div>
      </div>
    </div>
    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(this.selectedMeal.amount)"
      :unit="unit"
    ></pay-type-popup>
  </div>
</template>

<script>
import welfare0 from '@/assets/images/recharge/cloud-game/welfare-01.png';
import welfare1 from '@/assets/images/recharge/cloud-game/welfare-02.png';
import welfare2 from '@/assets/images/recharge/cloud-game/welfare-03.png';
import welfare3 from '@/assets/images/recharge/cloud-game/welfare-04.png';
import welfare4 from '@/assets/images/recharge/cloud-game/welfare-05.png';
import welfare5 from '@/assets/images/recharge/cloud-game/welfare-06.png';
import welfare6 from '@/assets/images/recharge/cloud-game/welfare-07.png';
import welfare7 from '@/assets/images/recharge/cloud-game/welfare-08.png';
import {
  ApiCloudUserInfo,
  ApiCloudPayBuyInfo,
  ApiCloudPayCreateOrder,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import { getQueryVariable } from "@/utils/function.js"
import { BOX_openInNewWindow, platform } from '@/utils/box.uni.js';
export default {
  name: 'CloudGame',
  data() {
    return {
      navbarOpacity: 0,
      selectedVipMeal: {},
      selectedDurationMeal: {},
      selectedMeal: {},
      orderType: 501,
      tab: 0,
      vipList: [],
      durationList: [],
      payList: [],
      selectedPayType: 'wx', // 支付方式
      payPopupShow: false,
      userCloudInfo: {},
      welfareList: [],
      text_list: [
        this.$t(
          '会员购买后立即生效，且将在购买后指定天数的同一时间过期，重复购买时长可叠加。',
        ),
        this.$t('会员在使用云服务器时，享有会员专属排队通道，可优先进入云玩。'),
        this.$t(
          '由于云玩成本高昂，平台目前仅收取提供技术服务和服务器资源的基础费用。',
        ),
        this.$t('云玩属于特殊虚拟商品，购买后无法进行退款。'),
        this.$t(
          '购买会员后，每日将额外获赠云玩限免时长，限免时长次日将会清空重置，且游戏时将会优先扣除限免时长。',
        ),
      ],
    };
  },
  computed: {
    unit() {
      return this.userInfo.is_hw ? '$' : '￥';
    },
  },
  watch: {
    tab(val) {
      if (val == 0) {
        this.selectedMeal = this.selectedVipMeal;
        this.orderType = 501;
      } else {
        this.selectedMeal = this.selectedDurationMeal;
        this.orderType = 502;
      }
      this.getPayMethod();
    },
    selectedVipMeal(val) {
      this.welfareList = [
        {
          title: `${this.$t('开通立送')}${val.duration || 0}${this.$t('小时')}`,
          desc: this.$t('购买即送免费时长'),
          icon: welfare0,
        },
        {
          title: `${this.$t('每日赠送')}${val.sign_duration || 0}${this.$t('分钟')}`,
          desc: this.$t('每日签到送限免时长'),
          icon: welfare1,
        },
        {
          title: this.$t('高清画质'),
          desc: this.$t('高清蓝光画质'),
          icon: welfare2,
        },
        {
          title: this.$t('快速通道'),
          desc: this.$t('会员排队优先'),
          icon: welfare3,
        },
        {
          title: this.$t('小窗服务'),
          desc: this.$t('会员专属小窗服务权益'),
          icon: welfare4,
        },
        {
          title: this.$t('会员游戏'),
          desc: this.$t('会员专属游戏体验'),
          icon: welfare5,
        },
        {
          title: this.$t('专属福利'),
          desc: this.$t('独享会员活动'),
          icon: welfare6,
        },
        {
          title: this.$t('纯净模式'),
          desc: this.$t('会员尊享免广告'),
          icon: welfare7,
        },
      ];
      this.selectedMeal = val;
    },
    selectedDurationMeal(val) {
      this.selectedMeal = val;
    },
  },
  async created() {
    this.tab = getQueryVariable("cgtab") ?? 0;
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  async activated() {
    await this.getUserInfo();
    await this.getCloudList();
    await this.getPayMethod();
  },
  methods: {
    async onResume() {
      await this.getUserInfo();
      await this.getCloudList();
      await this.getPayMethod();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let hour = this.addZero(Math.floor(timeStamp / 3600));
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return `${hour}时-${minute}分-${second}秒`;
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    async getCloudList() {
      const res = await ApiCloudPayBuyInfo();
      this.vipList = res.data.vip_list;
      this.durationList = res.data.duration_list;
      if (this.vipList.length) {
        this.selectedVipMeal =
          this.vipList.find((item, index) => {
            return item.is_recommend == 1;
          }) || this.vipList[0];              
      } else {
        this.selectedVipMeal = {};
      }

      if(this.durationList.length) {
        this.selectedDurationMeal =
          this.durationList.find((item, index) => {
            return item.is_recommend == 1;
          }) || this.durationList[0]; 
      } else {
        this.selectedDurationMeal = {};
      }

      setTimeout(() => {
          if (this.tab == 0) {
            this.selectedMeal = this.selectedVipMeal;
          } else {
            this.selectedMeal = this.selectedDurationMeal;
          }
        }, 0);
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: this.orderType,
      });
      this.payList = res.data;
    },
    clickRechargeBtn() {
      this.payPopupShow = true;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      const orderParams = {
        amount: this.selectedMeal.amount,
        config_id: this.selectedMeal.id,
        payWay: this.selectedPayType,
        order_type: this.orderType,
      };
      ApiCloudPayCreateOrder(orderParams).then(async orderRes => {
        await ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: this.orderType,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: this.orderType,
          });
        });
        await this.getUserInfo();
        await this.getCloudList();
      });
    },
    async getUserInfo() {
      const res = await ApiCloudUserInfo();
      this.userCloudInfo = res.data;
    },
    formatDuration(minute = 0) {
      if (minute) {
        let hour = Math.floor(minute / 60);
        let minutes = minute % 60;
        if (hour > 99999) {
          return `${hour}小时`;
        } else {
          return `${hour}小时${minutes}分`;
        }
      } else {
        return `0小时0分`;
      }
    },
    goToCloudGameBuyList() {
      this.$toast.clear();
      BOX_openInNewWindow(
        { name: 'CloudGameBuyList' },
        { url: `${window.location.origin}/#/cloud_game_buy_list` },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.cloud-game-buy-page {
  padding-top: 56 * @rem;
  padding-top: calc(56 * @rem + @safeAreaTop);
  padding-top: calc(56 * @rem + @safeAreaTopEnv);
  position: relative;

  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 220 * @rem;
    background: linear-gradient(
      168deg,
      #fff9ec 2%,
      #ffecc5 50%,
      #ffefcd 77%,
      #ffe7b4 100%
    );
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .introduction {
    color: rgba(106, 53, 9, 0.7);
    font-size: 13 * @rem;
  }

  .main {
    padding-bottom: 102 * @rem;

    .user-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20 * @rem 18 * @rem;

      .avatar {
        width: 40 * @rem;
        height: 40 * @rem;
        margin-right: 8 * @rem;
        border-radius: 50%;
        border: 2 * @rem solid rgba(192, 108, 24, 0.2);
        overflow: hidden;

        img {
          width: 40 * @rem;
          height: 40 * @rem;
          border-radius: 50%;
        }
      }

      .info {
        flex: 1;
        min-width: 0;

        .user-nickname {
          font-size: 15 * @rem;
          line-height: 19 * @rem;
          color: #4a2100;
          font-weight: bold;
        }
        .vip-overtime {
          font-size: 11 * @rem;
          line-height: 14 * @rem;
          color: rgba(106, 53, 9, 0.7);
          margin-top: 5 * @rem;
        }
      }

      .cloud-game-remain {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .remain-time {
          color: #4a2100;
          font-size: 18 * @rem;
          line-height: 23 * @rem;
          font-weight: bold;
          margin-top: 2 * @rem;
        }
        span {
          display: block;
          width: 59 * @rem;
          height: 17 * @rem;
          line-height: 17 * @rem;
          background: linear-gradient(-90deg, #ffa94c 0%, #f35805 100%);
          font-size: 11 * @rem;
          color: #fff;
          text-align: center;
          border-radius: 10 * @rem 12 * @rem 0 10 * @rem;
        }
      }
    }

    .tab-btns {
      display: flex;
      width: 100%;
      background: linear-gradient(186deg, #fff2e5 0%, #ffffff 100%);
      border: 1 * @rem solid #ffffff;
      border-bottom: none;
      border-radius: 30 * @rem 30 * @rem 0 0;
      box-sizing: border-box;
      position: relative;

      &::after {
        content: '';
        display: block;
        width: 100%;
        height: 20 * @rem;
        background: linear-gradient(to bottom, #fff2e500 0%, #ffffff 100%);
        position: absolute;
        bottom: 0 * @rem;
        left: 0;
      }

      .tab-btn {
        flex: 1;
        height: 57 * @rem;
        line-height: 57 * @rem;
        font-size: 15 * @rem;
        text-align: center;
        position: relative;
        color: rgba(34, 34, 34, 0.6);

        &.active {
          background: linear-gradient(
            180deg,
            #ffffff 0%,
            rgba(255, 255, 255, 0) 99%
          );
          box-shadow: 0 * @rem 4 * @rem 10 * @rem 0 * @rem
            rgba(234, 200, 119, 0.3);
          border-radius: 30 * @rem 30 * @rem 0 0;
          color: #222222;
          font-weight: bold;
        }
      }
    }

    .tab-content {
      position: relative;
      z-index: 1;
    }

    .section {
      padding: 0 18 * @rem;
      background-color: #fff;

      .title {
        font-size: 16 * @rem;
        line-height: 20 * @rem;
        font-weight: bold;
        margin-top: 26 * @rem;
        color: #111111;
      }
    }

    .vip-box {
      padding: 0 0 0 8 * @rem;

      .vip-list {
        display: flex;
        align-items: center;
        overflow-x: auto;
        padding: 15 * @rem 0 0 10 * @rem;
        &::-webkit-scrollbar {
          display: none;
        }

        &::after {
          content: '';
          display: block;
          width: 18 * @rem;
          height: 1 * @rem;
          flex-shrink: 0;
        }

        .vip-item {
          flex-shrink: 0;
          min-width: 149 * @rem;
          height: 124 * @rem;
          margin-right: 10 * @rem;
          border: 1 * @rem solid #f9f1de;
          border-radius: 8 * @rem;
          padding: 4 * @rem 8 * @rem 4 * @rem 15 * @rem;
          box-sizing: border-box;
          position: relative;
          background: #fffcf3;

          &:last-of-type {
            margin-right: 0;
          }

          &.active {
            background: linear-gradient(180deg, #ffe9be 0%, #fff9de 100%);
            border-color: #ffdd86;

            .vip-title {
              color: #87420b;
            }
            .vip-subtitle {
              color: rgba(158, 79, 15, 0.6);
            }
            .vip-price {
              color: #87420b;

              .vip-show-amount {
                color: rgba(158, 79, 15, 0.5);
              }
            }

            .vip-text {
              background-color: #ffe9be;
              color: #9e4f0f;
            }
          }

          &::before {
            content: '';
            display: block;
            width: 100%;
            height: 28 * @rem;
          }

          .vip-tips {
            display: block;
            max-width: 100%;
            height: 20 * @rem;
            line-height: 20 * @rem;
            padding: 0 6 * @rem 0 16 * @rem;
            position: absolute;
            top: -11 * @rem;
            left: 0;
            z-index: 1;
            background: linear-gradient(
              45deg,
              #ff5506 0%,
              #ff9138 78%,
              #ffbb5a 99%
            );
            border-radius: 0 6 * @rem 6 * @rem 0;
            color: #fff;
            font-size: 11 * @rem;
            box-sizing: border-box;

            &::before {
              content: '';
              display: block;
              width: 20 * @rem;
              height: 20 * @rem;
              background: url(~@/assets/images/recharge/cloud-game/tip-bg.png)
                no-repeat;
              background-size: 20 * @rem 20 * @rem;
              position: absolute;
              top: -2 * @rem;
              left: -5 * @rem;
            }

            span {
              display: block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .vip-title {
            width: calc(100% - 19 * @rem);
            height: 20 * @rem;
            font-size: 14 * @rem;
            line-height: 20 * @rem;
            font-weight: bold;
            margin-top: 8 * @rem;
            color: #222;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: absolute;
            top: 4 * @rem;
            left: 15 * @rem;
          }
          .vip-subtitle {
            width: 100%;
            height: 15 * @rem;
            font-size: 11 * @rem;
            line-height: 15 * @rem;
            margin-top: 2 * @rem;
            color: rgba(34, 34, 34, 0.6);
            overflow: hidden;
            white-space: nowrap;
          }
          .vip-price {
            display: flex;
            align-items: center;
            height: 35 * @rem;
            line-height: 35 * @rem;
            flex-wrap: wrap;
            color: #222;
            margin-top: 12 * @rem;
            overflow: hidden;

            .vip-amount {
              display: flex;
              align-items: center;
              height: 35 * @rem;
              line-height: 35 * @rem;
              font-size: 25 * @rem;
              font-weight: bold;

              &.smallText {
                font-size: 22 * @rem;
              }

              span {
                line-height: 20 * @rem;
                font-size: 14 * @rem;
                font-weight: bold;
                margin-top: 3 * @rem;
              }
            }

            .vip-show-amount {
              line-height: 17 * @rem;
              font-size: 12 * @rem;
              text-decoration: line-through;
              margin-left: 2 * @rem;
              margin-top: 6 * @rem;
              color: rgba(34, 34, 34, 0.5);
            }
          }
          .vip-text {
            width: calc(100% + 15 * @rem);
            height: 23 * @rem;
            line-height: 23 * @rem;
            margin: 0 -4 * @rem 0 -11 * @rem;
            background: #faf0d9;
            border-radius: 8 * @rem 8 * @rem 8 * @rem 8 * @rem;
            font-size: 11 * @rem;
            color: rgba(34, 34, 34, 0.6);
            box-sizing: border-box;
            padding: 0 5 * @rem;
          }
        }
      }

      .tips {
        color: #9a9a9a;
        font-size: 11 * @rem;
        line-height: 15 * @rem;
        margin-top: 8 * @rem;
        margin-left: 10 * @rem;
      }
    }

    .duration-box {
      padding: 0 0 0 8 * @rem;
      .duration-list {
        display: flex;
        align-items: center;
        overflow-x: auto;
        padding: 15 * @rem 0 0 10 * @rem;

        &::-webkit-scrollbar {
          display: none;
        }

        &::after {
          content: '';
          display: block;
          width: 18 * @rem;
          height: 1 * @rem;
          flex-shrink: 0;
        }

        .duration-item {
          flex-shrink: 0;
          min-width: 133 * @rem;
          height: 93 * @rem;
          margin-right: 10 * @rem;
          border: 1 * @rem solid #f9f1de;
          background-color: #fffcf3;
          border-radius: 8 * @rem;
          padding: 12 * @rem 15 * @rem;
          position: relative;
          box-sizing: border-box;

          &:last-of-type {
            margin-right: 0 * @rem;
          }

          &.active {
            background: linear-gradient(180deg, #ffe9be 0%, #fff9de 100%);
            border: 1 * @rem solid #ffdd86;

            .duration-title {
              color: #87420b;
            }
            .duration-price {
              color: #87420b;

              .duration-show-amount {
                color: rgba(158, 79, 15, 0.5);
              }
            }
          }
          &::before {
            content: '';
            display: block;
            width: 100%;
            height: 20 * @rem;
          }
          .duration-tips {
            display: block;
            max-width: 100%;
            height: 20 * @rem;
            line-height: 20 * @rem;
            padding: 0 6 * @rem 0 16 * @rem;
            position: absolute;
            top: -11 * @rem;
            left: 0;
            z-index: 1;
            background: linear-gradient(
              45deg,
              #ff5506 0%,
              #ff9138 78%,
              #ffbb5a 99%
            );
            border-radius: 0 6 * @rem 6 * @rem 0;
            color: #fff;
            font-size: 11 * @rem;
            box-sizing: border-box;

            &::before {
              content: '';
              display: block;
              width: 20 * @rem;
              height: 20 * @rem;
              background: url(~@/assets/images/recharge/cloud-game/tip-bg.png)
                no-repeat;
              background-size: 20 * @rem 20 * @rem;
              position: absolute;
              top: -2 * @rem;
              left: -5 * @rem;
            }

            span {
              display: block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .duration-title {
            color: #222;
            font-size: 14 * @rem;
            line-height: 20 * @rem;
            font-weight: bold;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            width: calc(100% - 30 * @rem);
            top: 12 * @rem;
            left: 15 * @rem;
            position: absolute;
          }
          .duration-price {
            display: flex;
            align-items: center;
            height: 35 * @rem;
            line-height: 35 * @rem;
            color: #222;
            margin-top: 14 * @rem;
            overflow: hidden;

            .duration-amount {
              display: flex;
              align-items: center;
              font-size: 25 * @rem;
              font-weight: bold;

              &.smallText {
                font-size: 22 * @rem;
              }

              span {
                font-size: 14 * @rem;
                font-weight: bold;
                line-height: 20 * @rem;
                margin-top: 3 * @rem;
              }
            }

            .duration-show-amount {
              line-height: 17 * @rem;
              color: rgba(34, 34, 34, 0.5);
              font-size: 12 * @rem;
              text-decoration: line-through;
              margin-left: 2 * @rem;
              margin-top: 6 * @rem;
            }
          }
        }
      }
      .tips {
        color: #9a9a9a;
        font-size: 11 * @rem;
        line-height: 15 * @rem;
        margin-top: 8 * @rem;
        margin-left: 10 * @rem;
      }
    }

    .welfare-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .welfare-item {
        width: 164 * @rem;
        height: 53 * @rem;
        display: flex;
        align-items: center;
        background: linear-gradient(178deg, #fefdf8 0%, #fcf9f0 99%);
        border-radius: 7 * @rem;
        padding: 11 * @rem 10 * @rem;
        box-sizing: border-box;
        margin-top: 12 * @rem;

        img {
          width: 31 * @rem;
          height: 31 * @rem;
          margin-right: 8 * @rem;
        }
        .text {
          flex: 1;
          min-width: 0;

          .welfare-title {
            font-size: 13 * @rem;
            line-height: 16 * @rem;
            color: #000;
          }
          .welfare-desc {
            font-size: 10 * @rem;
            margin-top: 4 * @rem;
            color: #9a9a9a;
          }
        }
      }
    }

    .buy-tips {
      .tips-content {
        margin-top: 12 * @rem;
        .tips-item {
          font-size: 12 * @rem;
          line-height: 17 * @rem;
          margin-bottom: 10 * @rem;
          color: #8a848a;
        }
      }
    }

    .fixed-bottom {
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 99;
      width: 100%;
      padding: 14 * @rem 18 * @rem 20 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      box-shadow: 0 * @rem 6 * @rem 30 * @rem 0 * @rem rgba(0, 0, 0, 0.06);

      .bg {
        width: 100%;
        padding: 1 * @rem;
        box-sizing: border-box;
        background: linear-gradient(
          272deg,
          rgba(239, 207, 137, 1),
          rgba(251, 243, 193, 1),
          rgba(246, 226, 179, 1)
        );
        border-radius: 10 * @rem;
      }

      .pay-btn {
        width: 100%;
        height: 47 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(
          92deg,
          #ffefb9 0%,
          #fef7db 37%,
          #ffe29f 87%,
          #fae7c3 99%
        );
        border-radius: 9 * @rem;
        font-size: 17 * @rem;
        font-weight: bold;
        line-height: 24 * @rem;
        color: #712203;
        text-align: center;

        span {
          font-weight: bold;
          font-size: 20 * @rem;
        }
      }
    }
  }
}
</style>
