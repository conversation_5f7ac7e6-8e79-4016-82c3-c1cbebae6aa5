<template>
  <van-popup
    v-model="popupShow"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    position="bottom"
    round
    get-container="body"
    class="xh-list-popup"
  >
    <div class="title">我的小号</div>
    <div class="close-btn" @click="popupShow = false"></div>
    <div class="popup-content">
      <div class="left">
        <div class="pic">
          <img :src="info.titlepic" alt="" />
        </div>
      </div>
      <div class="right">
        <div class="game-title">{{ info.main_title }}</div>
        <div class="list">
          <yy-list
            class="yy-list"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
          >
            <div class="xiaohao-list">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="selectXh(item)"
              >
                <div class="info">
                  <div class="xh-title">
                    <span>{{ item.nickname }}</span>
                    <div class="status" v-if="item.id == lastLogin.id"
                      >最近游戏</div
                    >
                  </div>
                  <div class="xh-info" v-if="item.role_info">
                    <div class="info-item">{{
                      item.role_info.role_area_name
                    }}</div>
                    <div class="info-item role-name">{{
                      item.role_info.role_name
                    }}</div>
                    <div class="info-item"
                      >{{ item.role_info.role_level }}级</div
                    >
                  </div>
                </div>
                <div
                  class="select"
                  :class="{
                    selected: selectedXh.id == item.id,
                  }"
                ></div>
              </div>
            </div>
          </yy-list>
        </div>
      </div>
    </div>
    <div
      class="submit-btn"
      :class="{ disabled: !selectedXh.id }"
      @click="setDefaultXh"
      >设为默认登录小号</div
    >
  </van-popup>
</template>
<script>
import {
  ApiXiaohaoMyList,
  ApiXiaohaoSetXhDefault,
} from '@/api/views/xiaohao.js';
export default {
  name: 'MycycleXhPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      required: true,
    },
    info: {
      required: true,
    },
  },
  data() {
    return {
      xiaohaoList: [],
      lastLogin: {},
      selectedXh: {},
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },
  methods: {
    async getXiaohaoList() {
      const res = await ApiXiaohaoMyList({
        appId: this.id,
        page: this.page,
        listRows: this.listRows,
        getRole: 1,
      });

      if (!res.data.list.length) {
        this.empty = true;
        this.tips = res.msg;
      } else {
        this.xiaohaoList = res.data.list;
        this.lastLogin = res.data.last_login;
        this.selectedXh = this.xiaohaoList[0];
      }
      this.loading = false;
      this.finished = true;
    },
    async onRefresh() {
      await this.getXiaohaoList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (this.finished) return false;
      await this.getXiaohaoList();
      this.loading = false;
    },
    selectXh(item) {
      this.selectedXh = item;
    },
    async setDefaultXh() {
      await ApiXiaohaoSetXhDefault({
        appId: this.info.app_id,
        xhId: this.selectedXh.id,
      });
      // await this.getXiaohaoList();
    },
  },
};
</script>
<style lang="less" scoped>
.xh-list-popup {
  display: flex;
  flex-direction: column;
  background-color: #fbfbfe;
  box-sizing: border-box;
  height: 60vh;

  .title {
    padding: 26 * @rem 0 24 * @rem;
    text-align: center;
    height: 22 * @rem;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #000000;
    line-height: 22 * @rem;
  }

  .close-btn {
    display: block;
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/my-game/close.png) no-repeat center;
    background-size: 16 * @rem 16 * @rem;
    padding: 10 * @rem;
    position: absolute;
    top: 15 * @rem;
    right: 8 * @rem;
  }

  .popup-content {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    display: flex;

    .left {
      flex-shrink: 0;
      width: 78 * @rem;
      background-color: #f2f4f5;
      border-top-right-radius: 10 * @rem;

      .pic {
        margin: 14 * @rem auto 0;
        width: 42 * @rem;
        height: 42 * @rem;
      }
    }

    .right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      padding: 0 14 * @rem 74 * @rem;

      .game-title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        width: 100%;
        height: 48 * @rem;
        line-height: 1;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #222222;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .list {
        flex: 1;
        min-height: 0;
        overflow: auto;
      }
    }

    .xiaohao-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 58 * @rem;
      border-radius: 10 * @rem;
      background-color: #fff;
      border: 0.5 * @rem solid #f0f0f0;
      margin-bottom: 8 * @rem;
      padding: 0 8 * @rem;
      box-sizing: border-box;

      .info {
        flex: 1;
        min-width: 0;

        .xh-title {
          display: flex;
          align-items: center;

          span {
            display: block;
            height: 16 * @rem;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #111111;
            line-height: 16 * @rem;
            text-align: left;
            text-transform: none;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .status {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            margin-left: 4 * @rem;
            background-color: #ecfbf4;
            color: #32b768;
            padding: 0 3 * @rem;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 10 * @rem;
            line-height: 15 * @rem;
            text-align: center;
          }
        }

        .xh-info {
          display: flex;
          margin-top: 5 * @rem;
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #9a9a9a;
          line-height: 17 * @rem;
          overflow: hidden;

          .info-item {
            flex-shrink: 0;
            margin-right: 10 * @rem;

            &.role-name {
              max-width: 70 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
      .select {
        width: 20 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/my-game/select.png) no-repeat center;
        background-size: 20 * @rem 20 * @rem;
        margin-left: 10 * @rem;

        &.disabled {
          background-image: url(~@/assets/images/my-game/select-disabled.png);
        }

        &.selected {
          background-image: url(~@/assets/images/my-game/selected.png);
        }
      }
    }
  }

  .submit-btn {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 339 * @rem;
    height: 44 * @rem;
    border-radius: 29 * @rem;
    line-height: 44 * @rem;
    background: @themeBg;
    font-weight: 500;
    font-size: 15 * @rem;
    color: #ffffff;
    text-align: center;
    margin: 20 * @rem auto 0;
    position: absolute;
    bottom: 10 * @rem;
    left: 50%;
    transform: translateX(-50%);

    &.disabled {
      background: #dcdcdc;
    }
  }
}
</style>
