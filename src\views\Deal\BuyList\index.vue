<template>
  <div class="buy-list-page">
    <div class="top-bar">
      <div class="top-first">
        <div
          class="search-container btn"
          v-if="!gameInfo.id"
          @click="toSearchGame"
        >
          <div class="search-icon"></div>
          <div class="search-text">{{ $t('搜索游戏') }}</div>
        </div>
        <div class="search-game btn" v-else @click="clearGame">
          <div class="close-icon"></div>
          <div class="game-name">{{ gameInfo.title }}</div>
        </div>
        <div
          class="switch-show btn"
          :class="{ waterfall: showWaterfall }"
          @click="showWaterfall = !showWaterfall"
        ></div>
      </div>
      <div class="filter-container">
        <van-dropdown-menu :active-color="themeColorLess">
          <van-dropdown-item
            v-model="order_value"
            :options="order_list"
            get-container="body"
            :lock-scroll="false"
            @change="onChange"
          />
          <van-dropdown-item
            v-model="type_value"
            :options="type_list"
            get-container="body"
            :lock-scroll="false"
            @change="onChange"
          />
          <van-dropdown-item
            v-model="platform_value"
            :options="platform_list"
            @change="onChange"
            get-container="body"
            :lock-scroll="false"
          />
        </van-dropdown-menu>
      </div>
    </div>
    <content-empty v-if="empty" :tips="$t('暂无游戏发布')"></content-empty>
    <yy-list
      v-else
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <buy-list-normal :list="buyList" v-if="showWaterfall"></buy-list-normal>
      <buy-list-waterfall :list="buyList" v-else></buy-list-waterfall>
    </yy-list>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import {
  ApiXiaohaoTradeList,
  ApiGameCateForTrade,
} from '@/api/views/xiaohao.js';
import buyListNormal from '../components/buy-list-normal';
import buyListWaterfall from '../components/buy-list-waterfall';
import { isIos } from '@/utils/userAgent.js';
export default {
  name: 'BuyList',
  components: {
    buyListNormal,
    buyListWaterfall,
  },
  data() {
    return {
      themeColorLess,
      order_value: 1,
      type_value: 0,
      platform_value: isIos ? 12 : 11,
      order_list: [
        {
          text: this.$t('最新发布'),
          value: 1,
        },
        {
          text: this.$t('价格最低'),
          value: 2,
        },
        {
          text: this.$t('价格最高'),
          value: 3,
        },
      ],
      type_list: [
        {
          text: this.$t('全部类型'),
          value: 0,
        },
      ],
      platform_list: [
        {
          text: this.$t('全平台'),
          value: 0,
        },
        {
          text: this.$t('安卓'),
          value: 11,
        },
        {
          text: 'iOS',
          value: 12,
        },
      ],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      buyList: [],
      showWaterfall: false,
      page: 1,
      listRows: 10,
      gameInfo: {},
      empty: false,
    };
  },
  async created() {
    await this.getCate();
  },
  activated() {
    let info = this.$route.params.info;
    if (info) {
      this.gameInfo = info;
      this.getBuyList();
    } else {
      this.gameInfo = {};
      this.getBuyList();
    }
  },
  methods: {
    clearGame() {
      this.gameInfo = {};
      this.getBuyList();
    },
    toSearchGame() {
      this.toPage('SearchGame', {
        from: 'Deal',
      });
    },
    async onChange() {
      this.buyList = [];
      this.page = 1;
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getBuyList();
      this.loadingObj.loading = false;
      this.$toast.clear();
    },
    async getCate() {
      const res = await ApiGameCateForTrade();
      let arr = res.data.map(item => {
        return {
          text: item.title,
          value: item.id,
        };
      });
      this.type_list.push(...arr);
    },
    async getBuyList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let params = {
        isDone: 0,
        page: this.page,
        listRows: this.listRows,
        order: this.order_value,
        deviceFrom: this.platform_value,
      };
      if (this.type_value) {
        params = { ...params, cateType: this.type_value };
      }
      if (this.gameInfo.id) {
        params = { ...params, gameId: this.gameInfo.id };
      }
      const res = await ApiXiaohaoTradeList(params);
      if (action === 1 || this.page === 1) {
        this.buyList = [];
      }
      this.buyList.push(...res.data.list);
      if (this.buyList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getBuyList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.buyList.length) {
        await this.getBuyList();
      } else {
        await this.getBuyList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>
<style lang="less">
.van-dropdown-item {
  z-index: 3000;
}
</style>
<style lang="less" scoped>
.buy-list-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top-bar {
    padding: 0 14 * @rem;
    height: 70 * @rem;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .top-first {
      display: flex;
      align-items: center;
      height: 30 * @rem;
    }
    .search-container {
      box-sizing: border-box;
      padding: 10 * @rem;
      flex: 1;
      min-width: 0;
      height: 30 * @rem;
      border-radius: 15 * @rem;
      border: 1px solid #e5e5e5;
      display: flex;
      align-items: center;
      .search-icon {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/search-icon.png) no-repeat;
        background-size: 15 * @rem 15 * @rem;
      }
      .search-text {
        font-size: 13 * @rem;
        color: #999999;
        margin-left: 8 * @rem;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .search-game {
      box-sizing: border-box;
      padding: 0 10 * @rem;
      display: flex;
      flex: 1;
      min-width: 0;
      height: 30 * @rem;
      display: flex;
      align-items: center;
      border-radius: 15 * @rem;
      border: 1px solid #e5e5e5;
      .close-icon {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/deal/clear-game.png) no-repeat;
        background-size: 15 * @rem 15 * @rem;
      }
      .game-name {
        font-size: 13 * @rem;
        color: #60c055;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 4 * @rem;
      }
    }
    .filter-container {
      height: 23 * @rem;
      margin-top: 5 * @rem;
    }
    .switch-show {
      width: 16 * @rem;
      height: 13 * @rem;
      padding: 10 * @rem 5 * @rem;
      background: url(~@/assets/images/deal/change-style.png) center center
        no-repeat;
      background-size: 16 * @rem 13 * @rem;
      margin-left: 12 * @rem;
      &.waterfall {
        background-image: url(~@/assets/images/deal/change-style-waterfall.png);
        background-size: 17 * @rem 17 * @rem;
      }
    }
  }
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }
  /deep/ .van-dropdown-item {
    position: absolute;
    top: 70 * @rem !important;
    height: calc(100vh - 164 * @rem - @safeAreaTop);
    height: calc(100vh - 164 * @rem - @safeAreaTopEnv);
    z-index: 3000;
  }
  /deep/ .van-dropdown-menu__bar {
    box-shadow: unset;

    height: 23 * @rem;
    .van-dropdown-menu__title--active::after {
      border-color: transparent transparent @themeColor @themeColor;
    }
  }
  /deep/ .van-dropdown-menu__item {
    margin-left: 12 * @rem;
    background: #f1f1f1;
    border-radius: 13 * @rem;
    &:nth-of-type(1) {
      margin-left: 0;
    }
  }
  /deep/ .van-dropdown-menu__title {
    position: relative;
    box-sizing: border-box;
    max-width: 100%;
    padding: 0 8px;
    color: #333333;
    font-size: 12px;
    line-height: 22px;
  }
  /deep/ .van-dropdown-menu__title::after {
    border-color: transparent transparent #5d5d5d #5d5d5d;
  }
}
</style>
