<template>
  <div class="my-up">
    <nav-bar-2
      :title="'我的资源'"
      :placeholder="false"
      :border="true"
      class="nav"
    >
    </nav-bar-2>
    <div class="explain">
      <span>注意：</span
      >请勿分析违法违规/色请暴力资源,资源发布后，如收到投诉，我方会第一时间将您的资源删除，敬请谅解。
    </div>
    <div class="list">
      <div v-for="(item, index) in list" class="item">
        <img :src="item.apk_icon" class="game-img" />
        <div class="text">{{ item.file_name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { ApiGetSelfUpGame } from '@/api/views/users';

export default {
  data() {
    return {
      list: [],
    };
  },
  async created() {
    const res = await ApiGetSelfUpGame();
    this.list = res.data.list;
  },
};
</script>
<style lang="less" scoped>
.my-up {
  margin-top: 50 * @rem;
}
.explain {
  padding: 10 * @rem 18 * @rem;
  font-size: 15 * @rem;
  line-height: 21 * @rem;
  span {
    color: @themeColor;
  }
}
.list {
  .item {
    padding: 10 * @rem 18 * @rem;
    display: flex;
    overflow: hidden;
    align-items: center;
    .game-img {
      width: 64 * @rem;
      height: 64 * @rem;
    }
    .text {
      margin-left: 10 * @rem;
    }
  }
}
</style>
