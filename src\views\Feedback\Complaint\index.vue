<template>
  <div class="complaint">
    <nav-bar-2 :border="true" :title="$t('投诉反馈')" :azShow="true">
      <template #right>
        <div class="my-feedback" @click="toPage('MyComplaint')">
          {{ $t('我的投诉') }}
        </div>
      </template>
    </nav-bar-2>
    <main>
      <div class="top-tab">
        <div class="container">
          <div class="left" @click="toPage('FeedbackGame', {}, 1)">
            {{ $t('游戏反馈') }}
          </div>
          <div class="right on">{{ $t('举报投诉') }}</div>
        </div>
      </div>
      <form>
        <div class="text">
          <input
            v-model="object"
            type="text"
            :placeholder="$t('投诉对象 （必填，如：XX部门-小花）')"
          />
        </div>
        <div class="text">
          <input
            v-model="object_tel"
            type="text"
            :placeholder="$t('投诉对象联系方式 （必填，如：电话1380013800）')"
          />
        </div>
        <div class="text">
          <input
            type="text"
            v-model="user"
            :placeholder="$t('投诉人 （选填，如：花先生）')"
          />
        </div>
        <div class="text">
          <input
            type="text"
            v-model="user_tel"
            :placeholder="$t('投诉人联系方式 （选填，如：微信13800138000）')"
          />
        </div>
        <div class="text">
          <input
            type="text"
            v-model="content"
            :placeholder="$t('填写投诉的具体内容，下方可添加图片（必填）')"
          />
        </div>
        <ul class="image-list">
          <van-uploader
            v-model="fileList"
            :after-read="afterRead"
            @delete="deletePic"
            :max-count="3"
          />
        </ul>
      </form>
      <div class="prompt">
        <div class="title">{{ $t('温馨提示') }}</div>
        <div class="content">
          {{ $t('1、受理对') }}<span id="app-name-1">{{ $t('本平台') }}</span
          >{{
            $t(
              '员工或其他关联企业工作中的失职、营私舞弊、弄虚作假等违反职业道德准则的行为的投诉举报。',
            )
          }}
        </div>
        <div class="content">
          {{
            $t(
              '2、对举报人信息进行严格保密是我们最基础职责，并要求被举报者不得对举报人实行打击报复行为，一经发现，严肃处理。',
            )
          }}
        </div>
        <div class="content">
          {{
            $t(
              '3、我们鼓励使用自己真实姓名进行实名举报，优先办理实名举报并及时反馈受理情况及通报处理结果。',
            )
          }}
        </div>
        <div class="content">
          {{ $t('4、游戏或平台相关的问题，请勿在此提交，可联系客服解决。') }}
        </div>
      </div>
    </main>
    <div class="bottom">
      <div class="button btn" @click="upload">{{ $t('确认提交') }}</div>
    </div>
  </div>
</template>
<script>
import { ApiComplainFeedbackPost } from '@/api/views/feedback';
import { ApiUploadImage } from '@/api/views/system';
import { checkEmail, checkIphone, checkQQ } from '@/utils/formValidation';
import md5 from 'js-md5';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'FeedbackComplaint',
  data() {
    return {
      object: '', //投诉对象
      object_tel: '', //投诉对象联系方式,
      user: '', //投诉人
      user_tel: '', //投诉人联系方式
      content: '', //投诉具体内容
      fileList: [], //图片列表
      imageList: [], //上传的图片列表
    };
  },
  created() {
    if (['android', 'ios'].includes(platform)) {
      document.title = this.$t('投诉反馈');
      // 因为反馈和投诉两个页面是replace的，在安卓里返回按钮都要关掉窗口
      window.sessionStorage.firstUrl = window.location.href;
    }
  },
  methods: {
    toPage(name, params = {}, replace = 0) {
      replace
        ? this.$router.replace({ name: name, params: params })
        : this.$router.push({ name: name, params: params });
    },
    async upload() {
      if (!this.object) {
        this.$toast(this.$t('投诉对象不能为空'));
        return false;
      }
      if (!this.object_tel) {
        this.$toast(this.$t('投诉对象联系方式不能为空'));
        return false;
      }
      if (
        !checkEmail(this.object_tel) &&
        !checkIphone(this.object_tel) &&
        !checkQQ(this.object_tel)
      ) {
        this.$toast(this.$t('请填写正确的联系方式'));
        return false;
      }
      if (!this.content) {
        this.$toast(this.$t('投诉内容不能为空'));
        return false;
      }
      let data = {
        object: this.object,
        object_tel: this.object_tel,
        user: this.user,
        user_tel: this.user_tel,
        content: this.content,
      };
      if (this.imageList.length) {
        let imageStr = this.imageList.join(',');
        data.photos = imageStr;
      }
      const res = await ApiComplainFeedbackPost(data);
      this.$toast(res.msg);
      this.init();
    },
    afterRead(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      ApiUploadImage(data).then(
        res => {
          this.imageList.push(res.data.url);
          file.status = 'done';
          file.message = this.$t('上传成功');
        },
        err => {
          file.status = 'failed';
          file.message = this.$t('上传失败');
        },
      );
    },
    init() {
      this.object = '';
      this.object_tel = '';
      this.user = '';
      this.user_tel = '';
      this.content = '';
      this.fileList = [];
      this.imageList = [];
    },
    deletePic(file, detail) {
      this.imageList.splice(detail.index, 1);
    },
  },
};
</script>

<style lang="less" scoped>
main {
  overflow: hidden;
}
/deep/ .van-uploader__preview-image {
  width: 106 * @rem;
  height: 106 * @rem;
}
/deep/ .van-uploader__upload {
  width: 106 * @rem;
  height: 106 * @rem;
}
/deep/ .van-uploader__preview {
  margin-right: 8 * @rem;
  &:nth-of-type(4n) {
    margin-right: 0;
  }
}
/deep/ .van-uploader__upload {
  margin-right: 0;
}
.image-list {
  font-size: 24 * @rem;
}
/deep/ .van-uploader__preview-delete {
  width: 24 * @rem;
  height: 24 * @rem;
}
/deep/ .van-uploader__preview-delete-icon {
  top: -5 * @rem;
  right: -4 * @rem;
}
/deep/ .van-uploader__preview-delete {
  width: 24 * @rem;
  height: 24 * @rem;
  border-radius: 0 0 0 30 * @rem;
}
.my-feedback {
  color: #000;
  font-size: 14 * @rem;
}
.top-tab .container {
  width: 5.34rem;
  height: 1.068rem;
  margin: 0 auto;
  line-height: 1.068rem;
  font-size: 0.4272rem;
  overflow: hidden;
}
.top-tab .container .left {
  float: left;
}
.top-tab .container .right {
  float: right;
}
.top-tab .container .on {
  color: @themeColor;
}
form {
  border-bottom: 0.267rem solid #f5f5f5;
  padding: 0 0.3738rem;
  background-color: #ffffff;
  overflow: hidden;
}
form .text {
  border-bottom: 1px solid #e5e5e5;
}
form .text input {
  width: 100%;
  height: 1.2015rem;
  border: none;
  font-size: 0.4005rem;
  color: #888888;
  outline: none;
}
form .text:first-of-type {
  border-top: 1px solid #e5e5e5;
}
form .text:last-of-type {
  margin-bottom: 0.4005rem;
}
.prompt {
  margin-bottom: 2.136rem;
  padding: 0.3738rem;
  background-color: #ffffff;
}
.prompt .title {
  font-size: 0.4005rem;
}
.prompt .content {
  margin-top: 0.3204rem;
  line-height: 0.5607rem;
  font-size: 0.3738rem;
  color: #333333;
}
.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  .fixed-center;
  height: 1.602rem;
  background-color: #ffffff;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
  z-index: 999;
}
.bottom .button {
  width: 6.408rem;
  height: 1.068rem;
  margin: 0.267rem auto;
  text-align: center;
  line-height: 1.068rem;
  border-radius: 5px;
  background-color: @themeColor;
  font-size: 0.4005rem;
  color: #ffffff;
}

.write_box {
  margin-top: 0.297rem;
  width: 100%;
  height: 6.804rem;
  border: none;
  padding: 0.405rem;
  box-sizing: border-box;
  font-size: 0.405rem;
}
.imgUpLoad {
  padding: 0.405rem;
  background: #ffffff;
  margin-top: 0.351rem;
}
.imgUpLoad .title {
  line-height: 1;
  font-size: 0.405rem;
  color: #333333;
  margin-bottom: 0.486rem;
}
.imgUpLoad .img_box {
  padding-bottom: 0.135rem;
}
.imgUpLoad .img_box img {
  width: 1.89rem;
  height: 1.89rem;
}
.tel_box {
  margin-top: 0.432rem;
  background: #ffffff;
  line-height: 1.215rem;
  padding: 0 0.405rem;
  font-size: 0.405rem;
  color: #333333;
}
.tel_box input {
  border: none;
  font-size: 0.405rem;
  padding-left: 0.27rem;
  width: 6.48rem;
}
</style>
