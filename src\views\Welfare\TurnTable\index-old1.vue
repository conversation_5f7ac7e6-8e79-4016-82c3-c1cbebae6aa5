<template>
  <rubber-band topColor="#2096FA" bottomColor="#FFDAAD">
    <div class="turn-table-page page">
      <nav-bar-2 bgStyle="transparent-white" :placeholder="false">
        <template #right>
          <div class="rule-btn btn" @click="ruleDialogShow = true">
            {{ $t('活动规则') }}
          </div>
        </template>
      </nav-bar-2>
      <div class="main">
        <div class="coin-banner">
          <img src="@/assets/images/turn-table/turn-table-top-new.png" alt="" />
        </div>
        <div class="table-box">
          <div class="table-1">
            <!-- 用户面板 -->
            <div class="user-bar">
              <div class="avatar">
                <user-avatar></user-avatar>
              </div>
              <div class="info">
                <div class="nickname">
                  {{ userInfo.token ? userInfo.nickname : $t('您好，请登录') }}
                </div>
                <div class="coins">
                  {{ $t('当前金币') }}：<span>{{
                    userInfo.token ? userInfo.gold : $t('请登录后查看')
                  }}</span>
                </div>
              </div>
              <div
                class="login"
                v-if="!userInfo.nickname"
                @click="toPage('PhoneLogin')"
              >
                {{ $t('登录') }}
              </div>
              <div class="my-reward" v-else @click="myRewardDialogShow = true">
                {{ $t('我的奖品') }}
              </div>
            </div>
          </div>
          <div class="table-2">
            <!-- 幸运用户轮播 -->
            <div class="lucky-container">
              <div class="notice-icon"></div>
              <van-swipe
                class="lucky-swipe"
                vertical
                :autoplay="2000"
                :show-indicators="false"
                :touchable="false"
              >
                <van-swipe-item v-for="(item, index) in maxReward" :key="index">
                  <div class="lucky-text">
                    <span>{{ item.nickname }}：</span>{{ item.num }}
                  </div>
                </van-swipe-item>
              </van-swipe>
            </div>
          </div>
          <div class="table-3">
            <!-- 祝福值 -->
            <div class="wish-container">
              <div class="left">
                <div class="wish-top">
                  <div class="current-wish">
                    {{ $t('当前祝福值') }}：{{ allNum }}
                  </div>
                  <div
                    class="wish-question"
                    @click="wishIntroductionDialogShow = true"
                  ></div>
                </div>
                <div class="wish-progress">
                  <div
                    class="current-progress"
                    :style="{ width: `${currentProgress * 100}%` }"
                  ></div>
                </div>
                <div class="wish-introduction">{{ boxContent }}</div>
              </div>
              <div class="right">
                <div class="wish-icon"></div>
                <div
                  class="wish-btn"
                  v-if="boxStatus == 0"
                  @click="handleGetWish"
                >
                  {{ $t('未达成') }}
                </div>
                <div
                  class="wish-btn on btn"
                  v-else-if="boxStatus == 1"
                  @click="handleGetWish"
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="wish-btn"
                  v-else-if="boxStatus == 2"
                  @click="handleGetWish"
                >
                  {{ $t('已领取') }}
                </div>
              </div>
            </div>
            <!-- 转盘 -->
            <div class="table-container">
              <div class="table-content">
                <div class="table-list">
                  <div
                    class="table-item"
                    v-for="(item, index) in dialList"
                    :key="index"
                    :class="{ on: current == index + 1 }"
                  >
                    <img
                      class="reward-icon"
                      :src="item.goods_icon_url"
                      alt=""
                    />
                    <div class="reward-text">{{ item.goods_name }}</div>
                  </div>
                  <div
                    class="table-item turn-btn-1 btn"
                    @click="handleRaffle(1)"
                  >
                    <div class="bg" :class="`turn-result-${turnIndex}`"></div>
                    <div class="turn-btn-1-content">
                      <div class="turn-btn-1-tip">
                        {{
                          Number(freeNum)
                            ? `${$t('可免费转')}${freeNum}${$t('次')}`
                            : `${$t('每次')}${oneGold}${$t('金币')}`
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="turn-btn-10 btn" @click="handleRaffle(2)">
                <!-- <div class="btn-text">{{ $t("幸运10连抽") }}</div> -->
                <div class="btn-tip">
                  （{{ $t('每次') }}{{ tenGold }}{{ $t('金币') }}）
                </div>
              </div>
            </div>
          </div>
          <div class="table-4"></div>
        </div>
      </div>

      <!-- 活动规则 -->
      <van-dialog
        v-model="ruleDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
        class="rule-dialog"
      >
        <div>
          <div class="title">{{ $t('活动规则') }}</div>
          <div class="close" @click="ruleDialogShow = false"></div>
          <div class="rule-content" v-html="rules"></div>
        </div>
      </van-dialog>

      <!-- 我的奖品 -->
      <van-dialog
        v-model="myRewardDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
        class="my-reward-dialog"
      >
        <div>
          <div class="title">{{ $t('我的奖品') }}</div>
          <div class="close" @click="myRewardDialogShow = false"></div>
          <div class="no-reward" v-if="!myWonList.length" @click="toLogin">
            {{ userInfo.token ? $t('暂无获奖记录') : $t('请先登录') }}
          </div>
          <div class="reward-list" v-else>
            <div
              class="reward-item"
              v-for="(item, index) in myWonList"
              :key="index"
            >
              <div class="date">
                {{ handleTimestamp(item.create_time).date }}&nbsp;
                {{ handleTimestamp(item.create_time).time }}
              </div>
              <div class="gold">{{ item.goods_name }}</div>
            </div>
          </div>
        </div>
      </van-dialog>

      <!-- 祝福宝箱介绍 -->
      <van-dialog
        v-model="wishIntroductionDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
        class="wish-introduction-dialog"
      >
        <div>
          <div class="title">{{ $t('什么是祝福宝箱') }}</div>
          <div class="wish-content" v-html="boxText"></div>
          <div class="wish-btn btn" @click="wishIntroductionDialogShow = false">
            {{ $t('我知道了') }}
          </div>
        </div>
      </van-dialog>

      <!-- 祝福宝箱介绍 -->
      <van-dialog
        v-model="wishResultDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
        class="wish-result-dialog"
      >
        <div>
          <div class="title">{{ $t('恭喜获得') }}</div>
          <div class="wish-result-content">{{ wishResultMessage }}</div>
          <div
            class="wish-result-btn btn"
            @click="wishResultDialogShow = false"
          >
            {{ $t('确定') }}
          </div>
        </div>
      </van-dialog>

      <!-- 抽奖结果弹窗 -->
      <van-dialog
        v-model="resultDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="false"
        class="result-dialog"
      >
        <div>
          <div class="dialog-title" :class="{ one: resultList.length == 1 }">
            {{ $t('恭喜获得') }}
          </div>
          <div class="result-list">
            <div
              class="result-item"
              v-for="(item, index) in resultList"
              :key="index"
              :class="{ one: resultList.length == 1 }"
            >
              <img class="result-icon" :src="item.goods_icon_url" alt="" />
              <div class="result-name">{{ item.goods_name }}</div>
            </div>
          </div>
          <div class="operate">
            <div class="operate-item">
              <div class="operate-btn btn" @click="handleRaffle(2)">
                <span>{{ tenGold }}{{ $t('金币十连') }}</span>
              </div>
            </div>
            <div class="operate-item">
              <div class="operate-btn btn" @click="handleRaffle(1)">
                <span>{{ oneGold }}{{ $t('金币再抽1次') }}</span>
              </div>
            </div>
          </div>
          <div class="close" @click="resultDialogShow = false"></div>
        </div>
      </van-dialog>

      <!-- 实物奖品提示弹窗 -->
      <!-- <van-dialog
        v-model="tipDialogShow"
        :lock-scroll="false"
        :show-confirm-button="true"
        confirm-button-color="#FF8B02"
        @confirm="closeTipDialog"
      >
        <div class="real-reward-tips">
          <div class="tip-text">
            {{
              $t(
                "即2020-06-08日起，抽中实物奖励的玩家可凭“我的奖品”中奖记录截图联系客服领取奖励"
              )
            }}
          </div>
          <div class="checkbox" @click="remember = !remember">
            <div class="gou" :class="{ on: remember }"></div>
            <div class="remember-text">{{ $t("我知道了，不再提示") }}</div>
          </div>
        </div>
      </van-dialog> -->
    </div>
  </rubber-band>
</template>

<script>
import rubberBand from '@/components/rubber-band';
import {
  ApiGoldDialList,
  ApiGoldDialMyWonList,
  ApiGoldDialRaffle,
} from '@/api/views/users.js';
export default {
  name: 'TurnTable',
  components: {
    rubberBand,
  },
  data() {
    return {
      current: 0,
      dialList: [], // 转盘内容
      rules: '', // 抽奖规则
      maxReward: [], // 幸运用户列表
      allNum: 0, //祝福总值
      blessNum: 0, //祝福达标值
      boxContent: '',
      boxStatus: 0, //祝福宝箱状态 0=未达成 1=未领取 2=已领取
      boxText: '',
      oneGold: 10,
      tenGold: 100,
      myWonList: [], // 我的奖品列表
      isWatch: false,
      freeNum: 0, // 剩余免费次数
      timer: null, // 定时器
      // tipDialogShow: true, // 实物奖品提示窗
      resultDialogShow: false, // 抽奖结果提示窗
      remember: false, // 实物奖品下次不提示？
      resultList: [],
      isStarting: false,
      ruleDialogShow: false, // 规则弹窗
      myRewardDialogShow: false, // 我的奖品弹窗
      wishIntroductionDialogShow: false, // 祝福值介绍
      wishResultDialogShow: false, // 祝福结果弹窗
      wishResultMessage: '', //祝福结果消息

      turnIndex: 0, // 转盘旋转角度
    };
  },
  computed: {
    currentProgress() {
      if (this.allNum > this.blessNum) {
        return 1;
      }
      return this.allNum / this.blessNum;
    },
  },
  async created() {
    // 是否显示实物奖品提示窗
    // this.remember = localStorage.getItem("REAL_REWARD_SHOW");
    // if (!!this.remember) {
    //   this.tipDialogShow = false;
    // }

    await this.getGoldDialList();
    await this.getMyWonList();
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    // 领取祝福奖励
    async handleGetWish() {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      try {
        const res = await ApiGoldDialRaffle({
          type: 4,
        });
        let { message } = res.data;
        this.wishResultMessage = message;
        this.wishResultDialogShow = true;
        await this.getGoldDialList();
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    async getGoldDialList() {
      const res = await ApiGoldDialList();
      let {
        dial_list,
        max_reward,
        text,
        all_num,
        bless_num,
        box_content,
        box_status,
        box_text,
        one_gold,
        ten_gold,
      } = res.data;
      this.dialList = dial_list;
      this.rules = text.replace(/\n/g, '<br>');
      this.maxReward = max_reward;
      this.allNum = all_num;
      this.blessNum = bless_num;
      this.boxContent = box_content;
      this.boxStatus = box_status;
      this.boxText = box_text.replace(/\n\n/gi, '<br/>');
      if (one_gold) this.oneGold = one_gold;
      if (ten_gold) this.tenGold = ten_gold;
    },
    async getMyWonList() {
      const res = await ApiGoldDialMyWonList();
      let { free_num, is_watch, list } = res.data;
      this.myWonList = list;
      this.isWatch = is_watch;
      this.freeNum = free_num;
    },
    async handleRaffle(type) {
      if (this.isStarting) {
        return false;
      }
      this.isStarting = true;
      this.resultDialogShow = false;
      this.current = 0;
      this.turnIndex = 0;

      if (type == 1) {
        type = this.freeNum != 0 ? 3 : 1;
      }
      // @param type 1 1连抽  2 10连抽  3 免费
      try {
        const res = await ApiGoldDialRaffle({
          type: type,
        });
        let { free_num, list } = res.data;

        this.resultList = list;

        await this.onTurning(this.resultList[0].id);
        this.resultDialogShow = true;
        this.freeNum = free_num;
        await this.getGoldDialList();
        await this.getMyWonList();
        await this.SET_USER_INFO();
      } finally {
        this.isStarting = false;
      }
    },
    async onTurning(id) {
      return new Promise(resolve => {
        this.turnIndex =
          this.dialList.findIndex(item => {
            return item.id == id;
          }) + 1;
        setTimeout(() => {
          resolve();
        }, 5000);
      });
    },
    handleTimestamp(ts) {
      let temp = new Date(ts * 1000),
        m = temp.getMonth() + 1,
        d = temp.getDate(),
        h = temp.getHours(),
        min = temp.getMinutes();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      h = h < 10 ? '0' + h : h;
      min = min < 10 ? '0' + min : min;
      return {
        date: `${m}-${d}`,
        time: `${h}:${min}`,
      };
    },
    closeTipDialog() {
      if (this.remember) {
        localStorage.setItem('REAL_REWARD_SHOW', this.remember);
      }
    },
    toLogin() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.turn-table-page {
  .rule-btn {
    font-size: 14 * @rem;
    color: #ffffff;
  }
  .main {
    background: #ed4250;
    // padding-bottom: calc(20 * @rem + @safeAreaBottom);
    // padding-bottom: calc(20 * @rem + @safeAreaBottomEnv);
    background: linear-gradient(180deg, #2096fa 0%, #7fceef 64%, #ffdaad 100%);
  }
  .coin-banner {
    width: 100%;
    height: 294 * @rem;
    margin: 78 * @rem auto 0;
  }

  .table-box {
    margin-top: -182 * @rem;
    position: relative;
    .table-1 {
      width: 100%;
      height: 93 * @rem;
      background: url(~@/assets/images/turn-table/table-1.png) center top
        no-repeat;
      background-size: 100% 93 * @rem;

      .user-bar {
        box-sizing: border-box;
        height: 93 * @rem;
        border-radius: 12 * @rem;
        margin: 13 * @rem auto 0;
        display: flex;
        align-items: center;
        padding: 23 * @rem 22 * @rem 30 * @rem 29 * @rem;
        position: relative;
        .avatar {
          box-sizing: border-box;
          width: 40 * @rem;
          height: 40 * @rem;
          border-radius: 50%;
          overflow: hidden;
          border: 1 * @rem solid #fff;
        }
        .info {
          margin-left: 10 * @rem;
          flex: 1;
          min-width: 0;
          .nickname {
            font-size: 14 * @rem;
            color: #fff;
            font-weight: 600;
            line-height: 20 * @rem;
          }
          .coins {
            font-size: 12 * @rem;
            line-height: 17 * @rem;
            color: #fff;
            font-weight: 400;
            margin-top: 3 * @rem;
            span {
              color: #fff;
              font-weight: 600;
            }
          }
        }
        .login,
        .my-reward {
          width: 66 * @rem;
          height: 28 * @rem;
          background: #fc4d2a;
          border-radius: 14 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #fff;
        }
      }
    }
    .table-2 {
      width: 100%;
      height: 34 * @rem;
      background: url(~@/assets/images/turn-table/table-2.png) center top
        no-repeat;
      background-size: 100% 34 * @rem;
      margin-top: -1 * @rem;

      .lucky-container {
        box-sizing: border-box;
        width: 100%;
        height: 26 * @rem;
        padding: 0 48 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .notice-icon {
          width: 17 * @rem;
          height: 14 * @rem;
          .image-bg('~@/assets/images/turn-table/notice-icon.png');
          margin-left: 10 * @rem;
          margin-right: 16 * @rem;
        }
        .lucky-swipe {
          width: 250 * @rem;
          height: 26 * @rem;
          .van-swipe-item {
            color: #a82501;
            font-size: 11 * @rem;
            line-height: 26 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            span {
              color: #fff;
            }
          }
        }
      }
    }
    .table-3 {
      width: 100%;
      height: 540 * @rem;
      background: url(~@/assets/images/turn-table/table-3.png) center top
        no-repeat;
      background-size: 100% 540 * @rem;
      margin-top: -1 * @rem;
      overflow: hidden;
      .wish-container {
        box-sizing: border-box;
        height: 87 * @rem;
        margin: 24 * @rem 32 * @rem 0;
        padding: 9 * @rem 11 * @rem 9 * @rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          width: 220 * @rem;
          .wish-top {
            display: flex;
            align-items: center;
            .current-wish {
              font-size: 14 * @rem;
              color: #ffffff;
              font-weight: 500;
            }
            .wish-question {
              width: 12 * @rem;
              height: 12 * @rem;
              .image-bg('~@/assets/images/turn-table/wish-question.png');
              margin-left: 10 * @rem;
            }
          }
          .wish-progress {
            width: 220 * @rem;
            height: 6 * @rem;
            background: #b0343e;
            border-radius: 3 * @rem;
            margin-top: 10 * @rem;
            .current-progress {
              width: 10%;
              height: 6 * @rem;
              border-radius: 3 * @rem;
              background: url(~@/assets/images/turn-table/wish-progress.png)
                left center no-repeat;
              background-size: auto 6 * @rem;
            }
          }
          .wish-introduction {
            font-size: 11 * @rem;
            color: #ffe3e6;
            line-height: 16 * @rem;
            margin-top: 7 * @rem;
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          align-items: center;
          .wish-icon {
            width: 37 * @rem;
            height: 37 * @rem;
            .image-bg('~@/assets/images/turn-table/wish-box.png');
          }
          .wish-btn {
            width: 56 * @rem;
            height: 24 * @rem;
            background: rgba(255, 198, 198, 0.5);
            border-radius: 13 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            color: #ffffff;
            font-weight: 600;
            margin-top: 6 * @rem;
            &.on {
              background: linear-gradient(180deg, #fff974 0%, #ffc700 100%);
              color: #ff3a39;
            }
          }
        }
      }

      .table-container {
        margin: 0 24 * @rem;
        .table-content {
          box-sizing: border-box;
          width: 327 * @rem;
          height: 327 * @rem;
          padding: 9 * @rem;
          .table-list {
            background-color: #fff;
            position: relative;
            .table-item {
              width: 103 * @rem;
              height: 103 * @rem;
              background: url(~@/assets/images/turn-table/turn-item-new.png)
                center center no-repeat;
              background-size: 103 * @rem 103 * @rem;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              position: absolute;
              .reward-icon {
                height: 57 * @rem;
                width: auto;
              }
              .reward-text {
                font-size: 13 * @rem;
                line-height: 18 * @rem;
                color: #b9450a;
                margin-top: 4 * @rem;
                font-weight: 600;
              }
              &:nth-of-type(1) {
                left: 206 * @rem;
                top: 0;
              }
              &:nth-of-type(2) {
                left: 206 * @rem;
                top: 103 * @rem;
              }
              &:nth-of-type(3) {
                left: 206 * @rem;
                top: 206 * @rem;
              }
              &:nth-of-type(4) {
                left: 103 * @rem;
                top: 206 * @rem;
              }
              &:nth-of-type(5) {
                left: 0;
                top: 206 * @rem;
              }
              &:nth-of-type(6) {
                left: 0;
                top: 103 * @rem;
              }
              &:nth-of-type(7) {
                left: 0;
                top: 0;
              }
              &:nth-of-type(8) {
                left: 103 * @rem;
                top: 0;
              }
              &.turn-btn-1 {
                left: 103 * @rem;
                top: 103 * @rem;
                width: 103 * @rem;
                height: 103 * @rem;
                background: none;
                .bg {
                  width: 103 * @rem;
                  height: 103 * @rem;
                  background-image: url(~@/assets/images/turn-table/turn-btn-bg.png);
                  background-size: 103 * @rem 103 * @rem;
                  position: absolute;
                  left: 0;
                  top: 0;
                  z-index: 1;
                  transform: rotate(0deg);
                  &.turn-result-0 {
                    transform: rotate(0deg);
                  }
                  &.turn-result-1 {
                    transform: rotate(3645deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-2 {
                    transform: rotate(3690deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-3 {
                    transform: rotate(3735deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-4 {
                    transform: rotate(3780deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-5 {
                    transform: rotate(3825deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-6 {
                    transform: rotate(3870deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-7 {
                    transform: rotate(3915deg);
                    transition: all 5s ease-in-out;
                  }
                  &.turn-result-8 {
                    transform: rotate(3960deg);
                    transition: all 5s ease-in-out;
                  }
                }
                .turn-btn-1-content {
                  width: 103 * @rem;
                  height: 103 * @rem;
                  background-image: url(~@/assets/images/turn-table/turn-btn-new.png);
                  background-size: 103 * @rem 103 * @rem;
                  position: absolute;
                  left: 0;
                  top: 0;
                  z-index: 2;
                }
                .turn-btn-1-title {
                  font-size: 26 * @rem;
                  font-weight: bold;
                  color: #8c0002;
                }
                .turn-btn-1-tip {
                  color: #ffffff;
                  margin-top: 2 * @rem;
                  text-align: center;
                  font-size: 10 * @rem;
                  font-weight: 600;
                  text-shadow: 2 * @rem 2 * @rem 2 * @rem #8c0002;
                  margin-top: 62 * @rem;
                }
              }
            }
          }
        }
        .turn-btn-10 {
          width: 262 * @rem;
          height: 78 * @rem;
          background: url(~@/assets/images/turn-table/turn-10.png) center center
            no-repeat;
          background-size: 262 * @rem 78 * @rem;
          margin: 12 * @rem auto 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 26 * @rem;
          .btn-text {
            font-size: 16 * @rem;
            color: #b01a1a;
            font-weight: 600;
          }
          .btn-tip {
            font-size: 12 * @rem;
            color: #cb0000;
            font-weight: 600;
            margin-top: 3 * @rem;
            margin-bottom: 3 * @rem;
          }
        }
      }
    }
    .table-4 {
      width: 100%;
      height: 131 * @rem;
      background: url(~@/assets/images/turn-table/table-4.png) center top
        no-repeat;
      background-size: 100% 131 * @rem;
      margin-top: -1 * @rem;
    }
  }

  .section-title {
    font-size: 18 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20 * @rem 0 15 * @rem;
    .title-text {
      display: inline-block;
      margin: 0 auto;
      position: relative;
      &:before {
        content: '';
        width: 6 * @rem;
        height: 6 * @rem;
        border-radius: 3 * @rem;
        background-color: #fff;
        position: absolute;
        left: -15 * @rem;
        top: 50%;
        transform: translateY(-50%);
      }
      &:after {
        content: '';
        width: 6 * @rem;
        height: 6 * @rem;
        border-radius: 3 * @rem;
        background-color: #fff;
        position: absolute;
        right: -15 * @rem;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .reward-container {
    box-sizing: border-box;
    width: 345 * @rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3 * @rem;
    margin: 16 * @rem auto 0;
    padding-bottom: 20 * @rem;
    .no-reward {
      padding: 20 * @rem 0;
      text-align: center;
      font-size: 15 * @rem;
      color: #ffffff;
    }
    .reward-list {
      padding: 0 16 * @rem;
      font-size: 14 * @rem;
      color: #ffffff;
      height: 150 * @rem;
      overflow-y: auto;
      .reward-item {
        display: flex;
        justify-content: space-between;
        line-height: 30 * @rem;
        height: 30 * @rem;
      }
    }
  }

  .rule-container {
    box-sizing: border-box;
    width: 345 * @rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3 * @rem;
    margin: 23 * @rem auto 0;
    padding: 0 13 * @rem 15 * @rem 16 * @rem;
    .rule-content {
      font-size: 14 * @rem;
      color: #fff;
      line-height: 22 * @rem;
    }
  }
  /deep/ .van-dialog {
    border-radius: 10 * @rem;
    width: 320 * @rem;
  }
  .real-reward-tips {
    padding: 25 * @rem 23 * @rem 25 * @rem;
    .tip-text {
      font-size: 15 * @rem;
      color: #333333;
      line-height: 24 * @rem;
    }
    .checkbox {
      display: flex;
      align-items: center;
      margin-top: 15 * @rem;
      .gou {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/turn-table/gou.png) center center
          no-repeat;
        background-size: 15 * @rem 15 * @rem;
        &.on {
          background-image: url(~@/assets/images/turn-table/gou-on.png);
        }
      }
      .remember-text {
        font-size: 13 * @rem;
        color: #999999;
        margin-left: 6 * @rem;
      }
    }
  }

  .result-dialog {
    background-color: transparent;
    width: 327 * @rem;
    background: #ffffff;
    border-radius: 23 * @rem;
    padding-bottom: 10 * @rem;
    .dialog-title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      padding: 20 * @rem 0 15 * @rem;
      &.one {
        color: #ff383c;
      }
    }
    .close {
      width: 15 * @rem;
      height: 15 * @rem;
      background: url(~@/assets/images/turn-table/popup-close.png) center center
        no-repeat;
      background-size: 15 * @rem 15 * @rem;
      position: absolute;
      right: 15 * @rem;
      top: 15 * @rem;
    }
    .result-list {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      padding: 0 10 * @rem;
      flex-wrap: wrap;
      width: 327 * @rem;
      .result-item {
        width: 60 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 15 * @rem auto 0;
        &:nth-of-type(-n + 5) {
          margin-top: 0;
        }
        .result-icon {
          height: 56 * @rem;
          width: auto;
          margin: 0 auto;
          transform: scale(0.7);
        }
        .result-name {
          font-size: 10 * @rem;
          color: #000000;
          font-weight: 400;
          white-space: nowrap;
          width: 100%;
          overflow: hidden;
          text-align: center;
        }
        &.one {
          width: 100%;
          position: relative;
          .result-icon {
            height: 96 * @rem;
            width: auto;
            margin: 16 * @rem auto 0;
            transform: scale(1);
          }
          .result-name {
            font-size: 14 * @rem;
            color: #000000;
            margin-top: 15 * @rem;
            font-weight: 400;
          }
        }
      }
    }
    .operate {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20 * @rem;
      .operate-item {
        padding: 15 * @rem 0;
        &:not(:first-of-type) {
          margin-left: 15 * @rem;
        }
        .operate-btn {
          width: 140 * @rem;
          height: 40 * @rem;
          .image-bg('~@/assets/images/turn-table/popup-raffle-btn-2.png');
          font-size: 12 * @rem;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;

          span {
            font-size: 14 * @rem;
            color: #b01a1a;
            font-weight: 600;
          }
        }
      }
    }
  }
  .rule-dialog,
  .my-reward-dialog {
    box-sizing: border-box;
    width: 335 * @rem;
    border-radius: 16px;
    background-color: #fff;
    top: 50%;
    padding: 10 * @rem 18 * @rem;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      padding: 12 * @rem 0;
    }
    .close {
      width: 33 * @rem;
      height: 33 * @rem;
      background: url(~@/assets/images/turn-table/popup-close.png) center center
        no-repeat;
      background-size: 15 * @rem 15 * @rem;
      position: absolute;
      right: 0;
      top: 0;
    }
    .no-reward {
      text-align: center;
      padding: 30 * @rem 0 50 * @rem;
    }
    .rule-content {
      font-size: 13 * @rem;
      line-height: 20 * @rem;
      color: #000000;
      font-weight: 400;
      /deep/ p {
        margin-bottom: 5 * @rem;
      }
    }
  }
  .my-reward-dialog {
    padding: 10 * @rem 0;
    .reward-list {
      height: 289 * @rem;
      overflow-y: auto;
      .reward-item {
        height: 40 * @rem;
        margin: 0 18 * @rem;
        border-bottom: 0.5px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        &:last-of-type {
          border-bottom: 0;
        }
      }
    }
  }
  .wish-introduction-dialog,
  .wish-result-dialog {
    width: 278 * @rem;
    padding: 10 * @rem 0;
    border-radius: 19 * @rem;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      text-align: center;
      padding: 20 * @rem 0 16 * @rem;
    }
    .wish-content {
      padding: 0 24 * @rem;
      font-size: 14 * @rem;
      color: #000000;
      line-height: 20 * @rem;
      font-weight: 400;
      text-align: justify;
    }
    .wish-btn {
      width: 208 * @rem;
      height: 40 * @rem;
      background: #ed4250;
      border-radius: 20 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      color: #ffffff;
      margin: 20 * @rem auto 10 * @rem;
    }
    .wish-result-content {
      font-size: 16 * @rem;
      color: #ed4250;
      text-align: center;
      padding: 12 * @rem 0;
    }
    .wish-result-btn {
      width: 172 * @rem;
      height: 48 * @rem;
      background: url(~@/assets/images/turn-table/wish-result-btn.png) center
        center no-repeat;
      background-size: 172 * @rem 48 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      line-height: 42 * @rem;
      text-align: center;
      margin: 20 * @rem auto 10 * @rem;
      color: #fff;
    }
  }
}
</style>
