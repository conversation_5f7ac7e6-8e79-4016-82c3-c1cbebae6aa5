<template>
  <div class="free-play-page">
    <nav-bar-2
      bgStyle="transparent"
      :border="false"
      :placeholder="false"
    ></nav-bar-2>
    <div class="main" :style="{ backgroundColor: pageData.color }">
      <div class="top-bar" v-if="pageData.title_img">
        <img :src="pageData.title_img" alt="" />
      </div>
      <div class="free-introduction" :style="{ color: pageData.font_color }">
        {{ pageData.content }}
      </div>
      <div class="game-list">
        <div
          class="game-item"
          v-for="(item, index) in zeroGameList"
          :key="index"
        >
          <game-item-2 :gameInfo="item"></game-item-2>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ApiGameZeroGame } from '@/api/views/game.js';
export default {
  name: 'FreePlay',
  data() {
    return {
      zeroGameList: [],
      pageData: {},
    };
  },
  async created() {
    this.$toast.loading({
      message: this.$t('拼命加载中...'),
      duration: 0,
    });
    await this.getZeroGames();
    this.$toast.clear();
  },
  methods: {
    async getZeroGames() {
      const res = await ApiGameZeroGame();
      let { list, color, content, font_color, sub_title, title_img } = res.data;
      this.zeroGameList = list;
      this.pageData = { color, content, font_color, sub_title, title_img };
    },
  },
};
</script>

<style lang="less" scoped>
.free-play-page {
  background-color: #ffffff;
  font-size: 14 * @rem;
  min-height: 100vh;
  .main {
    padding-bottom: 40 * @rem;
  }
  .top-bar {
    width: 100%;
    height: 190 * @rem;
  }
  .free-introduction {
    font-size: 12 * @rem;
    line-height: 20 * @rem;
    padding: 16 * @rem 30 * @rem 0;
  }
  .game-list {
    padding: 10 * @rem 20 * @rem;
    .game-item {
      padding: 5 * @rem 0;
      border-bottom: 0.5px solid #ebebeb;
      &:last-of-type {
        border-bottom: 0;
      }
    }
  }
}
</style>
