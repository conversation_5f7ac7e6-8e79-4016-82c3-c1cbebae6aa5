<template>
  <div class="invite-detail">
    <nav-bar-2
      :title="type == 1 ? '邀请明细' : '收入明细'"
      :azShow="true"
      :border="true"
    >
    </nav-bar-2>
    <table>
      <tr>
        <th v-for="(item, index) in th_list">{{ item }}</th>
      </tr>
      <tr v-if="type == 1" v-for="(item, index) in tr_list">
        <td>{{ handleTimestamp(item.create_time) }}</td>
        <td>{{ item.username }}</td>
      </tr>
      <tr v-if="type == 2" v-for="(item, index) in tr_list">
        <td>{{ handleTimestamp(item.create_time) }}</td>
        <td>{{ item.username }}</td>
        <td>{{ item.money }}元</td>
        <td>{{ item.status_str }}</td>
      </tr>
    </table>

    <content-empty v-if="finish && tr_list.length === 0"></content-empty>
  </div>
</template>
<script>
import {
  ApiInviteUserList,
  ApiInviteRewardList,
} from '@/api/views/weekWelfare.js';
import { handleTimestamp } from '@/utils/datetime';

export default {
  data() {
    return {
      type: 0, //1邀请明细2收入明细
      tr_list: [], //表格列表
      finish: 0, //0还没拉取数据1已经拉取完
    };
  },
  computed: {
    th_list() {
      return this.type == 1
        ? ['邀请时间', '好友账号']
        : ['充值时间', '好友账号', '获得奖励', '发放情况'];
    },
  },
  async created() {
    this.type = this.$route.params.type;
    await this.init();
  },
  methods: {
    async init() {
      let res;
      if (this.type == 1) {
        res = await ApiInviteUserList();
      } else {
        res = await ApiInviteRewardList();
      }
      this.finish = 1;
      this.tr_list = res.data.list;
    },
    handleTimestamp(time) {
      let obj = handleTimestamp(time);
      return `${obj.year}-${obj.date}`;
    },
  },
};
</script>
<style lang="less" scoped>
table {
  margin: 0 auto;
  width: 335px;
}
th {
  height: 70px;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
  color: #10141f;
  line-height: 70px;
}
td {
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  color: #999999;
}
</style>
