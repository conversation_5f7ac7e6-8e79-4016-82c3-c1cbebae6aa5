<template>
  <!-- 隐私政策未同意弹窗 -->
  <div>
    <van-popup
      v-model="show"
      :close-on-click-overlay="true"
      position="bottom"
      round
      class="no-safari-popup"
    >
      <div class="title">
        安装小贴士<span @click="show = false" class="close"></span>
      </div>
      <div class="content">
        <div class="tips">
          下载描述文件后，可参考如下提示进入 [设置]授权安装：
        </div>
        <div class="steps">
          <div class="step-item">
            <div class="step-num">1</div>
            <div class="step-right">
              <div class="step-title">打开手机 [设置]</div>
              <div class="step-content add-step-1"> </div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-num">2</div>
            <div class="step-right">
              <div class="step-title">点击 [通用]</div>
              <div class="step-content add-step-2"> </div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-num">3</div>
            <div class="step-right">
              <div class="step-title">拉到底部，点击 [VPN与设备管理]</div>
              <div class="step-content add-step-3"> </div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-num">4</div>
            <div class="step-right">
              <div class="step-title">开始安装</div>
              <div class="step-content add-step-4"> </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import addStep1 from '@/assets/images/tips/add-to-desk-1.png';
import addStep2 from '@/assets/images/tips/add-to-desk-2.png';
import addStep3 from '@/assets/images/tips/add-to-desk-3.png';
import addStep4 from '@/assets/images/tips/add-to-desk-4.png';
export default {
  data() {
    return {
      show: false,
      addStep1,
      addStep2,
      addStep3,
      addStep4,
    };
  },
  methods: {
    cancel() {
      this.show = false;
    },
    confirm() {
      this.closePopup();
    },
    handleLink(link, title) {
      this.show = false;
      this.toPage('Iframe', {
        title: title,
        url: link,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.no-safari-popup {
  .title {
    height: 44 * @rem;
    text-align: center;
    line-height: 44 * @rem;
    font-size: 16 * @rem;
    border-bottom: 1px solid #eeeeee;
  }
  .close {
    position: absolute;
    top: 14 * @rem;
    right: 14 * @rem;
    width: 14 * @rem;
    height: 14 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-size: no-repeat;
  }
  .content {
    padding: 21 * @rem 24 * @rem;
    .tips {
      font-size: 16 * @rem;
      color: #333333;
      line-height: 22 * @rem;
      font-weight: 600;
    }
    .steps {
      margin-top: 21 * @rem;
    }
    .step-item {
      display: flex;
      &:not(:first-of-type) {
        margin-top: 18 * @rem;
      }
      &:not(:last-of-type) {
        .step-num {
          &::after {
            content: '';
            width: 0 * @rem;
            height: 51 * @rem;
            position: absolute;
            left: 50%;
            top: 36 * @rem;
            border-left: 1 * @rem dashed #32b768;
          }
        }
      }

      .step-num {
        width: 24 * @rem;
        height: 24 * @rem;
        border-radius: 50%;
        background: #21b98a;
        font-size: 14 * @rem;
        color: #ffffff;
        line-height: 24 * @rem;
        text-align: center;
        position: relative;
      }
      .step-right {
        flex: 1;
        min-width: 0;
        margin-left: 13 * @rem;
        .step-title {
          font-size: 14 * @rem;
          font-weight: 600;
          color: #333333;
          line-height: 20 * @rem;
          margin-top: 2 * @rem;
        }
        .step-content {
          margin-top: 8 * @rem;
          width: 258 * @rem;
          height: 60 * @rem;
          background-size: 258 * @rem 60 * @rem;
          background-repeat: no-repeat;
          background-position: left center;
          &.add-step-1 {
            background-image: url(~@/assets/images/tips/add-to-desk-1.png);
          }
          &.add-step-2 {
            background-image: url(~@/assets/images/tips/add-to-desk-2.png);
          }
          &.add-step-3 {
            background-image: url(~@/assets/images/tips/add-to-desk-3.png);
          }
          &.add-step-4 {
            background-image: url(~@/assets/images/tips/add-to-desk-4.png);
          }
        }
      }
    }
  }
}
</style>
