<template>
  <div class="page">
    <nav-bar-2
      title=""
      bgStyle="transparent"
      :placeholder="false"
      :backShow="false"
    >
      <template #right>
        <div class="close btn" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="collection-info">
      <div class="title">{{ info.title }}</div>
      <div class="subtitle">{{ info.subtitle }}</div>
      <div class="banner" v-if="info.banner">
        <img :src="info.banner" :alt="info.title" />
      </div>
      <div class="desc" v-if="info.desc">{{ info.desc }}</div>
    </div>
    <div class="up-info" v-if="info.up_info">
      <div class="avatar" v-if="info.up_info">
        <user-avatar :src="info.up_info.avatar"></user-avatar>
      </div>
      <div class="nickname" v-if="info.up_info">
        {{ info.up_info.nickname }}
      </div>
      <div class="follow-btn btn" v-if="!info.is_focus" @click="handleFollow">
        关注
      </div>
      <div class="follow-btn had btn" v-else @click="handleFollow">已关注</div>
    </div>
    <div class="game-list">
      <div class="game-item">
        <up-game-item
          class="game-item"
          v-for="(item, index) in info.game_list"
          :key="index"
          :info="item"
        >
        </up-game-item>
      </div>
    </div>
  </div>
</template>

<script>
import upGameItem from '@/components/up-game-item';
import { ApiUserFollowUser } from '@/api/views/users.js';
import { ApiCollectGetUpCollectInfo } from '@/api/views/upCollection.js';
export default {
  components: {
    upGameItem,
  },
  data() {
    return {
      info: {},
    };
  },
  async created() {
    this.id = this.$route.params.id;
    this.$toast.loading('加载中');
    await this.getDetail();
    this.$toast.clear();
  },
  methods: {
    // 处理关注
    async handleFollow() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      const res = await ApiUserFollowUser({
        memId: this.info.up_id,
        type: this.info.is_focus == 0 ? 1 : 0,
      });
      this.info.is_focus = !this.info.is_focus;
      this.$toast(res.msg);
      await this.getDetail();
    },
    async getDetail() {
      try {
        const res = await ApiCollectGetUpCollectInfo({ id: this.id });
        this.info = res.data.info;
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'NotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url('~@/assets/images/up-collection/close-page.png') center
      center no-repeat;
    background-size: 28 * @rem 28 * @rem;
  }
  .collection-info {
    padding-top: 64 * @rem;
    .title {
      padding: 0 20 * @rem;
      padding-right: 60 * @rem;
      font-size: 18 * @rem;
      color: #333333;
      line-height: 23 * @rem;
      font-weight: bold;
    }
    .subtitle {
      padding: 0 20 * @rem;
      font-size: 15 * @rem;
      color: #333333;
      line-height: 18 * @rem;
      margin-top: 5 * @rem;
    }
    .banner {
      width: 100%;
      margin-top: 15 * @rem;
    }
    .desc {
      margin-top: 20 * @rem;
      padding: 0 20 * @rem;
      font-size: 12 * @rem;
      line-height: 15 * @rem;
      color: #666666;
    }
  }
  .up-info {
    margin-top: 20 * @rem;
    padding: 0 20 * @rem;
    display: flex;
    align-items: center;
    .avatar {
      width: 24 * @rem;
      height: 24 * @rem;
    }
    .nickname {
      flex: 1;
      min-width: 0;
      font-size: 14 * @rem;
      color: #333333;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 8 * @rem;
    }
    .follow-btn {
      width: 58 * @rem;
      height: 28 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeColor;
      color: #fff;
      font-size: 12 * @rem;
      border-radius: 14 * @rem;
      &.had {
        background-color: #efefef;
        color: #cccccc;
      }
    }
  }
  .game-list {
    padding: 20 * @rem 10 * @rem;
  }
}
</style>
