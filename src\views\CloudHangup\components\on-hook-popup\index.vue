<template>
  <van-dialog
    v-model="popupShow"
    :showConfirmButton="false"
    :lockScroll="false"
    :overlay-style="{ 'z-index': '2996' }"
    class="onhook-popup"
  >
    <div class="title" v-if="type === 0">{{ $t('返回盒子') }}</div>
    <div class="title" v-if="type === 1">{{ $t('提示') }}</div>
    <div class="title" v-if="type === 2">{{ $t('提示') }}</div>
    <div class="content" v-if="type === 0">
      {{ $t('你即将返回3733游戏盒云挂机页面，云挂机内的游戏会继续保持挂机') }}
    </div>
    <div class="content" v-if="type === 1">{{ errMsg }}</div>
    <div class="content" v-if="type === 2">{{ errMsg }}</div>
    <div class="tips" v-if="type === 0" @click.stop="setRemember()">
      <div class="gou" :class="{ remember: remember }"></div>
      <div class="tips-text">{{ $t('不再提示') }}</div>
    </div>
    <div class="btn-info">
      <div class="cancel" @click.stop="cancel()">{{ $t('取消') }}</div>
      <div class="confirm" @click.stop="confirm()">{{ $t('确定') }}</div>
    </div>
  </van-dialog>
</template>
<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    errMsg: {
      type: String,
      default: '',
    },
    type: {
      type: Number, // 0 返回 1 提示 2 重新连接
      default: 0,
    },
  },
  data() {
    return {
      remember: false,
    };
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  methods: {
    setRemember() {
      this.remember = !this.remember;
      localStorage.setItem('ON_HOOK_POPUP_HIDE', this.remember);
    },
    cancel() {
      this.$emit('update:show', false);
      this.$emit('send-cancelData', true, this.type);
    },
    confirm() {
      this.$emit('update:show', false);
      this.$emit('send-confirmData', true, this.type);
    },
  },
};
</script>
<style lang="less" scoped>
.onhook-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 20 * @rem 15 * @rem 25 * @rem 15 * @rem;
  background-size: 300 * @rem auto;
  border-radius: 20 * @rem;
  z-index: 2996 !important;
  .title {
    text-align: center;
    font-weight: bold;
  }
  .content {
    line-height: 17 * @rem;
    font-size: 14 * @rem;
    color: #777777;
    padding: 0 5 * @rem;
    margin-top: 22 * @rem;
    text-align: center;
  }
  .btn-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 12 * @rem 0 0;
    .cancel,
    .confirm {
      width: 126 * @rem;
      height: 40 * @rem;
      border-radius: 30 * @rem;

      font-size: 13 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cancel {
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .confirm {
      margin-left: 17 * @rem;
      color: #ffffff;
      background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
    }
  }

  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10 * @rem auto 0;
    padding: 0 25 * @rem;
    .gou {
      width: 12 * @rem;
      height: 12 * @rem;
      border-radius: 12 * @rem;
      border: 1px solid #7d7d7d;
      &.remember {
        background: url('~@/assets/images/cloudHangup/bzts-img.png') no-repeat 0
          0;
        background-size: 14px 14px;
        width: 14px;
        height: 14px;
        border: none;
      }
    }
    .tips-text {
      font-size: 12 * @rem;
      color: #999999;
      margin-left: 6 * @rem;
    }
  }
}
</style>
