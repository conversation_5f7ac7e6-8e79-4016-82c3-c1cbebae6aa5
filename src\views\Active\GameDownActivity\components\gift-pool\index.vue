<template>
  <div class="gift-pool">
    <div class="gift-pool-main">
      <div class="gift-content">
        <div class="gift-pool-title">{{ info.title }}</div>
        <div class="gift-pool-desc">
          <span v-for="(v, vIndex) in info.reward_desc" :key="vIndex">
            {{ v }}
          </span>
        </div>
      </div>
      <!-- 不可领取 -->
      <div
        class="reward-btn no"
        v-if="info.receive_status == 0"
        @click="$emit('takeGift')"
      >
        领取
      </div>
      <!-- 可领取 -->
      <div
        class="reward-btn"
        v-else-if="info.receive_status == 1"
        @click="$emit('takeGift')"
      >
        领取
      </div>
      <!-- 已领取 -->
      <div class="reward-btn no" v-else-if="info.receive_status == 2">
        已领取
      </div>
    </div>
    <div class="gift-pool-reward" v-if="info.card_pass">
      <!-- 已领取-有礼包码 -->
      <div class="reward-icon"></div>
      <div class="reward-text">礼包码：{{ info.card_pass }}</div>
      <div class="reward-btn copy" @click="copy(info.card_pass)">复制</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.gift-pool {
  box-sizing: border-box;
  background-color: #fafafa;
  border-radius: 8 * @rem;
  margin-left: -15 * @rem;
  margin-top: 10 * @rem;
  .gift-pool-main {
    display: flex;
    align-items: center;
    padding: 14 * @rem 15 * @rem;
    .gift-content {
      flex: 1;
      min-width: 0;
    }
    .gift-pool-title {
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      font-weight: 600;
      color: #121212;
    }
    .gift-pool-desc {
      font-size: 12 * @rem;
      line-height: 17 * @rem;
      color: #757575;
      margin-top: 4 * @rem;
      flex: 1;
      min-width: 0;
      span {
        &:not(:last-of-type) {
          &::after {
            content: '、';
          }
        }
      }
    }
  }

  .reward-btn {
    width: 46 * @rem;
    height: 25 * @rem;
    border-radius: 6 * @rem;
    background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12 * @rem;
    color: #ffffff;
    margin-left: 20 * @rem;
    &.no {
      color: #ffffff;
      background: linear-gradient(180deg, #d1d1d1 0%, #858585 99%);
    }
    &.copy {
      border: 1 * @rem solid #ff4d4f;
      color: #ff4d4f;
      background: transparent;
    }
  }
  .gift-pool-reward {
    display: flex;
    height: 45 * @rem;
    align-items: center;
    border-top: 0.5px solid #ebebeb;
    margin: 0 14 * @rem;
    .reward-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      background: url(~@/assets/images/game-down-activity/reward-icon.png)
        no-repeat;
      background-size: 18 * @rem 18 * @rem;
      margin-right: 4 * @rem;
    }
    .reward-text {
      flex: 1;
      min-width: 0;
      font-size: 13 * @rem;
      color: #757575;
    }
  }
}
</style>
