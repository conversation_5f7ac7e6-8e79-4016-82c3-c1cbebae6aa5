<template>
  <div class="xiaohao-item btn" @click="clickXiaohaoItem">
    <div class="top-content">
      <div class="game-icon">
        <img :src="xiaohaoInfo.titlepic" alt="" />
      </div>
      <div class="game-name">
        {{ xiaohaoInfo.main_title
        }}<span class="game-subtitle" v-if="xiaohaoInfo.subtitle">{{
          xiaohaoInfo.subtitle
        }}</span>
      </div>
    </div>
    <div class="xiaohao-info">
      <div class="info-item">{{ $t('小号ID') }}：{{ xiaohaoInfo.id }}</div>
      <div class="info-item">
        {{ $t('小号昵称') }}：{{ xiaohaoInfo.nickname }}
      </div>
      <div class="info-item">
        {{ $t('创建日期') }}：{{ xiaohaoInfo.create_time | formatTime }}
      </div>
      <div class="info-item orange">
        {{ $t('我的充值') }}：{{ xiaohaoInfo.paySum }}元
      </div>
      <div class="info-item">
        {{ $t('是否可出售') }}：<span
          :class="{ red: Number(xiaohaoInfo.sellStatus) }"
          >{{ xiaohaoInfo.sellStatusStr }}</span
        >
      </div>
    </div>
    <div class="dealing-icon" v-if="xiaohaoInfo.status == 2"></div>
    <van-dialog
      v-model="operateShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      get-container="body"
    >
      <div class="operate-list">
        <div class="operate-item btn" @click="clickChangeNickname">
          <div class="icon manage-nickname"></div>
          <div class="operate-text">{{ $t('修改小号昵称') }}</div>
        </div>
        <div class="operate-item btn" @click="clickRecycle">
          <div class="icon manage-recycle"></div>
          <div class="operate-text" :class="{ no: !xiaohaoInfo.recycleStatus }">
            {{ $t('小号回收')
            }}<span v-if="!xiaohaoInfo.recycleStatus"
              >({{ $t('不支持') }})</span
            >
          </div>
        </div>
        <div class="operate-item btn" @click="clickDeal">
          <div class="icon manage-deal"></div>
          <div class="operate-text" :class="{ no: !xiaohaoInfo.sellStatus }">
            {{ $t('小号交易')
            }}<span v-if="!xiaohaoInfo.sellStatus">({{ $t('不支持') }})</span>
          </div>
        </div>
      </div>
    </van-dialog>
    <van-dialog
      v-model="nicknameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      get-container="body"
    >
      <div class="nickname-dialog">
        <div class="title">{{ $t('重命名') }}</div>
        <div class="input-text">
          <input
            type="text"
            v-model="nickname"
            :placeholder="$t('请输入小号昵称')"
          />
        </div>
        <div class="bottom-bar">
          <div
            class="bottom-btn cancel btn"
            @click="nicknameDialogShow = false"
          >
            {{ $t('取消') }}
          </div>
          <div class="bottom-btn btn" @click="handleChangeNickname">
            {{ $t('确定') }}
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiXiaohaoChangeNickName } from '@/api/views/xiaohao.js';
import { handleTimestamp } from '@/utils/datetime.js';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'ManageXiaohaoItem',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      xiaohaoInfo: this.info,
      operateShow: false,
      nicknameDialogShow: false,
      nickname: '',
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  filters: {
    formatTime(val) {
      let { year, date } = handleTimestamp(val);
      return `${year}-${date}`;
    },
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    clickXiaohaoItem() {
      if (this.xiaohaoInfo.status == 2) {
        this.$toast(this.$t('小号处于交易状态'));
        return false;
      }
      this.operateShow = true;
    },
    async handleChangeNickname() {
      if (!this.nickname) {
        this.$toast(this.$t('请输入昵称!'));
        return false;
      }
      try {
        const res = await ApiXiaohaoChangeNickName({
          nickname: this.nickname,
          xhId: this.xiaohaoInfo.id,
        });
        if (res.code == 1) {
          this.xiaohaoInfo.nickname = this.nickname;
          this.$toast(this.$t('修改成功'));
        }
      } finally {
        this.nicknameDialogShow = false;
      }
    },
    // 点击修改小号昵称
    clickChangeNickname() {
      this.operateShow = false;
      this.nickname = this.xiaohaoInfo.nickname;
      this.nicknameDialogShow = true;
    },
    // 点击小号回收
    clickRecycle() {
      if (!Number(this.xiaohaoInfo.recycleStatus)) {
        return false;
      }
      this.operateShow = false;
      this.$nextTick(() => {
        let info = {
          id: this.xiaohaoInfo.id,
          game_name: this.xiaohaoInfo.title,
          game_icon: this.xiaohaoInfo.titlepic,
          nickname: this.xiaohaoInfo.nickname,
        };
        this.toPage('Recycle', {
          info,
        });
      });
    },
    // 点击小号交易
    clickDeal() {
      if (!Number(this.xiaohaoInfo.sellStatus)) {
        return false;
      }
      this.operateShow = false;
      let info = {
        game_icon: this.xiaohaoInfo.titlepic,
        game_name: this.xiaohaoInfo.title,
        app_id: this.xiaohaoInfo.app_id,
        nickname: this.xiaohaoInfo.nickname,
        pay_sum: this.xiaohaoInfo.paySum,
        xh_id: this.xiaohaoInfo.id,
        is_h5: this.xiaohaoInfo.is_h5,
      };
      this.setXiaohaoSellInfo({ ...info });
      this.$nextTick(() => {
        this.toPage('XiaohaoSellWrite');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-item {
  box-sizing: border-box;
  padding: 16 * @rem 14 * @rem;
  width: 347 * @rem;
  height: 140 * @rem;
  background: #ffffff;
  box-shadow: 0 * @rem 0 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.2);
  border-radius: 10 * @rem;
  margin: 0 auto;
  position: relative;
  .dealing-icon {
    position: absolute;
    right: 0;
    top: 0;
    width: 60 * @rem;
    height: 60 * @rem;
    background: url(~@/assets/images/deal/dealing-icon.png) center center
      no-repeat;
    background-size: 60 * @rem 60 * @rem;
  }
  .top-content {
    display: flex;
    align-items: center;
    .game-icon {
      width: 23 * @rem;
      height: 23 * @rem;
      border-radius: 6 * @rem;
      background-color: #bfbfbf;
      overflow: hidden;
    }
    .game-name {
      font-size: 16 * @rem;
      color: #ff5a00;
      flex: 1;
      min-width: 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 7 * @rem;
      display: flex;
      align-items: center;
      .game-subtitle {
        box-sizing: border-box;
        border: 1 * @rem solid fade(@themeColor, 80);
        border-radius: 3 * @rem;
        font-size: 11 * @rem;
        padding: 2 * @rem 3 * @rem;
        color: @themeColor;
        margin-left: 5 * @rem;
        vertical-align: middle;
        line-height: 1;
      }
    }
  }
  .xiaohao-info {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8 * @rem;
    .info-item {
      width: 50%;
      font-size: 14 * @rem;
      color: #666666;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-top: 10 * @rem;
    }
    .orange {
      color: #f99412;
    }
    .red {
      color: #ff2323;
    }
  }
}
.operate-list {
  padding: 0 20 * @rem;
  .operate-item {
    height: 70 * @rem;
    display: flex;
    align-items: center;
    border-top: 1px solid #eeeeee;
    &:nth-of-type(1) {
      border-top: 0;
    }
    .icon {
      width: 20 * @rem;
      height: 20 * @rem;
      &.manage-nickname {
        background: url(~@/assets/images/deal/manage-nickname.png) center center
          no-repeat;
        background-size: 20 * @rem 17 * @rem;
      }
      &.manage-recycle {
        background: url(~@/assets/images/deal/manage-recycle.png) center center
          no-repeat;
        background-size: 20 * @rem 20 * @rem;
      }
      &.manage-deal {
        background: url(~@/assets/images/deal/manage-deal.png) center center
          no-repeat;
        background-size: 20 * @rem 20 * @rem;
      }
    }
    .operate-text {
      font-size: 15 * @rem;
      color: #333333;
      margin-left: 15 * @rem;
      span {
        color: #f62f2f;
        font-size: 15 * @rem;
      }
      &.no {
        color: #999999;
      }
    }
  }
}
.nickname-dialog {
  padding-top: 23 * @rem;
  padding-bottom: 23 * @rem;
  .title {
    font-size: 18 * @rem;
    font-weight: bold;
    text-align: center;
  }
  .input-text {
    width: 260 * @rem;
    height: 45 * @rem;
    display: flex;
    align-items: center;
    border: 1px solid #dcdcdc;
    border-radius: 4 * @rem;
    margin: 30 * @rem auto 0;
    input {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 10 * @rem;
    }
  }
  .bottom-bar {
    width: 260 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 28 * @rem auto 0;
    .bottom-btn {
      width: 120 * @rem;
      height: 38 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      border-radius: 19px;
      font-size: 15 * @rem;
      color: #ffffff;
      &.cancel {
        color: #666666;
        background: #eeeeee;
      }
    }
  }
}
</style>
