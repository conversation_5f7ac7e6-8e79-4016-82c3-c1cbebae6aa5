<template>
  <div class="game-try-page page">
    <nav-bar-2
      title="游戏试玩"
      :bgStyle="navbarOpacity < 0.5 ? 'transparent-white' : 'transparent'"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
      <template #right>
        <div
          class="rule-btn btn"
          :class="{ black: navbarOpacity >= 0.5 }"
          @click="toPage('GameTryRule')"
        >
          规则
        </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="main-bg">
        <div class="line-1">本栏仅可领取一次哦</div>
        <div class="line-2">
          已有<span>{{ reward_num }}</span
          >人获得奖励
        </div>
      </div>

      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
      >
        <div class="game-list">
          <div
            class="game-item"
            v-for="(item, index) in gameList"
            :key="index"
            @click="toGameTryDetail(item.id)"
          >
            <div class="tag" :class="tagStatus(item.trial_status)"></div>
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="game-name">请体验游戏：{{ item.title }}</div>
            <div class="reward">
              奖励：<span>{{ item.task_reward }}</span>
            </div>
            <div class="date">{{ item.task_time }}</div>
            <div
              class="operate-btn btn"
              :class="{ empty: [2, 5].includes(Number(item.trial_status)) }"
            >
              {{ btnStatus(item.trial_status) }}
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiTrialPlayIndex } from '@/api/views/gameTry.js';
export default {
  data() {
    return {
      navbarOpacity: 0,
      reward_num: 0,
      gameList: [],
      page: 1,
      listRows: 10,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      empty: false,
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.getIndex();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async getIndex(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiTrialPlayIndex({
        page: this.page,
        listRows: this.listRows,
      });
      let { reward_num, list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        this.reward_num = reward_num;
      }
      this.gameList.push(...list);
      if (!this.gameList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getIndex();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getIndex(2);
      this.loadingObj.loading = false;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    // 标签类名
    tagStatus(status) {
      let className = '';
      status = Number(status);
      switch (status) {
        case 0:
          className = 'get-can';
          break;
        case 1:
          className = 'getting';
          break;
        case 2:
          className = 'get-done';
          break;
        case 5:
          className = '';
          break;
      }
      return className;
    },
    // 按钮名称
    btnStatus(status) {
      let text = '';
      status = Number(status);
      switch (status) {
        case 0:
          text = '立即试玩>>';
          break;
        case 1:
          text = '进行中';
          break;
        case 2:
          text = '已完成';
          break;
        case 5:
          text = '已结束';
          break;
      }
      return text;
    },
    toGameTryDetail(id) {
      this.toPage('GameTryDetail', { id });
    },
  },
};
</script>

<style lang="less" scoped>
.game-try-page {
  min-height: 100vh;
  .rule-btn {
    font-size: 15 * @rem;
    color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &.black {
      color: #000;
    }
  }
  .main {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    .main-bg {
      flex-shrink: 0;
      box-sizing: border-box;
      width: 100%;
      height: 254 * @rem;
      padding-left: 21 * @rem;
      padding-top: 169 * @rem;
      .image-bg('~@/assets/images/welfare/game-try/game-try-bg.png');
      .line-1 {
        margin-top: 6 * @rem;
        font-size: 14 * @rem;
        line-height: 18 * @rem;
        color: #934949;
        font-weight: bold;
      }
      .line-2 {
        margin-top: 6 * @rem;
        font-size: 14 * @rem;
        line-height: 18 * @rem;
        color: #934949;
        font-weight: bold;
        span {
          color: #ff4000;
          font-weight: bold;
        }
      }
    }
    .game-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 18 * @rem;
      justify-content: space-between;
      .game-item {
        box-sizing: border-box;
        padding: 16 * @rem 0 15 * @rem;
        width: 160 * @rem;
        height: 200 * @rem;
        background: #fff1e4;
        border-radius: 12px 12px 12px 12px;
        opacity: 1;
        border: 1px solid rgba(255, 225, 197, 1);
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 18 * @rem;
        position: relative;
        .tag {
          width: 58 * @rem;
          height: 59 * @rem;
          position: absolute;
          right: -3 * @rem;
          top: -3 * @rem;
          &.get-can {
            .image-bg('~@/assets/images/welfare/game-try/get-can.png');
          }
          &.getting {
            .image-bg('~@/assets/images/welfare/game-try/getting.png');
          }
          &.get-done {
            .image-bg('~@/assets/images/welfare/game-try/get-done.png');
          }
          &.get-none {
            display: none;
          }
        }
        .game-icon {
          width: 60 * @rem;
          height: 60 * @rem;
        }
        .game-name {
          box-sizing: border-box;
          padding: 0 5 * @rem;
          width: 100%;
          text-align: center;
          font-size: 12 * @rem;
          font-weight: bold;
          color: #934949;
          line-height: 15 * @rem;
          margin-top: 12 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .reward {
          box-sizing: border-box;
          width: 100%;
          text-align: center;
          padding: 0 5 * @rem;
          font-size: 12 * @rem;
          color: #b95959;
          margin-top: 4 * @rem;
          line-height: 15 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .date {
          font-size: 10 * @rem;
          color: #b95959;
          line-height: 13 * @rem;
          margin-top: 11 * @rem;
        }
        .operate-btn {
          width: 80 * @rem;
          height: 24 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 12 * @rem;
          background: #ff7a00;
          color: #fff;
          font-size: 12 * @rem;
          margin-top: 15 * @rem;
          &.empty {
            background: #dec8c3;
          }
        }
      }
    }
  }
}
</style>
