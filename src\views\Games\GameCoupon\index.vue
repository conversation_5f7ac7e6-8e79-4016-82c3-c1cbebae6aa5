<template>
  <div class="game-quan-page page">
    <nav-bar-2 :title="$t('代金券')">
      <template #right>
        <div class="instruction" @click="toIntroduction">
          {{ $t('使用说明') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="xiaohao-fixed-placeholder" ref="xiaohaoFixed">
      <div
        class="xiaohao-fixed"
        v-if="currentXiaohao.nickname"
        @click="changeXiaohao"
      >
        <div class="xiaohao-name">
          {{ currentXiaohao.nickname }}
        </div>
        <div class="change-xiaohao">
          {{ $t('切换') }}<span class="change-xiaohao-icon"></span>
        </div>
      </div>
      <div class="xiaohao-fixed" v-else>{{ fixedText }}</div>
    </div>
    <div class="quan-container">
      <!-- <div class="changwan-card" @click="toPage('ChangwanCard')"></div> -->
      <content-empty v-if="empty"></content-empty>
      <div class="quan-list" v-else>
        <template v-for="(item, index) in couponList">
          <div
            class="quan-item"
            :key="index"
            :class="{ 'quan-svip': item.type == 1 }"
          >
            <div
              v-if="item.type && item.type != 0 && subscript(item)"
              class="subscript"
              :class="{ svip: item.type == 1 }"
            >
              {{ subscript(item) }}
            </div>
            <div class="quan-info">
              <div class="top">
                <div class="left">
                  <div class="quan-num">
                    <em class="unit">¥</em><span>{{ item.money }}</span>
                  </div>
                  <div class="total-num">
                    {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
                  </div>
                </div>
                <div class="center">
                  <div class="title">{{ item.title }}</div>
                </div>
              </div>
              <div class="bottom">
                <div class="desc">
                  <div class="date">
                    {{ item.expire_time_text || item.period_title }}
                  </div>
                </div>
                <div
                  class="get btn"
                  :class="{
                    had: item.take_status != 0 || item.remain_percent == 0,
                  }"
                  @click="getCoupon(item)"
                >
                  {{
                    item.take_status != 0
                      ? $t('已领取')
                      : item.remain_percent != 0
                        ? $t('领取')
                        : $t('已抢光')
                  }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-fixed" v-if="couponList.length">
      <div class="get-all btn" @click="getCouponAll">{{ $t('全部领取') }}</div>
    </div>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(gameId)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import {
  ApiCouponCoupon,
  ApiCouponTake,
  ApiCouponTakeByGameId,
} from '@/api/views/coupon.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';
import { mapActions } from 'vuex';
import h5Page from '@/utils/h5Page';
import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
export default {
  name: 'GameCoupon',
  components: {
    xhCreateTipDialog,
  },
  data() {
    return {
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      xiaohaoListShowItem: {},
      currentXiaohao: {}, //当前选择小号
      fixedText: '',
      couponList: [],
      empty: false,
      gameId: this.$route.params.game_id,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
  },
  async created() {
    await this.getCurrentXiaohaoId();
    await this.getList();
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    // 获取当前的小号id
    async getCurrentXiaohaoId() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.gameId,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
        // 判断是否有已选择小号
        let flag = list.some(item => {
          return item.id == this.xiaohaoMap[this.gameId]?.id;
        });
        if (flag) {
          this.currentXiaohao = this.xiaohaoMap[this.gameId];
        } else {
          this.currentXiaohao = list[0];
          // this.setXiaohaoMap([this.gameId, list[0]]);
        }
      } else {
        this.xiaohaoList = [];
        this.currentXiaohao = {};
        this.setXiaohaoMap([this.gameId, {}]);
        if (text) this.fixedText = text;
      }
    },
    changeXiaohao() {
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    getCoupon(item) {
      if (item.take_status != 0) {
        this.$toast(this.$t('亲，当前账号已经领取过该券了~'));
        return false;
      }
      if (!this.currentXiaohao.id) {
        this.createDialogShow = true;
        return false;
      }
      this.$toast.loading({
        message: this.$t('领取中...'),
      });
      ApiCouponTake({
        couponId: item.id,
        xhId: this.currentXiaohao.id,
      }).then(
        async res => {
          this.$toast(res.msg);
          this.$set(this.couponList[this.findIndex(item)], 'take_status', true);
          this.SET_USER_INFO();

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${item.id}`,
            voucher_name: `${item.title}`,
            voucher_amount: `${item.money}`,
          });

          await this.getList();
        },
        err => {
          // if (err.code == 0) {
          //   this.$set(
          //     this.couponList[this.findIndex(item)],
          //     "remain_percent",
          //     0
          //   );
          // }
        },
      );
    },
    getCouponAll() {
      this.$toast.loading({
        message: this.$t('领取中...'),
      });
      ApiCouponTakeByGameId({
        gameId: this.gameId,
        xhId: this.currentXiaohao.id,
      }).then(async res => {
        this.$toast(res.msg);
        this.couponList.forEach((item, index) => {
          item.take_status = true;
        });
        this.SET_USER_INFO();
        await this.getList();
      });
    },
    findIndex(coupon) {
      return this.couponList.findIndex(item => {
        return item.id == coupon.id;
      });
    },
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: h5Page.daijinquanshuoming,
          title: this.$t('代金券使用说明'),
        },
      });
    },
    subscript(item) {
      switch (parseInt(item.type)) {
        case 1:
          return this.$t('SVIP代金券');
        case 2:
          return this.$t('无门槛');
        // case 5:
        //   return this.$t("零门槛");
        default:
          return false;
      }
    },
    async getList() {
      const res = await ApiCouponCoupon({
        gameId: this.gameId,
        xhId: this.currentXiaohao.id || '',
      });
      if (!res.data.list.length) {
        this.empty = true;
      } else {
        this.couponList = res.data.list;
      }
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.setXiaohaoMap([this.gameId, this.currentXiaohao]);
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getList();
      this.$toast.clear();
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-page {
  background-color: #f3f5f9;
  .xiaohao-fixed-placeholder {
    position: relative;
    width: 100%;
    height: 30 * @rem;
    flex-shrink: 0;
  }
  .xiaohao-fixed {
    position: fixed;
    z-index: 100;
    .fixed-center;
    box-sizing: border-box;
    padding: 0 12 * @rem;
    width: 100%;
    height: 32 * @rem;
    line-height: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    font-size: 13 * @rem;
    color: @themeColor;
    border-top: 1 * @rem solid #f3f5f9;

    .change-xiaohao {
      display: flex;
      align-items: center;

      .change-xiaohao-icon {
        margin-left: 4 * @rem;
        width: 12 * @rem;
        height: 12 * @rem;
        .image-bg('~@/assets/images/games/change-xiaohao-icon.png');
      }
    }
  }
  .instruction {
    font-size: 14 * @rem;
    color: #000000;
  }
  .quan-container {
    padding-bottom: 78 * @rem;
    background-color: #f3f5f9;

    .changwan-card {
      width: 339 * @rem;
      height: 42 * @rem;
      margin: 15 * @rem auto 12 * @rem;
      background: url(~@/assets/images/games/changwan-bg.png) center center
        no-repeat;
      background-size: 339 * @rem 42 * @rem;
    }
    .quan-list {
      .quan-item {
        position: relative;
        width: 339 * @rem;
        height: 115 * @rem;
        margin: 12 * @rem auto 0 * @rem;
        background-color: #fff;
        border-radius: 10 * @rem;
        padding: 0 16 * @rem;
        box-sizing: border-box;
        .subscript {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 54 * @rem;
          height: 18 * @rem;
          font-weight: bold;
          font-size: 9 * @rem;
          color: #ffffff;
          line-height: 1;
          border-top-left-radius: 8 * @rem;
          border-bottom-right-radius: 8 * @rem;
          background: @themeBg;
          position: absolute;
          top: 0;
          left: 0;

          &.svip {
            color: #754400;
            background: linear-gradient(90deg, #fed184 0%, #ffcd77 100%);
          }
        }
        .quan-info {
          box-sizing: border-box;
          height: 100%;
          position: relative;

          .top {
            display: flex;
            align-items: center;
            padding: 12 * @rem 0;
            .left {
              width: 85 * @rem;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              .quan-num {
                display: flex;
                align-items: center;
                justify-content: center;

                white-space: nowrap;
                .unit {
                  font-size: 11 * @rem;
                  line-height: 1;
                  color: #fe4a26;
                }
                span {
                  display: block;
                  font-size: 24 * @rem;
                  font-weight: bold;
                  margin-left: 1 * @rem;
                  line-height: 34 * @rem;
                  overflow: hidden;
                  color: #fe4a26;
                }
              }
              .total-num {
                font-size: 11 * @rem;
                line-height: 12 * @rem;
                color: #fe4a26;
                font-weight: 500;
              }
            }
            .center {
              flex: 1;
              min-width: 0;
              margin-left: 5 * @rem;
              .title {
                font-size: 14 * @rem;
                color: #000000;
                font-weight: bold;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-height: 16 * @rem;
                max-height: 32 * @rem;
                overflow: hidden;
              }
            }
          }

          .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 43 * @rem;
            border-top: 1 * @rem dashed #d8d8d8;

            .date {
              font-size: 11 * @rem;
              color: #909090;
              font-weight: 400;
            }
          }

          .get {
            width: 49 * @rem;
            height: 23 * @rem;
            border-radius: 4 * @rem;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 11 * @rem;
            font-weight: 500;
            background: @themeColor @themeBg;
            margin-left: 10 * @rem;
            &.had {
              background: #bababa;
            }
          }
        }
        &.quan-svip {
          background-image: url(~@/assets/images/games/coupon-bg-svip.png);
          .quan-info {
            .center {
              .title {
                color: #75440d;
              }
              .desc {
                .date {
                  color: #a16910;
                }
              }
            }
            .get {
              background: linear-gradient(90deg, #ffa06c 0%, #ff5050 100%);
            }
          }
        }
      }
    }
  }
  .bottom-fixed {
    width: 100%;
    height: 68 * @rem;
    padding: 12 * @rem 10 * @rem;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    box-shadow: 0 * @rem -4 * @rem 8 * @rem 0 * @rem rgba(0, 37, 168, 0.05);

    .get-all {
      width: 100%;
      height: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 1;
      text-align: center;
      border-radius: 43 * @rem;
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
