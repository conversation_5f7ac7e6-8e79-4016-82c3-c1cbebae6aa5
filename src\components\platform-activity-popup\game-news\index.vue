<template>
  <div class="game-news-page">
    <div class="tabs-list">
      <div class="tabs-item">
        <div
          class="item"
          v-for="(item, index) in tabsList"
          :key="index"
          :class="{ active: current === index }"
          @click="clickTab(index)"
        >
          <span>{{ item.title }}</span>
        </div>
      </div>
    </div>
    <div class="news-container">
      <news-fanli v-if="current == 0" :game_id="game_id"></news-fanli>
      <news-gonglue
        v-if="current == 1"
        :game_id="game_id"
        :vip_content="vip_content"
        :vip_price_list="vip_price_list"
      ></news-gonglue>
    </div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { Sticky } from 'vant';
import { mapGetters } from 'vuex';
import NewsFanli from './NewsFanli';
import NewsGonglue from './NewsGonglue';
export default {
  name: 'GameNews',
  props: {
    game_id: {
      type: Number,
      default: 0,
    },
    vip_content: {
      type: String,
      default:
        '部分游戏月卡、基金、理财、礼包不算VIP经验和充值，此表由游戏厂商提供，仅供参考。如与实际不符，请以游戏内为准，详细咨询客服',
    },
    vip_price_list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    'van-sticky': Sticky,
    NewsFanli,
    NewsGonglue,
  },
  data() {
    return {
      remNumberLess,
      current: 0,
      empty: false,
      tabsList: [
        {
          title: this.$t('返利活动'),
        },
        {
          title: this.$t('玩法攻略'),
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      gameInfo: 'game/gameInfo',
    }),
  },
  created() {
    if (
      !this.gameInfo.news_groups ||
      !this.gameInfo.news_groups[this.current]
    ) {
      this.empty = true;
    } else {
      this.empty = false;
    }
  },
  methods: {
    clickTab(index) {
      this.current = index;
      if (!this.gameInfo.news_groups || !this.gameInfo.news_groups[index]) {
        this.empty = true;
      } else {
        this.empty = false;
      }
    },
    toPage(item) {
      this.$router.push({
        name: 'Iframe',
        params: { url: item.titleurl, title: item.title },
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-sticky--fixed {
  right: none;
  left: 50%;
  transform: translate(-50%, 0);
  max-width: @maxWidth;
  width: 100%;
}
.game-news-page {
  background: #f7f8fa;
  .tabs-list {
    padding: 0 12 * @rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tabs-item {
      width: 192 * @rem;
      height: 42 * @rem;
      display: flex;
      align-items: center;
      .item {
        box-sizing: border-box;
        width: 192 * @rem;
        height: 26 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        border: 1 * @rem solid #ffffff;
        color: #93999f;
        display: flex;
        align-items: center;
        justify-content: center;
        &.active {
          border-radius: 6 * @rem;
          background: #ecfbf4;
          border: 1 * @rem solid #ecfbf4;
          font-weight: 500;
          color: #1cce94;
        }
      }
    }
    .instruction {
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
  .news-container {
    height: 386 * @rem;
    overflow: auto;
    // padding-top: 44 * @rem;
    .tabs {
      display: flex;
      // position: fixed;
      // top: calc(50 * @rem + @safeAreaTop);
      // top: calc(50 * @rem + @safeAreaTopEnv);
      background-color: #fff;
      z-index: 2000;
      .tab {
        width: 187 * @rem;
        height: 44 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          font-size: 15 * @rem;
          color: #666666;
        }
        &.active {
          span {
            font-size: 15 * @rem;
            font-weight: 600;
            color: #000000;
          }
        }
      }
      .tab-line {
        position: absolute;
        width: 12 * @rem;
        height: 4 * @rem;
        border-radius: 2 * @rem;
        background-color: @themeColor;
        left: 0;
        bottom: 0;
        transform: translateX(87 * @rem);
        transition: 0.3s;
      }
    }
    .game-vip {
      padding: 20 * @rem 15 * @rem 26 * @rem;
      border-bottom: 10 * @rem solid #f6f6f6;
      .title {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: bold;
      }
      .section-content {
        margin-top: 12 * @rem;
        .explain {
          line-height: 17 * @rem;
          font-size: 12 * @rem;
          color: rgba(102, 102, 102, 1);
        }
        table {
          width: 100%;
          margin-top: 10 * @rem;
          border-collapse: collapse;
          th,
          td {
            width: 50%;
            height: 35 * @rem;
            line-height: 35 * @rem;
            text-align: center;
            font-size: 14 * @rem;
            border: 1px solid rgba(220, 220, 220, 1);
          }
          th {
            background-color: rgba(238, 238, 238, 1);
            font-size: 15 * @rem;
            font-weight: normal;
          }
        }
      }
    }
    .news-list {
      padding: 0 14 * @rem 20 * @rem;
      .news-item {
        padding: 15 * @rem 0;
        border-bottom: 0.5 * @rem solid #e5e5e5;
        .news-title {
          font-size: 15 * @rem;
          color: #333333;
          font-weight: bold;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          max-height: 40 * @rem;
          line-height: 20 * @rem;
        }
        .date {
          font-size: 12 * @rem;
          color: #c2c2c2;
          margin-top: 10 * @rem;
          text-align: right;
        }
      }
    }
  }
}
</style>
