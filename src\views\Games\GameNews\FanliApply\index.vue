<template>
  <div class="page fanli-apply-page">
    <nav-bar-2
      title="返利申请"
      :azShow="true"
      :placeholder="true"
      :border="true"
    >
    </nav-bar-2>
    <div class="main" v-if="text_info.prompt">
      <div class="form">
        <form @submit.prevent="">
          <div class="form-item">
            <div class="form-item-title">活动标题：</div>
            <div class="form-item-content">
              <span>{{ activity.title }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏账号：</div>
            <div class="form-item-content">
              <span>{{ userInfo.nickname }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏名：</div>
            <div class="form-item-content">
              <span>{{ game_name }}</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏小号：</div>
            <div class="form-item-content" @click="chooseXh">
              <input
                type="text"
                v-model="formState.selectedXh.nickname"
                :placeholder="text_info.prompt.xh"
                disabled
                style="pointer-events: none"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">游戏区服：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.rolearea"
                :placeholder="text_info.prompt.area"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">角色名：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.rolename"
                :placeholder="text_info.prompt.role_name"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">角色ID：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.role_id"
                :placeholder="text_info.prompt.role_id"
              />
            </div>
          </div>
          <div class="form-item" v-if="show_activity_time">
            <div class="form-item-title">充值日期：</div>
            <div class="form-item-content" @click="calendarShow = true">
              <input
                type="text"
                v-model="formState.date"
                :placeholder="text_info.prompt.pay_time"
                disabled
                style="pointer-events: none"
              />
            </div>
          </div>
          <div class="form-item" v-if="!show_activity_time">
            <div class="form-item-title">冠名名称：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.name_text"
                :placeholder="text_info.prompt.gm"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">礼包选择：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.threshold_text"
                :placeholder="text_info.prompt.reward"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">联系方式：</div>
            <div class="form-item-content">
              <input
                type="text"
                v-model="formState.contact_info"
                :placeholder="text_info.prompt.phone"
              />
            </div>
          </div>
          <div class="form-item-column" v-if="!show_activity_time">
            <div class="form-item-title">
              上传图片：<span>（好评活动等需上传截图，最多支持上传3张）</span>
            </div>
            <div class="photos">
              <van-uploader
                v-model="fileList"
                :after-read="afterRead"
                @delete="deletePic"
                :max-count="3"
                accept="image/*"
                :before-read="beforeRead"
                :multiple="true"
              >
                <div class="upload-btn">
                  <img src="@/assets/images/feedback/upload-add.png" />
                </div>
              </van-uploader>
            </div>
          </div>
          <div class="form-item-column">
            <div class="form-item-title">
              申请备注：<span>（可自选奖励请备注说明）</span>
            </div>
            <textarea
              class="form-item-textarea"
              v-model="formState.remarks"
              :placeholder="text_info.prompt.remarks"
            ></textarea>
          </div>

          <div class="form-item">
            <div class="form-item-title">充值金额：</div>
            <div class="form-item-content">
              <div class="amount">{{ paySum }}</div>
            </div>
          </div>
          <button class="submit" @click="submit">提交</button>
        </form>
      </div>
      <div class="rule-container">
        <div class="rule-title"></div>
        <div class="rule-content" v-html="text_info.rule_description"></div>
      </div>
    </div>
    <van-calendar
      v-model="calendarShow"
      @confirm="onCalendarConfirm"
      :min-date="minDate"
      :max-date="maxDate"
    />

    <van-action-sheet
      v-model="xhListShow"
      :actions="xhListFormat"
      description="请选择小号"
      close-on-click-action
      :lock-scroll="false"
      :closeable="false"
      @select="onSelectSuccess"
    />
  </div>
</template>

<script>
import {
  ApiRebateSubmitRebate,
  ApiRebateGetUserPaySum,
  ApiRebateGetRebateInfo,
} from '@/api/views/rebate.js';
import { ApiUploadImage } from '@/api/views/system';
import { devLog } from '@/utils/function.js';
import md5 from 'js-md5';
let timer = null;
export default {
  data() {
    return {
      info: {},

      actId: this.$route.params.id,
      gameId: this.$route.params.game_id,

      activity: {},
      game_name: '',
      text_info: {},
      username: '',
      xiaohao_list: [],

      formState: {
        selectedXh: {},
        imageFileList: [],
        role_id: '',
        date: '',
      },
      fileList: [], //图片列表

      calendarShow: false,
      minDate: new Date(2023, 0, 1),
      maxDate: new Date(),
      xhListShow: false,

      paySum: 0,
    };
  },
  computed: {
    show_activity_time() {
      if (!this.activity.recharge_type) {
        return false;
      }
      return [1, 4].includes(this.activity.recharge_type);
    },
    xhListFormat() {
      return this.xiaohao_list.map(item => {
        return {
          ...item,
          name: item.nickname,
        };
      });
    },
  },
  watch: {
    formState: {
      handler(newVal) {
        clearTimeout(timer);
        timer = setTimeout(async () => {
          await this.refreshPaySum();
        }, 1000);
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    this.info = this.$route.params.info;
    await this.getRebateInfo();
  },
  methods: {
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.formState.imageFileList.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        devLog(err);
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.formState.imageFileList.splice(detail.index, 1);
    },
    beforeRead() {
      return true;
    },
    async getRebateInfo() {
      const res = await ApiRebateGetRebateInfo({
        act_id: this.actId,
        game_id: this.gameId,
      });
      const { activity, game_name, text_info, username, xiaohao_list } =
        res.data;
      this.activity = activity;
      this.game_name = game_name;
      this.text_info = text_info;
      this.username = username;
      this.xiaohao_list = xiaohao_list;
      if (this.xiaohao_list.length > 0) {
        this.formState.selectedXh = this.xiaohao_list[0];
      }
    },
    async refreshPaySum() {
      const params = {
        act_id: this.actId,
        game_id: this.gameId,
        xh_id: this.formState.selectedXh.id,
        role: this.formState.role_id,
      };
      if (!this.formState.role_id || !this.formState.selectedXh.id) {
        return false;
      }
      if ([1, 4].includes(this.activity.recharge_type)) {
        if (!this.formState.date) {
          return false;
        } else {
          params.date = this.formState.date;
        }
      }
      const res = await ApiRebateGetUserPaySum(params);
      this.paySum = res.data.amount;
    },
    async submit() {
      const params = {
        act_id: this.actId,
        game_id: this.gameId,
        xh_id: this.formState.selectedXh.id,
        role_id: this.formState.role_id,
        rolename: this.formState.rolename,
        rolearea: this.formState.rolearea,
        threshold_text: this.formState.threshold_text,
        contact_info: this.formState.contact_info,
        remarks: this.formState.remarks,
        imgs: JSON.stringify(this.fileList),
        date: this.formState.date,
        name_text: this.formState.name_text,
      };

      try {
        this.$toast.loading({
          message: this.$t('提交中'),
          forbidClick: false,
          duration: 2000,
        });
        const res = await ApiRebateSubmitRebate(params);
        this.back();
      } finally {
      }
    },
    onSelectSuccess(info) {
      this.formState.selectedXh = info;
    },
    chooseXh() {
      if (!this.xiaohao_list?.length) {
        this.$toast('请登录游戏创建小号');
        return;
      }
      this.xhListShow = true;
    },
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}-${month}-${day}`;
    },
    onCalendarConfirm(date) {
      this.calendarShow = false;
      this.formState.date = this.formatDate(date / 1000);
    },
  },
};
</script>

<style lang="less" scoped>
.fanli-apply-page {
  .main {
    box-sizing: border-box;
    padding: 16 * @rem 18 * @rem 40 * @rem;
    .form {
      .form-item {
        width: 100%;
        display: flex;
        height: 28 * @rem;
        &:not(:first-of-type) {
          margin-top: 10 * @rem;
        }
        .form-item-title {
          width: 70 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 28 * @rem;
          height: 28 * @rem;
          text-align: justify;
          &::after {
            content: '';
            width: 100%;
            display: inline-block;
          }
        }
        .form-item-content {
          height: 100%;
          flex: 1;
          min-width: 0;
          font-size: 13 * @rem;
          color: #000000;
          line-height: 18 * @rem;
          margin-left: 5 * @rem;
          display: flex;
          align-items: center;
          span {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          input {
            height: 100%;
            width: 100%;
            border-bottom: 0.5px solid #ebebeb;
          }
          .form-item-select {
            border-bottom: 0.5px solid #ebebeb;
            padding-right: 20 * @rem;
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%) rotate(90deg);
              display: block;
              width: 18 * @rem;
              height: 18 * @rem;
              background: url(~@/assets/images/right-icon.png) center center
                no-repeat;
              background-size: 6 * @rem 10 * @rem;
            }
          }
          .amount {
            font-size: 13 * @rem;
            color: @themeColor;
            font-weight: 600;
          }
        }
      }
      .form-item-column {
        margin-top: 10 * @rem;
        .form-item-title {
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 18 * @rem;
          display: flex;
          align-items: center;
          span {
            font-size: 11 * @rem;
            color: #333;
          }
        }
        .photos {
          margin-top: 15 * @rem;
          /deep/ .van-uploader__preview {
            margin: 0 15 * @rem 15 * @rem 0;
          }
          /deep/ .van-uploader__preview-image {
            width: 88 * @rem;
            height: 88 * @rem;
            border-radius: 7 * @rem;
          }
          /deep/ .van-uploader__preview-delete {
            width: 20 * @rem;
            height: 20 * @rem;
            top: -10 * @rem;
            right: -10 * @rem;
            transform: scale(0.8);
            border-radius: 10 * @rem;
            background-color: rgb(154, 154, 154);
          }
          /deep/ .van-uploader__preview-delete-icon {
            transform: scale(1);
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .upload-btn {
            width: 88 * @rem;
            height: 88 * @rem;
          }
        }
        .form-item-textarea {
          box-sizing: border-box;
          width: 339 * @rem;
          height: 127 * @rem;
          background-color: #f4f4f4;
          border-radius: 6 * @rem;
          border: 0;
          resize: none;
          outline: none;
          margin: 7 * @rem auto 0;
          padding: 9 * @rem 10 * @rem;
          line-height: 22 * @rem;
          font-size: 13 * @rem;
          color: #333;
        }
      }
      .submit {
        margin: 30 * @rem auto 0;
        width: 255 * @rem;
        height: 49 * @rem;
        border-radius: 25 * @rem;
        background: linear-gradient(222deg, #6ddc8c 0%, #21b98a 100%);
        outline: none;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        color: #ffffff;
        font-weight: 500;
      }
    }
    .rule-container {
      margin-top: 40 * @rem;
      .rule-title {
        width: 352 * @rem;
        height: 22 * @rem;
        background: url('~@/assets/images/rebate/rebate-rule-title.png')
          no-repeat;
        background-size: 352 * @rem 22 * @rem;
        margin: 0 auto;
      }
      .rule-content {
        color: #757575;
        font-size: 12 * @rem;
        line-height: 18 * @rem;
        margin-top: 10 * @rem;
        /deep/ p {
          margin-bottom: 10 * @rem;
        }
      }
    }
  }
}
</style>
