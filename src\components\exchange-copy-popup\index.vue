<template>
  <!-- 复制礼包弹窗 -->
  <van-dialog
    v-model="popupShow"
    :close-on-click-overlay="true"
    message-align="left"
    :lock-scroll="false"
    class="copy-dialog"
    :show-confirm-button="false"
  >
    <template v-if="info.cardpass">
      <div class="title-icon"></div>
      <div class="title"> 兑换成功 </div>
      <div class="desc">道具名：{{ info.title }}</div>
      <div class="desc">使用方式：{{ introduction }}</div>
      <div class="content">
        道具码：<span>{{ info.cardpass }}</span>
      </div>
      <div class="copy-btn btn" @click="copy(info.cardpass)"> 复制道具码 </div>
      <div class="close-popup-btn" @click="popupShow = false"></div>
    </template>
  </van-dialog>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    introduction() {
      if (this.info.cardtext) {
        return `${this.info.cardtext}`;
      } else {
        return `请在游戏中使用`;
      }
    },
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-button {
  border-radius: 16px;
}
.copy-dialog {
  box-sizing: border-box;
  width: 306 * @rem;
  border-radius: 18 * @rem;
  background-color: #fff;
  padding: 20 * @rem 60 * @rem 22 * @rem;
  overflow: unset;
  background: url(~@/assets/images/exchange-bg.png) center top no-repeat;
  background-size: 306 * @rem 140 * @rem;
  background-color: #fff;
  .title-icon {
    width: 110 * @rem;
    height: 66 * @rem;
    .image-bg('~@/assets/images/exchange-title-bg.png');
    position: absolute;
    top: -39 * @rem;
    left: 102 * @rem;
  }
  .close-popup-btn {
    width: 26 * @rem;
    height: 26 * @rem;
    .image-bg('~@/assets/images/close.png');
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -52 * @rem;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18 * @rem;
    color: #000000;
    font-weight: 600;
    margin-left: 4 * @rem;
    margin-top: 20 * @rem;
    margin-bottom: 20 * @rem;
  }
  .desc {
    font-size: 16 * @rem;
    line-height: 20 * @rem;
    color: #a1a3ad;
    font-weight: 400;
    margin-top: 7 * @rem;
    padding: 0 5 * @rem;
  }
  .content {
    font-size: 16 * @rem;
    line-height: 20 * @rem;
    color: #000000;
    font-weight: 400;
    margin-top: 7 * @rem;
    padding: 0 5 * @rem;
    text-align: left;
    span {
      color: #6c7aff;
    }
  }
  .copy-btn {
    width: 165 * @rem;
    height: 40 * @rem;
    margin: 20 * @rem auto 0;
    background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
    border-radius: 20 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15 * @rem;
    font-weight: 400;
    color: #ffffff;
  }
}
</style>
