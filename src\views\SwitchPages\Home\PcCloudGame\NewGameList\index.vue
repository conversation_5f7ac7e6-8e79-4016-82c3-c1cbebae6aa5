<template>
  <div class="developer-games-container page">
    <nav-bar-2 :title="header_title" :placeholder="false" :border="true">
    </nav-bar-2>
    <van-loading v-if="!loadSuccess && !defaultPageShow" />
    <div class="loading-content" v-if="!loadSuccess && defaultPageShow">
      <default-error-page @callback="parentCallback" />
    </div>
    <yy-list
      v-else-if="loadSuccess && resultList.length"
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh()"
      @loadMore="loadMore()"
      :empty="empty"
      :tips="tips"
    >
      <div class="other-game-list">
        <div
          class="other-game-item"
          v-for="(item, index) in resultList"
          :key="index"
        >
          <cloud-game-item :info="item"></cloud-game-item>
        </div>
      </div>
    </yy-list>
    <div class="emptyData" v-else-if="loadSuccess && !resultList.length">
      <div class="box">
        <img src="~@/assets/images/cloud-game/empty-data-img.png" alt="" />
        <div class="text">暂无数据</div>
      </div>
    </div>
    <div
      class="return-top"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>
  </div>
</template>

<script>
import { ApiCloudGameGetNewList } from '@/api/views/game.js';
import CloudGameItem from '../components/cloud-game-item/index.vue';
export default {
  name: 'NewGameList',
  components: { CloudGameItem },
  data() {
    return {
      header_title: '新游速递',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无游戏',
      showReturnTop: false,
      defaultPageShow: false,
      loadSuccess: false, //加载完毕
    };
  },
  async created() {
    this.addScrollEvent();
  },
  beforeDestroy() {
    this.removeScrollEvent();
  },
  async mounted() {},
  async activated() {
    await this.getSimulatorGetNewGameList();
  },
  methods: {
    async parentCallback() {
      this.loadSuccess = false;
      this.defaultPageShow = false;
      await this.getSimulatorGetNewGameList();
    },
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    async getSimulatorGetNewGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      try {
        const res = await ApiCloudGameGetNewList({
          page: this.page,
          listRows: this.listRows,
        });

        this.loadingObj.loading = false;
        if (action === 1 || this.page === 1) {
          this.resultList = [];
          if (!res.data.game_list.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.resultList.push(...res.data.game_list);
        if (res.data.game_list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
        this.loadSuccess = true;
      } catch (error) {
        if (!navigator.onLine) {
          this.loadSuccess = false;
          this.defaultPageShow = true;
        }
      } finally {
        this.$nextTick(() => {
          this.loadingObj.loading = false;
        });
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getSimulatorGetNewGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getSimulatorGetNewGameList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.developer-games-container {
  padding: 0 18 * @rem;
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .game-list-box {
    margin-top: 50 * @rem;
    .other-game-list {
      .other-game-item {
        margin-bottom: 20 * @rem;
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .emptyData {
    height: calc(100vh - 50 * @rem);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .box {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 128 * @rem;
        height: 128 * @rem;
      }
      .text {
        margin-top: 16 * @rem;
        height: 18 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #666666;
        line-height: 18 * @rem;
      }
    }
  }
  .return-top {
    position: fixed;
    right: 5 * @rem;
    bottom: 100 * @rem;
    width: 50 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/exclusive-activity/return-top.png) center
      center no-repeat;
    background-size: 50 * @rem 50 * @rem;
    z-index: 100;
  }
}
</style>
