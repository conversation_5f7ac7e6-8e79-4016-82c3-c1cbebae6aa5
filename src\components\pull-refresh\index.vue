<template>
  <div class="pull-refresh">
    <van-pull-refresh
      v-model="loading"
      :success-text="successText"
      :loosing-text="loosingText"
      :pulling-text="pullingText"
      :loading-text="loadingText"
      @refresh="onRefresh"
      :disabled="isPullRefresh"
    >
      <slot></slot>
    </van-pull-refresh>
  </div>
</template>

<script>
import { PullRefresh } from 'vant';
export default {
  name: 'PullRefresh',
  components: {
    'van-pull-refresh': PullRefresh,
  },
  model: {
    prop: 'reloading',
    event: 'update',
  },
  props: {
    successText: {
      type: String,
      default: '刷新成功',
    },
    loosingText: {
      type: String,
      default: '释放即可刷新...',
    },
    pullingText: {
      type: String,
      default: '下拉刷新...',
    },
    loadingText: {
      type: String,
      default: '刷新中...',
    },
    reloading: {
      type: Boolean,
      default: false,
    },
    isPullRefresh: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: this.reloading,
    };
  },
  watch: {
    loading(newVal) {
      this.$emit('update', newVal);
    },
    reloading(newVal) {
      this.loading = newVal;
    },
  },
  methods: {
    onRefresh() {
      this.$emit('refresh');
    },
  },
};
</script>

<style lang="less" scoped>
.pull-refresh {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 0;
  min-height: 0;
  /deep/ .van-pull-refresh {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
  }
  /deep/ .van-pull-refresh__track {
    flex-grow: 1;
    flex-shrink: 0;
  }
}
</style>
