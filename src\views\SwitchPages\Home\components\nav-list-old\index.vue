<template>
  <!-- 金刚区 -->
  <div class="nav-list-component">
    <swiper
      id="navListSwiper"
      ref="navListSwiper"
      :options="swiperOptions"
      :auto-update="true"
      style="width: 100%; margin: 0 auto"
      v-if="navList.length > 0"
    >
      <swiper-slide
        class="nav-item btn"
        v-for="(item, index) in navList"
        :key="index"
      >
        <template>
          <div class="nav-icon" :class="item.icon_url">
            <div
              class="img"
              :style="{ backgroundImage: `url(${item.icon_url})` }"
            ></div>
          </div>
          <div class="nav-name">{{ item.text1 }}</div>
        </template>
      </swiper-slide>
    </swiper>
    <div class="swiper-scrollbar"></div>
  </div>
</template>

<script>
import { PageName } from '@/utils/actionCode.js';
export default {
  name: 'NavList',
  props: {
    navList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    let that = this;
    return {
      swiperOptions: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.swiper-scrollbar',
        },
        on: {
          click: function () {
            setTimeout(() => {
              let nav = that.navList[this.clickedIndex];
              that.toPage(PageName[nav.action_code]);
            }, 0);
          },
        },
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      // 解决金刚区点击穿透的问题
      document
        .querySelector('#navListSwiper')
        .addEventListener('touchstart', function (e) {
          e.preventDefault();
        }),
        {
          passive: false,
        };
    });
  },
};
</script>

<style lang="less" scoped>
.nav-list-component {
  padding: 10 * @rem 0 10 * @rem;
  width: 100%;
  .swiper-scrollbar {
    margin: 13 * @rem auto 0;
    width: 32 * @rem;
    height: 4 * @rem;
  }
  /deep/ .swiper-scrollbar-drag {
    background: @themeColor;
  }
  .nav-item {
    width: 78 * @rem;
    flex-shrink: 0;
    &:nth-of-type(1) {
      margin-left: 0;
    }
    .nav-icon {
      width: 46 * @rem;
      height: 46 * @rem;
      border-radius: 16 * @rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      // background-position: center center;
      // background-repeat: no-repeat;
      // background-size: 50 * @rem 50 * @rem;
      margin: 0 auto;
      // &.changwan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.lingquan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.chongzhi-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.kaifu-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      img {
        width: 100%;
        height: 100%;
      }
      .img {
        width: 100%;
        height: 100%;
        background-size: 100%;
      }
    }
    .nav-name {
      text-align: center;
      font-size: 13 * @rem;
      color: #383838;
      font-weight: 400;
      margin-top: 8 * @rem;
    }
  }
}
</style>
