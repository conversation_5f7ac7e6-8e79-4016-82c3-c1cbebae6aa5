<template>
  <div class="user-recommend">
    <nav-bar-2 :title="$t('玩家推荐')" :border="true"></nav-bar-2>
    <div class="main">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
      >
        <div class="recommend-list">
          <div
            class="recommend-item"
            v-for="(item, index) in recommendList"
            :key="index"
          >
            <user-recommend-item :info="item"></user-recommend-item>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiGameGetRecommendList } from '@/api/views/game.js';
import userRecommendItem from '@/components/user-recommend-item/index.vue';
export default {
  components: {
    userRecommendItem,
  },
  data() {
    return {
      recommendList: [],
      page: 1,
      listRows: 10,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      empty: false,
    };
  },
  async created() {
    await this.getRecommendList();
  },
  methods: {
    async getRecommendList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameGetRecommendList({
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.recommendList = [];
      }
      this.recommendList.push(...list);
      if (!this.recommendList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getRecommendList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getRecommendList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.user-recommend {
  .main {
    .recommend-list {
      .recommend-item {
        margin-top: 22 * @rem;
        &:first-of-type {
          margin-top: 15 * @rem;
        }
      }
    }
  }
}
</style>
