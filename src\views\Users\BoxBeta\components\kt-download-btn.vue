<template>
  <div
    :class="[downloadState[state], 'base']"
    @click="onClick"
    :style="{ width: width + '*@rem' }"
  >
    <div
      class="progress-fill"
      :style="{ background: color, width: progress + '%' }"
    >
      <div class="text-box">
        <span class="text">{{ text }}</span>
        <i class="edition-text" v-if="editionText">（{{ editionText }}）</i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KtDownloadBtn',
  props: {
    width: {
      type: Number,
      default: 335,
    },
    color: {
      type: String,
      default: '#21ce9a',
    },
    progress: {
      type: [String, Number],
      default: 0,
    },
    initText: {
      type: String,
      default: '下载',
    },
    successText: {
      type: String,
      default: '完成',
    },
    failedText: {
      type: String,
      default: '失败',
    },
    editionText: {
      type: String,
      default: '', // 版本号
    },
  },
  data() {
    return {
      isStart: false,
      isSuccess: false,
      isFailed: false,
      downloadState: {
        0: 'downloadingState',
        1: 'successState',
        2: 'failedState',
        3: 'defaultState',
      },
    };
  },
  computed: {
    text() {
      switch (this.state) {
        case 0:
          return `${this.progress}%`;
        case 1:
          return this.successText;
        case 2:
          return this.failedText;
        default:
          return this.initText;
      }
    },
    state() {
      if (this.isSuccess) {
        return 1; // SUCCESS
      } else if (this.isFailed) {
        return 2; // FAILED
      } else if (this.isStart) {
        return 0; // DOWNLOADING
      } else {
        return 3; // DEFAULT
      }
    },
  },
  methods: {
    onClick() {
      // console.log('Current States:', {
      //   isStart: this.isStart,
      //   isSuccess: this.isSuccess,
      //   isFailed: this.isFailed,
      // });

      if (this.isStart && !this.isSuccess && !this.isFailed) {
        // 正在下载过程中，点击按钮不应该触发新的下载逻辑
        this.$emit('startCallback', '正在下载，请稍候');
      } else if (this.isSuccess) {
        // 下载成功后可以触发相应的成功逻辑
        this.$emit('successCallback', '下载已完成');
      } else if (this.isFailed) {
        // 下载失败后的处理逻辑
        this.$emit('failedCallback', '下载失败，请重试');
      } else {
        // 开始新的下载流程
        this.start();
        this.$emit('startDownload', '开始下载');
      }
    },
    init() {
      this.isStart = false;
      this.isSuccess = false;
      this.isFailed = false;
    },
    start() {
      this.isStart = true;
      this.isSuccess = false;
      this.isFailed = false;
      this.progress = 0; // 每次下载从0开始
    },
    success() {
      this.isSuccess = true;
      this.isFailed = false;
    },
    failed() {
      this.isFailed = true;
      this.isSuccess = false;
    },
  },
};
</script>

<style scoped lang="less">
.base {
  width: 335 * @rem;
  margin: 0 auto;
  height: 50 * @rem;
  line-height: 50 * @rem;
  background: #21ce9a;
  border-radius: 29 * @rem;
  text-align: center;
  color: #ffffff;
  overflow: hidden;
  position: relative;
  .progress-fill {
    display: block;
    text-align: center;
    height: 100%;
    max-width: 100%;
    transition: 0.2s width ease-in-out;
    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      //   background-image: linear-gradient(
      //     -45deg,
      //     rgba(255, 255, 255, 0.2) 25%,
      //     transparent 25%,
      //     transparent 50%,
      //     rgba(255, 255, 255, 0.2) 50%,
      //     rgba(255, 255, 255, 0.2) 75%,
      //     transparent 75%,
      //     transparent
      //   );
      background-size: 40 * @rem 40 * @rem;
      //   animation: loading-line 2s linear infinite;
    }
    .text-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      .text {
        font-family:
          PingFang HK,
          PingFang HK;
        font-weight: 500;
        font-size: 16 * @rem;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
      }
      .edition-text {
      }
    }
  }
}

.defaultState {
  color: #ffffff;
  background: #21ce9a;
}

.successState {
  background: #21ce9a;
  color: #ffffff;
}

.failedState {
  color: #ffffff;
  background-image: linear-gradient(90deg, #ff5608, #ff8a18);
}

.downloadingState {
  //   pointer-events: none;
  background-color: rgb(232, 232, 232);
  font-weight: bold;
  color: #fff;
  // box-shadow:
  //   0 0 0 3 * @rem rgba(255, 255, 255, 0.8),
  //  6 * @rem 2 * @rem 12 * @rem -2 * @rem rgba(0, 0, 0, 0.6);
}

@keyframes loading-line {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40 * @rem 40 * @rem;
  }
}
</style>
