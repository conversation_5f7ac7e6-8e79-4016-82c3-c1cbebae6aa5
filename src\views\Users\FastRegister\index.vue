<template>
  <div class="page">
    <nav-bar-2 :placeholder="false" bgStyle="transparent"> </nav-bar-2>
    <div class="main">
      <div class="top-banner">
        <div class="page-title">快速注册</div>
      </div>
      <div class="form">
        <div class="field-title">您的账号</div>
        <div class="field">
          <input type="text" :value="username" disabled />
          <div class="right">
            <div class="text" @click="copy(username)">复制</div>
          </div>
        </div>

        <div class="field-title">请输入密码</div>
        <div class="field">
          <input
            type="text"
            v-model="password"
            placeholder="请输入8~16位数字或字母"
          />
        </div>

        <div class="field-title">请选择密保问题</div>
        <security-question-bar
          class="field question-box"
          :selectedQuestion.sync="selectedQuestion"
        ></security-question-bar>

        <div class="field-title">密保问题答案</div>
        <div class="field">
          <input
            type="text"
            v-model="answer"
            placeholder="最长可输入10个字符"
          />
        </div>
      </div>

      <div class="button" @click="commit()">登录/注册</div>
      <div class="explain">
        <input type="checkbox" v-model="ifAgreement" />{{
          $t('登录即代表您已同意')
        }}
        <div
          class="link"
          @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
        >
          《{{ $t('用户协议') }}》
        </div>
        {{ $t('与') }}
        <div class="link" @click="handleLink($h5Page.yinsizhengce, '隐私条款')">
          《隐私条款》
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiUserGetUsername, ApiUserRegisterNew } from '@/api/views/users';
import { loginSuccess, getQueryVariable } from '@/utils/function';
import { mapGetters, mapMutations } from 'vuex';
import securityQuestionBar from '@/components/security-question-bar';
import useConfirmAgreement from '@/components/yy-confirm-agreement/index.js';
export default {
  name: 'PhoneLogin',
  components: {
    securityQuestionBar,
  },
  data() {
    return {
      username: '',
      password: '',
      answer: '',
      selectedQuestion: {
        title: '请选择问题',
      },

      ifAgreement: false,
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },

  async created() {
    // 获取随机用户名
    await this.getUsername();
  },
  methods: {
    async getUsername() {
      const res = await ApiUserGetUsername();
      this.username = res.data.username;
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
    async commit() {
      if (this.password === '') {
        this.$toast(this.$t('请输入正确的密码'));
        return false;
      }

      const hadAgree = await useConfirmAgreement(this.ifAgreement);
      if (!hadAgree) return;

      this.ifAgreement = true;

      if (!this.selectedQuestion.id) {
        this.$toast('请选择密保问题');
        return;
      }
      if (!this.answer) {
        this.$toast('请填写密保答案');
        return;
      }
      const toast1 = this.$toast.loading({
        message: '加载中..',
        forbidClick: true,
        duration: 0,
      });
      let params = {
        username: this.username,
        password: this.password,
        question_1: this.selectedQuestion.id,
        answer_1: this.answer,
      };
      const res = await ApiUserRegisterNew(params);
      loginSuccess(res);
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .main {
    flex: 1;
    padding-bottom: 40 * @rem;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 200 * @rem;
    background-image: url(~@/assets/images/users/login-top-bg.png);
    background-size: 100% 200 * @rem;
    background-repeat: no-repeat;
    padding-left: 30 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: bold;
      line-height: 40 * @rem;
      margin-top: 113 * @rem;
    }
    .login-desc {
      font-size: 14 * @rem;
      color: #9a9a9a;
      line-height: 20 * @rem;
      margin-top: 5 * @rem;
    }
  }
  .form {
    padding: 0 30 * @rem;
    margin-top: 0 * @rem;
    .field-title {
      font-size: 16 * @rem;
      color: #565656;
      line-height: 22 * @rem;
    }

    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 42 * @rem;
      border-bottom: 0.5 * @rem solid #e0e0e0;
      margin-top: 4 * @rem;
      margin-bottom: 32 * @rem;
      &.question-box {
        border: 0;
      }
      input {
        min-width: 0;
        flex: 1;
        height: 100%;
        line-height: 42 * @rem;
        font-size: 14 * @rem;
      }
      .right {
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .text {
          box-sizing: border-box;
          padding: 0 10 * @rem;
          box-sizing: border-box;
          height: 32 * @rem;
          line-height: 30 * @rem;
          font-size: 13 * @rem;
          text-align: center;
          color: @themeColor;
          border: 1 * @rem solid @themeColor;
          border-radius: 18 * @rem;
          &.text2 {
            border: 1 * @rem solid #a4a4a4;
            color: #a4a4a4;
          }
        }
        span {
          position: absolute;
          top: 0;
          right: 0;
          display: block;
          color: #a4a4a4;
        }
      }
      .clear {
        width: 16 * @rem;
        height: 16 * @rem;
        padding: 0 10 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .country-code {
        display: flex;
        height: 42 * @rem;
        align-items: center;
        padding-left: 9 * @rem;
        position: relative;
        &::before {
          content: '';
          width: 1 * @rem;
          height: 11 * @rem;
          background-color: #dadada;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        .country-code-text {
          font-size: 16 * @rem;
          color: #000000;
        }
        .arrow-down {
          width: 10 * @rem;
          height: 6 * @rem;
          .image-bg('~@/assets/images/users/arrow-down.png');
          margin-left: 5 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
  }
  .button {
    height: 50 * @rem;
    margin: 40 * @rem 30 * @rem 0;
    text-align: center;
    line-height: 50 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
  }
  .explain {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #797979;
    font-size: 12 * @rem;
    margin-top: 16 * @rem;
    input[type='checkbox'] {
      width: 12 * @rem;
      height: 12 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center;
      background-size: 100%;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    .text {
      display: flex;
      margin-bottom: 31 * @rem;
      justify-content: center;
      align-items: center;
      font-size: 14 * @rem;
      color: #999999;
      span {
        display: block;
        width: 30 * @rem;
        height: 1 * @rem;
        margin: 0 6 * @rem;
        background: #dcdcdc;
      }
    }
    .wechat {
      width: 40 * @rem;
      height: 40 * @rem;
      margin: 0 auto 27 * @rem;
      background-image: url('~@/assets/images/users/wechat.png');
      background-size: 100%;
    }
  }
}
</style>
