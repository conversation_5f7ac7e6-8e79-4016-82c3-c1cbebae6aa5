<template>
  <div class="game-collect-page page">
    <nav-bar-2
      :title="externalGameInfo.title"
      :placeholder="false"
      :border="true"
    >
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: externalGameInfo.is_collect }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-2>

    <div class="main">
      <div class="external-box">
        <div class="external-header">
          <div class="header-img">
            <img :src="externalGameInfo.list_banner" alt="" />
          </div>
          <div class="header-info">
            <div class="title">{{ externalGameInfo.title }}</div>
            <div class="up-info" v-if="externalGameInfo.up_info">
              <div
                class="up-info-avatar"
                @click="
                  toPage('UpMine', {
                    mem_id: externalGameInfo.up_info.user_id,
                  })
                "
              >
                <div class="avatar">
                  <div
                    class="user-avatar"
                    v-if="externalGameInfo.up_info.avatar"
                  >
                    <img :src="externalGameInfo.up_info.avatar" alt="" />
                  </div>
                </div>
                <span class="nickname"
                  >集主：{{ externalGameInfo.up_info.nickname }}</span
                >
              </div>
              <div
                :class="{ empty: externalGameInfo.up_info.is_focus == 1 }"
                @click="handleFollow"
                class="right"
              >
                {{ follow_status }}
              </div>
            </div>
          </div>
        </div>
        <div class="external-dsc">
          <div class="theme">
            <div class="types">
              <span
                class="type"
                v-for="(item, index) in externalGameInfo.theme_list"
                :key="index"
                >{{ item }}</span
              >
            </div>
            <span class="msg" v-if="externalGameInfo.game_count_str"
              >共
              <span class="number">{{ externalGameInfo.game_count_str }}</span>
              款游戏
            </span>
          </div>
          <div class="desc">
            <div>{{ externalGameInfo.desc }}</div>
          </div>
        </div>
        <div class="bg-h8"> </div>
        <div class="game-collect-list">
          <yy-list
            class="game-list-box"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh()"
            @loadMore="loadMore()"
            :check="false"
            :empty="empty"
            :tips="tips"
          >
            <div class="other-game-list">
              <div
                class="other-game-item"
                v-for="(item, index) in resultList"
                :key="index"
              >
                <other-game-item :couponItem="item"></other-game-item>
              </div>
            </div>
          </yy-list>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiCollectCollectionGame } from '@/api/views/game.js';
import {
  ApiCollectGetUpCollectInfo,
  ApiCollectGetUpGameList,
} from '@/api/views/upCollection.js';
import OtherGameItem from '@/views/Games/DevelopersOtherGames/Components/other-game-item';
import { ApiUserFollowUser } from '@/api/views/users.js';
export default {
  name: 'GameCollect',
  components: { OtherGameItem },
  data() {
    return {
      id: 0,
      externalGameInfo: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无游戏',
    };
  },
  computed: {
    follow_status() {
      return parseInt(this.externalGameInfo.up_info.is_focus) === 0
        ? '+关注'
        : '已关注';
    },
  },
  async activated() {
    this.id = this.$route.params.id;
    await this.getCollectGetUpCollectInfo();
    await this.getGameGetCPOtherGame();
  },
  methods: {
    // 获取合集信息
    async getCollectGetUpCollectInfo() {
      try {
        const res = await ApiCollectGetUpCollectInfo({ id: this.id });
        this.externalGameInfo = res.data.info;
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'NotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
        }
      }
    },
    // 设置收藏
    setCollectStatus() {
      let status;
      if (!this.externalGameInfo.is_collect) {
        status = 1;
      } else {
        status = 0;
      }
      ApiCollectCollectionGame({
        id: this.externalGameInfo.id,
        type: status,
      }).then(res => {
        this.externalGameInfo.is_collect = !this.externalGameInfo.is_collect;
        this.externalGameInfo.collect = this.externalGameInfo.is_collect
          ? this.externalGameInfo.collect + 1
          : this.externalGameInfo.collect - 1;
        if (this.externalGameInfo.collect < 0) {
          this.externalGameInfo.collect = 0;
        }
      });
    },
    // 处理关注
    async handleFollow() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      try {
        const res = await ApiUserFollowUser({
          memId: this.externalGameInfo.up_info.user_id,
          type: this.externalGameInfo.up_info.is_focus == 0 ? 1 : 0,
        });
        this.$toast(res.msg);
        await this.getCollectGetUpCollectInfo();
      } finally {
      }
    },
    async getGameGetCPOtherGame(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiCollectGetUpGameList({
        id: this.id,
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        this.finished = false;
        await this.getGameGetCPOtherGame();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameGetCPOtherGame(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-collect-page {
  .collect-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    padding: 10 * @rem;
    background: url(~@/assets/images/games/collect-black.png) center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    &.collect-btn-white {
      background: url(~@/assets/images/games/collect.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.had {
      background-image: url(~@/assets/images/games/collect-success.png);
      background-size: 28 * @rem 28 * @rem;
    }
  }
  .main {
    margin-top: 50 * @rem;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    .external-box {
      display: flex;
      flex-direction: column;
      margin-top: 30 * @rem;

      .external-header {
        display: flex;
        align-items: center;
        height: 80 * @rem;
        padding: 0 18 * @rem;
        overflow: hidden;
        .header-img {
          height: 80 * @rem;
          width: 140 * @rem;
          border-radius: 10 * @rem;
          overflow: hidden;
          flex-shrink: 0;
        }
        .header-info {
          margin-left: 10 * @rem;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .title {
            font-size: 16 * @rem;
            font-weight: 500;
            white-space: nowrap;
          }
          .up-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .up-info-avatar {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .avatar {
                width: 24 * @rem;
                height: 24 * @rem;
                border-radius: 50%;
                background: #ccc;
                overflow: hidden;
                .user-avatar {
                  width: 100%;
                  height: 100%;
                  border-radius: 50%;
                  overflow: hidden;
                }
              }
              .nickname {
                margin: 0 5 * @rem;
                font-size: 11 * @rem;
                color: #000;
                white-space: nowrap;
                max-width: 110 * @rem;
                overflow: hidden;
              }
            }
            .right {
              width: 56 * @rem;
              height: 24 * @rem;
              border: 1px solid @themeColor;
              border-radius: 12 * @rem;
              display: flex;
              justify-content: center;
              align-items: center;
              color: @themeColor;
              &.empty {
                border: 1px solid #dcdde1;
                background: #dcdde1;
                color: rgba(0, 0, 0, 0.3);
              }
            }
          }
        }
      }
      .external-dsc {
        display: flex;
        flex-direction: column;
        margin-bottom: 15 * @rem;
        padding: 0 18 * @rem;
        .theme {
          margin: 15 * @rem 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .types {
            display: flex;
            align-items: center;
            flex: 1;
            flex-shrink: 0;
            height: 20 * @rem;
            line-height: 20 * @rem;
            flex-flow: wrap;
            overflow: hidden;
            .type {
              box-sizing: border-box;
              height: 20 * @rem;
              margin-right: 10 * @rem;
              font-size: 12 * @rem;
              color: #21b98a;
              padding: 0 7 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              border-radius: 5 * @rem;
              border: 1px solid #12b289;
            }
          }
          .msg {
            display: flex;
            white-space: nowrap;
            .number {
              color: red;
            }
          }
        }
        .desc {
          div {
            line-height: 20 * @rem;
          }
        }
      }
      .bg-h8 {
        width: 100%;
        min-height: 8 * @rem;
        background: #f8f8f8;
      }
      .game-collect-list {
        padding: 0 18 * @rem;
        .game-list-box {
          .other-game-list {
            .other-game-item {
              margin-bottom: 20 * @rem;
              &:first-child {
                margin-top: 10 * @rem;
              }
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}
</style>
