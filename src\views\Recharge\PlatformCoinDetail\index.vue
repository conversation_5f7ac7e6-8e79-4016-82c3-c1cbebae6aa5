<template>
  <div class="page platform-coin-detail-page">
    <nav-bar-2
      :border="true"
      :title="$t('平台币明细')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
      >
        <div class="record-list">
          <div
            class="record-item"
            v-for="(item, index) in coinDetailList"
            :key="index"
          >
            <div class="content">
              <div class="title">{{ item.show_title }}</div>
              <div class="coin">
                {{ addPlus(item.num_sign) }}{{ item.ptb_cnt }}{{ $t('个') }}
              </div>
            </div>
            <div class="bottom">
              <div class="date">{{ item.create_time }}</div>
              <div class="tips">{{ item.show_info }}</div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>
<script>
import { ApiPlatCoinDetail } from '@/api/views/recharge.js';
export default {
  name: 'PlatformCoinDetail',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 30,
      coinDetailList: [],
      empty: false,
    };
  },
  methods: {
    async getList() {
      const res = await ApiPlatCoinDetail({
        page: this.page,
        listRows: this.listRows,
      });
      if (this.page === 1) this.coinDetailList = [];
      this.coinDetailList.push(...res.data);
      if (this.coinDetailList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
      this.page++;
    },
    addPlus(num) {
      return num == 0 ? '-' : '+';
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-detail-page {
  .main {
    display: flex;
    flex-direction: column;
    flex: 1;

    .record-list {
      .record-item {
        border-bottom: 1px solid #eeeeee;
        padding: 10 * @rem 14 * @rem;

        .content {
          display: flex;
          justify-content: space-between;

          .title {
            font-size: 15 * @rem;
            color: #333333;
          }

          .coin {
            font-size: 15 * @rem;
            color: #f60000;
          }
        }

        .bottom {
          display: flex;
          justify-content: space-between;
          margin-top: 5 * @rem;

          .date {
            font-size: 12 * @rem;
            color: #999999;
          }

          .tips {
            font-size: 12 * @rem;
            color: #999999;
          }
        }
      }
    }
  }
}
</style>
