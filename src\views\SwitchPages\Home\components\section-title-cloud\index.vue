<template>
  <div class="section-title-components">
    <div class="title">
      <span>{{ title }}</span>
    </div>
    <div class="more" @click="handleMore">
      {{ $t('查看更多') }}
      <div class="more-icon"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionTitle',
  data() {
    return {};
  },
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  methods: {
    handleMore() {
      this.$emit('handleMore');
    },
  },
};
</script>

<style lang="less" scoped>
.section-title-components {
  padding: 5 * @rem 14 * @rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 18 * @rem;
    color: #000;
    font-weight: bold;
    z-index: 2;
    span {
      font-weight: bold;
      position: relative;
      z-index: 2;
    }
    &:before {
      content: '';
      width: 46 * @rem;
      height: 14 * @rem;
      background: #ffd641;
      position: absolute;
      right: -8 * @rem;
      bottom: -2 * @rem;
      z-index: 1;
    }
  }
  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 65 * @rem;
    height: 23 * @rem;
    border-radius: 12 * @rem;
    font-size: 14 * @rem;
    color: #000000;
    .more-icon {
      width: 4 * @rem;
      height: 7 * @rem;
      background: url(~@/assets/images/home/<USER>
        no-repeat;
      background-size: 4 * @rem 7 * @rem;
      margin-left: 2 * @rem;
    }
  }
}
</style>
