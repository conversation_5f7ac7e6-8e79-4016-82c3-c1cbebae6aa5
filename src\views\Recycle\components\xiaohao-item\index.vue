<template>
  <div class="xiaohao-item-component">
    <div class="xiaohao-item" @click="selectXiaohao">
      <div class="top-content">
        <div class="title">
          {{ $t('小号') }}：<span>{{ info.nickname }}</span>
        </div>
        <div class="status" :style="{ color: info.status_info.color }">
          {{ info.status_info.str }}
        </div>
      </div>
      <div class="game-info">
        <div class="game-icon">
          <img :src="info.game_icon" alt="" />
        </div>
        <div class="game-name">
          {{ info.title
          }}<span class="game-subtitle" v-if="info.subtitle">{{
            info.subtitle
          }}</span>
        </div>
        <div class="right-icon"></div>
      </div>
    </div>
    <recycle-dialog
      v-model="dialogShow"
      :info="info"
      :payInfo="payInfo"
      @refresh="$emit('refresh')"
    ></recycle-dialog>
    <!-- 绑定手机 -->
    <bind-Mobile-dialog v-model="bindMobileDialogShow"></bind-Mobile-dialog>
    <!-- 绑定邮箱 -->
    <bind-Email-dialog v-model="bindEmailDialogShow"></bind-Email-dialog>
  </div>
</template>
<script>
import recycleDialog from '../recycle-dialog/index.vue';
import bindMobileDialog from '../bind-mobile-dialog/index.vue';
import bindEmailDialog from '../bind-email-dialog/index.vue';
import { ApiXiaohaoPaySum } from '@/api/views/xiaohao.js';
import { mapGetters } from 'vuex';
export default {
  name: 'XiaohaoItem',
  components: {
    recycleDialog,
    bindMobileDialog,
    bindEmailDialog,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogShow: false,
      bindMobileDialogShow: false,
      bindEmailDialogShow: false,
      payInfo: {},
    };
  },
  methods: {
    async selectXiaohao() {
      const fetchPayInfo = async () => {
        try {
          const res = await ApiXiaohaoPaySum({
            xhId: this.info.id,
            type: 2,
          });
          this.payInfo = res.data;
          this.dialogShow = !this.dialogShow;
        } catch (e) {
          console.error(e); // 可以在这里处理错误日志
        }
      };
      if (!this.isHw) {
        if (!this.userInfo.mobile.length) {
          this.bindMobileDialogShow = true;
          return false;
        }
        await fetchPayInfo();
      } else {
        if (!this.userInfo.email.length) {
          this.bindEmailDialogShow = true;
          return false;
        } else {
          await fetchPayInfo();
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      isHw: 'system/isHw',
    }),
  },
};
</script>

<style lang="less" scoped>
.xiaohao-item-component {
  background: #ffffff;
  box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
  border-radius: 12 * @rem;
  overflow: hidden;
  .top-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 * @rem 15 * @rem;
    background-color: #f5f5f6;
    height: 30 * @rem;
    .title {
      font-size: 14 * @rem;
      color: #9a9a9a;
      span {
        font-size: 14 * @rem;
        color: #9a9a9a;
      }
    }
    .status {
      font-size: 12 * @rem;
      font-weight: 600;
      color: #000;
    }
  }
  .game-info {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    height: 70 * @rem;
    padding: 0 15 * @rem;
    .game-icon {
      width: 38 * @rem;
      height: 38 * @rem;
      border-radius: 8 * @rem;
      background-color: #7c7c7c;
      overflow: hidden;
    }
    .game-name {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 11 * @rem;
      flex: 1;
      min-width: 0;
      display: flex;
      align-items: center;
      .game-subtitle {
        box-sizing: border-box;
        border: 1 * @rem solid fade(@themeColor, 80);
        border-radius: 3 * @rem;
        font-size: 11 * @rem;
        padding: 2 * @rem 3 * @rem;
        color: @themeColor;
        margin-left: 5 * @rem;
        vertical-align: middle;
        line-height: 1;
      }
    }
    .right-icon {
      width: 9 * @rem;
      height: 19 * @rem;
      background: url(~@/assets/images/recycle/right-icon.png) center center
        no-repeat;
      background-size: 9 * @rem 19 * @rem;
    }
  }
}
</style>
