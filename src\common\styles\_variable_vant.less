/* vant变量覆盖 */

/*tabBar*/
@tabbar-height: 100 * @rem;
/* navBar */
@nav-bar-background-color: @themeColor;
@nav-bar-icon-color: #fff;
@nav-bar-text-color: #fff;
@nav-bar-title-text-color: #fff;
@nav-bar-height: 50 * @rem;
/* sidebar */
@sidebar-selected-border-color: @themeColor;

.van-nav-bar--safe-area-inset-top {
  padding-top: @safeAreaTop;
  padding-top: @safeAreaTopEnv;
}
.android {
  .van-nav-bar--safe-area-inset-top {
    padding-top: var(--statusHeight);
  }
}
.ios {
  .van-nav-bar--safe-area-inset-top {
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
  }
}
