<template>
  <div class="game-activity-page" :style="{ background: bgc }" v-if="info.id">
    <rubber-band :topColor="bgc" :bottomColor="bgc">
      <div class="placeholder" :style="{ background: bgc }"></div>
      <div class="game-panel" @click="goToGame">
        <div
          class="top-shadow"
          :style="{
            background: topShadow,
          }"
        ></div>
        <img class="game-banner" :src="info.bg_img" alt="" />
        <div
          class="bottom-shadow"
          :style="{
            background: bottomShadow,
          }"
        ></div>
      </div>
      <div class="download-section" v-if="info.bg_img">
        <div class="download-btn btn" @click="goToGame">
          <img src="@/assets/images/game-activity/download-btn-2.png" alt="" />
        </div>
      </div>
      <div class="section section-1">
        <div class="section-title"></div>
        <div class="section-content">
          <div class="coupon-list">
            <div
              class="coupon-item"
              v-for="(item, index) in login.info.coupon_list"
              :key="index"
            >
              <div class="coupon-title">代金券</div>
              <div class="coupon-money">
                ¥<span>{{ item.money }}</span>
              </div>
              <div class="coupon-reach">满{{ item.reach_money }}可用</div>
            </div>
          </div>
          <div
            class="get-btn btn"
            @click="loginGet"
            v-if="!login.info.is_receive"
          >
            抽奖
          </div>
          <div class="get-btn had btn" v-else>已领取</div>
        </div>
      </div>
      <div class="section section-2" v-if="task.info">
        <div class="section-title"></div>
        <div class="section-content">
          <div class="task-tag">
            任务 {{ taskItem.level }}/{{ task.info.length }}
          </div>
          <div class="task-title">
            <span>{{ taskItem.level }}</span
            >{{ taskItem.control }}
          </div>
          <div class="task-cards">
            <div class="card-item">
              <div class="card-icon">
                <img :src="taskItem.img" alt="" />
              </div>
              <div
                class="get-btn btn"
                v-if="!taskItem.is_receive"
                @click="taskGet"
              >
                直接领取
              </div>
              <div
                class="get-btn had btn"
                v-else
                @click="showCopyBar(taskItem.get_card)"
              >
                已领取
              </div>
            </div>
            <div class="card-item">
              <div class="card-icon">
                <img :src="taskItem.up_img" alt="" />
              </div>
              <div
                class="get-btn btn"
                v-if="!taskItem.is_receive"
                @click="taskExpandGet"
              >
                膨胀领取
              </div>
              <div
                class="get-btn had btn"
                v-else
                @click="showCopyBar(taskItem.get_card)"
              >
                已领取
              </div>
            </div>
            <div class="right-arrow"></div>
          </div>
          <div class="bottom-tips">
            {{ task.text }}
          </div>
        </div>
      </div>
      <div class="section section-3">
        <div class="section-title"></div>
        <div class="section-content">
          <div class="invite-list">
            <div
              class="invite-item"
              v-for="(item, index) in invite.info"
              :key="index"
            >
              <div class="left">
                <div class="invite-title">
                  <span>{{ item.level }}</span>
                  <div class="title-text">{{ item.title }}</div>
                </div>
                <div class="invite-pic">
                  <img :src="item.img" alt="" />
                </div>
              </div>
              <div class="right">
                <div
                  class="get-btn btn"
                  v-if="!item.is_receive"
                  @click="inviteGet(item)"
                >
                  领取
                </div>
                <div
                  class="get-btn had btn"
                  v-else
                  @click="showCopyBar(item.get_card)"
                >
                  已领取
                </div>
                <div class="target">{{ item.control }}</div>
              </div>
            </div>
          </div>
          <div class="bottom-tips">
            <div class="tips-text">
              {{ invite.text }}
            </div>
          </div>
          <div class="invite-btn btn" @click="handleInvite">立即邀请好友</div>
        </div>
      </div>
    </rubber-band>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="copy-content">
        <div class="title">
          {{ $t('礼包码') }}
        </div>
        <div class="cardpass">{{ cardpass }}</div>
      </div>
      <div class="copy-btn btn" @click="copy(cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
    <!-- 小号选择弹窗 -->
    <xh-select-dialog
      :show.sync="xhDialogShow"
      :id="Number(info.game_id)"
      @handleGet="handleGet"
    ></xh-select-dialog>
    <!-- 消耗金币膨胀弹窗 -->
    <van-dialog
      v-model="expandPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="expand-popup"
    >
      <div class="popup-content">
        <div class="title"> 消耗金币领取膨胀 </div>
        <div class="task-cards">
          <div class="card-item">
            <div class="card-icon">
              <img :src="taskItem.img" alt="" />
            </div>
          </div>
          <div class="card-item">
            <div class="card-icon">
              <img :src="taskItem.up_img" alt="" />
            </div>
          </div>
          <div class="right-arrow"></div>
        </div>
        <div class="tips">
          {{ $t('确认消耗') }}
          <span>{{ taskItem.up_gold }}金币</span> 膨胀奖励？
        </div>
        <div class="bottom-btn">
          <div class="cancel btn" @click="expandPopupShow = false">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="taskUseGoldGet">
            {{ $t('确定') }}
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 代金券领取成功弹窗 -->
    <van-dialog
      v-model="couponDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="coupon-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        恭喜你获得 <span>¥ {{ couponPopupMoney }}</span> 代金券
      </div>
      <div class="desc">可前往 我的-代金券 页面查看</div>
      <div class="ok-btn btn" @click="couponDialogShow = false">
        {{ $t('知道了') }}
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiIndexGetDownActivityInfo,
  ApiIndexReceiveRrize,
} from '@/api/views/home.js';
import { BOX_goToGame } from '@/utils/box.uni.js';
import xhSelectDialog from '@/components/xh-select-dialog/index.vue';
export default {
  components: {
    xhSelectDialog,
  },
  data() {
    return {
      id: 0,
      info: {},
      bgc: '',
      invite: {},
      login: {},
      task: {},
      clickInfo: {}, // 点击领取的项的信息
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      cardpass: 'xxxxxx', // 弹窗的礼包码
      expandPopupShow: false, // 膨胀弹窗
      couponDialogShow: false, // 代金券领取成功弹窗
      couponPopupMoney: 0, // 领取到的代金券
    };
  },
  computed: {
    topShadow() {
      return `linear-gradient(360deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    bottomShadow() {
      return `linear-gradient(180deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    hexToRgba() {
      return `rgba(${parseInt('0x' + this.bgc.slice(1, 3))},${parseInt(
        '0x' + this.bgc.slice(3, 5),
      )},${parseInt('0x' + this.bgc.slice(5, 7))},0)`;
    },
    // 任务礼包完成进度
    taskItem() {
      if (!this.task.info) {
        return {};
      }
      // 任务礼包 -- 如果最后一项已领取，直接显示最后一项
      if (this.task.info[this.task.info.length - 1].is_receive) {
        return this.task.info[this.task.info.length - 1];
      }
      // 否则返回最近一个 未领取奖励的任务
      return this.task.info.find(item => {
        return item.is_receive == false;
      });
    },
  },

  async activated() {
    this.id = this.$route.params.id;
    await this.getActivityInfo();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    // 每日登陆抽奖
    loginGet() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.clickInfo = this.login.info;
      this.xhDialogShow = true;
    },
    // 任务奖励直接领取
    taskGet() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.clickInfo = this.taskItem;
      this.xhDialogShow = true;
    },
    // 任务奖励膨胀领取
    taskExpandGet() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.expandPopupShow = true;
    },
    // 金币膨胀逻辑
    taskUseGoldGet() {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.expandPopupShow = false;
      // 膨胀的id为up_id
      this.clickInfo = { ...this.taskItem, id: this.taskItem.up_id };
      this.xhDialogShow = true;
    },
    // 邀请奖励领取
    inviteGet(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.clickInfo = item;
      this.xhDialogShow = true;
    },
    // 页面数据
    async getActivityInfo() {
      const res = await ApiIndexGetDownActivityInfo({ id: this.id });
      let { info, invite, login, task } = res.data.list;
      this.info = info;
      this.invite = invite;
      this.login = login;
      this.task = task;
      this.bgc = this.info.bg_color;
    },
    goToGame() {
      BOX_goToGame(
        {
          params: {
            id: this.info.game_id,
            gameInfo: this.info,
          },
        },
        { id: this.info.game_id },
      );
    },
    // 领取奖励逻辑
    async handleGet(xh_id) {
      let params = { xh_id: xh_id };
      if (this.clickInfo.id) {
        params.id = this.clickInfo.id;
      }
      const res = await ApiIndexReceiveRrize(params);

      // card_type: 1->礼包 2->代金券
      if (this.clickInfo.card_type == 1) {
        // 礼包
        this.showCopyBar(res.data.cardpass);
      } else {
        // 代金券
        // 需要弹窗显示领取的代金券
        this.couponPopupMoney = res.data.money;
        this.couponDialogShow = true;
      }
      this.getActivityInfo();
    },
    showCopyBar(cardpass) {
      this.cardpass = cardpass ?? ' ';
      this.copyDialogShow = true;
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    // 邀请逻辑
    handleInvite() {
      let inviteUrl = `https://u.a3733.com/float.php/float/box/app_invite_register?referrer_user_id=${this.userInfo.user_id}&referrer_channel=${this.currentCps}`;
      this.$copyText(inviteUrl).then(
        res => {
          this.$toast(this.$t('链接已复制到剪贴板，快去邀请好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请联系客服'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.game-activity-page {
  min-height: 100vh;
  padding-bottom: 40 * @rem;
  .placeholder {
    position: relative;
    display: block;
    width: 100%;
    height: calc(56 * @rem + @safeAreaTop);
    height: calc(56 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .game-panel {
    position: relative;
    width: 100%;
    // height: 375 * @rem;
    // padding-bottom: 50 * @rem;
    .top-shadow {
      width: 100%;
      height: 150 * @rem;
      position: absolute;
      left: 0;
      top: -1 * @rem;
      z-index: 2;
    }
    .bottom-shadow {
      width: 100%;
      height: 90 * @rem;
      position: absolute;
      left: 0;
      bottom: 0 * @rem;
      z-index: 2;
    }
  }
  .download-section {
    margin-top: 10 * @rem;
    .download-btn {
      margin: 0 auto;
      width: 240 * @rem;
      height: 44 * @rem;
    }
  }
  .section {
    width: 355 * @rem;
    box-shadow: 0 * @rem 0 * @rem 8 * @rem 0 * @rem rgba(191, 0, 0, 0.06);
    border-radius: 20 * @rem;
    margin: 31 * @rem auto 0;
    background-color: #fff;
    position: relative;
    .section-title {
      background-size: 229 * @rem 30 * @rem;
      width: 229 * @rem;
      height: 30 * @rem;
      position: absolute;
      left: 0;
      right: 0;
      margin: 0 auto;
      top: -7 * @rem;
    }

    &.section-1 {
      .section-title {
        background-image: url(~@/assets/images/game-activity/section-1-title-bg.png);
      }
      .section-content {
        padding: 52 * @rem 0 16 * @rem;
        .coupon-list {
          display: flex;
          flex-wrap: nowrap;
          overflow-x: auto;
          padding-left: 20 * @rem;
          &::-webkit-scrollbar {
            display: none;
          }
          .coupon-item {
            box-sizing: border-box;
            width: 80 * @rem;
            height: 90 * @rem;
            background: url(~@/assets/images/game-activity/coupon-bg.png) center
              center no-repeat;
            background-size: 80 * @rem 90 * @rem;
            flex-grow: 1;
            flex-shrink: 0;
            margin-right: 16 * @rem;
            padding-top: 10 * @rem;
            .coupon-title {
              text-align: center;
              font-size: 14 * @rem;
              color: #ed8a44;
            }
            .coupon-money {
              text-align: center;
              font-size: 12 * @rem;
              color: #ff773d;
              margin-top: 3 * @rem;
              white-space: nowrap;
              span {
                font-size: 16 * @rem;
                color: #ff5d17;
                font-weight: bold;
              }
            }
            .coupon-reach {
              text-align: center;
              margin-top: 19 * @rem;
              font-size: 12 * @rem;
              color: #d48246;
              white-space: nowrap;
            }
          }
        }
        .get-btn {
          width: 160 * @rem;
          height: 36 * @rem;
          border-radius: 18 * @rem;
          margin: 20 * @rem auto 0;
          background-color: #ff7e06;
          font-size: 16 * @rem;
          color: #ffffff;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          &.had {
            background-color: #e4dfd9;
          }
        }
      }
    }
    &.section-2 {
      .section-title {
        background-image: url(~@/assets/images/game-activity/section-2-title-bg.png);
      }
      .section-content {
        position: relative;
        padding: 52 * @rem 17 * @rem 11 * @rem;
        .task-tag {
          position: absolute;
          right: 0;
          top: 24 * @rem;
          width: 63 * @rem;
          height: 24 * @rem;
          border-radius: 12 * @rem 0 0 12 * @rem;
          background-color: #ff7253;
          font-size: 12 * @rem;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .task-title {
          font-size: 14 * @rem;
          color: #745743;
          line-height: 25 * @rem;
          display: flex;
          align-items: center;
          span {
            color: #805e46;
            font-size: 20 * @rem;
            line-height: 25 * @rem;
            margin-right: 6 * @rem;
            font-weight: bold;
          }
        }

        .bottom-tips {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #666666;
          line-height: 15 * @rem;
          margin-top: 21 * @rem;
          &::before {
            content: '';
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/game-activity/wenhao.png) center
              center no-repeat;
            background-size: 12 * @rem 12 * @rem;
            margin-right: 4 * @rem;
            margin-top: -2 * @rem;
          }
        }
      }
    }
    &.section-3 {
      .section-title {
        background-image: url(~@/assets/images/game-activity/section-3-title-bg.png);
      }
      .section-content {
        padding: 35 * @rem 20 * @rem 16 * @rem;
        .invite-list {
          .invite-item {
            display: flex;
            align-items: center;
            &:not(:first-of-type) {
              margin-top: 16 * @rem;
            }
            .left {
              flex: 1;
              min-width: 0;
              margin-right: 10 * @rem;
              .invite-title {
                display: flex;
                .title-text {
                  font-size: 14 * @rem;
                  color: #745743;
                  line-height: 18 * @rem;
                  margin-top: 3 * @rem;
                }
                span {
                  color: #805e46;
                  font-size: 20 * @rem;
                  line-height: 25 * @rem;
                  margin-right: 6 * @rem;
                  font-weight: bold;
                }
              }
              .invite-pic {
                width: 80 * @rem;
                height: 50 * @rem;
                border-radius: 4 * @rem;
                overflow: hidden;
                background-color: #ebeef2;
                margin-top: 3 * @rem;
                margin-left: 17 * @rem;
              }
            }
            .right {
              .get-btn {
                width: 66 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                background-color: #ff7e06;
                font-size: 14 * @rem;
                color: #ffffff;
                display: flex;
                align-items: center;
                justify-content: center;
                &.had {
                  background-color: #e4dfd9;
                }
              }
              .target {
                font-size: 12 * @rem;
                color: #9c7255;
                margin-top: 4 * @rem;
                line-height: 15 * @rem;
                text-align: center;
              }
            }
          }
        }
        .bottom-tips {
          display: flex;
          justify-content: center;
          margin-top: 16 * @rem;
          padding: 0 24 * @rem;
          .tips-text {
            font-size: 12 * @rem;
            color: #9c7255;
            line-height: 15 * @rem;
            flex: 1;
            min-width: 0;
          }
          &::before {
            content: '';
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/game-activity/gantanhao.png) center
              center no-repeat;
            background-size: 12 * @rem 12 * @rem;
            margin-right: 4 * @rem;
          }
        }
        .invite-btn {
          width: 160 * @rem;
          height: 36 * @rem;
          background-color: #ff7e06;
          border-radius: 18 * @rem;
          font-size: 15 * @rem;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 18 * @rem auto 0;
        }
      }
    }
  }
}

.task-cards {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24 * @rem;
  .card-item {
    margin: 0 44 * @rem;
    .card-icon {
      width: 60 * @rem;
      height: 60 * @rem;
      border-radius: 6 * @rem;
      overflow: hidden;
      background-color: #ebeef2;
      margin: 0 auto;
    }
    .get-btn {
      width: 76 * @rem;
      height: 32 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ff7e06;
      border-radius: 16 * @rem;
      font-size: 14 * @rem;
      color: #ffffff;
      margin-top: 12 * @rem;
      &.had {
        background-color: #e4dfd9;
      }
    }
  }
  .right-arrow {
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 12 * @rem;
    width: 59 * @rem;
    height: 35 * @rem;
    background: url(~@/assets/images/game-activity/right-arrow.png) no-repeat;
    background-size: 59 * @rem 35 * @rem;
  }
}
.coupon-dialog {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 20 * @rem;
  padding: 40 * @rem 30 * @rem 18 * @rem;
  background: #fff url(~@/assets/images/game-activity/popup-bg.png) center top
    no-repeat;
  background-size: 300 * @rem auto;
  .title {
    font-size: 16 * @rem;
    color: #745743;
    line-height: 22 * @rem;
    font-weight: bold;
    text-align: center;
    span {
      color: #ff5d17;
      font-weight: bold;
    }
  }
  .desc {
    text-align: center;
    font-size: 14 * @rem;
    line-height: 20 * @rem;
    color: #9c7255;
    margin-top: 8 * @rem;
  }
  .ok-btn {
    margin: 17 * @rem auto 0;
    width: 102 * @rem;
    height: 35 * @rem;
    border-radius: 18 * @rem;
    background-color: #ff7e06;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    color: #ffffff;
  }
}
.expand-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 20 * @rem;
  padding: 30 * @rem 30 * @rem 18 * @rem;
  background: #fff url(~@/assets/images/game-activity/popup-bg.png) center top
    no-repeat;
  background-size: 300 * @rem auto;
  .popup-content {
    .title {
      font-size: 18 * @rem;
      font-weight: bold;
      color: #745743;
      text-align: center;
      line-height: 23 * @rem;
    }
    .info-item {
      width: 200 * @rem;
      margin: 20 * @rem auto 0;
      .info-icon {
        width: 80 * @rem;
        height: 80 * @rem;
        position: relative;
        margin: 0 auto;
        .info-icon-img {
          object-fit: cover;
          position: absolute;
          width: 70 * @rem;
          height: 70 * @rem;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        .frame {
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .info-title {
        text-align: center;
        font-size: 12 * @rem;
        line-height: 16 * @rem;
        color: @themeColor;
        font-weight: 600;
        margin-top: 8 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .tips {
      font-size: 14 * @rem;
      color: #805745;
      text-align: center;
      margin-top: 20 * @rem;
      line-height: 18 * @rem;

      span {
        color: #ff5d17;
        font-weight: bold;
      }
    }
    .bottom-btn {
      display: flex;
      justify-content: space-between;
      margin: 17 * @rem auto 0;
      .confirm {
        width: 110 * @rem;
        height: 35 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        border-radius: 18 * @rem;
        background-color: #ff7e06;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cancel {
        width: 110 * @rem;
        height: 35 * @rem;
        font-size: 13 * @rem;
        color: #7d7d7d;
        border-radius: 18 * @rem;
        background-color: #f2f2f2;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 20 * @rem;
  padding: 49 * @rem 15 * @rem 18 * @rem;
  background: #fff url(~@/assets/images/game-activity/popup-bg.png) center top
    no-repeat;
  background-size: 300 * @rem auto;
  .copy-content {
    display: flex;
    align-items: center;
    .title {
      font-size: 14 * @rem;
      color: #745743;
      font-weight: 500;
      margin-right: 12 * @rem;
    }
    .cardpass {
      box-sizing: border-box;
      flex: 1;
      min-width: 0;
      height: 40 * @rem;
      border-radius: 6 * @rem;
      padding: 0 10 * @rem;
      font-size: 14 * @rem;
      color: #000000;
      font-weight: 400;
      border: 1 * @rem solid #efefef;
      display: flex;
      align-items: center;
    }
  }
  .copy-btn {
    width: 102 * @rem;
    height: 35 * @rem;
    margin: 18 * @rem auto 0;
    background: #ff7e06;
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 400;
    color: #ffffff;
  }
}
</style>
