/* scss全局变量（定制主题） */

/* 状态栏高度 */
@statusHeightAZ: var(--statusHeight);
@safeAreaTop: calc(var(--statusHeight) + constant(safe-area-inset-top));
@safeAreaTopEnv: calc(var(--statusHeight) + env(safe-area-inset-top));
@safeAreaBottom: constant(safe-area-inset-bottom);
@safeAreaBottomEnv: env(safe-area-inset-bottom);

/* 设置rem倍数 */
@remNumber: 0.0267;
@rem: 0.0267rem;
@maxWidth: 450px;
// @pcmaxwidth: 414px;

/* 主题色相关 */
@themeColor: #2BBE88;
@themeBg: linear-gradient(90deg, #2BBE88 0%, #6DDC8C 99%);

/*背景*/
@bgColor: #fff;
@cbgColor: #fff;

/* 文字颜色 */
@titleColor: #000;
@textColor: #333;
@tipColor: #999;

/* 兼容全面屏 */
.min-height-safa-top (@height: 0px) {
  min-height: calc(100vh - @height - @safeAreaTop);
  min-height: calc(100vh - @height - @safeAreaTopEnv);
}

.fixed-center {
  // left: 50%;
  // transform: translateX(-50%);
  left: 0;
  right: 0;
  margin: 0 auto;
  max-width: @maxWidth;
}

.image-bg (@url) {
  background-image: url(@url);
  background-size: 100%;
  background-repeat: no-repeat;
}

:export {
  remLess: @rem;
  themeColorLess: @themeColor;
  bgColorLess: @bgColor;
  cbgColorLess: @cbgColor;
  textColorLess: @textColor;
  titleColorLess: @titleColor;
  tipColorLess: @tipColor;
  remNumberLess: @remNumber;
  safeAreaTop: @safeAreaTop;
  safeAreaTopEnv: @safeAreaTopEnv;
  safeAreaBottom: @safeAreaBottom;
  safeAreaBottomEnv: @safeAreaBottomEnv;
}