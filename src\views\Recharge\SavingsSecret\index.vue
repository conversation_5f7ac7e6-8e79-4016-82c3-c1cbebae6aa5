<template>
  <div class="page savings-secret-page">
    <nav-bar-2
      title="省钱秘籍"
      bgStyle="white"
      :border="false"
      :placeholder="true"
      :azShow="true"
    >
    </nav-bar-2>
    <div class="main">
      <div class="savings-list">
        <div
          class="savings-item"
          v-for="(item, index) in savingsList"
          :key="index"
        >
          <div class="item-title">
            {{ item.title }}
          </div>
          <div class="item-content" v-html="item.content"></div>
          <div class="btn item-btn" @click="goToPage(item)">
            {{ item.button_text }}&gt;&gt;
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiMoneySavingTipsIndex } from '@/api/views/recharge.js';
import { platform, BOX_openInNewWindow } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      pageTitle: '省钱秘籍',
      savingsList: [],
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
    await this.getSavingsTip();

    // 神策埋点
    this.$sensorsTrack('moneySavingTips_pageView')
  },
  methods: {
    // 获取省钱秘籍
    async getSavingsTip() {
      const res = await ApiMoneySavingTipsIndex();
      this.savingsList = res.data.list;
    },
    goToPage(item) {
      BOX_openInNewWindow(
        {
          name: item.url_info.web_path,
          params: {
            sensors_source: this.pageTitle,
          },
        },
        {
          url: item.url_info.url,
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.savings-secret-page {
  .main {
    padding-bottom: 40 * @rem;
    .savings-list {
      padding: 0 15 * @rem 0 20 * @rem;
      .savings-item {
        margin-top: 20 * @rem;
        &:not(:first-of-type) {
          margin-top: 40 * @rem;
        }
        .item-title {
          height: 18 * @rem;
          background: url(~@/assets/images/recharge/savings-secret-icon.png)
            left center no-repeat;
          background-size: 18 * @rem 18 * @rem;
          padding-left: 25 * @rem;
          font-size: 16 * @rem;
          line-height: 18 * @rem;
          color: #333333;
          font-weight: 600;
        }
        .item-content {
          margin-top: 10 * @rem;
          font-size: 13 * @rem;
          line-height: 18 * @rem;
          color: #444444;
          /deep/ p {
            margin-top: 10 * @rem !important;
          }
          /deep/ img {
            width: 100% !important; // 设置图片宽度为100%
            height: auto !important; // 设置图片高度为auto
            object-fit: contain;
            margin-top: 8 * @rem;
            display: block;
            border-radius: 4 * @rem;
          }
        }
        .item-btn {
          width: 176 * @rem;
          height: 36 * @rem;
          background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
          border-radius: 29 * @rem;
          margin: 20 * @rem auto 0;
          font-size: 14 * @rem;
          color: #ffffff;
          text-align: center;
          line-height: 36 * @rem;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
