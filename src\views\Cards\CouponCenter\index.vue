<template>
  <rubber-band :topColor="'#f5f5f6'" :bottomColor="'#f5f5f6'">
    <div class="coupon-center-page">
      <nav-bar-2
        :title="pageTitle"
        bgStyle="transparent-white"
        :border="false"
        :placeholder="false"
      >
        <template #right>
          <div class="my-coupon" @click="$router.push('/my_coupon')">
            {{ $t('我的代金券') }}
          </div>
        </template>
      </nav-bar-2>
      <div class="main">
        <div class="top-bar-placeholder"></div>
        <div class="top-bar-container">
          <img
            class="top-bar-bg"
            src="@/assets/images/cards/coupon-center-bg1.png"
            alt
          />
          <!-- <div class="coupon-navs">
            <div
              class="coupon-nav"
              :class="{ active: current === index }"
              v-for="(item, index) in navList"
              :key="index"
              @click="tapNav(index, item.orderType)"
            >
              {{ item.name }}
            </div>
          </div> -->
          <div class="top-bar-bg-zw"></div>
          <div class="coupon-navs">
            <div
              class="coupon-nav"
              :class="{
                'active-left': current === index && item.orderType === 0,
                'default-left': current !== index && item.orderType === 0,
                'active-right': current === index && item.orderType === 1,
                'default-right': current !== index && item.orderType === 1,
              }"
              v-for="(item, index) in navList2"
              :key="index"
              @click="tapNav(index, item.orderType)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>

        <div class="coupon-container">
          <content-empty v-if="empty"></content-empty>
          <load-more
            v-model="loading"
            :finished="finished"
            @loadMore="loadMore"
            :check="false"
          >
            <div class="coupon-list" v-if="current == 0">
              <div
                class="coupon-item"
                v-for="(item, index) in couponList"
                :key="`${item.id}-${index}`"
              >
                <game-coupon-item
                  :key="item.id"
                  :couponItem="item"
                ></game-coupon-item>
              </div>
            </div>
            <div class="coupon-list" v-else>
              <div
                class="coupon-item"
                v-for="(item, index) in couponList"
                :key="`${item.id}-${index}`"
              >
                <coupon-item :key="item.id" :couponItem="item"></coupon-item>
              </div>
            </div>
          </load-more>
        </div>
      </div>
    </div>
  </rubber-band>
</template>

<script>
import CouponItem from '../components/coupon-item';
import GameCouponItem from '../components/game-coupon-item';
import {
  ApiCouponList,
  ApiCouponGameCoupons,
  ApiCouponGetSpecialOrderCouponList,
} from '@/api/views/coupon.js';
export default {
  name: 'CouponCenter',
  components: {
    CouponItem,
    GameCouponItem,
  },
  data() {
    return {
      pageTitle: this.$t('代金券'),
      current: 0,
      // navList: [
      //   {
      //     name: this.$t("热门"),
      //     orderType: 3,
      //   },
      //   {
      //     name: this.$t("游戏"),
      //     orderType: 0,
      //   },
      //   {
      //     name: this.$t("最高折扣"),
      //     orderType: 1,
      //   },
      //   {
      //     name: this.$t("最新上架"),
      //     orderType: 2,
      //   },
      // ],
      navList2: [
        {
          name: this.$t('游戏列表'),
          orderType: 0,
        },
        {
          name: this.$t('SVIP专属'),
          orderType: 1,
        },
      ],
      orderType: 0,
      couponList: [],
      loading: false,
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  async activated() {
    // 神策埋点
    this.$sensorsTrack('all_voucher_page_view');

    this.orderType = this.$route.params.index || this.orderType;
    this.current = this.$route.params.index || this.current;
    this.loading = true;
    await this.getCouponList();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async tapNav(index) {
      if (this.current != index && !this.loading) {
        this.finished = false;
        this.current = index;
        this.orderType = this.navList2[index].orderType;
        this.couponList = [];
        this.loading = true;
        await this.getCouponList();
      }
    },
    async getCouponList(action = 1) {
      if (this.orderType == 0) {
        // 获取游戏列表代金券
        this.getGameListVouchers(action);
      } else {
        // 获取VIP专属代金券
        this.getVIPExclusiveVouchers(action);
      }
    },
    async getGameListVouchers(action) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res;
      res = await ApiCouponGetSpecialOrderCouponList({
        is_list: 0,
        page: this.page,
        listRows: this.listRows,
        type: 0,
      });
      let { list } = res.data;
      if (this.page === 1) {
        this.couponList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      list = list.map(item => {
        return {
          ...item.game_info,
          ...item.coupon_info,
        };
      });
      this.couponList.push(...list);
      this.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async getVIPExclusiveVouchers(action) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res;
      res = await ApiCouponGetSpecialOrderCouponList({
        is_list: 1,
        page: this.page,
        listRows: this.listRows,
        type: 1,
      });
      let { coupon_list } = res.data;
      if (this.page === 1) {
        this.couponList = [];
        if (!coupon_list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.couponList.push(...coupon_list);
      this.loading = false;
      if (coupon_list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getCouponList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.coupon-center-page {
  background-color: #fff;
  min-height: 100vh;
  padding-bottom: 0;
  .my-coupon {
    color: white;
    font-size: 14 * @rem;
  }
  .main {
    .top-bar-placeholder {
      height: 286 * @rem;
      width: 100%;
      background-color: #f5f5f6;
    }
    .top-bar-container {
      height: 286 * @rem;
      width: 100%;
      position: fixed;
      left: 0;
      top: 0 * @rem;
      z-index: 2;
      .fixed-center;
      .top-bar-bg {
        width: 100%;
        min-height: 281 * @rem;
        height: auto;
        position: relative;
      }
      .top-bar-bg-zw {
        background: #fff2ec;
        height: 20 * @rem;
      }
      .coupon-navs {
        position: relative;
        left: 0;
        bottom: 25 * @rem;
        z-index: 2;
        height: 76 * @rem;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20 * @rem 20 * @rem 0 0;
        background-color: #ffffff;
        .coupon-nav {
          font-size: 14 * @rem;
          position: relative;
          font-weight: 400;
          color: #000000;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          &.active {
            font-weight: 600;
            &::after {
              content: '';
              display: block;
              width: 12 * @rem;
              height: 4 * @rem;
              border-radius: 2 * @rem;
              background-color: @themeColor;
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              bottom: 0 * @rem;
            }
          }
          &.active-left {
            background: url('../../../assets/images/cards/left-coupon-active.png')
              0 0;
            background-size: 162 * @rem 37 * @rem;
            width: 162 * @rem;
            height: 37 * @rem;
            color: #fff;
          }

          &.default-left {
            background: url('../../../assets/images/cards/left-coupon-default.png')
              0 0;
            background-size: 162 * @rem 37 * @rem;
            width: 162 * @rem;
            height: 37 * @rem;
            color: #ff6a4c;
          }

          &.active-right {
            background: url('../../../assets/images/cards/right-coupon-active.png') -1 *
              @rem 0;
            background-size: 162 * @rem 37 * @rem;
            width: 161 * @rem;
            height: 37 * @rem;
            color: #fff;
            margin-left: 10 * @rem;
          }
          &.default-right {
            background: url('../../../assets/images/cards/right-coupon-default.png') -1 *
              @rem 0;
            background-size: 162 * @rem 37 * @rem;
            width: 161 * @rem;
            height: 37 * @rem;
            color: #ff6a4c;
            margin-left: 10 * @rem;
          }
        }
      }
    }

    .coupon-container {
      background: #fff;
      margin-top: 76 * @rem;
      .coupon-list {
        border-radius: 20 * @rem 20 * @rem 0 0;
      }
    }
  }
}
</style>
