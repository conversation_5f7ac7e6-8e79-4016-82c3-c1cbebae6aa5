<template>
  <div class="error-page">
    <nav-bar-2 title="" :azShow="true"></nav-bar-2>
    <content-empty
      :emptyImg="emptyImg"
      tips="抱歉，你访问的游戏去异世界冒险了"
      :showRetry="true"
      @retry="retry"
      class="empty-content"
    ></content-empty>
  </div>
</template>

<script>
import emptyImg from '@/assets/images/error/game-not.png';
export default {
  name: 'NotFoundPage',
  data() {
    return { emptyImg };
  },
  methods: {
    retry() {
      this.$router.replace(this.$route.params);
    },
  },
};
</script>

<style lang="less" scoped>
.error-page {
  display: flex;
  // align-items: center;
  justify-content: center;
  height: 100vh;
}
.empty-content {
  display: block !important;
  margin-top: 25vh;
  /deep/ .van-empty {
    .van-empty__description {
      margin-top: 8 * @rem;
    }

    .retry {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 108 * @rem;
      height: 40 * @rem;
      line-height: 1;
      text-align: center;
      color: #fff;
      font-size: 15 * @rem;
      background: @themeBg;
      border-radius: 29 * @rem;
      text-decoration: none;
    }
  }
}
</style>
