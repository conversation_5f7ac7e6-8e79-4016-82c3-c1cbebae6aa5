import Vue from 'vue';
import confirmDialog from './index.vue';
import router from '@/router';
import store from '@/store';

const ConfirmDialog = Vue.extend(confirmDialog);

// const options = {
//   // 配置项
//   title: '',
//   content: '',
//   desc: '',
//   tips: '',
//   cancelText: '',
//   confirmText: '',
//   cancelBgColor: '',
//   cancelTextColor: '',
//   confirmBgColor: '',
//   confirmTextColor: '',
//   showCancel: false,
//   showConfirm: true,
//   onCancel: () => {},
//   onConfirm: () => {},
// }

function useConfirmDialog(options) {
  return new Promise((resolve, reject) => {
    const dialog = new ConfirmDialog({
      router,
      store,
    });

    dialog.$mount(document.createElement('div'));
    document.body.appendChild(dialog.$el);

    dialog.$el.addEventListener(
      'animationend',
      () => {
        if (dialog.show == false) {
          dialog.$destroy();
          dialog.$el.parentNode.removeChild(dialog.$el);
        }
      },
      false,
    );

    dialog.show = true;

    dialog.title = options.title || '提示';
    dialog.content = options.content || '';
    dialog.desc = options.desc || '';
    dialog.cardpass = options.cardpass || '';
    dialog.tips = options.tips || '';
    dialog.cancelText = options.cancelText || '取消';
    dialog.cancelTextColor = options.cancelTextColor || '';
    dialog.cancelBgColor = options.cancelBgColor || '';
    dialog.confirmText = options.confirmText || '确定';
    dialog.confirmTextColor = options.confirmTextColor || '';
    dialog.confirmBgColor = options.confirmBgColor || '';
    dialog.showCancel = options.showCancel || false;
    dialog.showConfirm = options.showConfirm || true;
    if (options.onCancel) {
      dialog.onCancel = () => {
        options.onCancel();
        dialog.show = false;
      };
    }
    if (options.onConfirm) {
      dialog.onConfirm = () => {
        options.onConfirm();
        dialog.show = false;
      };
    }
  });
}

export default useConfirmDialog;
