<template>
  <div class="info-box" @click="clickGame(itemInfo)">
    <div class="icon">
      <img :src="itemInfo.titlepic" alt="" />
    </div>
    <div class="title">{{ itemInfo.title }}</div>
    <div class="price">
      <div>{{ itemInfo.subtitle }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: 'game-item-info',
    itemInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: {
    clickGame(item) {
      this.$emit('send-data', item);
      this.$emit('isShowClose', false);
    },
  },
};
</script>

<style lang="less" scoped>
.info-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  // width: 64 * @rem;
  margin: 0 auto;
  .icon {
    width: 64 * @rem;
    height: 64 * @rem;
  }
  .title {
    // width: 49 * @rem;
    // height: 12 * @rem;
    font-weight: bold;
    font-size: 12 * @rem;
    color: #333333;

    box-sizing: border-box;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-top: 7 * @rem;
    line-height: 15 * @rem;
    // padding: 5 * @rem;
  }
  .price {
    background: url(~@/assets/images/recharge/gold-coin-price.png) no-repeat 0 0;
    box-sizing: border-box;
    width: 60 * @rem;
    height: 17 * @rem;
    line-height: 17 * @rem;
    padding: 0 4 * @rem;
    overflow: hidden;
    background-size: 60 * @rem 17 * @rem;
    margin: 5 * @rem auto 0;
    text-align: center;
    font-size: 9 * @rem;
    color: #89888d;
    div {
      // white-space: nowrap;
      &.text-scroll {
        flex-shrink: 0;
        flex-grow: 1;
        white-space: nowrap;
        animation: scroll-left 4s linear forwards infinite;
      }
      @keyframes scroll-left {
        50% {
          transform: translateX(0%);
        }
        99.99% {
          transform: translateX(-120%);
        }
        100% {
          transform: translateX(0%);
        }
      }
    }
  }
}
</style>
