<template>
  <div class="box-beta-page">
    <nav-bar-2
      :border="false"
      :title="title"
      :bgStyle="bgStyle"
      :azShow="true"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bg"></div>
      <div class="container">
        <div class="describe-item">
          <div class="box-beta-title1"></div>
          <div class="content">
            <div class="info"
              >先锋测试是游戏盒官方产品最新研发功能的测试项目，我们希望有更多用户体验产品新的功能，让我们发现内测版本出现的bug或体验问题，保障与优化游戏盒产品质量。</div
            >
            <div class="info-before">
              <span></span>
            </div>
          </div>
        </div>
        <div class="describe-item"
          ><div class="box-beta-title2"></div>
          <div class="content">
            <div class="info"
              >体验内测版本新功能，测试功能是否可用、流程是否顺畅，帮助我们发现bug，优化产品体验。发现异常问题后在【我的-投诉建议】处反馈给我们，也可加入我们的内测讨论QQ群：
              <span class="selectable">{{ qq_qun }}</span
              >。
              <br />
              * 我们可视情况对新功能新玩法完善、调整或终止</div
            >
            <div class="info-before">
              <span></span>
            </div>
          </div>
        </div>
        <div class="describe-item"
          ><div class="box-beta-title3"></div>
          <div class="content">
            <div class="info"
              >1.抢先体验新功能
              <br />
              2.使用最新内测版本反馈有效bug，<span style="color: #fe6600"
                >可获得金币奖励，100金币/条</span
              >，同一问题重复反馈仅计算一次有效反馈</div
            >
            <div class="info-before">
              <span></span>
            </div>
          </div>
        </div>
      </div>
      <div class="dow-btn">
        <div
          v-if="status == 0 || status == 1"
          class="btn btn-style gray"
          @click="clickCurrenBtn()"
          >{{ button_text }}</div
        >
        <div
          v-else-if="status == 2"
          class="btn btn-style"
          @click="downFormalShow = true"
          >{{ button_text }}</div
        >
        <kt-download-btn
          v-else-if="status == 3"
          :progress="downloadPercent"
          @startDownload="onStartDownload"
          @startCallback="onStartCallback"
          @successCallback="onSuccessCallback"
          @failedCallback="onFailedCallback"
          successText="下载完成"
          :initText="button_text"
          :editionText="!isBtnOver ? betaVersion : ''"
          failedText="下载失败"
          ref="downloadBtn"
        ></kt-download-btn>
      </div>
    </div>
    <!-- 下载正式版 -->
    <van-dialog
      v-model="downFormalShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="formal-dialog"
    >
      <div class="dialog-top"> </div>
      <div class="dialog-close" @click="downFormalShow = false"></div>
      <div class="dialog-content">
        <div class="dialog-msg"
          >安装正式版，需卸载当前已安装<br />版本，重新安装</div
        >
        <div class="dialog-btn" @click="downloadFormal()">下载正式版</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import KtDownloadBtn from './components/kt-download-btn.vue';
import {
  platform,
  authInfo,
  Box_downloadApk,
  BOX_openInBrowser,
  BOX_getPackageName,
} from '@/utils/box.uni.js';
import { ApiUpdateGetTestPlan } from '@/api/views/users.js';
import { mapActions } from 'vuex';
export default {
  name: 'BoxBeta',
  components: { KtDownloadBtn },
  data() {
    let self = this;
    return {
      bgStyle: 'transparent',
      navbarOpacity: 0,
      title: '',
      downFormalShow: false,
      downloadPercent: 0,
      // versionCode: 4213, //当前版本号
      betaVersion: '', //测试版本号
      // isBeta: false, //当前是否内测版
      // isOpenBeta: false, //是否打开内测
      isBtnOver: false, //是否下载完成
      button_text: '', //按钮名称
      down_url: '', //安装包地址 正式、内测同一个
      qq_qun: '', //QQ群
      status: 0, //0关闭内测 1最新版 2重新下载正式版 3下载内测版
      JSBridge: {
        callNativeMethod: function () {
          if (platform == 'android' && authInfo.versionCode > 4209) {
            Box_downloadApk(self.down_url);
          } else {
            self.$refs.downloadBtn.init();
            BOX_openInBrowser(
              {
                h5_url: self.down_url,
              },
              {
                url: self.down_url,
              },
            );
          }
        },
        // 接收安卓传递的数据，并处理
        receiveBoxDownloadProgress: function (data) {
          self.downloadPercent = data;
          if (self.downloadPercent >= 100) {
            self.isBtnOver = true;
            self.$refs.downloadBtn.success();
          }
        },
      },
    };
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
    this.getTestPlanInfo();
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
      window.JSBridge = this.JSBridge;
    }
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      this.SET_USER_INFO(false);
      this.getTestPlanInfo();
    },
    // 获取内测信息
    async getTestPlanInfo() {
      const res = await ApiUpdateGetTestPlan({
        packageName: BOX_getPackageName(),
        versionCode: authInfo.versionCode,
        channel: authInfo.channel,
        isBeta: authInfo.isBeta,
      });
      const { button_text, betaVersion, down_url, qq_qun, status } =
        res.data.info;
      this.button_text = button_text;
      this.betaVersion = betaVersion;
      this.down_url = down_url;
      this.qq_qun = qq_qun;
      this.status = status;
    },
    handleMessage(e) {},
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        // this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.navbarOpacity = 1;
        this.title = '先锋测试计划';
      } else {
        this.navbarOpacity = 0;
        this.title = '';
      }
    },
    // 开始下载
    onStartDownload(e) {
      this.JSBridge.callNativeMethod();
    },
    // 下载中
    onStartCallback(e) {
      this.JSBridge.callNativeMethod();
    },
    // 成功回调
    onSuccessCallback(e) {
      this.JSBridge.callNativeMethod();
    },
    // 失败回调
    onFailedCallback(e) {},
    downloadFormal() {
      this.downFormalShow = false;
      BOX_openInBrowser(
        {
          h5_url: this.down_url,
        },
        {
          url: this.down_url,
        },
      );
    },
    clickCurrenBtn() {
      this.$toast({
        message: '当前版本为最新版本',
        duration: 1500,
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  // computed: {
  //   currentVersion() {
  //     // 1. 如果没有开启内测，直接返回最新版标志
  //     if (!this.isOpenBeta) {
  //       return 1; // 最新版
  //     }

  //     // 2. 开启了内测，进入详细判断
  //     if (this.isBeta) {
  //       // 2.1 当前是内测版的情况下
  //       if (this.versionCode >= this.betaVersion) {
  //         return 3; // 退回正式版
  //       } else {
  //         return 2; // 提示更新内测版+
  //       }
  //     } else {
  //       // 2.2 当前不是内测版的情况下
  //       if (this.versionCode < this.betaVersion) {
  //         return 2; // 提示安装内测版
  //       } else {
  //         return 1; // 当前已经是最新版
  //       }
  //     }
  //   },
  // },
};
</script>
<style lang="less" scoped>
.box-beta-page {
  background-color: #fff;
  .main {
    padding-bottom: 21 * @rem;
    .top-bg {
      width: 100%;
      height: 180 * @rem;
      background: url(~@/assets/images/users/box_beta_bg.png) no-repeat 0 0;
      background-size: 375 * @rem 281 * @rem;
      height: 281 * @rem;
    }
    .container {
      display: flex;
      flex-direction: column;
      padding: 0 25 * @rem;
      margin-top: -37 * @rem;
      .describe-item {
        display: flex;
        flex-direction: column;
        position: relative;
        &:not(:last-child) {
          margin-bottom: 26 * @rem;
        }
        .box-beta-title1 {
          background: url(~@/assets/images/users/box-beta-title1.png) no-repeat -1 *
            @rem 0;
          background-size: 124 * @rem 15 * @rem;
          width: 123 * @rem;
          height: 15 * @rem;
        }
        .box-beta-title2 {
          background: url(~@/assets/images/users/box-beta-title2.png) no-repeat
            0 0;
          background-size: 123 * @rem 15 * @rem;
          width: 123 * @rem;
          height: 15 * @rem;
        }
        .box-beta-title3 {
          background: url(~@/assets/images/users/box-beta-title3.png) no-repeat
            0 0;
          background-size: 74 * @rem 15 * @rem;
          width: 74 * @rem;
          height: 15 * @rem;
        }
        .content {
          position: relative;
          .info {
            margin: 12 * @rem 0 0 9 * @rem;
            width: 316 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 14 * @rem;
            color: #6c6c6c;
            line-height: 22 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            position: relative;
            z-index: 10;
            .selectable {
              user-select: text;
            }
          }
          .info-before {
            position: absolute;
            top: 2 * @rem;
            left: 0;
            z-index: 1;
            > span {
              display: inline-block;
              background: url(~@/assets/images/users/box_beta_icon.png)
                no-repeat 0 0;
              background-size: 33 * @rem 22 * @rem;
              width: 33 * @rem;
              height: 22 * @rem;
            }
          }
        }
      }
    }
    .dow-btn {
      margin-top: 57 * @rem;
      .btn {
        width: 335 * @rem;
        margin: 0 auto;
        height: 50 * @rem;
        line-height: 50 * @rem;
        background: #21ce9a;
        border-radius: 29 * @rem;
        margin-bottom: 21 * @rem;
        text-align: center;
        color: #ffffff;
        &.gray {
          color: #979797;
          background: #eaeaea;
        }
      }
      .btn-style {
        font-family:
          PingFang HK,
          PingFang HK;
        font-weight: 500;
        font-size: 16 * @rem;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .formal-dialog {
    width: 260 * @rem;
    height: 246 * @rem;
    background: #ffffff;
    border-radius: 12 * @rem;
    .dialog-top {
      box-sizing: border-box;
      position: relative;
      background: url('~@/assets/images/users/down-logo.png') no-repeat 0 0;
      background-size: 260 * @rem 82 * @rem;
      width: 260 * @rem;
      height: 82 * @rem;
    }
    .dialog-close {
      position: absolute;
      top: 0;
      right: 0;
      background: url('~@/assets/images/users/down-close.png') no-repeat 0 0;
      background-size: 24 * @rem 24 * @rem;
      width: 24 * @rem;
      height: 24 * @rem;
    }
    .dialog-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 30 * @rem;
      .dialog-msg {
        height: 36 * @rem;
        font-weight: 600;
        font-size: 14 * @rem;
        color: #666666;
        line-height: 18 * @rem;
        text-align: center;
      }
      .dialog-btn {
        margin-top: 30 * @rem;
        width: 168 * @rem;
        height: 32 * @rem;
        line-height: 32 * @rem;
        background: #3cd279;
        border-radius: 35 * @rem;
        font-weight: 500;
        font-size: 14 * @rem;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}
</style>
