<template>
  <div class="news-fanli-page">
    <content-empty v-if="empty" :tips="$t('暂无相关返利活动')"></content-empty>
    <load-more
      v-else
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <template v-for="(news, newsIndex) in newsList">
        <div class="news-type" :key="newsIndex" v-if="news.data.length">
          <div class="type-title">
            <div class="type-icon"></div>
            <div class="type-title-text">{{ formatType(news.type) }}</div>
          </div>
          <div class="news-list">
            <div class="news-item" v-for="item in news.data" :key="item.id">
              <div class="left" @click="toNewsDetail(item)">
                <div class="news-title">{{ item.title }}</div>
                <!-- <div class="news-desc" v-if="item.prizes_info">
                  {{ $t("可获得") }}：{{ item.prizes_info }}
                </div> -->
                <div class="time-throught" v-if="item.time_type == 1">
                  {{ item.time_text }}
                </div>
                <div class="time-throught" v-else>
                  {{ item.activity_starttime | formatTime }} -
                  {{ item.activity_endtime | formatTime }}
                </div>
              </div>
              <div class="right" v-if="item.apply_type.type == 1">
                {{ item.apply_type.type_str }}
              </div>
              <div
                class="right kefu"
                @click="clickNewsBtn(item)"
                v-else-if="item.apply_type.type == 3"
              >
                {{ item.apply_type.type_str }}
              </div>
              <div
                class="right kefu"
                @click="clickNewsBtn(item)"
                v-else-if="item.apply_type.type == 4"
              >
                {{ item.apply_type.type_str }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </load-more>
    <div
      class="history-btn"
      @click="toPage('NewsFanliHistory', { game_id: id })"
    >
      <div class="history-text">{{ $t('查看往期活动') }}</div>
      <div class="right-icon"></div>
    </div>
  </div>
</template>

<script>
import { ApiNewsGameNewsList } from '@/api/views/news.js';
import { ApiRebateObtainAward } from '@/api/views/rebate.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  data() {
    return {
      page: 1,
      listRows: 20,
      newsList: [
        {
          type: 1, // 长期
          data: [],
        },
        {
          type: 2, // 限时
          data: [],
        },
        {
          type: 3, // 货币
          data: [],
        },
      ],
      loading: false,
      finished: false,
      empty: false,
    };
  },
  filters: {
    formatTime(val) {
      let { year, month, day } = handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
  },
  async created() {
    this.id = this.$route.params.game_id;
    await this.getNewsList();
  },
  methods: {
    formatType(type) {
      let typeStr = '';
      switch (type) {
        case 1:
          typeStr = this.$t('长期活动');
          break;
        case 2:
          typeStr = this.$t('限时活动');
          break;
        case 3:
          typeStr = this.$t('货币返利');
          break;
        default:
          typeStr = this.$t('长期活动');
          break;
      }
      return typeStr;
    },
    async getNewsList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiNewsGameNewsList({
        gameId: this.id,
        type: 1,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.newsList = [
          {
            type: 1,
            data: [],
          },
          {
            type: 2,
            data: [],
          },
          {
            type: 3,
            data: [],
          },
        ];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      list.forEach(item => {
        let _index = this.newsList.findIndex(_news => {
          return item.time_type == _news.type;
        });
        this.newsList[_index].data.push(item);
      });
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getNewsList(2);
    },
    toNewsDetail(item) {
      this.$router.push({
        name: 'FanliDetail',
        params: {
          url: item.titleurl,
          title: item.title,
          showKefu: item.apply_type.type == 3 ? 1 : 0,
          gameId: item.game_id,
          id: item.id,
          info: item,
        },
      });
    },
    async clickNewsBtn(info) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        setTimeout(() => {
          this.toPage('PhoneLogin');
        }, 500);
        return;
      }
      if (info.apply_type.type == 4) {
        // 申请返利
        this.toPage('FanliApply', {
          id: info.id,
          game_id: info.game_id,
          info: info,
        });
      } else if (info.apply_type.type == 3) {
        // 联系客服
        this.openKefu({
          from: 'fanli',
          gameName: info.titlegame,
          activityName: info.title,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.news-fanli-page {
  /deep/ .van-list__finished-text {
    display: none;
  }
  .news-type {
    width: 347 * @rem;
    margin: 20 * @rem auto 0;
    background: #ffffff;
    box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
    border-radius: 12 * @rem;
    overflow: hidden;
    .type-title {
      display: flex;
      align-items: center;
      height: 38 * @rem;
      padding: 0 12 * @rem 0 19 * @rem;
      background-color: #f5f5f6;
      .type-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        .image-bg('~@/assets/images/games/sun-icon.png');
      }
      .type-title-text {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: bold;
        margin-left: 6 * @rem;
      }
    }
    .news-list {
      padding: 0 12 * @rem 0 19 * @rem;
      .news-item {
        display: flex;
        align-items: center;
        padding: 15 * @rem 0;
        &:not(:first-of-type) {
          border-top: 0.5 * @rem solid #ebebeb;
        }
        .left {
          flex: 1;
          min-width: 0;
          .news-title {
            font-size: 15 * @rem;
            color: #000000;
            line-height: 22 * @rem;
            max-height: 44 * @rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .news-desc {
            font-size: 13 * @rem;
            color: #909090;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-top: 5 * @rem;
          }
          .time-throught {
            font-size: 13 * @rem;
            color: #909090;
            line-height: 18 * @rem;
            margin-top: 5 * @rem;
          }
        }
        .right {
          width: 70 * @rem;
          height: 30 * @rem;
          background-color: #ebebeb;
          border: 1 * @rem solid #ebebeb;
          border-radius: 15 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 13 * @rem;
          color: #797979;
          margin-left: 10 * @rem;
          &.kefu {
            border: 1 * @rem solid @themeColor;
            background-color: #fff;
            color: @themeColor;
          }
        }
      }
    }
  }
  .history-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40 * @rem;
    font-size: 14 * @rem;
    color: @themeColor;
    margin-bottom: 40 * @rem;
    .right-icon {
      width: 13 * @rem;
      height: 12 * @rem;
      .image-bg('~@/assets/images/games/right-arrow.png');
      background-size: 10 * @rem 10 * @rem;
      background-position: center center;
      margin-left: 4 * @rem;
    }
  }
}
</style>
