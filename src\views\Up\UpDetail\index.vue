<template>
  <div class="up-detail">
    <div class="bg-color">
      <img src="~@/assets/images/up/up-detail-bg.png" alt="" />
    </div>
    <nav-bar-2
      bgStyle="transparent-white"
      :title="'资源详情'"
      :placeholder="false"
      v-if="nav_bg_transparent"
      class="nav-bar"
    >
      <template #right>
        <div @click="please_update_popup = true" class="text btn">求更新</div>
      </template>
    </nav-bar-2>
    <nav-bar-2 :title="'资源详情'" :placeholder="false" :border="true" v-else>
      <template #right>
        <div @click="please_update_popup = true" class="text1 btn">求更新</div>
      </template>
    </nav-bar-2>
    <header>
      <status-bar></status-bar>
      <div class="wrapper">
        <div class="bg"></div>
        <div class="game-info">
          <img :src="detail.titlepic" class="game-img" />
          <div class="container">
            <div class="top">{{ detail.title }}</div>
            <div class="center">
              <span v-if="detail.version">版本：{{ detail.version }}</span
              >&nbsp;&nbsp;
              <span>{{ detail.size_a }}</span>
            </div>
            <div class="bottom" v-if="up_info.up"
              >由
              <div
                @click="toPage('UpMine', { mem_id: up_info.up.user_id })"
                class="avatar"
              >
                <UserAvatar :src="up_info.up.avatar" :self="false" />
              </div>
              分享
            </div>
          </div>
        </div>
        <div class="game-info-tag" v-if="extra_tag_length">
          <span v-for="(item, index) in detail.extra_tag" :key="index">{{
            item.name
          }}</span>
        </div>
      </div>
    </header>
    <main>
      <div class="tab-container" ref="tabContainer">
        <van-sticky :offset-top="sticky_offset_top">
          <div class="tabs">
            <div
              class="tab btn"
              v-for="(tab, index) in tab_list"
              :key="index"
              :class="{ active: current === index }"
              @click="clickTab(index)"
            >
              <span>{{ tab.name }} </span>
            </div>
            <!-- <div class="line"></div> -->
            <div
              class="tab-line"
              :style="{ left: `${current * 125 * remNumberLess}rem` }"
            ></div>
            <!-- <div
              class="tab-line"
              :style="{ left: handleLeftDistance(current) }"
            ></div> -->
          </div>
        </van-sticky>
        <!-- 详情tab -->
        <div class="tab-content tab-detail" v-show="current === 0">
          <van-loading v-if="!load_success" />
          <template v-else>
            <!-- 温馨提示 -->
            <section class="warm-reminder-box" v-if="detail.is_content_show">
              <div class="game-warm-reminder">
                <span class="property_text" v-if="detail.property">{{
                  detail.property
                }}</span>
                <p class="up_title">UP主：({{ up_info.up.nickname }})</p>
                <span class="btn_title" v-if="detail.video_set">
                  <span @click="downloadVideoTutorial(detail.video_set_url)">{{
                    detail.video_set_title
                  }}</span>
                </span>
                <div
                  class="prompt-box"
                  v-if="detail.google_set && detail.spread_set"
                >
                  <div class="prompt-left">
                    <div class="prompt-title">
                      <i></i>
                      <span>温馨提示</span>
                    </div>
                    <span
                      class="prompt-google"
                      v-if="detail.google_set"
                      @click="googleInstaller()"
                      >谷歌安装器</span
                    >
                  </div>
                  <div
                    class="prompt-right"
                    v-if="detail.spread_set"
                    @click="acceleratorDow()"
                  >
                    <span>加速器下载</span>
                    <i></i>
                  </div>
                </div>
              </div>
              <div
                v-if="detail.characteristic"
                class="introduction-text"
                ref="content1"
                :class="{ on: !isAll1 }"
                v-html="detail.characteristic"
              ></div>
              <div
                class="more-text"
                @click="isAll1 = !isAll1"
                v-if="contentHeight1 > 100"
              >
                <span>{{ !isAll1 ? $t('展开') : $t('收起') }}</span>
                <div class="more-text-icon" :class="{ on: isAll1 }"></div>
              </div>
            </section>
            <!-- 截图 -->
            <!-- <section
              class="game-picture-swiper"
              v-if="detail.morepic && detail.morepic.small.length"
            >
              <swiper :options="swiperOption">
                <swiper-slide
                  class="swiper-slide video-container"
                  v-if="detail.video_url"
                >
                  <video
                    ref="videoPlayer"
                    id="video"
                    :src="detail.video_url"
                    :poster="detail.video_thumb"
                    :controls="isPlaying"
                    x5-playsinline=""
                    :playsinline="true"
                    :webkit-playsinline="true"
                    :x-webkit-airplay="true"
                    x5-video-player-type="h5"
                    :x5-video-player-fullscreen="true"
                    x5-video-orientation="portraint"
                    muted
                  ></video>
                  <div class="mask" v-show="!isPlaying">
                    <div class="play-btn" @click="handlePlay"></div>
                  </div>
                </swiper-slide>
                <swiper-slide
                  v-for="(item, index) in detail.morepic
                    ? detail.morepic.small
                    : []"
                  :key="index"
                  class="swiper-slide"
                >
                  <img
                    :src="item"
                    class="slide-img btn"
                    @click="showBigImage(detail.morepic.big, index)"
                  />
                  <i class="player" v-if="index === 1"></i>
                </swiper-slide>
              </swiper>
            </section> -->
            <div class="h8_bg" v-if="detail.is_content_show"></div>
            <!-- up主语录 -->
            <section class="up_main_quotation">
              <div class="title">
                <span class="title-text">
                  <div class="text">UP主语录</div>
                </span>
              </div>
              <div
                v-html="detail.features"
                class="introduction-text mb10"
              ></div>
              <div
                class="introduction-text"
                ref="content2"
                :class="{ on: !isAll2 }"
                v-html="detail.newstext"
              ></div>
              <div
                class="more-text"
                @click="isAll2 = !isAll2"
                v-if="contentHeight2 > 100"
              >
                <span>{{ !isAll2 ? $t('展开') : $t('收起') }}</span>
                <div class="more-text-icon" :class="{ on: isAll2 }"></div>
              </div>
            </section>

            <!-- 通知 -->
            <div v-html="up_info.up.desc" class="notice"></div>
            <!-- 觉得游戏怎么样 -->
            <!-- <section class="rate">
              <div class="bg3"></div>
              <div class="button-container">
                <div
                  :class="{ empty: evaluate != 0 }"
                  @click="gameRate(1)"
                  class="button left"
                >
                  非常棒
                </div>
                <div
                  :class="{ empty: evaluate != 0 }"
                  @click="gameRate(2)"
                  class="button right"
                >
                  臭鸡蛋
                </div>
              </div>
            </section> -->
            <!-- 截图2 -->
            <template>
              <section class="screenshot-list">
                <div
                  class="screenshot-list-item"
                  v-for="(item, index) in detail.morepic
                    ? detail.morepic.small
                    : []"
                  :key="index"
                  :class="{
                    'full-width': index === 2,
                    'half-width':
                      index === 0 || index === 1 || index === 3 || index === 4,
                  }"
                >
                  <img
                    :src="item"
                    @click="showBigImage(detail.morepic.big, index)"
                  />
                </div>
              </section>
            </template>
            <div class="h8_bg"></div>
            <!-- 广告 -->
            <div class="banner-id"></div>
            <!-- UP主其他分享 -->
            <section class="up-share" v-if="up_owner_game.length">
              <div class="title">
                <span class="title-text">
                  <div class="text">UP主其他分享</div>
                </span>
                <i class="title-icon" @click="clickTab(2)"></i>
              </div>
              <div class="deal-list">
                <template v-for="item in up_owner_game">
                  <div :key="item.id" class="deal-item">
                    <img
                      class="game-pic"
                      @click="toDetail(item.id, item)"
                      :src="item.titlepic"
                      alt=""
                    />
                    <div class="game-name">{{ item.title }}</div>
                    <div
                      :class="{ loading: downloadLoading1[item.id] }"
                      class="game-btn"
                      @click="downloadBtn(item.down_a, item.id)"
                      >下载</div
                    >
                  </div>
                </template>
              </div>
            </section>

            <!-- 相关游戏 -->
            <!-- <section class="related">
              <div class="title">
                <span class="title-text">
                  <div class="text">相关游戏</div>
                </span>
              </div>
              <div class="game-list">
                <div
                  class="game-group"
                  v-for="(related, relatedIndex) in formatRelatedList()"
                  :key="relatedIndex"
                >
                  <div
                    class="game-item"
                    @click="toPage('GameDetail', { id: item.id })"
                    v-for="(item, index) in related"
                    :key="index"
                  >
                    <game-item-2 :gameInfo="item" :type="5"></game-item-2>
                  </div>
                </div>
              </div>
            </section> -->
            <!-- <div class="h8_bg"></div> -->
            <!-- 权限信息 隐私政策 -->
            <section class="up-game-dsc">
              <div class="dsc-content" @click="handlePermission">
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/qxsm-img.png" alt="" />
                  </i>
                  <span>权限说明</span>
                </div>
                <div class="right-dsc">
                  <i class="title-icon"></i>
                </div>
              </div>
              <div
                class="dsc-content"
                @click="
                  toPage('Iframe', {
                    url: $h5Page.yinsizhengce,
                    title: '隐私政策',
                  })
                "
              >
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/yszc-img.png" alt="" />
                  </i>
                  <span>隐私政策</span>
                </div>
                <div class="right-dsc">
                  <i class="title-icon"></i>
                </div>
              </div>
              <div
                class="dsc-content"
                v-if="is_record_info_show"
                @click="filingInfoShow = true"
              >
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/baxx-img.png" alt="" />
                  </i>
                  <span>备案信息</span>
                </div>
                <div class="right-dsc">
                  <i class="title-icon"></i>
                </div>
              </div>
              <div
                class="dsc-content"
                @click="toPage('UpMine', { mem_id: up_info.up.user_id })"
              >
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/yyly-img.png" alt="" />
                  </i>
                  <span>应用来源</span>
                </div>
                <div class="right-dsc">
                  <span>
                    <div class="avatar">
                      <UserAvatar :src="up_info.up.avatar" :self="false" />
                    </div>
                    {{ up_info.up.nickname }}
                  </span>
                  <i class="title-icon"></i>
                </div>
              </div>
              <div class="dsc-content">
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/lwapp-img.png" alt="" />
                  </i>
                  <span>是否联网APP</span>
                </div>
                <div class="right-dsc is_right_text">{{
                  detail.is_networking ? '是' : '否'
                }}</div>
              </div>
              <div class="dsc-content" v-if="detail.age_appropriate">
                <div class="left-dsc">
                  <i>
                    <img src="~@/assets/images/up/slfw-img.png" alt="" />
                  </i>
                  <span>适龄范围</span>
                </div>
                <div class="right-dsc is_right_text">{{
                  detail.age_appropriate
                }}</div>
              </div>
            </section>
          </template>
        </div>
        <!-- 评论tab -->
        <div class="tab-content tab-comment" v-show="current === 1">
          <comment-tab></comment-tab>
        </div>
        <!-- 资源tab -->
        <div class="tab-content tab-resources" v-show="current === 2">
          <p class="empty-msg" v-if="showEmptyMsg">该UP主还分享了其他</p>
          <resourceList-tab
            :showEmptyMsg.sync="showEmptyMsg"
          ></resourceList-tab>
        </div>
      </div>
    </main>
    <!-- 底部fixed -->
    <div class="bottom-container" v-if="load_success">
      <div class="bottom-fixed">
        <div
          class="comment btn"
          @click="
            toPage('CommentEditor', { source_id: detail.id, class_id: 103 })
          "
        >
          <div class="comment-icon"></div>
          <div class="text">评价</div>
        </div>
        <div
          class="collect-btn btn"
          :class="{ had: is_collection }"
          @click="setCollectStatus"
        >
          <div class="collect-icon"></div>
          <div class="text">{{ is_collection ? '已收藏' : '收藏' }}</div>
        </div>
        <div class="download-bar">
          <div class="download-content">
            <!--              v-if="detail.down_a" -->
            <div
              v-if="isIos"
              :class="{ loading: downloadLoading }"
              @click="goToCloudDevices"
              class="btn download-btn"
            >
              云手机
            </div>
            <div
              v-else
              :class="{ loading: downloadLoading }"
              @click="downloadGame"
              class="btn download-btn"
            >
              下载（{{ detail.size_a }}）
            </div>
            <!-- <div class="download-no">暂不支持下载</div> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 求更新弹窗 -->
    <van-dialog
      v-model="please_update_popup"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="please-update-popup"
    >
      <div class="title">想对UP主说的话</div>

      <textarea
        class="please-update-input"
        v-model="please_update_input"
        placeholder="请输入您想对UP主说的内容"
      ></textarea>
      <div class="button-container">
        <div @click="please_update_popup = false" class="left">取消</div>
        <div @click="submitPleaseUpdate" class="right">确定</div>
      </div>
    </van-dialog>
    <!-- 续费/购买 设备弹窗 -->
    <van-dialog
      v-model="devicePayPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :overlay-style="{ 'z-index': '2996' }"
      class="onhook-popup"
    >
      <div class="title">{{ $t('温馨提示') }}</div>

      <div class="content"> 您当前无可使用的云手机，是否前往购买设置。 </div>
      <div class="btn-info">
        <div class="cancel" @click.stop="devicePayCancel()">
          {{ $t('取消') }}
        </div>
        <div class="confirm" @click.stop="devicePayConfirm()">
          {{ $t('前往购买') }}
        </div>
      </div>
    </van-dialog>
    <!-- 设备选择弹窗 -->
    <van-dialog
      v-model="deviceDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="device-dialog"
      :closeOnClickOverlay="true"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('温馨提示') }}</div>
        <div class="title-text">
          当前无闲置的云手机，可选择以下设备替换游戏，或者购买设备哦~
        </div>
        <div class="center">
          <div class="left">{{ $t('设备') }}</div>
          <div class="right">
            <div
              class="text"
              v-if="deviceListShowItem.title"
              @click="deviceListShow = !deviceListShow"
            >
              <span>{{ deviceListShowItem.title }}</span>
              <span
                class="more-text-icon"
                :class="{ on: deviceListShow }"
              ></span>
            </div>
            <div class="device-list" :class="{ on: deviceListShow }">
              <div
                class="device-item"
                v-for="(item, index) in onHookEquipmentList"
                :key="index"
                @click="deviceListClick(item)"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
        </div>
        <div class="hangingUpName">
          <span>挂机游戏：</span>
          <span class="game-subtitle">
            <div
              :class="{
                'text-scroll': gameTitle.length > 8,
              }"
            >
              <span>{{ gameTitle }}</span>
            </div>
          </span>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="replacementGame()"> 替换游戏 </div>
          <div class="confirm btn" @click="purchaseEquipment()">购买设备</div>
        </div>
      </div>
    </van-dialog>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 备案信息弹窗 -->
    <van-popup
      v-model="filingInfoShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('游戏备案信息详情') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in filingInfoList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import {
  ApiGameRead,
  ApiGameCollectionUpGame,
  ApiGameSubmitCrackWish,
  ApiUpGameEvaluate,
  ApiGameGetUpOwnerGame,
  ApiGameGetPermissionInfo,
} from '@/api/views/game.js';
import { ApiUserFollowUser } from '@/api/views/users.js';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { ImagePreview } from 'vant';
import { BOX_showActivity } from '@/utils/box.uni.js';
import { isAndroidBox } from '@/utils/userAgent';
import CommentTab from '@/views/Games/GameDetail2/components/comment-tab';
import { ApiIndexGetAd } from '@/api/views/system.js';
import { isIos } from '@/utils/userAgent';
import { ApiCloudList, ApiCloudEquipPercent } from '@/api/views/upCloud.js';
import ResourceListTab from './components/resourceList-tab/index.vue';
export default {
  name: 'UpDetail',
  data() {
    return {
      nav_bg_transparent: true, //导航背景是否透明
      detail: {}, //游戏详情相关信息
      up_info: {}, //up相关信息
      sticky_offset_top: 0, //顶部状态栏高度
      tab_list: [
        {
          name: '简介',
        },
        {
          name: '评价',
        },
        {
          name: '资源',
        },
      ], //tab按钮
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
      },
      current: 0, //当前高亮tab
      remNumberLess, //rem大小转换
      load_success: false, //加载是否完毕
      about_game_list: [], //相关游戏列表
      liked: [], //喜欢
      finished: true, //ajax防抖
      is_collection: false, //是否已收藏
      downloadLoading: false, //下载loading
      please_update_popup: false, //求更新弹窗
      please_update_input: '', //求更新input
      banner_ad_id: 0, //百度广告id
      interstitial_ad_id: 0, //百度广告id
      evaluate: 0, //玩家评价，0没评价1鲜花2臭鸡蛋
      isIos: isIos,
      cloudDeviceList: [], // 云设备列表
      available_devices: 0, // 1 有设备 0 没有设备
      idleEquipmentList: [], // 闲置设备
      onHookEquipmentList: [], // 挂机设备
      obsoleteEquipmentList: [], // 过期设备
      devicePayPopupShow: false, // 购买设备跳转弹窗
      deviceDialogShow: false, // 显示设备选择弹窗
      deviceListShow: false, // 显示设备列表
      deviceListShowItem: {}, // 选中的设备
      count: 0, // 总量
      remain: 0, // 剩余总量
      percent: 0, // 设备百分比限制
      isAll1: true, // 展开 -> 是否显示全部
      isAll2: true, // 展开 -> 是否显示全部
      contentHeight1: 0, //展开收起内容高度1
      contentHeight2: 0, //展开收起内容高度2
      permissionList: [], //游戏获取权限列表
      permissionShow: false, //游戏获取权限列表展示
      up_owner_game: [], //up主其他分享
      downloadLoading1: {}, //下载当前loading id
      showEmptyMsg: false, //显示up分享其他消息
      filingInfoShow: false, // 备案信息弹窗
      filingInfoList: [], //备案信息列表
      is_record_info_show: false, //是否展示备案信息
      property: '', // 特征说明
      google_set: 0, //谷歌安装器配置θ隐藏 1游戏2集合3自定义链接
      google_set_id: '', //谷歌安装配置集合|游戏id|自定义链接
      spread_set: 0, //加速器配置θ隐藏 1游戏2集合3自定义链接
      spread_set_id: '', //加速器配置集合|游戏id|自定义链接
      video_set: 0, //视频安装地址配置θ隐藏 1显示
      video_set_title: '', //视频安装地址标题
      video_set_url: '', //视频安装地址
    };
  },
  computed: {
    follow_status() {
      return parseInt(this.up_info.is_focus) === 0 ? '关注' : '已关注';
    },
    gameTitle() {
      return this.deviceListShowItem.game && this.deviceListShowItem.game?.title
        ? this.deviceListShowItem.game?.title
        : '';
    },
    ...mapGetters({
      interstitialAd: 'system/interstitialAd',
    }),
    extra_tag_length() {
      return this.detail?.extra_tag?.length || 0;
    },
  },
  created() {
    this.detail = this.$route.params.gameInfo || { id: this.$route.params.id };
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.sticky_offset_top =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.query.replace) {
        vm.$router.go(-1);
      }
    });
  },
  async activated() {
    this.isIos = isIos;
    this.devicePayPopupShow = false;
    this.deviceDialogShow = false;
    await this.init();
  },
  methods: {
    // 获取云挂机设备
    async GetCloudMountedList() {
      this.available_devices = 0;
      this.cloudDeviceList = [];
      this.idleEquipmentList = [];
      this.onHookEquipmentList = [];
      this.obsoleteEquipmentList = [];
      this.deviceListShowItem = {};
      const res = await ApiCloudList();
      const { available_devices, list } = res.data;
      this.available_devices = available_devices;
      this.cloudDeviceList = list;
      // 用户无设备 && 无后台云设备
      if (!this.available_devices && !this.cloudDeviceList) {
        this.$toast('暂时没有可购买的设备了');
        return;
      }
      // 有后台云设备
      if (this.cloudDeviceList) {
        // 区分用户设备状态
        this.cloudDeviceList.forEach(device => {
          switch (device.expire_code) {
            case 0:
              this.idleEquipmentList.push(device);
              break;
            case 1:
              this.onHookEquipmentList.push(device);
              break;
            case 2:
              this.obsoleteEquipmentList.push(device);
              break;
            default:
              break;
          }
        });
      }

      this.setReceiveData(this.detail);
      // 设备容量大于设置百分比限制
      // const isBeyondLimit = (this.remain / this.count) * 100 > this.percent
      if (this.onHookEquipmentList.length) {
        const foundItem = this.onHookEquipmentList.find(
          item => item.game.id === this.detail.id,
        );
        // 如果挂机设备中存在当前游戏 直接进入
        if (foundItem) {
          this.deviceListShowItem = foundItem;
          this.replacementGame();
          return;
        }
      }
      if (this.idleEquipmentList.length) {
        this.setCheckCloudDeviceItem(this.idleEquipmentList[0]);
        this.toPage('CloudHangupInterface', {
          receiveData: this.detail,
          checkCloudDeviceItem: this.idleEquipmentList[0],
        });
      }
      // else if (this.onHookEquipmentList.length && isBeyondLimit) {
      //   // 有挂机设备 库存容量超出限制
      //   this.devicePayPopupShow = true
      // }
      else if (this.onHookEquipmentList.length) {
        // 有挂机设备 库存容量低于限制
        this.deviceListShowItem = this.onHookEquipmentList[0];

        this.deviceDialogShow = true;
      } else {
        this.devicePayPopupShow = true;
      }
    },
    async getEquipmentInventoryCapacity() {
      const res = await ApiCloudEquipPercent();
      const { count, remain, percent } = res.data;
      this.count = count;
      this.remain = remain;
      this.percent = percent;
    },
    devicePayCancel() {
      this.devicePayPopupShow = false;
    },
    devicePayConfirm() {
      this.devicePayPopupShow = false;
      this.toPage('BuyCloudDevices');
    },
    deviceListClick(item) {
      this.deviceListShowItem = item;
      this.deviceListShow = false;
    },
    // 替换游戏
    replacementGame() {
      this.setReceiveData(this.detail);
      this.setCheckCloudDeviceItem(this.deviceListShowItem);
      this.toPage('CloudHangupInterface', {
        receiveData: this.detail,
        checkCloudDeviceItem: this.deviceListShowItem,
      });
    },
    // 购买设备
    purchaseEquipment() {
      this.deviceDialogShow = false;
      this.toPage('BuyCloudDevices');
    },
    // 初始化
    async init() {
      await this.getDetailData();
      if (this.$route.params.download) {
        this.downloadGame();
      }
      await this.INSERT_ADSDK();
      await this.getUpDetailedResourceList();
    },
    // 获取详情页数据
    async getDetailData() {
      try {
        const res = await ApiGameRead({ id: this.detail.id });
        this.detail = res.data.detail;
        this.is_collection = res.data.is_collection;
        this.evaluate = res.data.evaluate;
        this.setGameInfo(this.detail);
        this.up_info = res.data.up_info || {};
        this.about_game_list = res.data.game_list;
        this.liked = res.data.liked;
        this.up_owner_game = res.data.up_owner_game;
        this.filingInfoList = res.data.detail.record_number_info;
        this.is_record_info_show = res.data.detail.is_record_info_show
          ? true
          : false;
        this.load_success = true;
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'GameNotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
        }
      } finally {
        this.$nextTick(() => {
          if (!this.contentHeight1 && this.$refs.content1) {
            this.contentHeight1 = this.$refs.content1.clientHeight;
          }
          if (!this.contentHeight2 && this.$refs.content2) {
            this.contentHeight2 = this.$refs.content2.clientHeight;
          }
          if (this.contentHeight1 > 100) {
            this.isAll1 = false;
          }
          if (this.contentHeight2 > 100) {
            this.isAll2 = false;
          }
        });
      }
    },
    toDetail(id, gameInfo) {
      this.toPage('UpDetail', {
        id,
        gameInfo,
      });
    },
    // 下载up其他分享
    downloadBtn(link, id) {
      if (this.downloadLoading1[id]) {
        return;
      }
      if (this.interstitial_ad_id) {
        (window.slotbydup = window.slotbydup || []).push({
          id: this.interstitial_ad_id,
          container: 'up-detail',
          async: true,
        });
      }
      if (!link) {
        this.$toast('暂无下载噢~');
        return;
      }
      // loading动画
      this.$set(this.downloadLoading1, id, true);
      setTimeout(() => {
        this.$set(this.downloadLoading1, id, false);
      }, 2000);
      window.location.href = link;
    },
    // 谷歌安装器
    async googleInstaller() {
      switch (this.detail.google_set) {
        case 1: //游戏详情页
          if (
            Number(this.google_set_classid) == 41 ||
            Number(this.google_set_classid) == 115
          ) {
            // 外部cps 单机sdk 详情页
            this.toPage('ExternalGameDetail', {
              id: this.detail.google_set_id,
            });
          } else {
            this.toPage('GameDetail', { id: this.detail.google_set_id });
          }

          break;
        case 2: //合集页
          this.toPage('ExternalGameCollect', {
            id: this.detail.google_set_id,
            info: this.detail,
          });
          break;
        case 3: //自定义链接
          window.location.href = this.detail.google_set_id;
          break;

        default:
          break;
      }
    },
    // 加速器下载
    acceleratorDow() {
      switch (this.detail.spread_set) {
        case 1: //游戏详情页
          // if (
          //   Number(this.spread_set_classid) == 41 ||
          //   Number(this.spread_set_classid) == 115
          // ) {
          //   // 外部cps 单机sdk 详情页
          //   this.toPage('ExternalGameDetail', {
          //     id: this.detail.spread_set_id,
          //   });
          // } else {
          this.toPage('GameDetail', { id: this.detail.spread_set_id });
          // }
          break;
        case 2: //合集页
          this.toPage('ExternalGameCollect', {
            id: this.detail.spread_set_id,
            info: this.detail,
          });
          break;
        case 3: //自定义链接
          window.location.href = this.detail.google_set_id;
          // this.toPage('Iframe', {
          //   title: '',
          //   url: googleId,
          // });
          break;

        default:
          break;
      }
    },
    // 处理游戏权限详情
    async handlePermission() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
    // 获取up详情资源列表
    async getUpDetailedResourceList() {
      await ApiGameGetUpOwnerGame({
        game_id: this.detail.id,
        page: 1,
        listRows: 10,
      });
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.nav_bg_transparent = false;
      } else {
        this.nav_bg_transparent = true;
      }
    },
    // 处理关注
    async handleFollow() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.finished == false) return false;
      this.finished = false;
      try {
        const res = await ApiUserFollowUser({
          memId: this.up_info.up.user_id,
          type: this.up_info.is_focus == 0 ? 1 : 0,
        });
        this.$toast(res.msg);
        await this.getDetailData();
      } finally {
        this.finished = true;
      }
    },
    // 点击tab
    clickTab(index) {
      window.scrollTo(0, 0);
      this.current = index;
    },
    // 相关游戏
    formatRelatedList() {
      let arr = [];
      for (let i = 0; i < this.about_game_list.length; i += 2) {
        arr.push(this.about_game_list.slice(i, i + 2));
      }
      return arr;
    },
    // 设置收藏
    setCollectStatus() {
      ApiGameCollectionUpGame({
        gameId: this.detail.id,
        type: this.is_collection ? 0 : 1,
      }).then(res => {
        this.$toast(res.msg);
        if (res.code) {
          this.is_collection = !this.is_collection;
        }
      });
    },
    // 下载视频教程
    downloadVideoTutorial(VideoLink) {
      window.open(VideoLink, '_blank');
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    // 非常棒臭鸡蛋
    async gameRate(type) {
      if (this.evaluate != 0) {
        return false;
      }
      try {
        const res = await ApiUpGameEvaluate({
          gameId: this.detail.id,
          type: type,
        });
        this.evaluate = 1;
      } catch {}
    },
    // 云手机
    async goToCloudDevices() {
      // await this.getEquipmentInventoryCapacity()
      await this.GetCloudMountedList();
    },
    // 下载
    downloadGame() {
      if (this.interstitial_ad_id) {
        (window.slotbydup = window.slotbydup || []).push({
          id: this.interstitial_ad_id,
          container: 'up-detail',
          async: true,
        });
      }
      // loading动画
      this.downloadLoading = true;
      setTimeout(() => {
        this.downloadLoading = false;
      }, 2000);
      window.location.href = this.detail.down_a;
    },
    // 求更新提交
    async submitPleaseUpdate() {
      if (!this.please_update_input) {
        this.$toast('请填写内容');
        return false;
      }
      const res = await ApiGameSubmitCrackWish({
        remark: this.please_update_input,
        gameName: this.detail.title,
      });
      this.please_update_popup = false;
      this.please_update_input = '';
      this.$toast(res.msg);
    },
    // 百度联盟广告
    async INSERT_ADSDK() {
      // if (!isAndroidBox) {
      //   return false;
      // }
      const res = await ApiIndexGetAd({ type: 1 });
      const list = res.data.list || [];
      list.forEach(ele => {
        if (ele.state == 1) {
          this.banner_ad_id = ele.position_id;
        } else {
          this.interstitial_ad_id = ele.position_id;
        }
      });
      if (this.banner_ad_id) {
        (window.slotbydup = window.slotbydup || []).push({
          id: this.banner_ad_id,
          container: 'banner-id',
          async: true,
        });
      }
    },
    // 处理tab底下高亮标记位移
    // handleLeftDistance(current) {
    //   let width = document.body.clientWidth;
    //   let length = this.tab_list.length;
    //   let left = width / length / 2 - 6;
    //   let distance = current * (width / length);
    //   return `${left + distance}px`;
    // },

    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
      setReceiveData: 'cloud_hangup/setReceiveData',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
    }),
  },
  components: {
    CommentTab,
    ResourceListTab,
  },
};
</script>
<style lang="less" scoped>
/deep/.van-sticky--fixed {
  left: 0;
  right: 0;
  margin: 0 auto;
  max-width: 450px;
  width: 100%;
}
.up-detail {
  position: relative;
}
.bg-color {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 264 * @rem;
}
.nav-bar {
  .text {
    font-size: 14 * @rem;
    color: #fff;
  }
  .text1 {
    font-size: 14 * @rem;
    color: #000;
  }
}
header {
  overflow: hidden;
  position: relative;
  z-index: 2;
  .wrapper {
    position: relative;
    margin-top: 50 * @rem;
    overflow: hidden;
    height: auto;
    max-height: 155 * @rem;
    .bg {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      width: 100 * @rem;
      height: 51 * @rem;
      .image-bg('~@/assets/images/up/up_bg1.png');
    }
    .game-info {
      padding: 28 * @rem 0 15 * @rem 18 * @rem;
      display: flex;
      .game-img {
        width: 72 * @rem;
        height: 72 * @rem;
      }
      .container {
        display: flex;
        flex-direction: column;
        margin-left: 12 * @rem;
        .top {
          font-weight: 600;
          font-size: 18 * @rem;
          color: #ffffff;
          height: 18 * @rem;
          line-height: 18 * @rem;
          text-align: left;
          font-style: normal;
          text-transform: none;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .center,
        .bottom {
          font-size: 12 * @rem;
          color: #ffffff;
          opacity: 0.8;
          margin-top: 9 * @rem;
        }
        .bottom {
          display: flex;
          align-items: center;
          .avatar {
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 6 * @rem;
          }
        }
      }
    }
    .game-info-tag {
      margin: 0 0 18 * @rem 18 * @rem;
      display: flex;
      flex-flow: wrap;
      height: 20 * @rem;
      line-height: 20 * @rem;
      overflow: hidden;
      span {
        box-sizing: border-box;
        height: 20 * @rem;
        border-radius: 4 * @rem;
        border: 1px solid rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 11 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
        padding: 3 * @rem 4 * @rem;
        &:not(:first-of-type) {
          margin-left: 8 * @rem;
        }
      }
    }
  }
}
main {
  border-radius: 20 * @rem 20 * @rem 0 0;
  background: #fff;
  overflow: hidden;
  .banner-id {
    margin-bottom: 10 * @rem;
    overflow: hidden;
  }
  .tab-container {
    padding: 20 * @rem 0;
    .tabs {
      display: flex;
      position: relative;
      background-color: #fff;
      align-items: center;
      width: 100%;
      // padding: 0 18 * @rem;
      .tab {
        // width: 36 * @rem;
        width: 125 * @rem;
        height: 44 * @rem;
        // margin-right: 20 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          display: block;
          font-size: 18 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333333;
          position: relative;
        }
      }
      .line {
        flex: 1;
        height: 0 * @rem;
        border: 1 * @rem solid #e8e8e8;
      }
      .tab-line {
        position: absolute;
        width: 14 * @rem;
        height: 4 * @rem;
        border-radius: 2 * @rem;
        background-color: @themeColor;
        left: 0;
        bottom: 0;
        transform: translateX(56 * @rem);
        transition: 0.3s;
      }
    }
    .tab-content {
      /deep/ .van-loading {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &.tab-detail {
        padding: 0 0 * @rem 15 * @rem;
        section {
          .title {
            padding: 0 18 * @rem;
            margin-bottom: 12 * @rem;
            display: flex;
            align-items: center;
            &::before {
              content: '';
              display: block;
              width: 5 * @rem;
              height: 12 * @rem;
              border-radius: 16 * @rem;
              background: #21b98a;
              margin-right: 6 * @rem;
            }
            .title-text {
              flex: 1;
              min-width: 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
              .text {
                font-size: 15 * @rem;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #333333;
                height: 20 * @rem;
                font-weight: 600;
                font-size: 16 * @rem;
                line-height: 20 * @rem;
              }
            }
            .title-icon {
              width: 8 * @rem;
              height: 12 * @rem;
              .image-bg('~@/assets/images/mine/function-right-icon.png');
            }
          }
        }
        .warm-reminder-box {
          margin-bottom: 20 * @rem;
          .game-warm-reminder {
            padding: 24 * @rem 18 * @rem 0 18 * @rem;
            display: flex;
            flex-direction: column;
            .property_text {
              margin-bottom: 10 * @rem;
              // height: 15 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #555555;
              line-height: 15 * @rem;
            }
            .up_title {
              margin-bottom: 19 * @rem;
              color: #999999;
              line-height: 15 * @rem;
              height: 15px;
              font-weight: 400;
              font-size: 12px;
            }
            .btn_title {
              margin-bottom: 20 * @rem;
              span {
                height: 18 * @rem;
                font-weight: 600;
                font-size: 14 * @rem;
                color: #21b98a;
                line-height: 18 * @rem;
                text-align: left;
                font-style: normal;
                text-decoration-line: underline;
                text-transform: none;
              }
            }
            .prompt-box {
              height: 40 * @rem;
              background: #f6f6f6;
              border-radius: 4 * @rem;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 22 * @rem;
              padding: 0 20 * @rem 0 12 * @rem;
              box-sizing: border-box;
              .prompt-left {
                display: flex;
                align-items: center;
                .prompt-title {
                  display: flex;
                  align-items: center;
                  i {
                    background: url('~@/assets/images/up/wxts-img.png')
                      no-repeat 0 0;
                    background-size: 14 * @rem 14 * @rem;
                    width: 14 * @rem;
                    height: 14 * @rem;
                  }
                  span {
                    margin-left: 4 * @rem;
                    font-weight: 400;
                    font-size: 12 * @rem;
                    color: #555555;
                  }
                }
                .prompt-google {
                  margin-left: 20 * @rem;
                  font-weight: 600;
                  font-size: 12 * @rem;
                  color: #21b98a;
                  text-align: left;
                  font-style: normal;
                  text-decoration-line: underline;
                  text-transform: none;
                }
              }
              .prompt-right {
                display: flex;
                align-items: center;
                span {
                  font-weight: 600;
                  font-size: 12 * @rem;
                  color: #21b98a;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                }
                i {
                  margin-left: 4 * @rem;
                  background: url('~@/assets/images/up/xy-img.png') no-repeat 0
                    0;
                  background-size: 7 * @rem 11 * @rem;
                  width: 7 * @rem;
                  height: 11 * @rem;
                }
              }
            }
          }
          // .btn-video-tutorial {
          //   padding: 0 18 * @rem;
          //   margin-bottom: 20 * @rem;
          //   height: 18 * @rem;
          //   font-weight: 600;
          //   font-size: 14 * @rem;
          //   color: @themeColor;
          //   line-height: 18 * @rem;
          //   text-decoration: underline;
          // }
          .introduction-text {
            padding: 0 18 * @rem;
            font-size: 12 * @rem;
            color: #777;
            line-height: 18 * @rem;
            display: -webkit-box;
            &.mb10 {
              margin-bottom: 10 * @rem;
            }
            &.on {
              max-height: 100px;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 5;
              overflow: hidden;
            }
          }
          .more-text {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 18 * @rem;
            margin-top: 8 * @rem;
            span {
              font-size: 12 * @rem;
              color: @themeColor;
            }
            .more-text-icon {
              width: 11 * @rem;
              height: 7 * @rem;
              background: url(~@/assets/images/games/bottom-arrow-green.png)
                center center no-repeat;
              background-size: 11 * @rem 7 * @rem;
              margin-left: 4 * @rem;
              transition: 0.3s;
              &.on {
                transform: rotateZ(180deg);
              }
            }
          }
        }

        .up_main_quotation {
          padding-top: 32 * @rem;
          margin-bottom: 20 * @rem;
          .introduction-text {
            padding: 0 18 * @rem;
            font-size: 12 * @rem;
            color: #777;
            line-height: 18 * @rem;
            display: -webkit-box;
            &.mb10 {
              margin-bottom: 10 * @rem;
            }
            &.on {
              max-height: 100px;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 5;
              overflow: hidden;
            }
          }
          .more-text {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 18 * @rem;
            margin-top: 8 * @rem;
            span {
              font-size: 12 * @rem;
              color: @themeColor;
            }
            .more-text-icon {
              width: 11 * @rem;
              height: 7 * @rem;
              background: url(~@/assets/images/games/bottom-arrow-green.png)
                center center no-repeat;
              background-size: 11 * @rem 7 * @rem;
              margin-left: 4 * @rem;
              transition: 0.3s;
              &.on {
                transform: rotateZ(180deg);
              }
            }
          }
        }
        .notice {
          width: 340 * @rem;
          margin: 0 auto 12 * @rem;
          padding: 8 * @rem 10 * @rem;
          box-sizing: border-box;
          background: #fff8ec;
          border-radius: 4 * @rem 4 * @rem 4 * @rem 4 * @rem;
          font-size: 11 * @rem;
          color: #dd5102;
          line-height: 18 * @rem;
        }
        .game-picture-swiper {
          box-sizing: border-box;
          width: 100%;
          margin-top: 20 * @rem;
          margin-bottom: 20 * @rem;
          overflow: hidden;
          .swiper-container {
            box-sizing: border-box;
            width: 100%;
            padding-left: 18 * @rem;
            .swiper-slide {
              width: auto;
              height: 150 * @rem;
              margin-left: 10 * @rem;
              border-radius: 8 * @rem;
              overflow: hidden;
              &:first-child {
                margin-left: 0;
              }
              .slide-img {
                width: auto;
                height: 100%;
              }
              &.video-container {
                width: 267 * @rem;
                height: 150 * @rem;
                overflow: hidden;
                #video {
                  display: block;
                  width: 267 * @rem;
                  height: 150 * @rem;
                  outline: none;
                  border: none;
                }
                .mask {
                  width: 100%;
                  height: 150 * @rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: absolute;
                  left: 0;
                  top: 0;
                  .fixed-center;
                  background-color: rgba(0, 0, 0, 0);
                  z-index: 10;
                  .play-btn {
                    width: 38 * @rem;
                    height: 38 * @rem;
                    background: url(~@/assets/images/video-play.png) no-repeat;
                    background-size: 38 * @rem 38 * @rem;
                  }
                }
              }
            }
          }
        }
      }
      .hot-comments {
        .hot-comment-list {
          margin-top: -10 * @rem;
        }
        .comment-more {
          width: 170 * @rem;
          height: 40 * @rem;
          margin: 0 auto;
          background-color: #f5f5f6;
          border-radius: 20 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .comment-more-text {
            font-size: 14 * @rem;
            color: #9a9a9a;
            font-weight: 400;
          }
          i {
            display: block;
            width: 6 * @rem;
            height: 10 * @rem;
            background: url(~@/assets/images/games/arrow-right-grey.png) center
              center no-repeat;
            background-size: 6 * @rem 10 * @rem;
            margin-left: 6 * @rem;
          }
        }
      }
      .rate {
        margin: 30 * @rem auto;
        .bg3 {
          .image-bg('~@/assets/images/up/up_bg3.png');
          width: 302 * @rem;
          height: 14 * @rem;
          margin: 0 auto;
        }
        .button-container {
          display: flex;
          justify-content: center;
          margin: 16 * @rem auto;
          .button {
            box-sizing: border-box;
            border: 1 * @rem solid @themeColor;
            width: 120 * @rem;
            height: 28 * @rem;
            color: #fff;
            background: @themeColor;
            border-radius: 25 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            &.right {
              margin-left: 20 * @rem;
              color: @themeColor;
              background: #fff;
            }
            &.empty {
              color: #fff;
              background: #e0e0e0;
              border-color: #e0e0e0;
            }
          }
        }
      }
      .screenshot-list {
        padding: 0 12 * @rem;
        display: grid;
        // grid-template-columns: repeat(2, 164 * @rem);
        gap: 10 * @rem;
        justify-content: center;
        margin-bottom: 20 * @rem;
        .screenshot-list-item {
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .half-width {
          width: 164 * @rem;
          height: 115 * @rem;
          overflow: hidden;
        }
        .full-width {
          grid-column: span 2;
          width: 339 * @rem;
          height: 214 * @rem;
          overflow: hidden;
        }
      }
      .up-share {
        // margin-bottom: 32 * @rem!important;
        .deal-list {
          display: flex;
          padding: 0 18 * @rem 0;
          .deal-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 25%;
            overflow: hidden;
            .game-pic {
              width: 72 * @rem;
              height: 72 * @rem;
            }
            .game-name {
              text-align: center;
              width: 100%;
              text-overflow: ellipsis;
              overflow: hidden;
              font-size: 12 * @rem;
              color: #000000;
              line-height: 15 * @rem;
              margin-top: 7 * @rem;
              width: 72 * @rem;
              height: 30 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #333333;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              justify-content: flex-start;
            }
            .game-btn {
              margin-top: 12 * @rem;
              width: 64 * @rem;
              height: 24 * @rem;
              background: @themeBg;
              border-radius: 19 * @rem;
              text-align: center;
              line-height: 24 * @rem;
              font-size: 13 * @rem;
              color: #ffffff;
              &.loading {
                position: relative;
                font-size: 0;
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  display: block;
                  width: 16 * @rem;
                  height: 16 * @rem;
                  background-size: 16 * @rem 16 * @rem;
                  background-image: url(~@/assets/images/downloadLoading.png);
                  animation: rotate 1s infinite linear;
                }
              }
            }
          }
        }
      }
      .up-game-dsc {
        padding: 32 * @rem 12 * @rem 0 12 * @rem;
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
        .dsc-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left-dsc {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > span {
              font-weight: 600;
              font-size: 16 * @rem;
              color: #333333;
              line-height: 20 * @rem;
              margin-left: 6 * @rem;
            }
          }
          .right-dsc {
            display: flex;
            align-items: center;
            justify-content: space-between;
            span {
              display: flex;
              align-items: center;
              .avatar {
                width: 20 * @rem;
                height: 20 * @rem;
                border-radius: 50%;
                overflow: hidden;
                margin: 0 6 * @rem;
              }
            }
            .title-icon {
              margin-left: 8 * @rem;
              width: 8 * @rem;
              height: 12 * @rem;
              .image-bg('~@/assets/images/mine/function-right-icon.png');
            }
          }
          .is_right_text {
            font-weight: 600;
            font-size: 16 * @rem;
            color: #333333;
          }
          &:not(:first-of-type) {
            margin-top: 20 * @rem;
          }
        }
      }
      .related {
        margin-top: 5 * @rem;
        padding-right: 0;
        padding-left: 0;
        .section-title {
          padding-left: 18 * @rem;
        }
        .game-list {
          box-sizing: border-box;
          display: flex;
          justify-content: space-between;
          margin-top: 15 * @rem;
          width: 100%;
          overflow-x: auto;
          padding-left: 18 * @rem;
          padding-bottom: 20 * @rem;
          &::-webkit-scrollbar {
            display: none !important;
          }
          .game-group {
            &:not(:first-of-type) {
              margin-left: 10 * @rem;
            }
            &:last-of-type {
              padding-right: 18 * @rem;
            }
          }
          .game-item {
            width: 280 * @rem;
            height: 100 * @rem;
            background: #ffffff;
            box-shadow: 0 * @rem 3 * @rem 11 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
            border-radius: 10 * @rem 10 * @rem 10 * @rem 10 * @rem;
            box-sizing: border-box;
            padding: 0 14 * @rem;
            display: flex;
            align-items: center;
            &:not(:first-of-type) {
              margin-top: 8 * @rem;
            }
            /deep/ .game-item-components .tags .tag {
              background-color: #ebebeb;
            }
          }
        }
      }
    }
    .tab-resources {
      padding: 0 18 * @rem;
      .empty-msg {
        color: @themeColor;
        font-size: 13 * @rem;

        margin: 10 * @rem 0 0 0;
        text-align: left;
      }
    }
    .h8_bg {
      width: 100%;
      height: 8 * @rem;
      background: #f8f8f8;
      // margin-bottom: 32 * @rem;
    }
  }
}
.bottom-container {
  flex-shrink: 0;
  width: 100%;
  height: calc(60 * @rem + @safeAreaBottom);
  height: calc(60 * @rem + @safeAreaBottomEnv);
  .bottom-fixed {
    box-sizing: border-box;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    .fixed-center;
    width: 100%;
    z-index: 2000;
    box-shadow: 0 * @rem -3 * @rem 4 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    .download-bar {
      flex: 1;
      min-width: 0;
      height: 60 * @rem;
      .download-content {
        height: 60 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        .download-btn {
          flex: 1;
          height: 44 * @rem;
          background: @themeBg;
          text-align: center;
          line-height: 44 * @rem;
          color: #fefefe;
          font-size: 16 * @rem;
          font-weight: 500;
          border-radius: 8 * @rem;
          &.loading {
            position: relative;
            font-size: 0;
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              display: block;
              width: 16 * @rem;
              height: 16 * @rem;
              background-size: 16 * @rem 16 * @rem;
              background-image: url(~@/assets/images/downloadLoading.png);
              animation: rotate 1s infinite linear;
            }
          }
        }
        .download-no {
          background-color: #bbb;
          flex: 1;
          height: 44 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fefefe;
          font-size: 16 * @rem;
          font-weight: bold;
          border-radius: 6 * @rem;
          margin: 0 7 * @rem;
        }
      }
    }
    .comment,
    .collect-btn {
      margin-right: 20 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .comment-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        .image-bg('~@/assets/images/up/comment-icon.png');
      }
      &.had {
        .collect-icon {
          .image-bg('~@/assets/images/collect-success.png');
        }
        .text {
          color: rgb(255, 180, 0);
        }
      }
      .collect-icon {
        width: 20 * @rem;
        height: 20 * @rem;
        .image-bg('~@/assets/images/up/collect-icon.png');
      }
      .text {
        font-size: 13 * @rem;
        color: #000000;
        font-weight: 500;
        margin-top: 5 * @rem;
      }
    }
  }
}
.please-update-popup {
  width: 300 * @rem;
  .title {
    margin: 20 * @rem auto 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .please-update-input {
    display: block;
    box-sizing: border-box;
    padding: 7 * @rem 9 * @rem;
    margin: 0 auto;
    font-size: 16 * @rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    resize: none;
    line-height: 21 * @rem;
    width: 264 * @rem;
    height: 85 * @rem;
    border-radius: 6 * @rem 6 * @rem 6 * @rem 6 * @rem;
    border: 1 * @rem solid #efefef;
  }
  .button-container {
    margin: 15 * @rem auto;
    display: flex;
    justify-content: space-between;
    padding: 0 30 * @rem;
    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 110 * @rem;
      height: 35 * @rem;
      border-radius: 18 * @rem 18 * @rem 18 * @rem 18 * @rem;
    }
    .left {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .right {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #ffffff;
      background: @themeColor;
    }
  }
}
.onhook-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 20 * @rem 15 * @rem 25 * @rem 15 * @rem;
  background-size: 300 * @rem auto;
  border-radius: 20 * @rem;
  z-index: 2996 !important;
  .title {
    text-align: center;
    font-weight: bold;
  }
  .content {
    line-height: 17 * @rem;
    font-size: 14 * @rem;
    color: #777777;
    padding: 0 25 * @rem;
    margin-top: 22 * @rem;
  }
  .btn-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 12 * @rem 0 0;
    .cancel,
    .confirm {
      width: 126 * @rem;
      height: 40 * @rem;
      border-radius: 30 * @rem;

      font-size: 13 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cancel {
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .confirm {
      margin-left: 17 * @rem;
      color: #ffffff;
      background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
    }
  }

  .tips {
    display: flex;
    align-items: center;
    margin: 10 * @rem auto 0;
    padding: 0 25 * @rem;
    .gou {
      width: 12 * @rem;
      height: 12 * @rem;
      border-radius: 12 * @rem;
      border: 1px solid #7d7d7d;
      &.remember {
        background: url('~@/assets/images/cloudHangup/bzts-img.png') no-repeat 0
          0;
        background-size: 14 * @rem 14 * @rem;
        width: 14 * @rem;
        height: 14 * @rem;
        border: none;
      }
    }
    .tips-text {
      font-size: 12 * @rem;
      color: #999999;
      margin-left: 6 * @rem;
    }
  }
}
.device-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .title-text {
      font-size: 14 * @rem;
      padding: 5 * @rem 0;
      text-align: left;
      box-sizing: border-box;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .device-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .device-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }
    .hangingUpName {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 14 * @rem;
      margin: 10 * @rem 0;
      padding: 0 18 * @rem;
      white-space: nowrap;
      overflow: hidden;
      > span {
        max-width: 70 * @rem;
        background: #fff;
      }
      .game-subtitle {
        max-width: 120 * @rem;
        box-sizing: border-box;
        padding: 2 * @rem 4 * @rem;
        height: 17 * @rem;
        border-radius: 4 * @rem;
        line-height: 17 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        > div {
          &.text-scroll {
            flex-shrink: 0;
            flex-grow: 1;
            white-space: nowrap;
            animation: scroll-left 6s linear forwards infinite;
          }
          @keyframes scroll-left {
            0% {
              transform: translateX(100%);
            }
            100% {
              transform: translateX(-100%);
            }
          }
        }
      }
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.permission-popup {
  max-height: 400 * @rem;
  min-height: 200 * @rem;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 16 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 60 * @rem;
    background-color: #fff;
    flex-shrink: 0;
  }
  .permission-list {
    padding: 0 14 * @rem 20 * @rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    .permission-item {
      padding: 10 * @rem;
      border-bottom: 1px solid #ebebeb;
      .name {
        font-size: 14 * @rem;
        color: #666;
      }
      .desc {
        font-size: 12 * @rem;
        color: #999;
        line-height: 18 * @rem;
        margin-top: 10 * @rem;
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
