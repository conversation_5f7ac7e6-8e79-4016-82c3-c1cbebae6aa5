<template>
  <div class="invite-cost-record">
    <nav-bar-2 :azShow="true" :title="type == 1 ? '兑换记录' : '提现记录'">
    </nav-bar-2>
    <content-empty v-if="finish && list.length === 0"></content-empty>
    <div v-else class="list">
      <template v-for="(item, index) in list">
        <div :key="index" v-if="type == 1" class="item">
          <div class="left">消耗{{ Math.abs(item.money) }}现金</div>
          <div class="right">
            <span>+{{ item.extra }}</span
            >金币
          </div>
        </div>
        <div :key="index" v-if="type == 2" class="item">
          <div class="left">消耗{{ Math.abs(item.money) }}现金</div>
          <div class="right">
            <span>+{{ item.extra }}</span
            >元
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import { ApiInviteExchangeLogList } from '@/api/views/weekWelfare.js';

export default {
  data() {
    return {
      type: 1, //1兑换记录2提现记录
      list: [],
      finish: 0, //0还没拉取数据1已经拉取完
    };
  },
  async created() {
    this.type = this.$route.params.type;
    const res = await ApiInviteExchangeLogList({
      type: this.type == 1 ? 3 : 2,
    });
    this.finish = 1;
    this.list = res.data.list;
  },
};
</script>
<style lang="less" scoped>
.list {
  padding: 20 * @rem;
  .item {
    display: flex;
    justify-content: space-between;
    line-height: 40 * @rem;
    .left,
    .right {
      font-size: 15 * @rem;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #333438;
    }
    .right {
      span {
        color: #f04c32;
      }
    }
  }
}
</style>
