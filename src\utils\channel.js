import BASEPARAMS from '@/utils/baseParams.js'
import { isIos } from '@/utils/userAgent';

let default_package = {
  app_name: '3733游戏盒',
  app_icon: 'https://static-hw.3733.com/wa/public/logo-3733.png',
  favicon: 'https://static-hw.3733.com/wa/public/favicon.ico',
  start_img: [{
    media: "(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/2048-2732.png"
  },{
    media: "(device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1290-2796.png"
  }, {
    media: "(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1668-2388.png"
  }, {
    media: "(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1536-2048.png"
  }, {
    media: "(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1668-2224.png"
  }, {
    media: "(device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1620-2160.png"
  }, {
    media: "(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1284-2778.png"
  }, {
    media: "(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1170-2532.png"
  }, {
    media: "(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1125-2436.png"
  }, {
    media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1242-2688.png"
  }, {
    media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/828-1792.png"
  }, {
    media: "(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/1242-2208.png"
  }, {
    media: "(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/750-1334.png"
  }, {
    media: "(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/start-img2/640-1136.png"
  }]
}

let channel_package1 = {
  app_name: '0.1折手游',
  app_icon: 'https://static-hw.3733.com/wa/public/package1/logo.png',
  favicon: 'https://static-hw.3733.com/wa/public/package1/favicon.ico',
  start_img: [{
    media: "(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/2048-2732.jpg"
  }, {
    media: "(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1668-2388.jpg"
  }, {
    media: "(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1668-2224.jpg"
  }, {
    media: "(device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1620-2160.jpg"
  }, {
    media: "(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1536-2048.jpg"
  }, {
    media: "(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1284-2778.jpg"
  }, {
    media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1242-2688.jpg"
  }, {
    media: "(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1242-2208.jpg"
  }, {
    media: "(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1170-2532.jpg"
  }, {
    media: "(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/1125-2436.jpg"
  }, {
    media: "(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/828-1792.jpg"
  }, {
    media: "(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/750-1334.jpg"
  }, {
    media: "(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)",
    href: "https://static-hw.3733.com/wa/public/package1/640-1136.jpg"
  }]
}
// 查询是否满足a域名条件插入相关节点
export function loadWpaAbout() {
  if (BASEPARAMS.channel == 'cps5740') {
    insertLink(channel_package1)
  } else {
    insertLink(default_package)
  }
}

function insertLink(pack) {
  let linkElements = []

  let title = document.createElement('title')
  title.innerHTML = pack.app_name
  linkElements.push(title)

  let link1 = document.createElement('link')
  link1.rel = 'icon'
  link1.href = pack.favicon
  linkElements.push(link1)

  if(isIos) {
    let meta1 = document.createElement('meta')
    meta1.name = 'apple-mobile-web-app-title'
    meta1.content = pack.app_name
    linkElements.push(meta1)

    let link2 = document.createElement('link')
    link2.rel = 'apple-touch-icon-precomposed'
    link2.id = 'appleTouchIcon'
    link2.href = pack.app_icon
    linkElements.push(link2)

    pack.start_img.forEach(ele => {
      let link3 = document.createElement('link')
      link3.rel = 'apple-touch-startup-image'
      link3.media = ele.media
      link3.href = ele.href
      linkElements.push(link3)
    });
  }

  linkElements.forEach(link => {
    document.head.appendChild(link);
  });
}