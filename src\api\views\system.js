import { request } from '../index';
import h5Page from '@/utils/h5Page.js';

/**
 * 初始化接口
 * @param channel
 * @param build
 */
export function ApiIndexExtra(params = {}) {
  // return request('/v2024/index/extra', params); 
  return request('/v2025/Setting/extra', params);
}
/*
{
  app_name,  包名
  access_tracking_io,  热云统计，年底不续费后可删除
  access_medium_h5_io, h5来源统计
  access_medium_wechat_io, 小程序来源统计
  access_medium_io, IOS马甲包来源统计
  bang_email, 海外限定，绑定邮箱
  captcha_is_open, 滑动验证弹窗开启控制
  configs: {
      channel_ad_info, 渠道相关信息（涉及跳转页面）
      channel_show_ad, 渠道广告弹窗
      dl_config,  根据dl_config隐藏下载和预约按钮
      flavor,  判断是否UP版，tabbar显示内容不一样（有游戏库、排行）
      hide_jfq, 判断是否隐藏福利中心
      kefu: { 
          qq,  返利申请表单页、云挂机、客服相关页面里用客服qq
          qq_url, 云挂机、客服相关里有用到，但是只是用来判断是否需要客服功能
      },
      show_my_game,  底部展示我的游戏
      show_suspension,  展示引导和右下角弹窗
      show_transaction, 展示交易模块
  },
  click_event, 是否需要统计用户行为
  default_avatar,  默认头像，不同包不同
  default_search: [{  搜索框用
    keyword,
    img
  }],
  ios_mconfig,  ios马甲包专用弹窗，引导到桌面端用
  is_hw,  判断是否为海外
  is_h5_first, 原来是用来旧版我的游戏页面的，现已无用
  idcard_img, 实名制弹窗图片
  jump_game,  投放推荐包跳转游戏详情页用
  nav_angle, tabbar上面的小图片配置，不清楚现在还有没有在用
  levitated_sphere, 新人礼弹窗图片地址
  login_type: {  本来是给海外限定专用的， 现在修改密码相关代码里还有，感觉可以撤掉但是要改代码
    type,
    msg,
    copy_text,
  },
  question_list,  海外限定，密保相关问题
  sms_login,  海外限定，邮箱登录
  share_info,  不需要实际内容，只判断了数组长度大于0
  svip_img,  实际已经不需要
  underage_privacy_url,  有且只在实名页面需要
  web_show_down,  限定渠道控制引导桌面小球用
}
*/

export function ApiIndexExtra2(params = {}) {
  return request('/v2025/Setting/init', params);
}

/* 
上传图片
*/
export function ApiUploadImage(params = {}) {
  return request('/upload', params, false, h5Page.domainUpload);
}

/**
 * 活动列表
 * @param {showType} 1=banner列表  2=web热门活动
 * @return "data": {
  *    "list": [
  *      {
#        "id": 1084,
#        "titleimg": "https://pic5.pic3733.com/banner/202109/110e15d14a4267e65ff6f38d45d0fbd2_n.jpg",
#        "type": 4,  // 1=游戏，2=礼包，3=资讯，4=盒子内部打开url，5=外部浏览器打开url，6=回复，7=预约, 30=捡漏 21=限时兑换
#        "extra": "https://game.3733.com",//额外参数，不同type不同用法
#        "scale": "1",
#        "color_value": "#59676d" //色值
#      }
  *    ]
  *  }
*/
export function ApiIndexBannerList(params = {}) {
  return request('/api/index/bannerList', params);
}

/**
 * up资源功能 - 获取广告配置信息
 * */
export function ApiIndexGetAd(params = {}) {
  return request('/api/index/getAd', params);
}

/**
 * 获取分享信息
 * */
export function ApiCommonShareInfo(params = {}) {
  return request('/api/common/shareInfo', params);
}

// up资源功能 - 上传apk文件
/**
 * @param {string} up_file 必填，apk文件
 */
export function ApiUploadApk(params = {}) {
  return request('/h5/apk/upAPk', params, false);
}

// 关于我们
export function ApiCommonAboutus(params = {}) {
  return request('/api/common/aboutUs', params);
}

// 自家上报
export function ApiMediumSubmitMediumActivity(params = {}) {
  return request('/api/medium/submitMediumActivity', params);
}

// 投放 - 微信小程序上报激活
export function ApiMediumSubmitWechatMedium(params = {}) {
  return request('/api/medium/submitWechatMedium', params);
}

// 投放 - 微信小程序上报注册
export function ApiMediumSubmitWechatRegister(params = {}) {
  return request('/api/medium/submitWechatRegister', params);
}

// 6.1 - 用户行为埋点上报  2024年2月2日15:45:19
export function ApiClickClickEvent(params = {}) {
  return request(
    '/click/click/clickEvent',
    params,
    true,
    h5Page.clickEventApiUrl,
  );
}

// 马甲包更新
export function ApiUpdateMjbCheck(params = {}) {
  return request('/api/update/mjbCheck', params);
}

// 马甲包更新
export function ApiCommonGetAgentQqInfo(params = {}) {
  return request('/api/common/getAgentQqInfo', params);
}

// 首页系统弹窗
export function ApiV2024IndexGetPopOrder(params = {}) {
  return request('/v2024/index/getPopOrder', params);
}

// 首页系统弹窗-新人/回归弹窗-刷新
export function ApiV2024BeginnerPopupIndex(params = {}) {
  return request('/v2024/beginner/popUpIndex', params);
}

// 首页系统弹窗-新人/回归弹窗-膨胀配置内容
export function ApiV2024BeginnerPopupExpansion(params = {}) {
  return request('/v2024/beginner/popUpExpansion', params);
}

// 首页系统弹窗-新人/回归弹窗-膨胀配置内容
export function ApiV2024BeginnerReceiveNewCoupon(params = {}) {
  return request('/v2024/beginner/receiveNewCoupon', params);
}

// 首页悬浮球
export function ApiV2024IndexGetFloatWindow(params = {}) {
  return request('/v2024/index/getFloatWindow', params);
}

/**
 * 新弹窗系统关闭上报
 * @param type
 * @param pop_id
 * @param pop_type
 * @param not_tip_day
 */
export function ApiV2024IndexPopcloseLog(params = {}) {
  return request('/v2024/index/popcloseLog', params);
}

// 消息推送
export function ApiV2024IndexRecordedCid(params = {}) {
  return request('/v2024/index/recordedCid', params);
}

/**
 * @function 获取接口ad广告弹窗
 * @param {int} gameld box_position=5的时候传
 * @param {int} box_position 1-首页,2-辅助空间,3-省钱卡,4-SVIP,5-SDK游戏,6-FC游戏,7-云挂机首页通知弹窗
 */export function ApiV2024IndexGetPopUp(params = {}) {
  return request('/v2024/index/getPopUp', params);
}

