<template>
  <div class="rebate">
    <nav-bar-2 :border="true" :title="$t('返利申请')">
      <template #right>
        <div class="kefu-icon" @click="toPage('Kefu')"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="tips">
        <div class="icon"></div>
        <div class="text">
          {{
            $t(
              '温馨提示：请准确填写信息，便于奖励发放。部分游戏不支持自助申请，请联系客服~',
            )
          }}
        </div>
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('充值账号') }}：</div>
        </div>
        <input :value="userInfo.username" type="text" class="center" disabled />
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('游戏名') }}：</div>
        </div>
        <input
          :value="gameInfo.title"
          :placeholder="$t('请选择游戏名')"
          type="text"
          class="center"
          disabled
        />
        <div class="right">
          <div class="right-icon"></div>
        </div>
      </div>
      <div class="info-item" @click="toXH('RebateSelectXH')">
        <div class="left">
          <div class="item-title">{{ $t('游戏小号') }}：</div>
        </div>
        <input
          :value="smallAccountName"
          :placeholder="$t('请选择小号')"
          type="text"
          class="center"
          disabled
        />
        <div class="right">
          <div class="right-icon"></div>
        </div>
      </div>
      <div class="info-item" @click="isShowTime = true">
        <div class="left">
          <div class="item-title">{{ $t('充值日期') }}：</div>
        </div>
        <input
          :value="time"
          :placeholder="$t('充值时间（超过7天将不受理）')"
          type="text"
          class="center"
          disabled
        />
        <div class="right">
          <div class="right-icon"></div>
        </div>
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('联系QQ') }}：</div>
        </div>
        <input
          :value="userInfoEx.qq ? userInfoEx.qq : ''"
          :placeholder="$t('请填写您的联系QQ')"
          type="text"
          class="center"
          disabled
        />
        <div @click="isShowQQPopup = true" class="right qq">
          {{ $t('获取QQ号') }}
        </div>
      </div>
      <div @click="isShowHowQQPopup = true" class="question">
        <div class="text">{{ $t('如何获取QQ号') }}</div>
        <div class="icon"></div>
      </div>
    </div>
    <div class="bottom">
      <div class="button" @click="next()">{{ $t('下一步') }}</div>
      <div class="explain">
        {{
          $t(
            '提交返利申请后我们会第一时间为您审核发放，若您提交后再次充值需提交返利申请，可联系QQ',
          )
        }}：{{ initData.configs.kefu.qq }}。
      </div>
    </div>
    <!-- 设置QQ弹窗 -->
    <van-dialog
      v-model="isShowQQPopup"
      :confirmButtonText="$t('前去设置')"
      show-cancel-button
      :lockScroll="false"
      @confirm="setQQ"
    >
      <div class="popup1">
        <div class="text">
          {{
            $t(
              '在盒子内完善个人资料中的QQ信息，可直接获取QQ号，免去多次填写QQ号的频繁操作',
            )
          }}。
        </div>
        <div class="text text2">{{ $t('完善QQ信息步骤') }}：</div>
        <div class="text">{{ $t('我的--个人资料---填写QQ号') }}</div>
      </div>
    </van-dialog>
    <!-- 如何设置QQ弹窗 -->
    <van-dialog
      v-model="isShowHowQQPopup"
      :confirmButtonText="$t('我知道了')"
      :lockScroll="false"
    >
      <div class="popup2">
        <div class="text">
          {{
            $t(
              '在盒子内完善个人资料中的QQ信息，可直接获取QQ号，免去多次填写QQ号的频繁操作。',
            )
          }}
        </div>
        <div class="text text2">{{ $t('完善QQ信息步骤') }}：</div>
        <div class="text">{{ $t('我的--个人资料---填写QQ号') }}</div>
      </div>
    </van-dialog>
    <!-- 选择日期弹窗 -->
    <van-popup
      v-model="isShowTime"
      closeable
      position="bottom"
      :style="{ height: '347px' }"
      :lock-scroll="false"
      round
    >
      <van-datetime-picker
        v-model="date"
        type="date"
        :title="$t('选择月日')"
        :min-date="minDate"
        :max-date="maxDate"
        @cancel="isShowTime = !isShowTime"
        @confirm="timeFormat()"
      />
    </van-popup>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'RebateFirst',
  data() {
    return {
      qq: '', //qq
      isShowQQPopup: false, //没有qq号获取的弹窗
      isShowHowQQPopup: false, //如何获取qq号的弹窗
      isShowTime: false, //显示选择时间弹窗
      minDate: new Date(new Date().getTime() - 168 * 60 * 60 * 1000), //最小日期7天前
      currentDate: new Date(), //当前选择的日期
      maxDate: new Date(), //最大日期
    };
  },
  computed: {
    date: {
      get() {
        return this.time;
      },
      set(value) {
        this.setTime(value);
      },
    },
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
      gameInfo: 'rebate/gameInfo',
      smallAccountName: 'rebate/smallAccountName',
      smallAccountId: 'rebate/smallAccountId',
      time: 'rebate/time',
      initData: 'system/initData',
    }),
  },
  methods: {
    toXH() {
      if (!this.gameInfo.title) {
        this.$toast(this.$t('请选择游戏'));
        return false;
      }
      this.$router.push({
        name: 'RebateSelectXH',
        params: { ...this.$route.params },
        query: { ...this.$route.query },
      });
    },
    setQQ() {
      this.isShowQQPopup = false;
      this.$nextTick(() => this.toPage('ChangeQQ'));
    },
    timeFormat() {
      this.isShowTime = !this.isShowTime;
      this.date = `${this.time.getFullYear()}-${
        this.time.getMonth() + 1
      }-${this.time.getDate()}`;
    },
    next() {
      if (!this.gameInfo.title) {
        this.$toast(this.$t('请选择游戏'));
        return false;
      }
      if (!this.smallAccountId) {
        this.$toast(this.$t('请选择小号'));
        return false;
      }
      if (!this.date) {
        this.$toast(this.$t('请选择充值日期'));
        return false;
      }
      if (!this.userInfoEx.qq) {
        this.$toast(this.$t('请设置您的QQ号'));
        return false;
      }
      this.$router.push({
        name: 'RebateSecond',
        params: { ...this.$route.params },
        query: { ...this.$route.query },
      });
    },
    ...mapMutations({
      setTime: 'rebate/setTime',
    }),
  },
};
</script>
<style lang="less" scoped>
.kefu-icon {
  width: 18 * @rem;
  height: 20 * @rem;
  background-image: url(~@/assets/images/mine/icon_kefu_black.png);
  background-size: 100%;
}
.main {
  padding: 14 * @rem;
  .tips {
    display: flex;
    align-items: center;
    .icon {
      flex: 0 0 25.5 * @rem;
      width: 25.5 * @rem;
      height: 23 * @rem;
      margin-right: 10 * @rem;
      background-image: url(~@/assets/images/rebate/heart.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .text {
      line-height: 18 * @rem;
      font-size: 13 * @rem;
      color: #ff3158;
    }
  }
  .info-item {
    border-bottom: 1 * @rem solid #eeeeee;
    padding: 17 * @rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 0 0 100 * @rem;
      .item-title {
        font-size: 15 * @rem;
      }
    }
    .center {
      font-size: 14 * @rem;
      text-align: left;
      flex: 1;
    }
    input:disabled {
      color: #999999;
    }
    .right {
      font-size: 15 * @rem;
      color: #666666;
      display: flex;
      align-items: center;
      .right-icon {
        width: 6 * @rem;
        height: 10 * @rem;
        margin-left: 5 * @rem;
        background: url(~@/assets/images/right-icon.png) no-repeat;
        background-size: 6 * @rem 10 * @rem;
      }
      &.qq {
        font-size: 12 * @rem;
        color: @themeColor;
      }
    }
  }
  .question {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    margin-top: 10 * @rem;
    .icon {
      width: 16 * @rem;
      height: 16 * @rem;
      margin-right: 5 * @rem;
      background-image: url(~@/assets/images/rebate/question.png);
      background-size: 100%;
    }
    .text {
      color: #666666;
    }
  }
}
.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 15 * @rem;
  background-color: #ffffff;
  .button {
    width: 100%;
    height: 45 * @rem;
    line-height: 45 * @rem;
    text-align: center;
    background: @themeBg;
    font-size: 16 * @rem;
    border-radius: 6 * @rem;
    color: #ffffff;
  }
  .explain {
    margin: 25 * @rem 0 20 * @rem;
    line-height: 21 * @rem;
    color: #999999;
    word-break: break-all;
  }
}
/deep/ .van-picker__confirm {
  color: @themeColor;
}
/deep/ .van-picker__toolbar {
  height: 50px;
}
.popup1,
.popup2 {
  padding: 20 * @rem 14 * @rem;
  .text {
    color: #666666;
    font-size: 15 * @rem;
    line-height: 22 * @rem;
  }
  .text2 {
    margin: 20 * @rem 0 10 * @rem;
    color: #333;
  }
}
</style>
