<template>
  <van-popup
    v-model="popupShow"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    position="bottom"
    round
    get-container="body"
    @open="getData"
    class="welfare-popup"
  >
    <div class="title">福利</div>
    <div class="close-btn" @click="popupShow = false"></div>
    <div class="popup-content" v-if="info.card648">
      <div class="welfare-box">
        <div class="card648">
          <div class="price">
            <em>￥</em>
            <span v-if="info.card648.status">648</span>
            <span v-else>0</span>
          </div>
          <div class="get-btn" v-if="info.card648.status" @click="get648Card">{{
            info.card648.status == 1 ? '去使用' : '领取'
          }}</div>
          <div class="get-btn not" v-else>暂无</div>
        </div>
        <div
          class="card welfare-item"
          @click="
            $router.push({
              name: 'GameGift',
              params: {
                game_id: id,
                class_id: gameInfo.classid,
              },
            })
          "
        >
          <span class="count">{{ info.card_count }}</span>
          <div class="name">礼包</div>
        </div>
        <div
          class="card welfare-item"
          @click="
            $router.push({
              name: 'GameCoupon',
              params: { game_id: id },
            })
          "
        >
          <span class="count">{{ info.coupon_sum }}</span>
          <div class="name">代金券</div>
        </div>
        <div
          class="card welfare-item"
          @click="
            $router.push({
              name: 'GameNews',
              params: { game_id: id },
            })
          "
        >
          <span class="count">{{ info.act_count }}</span>
          <div class="name">返利</div>
        </div>
      </div>
      <div
        class="welfare-list"
        v-if="info.exclusive_benefits && info.exclusive_benefits.length"
      >
        <div class="header-title">独家福利</div>
        <div
          class="welfare-item"
          v-for="(welfare, index) in info.exclusive_benefits"
          :key="index"
        >
          <div class="icon">
            <img :src="welfare.icon" alt="" />
          </div>
          <div class="info">
            <div class="w-title">{{ welfare.title }}</div>
            <div class="desc">{{ welfare.desc }}</div>
          </div>
          <div class="get-btn" @click="clickBenefitItem(welfare)">去领取</div>
        </div>
      </div>
    </div>
    <cardpass-copy-popup
      v-if="giftCopyPopupShow"
      :show.sync="giftCopyPopupShow"
      :info="this.info.card648"
    ></cardpass-copy-popup>
  </van-popup>
</template>
<script>
import { ApiGameGetGameBenefits } from '@/api/views/game.js';
import { ApiCardGet } from '@/api/views/gift.js';
export default {
  name: 'WelfarePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      required: true,
    },
    gameInfo: {
      required: true,
    },
  },
  data() {
    return {
      info: {},
      giftCopyPopupShow: false,
    };
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },

  methods: {
    async getData() {
      let res = await ApiGameGetGameBenefits({
        id: this.id,
      });
      this.info = res.data.info;
    },
    async get648Card() {
      this.$toast.loading('加载中');
      if (!this.info.card648.cardpass) {
        const res = await ApiCardGet({
          cardId: this.info.card648.id,
          autoXh: 1,
        });
        this.info.card648.status = 1;
        this.info.card648 = { ...this.info.card648, ...res.data };

        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${this.info.card648.game_id}`,
          adv_id: '暂无',
          game_name: this.info.card648.titlegame,
          game_type: `${this.info.card648.classid}`,
          game_size: '暂无',
          reward_type: this.info.card648.title, // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });
      }
      this.$toast.clear();
      this.$nextTick(() => {
        this.giftCopyPopupShow = true;
      });
    },
    clickBenefitItem(item) {
      switch (item.action_code) {
        case 13: // web内页
          this.toPage(item.web_page);
          break;
        case 23: // 游戏签到
          this.toPage('GameSignInDetail', { id: this.id });
          break;
        case 21: // 开局号
          this.toPage('OpeningAccount', { id: this.id });
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.welfare-popup {
  display: flex;
  flex-direction: column;
  background-color: #fbfbfe;
  padding: 0 18 * @rem 20 * @rem;
  box-sizing: border-box;
  height: 50vh;

  .title {
    padding: 26 * @rem 0 24 * @rem;
    text-align: center;
    height: 22 * @rem;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #000000;
    line-height: 22 * @rem;
  }

  .close-btn {
    display: block;
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/my-game/close.png) no-repeat center;
    background-size: 16 * @rem 16 * @rem;
    padding: 10 * @rem;
    position: absolute;
    top: 15 * @rem;
    right: 8 * @rem;
  }

  .popup-content {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
  .welfare-box {
    display: flex;
    align-items: center;
    width: 100%;
    height: 80 * @rem;
    background: url(~@/assets/images/my-game/welfare-bg.png) no-repeat center;
    background-size: 100% 80 * @rem;

    .card648 {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 96 * @rem;
      text-align: center;

      .price {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 28 * @rem;
        font-size: 12 * @rem;
        color: #333333;
        line-height: 17 * @rem;
        text-align: center;
        em {
          margin-top: 6 * @rem;
        }

        span {
          font-size: 20 * @rem;
          line-height: 28 * @rem;
          font-weight: bold;
        }
      }
      .get-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 58 * @rem;
        height: 24 * @rem;
        background: @themeBg;
        border-radius: 47 * @rem;
        font-weight: 600;
        font-size: 11 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
        text-align: center;
        margin-top: 7 * @rem;

        &.not {
          background: #ced4d7;
        }
      }
    }

    .welfare-item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 80 * @rem;
      position: relative;

      .count {
        height: 28 * @rem;
        font-size: 20 * @rem;
        font-weight: bold;
        color: #333333;
        line-height: 28 * @rem;
        text-align: center;
      }

      .name {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: rgba(51, 51, 51, 0.5);
        line-height: 15 * @rem;
        text-align: center;
        margin-top: 11 * @rem;
      }

      &::after {
        content: '';
        display: block;
        width: 0 * @rem;
        height: 27 * @rem;
        border: 1 * @rem solid rgba(33, 185, 138, 0.12);
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      &:last-of-type::after {
        display: none;
      }
    }
  }

  .welfare-list {
    margin-top: 24 * @rem;
    .header-title {
      height: 20 * @rem;
      font-weight: bold;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
    }
    .welfare-item {
      display: flex;
      align-items: center;
      margin-top: 18 * @rem;

      .icon {
        width: 36 * @rem;
        height: 36 * @rem;
        margin-right: 10 * @rem;
      }

      .info {
        flex: 1;
        min-width: 0;
        .w-title {
          height: 18 * @rem;
          font-weight: bold;
          font-size: 14 * @rem;
          color: #111111;
          line-height: 18 * @rem;
        }
        .desc {
          width: 100%;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #666666;
          line-height: 14 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 4 * @rem;
        }
      }

      .get-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 58 * @rem;
        height: 24 * @rem;
        background: #ecfbf4;
        border-radius: 19 * @rem;
        margin-left: 30 * @rem;
        font-weight: bold;
        font-size: 11 * @rem;
        color: @themeColor;
        line-height: 14 * @rem;
      }
    }
  }
}
</style>
