<template>
  <div>
    <!-- 平台活动弹窗 -->
    <van-popup
      v-model="popupShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="true"
      class="platform-activity-popup"
    >
      <div class="container">
        <div class="tabs">
          <div
            class="tab"
            v-for="(tab, index) in tabList"
            :key="index"
            :class="{
              active: current === tab.tabIndex,
              text3_active: tab.name.length === 3,
              text4_active: tab.name.length >= 4,
            }"
            @click="clickTab(tab.tabIndex)"
          >
            <span>{{ tab.name }} </span>
          </div>
        </div>
        <div class="content">
          <div v-if="current == 0">
            <game-coupon
              :gameId="game_id"
              @close-popup="popupShow = false"
              :popupCounterNum="popupCounterNum"
            ></game-coupon>
          </div>
          <div v-else-if="current == 1">
            <game-gift
              :gameId="game_id"
              :class_id="class_id"
              @close-popup="popupShow = false"
              :popupCounterNum="popupCounterNum"
            ></game-gift>
          </div>
          <div v-else-if="current == 2">
            <game-news
              :game_id="game_id"
              :popupCounterNum="popupCounterNum"
              :vip_content="vip_content"
              :vip_price_list="vip_price_list"
            ></game-news>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import GameCoupon from './game-coupon/index.vue';
import GameGift from './game-gift/index.vue';
import GameNews from './game-news/index.vue';
export default {
  name: 'platformActivityPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    game_id: {
      type: Number,
      default: 0,
    },
    class_id: {
      type: Number,
      default: 0,
    },
    currentIndex: {
      type: Number,
      default: 0,
    },
    vip_content: {
      type: String,
      default:
        '部分游戏月卡、基金、理财、礼包不算VIP经验和充值，此表由游戏厂商提供，仅供参考。如与实际不符，请以游戏内为准，详细咨询客服',
    },
    vip_price_list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    GameCoupon,
    GameGift,
    GameNews,
  },
  data() {
    return {
      current: 0,
      popupCounterNum: 0, //弹窗计数器
      tabList: [
        { name: '代金券', tabIndex: 0 },
        { name: '礼包', tabIndex: 1 },
        { name: '返利', tabIndex: 2 },
      ],
    };
  },
  watch: {
    popupShow(newVal) {
      if (newVal) {
        this.current = this.currentIndex;
        this.popupCounterNum++;
      }
    },
    currentIndex(newIndex) {
      this.current = newIndex;
    },
  },
  async created() {
    this.current = this.currentIndex;
  },
  async mounted() {},

  methods: {
    clickTab(index) {
      this.current = index;
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.platform-activity-popup {
  max-height: 492 * @rem;
  padding-top: 22 * @rem;
  background: #ffffff;
  overflow: hidden;
  box-sizing: border-box;
  .container {
    .tabs {
      width: 278 * @rem;
      margin: 0 auto;
      display: flex;
      position: relative;
      background-color: #fff;
      .tab {
        width: 278 * @rem;
        height: 20 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          white-space: nowrap;
          display: block;
          font-size: 16 * @rem;
          font-weight: 400;
          color: #191b1f;
          position: relative;
        }
        &.active {
          font-size: 16 * @rem;
          background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg.png)
            no-repeat center center;
          background-size: 48 * @rem 18 * @rem;
          &.text3_active {
            background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg1.png)
              no-repeat center center;
            background-size: 58 * @rem 18 * @rem;
          }
          &.text4_active {
            background: url(~@/assets/images/games/tabs-active-icon/tabs-active-bg2.png)
              no-repeat center center;
            background-size: 64 * @rem 18 * @rem;
          }
          span {
            font-size: 16 * @rem;
            text-align: center;
            font-weight: bold;
            color: #191b1f;
            &::before {
              content: '';
              display: block;
              width: 12 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/games/tabs-active-icon/tabs-active-dot.png)
                no-repeat;
              background-size: 12 * @rem 12 * @rem;
              position: absolute;
              top: -4 * @rem;
              right: -12 * @rem;
            }
          }
        }
      }
    }
    .content {
      margin-top: 12 * @rem;
    }
  }
}
</style>
