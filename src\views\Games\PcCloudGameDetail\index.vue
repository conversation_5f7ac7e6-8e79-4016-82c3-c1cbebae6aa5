<template>
  <div class="cloud-page">
    <div class="bg-color" v-if="detail.titleimg">
      <img :src="detail.titleimg" alt="" />
    </div>
    <nav-bar-3
      bgStyle="transparent-white"
      :placeholder="false"
      v-if="navBgTransparent && detail.titleimg"
    >
      <template #right>
        <div
          class="collect-btn collect-btn-white"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-3>
    <nav-bar-3
      :placeholder="false"
      v-else-if="navBgTransparent && !detail.titleimg"
    >
      <template #right>
        <div
          class="collect-btn"
          :class="{
            had: collected == 1,
          }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-3>
    <nav-bar-3
      :title="detail.main_title"
      :placeholder="false"
      :border="true"
      v-else
    >
      <template #right>
        <div
          class="collect-btn btn"
          :class="{ had: collected == 1 }"
          @click="setCollectStatus"
        ></div>
      </template>
    </nav-bar-3>
    <div class="bg-shadow-box" :class="{ 'bg-shadow': detail.titleimg }"> </div>
    <!-- 顶部信息 -->
    <header>
      <status-bar></status-bar>
      <div class="wrapper">
        <div class="top-container">
          <div class="left-img">
            <img v-show="detail.verticalpic" :src="detail.verticalpic" alt="" />
          </div>
          <div class="right-info">
            <div class="title-box">
              <span
                class="title"
                :style="{ color: detail.titleimg ? '#fff' : '#111111' }"
              >
                {{ detail.main_title }}</span
              >
            </div>
            <div
              class="tag-list"
              v-if="
                detail?.new_cate_list &&
                detail?.new_cate_list.length &&
                detail.detailid == 1
              "
            >
              <div class="tags">
                <div
                  class="tag"
                  :style="{
                    color: detail.titleimg ? '#fff' : '#868686',
                    background: detail.titleimg ? '' : 'rgba(0,0,0,0.05)',
                  }"
                  v-for="tag in detail?.new_cate_list"
                  :key="tag.id"
                >
                  {{ tag.title }}
                </div>
              </div>
            </div>
            <div
              class="share-box"
              :class="{ mar_top26: detail.detailid == 2 }"
              v-if="up_info && Object.keys(up_info).length"
            >
              <span
                class="text2"
                :style="{ color: detail.titleimg ? '#fff' : '#121212' }"
                >由</span
              >
              <span
                class="up-icon"
                @click="toPage('UpMine', { mem_id: up_info.user_id })"
              >
                <UserAvatar :src="up_info.avatar" :self="false" />
              </span>
              <span
                class="text3"
                :style="{ color: detail.titleimg ? '#fff' : '#121212' }"
                >分享</span
              >
            </div>
            <div
              class="jy_app"
              :style="{
                color: !detail.titleimg ? '#121212' : 'rgba(255,255,255,0.75);',
              }"
              v-if="detail.detailid == 2 && detail.cp_txt"
            >
              {{ detail.cp_txt }}
            </div>
          </div>
          <div class="score" v-if="Number(cmt_sum)">
            <div
              class="fraction"
              :style="{ color: detail.titleimg ? '#fff' : '#0AB965' }"
              >{{ detail.score }}</div
            >
            <div
              class="number"
              :style="{
                color: detail.titleimg ? 'rgba(255,255,255,0.75)' : '#868686',
              }"
              >{{ cmt_sum_text }}评价</div
            >
          </div>
        </div>
      </div>
    </header>
    <div class="h10-bg" v-if="!detail.titleimg"></div>
    <!-- <div class="h10-bg" v-if="!detail.titleimg"></div> -->
    <main>
      <!-- 详情、评价 -->
      <div class="detail-content">
        <!-- tabs -->
        <van-sticky :offset-top="stickyOffsetTop">
          <div
            class="tabs"
            :style="{
              'border-radius':
                detail.titleimg && navBgTransparent
                  ? `${20 * remNumberLess}rem ${20 * remNumberLess}rem 0 0`
                  : '0',
            }"
          >
            <div
              class="tab btn"
              :class="{ tab1: !detail.titleimg }"
              v-for="(tab, index) in tabList"
              :key="index"
              @click="clickTab(index)"
            >
              <span :class="{ active: current === index }"
                >{{ tab.title }}
              </span>
            </div>
            <div
              v-if="detail.titleimg"
              class="tab-line"
              :style="{ left: `${current * 187.5 * remNumberLess}rem` }"
            ></div>
            <div
              v-else
              class="tab-line tab-line1"
              :style="{ left: `${current * 93.75 * remNumberLess}rem` }"
            ></div>
          </div>
        </van-sticky>
        <content-empty v-if="noGame" :tips="$t('没有该游戏')"></content-empty>
        <template v-else>
          <div class="content-container">
            <div class="tab-content tab-detail" v-if="current == 0">
              <van-loading v-if="!loadSuccess" />
              <template v-else>
                <!-- 截图 -->
                <div
                  class="game-picture-swiper"
                  v-if="detail.morepic && detail.morepic.small.length"
                >
                  <swiper :options="swiperOption">
                    <!-- slides -->
                    <!-- <swiper-slide
                    class="swiper-slide video-container"
                    v-if="detail.video_url"
                  >
                    <video
                      ref="videoPlayer"
                      id="video"
                      :src="detail.video_url"
                      :poster="detail.video_thumb"
                      :controls="isPlaying"
                      x5-playsinline=""
                      :playsinline="true"
                      :webkit-playsinline="true"
                      :x-webkit-airplay="true"
                      x5-video-player-type="h5"
                      :x5-video-player-fullscreen="true"
                      x5-video-orientation="portraint"
                      muted
                    ></video>
                    <div class="mask" v-show="!isPlaying">
                      <div class="play-btn" @click="handlePlay"></div>
                    </div>
                  </swiper-slide> -->
                    <swiper-slide
                      v-for="(item, index) in detail.morepic
                        ? detail.morepic.small
                        : []"
                      :key="index"
                      class="swiper-slide"
                      :class="{ is_hp: isHp }"
                    >
                      <div v-if="!item" class="default_loading">
                        <div class="loading">
                          <img
                            src="~@/assets/images/games/default_loading_icon.png"
                            alt=""
                          />
                        </div>
                        <div class="text">正在加载...</div>
                      </div>
                      <img
                        v-else
                        :src="item"
                        class="slide-img btn"
                        @click="showBigImage(detail.morepic.big, index)"
                      />
                      <i class="player" v-if="index === 1"></i>
                    </swiper-slide>
                  </swiper>
                </div>
                <div
                  class="category-tags"
                  v-if="detail?.other_cate && detail?.other_cate.length"
                >
                  <div
                    class="category-tag-item"
                    v-for="tag in detail?.other_cate"
                    :key="tag.id"
                  >
                    {{ tag.title }}
                  </div>
                </div>
                <!-- 游戏介绍 -->
                <div class="game-introduction-text" v-if="detail.newstext">
                  <div class="introduction-text" v-html="detail.newstext">
                  </div>
                  <div
                    class="introduction-icon"
                    v-if="!isExpanded"
                    @click="toggleIntroduction"
                  >
                    <img
                      src="@/assets/images/games/introduction-icon.png"
                      alt=""
                    />
                  </div>
                </div>
                <!-- 温馨提示 -->
                <div class="reminder-text" v-if="detail.characteristic">
                  <div class="title">温馨提示</div>
                  <div class="content">
                    <div
                      class="content-text"
                      ref="content1"
                      :class="{ on: !isAll1 }"
                      v-html="detail.characteristic"
                    ></div>
                    <div
                      class="more-text"
                      @click="moreText"
                      v-if="contentHeight1 > 100 && !isAll1"
                    >
                      <div> {{ !isAll1 ? '&nbsp;...' : '' }}</div>
                      <span>{{ !isAll1 ? $t('展开') : '' }}</span>
                    </div>
                  </div>
                </div>
                <!-- 活动公告 -->
                <div class="event-announcement" v-if="notice_list.length">
                  <div class="header_title">
                    <div class="title">活动与公告</div>
                    <div class="icon">
                      <img
                        src="~@/assets/images/cloud-game/right-icon.png"
                        alt=""
                      />
                    </div>
                  </div>

                  <div class="notice-list">
                    <div
                      v-for="(item, index) in notice_list"
                      :key="index"
                      class="notice-item"
                    >
                      <!-- <span
                      :style="{ color: item.tag_color }"
                      class="notice-type"
                      >{{ item.tag_name }}</span
                    >| -->
                      <span class="advertisement-icon">
                        <img
                          src="~@/assets/images/cloud-game/advertisement-icon.png"
                          alt=""
                        />
                      </span>
                      <span @click="toNoticeDetail(item)" class="notice-title"
                        >{{ item.infomation_title }}
                        <!-- <i class="notice-icon"></i> -->
                      </span>
                    </div>
                  </div>
                </div>

                <div class="h10-bg mat-8" v-if="hotCommentList.length"> </div>
                <!-- 游戏评价 -->
                <div class="game-review-content" v-if="hotCommentList.length">
                  <div class="title-info">
                    <div class="left-box">
                      <span class="title">游戏评价</span>
                      <span class="num" v-if="cmt_sum_text"
                        >({{ cmt_sum_text }})</span
                      >
                    </div>
                    <div class="right-box" @click="clickComment">
                      <span class="review-logo"
                        ><img
                          src="~@/assets/images/games/review-logo.png"
                          alt=""
                      /></span>
                      <div class="to-review">去评价</div>
                    </div>
                  </div>
                  <div class="content-info">
                    <div class="comment-list">
                      <comment-item-2
                        class="item"
                        :comment="item"
                        :showScoreTime="true"
                        v-for="(item, index) in hotCommentList.slice(0, 4)"
                        :key="index"
                      ></comment-item-2>
                    </div>
                    <div
                      class="see-more-game-info"
                      v-if="Number(cmt_sum) > 4"
                      @click="viewAllGameReviews"
                    >
                      <div>查看全部游戏评价</div>
                      <span> </span>
                    </div>
                  </div>
                </div>
                <div
                  class="h10-bg mat-8"
                  v-if="detail?.otherinfo?.info_exterior.length"
                >
                </div>
                <!-- 详细信息 -->
                <div
                  class="detailed-info"
                  v-if="detail?.otherinfo?.info_exterior.length"
                >
                  <div class="title">详细信息</div>
                  <div class="content">
                    <template v-for="item in detail?.otherinfo?.info_exterior">
                      <div
                        class="content-item"
                        :key="item.key"
                        v-if="item.val.length > 0 || item.key == 'privacy'"
                      >
                        <div class="label">{{ item.title }}</div>

                        <div class="value-box" v-if="item.key !== 'privacy'">
                          <div class="value1">{{ item.val }}</div>
                        </div>
                        <div class="value-box" v-else>
                          <div
                            class="value2"
                            @click="handleApplicationPermissions"
                            >应用权限</div
                          >
                          <div class="value2" @click="handlePrivacyPermission"
                            >隐私政策</div
                          >
                        </div>
                      </div>
                    </template>

                    <div class="see-more-info" @click="handlePermission">
                      <div>查看全部信息</div>
                      <span> </span>
                    </div>
                  </div>
                  <div class="tips" v-if="up_info?.up">{{
                    up_info?.up?.desc
                  }}</div>
                </div>
                <div class="h10-bg mat-8" v-if="formatRelatedList.length">
                </div>
                <!-- 更多游戏推荐 -->
                <div class="related" v-if="formatRelatedList.length">
                  <div class="section-title">
                    <div class="title-text">更多游戏推荐</div>
                  </div>
                  <div class="game-list">
                    <div
                      class="game-group"
                      v-for="(related, relatedIndex) in formatRelatedList"
                      :key="relatedIndex"
                    >
                      <div
                        class="game-item"
                        v-for="(item, index) in related"
                        :key="index"
                      >
                        <!-- <emulator-game :gameInfo="item"></emulator-game> -->
                        <!-- <simulator-zone-tabs-item
                        :info="item"
                      ></simulator-zone-tabs-item> -->
                        <game-item-4
                          :gameInfo="item"
                          :iconSize="72"
                        ></game-item-4>
                      </div>
                    </div>
                  </div>
                  <div class="h28-bg"> </div>
                </div>
              </template>
            </div>
            <template v-if="current == 1">
              <evaluate-news :game_id="detail.id"></evaluate-news>
            </template>
          </div>
        </template>
      </div>

      <div class="fixed-review-box" v-if="current == 1 && !noGame">
        <div class="fixed-review-btn" @click="clickComment()">
          <img src="~@/assets/images/games/fixed-review-btn.png" alt="" />
        </div>
      </div>
    </main>

    <!-- 底部fixed -->
    <div class="bottom-container" v-if="loadSuccess">
      <div class="bottom-fixed">
        <!-- <div class="comment btn" @click="clickComment">
          <div class="comment-icon"></div>
          <div class="comment-title">评价</div>
        </div> -->
        <div class="download-bar">
          <div class="download-content">
            <div
              v-if="detail.detailid == 1"
              class="btn download-btn"
              @click="cloudPlayInit(detail, detail.id)"
            >
              <div
                class="text"
                :class="{ loading: pcCloudGameInitLoading[detail.id] }"
                >云玩</div
              >
            </div>
            <div v-else class="btn download-btn" @click="downJymyBtn(detail)">
              <div
                class="text"
                :class="{ loading: pcCloudGameInitLoading[detail.id] }"
                >下载</div
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 详细信息弹窗 -->
    <van-popup
      v-model="detailedInfoShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="detailedInfo-popup">
        <div class="permission-top">
          <div class="title">详细信息</div>
          <div class="close" @click="detailedInfoShow = false"></div>
        </div>

        <div class="permission-list">
          <template v-for="item in detail?.otherinfo?.info_internal">
            <div
              class="permission-item"
              :key="item.key"
              v-if="
                (item.key === 'icp_number' && detail.record_number) ||
                (item.key === 'source' && detail.company_name) ||
                item.val.length > 0 ||
                item.key == 'privacy'
              "
            >
              <div class="name">{{ item.title }}</div>
              <div class="value-box" v-if="item.key !== 'privacy'">
                <div class="value1" v-if="item.val">{{ item.val }}</div>
                <div class="value1" v-if="item.key === 'icp_number'">{{
                  detail.record_number
                }}</div>
                <div class="value1" v-if="item.key === 'source'">{{
                  detail.company_name
                }}</div>
              </div>
              <div class="value-box" v-else>
                <div class="value2" @click="handleApplicationPermissions"
                  >应用权限</div
                >
                <div class="value2" @click="handlePrivacyPermission"
                  >隐私权限</div
                >
              </div>
            </div>
          </template>
        </div>
      </div>
    </van-popup>
    <!-- 访问权限弹窗 -->
    <van-popup
      v-model="permissionShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="permission-popup">
        <div class="title">{{ $t('该应用需要访问以下权限') }}</div>
        <div class="permission-list">
          <div
            class="permission-item"
            v-for="(item, index) in permissionList"
            :key="index"
          >
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- pc云游戏任务弹窗 -->
    <taskListPopup :show.sync="_taskListPopupShow"></taskListPopup>

    <DurationOverPopup :show.sync="_durationOverShow"> </DurationOverPopup>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { ImagePreview } from 'vant';
import {
  ApiCloudGameDetail,
  ApiGameGetPermissionInfo,
  ApiResourceCollect,
  ApiResourceCollectStatus,
} from '@/api/views/game.js';
import { jumpAnnouncement, downJymyBtnCallback } from '@/utils/function';
import { ApiCommentClickComment } from '@/api/views/comment.js';
import TextOverflow from '@/components/text-overflow';
import EmulatorGame from './components/emulator-game/index.vue';
import EvaluateNews from '../EmulatorGameDetail/components/evaluate-news/index.vue';
import CommentItem2 from '@/components/comment-item-2';
import SimulatorZoneTabsItem from '../../SimulatorZone/components/simulator-zone-tabs-item/index.vue';
import { mapActions, mapMutations, mapGetters } from 'vuex';
import DurationOverPopup from '@/components/duration-over-popup';
import taskListPopup from '@/components/task-list-popup';
import { BOX_openInBrowser, platform } from '@/utils/box.uni.js';
export default {
  name: 'PcCloudGameDetail',
  components: {
    TextOverflow,
    EmulatorGame,
    EvaluateNews,
    CommentItem2,
    SimulatorZoneTabsItem,
    DurationOverPopup,
    taskListPopup,
  },
  data() {
    return {
      navbarOpacity: 0,
      stickyOffsetTop: '0px', //顶部导航栏的高度
      navBgTransparent: true,
      collected: 0, //是否已收藏
      detail: {}, // 游戏详情信息
      up_info: {}, // up详情信息
      current: 0, //tabs选中
      remNumberLess, //rem大小转换
      tabList: [
        {
          title: '详情',
          status: 0,
        },
        {
          title: '评价',
          status: 1,
        },
      ],
      loadSuccess: false, //加载完毕
      permissionList: [],
      detailedInfoShow: false, //游戏获取权限列表展示
      permissionShow: false,
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
      },
      isPlaying: false, //是否正在播放
      isExpanded: false,
      relatedList: [], // 相关游戏
      notice_list: [], // 公告列表
      hotCommentList: [], // 热门评论
      cmt_sum: '', // 实际评论数量
      cmt_sum_text: '', // 评论数量
      noGame: false, // 是否没有该游戏
      rows: 3,
      btnText: '展开',
      ellipsisText: '...',
      ellipsis: true,
      isAll1: true, // 展开 -> 是否显示全部
      contentHeight1: 0, //展开收起内容高度1
      isHp: false, // 截图是否是横屏
    };
  },
  async created() {
    this.detail = this.$route.params.id || {};
  },
  async mounted() {
    this.setPcCloudGameInitLoadingEmpty({});
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.detailedInfoShow = false;
    this.setDurationOverShow(false);
    await this.getDetail();
    if (!this.noGame) {
      await this.getCollectStatus();
    }
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    toNoticeDetail(item) {
      jumpAnnouncement(item);
    },
    getImageSize(filePath) {
      return new Promise((resolve, reject) => {
        try {
          let image = new Image();
          image.onload = function () {
            resolve({ width: this.width, height: this.height });
            this.removeAttribute('src');
            image = null;
          };
          image.onerror = error => {
            resolve({ width: this.width, height: this.height });
            image = null;
          };
          image.src = `${filePath}`;
        } catch (error) {
          resolve({ width: this.width, height: this.height });
        }
      });
    },
    // 更多
    hideMore(ellipsis) {
      this.ellipsis = ellipsis;
    },
    toggleIntroduction() {
      this.isExpanded = !this.isExpanded;
      const gameIntroductionText = document.querySelector(
        '.game-introduction-text',
      );
      if (this.isExpanded) {
        gameIntroductionText.classList.add('expanded');
      } else {
        gameIntroductionText.classList.remove('expanded');
      }
    },
    // 获取收藏状态
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 3,
        sourceId: this.detail.id,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    // 设置收藏
    setCollectStatus() {
      if (this.noGame) return;
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;
      }
      ApiResourceCollect({
        classId: 3,
        sourceId: this.detail.id,
        status: status,
      }).then(res => {
        this.collected = res.data.status;
      });
    },
    // 获取详情页信息
    async getDetail() {
      try {
        const res = await ApiCloudGameDetail({
          id: this.$route.params.id,
        });
        this.noGame = false;

        const { width, height } = await this.getImageSize(
          res.data.detail.morepic.big[0],
        );
        // 截图是否横屏
        this.isHp =
          width === undefined || height === undefined || width >= height;
        this.detail = res.data.detail;
        this.cmt_sum = res.data.cmt_sum;
        this.cmt_sum_text = res.data.cmt_sum_text;
        this.hotCommentList = res.data.comment;
        this.relatedList = res.data.likeds;
        // this.notice_list = res.data.tag_information;
        // console.log(res.data.likeds);
        this.up_info = res.data.detail?.up_info;
        this.loadSuccess = true;
        // 如果有视频就播放 ===> 暂时改成一进来不自动播放
        // if (this.detail.video_url) {
        //   this.$nextTick(() => {
        //     this.handlePause();
        //   });
        // }
      } catch (e) {
        if (e.code == 0) {
          this.$router.replace({
            name: 'GameNotFoundPage',
            params: { name: this.$route.name, params: this.$route.params },
          });
          this.noGame = true;
        }
      } finally {
        this.$nextTick(() => {
          this.$toast.clear();
          if (!this.contentHeight1 && this.$refs.content1) {
            this.contentHeight1 = this.$refs.content1.clientHeight;
          }
          if (this.contentHeight1 > 100) {
            this.isAll1 = false;
          }
        });
      }
    },
    clickTab(index) {
      window.scrollTo(0, 0);
      if (!this.loadSuccess) return false;
      this.current = index;
    },
    // 处理tab底下高亮标记位移
    handleLeftDistance(current) {
      let width = document.body.clientWidth;
      let length = this.tabList.length;
      let left = width / length / 2 - 6;
      let distance = current * (width / length);
      return `${left + distance}px`;
    },
    moreText() {
      this.isAll1 = true;
    },
    // 点击评论时先判断有没有评论的权限
    async clickComment() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      if (![1, 2].includes(parseInt(this.userInfo.auth_status))) {
        this.$toast.clear();
        this.$dialog({
          title: '提示',
          message: '评论失败：您还未完善实名认证信息！',
          confirmButtonText: '我知道了',
        });
        return;
      }
      try {
        const res = await ApiCommentClickComment({
          classId: 103,
          sourceId: this.detail.id,
        });
        if (res.data && res.data.length) {
          this.setCommentTypeList(res.data);
        }
        this.$toast.clear();
        this.toPage('CommentEditor', {
          source_id: this.detail.id,
          class_id: 103,
        });
      } catch (e) {
        if (e.code == 0) {
          // 不能评论，弹窗提示
          this.$toast.clear();
          this.$dialog({
            title: '提示',
            message: e.msg,
            confirmButtonText: '我知道了',
          });
        }
      }
    },
    handlePlay() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
    handlePause() {
      this.$refs.videoPlayer.pause();
      this.isPlaying = false;
    },
    // 查看全部评价
    viewAllGameReviews() {
      window.scrollTo(0, 0);
      this.current = 1;
    },
    // 处理隐私权限
    handlePrivacyPermission() {
      if (this.detail.privacy_url) {
        this.toPage('Iframe', {
          url: this.detail.privacy_url,
          title: '隐私政策',
        });
      } else {
        this.$toast('开发者正在努力完善中...');
      }
    },
    // 处理应用权限
    async handleApplicationPermissions() {
      const res = await ApiGameGetPermissionInfo({
        id: this.detail.id,
      });
      if (res.data.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.permissionList = res.data;
      this.permissionShow = true;
    },
    // 处理游戏权限详情
    async handlePermission() {
      if (this.detail.otherinfo.info_internal.length === 0) {
        this.$toast(this.$t('该游戏暂无权限详情'));
        return false;
      }
      this.detailedInfoShow = true;
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
      // let scrollTop =
      //   document.documentElement.scrollTop ||
      //   window.pageYOffset ||
      //   document.body.scrollTop;
      // if (Math.abs(scrollTop) > 10) {
      //   this.navbarOpacity = Math.abs(scrollTop) / 120;
      // } else {
      //   this.navbarOpacity = 0;
      // }
    },
    // 云玩
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    ...mapActions({
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
    }),
    ...mapMutations({
      setPcCloudGameInitLoadingEmpty: 'game/setPcCloudGameInitLoadingEmpty',
      setDurationOverShow: 'game/setDurationOverShow',
      setTaskListPopupShow: 'game/setTaskListPopupShow',
      setCommentTypeList: 'comment/setCommentTypeList',
    }),
  },
  computed: {
    // 相关游戏
    formatRelatedList() {
      let arr = [];
      for (let i = 0; i < this.relatedList.length; i += 3) {
        arr.push(this.relatedList.slice(i, i + 3));
      }
      return arr;
    },
    ...mapGetters({
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
      durationOverShow: 'game/durationOverShow',
      taskListPopupShow: 'game/taskListPopupShow',
      hereItIosBoxTaskShow: 'game/hereItIosBoxTaskShow',
    }),
    _taskListPopupShow: {
      //是否显示任务弹窗
      get() {
        return this.taskListPopupShow;
      },
      set(value) {
        this.setTaskListPopupShow(value);
      },
    },
    _durationOverShow: {
      //是否显示时长不足弹窗
      get() {
        return this.durationOverShow;
      },
      set(value) {
        this.setDurationOverShow(value);
      },
    },
  },
  watch: {
    hereItIosBoxTaskShow(val) {
      if (val) {
        this.setTaskListPopupShow(true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.van-sticky--fixed {
  left: 0;
  right: 0;
  margin: 0 auto;
  max-width: 450px;
  width: 100%;
}
.cloud-page {
  // background: #ffffff;
  // padding-top: @safeAreaTop;
  // padding-top: @safeAreaTopEnv;
  height: 100vh;
  position: relative;
  .collect-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    padding: 10 * @rem;
    background: url(~@/assets/images/games/collect-black.png) center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    &.collect-btn-white {
      background: url(~@/assets/images/games/collect.png) center center
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
    }
    &.had {
      background-image: url(~@/assets/images/games/collect-success.png);
      background-size: 28 * @rem 28 * @rem;
    }
  }
  .bg-color {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -2;
    width: 100%;
    height: 238 * @rem;
  }
  .bg-shadow-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 245 * @rem;
    z-index: -1;
    &.bg-shadow {
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0.55) 56%,
        rgba(0, 0, 0, 0.8) 100%
      );
    }
  }
  header {
    overflow: hidden;
    position: relative;
    z-index: 2;
    .wrapper {
      position: relative;
      margin-top: 50 * @rem;
      overflow: hidden;
      height: auto;
      max-height: 155 * @rem;
      .bg {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
        width: 100 * @rem;
        height: 51 * @rem;
        .image-bg('~@/assets/images/up/up_bg1.png');
      }
      .top-container {
        height: auto;
        min-height: 115 * @rem;
        padding: 17 * @rem 18 * @rem 12 * @rem 18 * @rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .left-img {
          width: 66 * @rem;
          height: 85 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
          background: #eeeeee;
          flex-shrink: 0;
          min-width: 0;
        }
        .right-info {
          height: 85 * @rem;
          margin-left: 12 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .title-box {
            width: 232 * @rem;
            height: 20 * @rem;
            display: flex;
            align-items: center;
            .title {
              width: 215 * @rem;
              height: 20 * @rem;
              font-weight: 600;
              font-size: 16 * @rem;
              color: #ffffff;
              line-height: 20 * @rem;
              text-align: left;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-title {
              margin-left: 16 * @rem;
              width: 61 * @rem;
              white-space: nowrap;
              border-radius: 4 * @rem;
              border: 1px solid rgba(0, 0, 0, 0.23);
              font-weight: 400;
              font-size: 10 * @rem;
              color: #979797;
              padding: 2 * @rem 4 * @rem;
            }
          }
          .tag-list {
            display: flex;
            align-items: center;

            .tags {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              overflow: hidden;
              margin-top: 10 * @rem;
              height: 17 * @rem;
              line-height: 17 * @rem;
              .tag {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                white-space: nowrap;
                border-radius: 4 * @rem;
                padding: 2 * @rem 4 * @rem;
                font-weight: 400;
                font-size: 10 * @rem;
                color: rgba(255, 255, 255, 0.8);
                height: 17 * @rem;
                line-height: 17 * @rem;
                border: 1 * @rem solid rgba(255, 255, 255, 0.5);
                box-sizing: border-box;
                &:not(:first-child) {
                  margin-left: 8 * @rem;
                }
              }
            }
          }
          .desc-box {
            padding: 12 * @rem 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 239 * @rem;
            height: 15 * @rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #9a9a9a;
            line-height: 15 * @rem;
          }
          .share-box {
            margin-top: 20 * @rem;
            display: flex;
            align-items: center;
            .text2,
            .text3 {
              font-weight: 400;
              font-size: 12 * @rem;
              color: rgba(255, 255, 255, 0.75);
            }
            .up-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              border-radius: 50%;
              padding: 0 5 * @rem;
              overflow: hidden;
            }
            &.mar_top26 {
              margin-top: 26 * @rem;
            }
          }
          .jy_app {
            margin-top: 6 * @rem;
            width: 200 * @rem;
            height: 17 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: rgba(255, 255, 255, 0.75);
            line-height: 14 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .score {
          position: absolute;
          right: 18 * @rem;
          bottom: 20 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          .fraction {
            height: 35 * @rem;
            font-weight: bold;
            font-size: 28 * @rem;
            color: #ffffff;
            line-height: 35 * @rem;
          }
          .number {
            margin-top: 2 * @rem;
            height: 13 * @rem;
            font-weight: 400;
            font-size: 10 * @rem;
            color: rgba(255, 255, 255, 0.75);
            line-height: 13 * @rem;
          }
        }
      }
    }
  }
  .h10-bg {
    min-height: 10 * @rem;
    height: 10 * @rem;
    width: 100%;
    background: #f9f9f9;
  }
  .h28-bg {
    min-height: 28 * @rem;
    height: 28 * @rem;
    width: 100%;
    background: #f9f9f9;
  }
  .mat-8 {
    margin-top: 8 * @rem;
  }
  main {
    border-radius: 20 * @rem 20 * @rem 0 0;
    background: #fff;
    .detail-content {
      // padding: 20 * @rem 0;
      .tabs {
        display: flex;
        position: relative;
        background-color: #fff;
        align-items: center;
        // border-radius: 20 * @rem 20 * @rem 0 0;
        padding: 0 0 8 * @rem 0;
        width: 100%;
        .tab {
          width: 187.5 * @rem;
          height: 40 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          &.tab1 {
            width: 93.75 * @rem;
          }
          span {
            display: block;
            font-size: 15 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 400;
            color: #999999;
            position: relative;
          }
          .notice {
            position: absolute;
            left: 50%;
            top: 2 * @rem;
            transform: translateX(12 * @rem);
            height: 14 * @rem;
            line-height: 14 * @rem;
            background: #ff504f;
            color: #ffffff;
            font-size: 10 * @rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4 * @rem;
            border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
          }
        }
        .line {
          flex: 1;
          height: 0 * @rem;
          border: 1 * @rem solid #e8e8e8;
        }
        .tab-line {
          position: absolute;
          width: 10 * @rem;
          height: 4 * @rem;
          border-radius: 10 * @rem;
          background-color: @themeColor;
          left: 0;
          bottom: 10 * @rem;
          transform: translateX(87 * @rem);
          transition: 0.3s;
          &.tab-line1 {
            transform: translateX(39.875 * @rem);
          }
        }
        .active {
          font-size: 16 * @rem!important;
          font-weight: 600 !important;
          color: #111111 !important;
        }
      }
      .content-container {
        .tab-content {
          /deep/ .van-loading {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          &.tab-detail {
            .game-picture-swiper {
              box-sizing: border-box;
              width: 100%;
              overflow: hidden;
              .swiper-container {
                box-sizing: border-box;
                padding: 0 18 * @rem;
                width: 100%;
                .swiper-slide {
                  width: auto;
                  height: 219 * @rem;
                  margin-left: 8 * @rem;
                  border-radius: 8 * @rem;
                  overflow: hidden;
                  min-width: 124 * @rem;
                  &:first-child {
                    margin-left: 0;
                  }
                  .slide-img {
                    width: auto;
                    height: 100%;
                    border-radius: 8 * @rem;
                    overflow: hidden;
                    background: #ccc;
                  }
                  &.video-container {
                    width: 267 * @rem;
                    height: 219 * @rem;
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    #video {
                      display: block;
                      width: 267 * @rem;
                      height: 219 * @rem;
                      outline: none;
                      border: none;
                    }
                    .mask {
                      width: 100%;
                      height: 219 * @rem;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      position: absolute;
                      left: 0;
                      top: 0;
                      .fixed-center;
                      background-color: rgba(0, 0, 0, 0);
                      z-index: 10;
                      .play-btn {
                        width: 38 * @rem;
                        height: 38 * @rem;
                        background: url(~@/assets/images/video-play.png)
                          no-repeat;
                        background-size: 38 * @rem 38 * @rem;
                      }
                    }
                  }
                  &.is_hp {
                    height: 160 * @rem;
                  }
                  .default_loading {
                    background: #4f4f4f;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    .loading {
                      width: 28 * @rem;
                      height: 28 * @rem;
                      animation: loading_rotate 0.8s linear infinite;
                    }
                    .text {
                      margin-top: 24 * @rem;
                      font-weight: 400;
                      font-size: 11 * @rem;
                      color: #ffffff;
                    }
                  }
                }
              }
            }
          }
          .category-tags {
            display: flex;
            align-items: center;
            overflow-y: auto;
            padding: 10 * @rem 18 * @rem 0;
            .category-tag-item {
              white-space: nowrap;
              height: 23 * @rem;
              margin-right: 10 * @rem;
              font-size: 11 * @rem;
              color: #777777;
              font-weight: 400;
              padding: 0 7 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4 * @rem;
              border: 1px solid #e0e0e0;
            }
          }
          .game-introduction-text {
            display: flex;
            align-items: center;
            margin: 10 * @rem 18 * @rem 8 * @rem 18 * @rem;
            .introduction-text {
              width: 322 * @rem;
              color: #666666;
              overflow: hidden;
              font-weight: 400;
              // white-space: pre-wrap;
              // transition: all 0.3s ease;
              flex: 1;
              font-size: 14px;
              height: 20px;
              line-height: 24px;
              // display: -webkit-box;
              max-height: unset;
              transition: 0.3s;
            }
            .introduction-icon {
              margin-top: 4 * @rem;
              margin-left: 9 * @rem;
              width: 12 * @rem;
              height: 11 * @rem;
            }
            &.expanded .introduction-text {
              overflow: visible;
              height: auto;
            }

            &.expanded .introduction-icon {
              opacity: 0;
            }
          }
          .reminder-text {
            margin: 20 * @rem 18 * @rem 0 16 * @rem;
            .title {
              white-space: nowrap;
              font-weight: 500;
              font-size: 15 * @rem;
              color: #222222;
              font-weight: 600;
              flex: 1;
              min-width: 0;
            }
            .content {
              margin-top: 12 * @rem;
              background: #fafafa;
              border-radius: 8 * @rem;
              padding: 14 * @rem 10 * @rem;
              width: 100%;
              box-sizing: border-box;
              position: relative;
              .content-text {
                font-size: 14px;
                color: #9a9a9a;
                line-height: 20px;
                display: -webkit-box;
                max-height: unset;
                height: auto;
                transition: 0.3s;

                &.on {
                  max-height: 100px;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 5;
                  overflow: hidden;
                }
              }
              .more-text {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 42 * @rem;
                position: absolute;
                right: 10 * @rem;
                bottom: 17 * @rem;
                background: #fff;
                span {
                  font-size: 12 * @rem;
                  color: #32b768;
                  height: 17 * @rem;
                  line-height: 17 * @rem;
                }
              }
              .ellipsis-content {
                font-weight: 400;
                font-size: 13px;
                color: #9a9a9a;
                position: relative;
                line-height: 1.5;
                text-align: justify;
                word-spacing: 2px;
                .btn {
                  color: #32b768;
                }
              }
            }
          }
          .event-announcement {
            display: flex;
            flex-direction: column;
            padding: 20 * @rem 18 * @rem 12 * @rem 18 * @rem;
            .header_title {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .title {
                height: 19 * @rem;
                line-height: 19 * @rem;
                white-space: nowrap;
                font-weight: 500;
                font-size: 15 * @rem;
                color: #222222;
                font-weight: 600;
                flex: 1;
                min-width: 0;
              }
              .icon {
                width: 8 * @rem;
                height: 12 * @rem;
              }
            }
            .notice-list {
              margin-top: 12 * @rem;
              background: #f9f9f9;
              border-radius: 6 * @rem;

              .notice-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 10 * @rem;
                height: 40 * @rem;
                .notice-type {
                  margin-right: 10 * @rem;
                  font-size: 13 * @rem;
                  font-weight: 600;
                }
                .advertisement-icon {
                  width: 27 * @rem;
                  height: 25 * @rem;
                }
                .notice-title {
                  display: flex;
                  justify-content: space-between;
                  flex: 1;
                  margin-left: 10 * @rem;
                  color: #666;
                  .notice-icon {
                    display: block;
                    width: 14 * @rem;
                    height: 14 * @rem;
                    .image-bg('~@/assets/images/more-right-arrow.png');
                  }
                }
              }
            }
          }

          .game-review-content {
            padding: 8 * @rem 18 * @rem;
            .title-info {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .left-box {
                .title,
                .num {
                  font-weight: 600;
                  font-size: 16 * @rem;
                  color: #222222;
                }
                .num {
                  margin-left: 6 * @rem;
                  font-weight: 600;
                  font-size: 14 * @rem;
                  color: #979797;
                }
              }
              .right-box {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 28 * @rem;
                border-radius: 26 * @rem;
                border: 1px solid #e0e0e0;
                width: 68 * @rem;
                white-space: nowrap;
                .review-logo {
                  background-size: 12 * @rem 12 * @rem;
                  width: 12 * @rem;
                  height: 12 * @rem;
                }
                .to-review {
                  margin-left: 4 * @rem;
                  width: 36 * @rem;
                  white-space: nowrap;
                  font-weight: 500;
                  font-size: 12 * @rem;
                  color: #222222;
                }
              }
            }
            .content-info {
              display: flex;
              flex-direction: column;
              align-items: center;
              .comment-list {
                .item {
                  margin-top: 20 * @rem;
                  &:not(:first-child) {
                    margin-top: 12 * @rem;
                  }
                  &:not(:last-child) {
                    padding-bottom: 14 * @rem;
                    border-bottom: 1px solid #f9f9f9;
                  }
                }
                width: 100%;
                margin-bottom: 32 * @rem;
              }
              .see-more-game-info {
                display: flex;
                align-items: center;
                > div {
                  font-weight: 600;
                  font-size: 14 * @rem;
                  color: #333333;
                }
                > span {
                  width: 8 * @rem;
                  height: 12 * @rem;
                  margin-left: 4 * @rem;
                  background: url(~@/assets/images/games/arrow-right.png) right
                    center no-repeat;
                  background-size: 6 * @rem 10 * @rem;
                }
              }
            }
          }

          .detailed-info {
            display: flex;
            flex-direction: column;
            margin: 16 * @rem 18 * @rem;
            .title {
              font-weight: 600;
              font-size: 16 * @rem;
              color: #111111;
            }
            .content {
              margin: 12 * @rem 0 0 0;
              width: 339 * @rem;
              background: #fafafa;
              border-radius: 8 * @rem;
              box-sizing: border-box;
              padding: 15 * @rem 10 * @rem 14 * @rem;
              display: flex;
              flex-direction: column;
              align-items: center;
              .content-item {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .label {
                  font-weight: 400;
                  font-size: 14 * @rem;
                  color: #777777;
                  white-space: nowrap;
                }
                .value-box {
                  width: 215 * @rem;
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                  .value1 {
                    font-weight: 400;
                    font-size: 14 * @rem;
                    color: #333333;
                  }
                  .value2 {
                    font-weight: 400;
                    font-size: 14 * @rem;
                    color: #32b768;
                    line-height: 14 * @rem;
                    // text-decoration: underline;
                    display: inline;
                    border-bottom: solid 1px #32b768;
                    &:not(:first-child) {
                      margin-left: 9 * @rem;
                    }
                  }
                }
                &:not(:first-child) {
                  margin-top: 13 * @rem;
                }
              }
              .see-more-info {
                display: flex;
                align-items: center;
                margin-top: 16 * @rem;
                > div {
                  font-weight: 600;
                  font-size: 14 * @rem;
                  color: #333333;
                }
                > span {
                  width: 8 * @rem;
                  height: 12 * @rem;
                  margin-left: 4 * @rem;
                  background: url(~@/assets/images/games/arrow-right.png) right
                    center no-repeat;
                  background-size: 6 * @rem 10 * @rem;
                }
              }
            }
            .tips {
              font-weight: 400;
              font-size: 12 * @rem;
              color: #9a99a9;
              line-height: 19 * @rem;
            }
          }
          .related {
            margin-top: 16 * @rem;
            padding-right: 0;
            padding-left: 0;
            .section-title {
              padding-left: 18 * @rem;
              .title-text {
                font-weight: 600;
                font-size: 16 * @rem;
                color: #111111;
              }
            }
            .game-list {
              box-sizing: border-box;
              display: flex;
              justify-content: space-between;
              // margin-top: 15 * @rem;
              width: 100%;
              overflow-x: auto;
              padding-left: 18 * @rem;
              &::-webkit-scrollbar {
                display: none !important;
              }
              .game-group {
                &:not(:first-of-type) {
                  margin-left: 10 * @rem;
                }
                &:last-of-type {
                  padding-right: 18 * @rem;
                }
              }
              .game-item {
                box-sizing: border-box;
                width: 275 * @rem;
                // height: 98 * @rem;
                // padding: 0 14 * @rem;
                border-radius: 12 * @rem;
                display: flex;
                align-items: center;
                // &:not(:first-of-type) {
                //   margin-top: 8 * @rem;
                // }
                &:not(:first-child) {
                  margin-bottom: 20 * @rem;
                }
                &:first-child {
                  margin-top: 14 * @rem;
                  margin-bottom: 20 * @rem;
                }
                /deep/ .game-item-components {
                  padding: 0;
                }
                /deep/ .game-item-components .tags .tag {
                  background-color: #f7f7f7;
                }
              }
            }
          }
        }
      }
    }
  }

  .content2 {
    width: 200px;
    line-height: 20px;
    /* 设置为行高的整倍数，此处显示两行: 2 * 20px */
    max-height: 40px;
  }
  .fixed-review-box {
    position: fixed;
    right: 14 * @rem;
    bottom: 0;
    z-index: 99;
    padding-bottom: calc(95 * 0.0267rem + env(safe-area-inset-bottom));
    .fixed-review-btn {
      background-size: 58 * @rem 56 * @rem;
      width: 58 * @rem;
      height: 56 * @rem;
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .download-bar {
        flex: 1;
        min-width: 0;
        height: 60 * @rem;
        .download-content {
          height: 60 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          .download-btn {
            flex: 1;
            height: 44 * @rem;
            background: @themeBg;
            // margin: 0 7 * @rem;
            text-align: center;
            line-height: 44 * @rem;
            color: #fefefe;
            font-size: 16 * @rem;
            font-weight: 500;
            border-radius: 8 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 29 * @rem;
            .icon {
              background-origin: -1 * @rem -1 * @rem;
              background-size: 22 * @rem 24 * @rem;
              width: 21 * @rem;
              height: 23 * @rem;
            }
            .text {
              margin-left: 4 * @rem;
              font-weight: 600;
              font-size: 16 * @rem;
              color: #ffffff;
              &.loading {
                position: relative;
                font-size: 0;
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  display: block;
                  width: 16 * @rem;
                  height: 16 * @rem;
                  background-size: 16 * @rem 16 * @rem;
                  background-image: url(~@/assets/images/downloadLoading.png);
                  animation: rotate 1s infinite linear;
                }
              }
            }
          }
        }
      }
      .comment {
        margin-right: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .comment-icon {
          width: 23 * @rem;
          height: 22 * @rem;
          background-size: 24 * @rem 24 * @rem;
          .image-bg('~@/assets/images/games/comment-icon-emn.png');
        }
        .comment-title {
          font-size: 12 * @rem;
          color: #222222;
          font-weight: 500;
          margin-top: 3 * @rem;
        }
      }
    }
  }
  .detailedInfo-popup {
    max-height: 400 * @rem;
    // min-height: 200 * @rem;
    display: flex;
    flex-direction: column;

    .permission-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20 * @rem 18 * @rem 0 18 * @rem;
      margin-bottom: 20 * @rem;
      .title {
        font-size: 16 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        flex-shrink: 0;
        font-weight: 600;
        color: #111111;
      }
      .close {
        background: url('~@/assets/images/games/permission-close.png') no-repeat
          0 0;
        background-size: 14 * @rem 14 * @rem;
        width: 13 * @rem;
        height: 13 * @rem;
      }
    }

    .permission-list {
      // padding: 0 0 43 * @rem 0;
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      padding: 0 18 * @rem 0 18 * @rem;
      .permission-item {
        // padding: 10 * @rem;
        // border-bottom: 1px solid #ebebeb;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .name {
          font-weight: 400;
          font-size: 14 * @rem;
          color: #777777;
          white-space: nowrap;
        }
        .value-box {
          display: flex;
          align-items: center;
          width: 210 * @rem;
          justify-content: flex-end;
          .value1 {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #333333;
          }
          .value2 {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #32b768;
            line-height: 14 * @rem;
            display: inline;
            border-bottom: solid 1px #32b768;
            &:not(:first-child) {
              margin-left: 9 * @rem;
            }
          }
        }
        &:not(:first-child) {
          margin-top: 16 * @rem;
        }
        &:last-child {
          margin-bottom: 43 * @rem;
        }
      }
    }
  }

  .permission-popup {
    max-height: 400 * @rem;
    min-height: 200 * @rem;
    display: flex;
    flex-direction: column;
    .title {
      font-size: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 60 * @rem;
      background-color: #fff;
      flex-shrink: 0;
    }
    .permission-list {
      padding: 0 14 * @rem 20 * @rem;
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      .permission-item {
        padding: 10 * @rem;
        border-bottom: 1px solid #ebebeb;
        .name {
          font-size: 14 * @rem;
          color: #666;
        }
        .desc {
          font-size: 12 * @rem;
          color: #999;
          line-height: 18 * @rem;
          margin-top: 10 * @rem;
        }
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loading_rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* 浏览器滚动条隐藏 */
* {
  scrollbar-width: none;
}
*::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
}
* {
  -ms-overflow-style: none;
}
* {
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}
</style>
