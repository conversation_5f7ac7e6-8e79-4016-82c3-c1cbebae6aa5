<template>
  <van-dialog
    v-model="popupShow"
    :close-on-click-overlay="false"
    message-align="left"
    :lock-scroll="false"
    class="quesiton-popup"
    :show-confirm-button="false"
  >
    <div class="question-container">
      <div class="question-title">绑定微信常见问题</div>
      <div class="question-content">
        <p>
          有部分玩家在绑定微信提醒的过程中，会出现“获取微信参数出错，请稍后再试”的提醒，导致微信无法绑定，出现该问题可以通过以下方式进行解决
        </p>
        <h3> 1、检查手机内是否有多开微信，或者安装多个微信。 </h3>
        <p>
          如果有多开微信，或者安装多个微信，将无法进行绑定。请保留一个微信，再进行微信提醒绑定。
        </p>
        <h3> 2、手机内的微信是否为腾讯官方正版微信APP。 </h3>
        <p>
          如果无法确定是否为正版微信，可以尝试登陆其他能够微信登陆的APP，如：应用宝等，看看是否能够正常登陆。如果无法正常登陆，请前往微信官网下载正版微信，登陆之后再进行绑定微信提醒。
        </p>
        <h3> 3、奖励领取问题。 </h3>
        <p>
          当玩家正常绑定及关注公众号后均可到“任务中心-新手任务处”手动领取奖励。注：关注公众号的微信需为您3733游戏盒账号绑定的微信哦，且每个账号这个领取一次奖励。
        </p>
      </div>
    </div>
    <div class="close" @click="popupShow = false"></div>
  </van-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
  methods: {
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.quesiton-popup {
  width: 334 * @rem;
  background: transparent;
  .question-container {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16 * @rem;
    width: 334 * @rem;
    padding: 28 * @rem 20 * @rem;
    .question-title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
      padding-bottom: 6 * @rem;
    }
    .question-content {
      h3 {
        font-size: 13 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        font-weight: 600;
        margin-top: 20 * @rem;
      }
      p {
        font-size: 13 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        margin-top: 8 * @rem;
      }
    }
  }
  .close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/close.png) center center no-repeat;
    background-size: 28 * @rem 28 * @rem;
    margin: 28 * @rem auto 0;
  }
}
</style>
