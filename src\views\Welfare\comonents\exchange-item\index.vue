<template>
  <div class="exchange-item">
    <div class="exchange-icon"></div>
    <div class="exchange-info">
      <div class="title">{{ exchangeInfo.goods_name }}</div>
      <div class="fee">
        <span>{{ $t('金币') }}</span
        >{{ $t('消耗') }}{{ exchangeInfo.use_gold_num }}{{ $t('金币') }}
      </div>
    </div>
    <div class="exchange-right">
      <div class="exchange-btn btn" v-if="Number(exchangeInfo.ex_status) == 0">
        {{ $t('未开抢') }}
      </div>
      <div
        class="exchange-btn can btn"
        v-else-if="Number(exchangeInfo.ex_status) == 1"
        @click="handleExchange(exchangeInfo)"
      >
        {{ $t('立即兑换') }}
      </div>
      <div
        class="exchange-btn btn"
        v-else-if="Number(exchangeInfo.ex_status) == 3"
      >
        {{ $t('已兑换') }}
      </div>
      <div
        class="exchange-btn btn"
        v-else-if="Number(exchangeInfo.ex_status) == 2"
      >
        {{ $t('来晚啦') }}
      </div>
      <div class="rest">
        {{ $t('剩余') }}({{ exchangeInfo.rest_num }}/{{
          exchangeInfo.max_ex_num
        }})
      </div>
    </div>
  </div>
</template>

<script>
import { ApigoldExchangeRaffle } from '@/api/views/gold.js';
import { mapActions } from 'vuex';
export default {
  name: 'exchangeItem',
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      exchangeInfo: {},
    };
  },
  created() {
    this.exchangeInfo = this.info;
  },
  methods: {
    async handleExchange(info) {
      try {
        const res = await ApigoldExchangeRaffle({
          ex_info_id: info.id,
        });
        if (res.code > 0 && res.data != null) {
          if (res.data.type == -1) {
            this.$toast(res.msg);
            this.SET_USER_INFO();
            return false;
          }
          this.$toast(this.$t('兑换成功,请在“我的”页面中查看奖励'));
          this.exchangeInfo = res.data;
          this.SET_USER_INFO();
        }
      } catch (e) {}
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
};
</script>

<style lang="less" scoped>
.exchange-item {
  padding: 23 * @rem 0;
  display: flex;
  align-items: center;
  .exchange-icon {
    width: 50 * @rem;
    height: 50 * @rem;
    .image-bg('~@/assets/images/welfare/exchange-coupon-icon.png');
  }
  .exchange-info {
    flex: 1;
    min-width: 0;
    margin-left: 12 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #111111;
      font-weight: 500;
    }
    .fee {
      display: flex;
      align-items: center;
      font-size: 12 * @rem;
      color: #797979;
      font-weight: 400;
      margin-top: 8 * @rem;
      span {
        height: 13 * @rem;
        display: flex;
        align-items: center;
        padding: 0 4 * @rem;
        background: #ffecdb;

        border: 1 * @rem solid #ff9a54;
        border-radius: 8 * @rem;
        font-size: 8 * @rem;
        color: #fd854c;
        font-weight: 600;
        margin-right: 5 * @rem;
      }
    }
  }
  .exchange-right {
    width: 100 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    .exchange-btn {
      width: 66 * @rem;
      height: 28 * @rem;
      border: 1px solid #c1c1c1;
      border-radius: 15 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: #9a9a9a;
      font-weight: 500;
      &.can {
        border: 1px solid @themeColor;
        color: @themeColor;
      }
    }
    .rest {
      font-size: 11 * @rem;
      color: #9a9a9a;
      font-weight: 400;
      white-space: nowrap;
      margin-top: 5 * @rem;
    }
  }
  &:not(:last-of-type) {
    border-bottom: 1px solid #e8e8e8;
  }
}
</style>
