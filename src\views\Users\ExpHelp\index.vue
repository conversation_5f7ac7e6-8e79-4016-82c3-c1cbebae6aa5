<template>
  <div class="exp-help-page">
    <nav-bar-2
      :placeholder="false"
      bgStyle="transparent"
      :title="pageTitle"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <section class="first-section">
      <div class="background">
        <div class="level">
          <div class="content">{{ userExp.exp_level_name }}</div>
        </div>
      </div>
      <div class="container-wrapper">
        <div class="title">
          <span class="curr-exp">{{ userExp.exp_level_name }}</span>
          <span class="next-exp">{{ userExp.next_level_name }}</span>
        </div>
        <div class="container">
          <div class="max-bar">
            <div class="now-bar" :style="{ width: `${percentage}%` }"></div>
          </div>
          <div class="content">
            <div class="left">{{ $t('经验值') }}:{{ userExp.exp_sum }}</div>
            <div class="right">
              {{ $t('还差') }}{{ userExp.exp_next }}{{ $t('经验值升级') }}
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="second-section">
      <div class="title">{{ $t('如何获取经验值') }}：</div>
      <ul class="list">
        <li class="item">
          {{ $t('每日签到') }}<span>3~10{{ $t('经验') }}</span>
        </li>
        <li class="item">
          {{ $t('每日首次下载游戏') }}<span>3~10{{ $t('经验') }}</span>
        </li>
        <li class="item">
          {{ $t('每日首次登陆游戏') }}<span>5{{ $t('经验') }}</span>
        </li>
        <li class="item">
          {{ $t('每日首次发表评论') }}<span>3{{ $t('经验') }}</span>
        </li>
        <li class="item">
          {{ $t('每日首次分享游戏盒') }}<span>3{{ $t('经验') }}</span>
        </li>
        <li class="item">{{ $t('通过完成任务也可获得大量经验!') }}</li>
      </ul>
    </section>
    <section class="third-section">
      <div class="title">{{ $t('经验等级说明') }}：</div>
      <div class="table">
        <div class="row">
          <div class="th">{{ $t('等级') }}</div>
          <div class="th">{{ $t('头衔') }}</div>
          <div class="th">{{ $t('所需经验') }}</div>
        </div>
        <div class="row" v-for="(item, index) in expList" :key="index">
          <div class="td">{{ item.level_id }}</div>
          <div class="td">{{ item.level_name_s }}</div>
          <div class="td">{{ item.require_exp }}</div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import { ApiUserExpRank } from '@/api/views/users';
import { platform } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      pageTitle: this.$t('经验等级'),
      userExp: {},
      expList: [],
      navbarOpacity: 0,
    };
  },
  computed: {
    percentage() {
      return Math.floor(
        (parseInt(this.userExp.exp_sum) /
          (parseInt(this.userExp.exp_sum) + parseInt(this.userExp.exp_next))) *
          100,
      );
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
    window.addEventListener('scroll', this.handleScroll);
    const res = await ApiUserExpRank();
    this.expList = res.data.exp_rank;
    this.userExp = res.data.user_exp;
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.exp-help-page {
  .first-section {
    position: relative;
    height: 325 * @rem;
    width: 100%;
    .background {
      width: 100%;
      height: 325 * @rem;
      background-size: 100% 289 * @rem;
      background-repeat: no-repeat;
      overflow: hidden;
      background-image: url(~@/assets/images/users/exp_help_bg_new.png);
      .level {
        position: relative;
        width: 331 * @rem;
        height: 325 * @rem;
        margin: 0 auto;
        background-size: 331 * @rem 325 * @rem;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/users/exp_help_level.png);
        position: relative;
        .content {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 170 * @rem;
          width: 80 * @rem;
          height: 20 * @rem;
          text-align: center;
          line-height: 20 * @rem;
          font-size: 14 * @rem;
          font-weight: bold;
          color: #ff7554;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .container-wrapper {
      box-sizing: border-box;
      position: absolute;
      right: 0;
      bottom: 8 * @rem;
      left: 0;
      width: 339 * @rem;
      height: 90 * @rem;
      margin: auto;
      background-color: #ffffff;
      border-radius: 12 * @rem;
      box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
      padding: 0 28 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16 * @rem;
        height: 22 * @rem;
        line-height: 22 * @rem;
        color: #000000;
        font-weight: bold;
        span {
          font-weight: bold;
        }
      }
      .container {
        width: 280 * @rem;
        margin: 12 * @rem auto 0;
        .max-bar {
          width: 100%;
          height: 8 * @rem;
          background-color: #f3efe7;
          border-radius: 4 * @rem;
          .now-bar {
            width: 0%;
            height: 8 * @rem;
            background-color: @themeColor;
            border-radius: 4 * @rem;
          }
        }
        .content {
          margin-top: 7 * @rem;
          color: #666666;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 11 * @rem;
          line-height: 16 * @rem;
          color: #9a9a9a;
        }
      }
    }
  }
  .second-section {
    padding: 17 * @rem 18 * @rem 6 * @rem;
    border-bottom: 8 * @rem solid #f5f5f6;
    background: #ffffff;

    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .list {
      margin-top: 7 * @rem;
      .item {
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 47 * @rem;
        padding-left: 9 * @rem;
        font-size: 14 * @rem;
        color: #000000;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #e8e8e8;
        }
        &::before {
          content: '';
          width: 4 * @rem;
          height: 4 * @rem;
          border-radius: 50%;
          background-color: @themeColor;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        span {
          color: @themeColor;
        }
      }
    }
  }
  .third-section {
    padding: 19 * @rem 18 * @rem 32 * @rem;
    background: #ffffff;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .table {
      box-sizing: border-box;
      width: 100%;
      margin-top: 20 * @rem;
      border-radius: 12 * @rem;
      border: 0.5 * @rem solid #cba597;
      overflow: hidden;
      .row {
        display: flex;
        &:nth-of-type(n + 2) {
          border-top: 0.5 * @rem solid #cba597;
        }
        &:nth-of-type(2n + 1) {
          background-color: #f5f5f6;
        }
      }
      .th,
      .td {
        width: 50%;
        text-align: center;
        font-size: 12 * @rem;
        color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 11 * @rem 5 * @rem;
        &:nth-of-type(n + 2) {
          border-left: 0.5 * @rem solid #cba597;
        }
      }
      .th {
        background-color: #f5e2ce;
        font-size: 14 * @rem;
        color: #7a532a;
        font-weight: normal;
      }
    }
  }
}
</style>
