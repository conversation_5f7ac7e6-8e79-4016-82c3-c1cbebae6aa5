<template>
  <div class="page my-deal-page">
    <nav-bar-2 :border="true" :title="$t('我的交易')">
      <template #right>
        <div class="kefu-btn" @click="toPage('Kefu')"></div>
      </template>
    </nav-bar-2>
    <div class="main fixed-center">
      <div class="navs">
        <div
          class="nav"
          v-for="(item, index) in navs"
          :key="index"
          :class="[{ current: $route.path.includes(item.path) }]"
          @click="toPage(item.name, {}, 1)"
        >
          {{ item.title }}
        </div>
      </div>
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyDeal',
  data() {
    return {
      navs: [
        {
          title: this.$t('购买'),
          path: 'my_buy_list',
          name: 'MyBuyList',
        },
        {
          title: this.$t('出售'),
          path: 'my_sell_list',
          name: 'MySellList',
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.my-deal-page {
  .kefu-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/mine/icon_kefu.png) right center no-repeat;
    background-size: 20 * @rem 20 * @rem;
  }
  .main {
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    width: 100%;
    .navs {
      box-sizing: border-box;
      width: 240 * @rem;
      height: 30 * @rem;
      border-radius: 18 * @rem;
      overflow: hidden;
      display: flex;
      border: 1px solid #46cd3d;
      margin: 10 * @rem auto;
      .nav {
        width: 50%;
        height: 30 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        color: #46cd3d;
        &.current {
          background: @themeBg;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
