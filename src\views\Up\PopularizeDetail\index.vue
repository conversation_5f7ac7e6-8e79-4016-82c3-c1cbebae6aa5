<template>
  <div class="popularize-detail">
    <nav-bar-2
      :placeholder="false"
      :bgStyle="navbarOpacity > 0.8 ? 'transparent' : 'transparent-white'"
      :title="navbarOpacity > 0.8 ? detail.title : ''"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="bg">
      <img :src="detail.video_thumb" />
    </div>
    <div class="game-section">
      <img :src="detail.titlepic" class="game-img" />
      <div class="game-info">
        <div class="game-title">{{ detail.title }}</div>
        <div class="tag-list">
          <div
            v-for="(item2, index) in detail.app_tag"
            :key="index"
            class="tag-item"
          >
            {{ item2.name }}
          </div>
        </div>
      </div>
      <div
        @click="
          toPage('UpDetail', {
            id: detail.id,
            gameInfo: detail,
          })
        "
        class="button"
      >
        试玩
      </div>
    </div>
    <div class="game-introduce" v-html="content"></div>
    <div class="bottom-container">
      <img :src="detail.titlepic" class="game-img" />
      <div class="game-title">{{ detail.title }}</div>
      <div
        @click="
          toPage('UpDetail', {
            id: detail.id,
            gameInfo: detail,
          })
        "
        class="button"
      >
        试玩
      </div>
      <div class="text">{{ up.nickname }}up主分享</div>
    </div>
  </div>
</template>
<script>
import { ApiGameRead } from '@/api/views/game.js';
import { mapMutations } from 'vuex';

export default {
  name: 'PopularizeDetail',
  data() {
    return {
      navbarOpacity: 0,
      detail: {},
      up: {},
      content: '',
    };
  },
  async created() {
    if (this.$route.params.gameInfo) {
      this.detail = this.$route.params.gameInfo;
      this.content = this.$route.params.content;
      this.up = this.detail.up_info;
      this.setGameInfo(this.detail);
    } else {
      await this.getDetailData();
    }
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 获取详情页数据
    async getDetailData() {
      const res = await ApiGameRead({ id: this.$route.params.id });
      this.detail = res.data.detail;
      this.content = '';
      this.up = res.data.up_info.up;
      this.setGameInfo(this.detail);
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
  },
};
</script>
<style lang="less" scoped>
.popularize-detail {
  position: relative;
  min-height: 100vh;
  .bg {
    width: 100%;
    height: 200 * @rem;
    overflow: hidden;
    img {
      width: 100%;
      height: auto;
    }
  }
  .game-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15 * @rem 18 * @rem 0;
    .game-img {
      flex: 0 0 60 * @rem;
      width: 60 * @rem;
      height: 60 * @rem;
      margin-right: 10 * @rem;
      border-radius: 10 * @rem;
    }
    .game-info {
      flex: 1;
      .game-title {
        font-size: 16 * @rem;
        font-weight: 600;
        color: #333333;
        line-height: 20 * @rem;
      }
      .tag-list {
        display: flex;
        margin-top: 10 * @rem;
        .tag-item {
          font-size: 10 * @rem;
          color: #666666;
          line-height: 13 * @rem;
          padding: 2 * @rem 4 * @rem;
          background: #f5f5f6;
          border-radius: 4 * @rem 4 * @rem 4 * @rem 4 * @rem;
          margin-right: 8 * @rem;
        }
      }
    }
    .button {
      flex: 0 0 58 * @rem;
      margin-left: 10 * @rem;
      width: 58 * @rem;
      height: 28 * @rem;
      background: rgba(254, 102, 0, 0.1);
      border-radius: 25 * @rem 25 * @rem 25 * @rem 25 * @rem;
      color: @themeColor;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .game-introduce {
    margin: 30 * @rem 18 * @rem 215 * @rem;
    color: #666666;
    line-height: 15 * @rem;
  }
  .bottom-container {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 200 * @rem;
    background: #ffffff;
    box-shadow: 0 * @rem 0 * @rem 10 * @rem 0 * @rem rgba(7, 7, 7, 0.08);
    border-radius: 25 * @rem 25 * @rem 0 0;
    .game-img {
      width: 70 * @rem;
      height: 70 * @rem;
      border-radius: 10 * @rem;
      margin: 20 * @rem auto 8 * @rem;
    }
    .game-title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
    }
    .button {
      width: 64 * @rem;
      height: 28 * @rem;
      background: @themeColor;
      border-radius: 25 * @rem 25 * @rem 25 * @rem 25 * @rem;
      margin: 12 * @rem auto;
      font-size: 13 * @rem;
      font-family:
        PingFang SC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .text {
      text-align: center;
      font-size: 13 * @rem;
      color: #9a9a9a;
    }
  }
}
</style>
