<template>
  <div class="search-container" :class="{ white: white }">
    <div class="search-bar" @click="goToSearch">
      <div class="icon"></div>
      <div
        class="search-text"
        v-if="!initData.default_search || !initData.default_search.length"
        >{{ $t('搜一搜') }}</div
      >
      <div class="search-text-box" v-else>
        <swiper
          v-if="
            initData.default_search &&
            initData.default_search.length &&
            isKeep &&
            initData.default_search.length > 1
          "
          class="hot-center"
          :options="swiperOptionsHot"
          :auto-update="false"
          ref="mySearchSwiper1"
        >
          <swiper-slide
            class="hot-box"
            v-for="(item, index) in initData.default_search"
            :key="index"
          >
            <div class="search-text">{{ item.keyword }}</div>
            <img v-if="item.keyword && item.img" :src="item.img" alt="" />
          </swiper-slide>
        </swiper>
        <template v-if="initData.default_search.length == 1">
          <div
            class="hot-box"
            v-for="(item, index) in initData.default_search"
            :key="index"
            @click="clickGoToSearch(item, index)"
          >
            <div class="search-text">{{ item.keyword }}</div>
            <img v-if="item.keyword && item.img" :src="item.img" alt="" />
          </div>
        </template>
      </div>
    </div>
    <!-- <div class="sign-in" @click="toSignIn"></div> -->
    <div @click="goToNotice" class="message">
      <div class="dot-red" v-if="unreadCount.sum > 0"></div>
      <!-- <div class="dot" v-if="unreadCount.sum > 0">
        {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
      </div> -->
    </div>
    <div class="my-game" @click="toMyGame"></div>
    <!-- <div class="grq" @click="toDownManagement"></div>    -->
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex';
import { BOX_showActivity } from '@/utils/box.uni.js';
import { ApiGameCheckDown } from '@/api/views/game.js';

export default {
  props: {
    white: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    let that = this;
    return {
      searchHotText: '',
      swiperOptionsHot: {
        direction: 'vertical', // 垂直滚动
        slidesPerView: 1,
        loop: true,
        autoplay: true,
        allowTouchMove: false,
        autoplay: {
          delay: 3000,
        },
        on: {
          slideChange: function (e) {
            setTimeout(() => {
              that.currentSlideIndex = this.realIndex + 1;
              that.searchHotText = that.initData.default_search[this.realIndex];
            }, 0);
          },
          init() {
            this.slideTo(that.currentSlideIndex, 0, true);
          },
        },
      },
      isKeep: false, //控制页面keepAlive后轮播不播放问题
      currentSlideIndex: 0, // 保存当前swiperOptionsHot的索引
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      initData: 'system/initData',
      searchCurrentSlideIndex: 'system/searchCurrentSlideIndex',
    }),
  },
  methods: {
    clickGoToSearch(item, index) {
      this.currentSlideIndex = index;
      this.searchHotText = item;
    },
    goToNotice() {
      this.CLICK_EVENT('M1_MSG');
      this.toPage('Notice');
    },
    goToSearch() {
      window.scrollTo(0, 0);
      this.setSearchHotText(this.searchHotText);
      this.CLICK_EVENT('M1_SEARCH');
      this.toPage('Search');
    },
    async toDownManagement() {
      this.CLICK_EVENT('M1_GAMELIST');
      const checkRes = await this.checkDownload();
      if (!checkRes) {
        return false;
      }
      BOX_showActivity(
        { name: 'GrqList', isAndroidBoxToNative: true },
        { page: 'yygl' },
      );
    },
    async toMyGame() {
      this.CLICK_EVENT('M1_GAMELIST');
      this.$router.push({ name: 'MyGame' });
    },
    async toSignIn() {
      this.CLICK_EVENT('M1_SignIn');
      this.$router.push({ name: 'GoldCoinCenter' });
    },
    // 下载按钮防沉迷check
    async checkDownload() {
      const res = await ApiGameCheckDown();
      if (res.code > 0) {
        return true;
      }
      return false;
    },
    ...mapMutations({
      setSearchHotText: 'system/setSearchHotText',
      setSearchCurrentSlideIndex: 'system/setSearchCurrentSlideIndex',
    }),
  },
  created() {
    this.isKeep = true;
  },
  activated() {
    this.isKeep = true;
    this.currentSlideIndex = this.searchCurrentSlideIndex;
  },
  deactivated() {
    this.isKeep = false;
    if (this.$refs.mySearchSwiper1) {
      // 离开前保存当前的索引
      this.setSearchCurrentSlideIndex(this.currentSlideIndex);
    }
  },
};
</script>
<style lang="less" scoped>
.search-bar {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
  padding: 0 19 * @rem 0 6 * @rem;
  width: 310 * @rem;
  height: 30 * @rem;
  border-radius: 18 * @rem;
  border: 2 * @rem solid #191b1f;
  display: flex;
  align-items: center;
  .icon {
    width: 24 * @rem;
    height: 24 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 24 * @rem 24 * @rem;
  }
  .hot-box {
    display: flex;
    align-items: center;
    .search-text {
      font-size: 12 * @rem;
      color: #93999f;
      margin-left: 7 * @rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    img {
      margin-left: 4 * @rem;
      width: 12 * @rem;
      height: 12 * @rem;
    }
  }

  .search-text-box {
    flex: 1;
    min-width: 0;
    height: 30 * @rem;
    line-height: 30 * @rem;
    .swiper-container {
      height: 100%;
    }
  }
}
.search-container {
  display: flex;
  align-items: center;
  padding: 10 * @rem 18 * @rem 5 * @rem;
  .grq {
    width: 17 * @rem;
    height: 18 * @rem;
    padding: 5 * @rem 5 * @rem 5 * @rem 15 * @rem;
    background-image: url(~@/assets/images/download-black.png);
    background-size: 17 * @rem;
    background-position: 16 * @rem 5 * @rem;
    background-repeat: no-repeat;
  }
  .message {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 12 * @rem;
    background-size: 28 * @rem 28 * @rem;
    background-repeat: no-repeat;
    background-image: url(~@/assets/images/message.png);
    background-position: center center;
    position: relative;
    .dot {
      position: absolute;
      left: 63%;
      top: -2 * @rem;
      padding: 0 5 * @rem;
      height: 14 * @rem;
      border-radius: 7 * @rem 7 * @rem 7 * @rem 0;
      background-color: #fe4a55;
      color: #fff;
      font-size: 10 * @rem;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dot-red {
      width: 5 * @rem;
      height: 5 * @rem;
      border-radius: 50%;
      border: 1 * @rem solid #fff;
      background-color: #ff0000;
      position: absolute;
      top: 3 * @rem;
      right: 3 * @rem;
    }
  }
  .my-game {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 10 * @rem;
    background-image: url(~@/assets/images/my-game.png);
    background-size: 28 * @rem;
    background-repeat: no-repeat;
  }
  .sign-in {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 10 * @rem;
    background-image: url(~@/assets/images/clock-in.png);
    background-size: 28 * @rem;
    background-repeat: no-repeat;
  }
  &.white {
    .search-bar {
      border: 2 * @rem solid #fff;
      .icon {
        background-image: url(~@/assets/images/home/<USER>
      }
      .search-text {
        color: #fff;
      }
    }
    .grq {
      background-image: url(~@/assets/images/download-white.png);
    }
    .message {
      background-image: url(~@/assets/images/message-white.png);
    }
    .sign-in {
      background-image: url(~@/assets/images/clock-in-white.png);
    }
    .my-game {
      background-image: url(~@/assets/images/my-game-white.png);
    }
  }
  .search-text {
    margin-left: 7 * @rem;
  }
}
</style>
