<template>
  <div class="step-container">
    <div class="title" v-if="step == 4"> 恭喜您，已成功开启微信消息通知！ </div>
    <div class="title" v-else>
      微信提醒开通进度 <span>{{ step - 1 }}</span
      >/3
    </div>
    <div class="step-list">
      <div class="step-item">
        <div
          class="num num1"
          :class="{ on: step >= 1, success: wxInfo.is_bind }"
        ></div>
        <div class="step-card" :class="{ on: step == 1 }">
          <div class="bind-wechat">
            <div class="bind-title">绑定微信</div>
            <div
              class="btn bind-btn"
              :class="{ on: !wxInfo.is_bind }"
              @click="clickBindBtn"
            >
              {{ !wxInfo.is_bind ? '立即绑定' : '取消绑定' }}
            </div>
          </div>
          <div class="bind-nickname" v-if="wxInfo.is_bind && wxInfo.nickname">
            已绑定微信：{{ wxInfo.nickname }}
          </div>
          <div class="bind-question" @click="bindQuestionShow = true" v-else>
            绑定微信常见问题&gt;
          </div>
        </div>
      </div>
      <div class="step-item">
        <div
          class="num num2"
          :class="{ on: step >= 2, success: wxInfo.is_bind }"
        ></div>
        <div class="step-card" :class="{ on: step == 2 }">
          <div class="bind-gzh">
            <div class="bind-title">关注微信公众号</div>
            <div
              class="btn bind-btn"
              v-if="step != 1"
              @click="clickGzh"
              :class="{ had: wxInfo.is_gz }"
            >
              {{ !wxInfo.is_gz ? '立即关注' : '已关注' }}
            </div>
          </div>
          <div class="bind-desc">
            1.前往微信 - 顶部 [搜索] <br />
            2.搜索并关注“3733游戏”公众号
          </div>
          <div class="bind-info">
            <div class="icon">
              <img src="@/assets/images/app-icon2.png" alt="" />
            </div>
            <div class="name">3733游戏</div>
          </div>
        </div>
      </div>
      <div class="step-item">
        <div
          class="num num3"
          :class="{ on: step >= 3, success: step == 4 }"
        ></div>
        <div class="step-card" :class="{ on: step == 3 }">
          <div class="bind-notice">
            <div class="notice-title">开启微信消息通知</div>
            <div
              class="notice-btn"
              :class="{ had: is_notice }"
              v-if="step != 1 && step != 2"
              @click="clickNotice"
            >
              {{ is_notice ? '已完成' : '立即开启' }}
            </div>
          </div>
          <div class="notice-desc">完成设置，重要信息不错过！</div>
        </div>
      </div>
    </div>
    <bind-question :show.sync="bindQuestionShow"></bind-question>
    <gzh-popup :show.sync="gzhPopupShow"></gzh-popup>
    <open-notice
      :show.sync="noticePopupShow"
      @success="noticeRefresh"
    ></open-notice>

    <van-dialog
      v-model="unbindPopupShow"
      :close-on-click-overlay="false"
      message-align="left"
      :lock-scroll="false"
      class="unbind-popup"
      :show-confirm-button="false"
    >
      <div class="title">温馨提示</div>
      <div class="content">
        解绑后该微信将无法收到预约提醒，你可以重新绑定其他微信账号。
      </div>
      <div class="btn confirm" @click="unbindWX">确定</div>
      <div class="btn cancel" @click="unbindPopupShow = false">取消</div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiUserInviterExchangeWx,
  ApiUserWxUnbind,
  ApibindWx,
} from '@/api/views/users.js';
import {
  BOX_openWx,
  BOX_getProtocolKey,
  platform,
  Box_wxOAuth2,
} from '@/utils/box.uni.js';
import bindQuestion from '@/components/bind-question/index.vue';
import gzhPopup from './components/gzh-popup/index.vue';
import openNotice from './components/open-notice/index.vue';
export default {
  name: 'BindWeChatAZ',
  components: {
    bindQuestion,
    gzhPopup,
    openNotice,
  },
  data() {
    return {
      wxInfo: {
        is_bind: 0,
        is_gz: 0,
      },
      text: '3733游戏',
      is_notice: 0,

      bindQuestionShow: false,
      gzhPopupShow: false,
      unbindPopupShow: false,
      noticePopupShow: false,
    };
  },
  computed: {
    step() {
      let value = 1;
      if (this.wxInfo.is_bind) {
        value = 2;
        if (this.wxInfo.is_gz) {
          value = 3;
          if (this.is_notice) {
            value = 4;
          }
        }
      }
      return value;
    },
  },
  created() {
    this.noticeRefresh();
    this.getWxInfo();
  },
  mounted() {
    if (platform == 'android') {
      window.getWxUserInfo = async params => {
        this.$toast.loading('加载中');
        const res = await ApibindWx({
          wxCode: params.wxCode,
        });
        await this.getWxInfo();
        this.$toast(`绑定成功${res.data.wx_nickname}`);
      };
    }
  },
  methods: {
    clickGzh() {
      if (!this.wxInfo.is_gz) {
        this.$copyText(this.text).then(() => {
          this.gzhPopupShow = true;
        });
      }
    },
    async clickBindBtn() {
      if (!this.wxInfo.is_bind) {
        this.bindWX();
      } else {
        this.unbindPopupShow = true;
      }
    },
    async clickNotice() {
      if (!this.is_notice) {
        this.noticePopupShow = true;
      }
    },
    noticeRefresh() {
      let is_notice = localStorage.getItem('is_notice');
      if (is_notice) {
        this.is_notice = is_notice;
      }
    },
    async getWxInfo() {
      try {
        const res = await ApiUserInviterExchangeWx();
        // is_bind is_gz
        this.wxInfo = res.data;
      } catch (e) {}
    },
    // 解绑微信
    async unbindWX() {
      const res = await ApiUserWxUnbind();
      await this.getWxInfo();
      this.is_notice = 0;
      localStorage.removeItem('is_notice');
      this.unbindPopupShow = false;
    },
    // 绑定微信
    async bindWX() {
      Box_wxOAuth2();
    },
    // 打开微信
    openWX() {
      BOX_openWx();
    },
  },
};
</script>

<style lang="less" scoped>
.bind-wechat-page {
  background-color: #fff;
  .main {
    padding-bottom: 30 * @rem;
    .top-bg {
      width: 100%;
      height: 180 * @rem;
      background: linear-gradient(180deg, #4ad950 0%, #ffffff 100%);
    }
    .explain-container {
      box-sizing: border-box;
      width: 335 * @rem;
      height: 123 * @rem;
      margin: -82 * @rem auto 0;
      background-color: #fff;
      box-shadow: 0px 0px 10px 0px rgba(0, 102, 255, 0.06);
      border-radius: 14 * @rem;
      padding: 13 * @rem 12 * @rem;
      .content {
        font-size: 13 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        text-align: left;
      }
      .tips {
        font-size: 12 * @rem;
        margin-top: 10 * @rem;
        line-height: 15 * @rem;
        color: #32b768;
      }
    }

    .step-container {
      box-sizing: border-box;
      padding: 28 * @rem 0 40 * @rem;
      margin: 0 25 * @rem;
      border-bottom: 1 * @rem dashed #32b768;
      .title {
        font-size: 14 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        font-weight: 600;
        span {
          font-weight: 600;
          color: #32b768;
        }
      }
      .step-list {
        .step-item {
          position: relative;
          display: flex;
          margin-top: 29 * @rem;
          &:not(:last-child) {
            &::before {
              content: '';
              position: absolute;
              left: 10 * @rem;
              top: 28 * @rem;
              width: 0;
              height: calc(100% - 8 * @rem);
              border-left: 1 * @rem dashed #32b768;
            }
          }
          .num {
            width: 20 * @rem;
            height: 20 * @rem;
            background-size: 20 * @rem 20 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.num1 {
              &.on {
                background-image: url(~@/assets/images/step-num-1-on.png);
              }
            }
            &.num2 {
              background-image: url(~@/assets/images/step-num-2.png);
              &.on {
                background-image: url(~@/assets/images/step-num-2-on.png);
              }
            }
            &.num3 {
              background-image: url(~@/assets/images/step-num-3.png);
              &.on {
                background-image: url(~@/assets/images/step-num-3-on.png);
              }
            }
            &.success {
              background-image: url(~@/assets/images/step-success.png) !important;
            }
          }
          .step-card {
            border-radius: 12 * @rem;
            background-color: #fff;
            flex: 1;
            min-width: 0;
            margin-left: 12 * @rem;
            border: 1 * @rem solid #e0f1e0;
            padding: 17 * @rem 14 * @rem;
            &.on {
              border: 1 * @rem solid #32b768;
              background-color: #f2fbf2;
            }
            .bind-wechat {
              display: flex;
              align-items: center;
              .bind-title {
                flex: 1;
                min-width: 0;
                font-size: 14 * @rem;
                color: #333333;
                font-weight: 600;
                line-height: 18 * @rem;
              }
              .bind-btn {
                width: 64 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                display: flex;
                line-height: 24 * @rem;
                justify-content: center;
                font-size: 11 * @rem;
                color: #32b768;
                font-weight: 600;
                background: #fff;
                border: 1 * @rem solid #32b768;
                &.on {
                  background: #32b768;
                  color: #ffffff;
                }
              }
            }
            .bind-question {
              font-size: 12 * @rem;
              color: #27bb3f;
              line-height: 15 * @rem;
              margin-top: 11 * @rem;
            }
            .bind-nickname {
              font-size: 12 * @rem;
              color: #aaaaaa;
              line-height: 15 * @rem;
              margin-top: 11 * @rem;
            }

            .bind-gzh {
              display: flex;
              align-items: center;
              .bind-title {
                flex: 1;
                min-width: 0;
                font-size: 14 * @rem;
                line-height: 18 * @rem;
                color: #333333;
                font-weight: 600;
              }
              .bind-btn {
                width: 64 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                background-color: #32b768;
                display: flex;
                line-height: 24 * @rem;
                justify-content: center;
                font-size: 11 * @rem;
                color: #ffffff;
                font-weight: 600;
                &.had {
                  background-color: #cae7ca;
                }
              }
            }
            .bind-desc {
              line-height: 25 * @rem;
              font-size: 12 * @rem;
              color: #777777;
              margin-top: 7 * @rem;
            }
            .bind-info {
              box-sizing: border-box;
              display: flex;
              align-items: center;
              margin-left: 4 * @rem;
              margin-top: 6 * @rem;
              width: fit-content;
              padding: 3 * @rem 4 * @rem;
              background-color: #f2fbf2;
              border-radius: 4 * @rem;
              .icon {
                width: 18 * @rem;
                height: 18 * @rem;
              }
              .name {
                font-size: 12 * @rem;
                color: #32b768;
                margin-left: 4 * @rem;
                line-height: 15 * @rem;
              }
            }

            .bind-notice {
              display: flex;
              align-items: center;
              .notice-title {
                font-size: 14 * @rem;
                color: #333333;
                font-weight: 600;
                line-height: 18 * @rem;
                flex: 1;
                min-width: 0;
              }
              .notice-btn {
                width: 64 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                background-color: #32b768;
                display: flex;
                line-height: 24 * @rem;
                justify-content: center;
                font-size: 11 * @rem;
                color: #ffffff;
                font-weight: 600;
                &.had {
                  background-color: #cae7ca;
                }
              }
            }
            .notice-desc {
              font-size: 12 * @rem;
              line-height: 15 * @rem;
              margin-top: 11 * @rem;
              color: #777777;
            }
          }
        }
      }
    }

    .page-bottom {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 325 * @rem;
      height: 136 * @rem;
      border-radius: 12 * @rem;
      background: #23b960;
      margin: 32 * @rem auto 0;
      padding: 13 * @rem 15 * @rem;
      .ewm {
        width: 110 * @rem;
        height: 110 * @rem;
        background: #ffffff;
      }
      .right-content {
        margin-left: 18 * @rem;
        .gzh-name {
          font-size: 18 * @rem;
          color: #ffffff;
          font-weight: 600;
          line-height: 23 * @rem;
          white-space: nowrap;
        }
        .gzh-btn {
          font-size: 13 * @rem;
          color: #5bc69c;
          width: 135 * @rem;
          height: 34 * @rem;
          background: #fefefe;
          border-radius: 17 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 14 * @rem;
          font-weight: 500;
        }
      }
    }
  }
}

.unbind-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 16 * @rem;
  padding: 30 * @rem 30 * @rem 24 * @rem;
  .title {
    text-align: center;
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
  }
  .content {
    font-size: 15 * @rem;
    color: #333333;
    text-align: center;
    line-height: 21 * @rem;
    margin-top: 21 * @rem;
  }
  .confirm {
    width: 222 * @rem;
    height: 36 * @rem;
    border-radius: 18 * @rem;
    display: flex;
    justify-content: center;
    line-height: 36 * @rem;
    font-size: 13 * @rem;
    color: #ffffff;
    background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
    margin: 24 * @rem auto 0;
  }
  .cancel {
    width: 222 * @rem;
    height: 36 * @rem;
    border-radius: 18 * @rem;
    display: flex;
    justify-content: center;
    line-height: 36 * @rem;
    font-size: 13 * @rem;
    color: #7d7d7d;
    background-color: #f2f2f2;
    margin: 10 * @rem auto 0;
  }
}
</style>
