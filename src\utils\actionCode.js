import {
  toPage
} from '@/utils/tools.js';
import {
  BOX_openInNewWindow,
  BOX_openInNewNavWindow,
} from '@/utils/box.uni.js';
import router from '../router';
/**
 * 以下是服务端完整的actionCode，还在持续增加中 (主要是给原生安卓用的，所以有部分actionCode在web端是不存在页面的)
 * update_date: 2023年11月2日11:46:06
 */
const AC_ZXW = 1; //在线玩
const AC_NOT_ROOT = 2; //免ROOT
const AC_GAME = 3; //游戏碎片二级页面
const AC_ZXW_XY = 4; //在线玩-新游
const AC_ZXW_BD = 5; //在线玩-榜单
const AC_ZXW_FL = 6; //在线玩-福利
const AC_ZXW_LB = 7; //在线玩-礼包
const AC_ZJW = 8; //在线玩-最近玩过
const AC_ONLINE_NEW_SUBSCRIBE = 9; //网游版-新游预约
const AC_ONLINE_LB = 10; //网游版-礼包
const AC_ONLINE_LQ = 11; //网游版-领券
const AC_INDEX_CATE = 12; //首页-分类  //extra_id 首页tab下标
const AC_HALL_OF_FAME = 13; //跳转地址全屏
const AC_ZORE_GAME = 14; //福利中心0元首充
const AC_CLOSED_BETA = 15; //内测招募
const AC_TRIAL_PLAY = 16; //试玩
const AC_SANDBOX = 17; //沙盒
const QQ_GAME = 18; //QQ小游戏
const JPYX_GAME = 19; //精品小游戏
const CARD648 = 20; //648礼包
const OPENING_ACCOUNT = 21; //开局号
const BOUNTY_TASK = 22; //赏金任务
const GAME_SIGN_IN = 23; //游戏签到
const GAME_SIGN_IN_MORE = 24; //游戏签到更多
const TOOLBOX = 25; //工具箱
const NEW_XYX = 26; //新小游戏页面
const MORE_COLLECT = 28; //更多合集页面
const CLOUD_ON_HOOK = 29; //云挂机
const WELFARE_CENTER = 30; //福利中心
const MONEY_SAVING_TIPS = 31; //省钱秘籍
const HANGUP_ASSISTANT = 32; //挂机助手
const VIP_COUPON = 33; //领券中心svip专属
const SDK_ACCELERATE = 34; //sdk加速
const SDK_CONTINUOUS_CLICKS = 35; //sdk连点
const SDK_RECORD_VIDEO = 36; //录制视频
const ACTION_CHANGE_PASSWORD = 37; //修改密码
const ACTION_BIND_PHONE = 38; //绑定手机
const ACTION_BIND_EMAIL = 39; //绑定邮箱
const ACTION_REAL_NAME = 40; //实名认证
const ACTION_QUIT = 41; //退出
const Notice = 47; //消息中心

const HOME_INDEX_NEW_GAME = 48; //首页新游tab
const DEVELOP_OTHER_GAME = 49; //开发商其他游戏
const GAME_NEW = 50; //新游
const SP_INDEX_ACTIVITY = 51; //二级通用碎片页
const SP_TOPIC_INDEX_ACTIVITY = 52; //二级专题通用碎片页
const MORE_GAME_LIST = 53; //新版通用更多游戏列表
const BADGE_COLLECTION = 60; //我的徽章

const GAME_DETAIL = 1000; //需要传extra_id(游戏id)
const GAME_LIST = 1002; //需要传extra_id(游戏tag),text1(游戏类型名)
const GAME_GIFT_LIST = 1004; //礼包列表
const WEB = 1005; //需要传web_url
const HOT_ACTIVITY = 1006; //热门活动
const SPEEDUP = 1009; //加速版
const DYNAMIC = 1010; //动态
const SUBSCRIBE = 1011; //新游预约
const GAME_ONLINE = 1013; //网游
const SIGNIN = 1014; //签到
const EXCHANGE = 1015; //限时兑换
const MISSION = 1016; //赚金任务
const TRADE = 1017; //账号交易
const FUN_CARD = 1018; //畅玩卡
const MOD_GAME = 1019; //MOD游戏
const RECOMMEND = 1020; //推荐
const VIDEO = 1021; //视频
const JIANLOU = 1022; //捡漏
const CONPON = 1023; //领券中心
const PTB_RECHARGE = 1024; //平台币充值
const OPEN_SERVICE = 1025; //开服列表
const NEW_GAME = 1026; //新游首发
const RANKING = 1027; //排行榜
const CARD_HUB = 1028; //礼包中心
const GOLD_TURNTABLE = 1029; //大转盘
const ZERO_GAME = 1030; //0元畅玩
const ZHUANYOU_GAME = 1031; //转游中心
const GOLD_MALL = 1032; //金币商城
const GOLD_DUOBAO = 1033; //金币夺宝
const COLLECT_GAME = 1034; //614礼包
const UP_GAME = 1035; //UP游戏
const JYZX = 1036; //交易中心
const FLZX = 1037; //福利中心
const BT_GAME = 1038; //变态游戏
const BIG_GAME = 1039; //大厂游戏
const BT_GAME_NEW = 1040; //bt游戏分类
const CWB_TAB_MOD = 1041; //畅玩版分类   extra_id   type_id  cate_id
const CWB_TAB_JSB = 1042; //畅玩版加速版 金刚区
const CWB_TAB_BTB = 1043; //畅玩版变态版 金刚区
const CWB_TAB_UP = 1044; //畅玩版UP 金刚区
const CWB_TAB_HOT_UP = 1045; //畅玩版合集
const CWB_TAB_MOD_NEW = 1046; //畅玩版分类-最新列表 金刚区
const CWB_TAB_BT_RANK = 1047; //畅玩版变态排行榜
const ACTION_RECYCLE = 1048; //小号回收
const GAME_LIST_CLASS = 1049; //需要传extra_id(游戏分类)
const SAVINGS_CARD = 1050; //需要传extra_id(游戏分类)

const BT_GAME_CATE = 1040; //BT分类

const GOLD_COIN_MALL = 5000; //金币商城 
const GOLD_COIN_MALL_EXCHANGE = 5001; //金币商城-兑换
const SET_WE_CHAT_BINDING = 5002; //设置微信绑定
const PTB_RECHARGE_2 = 5005; //平台币
const MONEY_SAVING_CARD = 5011; //省钱卡
const SVIP = 5012; //svip

export const PageName = {
  [FUN_CARD]: 'ChangwanCard',
  [NEW_GAME]: 'HomeNewGame',
  [ZHUANYOU_GAME]: 'Zhuanyou',
  [GOLD_TURNTABLE]: 'TurnTable',
  [ZERO_GAME]: 'FreePlay',
  [JIANLOU]: 'Jianlou',
  [CONPON]: 'CouponCenter',
  [PTB_RECHARGE]: 'PlatformCoin',
  [GOLD_MALL]: 'GoldMall',
  [CARD_HUB]: 'GiftCenter',
  [GOLD_DUOBAO]: 'GoldGamble',
  [GAME_DETAIL]: 'GameDetail',
  [WEB]: 'Activity',
  [COLLECT_GAME]: 'GameCollect',
  [UP_GAME]: 'Up',
  [ACTION_RECYCLE]: 'Recycle',
  [SIGNIN]: 'ClockIn',
  [CARD648]: 'Welfare648',
  [OPENING_ACCOUNT]: 'OpeningAccount',
  [BOUNTY_TASK]: 'BountyTask',
  [GAME_SIGN_IN]: 'SignInDetail',
  [GAME_SIGN_IN_MORE]: 'GameSignInList',
  [MONEY_SAVING_TIPS]: 'SavingsSecret',
  [CLOUD_ON_HOOK]: 'CloudHangup',
  [GOLD_COIN_MALL]: 'GoldCoinCenter',
  [GOLD_COIN_MALL_EXCHANGE]: 'WelfareGoldCoinExchange',
  [PTB_RECHARGE_2]: 'PlatformCoin',
  [MONEY_SAVING_CARD]: 'SavingsCard',
  [SVIP]: 'Svip',
  [SET_WE_CHAT_BINDING]: 'BindWeChat',
  [Notice]: 'Notice',
  [RANKING]: 'Rank',
  [CWB_TAB_BT_RANK]: 'Rank',
  [SP_TOPIC_INDEX_ACTIVITY]: 'SpecialTopic',
  [MORE_GAME_LIST]: 'MoreGameList',
  [GAME_NEW]: 'HomeNewGame',
  [CWB_TAB_HOT_UP]: 'ExternalGameCollect',
  [JYZX]: 'RoleTransfer',
  [TRADE]: 'Deal',
  [BADGE_COLLECTION]: 'BadgeCollection',
};


export function handleActionCode(info) {
  // 合集页面特殊处理，需要传id
  if (info.action_code == 1034) {
    toPage(PageName[info.action_code], {
      id: info.heji_id
    });
    return false;
  }
  // up资源
  if (info.action_code == 1035) {
    toPage('Category', {
      classId: 40
    });
    return false;
  }
  // 福利中心
  if (info.action_code == 30) {
    toPage(info.web_url, info);
    return false;
  }
  // 分类页BT
  if (info.action_code == 1040) {
    toPage('Category', {
      classId: 1
    });
    return false;
  }
  //web全屏
  if (info.action_code == 13) {
    if (info.web_page) {
      BOX_openInNewWindow({
        name: info.web_page
      }, {
        url: info.web_url
      });
    } else {
      if (info.web_url.indexOf('activity.3733.com') > -1) {
        toPage('Activity', {
          url: info.web_url
        });
        return false;
      }
      // 专属活动
      if (info.web_url.indexOf('/exclusive_activity/') > -1) {
        toPage('ExclusiveActivity', {
          id: info.web_url.split('exclusive_activity/')[1],
        });
        return false;
      }
      // 其他url通知跳转
      toPage("Iframe", {
        url: info.web_url, // iframe url
        title: info.title, // iframe title
      });
      // BOX_openInNewWindow({ h5_url: info.web_url }, { url: info.web_url });
    }
    return false;
  }
  if (info.action_code == 1005) {
    if (info.web_page) {
      BOX_openInNewNavWindow({
        name: info.web_page
      }, {
        url: info.web_url
      });
    } else {
      BOX_openInNewNavWindow({
        h5_url: info.web_url
      }, {
        url: info.web_url
      });
    }
    return false;
  }
  //省钱卡
  if (info.action_code == 1050) {
    toPage('SavingsCard');
    return false;
  }
  //首页
  if (info.action_code == 46) {
    toPage('QualitySelect');
    return false;
  }
  // 游戏详情
  if (info.action_code == 1000) {
    toPage('GameDetail', {
      id: info.extra_id || info.game_id
    });
    return false;
  }
  /**
   *  Anchor 2025年3月12日17:59:23
   *  新增专题碎片页和更多游戏页
   */
  if (info.action_code == 52) {
    toPage('SpecialTopic', {
      id: info.extra_id || info.id,
      title: info.next_page_title || info.text1 || info.header_title || '',
    });
    return false;
  }
  if (info.action_code == 53) {
    toPage('MoreGameList', {
      id: info.extra_id || info.id, // id=topic:76
      title: info.next_page_title || info.text1 || info.header_title || '更多游戏',
    });
    return false;
  }
  if (info.action_code == 1041) {
    toPage('CateGameList', {
      basis_id: info.basis_id || 0,
      cate_id: info.extra_id || 0,
      title: info.next_page_title || info.text1 || info.header_title || '全部分类',
    });
    return false;
  }
  if (info.action_code == 1045) {
    toPage('ExternalGameCollect', {
      id: info.extra_id || 0,
    });
    return false;
  }
  // 排行榜
  if (info.action_code == 1027) {
    // extra_id ==> order
    router.push({ path: '/home/<USER>', query: { tab: info.extra_id } });
    return false
  }

  toPage(PageName[info.action_code]);
}