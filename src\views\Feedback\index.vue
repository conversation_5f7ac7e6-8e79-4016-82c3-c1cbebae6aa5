<template>
  <div class="feedback-page" @click="closeSearch">
    <nav-bar-2 :title="pageTitle" :border="true" :azShow="true"></nav-bar-2>
    <div class="main">
      <div class="page-tips">
        <span>注意：</span>此处只做投诉与建议的处理，其他游戏问题，请在对
        应的问题工单进行提交，才能得到有效处理哦！
      </div>
      <div class="section">
        <div class="section-title">
          <div class="title-text">{{ $t('选择反馈类型') }}<span>*</span></div>
        </div>
        <div class="type-list">
          <div
            class="type-item"
            :class="{ on: currentType == type.type }"
            v-for="type in typeList"
            :key="type.type"
            @click="clickType(type.type)"
          >
            {{ type.title }}
          </div>
        </div>
      </div>
      <div class="section" v-if="currentType == 1" @click.stop="">
        <div class="section-title">
          <div class="title-text">{{ $t('游戏名称') }}<span>*</span></div>
        </div>
        <div class="game-name">
          <input
            type="text"
            :placeholder="$t('请填写游戏名称（必填）')"
            v-model.trim="gameName"
            :disabled="disable"
          />
        </div>
        <div class="search-list" v-show="searchListShow">
          <div
            class="search-item"
            v-for="(item, index) in searchList"
            :key="index"
            @click.stop="selectGameName(item)"
          >
            <div class="search-icon"></div>
            <div class="search-name">{{ item }}</div>
          </div>
        </div>
      </div>
      <div class="section" v-if="currentType == 3">
        <div class="section-title">
          <div class="title-text">{{ $t('投诉对象') }}<span>*</span></div>
        </div>
        <div class="game-name">
          <input
            type="text"
            :placeholder="$t('必填，如XX部门-小花')"
            v-model.trim="targetUser"
          />
        </div>
      </div>
      <div class="section">
        <div class="section-title">
          <div class="title-text">{{ $t('详细说明') }}<span>*</span></div>
          <div class="title-desc">{{ explainLength }}/1000{{ $t('字数') }}</div>
        </div>
        <div class="explain">
          <textarea
            type="text"
            :placeholder="explainPlaceholder"
            v-model.trim="explain"
          ></textarea>
        </div>
      </div>
      <div class="section">
        <div class="section-title">
          <div class="title-text">{{ $t('上传图片') }}</div>
          <div class="title-desc">
            {{ $t('支持PNG，JPEG格式，') }}{{ images.length }}/5
          </div>
        </div>
        <div class="photos">
          <van-uploader
            v-model="imageFileList"
            :after-read="afterRead"
            @delete="deletePic"
            :max-count="5"
            accept="image/*"
            :before-read="beforeRead"
            :multiple="true"
          >
            <div class="upload-btn">
              <img src="@/assets/images/feedback/upload-add.png" />
            </div>
          </van-uploader>
        </div>
      </div>
      <div class="bottom-container">
        <div class="bottom-fixed">
          <div class="submit">
            <div
              class="submit-btn"
              :class="{ 'can-submit': canSubmit }"
              @click="submit"
            >
              {{ $t('提交') }}
            </div>
          </div>
          <bottom-safe-area></bottom-safe-area>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiFeedbackPost, ApiGameFeedback } from '@/api/views/feedback.js';
import { checkIphone, checkQQ } from '@/utils/formValidation';
import md5 from 'js-md5';
export default {
  name: 'Feedback',
  data() {
    return {
      pageTitle: this.$t('投诉建议'),
      currentType: 3,
      typeList: [
        {
          title: this.$t('投诉客服'),
          type: 3,
        },
        {
          title: this.$t('下载安装'),
          type: 2,
        },
        {
          title: this.$t('游戏相关'),
          type: 1,
        },
        {
          title: this.$t('其他建议'),
          type: 4,
        },
      ],
      gameName: '', // 游戏名称
      gameId: '', // 游戏id
      targetUser: '', // 投诉对象
      explain: '', // 详细说明
      imageFileList: [], // 上传截图资源
      images: [], // 上传截图链接
      searchListShow: false, // 搜索列表是否显示
      searchList: [], // 匹配的游戏列表
      gameList: [], // 所有游戏列表
      disable: false, //游戏栏是否可编辑
    };
  },
  computed: {
    explainPlaceholder() {
      if (this.currentType === 1) {
        return this.$t(
          '请填写投诉的具体内容，游戏紧急问题请联系在线客服（必填）',
        );
      }
      return this.$t('请填写投诉的具体内容（必填）');
    },
    explainLength() {
      return this.explain.length;
    },
    canSubmit() {
      // 是否可提交
      if (!this.currentType) return false;
      switch (Number(this.currentType)) {
        case 1:
          if (!this.gameName) return false;
          break;
        case 3:
          if (!this.targetUser) return false;
          break;
        default:
          break;
      }
      if (!this.explain) return false;
      return true;
    },
  },
  async created() {
    if (this.$route.params.id) {
      this.gameId = this.$route.params.id;
      this.gameName = this.$route.params.gameName;
      if (this.$route.params.type) {
        this.currentType = this.$route.params.type;
      }
      this.disable = true;
    } else if (this.$route.query.id) {
      this.gameId = this.$route.query.id;
      this.gameName = this.$route.query.gameName;
      if (this.$route.query.type) {
        this.currentType = this.$route.query.type;
      }
      this.disable = true;
    }
    await this.getGameList();
  },
  watch: {
    gameName() {
      this.searchList = [];
      this.searchListShow = this.gameName.length > 0 ? true : false;
      for (let i of this.gameList) {
        if (i.game_name.indexOf(this.gameName) > -1)
          this.searchList.push(i.game_name);
      }
    },
  },
  methods: {
    async submit() {
      if (!this.canSubmit) {
        this.$toast(this.$t('还有未填写的必填项'));
        return false;
      }
      if (Number(this.currentType) == 1) {
        this.gameId = '';
        for (let i of this.gameList) {
          if (i.game_name === this.gameName) this.gameId = i.game_id;
        }
        if (!this.gameId) {
          this.$toast(this.$t('游戏不存在'));
          return false;
        }
      }
      // 开始提交操作
      let params = {
        type: this.currentType,
        content: this.explain,
      };
      // 有上传图片就加图片参数
      if (this.images.length) {
        params.photos = this.images.join(',');
      }
      if (Number(this.currentType) == 1) {
        params.game_id = this.gameId;
      }
      if (Number(this.currentType) == 3) {
        params.object = this.targetUser;
      }
      try {
        const res = await ApiFeedbackPost(params);
        if (res.code > 0) {
          this.$toast(res.msg);
          // 提交成功清除已填写的数据
          this.currentType = 0;
          this.gameName = '';
          this.gameId = '';
          this.disable = false;
          this.targetUser = '';
          this.explain = '';
          this.imageFileList = [];
          this.images = [];
        }
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    clickType(type) {
      this.currentType = type;
    },
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.images.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    selectGameName(item) {
      // 搜索框点击确认
      this.gameName = item;
      this.searchList.splice(0, this.searchList.length);
      this.$nextTick(() => {
        this.searchListShow = false;
      });
    },
    async getGameList() {
      const res = await ApiGameFeedback();
      this.gameList = res.data.gameList;
    },
    closeSearch() {
      if (this.searchListShow) this.searchListShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.feedback-page {
  background-color: #fff;
  min-height: 100vh;
  .section {
    width: 100%;
    margin-top: 25 * @rem;
    position: relative;
  }
  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title-text {
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      color: #000000;
      font-weight: bold;
      span {
        color: @themeColor;
        padding: 0 5 * @rem;
      }
    }
    .title-desc {
      font-size: 12 * @rem;
      color: #c1c1c1;
    }
  }
  .main {
    padding: 3 * @rem 18 * @rem;
    padding-bottom: 70 * @rem;
    .page-tips {
      padding-top: 15 * @rem;
      font-size: 13 * @rem;
      line-height: 20 * @rem;
      color: #797979;
      span {
        color: @themeColor;
      }
    }
    .type-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10 * @rem;
      .type-item {
        width: 72 * @rem;
        height: 30 * @rem;
        background-color: #f5f5f6;
        border-radius: 6 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        color: #797979;
        &.on {
          background: linear-gradient(222deg, #6ddc8c 0%, #21b98a 100%);
          color: #fff;
        }
      }
    }
    .game-name {
      width: 100%;
      height: 36 * @rem;
      background-color: #f5f5f6;
      border-radius: 6 * @rem;
      overflow: hidden;
      margin-top: 10 * @rem;
      input {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 100%;
        font-size: 14 * @rem;
        color: #333;
        background-color: transparent;
        padding: 0 10 * @rem;
        &::placeholder {
          color: #c1c1c1;
        }
      }
    }
    .explain {
      background-color: #f5f5f6;
      width: 100%;
      height: 180 * @rem;
      border-radius: 6 * @rem;
      overflow: hidden;
      margin-top: 10 * @rem;
      textarea {
        box-sizing: border-box;
        padding: 10 * @rem;
        display: block;
        width: 100%;
        height: 100%;
        border: 0;
        outline: none;
        background-color: transparent;
        line-height: 20 * @rem;
        font-size: 14 * @rem;
        color: #333;
        resize: none;
        &::placeholder {
          color: #c1c1c1;
        }
      }
    }
    .photos {
      margin-top: 15 * @rem;
      /deep/ .van-uploader__preview {
        margin: 0 15 * @rem 15 * @rem 0;
      }
      /deep/ .van-uploader__preview-image {
        width: 88 * @rem;
        height: 88 * @rem;
        border-radius: 7 * @rem;
      }
      /deep/ .van-uploader__preview-delete {
        width: 20 * @rem;
        height: 20 * @rem;
        top: -10 * @rem;
        right: -10 * @rem;
        transform: scale(0.8);
        border-radius: 10 * @rem;
        background-color: rgb(154, 154, 154);
      }
      /deep/ .van-uploader__preview-delete-icon {
        transform: scale(1);
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .upload-btn {
        width: 88 * @rem;
        height: 88 * @rem;
      }
    }
  }
  .bottom-container {
    height: calc(58 * @rem + @safeAreaBottom);
    height: calc(58 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 2000;
      border-top: 0.5 * @rem solid #ebebeb;
      .submit {
        height: 58 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        .submit-btn {
          width: 339 * @rem;
          margin: 0 * @rem auto;
          height: 44 * @rem;
          background-color: #cccccc;
          border-radius: 22 * @rem;
          color: #fff;
          font-size: 16 * @rem;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          &.can-submit {
            background: linear-gradient(222deg, #6ddc8c 0%, #21b98a 100%);
          }
        }
      }
    }
  }
}
.search-list {
  box-sizing: border-box;
  width: 100%;
  max-height: 210px;
  overflow-y: auto;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #f2f2f2;
  position: absolute;
  left: 0;
  top: 66 * @rem;
  background-color: #fff;
  z-index: 1000;
  padding: 5 * @rem 24 * @rem 5 * @rem 14 * @rem;
  .search-item {
    display: flex;
    align-items: center;
    height: 37 * @rem;
    &:not(:last-of-type) {
      .search-name {
        border-bottom: 0.5 * @rem solid #f2f2f2;
      }
    }
    .search-icon {
      width: 10 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/search-icon.png) center center no-repeat;
      background-size: 10 * @rem 10 * @rem;
    }
    .search-name {
      height: 100%;
      font-size: 13 * @rem;
      color: #000000;
      flex: 1;
      min-width: 0;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
      margin-left: 11 * @rem;
    }
  }
}
</style>
