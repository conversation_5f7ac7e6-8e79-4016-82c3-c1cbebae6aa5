<template>
  <div class="welfare-page">
    <nav-bar-2
      bgStyle="transparent-black"
      :placeholder="false"
      :bgColor="`rgba(3,7,52, ${navbarOpacity})`"
      title="SVIP专属福利"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="top-img"></div>
      <div class="user-bar">
        <div
          class="no-svip"
          v-if="userSvipInfo.svip_status != 1"
          @click="toSvip"
        >
          <div class="tips-text">
            <div class="p1">SVIP超值权益</div>
            <div class="p2">开通即可拥有</div>
          </div>
          <div class="no-svip-text">{{ $t('去开通') }}</div>
        </div>
        <div class="user-info" v-else>
          <user-avatar class="avatar" :src="userSvipInfo.avatar"></user-avatar>
          <div class="info-right">
            <div class="nickname">{{ userSvipInfo.nickname }}</div>
            <div class="end-date">
              {{ 'SVIP' + $t('有效期至') }}：{{ userSvipInfo.svip_time }}
            </div>
          </div>
        </div>
      </div>
      <div class="coin-mall">
        <div class="title" @click="toGoldCoinExchange">
          <img
            src="~@/assets/images/welfare/svip-welfare/coin-title.png"
            alt=""
          />
        </div>
        <div class="mall-content">
          <div class="tab-btns" :class="{ last: coinTab == 2 }">
            <div
              class="tab-btn"
              :class="{ active: coinTab == 1 }"
              @click="coinTab = 1"
            >
              兑换平台币
            </div>
            <div
              class="tab-btn"
              :class="{ active: coinTab == 2 }"
              @click="coinTab = 2"
            >
              兑换游戏道具
            </div>
          </div>
          <div class="content">
            <div class="bottom-color-border"></div>
            <div class="platform-coin-cont" v-if="coinTab == 1">
              <div class="loading-box" v-if="exchangeListLoading">
                <van-loading />
              </div>
              <template v-else>
                <div class="platform-coin-list" v-if="exchangeList.length">
                  <div
                    class="platform-coin-item"
                    v-for="(item, index) in exchangeList"
                    :key="index"
                  >
                    <div class="cont">
                      <div class="icon">
                        <img :src="item.img" :alt="item.title" />
                      </div>
                      <div class="info">
                        <div class="name">{{ item.title }}</div>
                        <div class="desc">
                          {{ $t('消耗') }}{{ item.gold }}{{ $t('金币') }}
                        </div>
                      </div>
                      <div class="right">
                        <div class="btn" @click="getPtb(item)">
                          {{ $t('立即兑换') }}
                        </div>
                        <div class="count">
                          {{ $t('剩余') }}({{ item.inventory }}/2000)
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <content-empty v-else></content-empty>
                <div
                  class="more"
                  v-if="exchangeList.length"
                  @click="toGoldCoinExchange"
                >
                  查看更多
                </div>
              </template>
            </div>
            <div class="game-props-cont" v-if="coinTab == 2">
              <div class="exchange-content">
                <div class="more-game-btn" @click="clickMoreGame"></div>
                <div class="nav-list">
                  <div
                    class="nav-item"
                    :class="{
                      active: game.game_info.id == currentGame.game_info.id,
                    }"
                    v-for="game in goldCardList"
                    :key="game.id"
                    @click="clickNavGame(game)"
                  >
                    <div class="game-icon">
                      <img :src="game.game_info.titlepic" alt="" />
                    </div>
                    <div
                      class="game-info"
                      v-if="game.game_info.id == currentGame.game_info.id"
                    >
                      <div class="game-title">
                        <div class="title-content text-scroll">
                          <div class="title-text">
                            {{ game.game_info.title }}
                          </div>
                          <div class="title-tag" v-if="game.game_info.subtitle">
                            {{ game.game_info.subtitle }}
                          </div>
                          <div class="title-text">
                            {{ game.game_info.title }}
                          </div>
                          <div class="title-tag" v-if="game.game_info.subtitle">
                            {{ game.game_info.subtitle }}
                          </div>
                        </div>
                      </div>
                      <div
                        class="game-discount"
                        v-if="Number(game.game_info.pay_rebate / 10) < 10"
                      >
                        <div class="left">折扣</div>
                        <div class="right">
                          {{ Number(game.game_info.pay_rebate / 10) }}折直充
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="exchange-list">
                  <template v-for="(item, index) in currentGame.card_info">
                    <div
                      class="exchange-item-bg"
                      v-if="!(index > 3 && !isExpand)"
                      :key="item.id"
                    >
                      <div class="exchange-item">
                        <div class="limit" v-if="item.redeem_num_text">
                          {{ item.redeem_num_text }}
                        </div>
                        <div class="exchange-icon">
                          <img :src="item.titlepic" alt="" />
                        </div>
                        <div class="exchange-info">
                          <marquee-text
                            class="item-title"
                            :text="item.title"
                          ></marquee-text>
                          <div class="cost">
                            消耗<span>{{ item.need_gold }}</span
                            >金币
                          </div>
                        </div>
                        <div class="exchange-btn" @click="getCard(item)">
                          立即兑换
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-if="currentGame.card_info.length > 4">
                    <div
                      class="exchange-down"
                      v-if="!isExpand"
                      @click="isExpand = !isExpand"
                    >
                      更多道具<i></i>
                    </div>
                    <div
                      class="exchange-down up"
                      v-else
                      @click="isExpand = !isExpand"
                    >
                      收起<i></i>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="coupon-box" v-if="specialOrderCouponList.length">
        <div class="top-color-border"></div>
        <div class="title" @click="toCouponCenter">
          <img
            src="~@/assets/images/welfare/svip-welfare/coupon-title.png"
            alt=""
          />
        </div>
        <div class="loading-box" v-if="specialOrderCouponListLoading">
          <van-loading />
        </div>
        <template v-else>
          <div class="coupon-list" v-if="specialOrderCouponList.length">
            <swiper :options="couponSwiperOption">
              <swiper-slide
                class="swiper-slide"
                v-for="(couponGroup, index) in specialOrderCouponList"
                :key="index"
              >
                <div class="coupon-group">
                  <div
                    class="coupon-item"
                    v-for="(coupon, couponIndex) in couponGroup"
                    :key="couponIndex"
                  >
                    <div class="coupon-color-bg">
                      <div class="coupon-left">
                        <div class="price">
                          <em>¥</em>
                          <span>{{ coupon.money }}</span>
                        </div>
                        <div class="threshold">
                          满{{ coupon.reach_money }}可用
                        </div>
                      </div>
                      <div class="info">
                        <div class="name">{{ coupon.remark }}</div>
                        <div class="date">{{ coupon.period_title }}</div>
                      </div>
                      <div class="coupon-right">
                        <div class="get-btn" @click="getCoupon(coupon)">
                          领取
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <div class="swiper-pagination coupon-pagination"></div>
          </div>
          <content-empty v-else></content-empty>
        </template>
      </div>
      <div class="card-box">
        <div class="top-color-border"></div>
        <div class="title">
          <img
            src="~@/assets/images/welfare/svip-welfare/gift-title.png"
            alt=""
          />
        </div>
        <div class="loading-box" v-if="specialOrderCouponListLoading">
          <van-loading />
        </div>
        <template v-else>
          <div class="card-list" v-if="specialOrderCardList.length">
            <swiper :options="cardSwiperOption">
              <swiper-slide
                class="swiper-slide"
                v-for="(cardGroup, index) in specialOrderCardList"
                :key="index"
              >
                <div class="card-group">
                  <div
                    class="card-item"
                    v-for="(card, cardIndex) in cardGroup"
                    :key="cardIndex"
                  >
                    <div class="card-color-bg">
                      <div class="card-info">
                        <div class="card-left">
                          <img :src="card.game_titlepic" alt="" />
                        </div>
                        <div class="info">
                          <div class="name">
                            <marquee-text
                              class="item-title"
                              :text="`《${card.titlegame}》${card.title}`"
                              :time="6"
                            ></marquee-text>
                          </div>
                          <div class="desc">
                            <marquee-text
                              :text="card.cardbody"
                              :time="6"
                            ></marquee-text>
                          </div>
                        </div>
                        <div class="card-right">
                          <div class="get-btn" @click="getCard(card)">领取</div>
                        </div>
                      </div>
                      <div class="card-count">
                        <div class="process">
                          <span :style="`width: ${card.remain}%`"></span>
                        </div>
                        <div class="number">剩余{{ card.remain }}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <div class="swiper-pagination card-pagination"></div>
          </div>
          <content-empty v-else></content-empty>
        </template>
      </div>
    </div>
    <div class="popup svip-popup">
      <van-dialog
        v-model="isSvipPopupShow"
        :show-confirm-button="false"
        :lock-scroll="false"
        :closeOnClickOverlay="true"
      >
        <div class="popup-content" @click.stop="">
          <div class="popup-bg">
            <div class="text">
              抱歉，您当前尚未开通SVIP会员<br />
              请先开通SVIP会员！
            </div>
            <div class="btn" @click="toSvip">前往开通</div>
          </div>
        </div>
      </van-dialog>
    </div>
    <div class="popup coin-popup">
      <van-dialog
        v-model="isCoinPopupShow"
        :show-confirm-button="false"
        :lock-scroll="false"
        :closeOnClickOverlay="true"
      >
        <div class="popup-content" @click.stop="">
          <div class="popup-bg">
            <div class="text">
              抱歉，您当前金币不足<br />
              可以前往赚取更多金币再来哦～
            </div>
            <div class="btn" @click="toGoldCoin">去赚金币</div>
          </div>
        </div>
      </van-dialog>
    </div>
    <exchange-game-search-popup
      :show.sync="moreGamePopup"
      @onSelectSuccess="onGameSelectSuccess"
    ></exchange-game-search-popup>
    <!-- 小号选择弹窗 -->
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="Number(xhSelectGame.game_id)"
      @onSelectSuccess="getGift"
    ></yy-xh-select>
    <!-- 礼包领取成功 -->
    <cardpass-copy-popup
      :show.sync="giftCopyPopupShow"
      :info="giftSelected"
      :autoXh="1"
    ></cardpass-copy-popup>
  </div>
</template>

<script>
import exchangeGameSearchPopup from '@/components/exchange-game-search-popup';
import { ApiGetUserSvipInfo } from '@/api/views/recharge.js';
import {
  ApiGetSpecialOrderCouponList,
  ApiCouponTake,
} from '@/api/views/coupon.js';
import { ApiGetSpecialOrderCardList } from '@/api/views/card.js';
import { ApiCardGet } from '@/api/views/gift.js';
import {
  ApigoldToPtbExchangeList,
  ApigoldExchangeRaffle,
  ApigoldToPtbExchange,
  ApiGameGetGameCardList,
} from '@/api/views/gold.js';
import yyXhSelect from '@/components/yy-xh-select/index.vue';
import {
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_showActivityByAction,
  BOX_goToCouponCenter,
  BOX_takeGift,
  BOX_takeCoupon,
  platform,
} from '@/utils/box.uni.js';
export default {
  components: {
    yyXhSelect,
    exchangeGameSearchPopup,
  },
  data() {
    return {
      navbarOpacity: 0,
      platformCoinLoading: false,
      gamePropsLoading: false,
      userSvipInfo: {},
      coinTab: 1,
      couponSwiperOption: {
        slidesPerView: 1,
        spaceBetween: 20,
        autoHeight: true,
        resizeObserver: true,
        observer: true,
        observeSlideChildren: true,
        pagination: {
          el: '.coupon-pagination',
        },
      },
      cardSwiperOption: {
        slidesPerView: 1,
        spaceBetween: 20,
        autoHeight: true,
        resizeObserver: true,
        observer: true,
        observeSlideChildren: true,
        pagination: {
          el: '.card-pagination',
        },
      },
      isCoinPopupShow: false,
      isSvipPopupShow: false,
      exchangeList: [],
      specialOrderCouponList: [],
      specialOrderCardList: [],
      exchangeListLoading: false,
      specialOrderCouponListLoading: false,
      specialOrderCardListLoading: false,
      xhDialogShow: false, //小号选择弹窗
      xhSelectGame: {},
      getWelfareType: '',
      giftCopyPopupShow: false,
      giftSelected: {},
      goldCardList: [],
      currentGame: {},
      moreGamePopup: false,
      xhGameId: 0,
      exchangeGift: {},
      giftCopyPopupShow: false,
      isExpand: false,
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.getSvipInfo();
    await this.getExchangeList();
    await this.getSpecialOrderCouponList();
    await this.getSpecialOrderCardList();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async getSvipInfo() {
      const res = await ApiGetUserSvipInfo();
      this.userSvipInfo = res.data;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async getExchangeList() {
      this.exchangeListLoading = true;
      try {
        const res = await ApigoldToPtbExchangeList({
          is_reach: 1,
        });
        this.exchangeList = res.data.list;
        this.goldCardList = res.data.card_list.gold_card_list;
        this.currentGame = this.goldCardList[0];
      } finally {
        this.exchangeListLoading = false;
      }
    },
    async getSpecialOrderCouponList() {
      this.specialOrderCouponListLoading = true;
      try {
        const res = await ApiGetSpecialOrderCouponList({
          is_list: 1,
          type: 1,
          is_648: 0,
        });
        let list = [...res.data.coupon_list];
        if (list.length) {
          if (list.length > 3) {
            let arr = [];
            list.forEach((item, index) => {
              arr.push(item);
              if (index == list.length - 1) {
                this.specialOrderCouponList.push(arr);
                return false;
              }
              if ((index + 1) % 3 == 0) {
                this.specialOrderCouponList.push(arr);
                arr = [];
              }
            });
          } else {
            this.specialOrderCouponList.push(list);
          }
        }
      } finally {
        this.specialOrderCouponListLoading = false;
      }
    },
    async getSpecialOrderCardList() {
      this.specialOrderCardListLoading = true;
      try {
        const res = await ApiGetSpecialOrderCardList({
          is_list: 1,
          page: 1,
          listRows: 9,
        });
        let list = [...res.data.list];
        if (list.length) {
          if (list.length > 3) {
            let arr = [];
            list.forEach((item, index) => {
              arr.push(item);
              if (index == list.length - 1) {
                this.specialOrderCardList.push(arr);
                return false;
              }
              if ((index + 1) % 3 == 0) {
                this.specialOrderCardList.push(arr);
                arr = [];
              }
            });
          } else {
            this.specialOrderCardList.push(list);
          }
        }
      } finally {
        this.specialOrderCardListLoading = false;
      }
    },
    toSvip() {
      BOX_openInNewWindow(
        { name: 'Svip' },
        { url: `${window.location.origin}/#/svip` },
      );
    },
    toGoldCoinExchange() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },
    toGoldCoin() {
      BOX_openInNewWindow(
        { name: 'GoldCoin' },
        { url: `${window.location.origin}/#/gold_coin_task` },
      );
    },
    toCouponCenter() {
      BOX_goToCouponCenter(
        { name: 'CouponCenter', params: { index: 1 } },
        { position: 1 },
      );
    },
    async getPtb(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (!item.exchange_status) {
        this.isCoinPopupShow = true;
        return false;
      }
      this.toPage('ExchangePtb', { id: item.id });
      return false;
      this.$dialog({
        title: '确认兑换',
        message: `是否消耗${item.gold}金币${item.title}`,
        showCancelButton: true,
        cancelButtonText: this.$t('关闭'),
        confirmButtonText: this.$t('确认'),
        lockScroll: false,
      }).then(async () => {
        let res = await ApigoldToPtbExchange({
          exchange_id: item.id,
          buy_num: 1,
        });
        this.$toast('兑换成功');
        this.getExchangeList();
      });
    },
    async getCoupon(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (platform == 'android') {
        BOX_takeCoupon(item);
        return;
      }
      this.getWelfareType = 'coupon';
      this.xhDialogShow = true;
      this.xhSelectGame = item;
    },
    async getCard(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (item.get_status === false) {
        this.isCoinPopupShow = true;
        return false;
      }
      if (platform == 'android') {
        BOX_takeGift(item);
        return;
      }
      this.getWelfareType = 'card';
      this.xhDialogShow = true;
      this.xhSelectGame = item;
    },
    async getGift(xhInfo) {
      this.xhDialogShow = false;
      if (this.getWelfareType == 'coupon') {
        try {
          let res = await ApiCouponTake({
            couponId: this.xhSelectGame.id,
            xhId: xhInfo.xhId,
          });

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${this.xhSelectGame.id}`,
            voucher_name: `${this.xhSelectGame.title}`,
            voucher_amount: `${this.xhSelectGame.money}`,
          });

          this.getSpecialOrderCouponList();
        } catch {}
      } else if (this.getWelfareType == 'card') {
        try {
          let res = await ApiCardGet({
            cardId: this.xhSelectGame.id,
            xhId: xhInfo.xhId,
          });
          this.$nextTick(() => {
            this.giftSelected = res.data;
            this.giftCopyPopupShow = true;

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${this.xhSelectGame.game_id}`,
              adv_id: '暂无',
              game_name: this.xhSelectGame.titlegame,
              game_type: `${this.xhSelectGame.classid}`,
              game_size: '暂无',
              reward_type: this.xhSelectGame.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });

            this.getSpecialOrderCardList();
          });
        } catch {}
      }
    },
    clickMoreGame() {
      this.moreGamePopup = true;
    },
    async onGameSelectSuccess(info) {
      this.isExpand = false;
      let scrollEl = document.querySelector('.nav-list');
      let findOne = this.goldCardList.find(
        item => item.game_info.id === info.id,
      );
      if (!findOne) {
        this.goldCardList.unshift({
          game_info: info,
          card_info: [],
        });
        this.currentGame = this.goldCardList[0];
        this.$nextTick(() => {
          scrollEl.scrollTo({
            left: 0,
            behavior: 'smooth',
          });
        });
      } else {
        this.currentGame = findOne;
        this.$nextTick(() => {
          let index = this.goldCardList.findIndex(
            item => item.game_info.id === info.id,
          );
          scrollEl.scrollTo({
            left: index * 46,
            behavior: 'smooth',
          });
        });
      }

      await this.updatePropList();
      this.moreGamePopup = false;
    },
    async updatePropList() {
      const res = await ApiGameGetGameCardList({
        id: this.currentGame.game_info.id,
      });
      this.currentGame.card_info = res.data.list;
      this.goldCardList.find(
        item => item.game_info.id === this.currentGame.game_info.id,
      ).card_info = res.data.list;
    },
    clickNavGame(game) {
      this.isExpand = false;
      this.currentGame = game;
    },
  },
};
</script>

<style lang="less" scoped>
.welfare-page {
  background: linear-gradient(
    139deg,
    #030734 0%,
    #170d58 11%,
    #110439 55%,
    #1a0342 68%,
    #030734 100%
  );
  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20 * @rem;
  }

  .main {
    padding-bottom: 45 * @rem;
    .top-img {
      width: 100%;
      height: 223 * @rem;
      background: url(~@/assets/images/welfare/svip-welfare/top-bg.png)
        no-repeat top center;
      background-size: 375 * @rem auto;
    }
    .user-bar {
      width: 347 * @rem;
      height: 89 * @rem;
      margin: -120 * @rem auto 0;
      border-radius: 10 * @rem;
      padding: 1 * @rem;
      background: linear-gradient(
        95deg,
        rgba(124, 200, 255, 1),
        rgba(205, 99, 255, 1),
        rgba(113, 90, 255, 1)
      );
      box-sizing: border-box;

      .no-svip {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 0 15 * @rem;
        box-sizing: border-box;
        background: url(~@/assets/images/welfare/svip-welfare/top-bg.png)
          no-repeat center -102 * @rem;
        background-size: 375 * @rem auto;
        border-radius: 10 * @rem;
        .tips-text {
          flex: 1;
          min-width: 0;
          .p1 {
            width: 100%;
            height: 20 * @rem;
            line-height: 20 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #f4dfc6;
          }
          .p2 {
            width: 100%;
            height: 25 * @rem;
            line-height: 25 * @rem;
            font-weight: bold;
            font-size: 18 * @rem;
            color: #f4dfc6;
            background: linear-gradient(
              89.9999994496653deg,
              #f4e4c6 0%,
              #f2c590 97%
            );
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 10 * @rem;
          }
        }

        .no-svip-text {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 66 * @rem;
          height: 26 * @rem;
          text-align: center;
          font-weight: 500;
          font-size: 11 * @rem;
          color: #a65001;
          line-height: 14 * @rem;
          background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
          border-radius: 26 * @rem;
          margin-left: 10 * @rem;
        }
      }

      .user-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 0 15 * @rem;
        box-sizing: border-box;
        background: url(~@/assets/images/welfare/svip-welfare/top-bg.png)
          no-repeat center -102 * @rem;
        background-size: 375 * @rem auto;
        border-radius: 10 * @rem;

        .avatar {
          width: 53 * @rem;
          height: 53 * @rem;
          border-radius: 50%;
          margin-right: 20 * @rem;
        }

        .info-right {
          flex: 1;
          min-width: 0;

          .nickname {
            width: 100%;
            height: 21 * @rem;
            line-height: 21 * @rem;
            font-weight: 500;
            font-size: 16 * @rem;
            color: #f4dfc6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .end-date {
            width: 100%;
            height: 17 * @rem;
            line-height: 17 * @rem;
            font-weight: bold;
            font-size: 11 * @rem;
            background: linear-gradient(
              89.9999994496653deg,
              #f4e4c6 0%,
              #f2c590 97%
            );
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 10 * @rem;
          }
        }
      }
    }
    .coin-mall {
      width: 345 * @rem;
      margin: 30 * @rem auto;

      .title {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: auto;
          height: 34 * @rem;
        }
      }

      .mall-content {
        width: 100%;
        border-radius: 15 * @rem;
        margin-top: 31 * @rem;

        .tab-btns {
          display: flex;
          align-items: flex-end;
          width: 100%;
          height: 55 * @rem;
          padding-bottom: 23 * @rem;
          background: url(~@/assets/images/welfare/svip-welfare/first-active-tab.png)
            no-repeat;
          background-size: 100% auto;

          &.last {
            background-image: url(~@/assets/images/welfare/svip-welfare/last-active-tab.png);
          }

          .tab-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            height: 40 * @rem;
            font-weight: 400;
            font-size: 14 * @rem;
            color: rgba(244, 223, 198, 0.77);
            text-align: center;
            border-bottom: none;
            box-sizing: border-box;

            &.active {
              height: 56 * @rem;
              font-weight: 600;
              font-size: 18 * @rem;
              color: #ffdca8;
              position: relative;
            }
          }
        }

        .content {
          width: 100%;
          padding: 0 12 * @rem 12 * @rem;
          background-color: #1d193f;
          border: 1 * @rem solid #2c2b56;
          border-top: none;
          border-bottom-right-radius: 15 * @rem;
          border-bottom-left-radius: 15 * @rem;
          box-sizing: border-box;
          position: relative;
          .bottom-color-border {
            width: 30%;
            height: 1 * @rem;
            background: linear-gradient(
              to right,
              #2c2b56 0%,
              #634caa 50%,
              #2c2b56 100%
            );
            position: absolute;
            bottom: -1 * @rem;
            left: 30%;
            z-index: 1;
          }

          &::before {
            content: '';
            display: block;
            width: 1 * @rem;
            height: 50%;
            background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
            position: absolute;
            top: 10%;
            left: -1 * @rem;
            z-index: 1;
          }

          &::after {
            content: '';
            display: block;
            width: 1 * @rem;
            height: 50%;
            background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
            position: absolute;
            bottom: 10%;
            right: -1 * @rem;
            z-index: 1;
          }
        }
      }

      .platform-coin-list {
        .platform-coin-item {
          width: 100%;
          height: 75 * @rem;
          border-radius: 8 * @rem;
          padding: 1 * @rem;
          box-sizing: border-box;
          background: linear-gradient(
            93deg,
            rgba(153, 210, 255, 1),
            rgba(117, 84, 187, 1),
            rgba(170, 172, 188, 0.53),
            rgba(255, 245, 157, 0.82),
            rgba(169, 181, 216, 0.54),
            rgba(104, 141, 234, 1)
          );
          margin-bottom: 14 * @rem;

          &:last-of-type {
            margin-bottom: 0;
          }

          .cont {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 0 12 * @rem;
            box-sizing: border-box;
            background: linear-gradient(
              93deg,
              #1e1449 0%,
              #0b0e2f 55%,
              #0a102d 95%
            );
            border-radius: 8 * @rem;

            .icon {
              display: block;
              width: 46 * @rem;
              height: 34 * @rem;
              margin-right: 11 * @rem;
            }

            .info {
              flex: 1;
              min-width: 0;

              .name {
                width: 100%;
                height: 22 * @rem;
                line-height: 22 * @rem;
                font-weight: 400;
                font-size: 16 * @rem;
                color: #ffdca8;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .desc {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #ffdca8;
                line-height: 13 * @rem;
                text-align: center;
                margin-top: 9 * @rem;

                &::before {
                  content: '';
                  display: block;
                  width: 14 * @rem;
                  height: 14 * @rem;
                  background: url(~@/assets/images/welfare/svip-welfare/pay-coin-icon.png)
                    no-repeat;
                  background-size: contain;
                  margin-right: 4 * @rem;
                }
              }
            }

            .right {
              flex-shrink: 0;
              margin-left: 10 * @rem;
              width: 90 * @rem;
              display: flex;
              flex-wrap: wrap;
              justify-content: flex-end;

              .btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 65 * @rem;
                height: 24 * @rem;
                line-height: 24 * @rem;
                text-align: center;
                font-weight: 500;
                font-size: 11 * @rem;
                color: #a55001;
                border-radius: 26 * @rem;
                background: linear-gradient(#f4dfc6, #ffae63);
              }

              .count {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 15 * @rem;
                line-height: 15 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #ffdca8;
                text-align: center;
                margin-top: 8 * @rem;
              }
            }
          }
        }
      }

      .game-props-list {
        .game-props-item {
          width: 321 * @rem;
          height: 75 * @rem;
          border-radius: 8 * @rem;
          padding: 1 * @rem;
          box-sizing: border-box;
          background: linear-gradient(
            93deg,
            rgba(153, 210, 255, 1),
            rgba(117, 84, 187, 1),
            rgba(170, 172, 188, 0.53),
            rgba(255, 245, 157, 0.82),
            rgba(169, 181, 216, 0.54),
            rgba(104, 141, 234, 1)
          );
          margin-bottom: 14 * @rem;
          position: relative;

          &:last-of-type {
            margin-bottom: 0;
          }

          .cont {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 0 12 * @rem;
            box-sizing: border-box;
            background: linear-gradient(
              93deg,
              #1e1449 0%,
              #0b0e2f 55%,
              #0a102d 95%
            );
            border-radius: 8 * @rem;

            .icon {
              display: block;
              width: 46 * @rem;
              height: 34 * @rem;
              background: url(~@/assets/images/welfare/svip-welfare/coin.png)
                no-repeat;
              background-size: contain;
              margin-right: 11 * @rem;
            }

            .info {
              flex: 1;
              min-width: 0;

              .name {
                width: 100%;
                height: 22 * @rem;
                line-height: 22 * @rem;
                font-weight: 400;
                font-size: 16 * @rem;
                color: #ffdca8;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .desc {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #ffdca8;
                line-height: 13 * @rem;
                text-align: center;
                margin-top: 9 * @rem;

                &::before {
                  content: '';
                  display: block;
                  width: 14 * @rem;
                  height: 14 * @rem;
                  background: url(~@/assets/images/welfare/svip-welfare/pay-coin-icon.png)
                    no-repeat;
                  background-size: contain;
                  margin-right: 4 * @rem;
                }
              }
            }

            .right {
              flex-shrink: 0;
              margin-left: 10 * @rem;

              .btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 65 * @rem;
                height: 24 * @rem;
                line-height: 24 * @rem;
                text-align: center;
                font-weight: 500;
                font-size: 11 * @rem;
                color: #a55001;
                border-radius: 26 * @rem;
                background: linear-gradient(#f4dfc6, #ffae63);
              }
            }
          }

          .limit {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 16 * @rem;
            padding: 0 8 * @rem;
            background-color: #792bc7;
            border-top-right-radius: 8 * @rem;
            border-bottom-left-radius: 8 * @rem;
            font-weight: 400;
            font-size: 10 * @rem;
            color: #ffdca8;
            line-height: 10 * @rem;
            text-align: center;
            position: absolute;
            top: 0;
            right: 0;
          }
        }
      }

      .more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #ffdca8;
        line-height: 14 * @rem;
        text-align: center;
        padding: 15 * @rem 0;

        &::after {
          content: '';
          display: block;
          width: 9 * @rem;
          height: 9 * @rem;
          margin-left: 2 * @rem;
          background: url(~@/assets/images/welfare/svip-welfare/double-right-arrow.png)
            no-repeat;
          background-size: contain;
        }
      }
    }
    .exchange-content {
      position: relative;
      .more-game-btn {
        width: 23 * @rem;
        height: 57 * @rem;
        background: url(~@/assets/images/clock-in/more-game-btn.png) center
          center no-repeat;
        background-size: 23 * @rem 57 * @rem;
        position: absolute;
        right: -12 * @rem;
        top: 6 * @rem;
        z-index: 10;
      }
      .nav-list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        overflow-x: auto;
        position: relative;
        &::-webkit-scrollbar {
          display: none;
        }
        .nav-item {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          height: 66 * @rem;
          margin-right: 10 * @rem;
          .game-icon {
            width: 46 * @rem;
            height: 46 * @rem;
            border-radius: 10 * @rem;
            overflow: hidden;
          }
          .game-info {
            margin-left: 8 * @rem;
            min-width: 0;
            flex: 1;
            .game-title {
              display: flex;
              align-items: center;
              overflow: hidden;
              .title-content {
                display: flex;
                align-items: center;
              }
              .title-text {
                flex: 1;
                min-width: 0;
                font-size: 14 * @rem;
                color: #ffdca8;
                font-weight: 600;
                overflow: hidden;
                white-space: nowrap;
              }
              .title-tag {
                box-sizing: border-box;
                background: rgba(161, 103, 255, 0.19);
                border-radius: 8 * @rem 8 * @rem 8 * @rem 0;
                height: 15 * @rem;
                margin-left: 4 * @rem;
                font-size: 10 * @rem;
                color: #8b6eff;
                font-weight: 400;
                white-space: nowrap;
                padding: 1 * @rem 4 * @rem;
                margin-right: -5 * @rem;
                margin-right: 30 * @rem;
              }
            }
            .game-discount {
              display: inline-block;
              border: 0.5 * @rem solid #f5dfc6;
              border-radius: 2 * @rem;
              overflow: hidden;
              height: 17 * @rem;
              margin-top: 5 * @rem;
              .left {
                display: inline-block;
                background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
                height: 17 * @rem;
                line-height: 17 * @rem;
                font-size: 10 * @rem;
                color: #a55001;
                font-weight: 600;
                padding: 0 3 * @rem;
              }
              .right {
                display: inline-block;
                font-size: 10 * @rem;
                color: #f5dfc6;
                height: 17 * @rem;
                line-height: 17 * @rem;
                padding: 0 4 * @rem;
              }
            }
          }
          &.active {
            flex-shrink: 0;
            width: 184 * @rem;
            height: 68 * @rem;
            margin-right: 12 * @rem;
            padding: 0 10 * @rem;
            border-radius: 12 * @rem;
            position: relative;
            z-index: 2;
            &::before {
              content: '';
              background: linear-gradient(
                93deg,
                rgba(153, 210, 255, 1),
                rgba(117, 84, 187, 1),
                rgba(170, 172, 188, 0.53),
                rgba(255, 245, 157, 0.82),
                rgba(169, 181, 216, 0.54),
                rgba(104, 141, 234, 1)
              );
              width: 184 * @rem;
              height: 68 * @rem;
              position: absolute;
              left: 0;
              left: 0;
              border-radius: 12 * @rem;
              z-index: -2;
            }
            &::after {
              content: '';
              background: linear-gradient(
                93deg,
                #1e1449 0%,
                #0b0e2f 55%,
                #0a102d 95%
              );
              width: 182 * @rem;
              height: 66 * @rem;
              position: absolute;
              left: 1 * @rem;
              top: 1 * @rem;
              border-radius: 10 * @rem;
              z-index: -1;
            }
          }
        }
      }

      .exchange-list {
        padding-top: 7 * @rem;

        .exchange-item-bg {
          width: 320 * @rem;
          height: 75 * @rem;
          padding: 1 * @rem;
          background: linear-gradient(
            93deg,
            rgba(153, 210, 255, 1),
            rgba(117, 84, 187, 1),
            rgba(170, 172, 188, 0.53),
            rgba(255, 245, 157, 0.82),
            rgba(169, 181, 216, 0.54),
            rgba(104, 141, 234, 1)
          );
          border-radius: 9 * @rem;
          margin-top: 9 * @rem;
        }
        .exchange-item {
          box-sizing: border-box;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          position: relative;
          overflow: hidden;
          background: linear-gradient(
            93deg,
            #1e1449 0%,
            #0b0e2f 55%,
            #0a102d 95%
          );
          border-radius: 9 * @rem;
          padding: 0 10 * @rem;
          .limit {
            position: absolute;
            right: -1 * @rem;
            top: -1 * @rem;
            height: 16 * @rem;
            background-color: #792bc7;
            background-size: 58 * @rem 21 * @rem;
            padding: 0 8 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10 * @rem;
            color: #ffdca8;
            border-radius: 0 8 * @rem 0 8 * @rem;
          }
          .exchange-icon {
            width: 45 * @rem;
            height: 45 * @rem;
            border-radius: 11 * @rem;
            background: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .exchange-info {
            flex: 1;
            min-width: 0;
            margin-left: 12 * @rem;
            .item-title {
              font-size: 16 * @rem;
              color: #ffdca8;
              font-weight: 400;
              line-height: 19 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .cost {
              display: flex;
              align-items: center;
              font-size: 11 * @rem;
              color: #ffdca8;
              line-height: 14 * @rem;
              margin-top: 8 * @rem;

              &::before {
                content: '';
                display: block;
                width: 14 * @rem;
                height: 14 * @rem;
                background: url(~@/assets/images/welfare/svip-welfare/pay-coin-icon.png)
                  no-repeat;
                background-size: contain;
                margin-right: 4 * @rem;
              }
            }
          }
          .exchange-btn {
            width: 65 * @rem;
            height: 24 * @rem;
            color: #a55001;
            background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
            font-size: 11 * @rem;
            font-weight: 600;
            border-radius: 26 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .exchange-down {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #ffdca8;
        padding: 10 * @rem 0 0;
        i {
          display: block;
          width: 10 * @rem;
          height: 7 * @rem;
          margin-left: 4 * @rem;
          background: url(~@/assets/images/down-icon.png) center center
            no-repeat;
          background-size: 10 * @rem 7 * @rem;
        }
        &.up {
          i {
            transform: rotate(180deg);
          }
        }
      }
    }
    .coupon-box {
      width: 345 * @rem;
      padding: 16 * @rem 12 * @rem 12 * @rem;
      margin: 30 * @rem auto;
      background: linear-gradient(
        227deg,
        #221c48 0%,
        rgba(15, 16, 37, 0.77) 44%,
        #12113f 97%
      );
      border-radius: 15 * @rem;
      border: 1 * @rem solid #2c2b56;
      box-sizing: border-box;
      position: relative;

      .top-color-border {
        width: 30%;
        height: 1 * @rem;
        background: linear-gradient(
          to right,
          #2c2b56 0%,
          #4c6caa 50%,
          #2c2b56 100%
        );
        position: absolute;
        top: -1 * @rem;
        right: 30%;
        z-index: 1;
      }

      &::before {
        content: '';
        display: block;
        width: 1 * @rem;
        height: 50%;
        background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
        position: absolute;
        top: 10%;
        left: -1 * @rem;
        z-index: 1;
      }

      &::after {
        content: '';
        display: block;
        width: 1 * @rem;
        height: 50%;
        background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
        position: absolute;
        bottom: 10%;
        right: -1 * @rem;
        z-index: 1;
      }

      .title {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: auto;
          height: 34 * @rem;
        }
      }
      .coupon-list {
        width: 100%;
        position: relative;
      }
      .coupon-group {
        margin-top: 16 * @rem;
        margin-bottom: 25 * @rem;
      }
      .coupon-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 75 * @rem;
        border-radius: 8 * @rem;
        padding: 1 * @rem;
        margin-bottom: 14 * @rem;
        box-sizing: border-box;
        background: linear-gradient(
          93deg,
          rgba(153, 210, 255, 1),
          rgba(117, 84, 187, 1),
          rgba(170, 172, 188, 0.53),
          rgba(255, 245, 157, 0.82),
          rgba(169, 181, 216, 0.54),
          rgba(104, 141, 234, 1)
        );

        .coupon-left {
          display: block;
          width: 56 * @rem;
          margin-right: 18 * @rem;

          .price {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            height: 26 * @rem;
            text-align: center;

            em {
              font-size: 12 * @rem;
              line-height: 16 * @rem;
              color: #f4dfc6;
              background: linear-gradient(
                89.9999994496653deg,
                #f4e4c6 0%,
                #f2c590 97%
              );
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            span {
              font-size: 26 * @rem;
              line-height: 26 * @rem;
              font-weight: bold;
              color: #f4dfc6;
              background: linear-gradient(#f4e4c6 0%, #f2c590 97%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .threshold {
            height: 11 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: rgba(244, 223, 198, 0.7);
            line-height: 11 * @rem;
            text-align: center;
            margin-top: 10 * @rem;
            white-space: nowrap;
          }
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            width: 100%;
            max-height: 42 * @rem;
            font-weight: 400;
            font-size: 14 * @rem;
            color: #ffdca8;
            line-height: 21 * @rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .date {
            width: 100%;
            height: 11 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: rgba(244, 223, 198, 0.7);
            line-height: 11 * @rem;
            margin-top: 5 * @rem;
          }
        }

        .coupon-right {
          margin-left: 10 * @rem;
          .get-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 52 * @rem;
            height: 23 * @rem;
            line-height: 23 * @rem;
            font-weight: 500;
            font-size: 11 * @rem;
            color: #a55001;
            text-align: center;
            background: #ffdca8;
            background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
            border-radius: 24 * @rem;
          }
        }
      }
      .coupon-color-bg {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        border-radius: 8 * @rem;
        padding: 0 12 * @rem;
        background: linear-gradient(
          93deg,
          #1e1449 0%,
          #0b0e2f 55%,
          #0a102d 95%
        );
      }
    }
    .card-box {
      width: 345 * @rem;
      padding: 16 * @rem 12 * @rem 12 * @rem;
      margin: 30 * @rem auto 0;
      background: linear-gradient(
        227deg,
        #221c48 0%,
        rgba(15, 16, 37, 0.77) 44%,
        #12113f 97%
      );
      border-radius: 15 * @rem;
      border: 1 * @rem solid #2c2b56;
      box-sizing: border-box;
      position: relative;

      .top-color-border {
        width: 30%;
        height: 1 * @rem;
        background: linear-gradient(
          to right,
          #2c2b56 0%,
          #4c6caa 50%,
          #2c2b56 100%
        );
        position: absolute;
        top: -1 * @rem;
        right: 30%;
        z-index: 1;
      }

      &::before {
        content: '';
        display: block;
        width: 1 * @rem;
        height: 50%;
        background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
        position: absolute;
        top: 10%;
        left: -1 * @rem;
        z-index: 1;
      }

      &::after {
        content: '';
        display: block;
        width: 1 * @rem;
        height: 50%;
        background: linear-gradient(#2c2b56 0%, #634caa 50%, #2c2b56 100%);
        position: absolute;
        bottom: 10%;
        right: -1 * @rem;
        z-index: 1;
      }

      .title {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: auto;
          height: 34 * @rem;
        }
      }

      .card-list {
        width: 100%;
        position: relative;
      }
      .card-group {
        margin-top: 16 * @rem;
        margin-bottom: 25 * @rem;
      }
      .card-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 91 * @rem;
        border-radius: 8 * @rem;
        padding: 1 * @rem;
        margin-bottom: 14 * @rem;
        box-sizing: border-box;
        background: linear-gradient(
          93deg,
          rgba(153, 210, 255, 1),
          rgba(117, 84, 187, 1),
          rgba(170, 172, 188, 0.53),
          rgba(255, 245, 157, 0.82),
          rgba(169, 181, 216, 0.54),
          rgba(104, 141, 234, 1)
        );

        .card-info {
          display: flex;
          align-items: center;
          width: 100%;
          margin-top: 14 * @rem;

          .card-left {
            width: 36 * @rem;
            height: 36 * @rem;
            margin-right: 7 * @rem;
          }

          .info {
            flex: 1;
            min-width: 0;

            .name {
              width: 100%;
              height: 14 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #ffdca8;
              line-height: 14 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .desc {
              width: 100%;
              height: 11 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: rgba(244, 223, 198, 0.7);
              line-height: 11 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-top: 12 * @rem;
            }
          }

          .card-right {
            margin-left: 10 * @rem;
            .get-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 52 * @rem;
              height: 23 * @rem;
              line-height: 23 * @rem;
              font-weight: bold;
              font-size: 11 * @rem;
              color: #a55001;
              text-align: center;
              background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
              border-radius: 26 * @rem;

              &.not {
                background: #2f2f3f;
                color: rgba(244, 223, 198, 0.7);
              }
            }
          }
        }
        .card-count {
          display: flex;
          align-items: center;
          margin-top: 16 * @rem;

          .process {
            flex: 1;
            min-width: 0;
            height: 5 * @rem;
            background: #200e32;
            border-radius: 19 * @rem;
            margin-right: 10 * @rem;
            position: relative;

            span {
              display: block;
              height: 5 * @rem;
              height: 5 * @rem;
              background: linear-gradient(
                90deg,
                #935fee 0%,
                #c861ee 37%,
                #f1c358 69%,
                #f5e3b1 98%
              );
              border-radius: 19 * @rem;
              position: absolute;
              top: 0;
              left: 0;
            }
          }

          .number {
            display: block;
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #ffdca8;
            padding: 0 3 * @rem;
          }
        }
      }
      .card-color-bg {
        width: 100%;
        height: 100%;
        border-radius: 8 * @rem;
        padding: 0 12 * @rem;
        box-sizing: border-box;
        background: linear-gradient(
          93deg,
          #1e1449 0%,
          #0b0e2f 55%,
          #0a102d 95%
        );
      }
    }
  }

  ::v-deep {
    .swiper-pagination {
      bottom: 5 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 5 * @rem;

      .swiper-pagination-bullet {
        display: block;
        width: 5 * @rem;
        height: 5 * @rem;
        margin-right: 5 * @rem;
        border-radius: 10 * @rem;
        background: rgba(255, 220, 168, 0.25);

        &:last-of-type {
          margin-right: 0;
        }

        &.swiper-pagination-bullet-active {
          width: 15 * @rem;
          background-color: #ffdca8;
        }
      }
    }
  }
  .text-scroll {
    flex-shrink: 0;
    flex-grow: 1;
    white-space: nowrap;
    animation: scroll-left 5s linear forwards infinite;
  }
  @keyframes scroll-left {
    0% {
      transform: translateX(0%);
    }
    20% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-50%);
    }
  }
}

.popup {
  ::v-deep {
    .van-dialog {
      background-color: transparent;
      border-radius: 0;
    }
  }

  .popup-content {
    width: 100%;
    padding: 1 * @rem;
    box-sizing: border-box;
    background: linear-gradient(
      93deg,
      rgba(153, 210, 255, 1),
      rgba(117, 84, 187, 1),
      rgba(170, 172, 188, 0.53),
      rgba(255, 245, 157, 0.82),
      rgba(169, 181, 216, 0.54),
      rgba(104, 141, 234, 1)
    );
    border-radius: 8 * @rem;

    .popup-bg {
      background: linear-gradient(93deg, #1e1449 0%, #0b0e2f 55%, #0a102d 95%);
      border-radius: 8 * @rem;
      padding-bottom: 36 * @rem;
    }

    .text {
      width: 250 * @rem;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #ffdca8;
      line-height: 20 * @rem;
      text-align: center;
      padding-top: 36 * @rem;
      margin: 0 auto;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 149 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      text-align: center;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #a55001;
      background: linear-gradient(180deg, #f4dfc6 0%, #ffae63 98%);
      border-radius: 26 * @rem;
      margin: 46 * @rem auto 0;
    }
  }
}
</style>
