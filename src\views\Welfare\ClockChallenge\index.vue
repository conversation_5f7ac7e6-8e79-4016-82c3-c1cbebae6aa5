<template>
  <div class="clock-challenge-page">
    <nav-bar-2 bgStyle="transparent-white" :placeholder="false" :azShow="true">
    </nav-bar-2>
    <div class="top-container">
      <div class="clock-num">
        当前共<span>{{ info.in_count }}人</span>挑战打卡
      </div>
    </div>
    <template>
      <div class="operation-board">
        <div class="price-bar">
          <div
            class="num-item"
            v-for="(item, index) in toFixed(info.gold_sum)"
            :key="index"
          >
            {{ item }}
          </div>
        </div>
        <div class="btn-bar">
          <div
            class="btn"
            :class="{
              'join-btn': index == 0,
              'clock-btn': index == 1,
              'hide': item.is_hide,
              'no': !item.is_clock,
            }"
            v-for="(item, index) in info.clock"
            :key="index"
            @click="clickSign(index, item.is_clock)"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="rule" @click="rulePopup = true">查看活动规则></div>
      </div>
      <div class="section">
        <div class="section-title">今日挑战情况</div>
        <div class="situation" v-if="info.tzqk">
          <div class="situation-item">
            <div class="title">挑战成功</div>
            <div class="content">{{ info.tzqk.success }}人</div>
          </div>
          <div class="situation-item">
            <div class="title">挑战失败</div>
            <div class="content">{{ info.tzqk.fail }}人</div>
          </div>
          <div class="situation-item">
            <div class="title">人均瓜分</div>
            <div class="content">{{ info.tzqk.gold }}金币</div>
          </div>
        </div>
        <div class="tips gray">每日11:00公布当日打卡情况</div>
      </div>
      <div class="section">
        <div class="section-title">天选之人</div>
        <div class="user-list" v-if="info.txzr && info.txzr.length">
          <div
            class="user-item"
            v-for="(item, index) in info.txzr"
            :key="index"
          >
            <user-avatar
              class="user-avatar"
              :src="item.mem_avatar"
              :self="false"
            ></user-avatar>
            <div class="user-nickname">{{ item.mem_username }}</div>
          </div>
        </div>
        <div class="user-notice" v-else>11:00 公布天选之人</div>
        <div class="tips">每日从打卡成功的用户中抽取3个奖励3733礼金</div>
      </div>
      <div class="section">
        <div class="section-title">嘉奖令</div>
        <div class="clock-num">
          本月已打卡<span>{{ info.mouth_count }}</span
          >天
        </div>
        <div class="price">
          <div
            class="price-item"
            v-for="(item, index) in info.jjl"
            :key="index"
          >
            <div class="title">{{ item.title }}</div>
            <div class="icon">
              <img :src="item.img" alt="" />
              <div class="tag">{{ item.desc }}</div>
            </div>
            <div class="desc">{{ item.price }}</div>
            <div class="get get-no btn" v-if="item.is_receive">已领取</div>
            <div class="get get-no btn" v-else-if="!item.is_ok">未完成</div>
            <div class="get btn" @click="receiveReward(index + 1)" v-else>
              领取
            </div>
          </div>
        </div>
        <div class="tips gray">领取时间为每月最后一天的10：00-23：59</div>
      </div>
    </template>
    <!-- 确认报名 -->
    <van-popup
      v-model="signUpConfirmPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-title">确认报名</div>
      <div class="popup-content">确认消耗 <span>500金币</span> 报名活动？</div>
      <div class="bottom-bar">
        <div @click="signUpConfirmPopup = false" class="bottom-btn no btn">
          取消
        </div>
        <div @click="parTake(0)" class="bottom-btn btn"> 确认参加 </div>
      </div>
    </van-popup>
    <!-- 报名成功 -->
    <van-popup
      v-model="signUpSuccessPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-title">报名成功</div>
      <div class="popup-content">明日07:00 - 10:00打卡<br />记得来打卡哦~</div>
      <div class="bottom-bar">
        <div @click="signUpSuccessPopup = false" class="bottom-btn btn">
          知道了
        </div>
      </div>
    </van-popup>
    <!-- 打卡成功 -->
    <van-popup
      v-model="clockSuccessPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-title">打卡成功</div>
      <div class="popup-content">
        奖励将于11:00之后陆续发放天选之人<br />也将随之产生敬请期待
      </div>
      <div class="bottom-bar">
        <div @click="clockSuccessPopup = false" class="bottom-btn btn">
          知道了
        </div>
      </div>
    </van-popup>
    <!-- 活动规则 -->
    <van-popup
      v-model="rulePopup"
      :lock-scroll="true"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup rule-popup"
    >
      <div class="popup-title">活动规则</div>
      <div class="popup-content rule-content" v-html="ruleContent"></div>
      <div class="bottom-bar">
        <div @click="rulePopup = false" class="bottom-btn btn">知道了</div>
      </div>
    </van-popup>

    <!-- 奖励领取 -->
    <van-popup
      v-model="priceSuccessPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-title">领取成功</div>
      <div class="popup-content">
        恭喜您获得 <span>{{ priceNum }}礼金</span>
      </div>
      <div class="bottom-bar">
        <div @click="priceSuccessPopup = false" class="bottom-btn no btn">
          知道了
        </div>
        <div @click="toMyCashGift" class="bottom-btn btn"> 去查看 </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiActivityClockInIndex,
  ApiActivityClockInPartake,
  ApiActivityClockInReceiveReward,
} from '@/api/views/clockChallenge.js';
import h5Page from '@/utils/h5Page.js';
import { BOX_openInNewWindow } from '@/utils/box.uni.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  data() {
    return {
      info: {
        in_count: 0,
        gold_sum: 0,
      }, // 页面信息
      rulePopup: false, // 规则弹窗
      clockSuccessPopup: false, // 打卡成功弹窗
      signUpSuccessPopup: false, // 报名成功弹窗
      signUpConfirmPopup: false, // 报名确认弹窗
      priceSuccessPopup: false, // 奖励领取成功弹窗
      priceNum: '', // 获得的礼金
    };
  },
  computed: {
    // 规则内容
    ruleContent() {
      let activity_time = '0000.00.00-0000.00.00';
      if (this.info?.activity_time?.length) {
        activity_time = `${this.formatTime(
          this.info?.activity_time[0],
        )}-${this.formatTime(this.info?.activity_time[1])}`;
      }
      return `
        活动时间：${activity_time}<br>
        <br>
        活动说明：<br>
        1. 玩家当日（07:00-23:59）消耗500金币报名，次日早上（7:00-10:00)参与打卡，成功则平均瓜分当日奖池里的所有金币。未按时打卡则不参与瓜分。打卡奖励将于11点后陆续自动发放到账。<br>
        <br>
        2. 天选之人：每日从打卡成功的用户中抽取3个奖励3733礼金。<br>
        <br>
        3. 嘉奖令:<br>
        7天打卡奖励：完成7天打卡的玩家可领取3733礼金（前500名领取的玩家）
        21天打卡奖励：完成21天打卡的玩家可瓜分500万礼金（随机金额）
        领取时间为每月最后一天的10:00-23:59。<br>
        <br>
        温馨提示:<br>
        1. 本活动仅限使用同一设备、账号参与，如因频繁切
        换账号、设备或使用作弊软件造成无法参与后果自行承
        担。<br>
        2. 严禁任何形式违规操作，一经发现奖励不予派发且
        严肃处理。<br>
        3. 如您在活动过程中有遇到任何问题，可联系3733客服进行咨询。`;
    },
  },
  async created() {
    await this.getIndex();
  },
  methods: {
    formatTime(val) {
      let { year, date } = handleTimestamp(val);
      return `${year}-${date}`;
    },
    toMyCashGift() {
      this.priceSuccessPopup = false;
      this.$nextTick(() => {
        BOX_openInNewWindow({ name: 'MyCashGift' }, { url: h5Page.wodelijin });
      });
    },

    async clickSign(index, is_clock) {
      if (!is_clock) {
        return false;
      }
      if (index == 0) {
        this.signUpConfirmPopup = true;
      } else if (index == 1) {
        this.parTake(1);
      }
    },

    async getIndex() {
      const res = await ApiActivityClockInIndex();
      this.info = res.data;
    },

    // 包名和打卡
    async parTake(type) {
      // 0参加活动 1打卡
      try {
        const res = await ApiActivityClockInPartake({ type });
        if (res.code == 1) {
          if (type == 0) {
            this.signUpSuccessPopup = true;
          } else if (type == 1) {
            this.clockSuccessPopup = true;
          }
        }
      } finally {
        this.signUpConfirmPopup = false;
        await this.getIndex();
      }
    },

    // 领取嘉奖令
    async receiveReward(type) {
      // 1 七天 2 21天
      try {
        const res = await ApiActivityClockInReceiveReward({ type });
        this.priceNum = res.data.lijin;
        this.priceSuccessPopup = true;
      } finally {
        await this.getIndex();
      }
    },

    // 数字不满7位前面补0
    toFixed(num) {
      return ('0000000' + num).substr(-7, 7);
    },
  },
};
</script>

<style lang="less" scoped>
.clock-challenge-page {
  min-height: 100vh;
  background-color: #6011a9;
  padding-bottom: 40 * @rem;
}
.top-container {
  overflow: hidden;
  width: 100%;
  height: 393 * @rem;
  .image-bg('~@/assets/images/welfare/clock-challenge/clock-challenge-bg.png');
  .clock-num {
    font-size: 16 * @rem;
    line-height: 22 * @rem;
    color: #a74d2e;
    text-align: center;
    margin-top: 344 * @rem;
    span {
      color: #ff4200;
      font-weight: bold;
    }
  }
}
.operation-board {
  width: 100%;
  height: 205 * @rem;
  margin-top: 13 * @rem;
  margin-bottom: 16 * @rem;
  .image-bg('~@/assets/images/welfare/clock-challenge/operation-board-bg.png');
  margin-top: -1 * @rem;
  .price-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    .num-item {
      width: 28 * @rem;
      height: 46 * @rem;
      border-radius: 4 * @rem;
      background-color: #ffc786;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 8 * @rem;
      font-size: 32 * @rem;
      color: #ff4d00;
      font-weight: bold;
    }
  }
  .btn-bar {
    margin-top: 38 * @rem;
    height: 48 * @rem;
    display: flex;
    justify-content: center;
    .btn {
      &.hide {
        display: none;
      }
    }
    .join-btn {
      width: 185 * @rem;
      height: 48 * @rem;
      line-height: 48 * @rem;
      background: linear-gradient(180deg, #ff9900 0%, #ff3d00 100%);
      box-shadow: 0 * @rem 4 * @rem 0 * @rem 0 * @rem #c13500;
      border-radius: 32 * @rem 32 * @rem 32 * @rem 32 * @rem;
      font-size: 18 * @rem;
      color: #ffffff;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      &.no {
        background: linear-gradient(180deg, #d7d7d7 0%, #d0d0d0 41%);
        box-shadow: 0 * @rem 4 * @rem 0 * @rem 0 * @rem #b4b4b4;
      }
    }
    .clock-btn {
      width: 140 * @rem;
      height: 48 * @rem;
      line-height: 48 * @rem;
      background: linear-gradient(180deg, #ff9900 0%, #ff3d00 100%);
      box-shadow: 0 * @rem 4 * @rem 0 * @rem 0 * @rem #c13500;
      border-radius: 32 * @rem 32 * @rem 32 * @rem 32 * @rem;
      font-size: 18 * @rem;
      color: #ffffff;
      font-weight: bold;
      display: flex;
      justify-content: center;
      margin-left: 10 * @rem;
      white-space: nowrap;
      &.no {
        background: linear-gradient(180deg, #d7d7d7 0%, #d0d0d0 41%);
        box-shadow: 0 * @rem 4 * @rem 0 * @rem 0 * @rem #b4b4b4;
      }
    }
  }
  .rule {
    margin-top: 24 * @rem;
    font-size: 14 * @rem;
    color: #8c5c3e;
    line-height: 20 * @rem;
    text-align: center;
  }
}
.section {
  box-sizing: border-box;
  background-color: #fbf6f1;
  border-radius: 10 * @rem;
  margin: 20 * @rem 8 * @rem 0;
  padding-bottom: 12 * @rem;
  .section-title {
    box-sizing: border-box;
    margin: 0 auto;
    width: 194 * @rem;
    height: 38 * @rem;
    .image-bg('~@/assets/images/welfare/clock-challenge/section-title-bg.png');
    display: flex;
    justify-content: center;
    font-size: 18 * @rem;
    font-weight: bold;
    color: #ffffff;
    padding-top: 3 * @rem;
  }
  .situation {
    display: flex;
    justify-content: center;
    margin-bottom: 30 * @rem;
    margin-top: 24 * @rem;
    .situation-item {
      width: 100 * @rem;
      .title {
        box-sizing: border-box;
        padding: 0 5 * @rem;
        line-height: 18 * @rem;
        font-size: 14 * @rem;
        color: #8c5c3e;
        text-align: center;
        white-space: nowrap;
      }
      .content {
        box-sizing: border-box;
        padding: 0 5 * @rem;
        line-height: 19 * @rem;
        font-size: 15 * @rem;
        color: #ff5001;
        font-weight: bold;
        text-align: center;
        white-space: nowrap;
        margin-top: 12 * @rem;
      }
    }
  }
  .user-list {
    display: flex;
    justify-content: center;
    margin: 24 * @rem auto 20 * @rem;
    .user-item {
      width: 100 * @rem;
      .user-avatar {
        width: 40 * @rem;
        height: 40 * @rem;
        margin: 0 auto;
      }
      .user-nickname {
        box-sizing: border-box;
        padding: 0 5 * @rem;
        margin-top: 8 * @rem;
        line-height: 15 * @rem;
        font-size: 12 * @rem;
        color: #8c5c3e;
        font-weight: bold;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .user-notice {
    width: 248 * @rem;
    height: 88 * @rem;
    .image-bg('~@/assets/images/welfare/clock-challenge/user-notice-bg.png');
    margin: 7 * @rem auto 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16 * @rem;
    color: #844c25aa;
    font-weight: bold;
  }
  .clock-num {
    width: 231 * @rem;
    height: 18 * @rem;
    .image-bg('~@/assets/images/welfare/clock-challenge/clock-num-bg.png');
    font-size: 14 * @rem;
    color: #8c5c3e;
    font-weight: bold;
    margin: 17 * @rem auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      color: #ff5001;
      font-weight: bold;
    }
  }
  .price {
    display: flex;
    justify-content: center;
    margin-top: 15 * @rem;
    margin-bottom: 12 * @rem;
    .price-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 150 * @rem;
      .title {
        font-size: 14 * @rem;
        line-height: 18 * @rem;
        color: #8c5c3e;
        font-weight: bold;
      }
      .icon {
        margin-top: 4 * @rem;
        position: relative;
        width: 80 * @rem;
        height: 60 * @rem;
        .tag {
          position: absolute;
          right: -26 * @rem;
          top: -4 * @rem;
          width: 46 * @rem;
          height: 18 * @rem;
          background: #ff1e2f;
          border-radius: 9 * @rem;
          font-size: 10 * @rem;
          color: #fff;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .desc {
        font-size: 12 * @rem;
        color: #ff5001;
        font-weight: bold;
        margin-top: 4 * @rem;
        line-height: 15 * @rem;
      }
      .get {
        width: 70 * @rem;
        height: 24 * @rem;
        background: linear-gradient(180deg, #ff9900 0%, #ff3d00 100%);
        box-shadow: 0 * @rem 2 * @rem 0 * @rem 0 * @rem #c13500;
        border-radius: 32 * @rem 32 * @rem 32 * @rem 32 * @rem;
        display: flex;
        justify-content: center;
        font-size: 12 * @rem;
        line-height: 26 * @rem;
        color: #ffffff;
        font-weight: bold;
        margin-top: 10 * @rem;
        &.get-no {
          background: linear-gradient(180deg, #d7d7d7 0%, #d0d0d0 41%);
          box-shadow: 0 * @rem 2 * @rem 0 * @rem 0 * @rem #b4b4b4;
        }
      }
    }
  }

  .tips {
    font-size: 12 * @rem;
    color: #ff5001;
    line-height: 17 * @rem;
    text-align: center;
    &.gray {
      color: #8c5c3e;
    }
  }
}
.popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 20 * @rem 10 * @rem;
  border-radius: 20 * @rem;
  &.rule-popup {
    .rule-content {
      min-height: 120 * @rem;
      max-height: 300 * @rem;
      overflow-y: auto;
      margin-top: 20 * @rem;
      margin-bottom: 26 * @rem;
      text-align: left;
    }
  }
  .popup-title {
    font-size: 16 * @rem;
    color: #333333;
    line-height: 20 * @rem;
    font-weight: bold;
    text-align: center;
  }
  .popup-content {
    text-align: center;
    margin-top: 20 * @rem;
    margin-bottom: 26 * @rem;
    padding: 0 22 * @rem;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    span {
      color: @themeColor;
      font-weight: bold;
    }
  }
  .bottom-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    .bottom-btn {
      width: 110 * @rem;
      height: 35 * @rem;
      margin: 0 10 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #ffffff;
      background-color: @themeColor;
      border-radius: 18 * @rem;
      &.no {
        color: #7d7d7d;
        background: #f2f2f2;
      }
    }
  }
}
</style>
