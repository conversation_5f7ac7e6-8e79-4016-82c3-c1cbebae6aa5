<template>
  <div class="phone-login-page">
    <nav-bar-2 :placeholder="false" bgStyle="transparent">
      <template #right>
        <div class="login" @click="goToLogin">{{ $t('密码登录') }}</div>
      </template>
    </nav-bar-2>
    <div class="top-banner">
      <div class="page-title">{{ $t('验证码登录') }}</div>
      <div class="login-desc">{{ $t('未注册的手机号码验证后自动注册') }}</div>
    </div>
    <div class="form">
      <div class="field">
        <input type="number" v-model="phone" placeholder="请输入手机号码" />
        <div v-if="phone != ''" class="clear" @click="phone = ''"></div>
        <div class="country-code" @click="toPage('AreaCode')">
          <div class="country-code-text">+{{ areaCode }}</div>
          <div class="arrow-down"></div>
        </div>
      </div>
      <div class="field">
        <input
          type="number"
          v-model="authCode"
          :placeholder="$t('请输入验证码')"
        />
        <div class="right">
          <div class="text" v-if="!ifCount" @click="captchaClick()">
            {{ $t('获取验证码') }}
          </div>
          <div class="text text2" v-else>
            {{ `${$t('重新获取')}${countdown}s` }}
          </div>
        </div>
      </div>
    </div>
    <div class="button" @click="commit()">{{ $t('登录/注册') }}</div>
    <div class="explain">
      <input type="checkbox" v-model="ifAgreement" />{{
        $t('登录即代表您已同意')
      }}
      <div
        class="link"
        @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
      >
        《{{ $t('用户协议') }}》
      </div>
      {{ $t('与') }}
      <div
        class="link"
        @click="handleLink($h5Page.yinsizhengce, $t('隐私政策'))"
      >
        《{{ $t('隐私政策') }}》
      </div>
    </div>
  </div>
</template>

<script>
import { ApiLogin, ApiAuthCode } from '@/api/views/users';
import { loginSuccess, getQueryVariable } from '@/utils/function';
import { mapGetters, mapMutations } from 'vuex';
import { isWebApp } from '@/utils/userAgent.js';
import useConfirmAgreement from '@/components/yy-confirm-agreement/index.js';
export default {
  name: 'PhoneLogin',
  data() {
    return {
      isWebApp,
      phone: '',
      authCode: '',
      countdown: 60,
      ifCount: false,
      ifAgreement: false,
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
  },
  watch: {
    authCode() {
      if (this.authCode > 1000000)
        this.authCode = Math.floor(this.authCode / 10);
    },
  },

  /**
   * 海外新增路由守卫
   * 默认登录都是跳转此页面，但是海外用户使用的是密码登录，所以在这里拦截
   */
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 2023年12月11日17:28:41
      if (vm.isHw) {
        if (from.name != 'LoginHw') {
          if (localStorage.getItem('HAD_LOGIN_HW')) {
            vm.toPage('LoginHw');
          } else {
            vm.toPage('RegisterHw');
          }
        }
        return false;
      }
      if (
        from.name != 'Login' &&
        [1, 2, 3].includes(Number(vm.initData.login_type?.type))
      ) {
        vm.toPage('Login');
      } else if ([1].includes(Number(vm.initData.login_type?.type))) {
        // 新增提示弹窗
        if (!sessionStorage.getItem('PHONE_LOGIN_HW_TIP')) {
          vm.$dialog
            .alert({
              message: vm.initData.login_type.msg || '暂无消息',
              allowHtml: true,
              confirmButtonText: '我知道了',
              confirmButtonColor: '@themeColor',
            })
            .then(() => {
              sessionStorage.setItem('PHONE_LOGIN_HW_TIP', true);
            });
        }
      }
    });
  },

  created() {
    if (localStorage.getItem('phone')) {
      this.phone = localStorage.getItem('phone');
    }
    if (!this.areaCode) {
      this.setAreaCode(86);
    }
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  activated() {
    // 神策埋点
    this.$sensorsTrack('loginpage_view', {
      referrer_page_name: this.$sensorsChainGet(),
    });
  },
  methods: {
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    goToLogin() {
      if (this.isHw) {
        this.toPage('LoginHw');
      } else {
        this.toPage('Login');
      }
    },
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
    toPage(page) {
      ['Login', 'LoginHw', 'RegisterHw'].includes(page)
        ? this.$router.replace({ name: page })
        : this.$router.push({ name: page });
    },
    async commit() {
      if (this.phone === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.authCode === '') {
        this.$toast(this.$t('请输入获得的验证码!'));
        return false;
      }

      const hadAgree = await useConfirmAgreement(this.ifAgreement);
      if (!hadAgree) return;

      this.ifAgreement = true;

      const toast1 = this.$toast.loading({
        message: this.$t('登录中...'),
        forbidClick: true,
        duration: 0,
      });
      let params = {
        phone: this.phone,
        code: this.authCode,
      };
      if (this.areaCode) {
        params.countryCode = this.areaCode;
      }
      if (this.isWebApp) {
        params.is_ios_standalone = 1;
      }
      if (getQueryVariable('inviterUserId')) {
        params.inviterUserId = getQueryVariable('inviterUserId');
      }
      ApiLogin(params).then(res => {
        localStorage.setItem('phone', this.phone);
        loginSuccess(res);
      });
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      this.$sensorsTrack('getcode_click')
      if (this.phone === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      let params = {
        phone: this.phone,
        type: 3,
      };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      if (this.areaCode) {
        params.countryCode = this.areaCode;
      }
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
  },
};
</script>

<style lang="less" scoped>
.phone-login-page {
  .login {
    font-size: 14 * @rem;
    color: #000000;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 200 * @rem;
    background-image: url(~@/assets/images/users/login-top-bg.png);
    background-size: 100% 200 * @rem;
    background-repeat: no-repeat;
    padding-left: 30 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: bold;
      line-height: 40 * @rem;
      margin-top: 113 * @rem;
    }
    .login-desc {
      font-size: 14 * @rem;
      color: #9a9a9a;
      line-height: 20 * @rem;
      margin-top: 5 * @rem;
    }
  }
  .form {
    padding: 0 30 * @rem;
    margin-top: 30 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 42 * @rem;
      border-bottom: 1 * @rem solid #d2d2d2;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      input {
        flex: 1;
        height: 100%;
        line-height: 42 * @rem;
        font-size: 16 * @rem;
        letter-spacing: 1 * @rem;
      }
      .right {
        position: relative;
        flex: 0 0 90 * @rem;
        height: 100%;
        .text {
          box-sizing: border-box;
          line-height: 32 * @rem;
          font-size: 13 * @rem;
          text-align: center;
          color: @themeColor;
          border: 1 * @rem solid @themeColor;
          border-radius: 18 * @rem;
          height: 32 * @rem;
          &.text2 {
            border: 1 * @rem solid #a4a4a4;
            color: #a4a4a4;
          }
        }
        span {
          position: absolute;
          top: 0;
          right: 0;
          display: block;
          color: #a4a4a4;
        }
      }
      .clear {
        width: 16 * @rem;
        height: 16 * @rem;
        padding: 0 10 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .country-code {
        display: flex;
        height: 42 * @rem;
        align-items: center;
        padding-left: 9 * @rem;
        position: relative;
        &::before {
          content: '';
          width: 1 * @rem;
          height: 11 * @rem;
          background-color: #dadada;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        .country-code-text {
          font-size: 16 * @rem;
          color: #000000;
        }
        .arrow-down {
          width: 10 * @rem;
          height: 6 * @rem;
          .image-bg('~@/assets/images/users/arrow-down.png');
          margin-left: 5 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
  }
  .button {
    height: 50 * @rem;
    margin: 40 * @rem 30 * @rem 0;
    text-align: center;
    line-height: 50 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
  }
  .explain {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #797979;
    font-size: 12 * @rem;
    margin-top: 16 * @rem;
    input[type='checkbox'] {
      width: 12 * @rem;
      height: 12 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center;
      background-size: 100%;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    .text {
      display: flex;
      margin-bottom: 31 * @rem;
      justify-content: center;
      align-items: center;
      font-size: 14 * @rem;
      color: #999999;
      span {
        display: block;
        width: 30 * @rem;
        height: 1 * @rem;
        margin: 0 6 * @rem;
        background: #dcdcdc;
      }
    }
    .wechat {
      width: 40 * @rem;
      height: 40 * @rem;
      margin: 0 auto 27 * @rem;
      background-image: url('~@/assets/images/users/wechat.png');
      background-size: 100%;
    }
  }
}
</style>
