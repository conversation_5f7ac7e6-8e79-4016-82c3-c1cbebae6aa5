<template>
  <div class="section-title-component">
    <div class="title">
      <div class="main-title">{{ mainTitle }}</div>
      <div class="small-title">{{ smallText }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionTitle',
  data() {
    return {};
  },
  props: {
    mainTitle: {
      type: String,
      required: true,
    },
    smallText: {
      type: String,
      default: '',
    },
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.section-title-component {
  padding: 24 * @rem 18 * @rem 20 * @rem;
  .title {
    .main-title {
      display: flex;
      align-items: center;
      font-size: 18 * @rem;
      line-height: 25 * @rem;
      color: #000000;
      font-weight: 600;

      &::before {
        content: '';
        width: 6 * @rem;
        height: 12 * @rem;
        background: @themeColor;
        border-radius: 16 * @rem;
        flex-shrink: 0;
        margin-right: 8 * @rem;
      }
    }
    .small-title {
      line-height: 18 * @rem;
      font-size: 13 * @rem;
      color: #aaaaaa;
      font-weight: 400;
      margin-top: 3 * @rem;
    }
  }
}
</style>
