<template>
  <div class="my-sell-list-page">
    <van-tabs
      v-model="active"
      swipeable
      animated
      title-active-color="#000000"
      title-inactive-color="#797979"
      :border="true"
      color="#21b98a"
      line-height="4px"
      class="tab-bar"
    >
      <van-tab
        v-for="(item, index) in tabList"
        :key="index"
        :title="item.title"
      >
        <deal-list :status="item.status" type="sell"></deal-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import dealList from '../../components/deal-list';
export default {
  name: 'MySellList',
  components: {
    dealList,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          title: this.$t('全部'),
          status: -1,
        },
        {
          title: this.$t('审核中'),
          status: 9,
        },
        {
          title: this.$t('出售中'),
          status: 6,
        },
        {
          title: this.$t('已出售'),
          status: 7,
        },
        {
          title: this.$t('已下架'),
          status: 4,
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.my-sell-list-page {
  border-radius: 20 * @rem 20 * @rem 0 0;
  overflow: hidden;
  height: calc(100vh - 100 * @rem - @safeAreaTop);
  height: calc(100vh - 100 * @rem - @safeAreaTopEnv);
  background-color: #f7f8fa;
  /deep/ .van-tabs--line .van-tabs__wrap {
    height: 50 * @rem;
  }
  .tab-bar {
    height: 50 * @rem;
    /deep/ .van-tab {
      font-size: 15 * @rem;
    }
    /deep/ .van-tabs__line {
      width: 14 * @rem;
      background-color: @themeColor;
      bottom: 8 * @rem;
      height: 4 * @rem;
    }
    /deep/ .van-tabs__nav--line {
      padding-bottom: 0 * @rem;
      height: 50 * @rem;
    }
    /deep/.van-tabs__nav {
      background-color: #f7f8fa;
    }
    /deep/.van-tab--active {
      font-weight: 600;
      font-size: 16 * @rem;
    }
  }
}
</style>
