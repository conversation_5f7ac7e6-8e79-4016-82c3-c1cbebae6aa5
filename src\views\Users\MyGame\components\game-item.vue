<template>
  <div class="my-game-item">
    <div class="game-info" @click="toDetail">
      <div class="game-pic">
        <img :src="info.titlepic" alt="" />
      </div>
      <div class="info">
        <div class="title">
          <span>{{ info.main_title }}</span>
        </div>
        <div class="info-bottom">
          <div class="play-time" v-if="info.play_time">
            {{ info.play_time }}
          </div>
          <div class="version" v-else-if="info.version"
            >V{{ info.version }}</div
          >
          <div
            class="server"
            v-if="info.service_date"
            @click.stop="serverPopupShow = true"
            >{{ info.service_date }}></div
          >
        </div>
      </div>
      <div class="operation">
        <div class="more" @click.stop="operationPopupShow = true"></div>
        <div class="btns">
          <div
            v-if="info.detailid == 1"
            class="download-btn"
            @click.stop="cloudPlayInit(info, info.id)"
            >云玩</div
          >
          <div
            v-else-if="info.detailid == 2"
            class="download-btn"
            @click.stop="downJymyBtn(info)"
            >下载</div
          >
          <div
            v-else-if="info.classid == 140"
            class="download-btn"
            @click.stop="playDirectly(info)"
            >直接玩</div
          >
          <yy-download-btn
            class="game-download btn"
            :gameInfo="info"
            v-else
          ></yy-download-btn>
        </div>
      </div>
    </div>
    <div class="welfare-info" v-if="hasBenefits">
      <div
        class="card648 welfare-item"
        v-if="info.benefits.card648"
        @click="get648Card"
      >
        <div class="price">
          <em>￥</em>
          <span>648</span>
        </div>
        <div class="get-btn">
          {{ info.benefits.card648.status == 1 ? '去使用' : '去领取' }}
        </div>
      </div>
      <div
        class="card welfare-item"
        v-if="info.benefits.card_count"
        @click="
          $router.push({
            name: 'GameGift',
            params: {
              game_id: info.id,
              class_id: info.classid,
            },
          })
        "
      >
        <span class="count">{{ info.benefits.card_count }}</span>
        <div class="name">礼包</div>
      </div>
      <div
        class="coupon welfare-item"
        v-if="info.benefits.coupon_sum"
        @click="
          $router.push({
            name: 'GameCoupon',
            params: { game_id: info.id },
          })
        "
      >
        <div class="price">
          <em>￥</em>
          <span>{{ info.benefits.coupon_sum }}</span>
        </div>
        <div class="name">代金券</div>
      </div>
      <div
        class="fanli welfare-item"
        v-if="info.benefits.act_count"
        @click="
          $router.push({
            name: 'GameNews',
            params: { game_id: info.id },
          })
        "
      >
        <span class="count">{{ info.benefits.act_count }}</span>
        <div class="name">返利</div>
      </div>
    </div>
    <div
      class="welfare-list"
      :class="{
        'item-left': info.ex_benefits.length == 2,
      }"
      v-if="info.ex_benefits && info.ex_benefits.length"
    >
      <div
        class="welfare-item"
        :class="{ 'more-item': info.ex_benefits.length > 1 }"
        v-for="(welfare, index) in info.ex_benefits"
        :key="index"
        @click="clickBenefitItem(welfare)"
      >
        <div class="title">{{ welfare.title }}</div>
        <div class="info">{{ welfare.desc }}</div>
      </div>
    </div>
    <div class="user-info" v-if="info.tab_list && info.tab_list.length">
      <div
        class="user-popup-btn"
        v-for="(tab, tabIndex) in info.tab_list"
        :key="tabIndex"
        @click="userTabClick(tab)"
      >
        {{ tab.title }}
      </div>
    </div>

    <!-- 更多点击弹窗 -->
    <van-popup
      v-model="operationPopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      class="more-operation-popup"
    >
      <div class="operation-list">
        <!-- <div
          class="operation-item cloud"
          v-if="info.is_cloud"
          @click="addToCloud"
          >添加到云挂机</div
        > -->
        <div class="operation-item delete" @click="deleteItem"
          >从列表中删除</div
        >
      </div>
      <div class="cancel-btn btn" @click="operationPopupShow = false">取消</div>
    </van-popup>
    <!-- 服务器列表弹窗 -->
    <server-popup :show.sync="serverPopupShow" :id="info.id"></server-popup>
    <!-- 返利活动 -->
    <active-popup :show.sync="fanliPopupShow" :id="info.id"></active-popup>
    <!-- 回收小号 -->
    <recycle-xh-popup
      :show.sync="recycleXhPopupShow"
      :id="info.app_id"
    ></recycle-xh-popup>
    <!-- 我的小号 -->
    <my-xh-popup
      :show.sync="myXhPopupShow"
      :info="info"
      :id="info.app_id"
    ></my-xh-popup>
    <!-- 福利 -->
    <welfare-popup
      :show.sync="welfarePopupShow"
      :id="info.id"
      :gameInfo="info"
    ></welfare-popup>
    <!-- 648礼包 -->
    <cardpass-copy-popup
      v-if="giftCopyPopupShow"
      :show.sync="giftCopyPopupShow"
      :info="this.info.benefits.card648"
    ></cardpass-copy-popup>
  </div>
</template>

<script>
import { downJymyBtnCallback, navigateToGameDetail } from '@/utils/function';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import {
  ApiPlayingCloudDeviceList,
  ApiPlayingGameDelete,
} from '@/api/views/game.js';
import ServerPopup from './server-popup.vue';
import ActivePopup from './active-popup.vue';
import RecycleXhPopup from './recycle-xh-popup.vue';
import MyXhPopup from './my-xh-popup.vue';
import WelfarePopup from './welfare-popup.vue';
import { ApiCardGet } from '@/api/views/gift.js';

export default {
  name: 'GameItem',
  props: {
    info: {
      type: Object,
      default: {},
    },
  },
  components: {
    ServerPopup,
    ActivePopup,
    RecycleXhPopup,
    MyXhPopup,
    WelfarePopup,
  },
  data() {
    return {
      operationPopupShow: false,
      cloudDeviceList: [],
      cloudDevicePopupShow: false,
      checkCloudDeviceItem: {},
      serverPopupShow: false,
      fanliPopupShow: false,
      recycleXhPopupShow: false,
      myXhPopupShow: false,
      welfarePopupShow: false,
      giftCopyPopupShow: false,
    };
  },
  computed: {
    ...mapGetters({
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
      simulatorInitLoading: 'game/simulatorInitLoading',
    }),
    hasBenefits() {
      return (
        this.info.benefits.act_count ||
        this.info.benefits.card648 ||
        this.info.benefits.card_count ||
        this.info.benefits.coupon_sum
      );
    },
  },
  mounted() {
    this.setSimulatorInitLoadingEmpty({});
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
      setReceiveData: 'cloud_hangup/setReceiveData',
      setSimulatorInitLoadingEmpty: 'game/setSimulatorInitLoadingEmpty',
      setCloudType: 'system/setCloudType',
    }),
    ...mapActions({
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
      OPEN_SIMULATOR_GAME: 'game/OPEN_SIMULATOR_GAME',
    }),
    toDetail() {
      navigateToGameDetail(this.info);
    },
    async deleteItem() {
      this.operationPopupShow = false;
      await ApiPlayingGameDelete({
        game_ids: this.info.id,
      });
      this.$toast('删除成功');
      setTimeout(() => {
        this.$emit('updateList');
        this.$toast.clear();
      }, 500);
    },
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    async playDirectly(item) {
      if (
        this.simulatorInitLoading[item.id] ||
        Object.values(this.simulatorInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_SIMULATOR_GAME(item);
    },
    async addToCloud() {
      this.$toast.loading();
      try {
        let res = await ApiPlayingCloudDeviceList();
        this.cloudDeviceList = res.data.list;
        // 设置云类型并加载相应的SDK 默认值给0 0百度云 2臂云
        if (this.cloudDeviceList.length) {
          const cloudType =
            list[0]?.cloud_type !== undefined ? list[0]?.cloud_type : 0;
          this.setCloudType(cloudType);
        }
        if (res.data.count > 1) {
          this.checkCloudDeviceItem =
            this.cloudDeviceList.find(item => {
              return !item.game;
            }) || this.cloudDeviceList[0];
          this.cloudDevicePopupShow = true;
          this.operationPopupShow = false;
        } else if (res.data.count == 1) {
          this.checkCloudDeviceItem = this.cloudDeviceList[0];
          this.selectDevice();
        } else {
          this.$toast('尚未购买云挂机，请先购买');
          setTimeout(() => {
            this.$toast.clear();
            this.toPage('BuyCloudDevices');
            this.operationPopupShow = false;
          }, 500);
        }
      } finally {
        this.$toast.clear();
      }
    },
    selectDevice() {
      if (this.checkCloudDeviceItem.game) {
        this.$dialog({
          title: '设备挂机中',
          message: '当前设备挂机中，是否退出并且加载新游戏',
          showCancelButton: true,
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          lockScroll: false,
        }).then(async () => {
          this.setCheckCloudDeviceItem(this.checkCloudDeviceItem);
          this.setReceiveData(this.info);
          this.toPage('CloudHangupInterface', {
            receiveData: this.info,
            checkCloudDeviceItem: this.checkCloudDeviceItem,
          });
          this.cloudDevicePopupShow = false;
          this.operationPopupShow = false;
        });
      } else {
        this.setCheckCloudDeviceItem(this.checkCloudDeviceItem);
        this.setReceiveData(this.info);
        this.toPage('CloudHangupInterface', {
          receiveData: this.info,
          checkCloudDeviceItem: this.checkCloudDeviceItem,
        });
        this.cloudDevicePopupShow = false;
        this.operationPopupShow = false;
      }
    },

    async get648Card() {
      this.$toast.loading('加载中');
      if (!this.info.benefits.card648.cardpass) {
        const res = await ApiCardGet({
          cardId: this.info.benefits.card648.id,
          autoXh: 1,
        });
        this.info.benefits.card648.status = 1;
        this.info.benefits.card648 = {
          ...this.info.benefits.card648,
          ...res.data,
        };

        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${this.info.benefits.card648.game_id}`,
          adv_id: '暂无',
          game_name: this.info.benefits.card648.titlegame,
          game_type: `${this.info.benefits.card648.classid}`,
          game_size: '暂无',
          reward_type: this.info.benefits.card648.title, // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });
      }
      this.$toast.clear();
      this.$nextTick(() => {
        this.giftCopyPopupShow = true;
      });
    },
    clickBenefitItem(item) {
      switch (item.action_code) {
        case 13: // web内页
          this.toPage(item.web_page);
          break;
        case 23: // 游戏签到
          this.toPage('GameSignInDetail', { id: this.info.id });
          break;
        case 21: // 开局号
          this.toPage('OpeningAccount', { id: this.info.id });
          break;
      }
    },
    async userTabClick(data) {
      //1=我的小号 2=小号回收 3=开服 4=返利 5=我的返利
      switch (data.action) {
        case 1:
          this.myXhPopupShow = true;
          break;
        case 2:
          this.recycleXhPopupShow = true;
          break;
        case 3:
          d;
          this.serverPopupShow = true;
          break;
        case 4:
          this.fanliPopupShow = true;
          break;
        case 5:
          this.fanliPopupShow = true;
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.my-game-item {
  background-color: #fff;
  border-radius: 12 * @rem;
  padding: 12 * @rem;

  .game-info {
    display: flex;
    align-items: center;

    .game-pic {
      width: 64 * @rem;
      height: 64 * @rem;
      border-radius: 12 * @rem;
      margin-right: 8 * @rem;

      img {
        border-radius: 12 * @rem;
      }
    }

    .info {
      flex: 1;
      min-width: 0;

      .title {
        display: flex;
        align-items: center;
        span {
          display: block;
          height: 18 * @rem;
          font-weight: 600;
          font-size: 14 * @rem;
          color: #222222;
          line-height: 18 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        em {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 15 * @rem;
          border-radius: 4 * @rem;
          border: 1 * @rem solid #e0e0e0;
          padding: 0 4 * @rem;
          line-height: 1;
          font-weight: 400;
          font-size: 9 * @rem;
          color: #808080;
          margin-left: 4 * @rem;
        }
      }

      .info-bottom {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 11 * @rem;
        height: 14 * @rem;
        overflow: hidden;

        .play-time {
          display: flex;
          align-items: center;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #9a9a9a;
          line-height: 14 * @rem;
          margin-right: 8 * @rem;

          &::before {
            flex-shrink: 0;
            content: '';
            display: block;
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/my-game/clock-icon.png) no-repeat;
            background-size: 12 * @rem 12 * @rem;
            margin-right: 4 * @rem;
          }
        }

        .version {
          flex-shrink: 0;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #9a9a9a;
          line-height: 14 * @rem;
          margin-right: 8 * @rem;
        }

        .server {
          flex-shrink: 0;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: @themeColor;
          line-height: 14 * @rem;
        }
      }
    }

    .welfare {
      width: 56 * @rem;
      height: 28 * @rem;
      flex-shrink: 0;
      background: url(~@/assets/images/my-game/welfare-icon.png) no-repeat
        center;
      background-size: 56 * @rem 28 * @rem;
      margin-left: 5 * @rem;
    }
  }

  .welfare-info {
    display: flex;
    align-items: center;
    width: 100%;
    height: 57 * @rem;
    background: #fffbf8;
    border-radius: 8 * @rem;
    margin-top: 14 * @rem;
    box-sizing: border-box;

    .card648,
    .coupon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;

      .price {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 22 * @rem;
        font-size: 10 * @rem;
        color: #be8e77;
        line-height: 14 * @rem;
        text-align: center;

        span {
          font-size: 14 * @rem;
          line-height: 23 * @rem;
        }
      }
      .get-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 14 * @rem;
        font-size: 11 * @rem;
        color: #be8e77;
        line-height: 14 * @rem;
        text-align: center;
        margin-top: 4 * @rem;

        &::after {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 8 * @rem;
          background: url(~@/assets/images/my-game/right-arrow-brown.png)
            no-repeat;
          background-size: 6 * @rem 8 * @rem;
          margin-left: 2 * @rem;
        }
      }
    }

    .welfare-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: relative;
      box-sizing: border-box;

      .count {
        height: 20 * @rem;
        font-size: 14 * @rem;
        color: #be8e77;
        line-height: 20 * @rem;
        text-align: center;
      }

      .name {
        height: 14 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #be8e77;
        line-height: 14 * @rem;
        text-align: center;
        margin-top: 4 * @rem;
      }

      &::after {
        content: '';
        display: block;
        width: 0 * @rem;
        height: 24 * @rem;
        border: 1 * @rem solid rgba(153, 109, 84, 0.1);
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      &:last-of-type::after {
        display: none;
      }
    }
  }

  .welfare-list {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 33 * @rem;
    margin-top: 10 * @rem;
    background-color: #fffbf8;
    border-radius: 5 * @rem;

    &.item-left {
      .welfare-item {
        justify-content: left;
        padding: 0 14 * @rem;

        &.more-item {
          padding: 0 14 * @rem;
        }
      }
    }
    .welfare-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 14 * @rem;
      width: 100%;
      box-sizing: border-box;

      &.more-item {
        padding: 0;

        .info {
          display: none;
        }
      }

      .icon {
        width: 16 * @rem;
        height: 16 * @rem;
        margin-right: 2 * @rem;
      }

      .title {
        display: flex;
        align-items: center;
        height: 15 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #be8e77;
        line-height: 15 * @rem;
        text-align: left;

        &::after {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 8 * @rem;
          background: url(~@/assets/images/my-game/right-arrow-brown.png)
            no-repeat;
          background-size: 6 * @rem 8 * @rem;
          margin-left: 2 * @rem;
        }
      }

      .info {
        flex: 1;
        min-width: 0;
        height: 14 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #be8e77;
        line-height: 14 * @rem;
        text-align: right;
        margin-left: 90 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 12 * @rem;

    .user-popup-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 22 * @rem;
      font-weight: 400;
      font-size: 11 * @rem;
      color: #979797;
      padding: 0 7 * @rem;
      position: relative;
      margin-right: 10 * @rem;
      border-radius: 16 * @rem;
      border: 1px solid rgba(151, 151, 151, 0.46);

      i {
        display: block;
        width: 14 * @rem;
        height: 14 * @rem;
        margin-right: 2 * @rem;
        background: url(~@/assets/images/my-game/wdxh.png) no-repeat center;
        background-size: 14 * @rem 14 * @rem;
      }

      &::after {
        content: '';
        display: block;
        width: 6 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/my-game/right-arrow.png) no-repeat;
        background-size: 6 * @rem 8 * @rem;
        margin-left: 3 * @rem;
      }
    }
  }

  .operation {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .more {
      width: 11 * @rem;
      height: 13 * @rem;
      background: url(~@/assets/images/my-game/more-icon.png) no-repeat center
        center;
      background-size: 11 * @rem 13 * @rem;
      margin-left: 6 * @rem;
      padding: 4 * @rem;
    }

    .btns {
      display: flex;
      align-items: center;

      .game-download {
        margin-left: 8 * @rem;
        /deep/ .download-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 68 * @rem;
          height: 28 * @rem;
          background: @themeBg;
          border-radius: 25 * @rem;
          font-weight: 600;
          font-size: 12 * @rem;
          color: #fff;
          line-height: 15 * @rem;
          text-align: center;
        }
      }

      .h5-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68 * @rem;
        height: 28 * @rem;
        background: #ecfbf4;
        border-radius: 25 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: @themeColor;
        line-height: 15 * @rem;
        text-align: center;
        margin-left: 12 * @rem;
      }
      .download-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68 * @rem;
        height: 28 * @rem;
        background: @themeBg;
        border-radius: 25 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #fff;
        line-height: 15 * @rem;
        text-align: center;
        margin-left: 12 * @rem;
      }
    }
  }
}

.more-operation-popup {
  padding: 26 * @rem 24 * @rem 20 * @rem 24 * @rem;
  box-sizing: border-box;
  .operation-list {
    overflow: hidden;
    .operation-item {
      display: flex;
      align-items: center;
      height: 32 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #333333;
      line-height: 32 * @rem;
      margin-bottom: 14 * @rem;

      &::before {
        flex-shrink: 0;
        display: block;
        content: '';
        width: 32 * @rem;
        height: 32 * @rem;
        background: url('~@/assets/images/my-game/cloud-game-icon.png')
          no-repeat center;
        background-size: 32 * @rem 32 * @rem;
        margin-right: 8 * @rem;
      }

      &.delete::before {
        background-image: url('~@/assets/images/my-game/delete-icon.png');
      }
    }
  }
  .cancel-btn {
    width: 295 * @rem;
    height: 44 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: @themeBg;
    border-radius: 29 * @rem;
    margin: 12 * @rem auto 0;
    font-weight: 500;
    font-size: 15 * @rem;
    color: #ffffff;
    line-height: 21 * @rem;
    text-align: center;
  }
}

.select-device-popup {
  background-color: #fbfbfe;
  padding-bottom: 88 * @rem;

  .title {
    height: 25 * @rem;
    font-weight: bold;
    font-size: 18 * @rem;
    color: #000000;
    line-height: 25 * @rem;
    text-align: left;
    padding: 20 * @rem;
  }

  .return {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 61 * @rem;
    height: 28 * @rem;
    border-radius: 18 * @rem;
    border: 1 * @rem solid rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 18 * @rem;
    right: 18 * @rem;
    color: #7f7f7f;
    font-size: 13 * @rem;

    &::before {
      content: '';
      display: block;
      width: 5 * @rem;
      height: 10 * @rem;
      margin-right: 6 * @rem;
      background: url('~@/assets/images/my-game/left-arrow.png') no-repeat
        center;
      background-size: 5 * @rem 10 * @rem;
    }
  }

  .device-list {
    margin: 5 * @rem 24 * @rem 0;
    max-height: calc(60vh - 65 * @rem - 64 * @rem);
    overflow: auto;

    .device-item {
      display: flex;
      align-items: center;
      height: 72 * @rem;
      background: #ffffff;
      border-radius: 6 * @rem;
      border: 1 * @rem solid #f0f0f0;
      padding: 0 12 * @rem;
      margin-bottom: 8 * @rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      .info {
        flex: 1;
        min-width: 0;

        .device-name {
          height: 20 * @rem;
          font-weight: 600;
          font-size: 14 * @rem;
          color: #111111;
          line-height: 20 * @rem;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .status {
          display: flex;
          align-items: center;
          margin-top: 7 * @rem;
          color: @themeColor;
          height: 17 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          line-height: 17 * @rem;

          &::before {
            content: '';
            display: block;
            width: 10 * @rem;
            height: 10 * @rem;
            background: url(~@/assets/images/my-game/free.png) no-repeat;
            background-size: 10 * @rem 10 * @rem;
          }

          span {
            flex-shrink: 0;
            display: block;
            margin: 0 12 * @rem 0 8 * @rem;
          }

          .game-name {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &.working {
            color: #ff701d;

            &::before {
              background-image: url(~@/assets/images/my-game/working.png);
            }
          }
        }
      }

      .select {
        width: 20 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/my-game/select.png) no-repeat center;
        background-size: 20 * @rem 20 * @rem;
        margin-left: 10 * @rem;

        &.selected {
          background-image: url(~@/assets/images/my-game/selected.png);
        }
      }
    }
  }

  .bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 64 * @rem;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;

    .submit-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 339 * @rem;
      height: 44 * @rem;
      border-radius: 29 * @rem;
      background: @themeBg;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 15 * @rem;
      text-align: center;
    }
  }
}
</style>
