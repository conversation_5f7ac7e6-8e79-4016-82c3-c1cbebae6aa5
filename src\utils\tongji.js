import { isWebApp } from '@/utils/userAgent';
import BASEPARAMS from '@/utils/baseParams';
import Vue from 'vue';

export const baiDu = {
  fun() {
    window._hmt = [];
    let hm = document.createElement('script');
    hm.src = 'https://hm.baidu.com/hm.js?9f081970302cb5d5fa442076da5063ff';
    document.body.appendChild(hm);
    // 桌面版新增一个
    if (isWebApp) {
      let hm2 = document.createElement('script');
      hm2.src = 'https://hm.baidu.com/hm.js?24e7054b04db3e13609387ffe3932e2d';
      document.body.appendChild(hm2);
    }
    // 3518渠道号的时候添加这个统计
    if (BASEPARAMS.channel === 'cps3518') {
      let hm2 = document.createElement('script');
      hm2.src = 'https://hm.baidu.com/hm.js?1ee68884949b975513d3014ef586fb01';
      document.body.appendChild(hm2);
    }
  },
};
