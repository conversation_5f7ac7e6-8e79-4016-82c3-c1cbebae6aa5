<template>
  <div class="page">
    <nav-bar-2 :border="true" title="注销账号"> </nav-bar-2>
    <div class="tips-title">注销须知</div>
    <div class="tips-container">
      <p>
        1.本平台账号一旦注销将不可恢复，无法找回
        (包括但不限于已玩过游戏记录、充值记录、个人资料等)
      </p>
      <p>
        2.您在本平台历史已获得的金币、代金券、平台币SVIP会员权益等均视为您自行放弃，将无法继续使用或转移。
      </p>
      <p>
        3.当前账号下所有小号将无法继续登录游戏、不可进行交易、回收等操作(不包括已完成出售的小号)。
      </p>
      <p>4.账号注销后将自动解除当前账号绑定的手机号，身份证等相关信息。</p>
    </div>
    <div class="confirm-btn btn" @click="clickCancelBtn">确认注销</div>
    <!-- 注销弹窗 -->
    <van-dialog
      v-model="popupShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="false"
      get-container="body"
    >
      <div class="account-cancel-editor">
        <div class="popup-title">提示</div>
        <div class="popup-desc">
          账号一旦注销无法找回，且当前绑定手机号24小时内不可再次注册新的账号。
        </div>
        <div class="read-container" @click="read = !read">
          <div class="gou" :class="{ read: read }"></div>
          <div class="read-text">我知道了</div>
        </div>
        <div class="edit-code">
          <input
            type="number"
            :maxlength="6"
            class="input-code"
            v-model="smsCode"
            placeholder="请输入短信验证码"
          />
          <div class="get-code btn" v-if="!ifCount" @click="captchaClick()">
            {{ $t('获取验证码') }}
          </div>
          <div class="get-code getting" v-else>
            {{ `${$t('已发送')}(${countdown}s)` }}
          </div>
        </div>
        <div class="operation-container">
          <div class="change-btn btn" @click="handleConfirm">确认注销</div>
          <div class="change-btn btn" @click="closePopup">放弃注销</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiCancelUser } from '@/api/views/users';
import { mapMutations, mapActions } from 'vuex';
import { ApiAuthCode } from '@/api/views/users';
import { mapGetters } from 'vuex';
export default {
  name: 'ChangeNickname',
  data() {
    return {
      popupShow: false,
      timer: null,
      countdown: 60,
      ifCount: false,
      smsCode: '',
      read: false, // 是否已读
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  created() {
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  mounted() {
    if (this.userInfo.nickname != '') {
      this.inputText = this.userInfo.nickname;
    }
  },
  methods: {
    ...mapMutations({
      setUserInfo: 'user/setUserInfo',
      setUserInfoEx: 'user/setUserInfoEx',
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    ...mapActions({
      SET_REGRESSION_POPUP: 'user/SET_REGRESSION_POPUP',
      SET_FIRST_COUPON_ICON: 'user/SET_FIRST_COUPON_ICON',
    }),
    clickCancelBtn() {
      this.smsCode = '';
      this.read = false;
      this.popupShow = true;
    },
    closePopup() {
      this.popupShow = false;
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.userInfo.mobile === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      let params = { phone: this.userInfo.mobile, type: 9, countryCode: this.userInfo.country_code || 86 };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
    handleConfirm() {
      if (!this.read) {
        this.$toast('请详看提示信息，并勾选“我知道了”');
        return false;
      }
      if (!this.smsCode) {
        this.$toast('请输入短信验证码');
        return false;
      }
      ApiCancelUser({ code: this.smsCode }).then(async () => {
        this.$toast('账号注销成功');
        this.setUserInfo({});
        this.setUserInfoEx({});
        // 清除礼包小号数据
        this.setXiaohaoMap();
        await this.SET_REGRESSION_POPUP();
        await this.SET_FIRST_COUPON_ICON();

        localStorage.setItem('STORE', JSON.stringify(this.$store.state));
        this.$router.replace({ name: 'Mine' });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #fff;
  padding: 0 15 * @rem;
  .tips-title {
    height: 50 * @rem;
    display: flex;
    align-items: center;
    font-size: 17 * @rem;
    font-weight: bold;
    color: #000000;
  }
  .tips-container {
    box-sizing: border-box;
    width: 100%;
    background-color: #ffffff;
    p {
      line-height: 17 * @rem;
      font-size: 14 * @rem;
      color: #000000;
      margin-bottom: 18 * @rem;
    }
  }
  .confirm-btn {
    height: 44 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16 * @rem;
    font-weight: bold;
    color: #fff;
    border-radius: 8 * @rem;
    background-color: #fe382f;
    margin-top: 100 * @rem;
  }
}

.account-cancel-editor {
  box-sizing: border-box;
  padding: 20 * @rem 20 * @rem;
  .popup-title {
    font-size: 18 * @rem;
    text-align: center;
    padding: 0 * @rem 10 * @rem;
    font-weight: bold;
    color: #000;
  }
  .popup-desc {
    font-size: 14 * @rem;
    line-height: 20 * @rem;
    color: #333;
    margin-top: 20 * @rem;
  }
  .edit-account-cancel {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding: 11 * @rem 0;
    line-height: 30 * @rem;
    .title {
      font-size: 15 * @rem;
      color: #000000;
    }
    .input-text {
      display: block;
      flex: 1;
      min-width: 0;
      text-align: right;
      font-size: 15 * @rem;
    }
  }

  .edit-code {
    display: flex;
    align-items: center;
    padding: 10 * @rem 0;
    margin-top: 20 * @rem;
    border-bottom: 1px solid #e5e5e5;
    .input-code {
      box-sizing: border-box;
      flex: 1;
      min-width: 0;
      padding: 0 10 * @rem;
    }
    .get-code {
      font-size: 13 * @rem;
      color: #fff;
      width: 112 * @rem;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8 * @rem;
      background-color: #fc6701;
      &.getting {
        box-sizing: border-box;
        color: #ccc;
        border: 1px solid #ccc;
        background-color: #fff;
      }
    }
  }
  .operation-container {
    display: flex;
    align-content: center;
    justify-content: space-between;
    margin-top: 30 * @rem;
    .change-btn {
      flex: 1;
      background: #fff;
      font-size: 16 * @rem;
      color: #000;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 44 * @rem;
      border: 1 * @rem solid #e3e3e3;
      &:not(:first-child) {
        margin-left: 10 * @rem;
      }
    }
  }

  .read-container {
    display: flex;
    align-items: center;
    margin: 10 * @rem auto 0;
    .gou {
      width: 18 * @rem;
      height: 18 * @rem;
      background: url(~@/assets/images/turn-table/gou.png) center center
        no-repeat;
      background-size: 18 * @rem 18 * @rem;
      &.read {
        background-image: url(~@/assets/images/turn-table/gou-on.png);
      }
    }
    .read-text {
      font-size: 16 * @rem;
      color: #353535;
      margin-left: 8 * @rem;
    }
  }
}
</style>
