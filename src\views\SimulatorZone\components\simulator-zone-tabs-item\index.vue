<template>
  <div class="simulator-zone-tabs-item">
    <div
      class="game-item-components"
      @click="toPage('EmulatorGameDetail', { id: simulatorInfo.id })"
    >
      <div class="game-icon">
        <img :src="simulatorInfo.titlepic" alt="" />
      </div>
      <div class="game-info">
        <div class="game-name">{{ simulatorInfo.title }}</div>
        <div class="tag-list" v-if="simulatorInfo.new_cate_list">
          <div class="tags">
            <div
              class="tag"
              v-for="item in simulatorInfo.new_cate_list"
              :key="item.id"
              >{{ item.title }}</div
            >
          </div>
        </div>
        <div class="info-bottom">{{ simulatorInfo.yxftitle }}</div>
      </div>
    </div>
    <div class="game-btn" v-if="isShowBtn">
      <div
        class="btn"
        :class="{ loading: simulatorInitLoading[simulatorInfo.id] }"
        @click="playDirectly(simulatorInfo)"
        >直接玩</div
      >
    </div>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from 'vuex';
export default {
  name: 'SimulatorZoneTabsItem',
  data() {
    return {
      simulatorInfo: {},
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
    isShowBtn: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    info(val) {
      this.simulatorInfo = val;
    },
  },
  created() {
    this.simulatorInfo = this.info;
  },
  mounted() {
    this.setSimulatorInitLoadingEmpty({});
  },
  methods: {
    // 直接玩
    async playDirectly(item) {
      if (
        this.simulatorInitLoading[item.id] ||
        Object.values(this.simulatorInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_SIMULATOR_GAME(item);
    },
    ...mapActions({
      OPEN_SIMULATOR_GAME: 'game/OPEN_SIMULATOR_GAME',
    }),
    ...mapMutations({
      setSimulatorInitLoadingEmpty: 'game/setSimulatorInitLoadingEmpty',
    }),
  },
  computed: {
    ...mapGetters({
      simulatorInitLoading: 'game/simulatorInitLoading',
    }),
  },
};
</script>

<style lang="less" scoped>
.simulator-zone-tabs-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .game-item-components {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex: 1;
    min-width: 0;
    .game-icon {
      width: 72 * @rem;
      height: 72 * @rem;
      border-radius: 12 * @rem;
      background-color: #eee;
      overflow: hidden;
    }
    .game-info {
      margin-left: 12 * @rem;
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
      justify-content: space-evenly;
      .game-name {
        width: 185 * @rem;
        font-weight: 600;
        font-size: 15 * @rem;
        color: #111111;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .tag-list {
        display: flex;
        align-items: center;

        .tags {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 8 * @rem;
          height: 22 * @rem;
          line-height: 22 * @rem;
          .tag {
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4 * @rem;
            padding: 2 * @rem 4 * @rem;
            color: #868686;
            height: 18 * @rem;
            line-height: 18 * @rem;
            &:not(:first-child) {
              margin-left: 6 * @rem;
            }
          }
        }
      }
      .info-bottom {
        margin-top: 8 * @rem;
        width: 185 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #868686;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .game-btn {
    position: relative;
    .btn {
      position: relative;
      width: 58 * @rem;
      height: 28 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
      border-radius: 19 * @rem;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #ffffff;
      &.loading {
        position: relative;
        font-size: 0;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          -webkit-transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          -webkit-animation: rotate 1s infinite linear;
          animation: rotate 1s infinite linear;
        }
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
