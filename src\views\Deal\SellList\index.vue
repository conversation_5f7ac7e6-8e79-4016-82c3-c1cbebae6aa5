<template>
  <div class="sell-list-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <sell-list-normal :list="sellList"></sell-list-normal>
    </yy-list>
  </div>
</template>

<script>
import sellListNormal from '../components/sell-list-normal';
import { ApiXiaohaoTradeList } from '@/api/views/xiaohao.js';
export default {
  name: 'SellList',
  components: {
    sellListNormal,
  },
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      sellList: [],
      page: 1,
      listRows: 10,
    };
  },
  methods: {
    async getSellList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiXiaohaoTradeList({
        isDone: 1,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.sellList = [];
      }
      this.sellList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getSellList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.sellList.length) {
        await this.getSellList();
      } else {
        await this.getSellList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.sell-list-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }
}
</style>
