<template>
  <div class="platform-coin-exchange-prop">
    <van-popup
      v-model="payPopupShow"
      position="bottom"
      :lock-scroll="true"
      round
      @click-overlay="closePopup"
      class="platform-coin-container-popup"
    >
      <div class="platform-coin-container">
        <div class="title">订单确认</div>
        <div class="close" @click="closePopup"> </div>
        <div class="order-container" v-if="cardOrderInfo">
          <div class="order-ptb">
            <div class="text">平台币余额</div>
            <div class="remaining-ptb">
              <span class="ptb">{{
                cardOrderInfo?.ptb_info?.total_ptb
                  ? Number(cardOrderInfo?.ptb_info?.total_ptb).toFixed(1)
                  : 0
              }}</span>
              <span class="binding"
                >(含{{ cardOrderInfo?.ptb_info?.fake_ptb_balance }}绑定)</span
              >
            </div>
          </div>
          <div class="order-tips">* 不可使用绑定平台币兑换道具</div>
          <div class="game-info">
            <div class="info">
              <div class="top-title">
                <div class="order-title">{{
                  cardOrderInfo?.game_info?.main_title
                }}</div>
                <div class="sub-title">{{
                  cardOrderInfo?.game_info?.subtitle
                }}</div>
              </div>
              <div class="content">
                <div class="prop-box">
                  <div class="icon">
                    <img :src="cardOrderInfo?.card_info?.titlepic" alt="" />
                  </div>
                  <div class="prop-info">
                    <div class="prop-name">
                      <div
                        class="prompt-content"
                        :style="{
                          marginRight:
                            cardOrderInfo?.card_info?.title &&
                            cardOrderInfo?.card_info?.title.length > 6
                              ? '4*@rem'
                              : '0',
                        }"
                      >
                        <div
                          :class="{
                            'text-scroll':
                              cardOrderInfo?.card_info?.title &&
                              cardOrderInfo?.card_info?.title.length > 6,
                            'text-scroll-fast':
                              cardOrderInfo?.card_info?.title &&
                              cardOrderInfo?.card_info?.title.length > 15,
                          }"
                          :style="{
                            animationDuration: `${
                              cardOrderInfo?.card_info?.title.length > 15
                                ? cardOrderInfo?.card_info?.title.length * 400
                                : cardOrderInfo?.card_info?.title.length * 800
                            }ms`,
                            animationDelay: 0,
                          }"
                          >{{ cardOrderInfo?.card_info?.title }}
                          <template
                            v-if="cardOrderInfo?.card_info?.title.length > 6"
                            >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{
                              cardOrderInfo?.card_info?.title
                            }}
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</template
                          >
                        </div></div
                      >
                    </div>
                    <div class="prop-type">消耗平台币</div>
                  </div>
                </div>
                <div class="pay-num">
                  {{ cardOrderInfo?.card_info?.need_ptb }}
                </div>
              </div>
            </div>
            <div
              class="line"
              v-if="
                cardOrderInfo?.card_info?.redeem_num ||
                cardOrderInfo?.card_info?.cardtext ||
                cardOrderInfo?.card_info?.cardbody
              "
            ></div>
            <div
              class="exchange-times"
              v-if="cardOrderInfo?.card_info?.redeem_num"
            >
              <span>兑换次数</span>
              <span
                >同一小号仅限兑换{{
                  cardOrderInfo?.card_info?.redeem_num
                }}次</span
              >
            </div>
            <div
              class="exchange-times"
              v-if="cardOrderInfo?.card_info?.prop_use"
            >
              <span>道具用途</span>
              <span>{{ cardOrderInfo?.card_info?.prop_use }}</span>
            </div>
            <div
              class="exchange-times"
              v-if="cardOrderInfo?.card_info?.cardtext"
            >
              <span>使用方法</span>
              <span>{{ cardOrderInfo?.card_info?.cardtext }}</span>
            </div>
            <div
              class="exchange-times"
              v-if="cardOrderInfo?.card_info?.cardbody"
            >
              <span>兑后须知</span>
              <span>{{ cardOrderInfo?.card_info?.cardbody }}</span>
            </div>
          </div>
          <div class="pay-type" v-if="!isOnlyPtb && !ptbIsSufficient">
            <div class="pay-type-tips">
              <span>平台币余额不足，请选择支付方式</span>
              <span>(10平台币 = 1元)</span>
            </div>
            <div class="pay-type-container">
              <div class="pay-list" ref="payList" :class="{ showAll: showAll }">
                <div
                  class="pay-item"
                  v-for="(item, index) in list"
                  :key="index"
                  @click="selectedPayType = item"
                >
                  <div class="pay-center">
                    <div class="line">
                      <img class="item-icon" :src="item.icon" alt="" />
                      <div class="pay-name">{{ item.name }}</div>
                      <div class="recommend-icon" v-if="item.recommend">{{
                        $t('推荐')
                      }}</div>
                    </div>
                    <div
                      v-if="item.activity && item.activity_info?.act_tag"
                      class="subtitle"
                    >
                      <span
                        v-for="(tag, tagIndex) in item.activity_info.act_tag"
                        :key="tagIndex"
                        >{{ tag }}</span
                      >
                    </div>
                  </div>
                  <div
                    class="choose"
                    :class="{ on: selectedPayType.symbol == item.symbol }"
                  ></div>
                </div>
              </div>
              <!-- <div
                class="more-pay-list"
                @click="showAll = true"
                v-if="list.length > 2 && !showAll"
              >
                {{ $t('更多支付方式') }}
              </div> -->
            </div>
          </div>
        </div>
        <div class="order-btn">
          <div class="pay-number">
            <span class="text1">合计:</span>
            <span class="unit">￥</span>
            <span class="num" v-if="!isOnlyPtb && !ptbIsSufficient">{{
              (Number(cardOrderInfo?.card_info?.need_ptb) / 10).toFixed(2)
            }}</span>
            <span class="num" v-else>{{
              cardOrderInfo?.card_info?.need_ptb
            }}</span>
            <span class="text2" v-if="!isOnlyPtb && !ptbIsSufficient">
              {{ $t('元') }}
            </span>
            <span class="text2" v-else> {{ $t('平台币') }} </span>
          </div>
          <div class="pay-btn" @click="handlePay">
            <span v-if="!isOnlyPtb && !ptbIsSufficient">确认支付</span>
            <span v-else>确认兑换</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'platformCoinExchangeProp',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    cardOrderInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectedPayType: {},
      showAll: false,
      welfarePopupShow: false,
      welfareInfo: {},
      welfareOnce: true,
    };
  },
  computed: {
    payPopupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    isOnlyPtb() {
      // 检查支付方式列表是否只有一个，并且该支付方式是平台币
      return this.list.length === 1 && this.list[0].symbol === 'ptb'
        ? true
        : false;
    },
    // 判断当前平台币是否充足（不含绑定平台币）
    ptbIsSufficient() {
      return (
        Number(this.cardOrderInfo?.ptb_info?.ptb_balance) >=
        Number(this.cardOrderInfo?.card_info?.need_ptb)
      );
    },
  },

  watch: {
    list(val) {
      this.selectedPayType =
        val.find(item => {
          return item.recommend;
        }) || val[0];
    },
    payPopupShow(val) {
      if (val) {
        this.scrollToSelectedPayType();
      }
    },
  },
  methods: {
    closePopup() {
      this.payPopupShow = false;
      if (this.selectedPayType.symbol !== 'ptb') {
        this.$emit('closePopup');
      }
    },
    scrollToSelectedPayType() {
      this.$nextTick(() => {
        const payList = this.$refs.payList;
        if (payList) {
          payList.scrollTop = 0;
        }
      });
    },
    handlePay() {
      if (
        this.selectedPayType.symbol == 'ptb' &&
        Number(this.cardOrderInfo?.ptb_info?.ptb_balance) <
          Number(this.cardOrderInfo.card_info.need_ptb)
      ) {
        this.$emit('ptbInsufficient');
        this.payPopupShow = false;
        return;
      }
      this.$emit('choosePayType', this.selectedPayType);
      this.payPopupShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.platform-coin-container {
  display: flex;
  flex-direction: column;
  max-height: 621 * @rem;
  overflow: hidden;
  padding: 16 * @rem 0 90 * @rem;
  box-sizing: border-box;
  position: relative;
  background: #f3f5f9;
  .title {
    font-weight: 500;
    font-size: 18 * @rem;
    color: #000000;
    margin: 0 auto 9 * @rem;
  }
  .close {
    position: absolute;
    top: 20 * @rem;
    right: 14 * @rem;
    background: url('~@/assets/images/games/order-btn-close.png') no-repeat
      center center;
    width: 20 * @rem;
    height: 20 * @rem;
    background-size: 20 * @rem 20 * @rem;
  }
  .order-container {
    position: relative;
    margin-top: 8 * @rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    padding: 0 12 * @rem;
    .order-ptb {
      width: 351 * @rem;
      box-sizing: border-box;
      padding: 0 10 * @rem;
      min-height: 44 * @rem;
      background: #ffffff;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        font-weight: 500;
        font-size: 14 * @rem;
        color: #191b1f;
      }
      .remaining-ptb {
        display: flex;
        align-items: center;
        .ptb {
          font-weight: 600;
          font-size: 16 * @rem;
          color: #191b1f;
        }
        .binding {
          margin-left: 4 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #60666c;
        }
      }
    }
    .order-tips {
      width: 100%;
      padding: 6 * @rem 0 8 * @rem 0;
      text-align: right;
      font-weight: 400;
      font-size: 10 * @rem;
      color: #93999f;
    }
    .game-info {
      width: 351 * @rem;
      box-sizing: border-box;
      padding: 16 * @rem 10 * @rem 13 * @rem;
      background: #ffffff;
      border-radius: 12 * @rem;
      .info {
        .top-title {
          display: flex;
          align-items: center;
          .order-title {
            font-weight: 600;
            font-size: 15px;
            color: #191b1f;
          }
          .sub-title {
            margin-left: 6 * @rem;
            height: 18 * @rem;
            border: 1 * @rem solid #e3e5e8;
            padding: 0 4 * @rem;
            box-sizing: border-box;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #93999f;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .content {
          margin: 10 * @rem 0;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .prop-box {
            display: flex;
            align-items: center;
            .icon {
              width: 45 * @rem;
              height: 45 * @rem;
              flex-shrink: 0;
            }
            .prop-info {
              margin-left: 6 * @rem;
              display: flex;
              flex-direction: column;
              .prop-name,
              .prop-type {
                white-space: nowrap;
                max-width: 180 * @rem;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .prop-name {
                font-weight: bold;
                font-size: 14 * @rem;
                color: #191b1f;
                .prompt-content {
                  overflow: hidden;
                  display: flex;
                }
              }
              .prop-type {
                margin-top: 8 * @rem;
                font-weight: 400;
                font-size: 12 * @rem;
                color: #60666c;
              }
            }
          }
          .pay-num {
            flex-shrink: 0;
            overflow: hidden;
            font-weight: 600;
            font-size: 18 * @rem;
            color: #ff6649;
          }
        }
      }
      .line {
        opacity: 0.6;
        border: 0.5 * @rem solid #f0f1f5;
      }
      .exchange-times {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 12 * @rem;
        overflow: hidden;
        span {
          white-space: nowrap;
          &:first-child {
            font-weight: 400;
            font-size: 13 * @rem;
            color: #93999f;
          }
          &:last-child {
            max-width: 200 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400;
            font-size: 13 * @rem;
            color: #60666c;
          }
        }
      }
    }
    .pay-type {
      margin-top: 12 * @rem;
      max-height: 195 * @rem;
      width: 100%;
      display: flex;
      flex-direction: column;
      .pay-type-tips {
        display: flex;
        justify-content: space-between;
        span {
          &:first-child {
            font-weight: 400;
            font-size: 12 * @rem;
            color: #60666c;
          }
          &:last-child {
            font-weight: 400;
            font-size: 10 * @rem;
            color: #93999f;
          }
        }
      }
      .pay-type-container {
        margin-top: 13 * @rem;
        position: relative;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .pay-list {
          flex: 1;
          min-height: 0;
          overflow: auto;
          scroll-behavior: smooth;

          &.showAll {
            overflow: auto;
          }

          .pay-item {
            // width: 339 * @rem;
            height: 70 * @rem;
            background: #ffffff;
            border-radius: 8 * @rem;
            border: 1 * @rem solid #efefef;
            display: flex;
            align-items: center;
            padding: 0 12 * @rem;
            box-sizing: border-box;
            margin: 0 auto 8 * @rem;

            .pay-center {
              flex: 1;

              .line {
                display: flex;
                align-items: center;
                width: 100%;

                .item-icon {
                  flex-shrink: 0;
                  width: 20 * @rem;
                  height: 20 * @rem;
                  margin-right: 10 * @rem;
                }

                .pay-name {
                  height: 21 * @rem;
                  font-weight: 600;
                  font-size: 15 * @rem;
                  color: #222222;
                  line-height: 21 * @rem;
                  overflow: hidden;
                }

                .recommend-icon {
                  flex-shrink: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 34 * @rem;
                  height: 18 * @rem;
                  line-height: 1;
                  margin-left: 3 * @rem;
                  background: #fc3a3b;
                  border-radius: 4 * @rem;
                  font-weight: 600;
                  font-size: 11 * @rem;
                  color: #ffffff;
                  text-align: center;
                }
              }

              .subtitle {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                height: 18 * @rem;
                overflow: hidden;
                margin-left: 30 * @rem;
                margin-top: 4 * @rem;

                span {
                  flex-shrink: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 16 * @rem;
                  line-height: 16 * @rem;
                  font-weight: 400;
                  font-size: 10 * @rem;
                  color: #fc3a3b;
                  text-align: center;
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid rgba(252, 58, 59, 0.4);
                  padding: 0 4 * @rem;
                  margin-right: 6 * @rem;
                }
              }
            }

            .choose {
              flex-shrink: 0;
              width: 18 * @rem;
              height: 18 * @rem;
              border-radius: 50%;
              border: 1 * @rem solid #e5e6eb;

              &.on {
                background: url(~@/assets/images/recharge/pay-selected-circle.png)
                  no-repeat;
                background-size: 18 * @rem 18 * @rem;
                border: none;
              }
            }
          }

          .shouqi {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 13 * @rem;
            color: #666666;
            line-height: 18 * @rem;
            margin: 20 * @rem;

            &::after {
              content: '';
              display: block;
              width: 12 * @rem;
              height: 7 * @rem;
              background: url(~@/assets/images/recharge/gray-bottom-arrow.png)
                no-repeat;
              background-size: 12 * @rem 7 * @rem;
              margin-left: 4 * @rem;
              transform: rotate(-180deg);
            }
          }
        }
        .pay-item {
          height: 70 * @rem;
          background: #ffffff;
          border-radius: 8 * @rem;
          border: 1 * @rem solid #efefef;
          display: flex;
          align-items: center;
          padding: 0 12 * @rem;
          box-sizing: border-box;
          margin: 0 auto 8 * @rem;

          .pay-center {
            flex: 1;
            .line {
              display: flex;
              align-items: center;
              width: 100%;
              .item-icon {
                flex-shrink: 0;
                width: 20 * @rem;
                height: 20 * @rem;
                margin-right: 10 * @rem;
              }
              .pay-name {
                height: 21 * @rem;
                font-weight: 600;
                font-size: 15 * @rem;
                color: #222222;
                line-height: 21 * @rem;
                overflow: hidden;
              }
              .recommend-icon {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 34 * @rem;
                height: 18 * @rem;
                line-height: 1;
                margin-left: 3 * @rem;
                background: #fc3a3b;
                border-radius: 4 * @rem;
                font-weight: 600;
                font-size: 11 * @rem;
                color: #ffffff;
                text-align: center;
              }
            }

            .subtitle {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              height: 18 * @rem;
              overflow: hidden;
              margin-left: 30 * @rem;
              margin-top: 4 * @rem;

              span {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 16 * @rem;
                line-height: 16 * @rem;
                font-weight: 400;
                font-size: 10 * @rem;
                color: #fc3a3b;
                text-align: center;
                border-radius: 4 * @rem;
                border: 1 * @rem solid rgba(252, 58, 59, 0.4);
                padding: 0 4 * @rem;
                margin-right: 6 * @rem;
              }
            }
          }

          .choose {
            flex-shrink: 0;
            width: 18 * @rem;
            height: 18 * @rem;
            border-radius: 50%;
            border: 1 * @rem solid #e5e6eb;

            &.on {
              background: url(~@/assets/images/recharge/pay-selected-circle.png)
                no-repeat;
              background-size: 18 * @rem 18 * @rem;
              border: none;
            }
          }
        }
        .more-pay-list {
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 500;
          font-size: 13 * @rem;
          color: #666666;
          line-height: 18 * @rem;
          margin: 20 * @rem 20 * @rem 0 20 * @rem;

          &::after {
            content: '';
            display: block;
            width: 12 * @rem;
            height: 7 * @rem;
            background: url(~@/assets/images/recharge/gray-bottom-arrow.png)
              no-repeat;
            background-size: 12 * @rem 7 * @rem;
            margin-left: 4 * @rem;
            transition: all 0.2s linear;
          }
        }
      }
    }
  }
  .order-btn {
    position: absolute;
    box-sizing: border-box;
    padding: 0 12 * @rem 0 18 * @rem;
    width: 100%;
    bottom: 0;
    left: 0;
    height: 68 * @rem;
    background: #ffffff;
    box-shadow: 0px -1px 6px 0px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .pay-number {
      display: flex;
      align-items: flex-end;
      height: 22 * @rem;
      line-height: 22 * @rem;
      .text1 {
        font-weight: 600;
        font-size: 12 * @rem;
        line-height: 12 * @rem;
        color: #191b1f;
      }
      .unit {
        margin-left: 4 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #ff6649;
        line-height: 12 * @rem;
      }
      .num {
        line-height: 22 * @rem;
        margin-right: 3 * @rem;
        font-weight: 600;
        font-size: 22 * @rem;
        color: #ff6649;
      }
      .text2 {
        line-height: 12 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #60666c;
      }
    }
    .pay-btn {
      width: 98 * @rem;
      height: 38 * @rem;
      background: #1cce94;
      color: #ffffff;
      border-radius: 25 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14 * @rem;
      box-sizing: border-box;
      padding: 0 21 * @rem;
    }
  }
}
.text-scroll {
  white-space: nowrap;
  animation: scroll-left 3s linear forwards infinite;
}
.text-scroll-fast {
  white-space: nowrap;
  animation: scroll-left-fast 3s linear forwards infinite;
}
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(-50%);
  }
}
@keyframes scroll-left-fast {
  0% {
    transform: translateX(0);
  }
  70% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(-50%);
  }
}
</style>
