<template>
  <div v-if="loading" class="game-select">
    <nav-bar-2 :border="true">
      <template #center>
        <div class="search-bar">
          <div class="search-icon"></div>
          <div class="search-input">
            <input
              v-model="key"
              type="text"
              :placeholder="$t('请输入返利的游戏名')"
            />
          </div>
        </div>
      </template>
    </nav-bar-2>
    <game-list v-if="list.length > 0" :gameList="list"></game-list>
    <div v-if="list.length === 0" class="empty">
      <div class="text">{{ $t('您暂无可申请返利的游戏哦') }}</div>
      <div class="text color">{{ $t('点击重试') }}</div>
    </div>
  </div>
</template>
<script>
import { ApiRebateGameList } from '@/api/views/rebate';
import GameList from '../components/GameList';

export default {
  name: 'RebateGamename',
  data() {
    return {
      list: [],
      loading: false,
      key: '',
    };
  },
  watch: {
    async key() {
      const res = await ApiRebateGameList({ keyword: this.key });
      this.list = res.data.list;
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const toast1 = this.$toast.loading({
        message: this.$t('加载中...'),
        forbidClick: true,
      });
      const res = await ApiRebateGameList();
      toast1.clear();
      this.loading = true;
      this.list = res.data.list;
    },
  },
  components: {
    GameList,
  },
};
</script>
<style lang="less" scoped>
/deep/ .van-nav-bar__title {
  max-width: 270 * @rem;
}
.search-bar {
  box-sizing: border-box;
  padding: 0 18 * @rem;
  width: 270 * @rem;
  height: 33 * @rem;
  border-radius: 17 * @rem;
  display: flex;
  align-items: center;
  background-color: #fff;
  .search-icon {
    width: 17 * @rem;
    height: 17 * @rem;
    background: url(~@/assets/images/search-icon.png) center center no-repeat;
    background-size: 17 * @rem 17 * @rem;
  }
  .search-input {
    flex: 1;
    height: 33 * @rem;
    margin-left: 7 * @rem;
    form {
      border: 0;
      outline: 0;
      display: block;
      width: 100%;
      height: 100%;
    }
    input {
      border: 0;
      outline: 0;
      display: block;
      width: 100%;
      height: 100%;
      background-color: transparent;
      font-size: 14 * @rem;
      color: #333;
    }
  }
}
.right-btn {
  color: #ffffff;
  font-size: 15 * @rem;
  width: 100%;
}

.empty {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translate(0, -50%);
  width: 100%;
  .text {
    width: 100%;
    margin-bottom: 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    &.color {
      color: @themeColor;
    }
  }
}
</style>
