<template>
  <div class="tx-recharge-area">
    <nav-bar-2
      title="腾讯游戏充值专区"
      :bgStyle="navbarOpacity < 0.5 ? 'transparent-white' : 'transparent'"
      :placeholder="false"
      :azShow="true"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
    </nav-bar-2>
    <main>
      <div class="list">
        <div
          v-for="item in list"
          :key="item.id"
          @click="toRechargeUrl(item.recharge_url)"
          class="item"
        >
          <img :src="item.icon" class="game-img" />
          <div class="game-title">{{ item.name }}</div>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { ApiTxRechargeArea } from '@/api/views/weekWelfare';
import { BOX_openInBrowser } from '@/utils/box.uni';
import { isIosBox } from '@/utils/userAgent';

export default {
  name: 'TxRechargeArea',
  data() {
    return {
      navbarOpacity: 0,
      list: [],
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    const res = await ApiTxRechargeArea();
    this.list = res.data.list;
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    toRechargeUrl(url) {
      if (isIosBox) {
        BOX_openInBrowser({ h5_url: url }, {});
      } else {
        window.open(url);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.tx-recharge-area {
  .image-bg('~@/assets/images/welfare/txrechargearea_bg.png');
  background-color: #f5f5f6;
  min-height: 100vh;
  overflow: hidden;
}
main {
  margin-top: 230 * @rem;
  background-color: #f5f5f6;
  min-height: 200 * @rem;
  border-radius: 20 * @rem;
  .list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 20 * @rem 12 * @rem;
    .item {
      flex: 0 0 48%;
      display: flex;
      align-items: center;
      margin-bottom: 10 * @rem;
      background: #fff;
      width: 170 * @rem;
      height: 60 * @rem;
      border-radius: 10 * @rem;
      padding: 0 10 * @rem;
      box-sizing: border-box;
      .game-img {
        width: 40 * @rem;
        height: 40 * @rem;
        margin-right: 10 * @rem;
      }
      .game-title {
        font-size: 14 * @rem;
        font-family:
          PingFangSC-Medium,
          PingFang SC;
        font-weight: 500;
        color: #383838;
      }
    }
  }
}
</style>
