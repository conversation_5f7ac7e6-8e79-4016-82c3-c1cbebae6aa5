<template>
  <div class="container">
    <div class="content-container">
      <div class="top-line">
        <div class="date">
          {{ getLastWeek(Date.now())[0] }} -
          {{ getLastWeek(Date.now())[1] }}榜单
        </div>
        <div class="update">每周一1点更新榜单</div>
      </div>
      <template v-if="rankList.length">
        <div class="rank-list">
          <template v-for="(item, index) in rankList">
            <div class="rank-item" :key="index">
              <div class="num">{{ index + 1 }}</div>
              <user-avatar
                class="avatar"
                :self="false"
                :src="item.avatar"
              ></user-avatar>
              <div class="nickname">{{ item.nickname }}</div>
              <div class="gold"
                >共得<span>{{ item.sum_gold }}</span
                >金币</div
              >
            </div>
          </template>
        </div>
        <div class="rank-tips">仅展示前五名用户</div>
      </template>
      <content-empty v-else></content-empty>
    </div>
  </div>
</template>

<script>
import { ApiBountyTaskGetBountyHunter } from '@/api/views/bounty.js';
export default {
  props: {
    hunter_subtitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      rankList: [],
    };
  },
  async created() {
    await this.getTaskList();
  },
  methods: {
    async getTaskList() {      
      const res = await ApiBountyTaskGetBountyHunter({
        page: this.page,
        listRows: this.listRows,
      });
      this.rankList.push(...res.data);
    },
    getLastWeek(date) {
      //根据传入日期，获取该日期的上一周
      let day = new Date(date).getTime();
      //判断传入日期是周几
      let dayCode = new Date(day).getDay() > 0 ? new Date(day).getDay() : 7;
      let dayTime = 24 * 60 * 60 * 1000;
      let Mon = new Date(day - dayTime * (dayCode - 1)); //传入时间的周一
      // Mon = this.dateFormat(Mon);

      let endDate = new Date(new Date(Mon).getTime() - dayTime); //上周日
      let startDate = new Date(new Date(endDate).getTime() - 6 * dayTime); //上周一
      return [this.dateFormatFn(startDate), this.dateFormatFn(endDate)];
    },

    //日期转换，输出格式为yyyy-MM-dd
    dateFormatFn(date) {
      let m = new Date(date).getMonth() + 1;
      let d = new Date(date).getDate();
      return `${m < 10 ? '0' + m : m}.${d < 10 ? '0' + d : d}`;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  padding-bottom: 20 * @rem;
  .content-container {
    box-sizing: border-box;
    border-radius: 12 * @rem;
    width: 355 * @rem;
    max-height: 542 * @rem;
    min-height: 446 * @rem;
    background: #fff;
    margin: 0 auto 0;
    position: relative;
    padding-bottom: 68 * @rem;
    padding-top: 26 * @rem;
    overflow: hidden;
    .top-bar {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;

      p {
        background: url(~@/assets/images/welfare/bounty-mission-bg1.png)
          no-repeat center 0;
        width: 100%;
        height: 38 * @rem;
      }

      span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -65%);
        font-weight: 600;
        font-size: 15 * @rem;
        color: #284f86;
      }
    }
    .top-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .date {
        height: 28 * @rem;
        line-height: 28 * @rem;
        padding: 0 8 * @rem;
        border-radius: 0 14 * @rem 14 * @rem 0;
        font-size: 13 * @rem;
        color: #284f86;
        font-weight: 500;
        background-image: linear-gradient(45deg, #e2ffca 0%, #b4f9de 100%);
      }
      .update {
        font-weight: 400;
        font-size: 12 * @rem;
        color: #97a5ba;
        padding-right: 12 * @rem;
      }
    }
    .rank-list {
      padding: 24 * @rem 13 * @rem 0;
      display: grid;
      gap: 11 * @rem;
      .rank-item {
        box-sizing: border-box;
        width: 329 * @rem;
        height: 68 * @rem;
        padding: 0 10 * @rem;
        display: flex;
        align-items: center;
        border-radius: 8 * @rem;
        .num {
          width: 34 * @rem;
          height: 26 * @rem;
          background-size: 34 * @rem 26 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22 * @rem;
          color: #333333;
          font-weight: 600;
        }
        &:nth-of-type(1) {
          background: linear-gradient(90deg, #fff5d6, #ffffff 70%);
          .num {
            background-image: url(~@/assets/images/welfare/bounty-rank-1.png);
            text-indent: -99999 * @rem;
          }
        }
        &:nth-of-type(2) {
          background: linear-gradient(90deg, #d1fbf1, #ffffff 70%);
          .num {
            background-image: url(~@/assets/images/welfare/bounty-rank-2.png);
            text-indent: -99999 * @rem;
          }
        }
        &:nth-of-type(3) {
          background: linear-gradient(90deg, #fbe5d1, #ffffff 70%);
          .num {
            background-image: url(~@/assets/images/welfare/bounty-rank-3.png);
            text-indent: -99999 * @rem;
          }
        }
        &:nth-of-type(4) {
          .num {
            background-image: url(~@/assets/images/welfare/bounty-rank-4.png);
            text-indent: -99999 * @rem;
          }
        }
        &:nth-of-type(5) {
          .num {
            background-image: url(~@/assets/images/welfare/bounty-rank-5.png);
            text-indent: -99999 * @rem;
          }
        }
        .avatar {
          width: 36 * @rem;
          height: 36 * @rem;
          margin-left: 10 * @rem;
        }
        .nickname {
          font-size: 12 * @rem;
          color: #303236;
          margin-left: 8 * @rem;
          margin-right: auto;
        }
        .gold {
          font-size: 12 * @rem;
          color: #60666c;

          span {
            font-weight: bold;
            font-size: 18 * @rem;
            color: #ff850a;
            line-height: 21 * @rem;
          }
        }
      }
    }
    .rank-tips {
      width: 248 * @rem;
      height: 18 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: 20 * @rem;
      left: 50%;
      transform: translateX(-50%);
      font-size: 13 * @rem;
      color: #97a5ba;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 62 * @rem;
        height: 1 * @rem;
        background: linear-gradient(
          90deg,
          #d4dae3 0%,
          rgba(212, 218, 227, 0) 100%
        );
      }
      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 62 * @rem;
        height: 1 * @rem;
        background: linear-gradient(
          90deg,
          #d4dae3 0%,
          rgba(212, 218, 227, 0) 100%
        );
      }
    }
  }
}
</style>
