<template>
  <div class="yy-download-popup">
    <!-- 安装配置文件，是safari -->
    <van-popup
      v-model="_getUdidPopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      class="grqPopup"
    >
      <div class="top-right" @click="setGetUdidPopupShow(false)">
        {{ $t('暂不安装') }}
      </div>
      <div class="container">
        <div class="text">
          {{ $t('安装至尊版游戏前须安装苹果系统描述文件。安装时点击“')
          }}<span class="color1">{{ $t('允许') }}</span
          >{{ $t('”，切勿点击“') }}<span class="color2">{{ $t('忽略') }}</span
          >”
        </div>
        <div
          :class="{ loading: grqLoading }"
          @click="checkUdid()"
          class="button"
        >
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-popup>
    <!-- 个人签安装进度 -->
    <van-dialog
      v-model="_downloadGrqDialogShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="downloadGrqDialog"
    >
      <div v-for="(item, index) in grqStatusName" :key="index" class="item">
        <div class="left">{{ item }}</div>
        <div
          class="right"
          :class="{
            loading: grqStatus === index,
            success: grqStatus === index + 1 || grqStatus === index + 2,
          }"
        ></div>
      </div>
      <div class="text">{{ $t('请不要离开此页面并保持屏幕常亮') }}</div>
      <div @click="toPage('GrqList')" class="button">
        {{ $t('查看进度') }}<i class="icon"></i>
      </div>
    </van-dialog>
    <!-- 无论个人签企业签在下载的时候都给他弹的提示窗口 -->
    <van-popup
      v-model="_downloadPopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      round
      class="no-safari-popup"
    >
      <div class="title">
        {{ $t('下载小贴士')
        }}<span @click="setDownloadPopupShow(false)" class="close"></span>
      </div>
      <div class="content">
        <div class="item">
          <div class="big-text">
            <span class="sign">1.</span
            ><span class="text">{{ $t('同意安装游戏') }}</span>
          </div>
          <div class="small-text">{{ $t('游戏将在桌面自动下载哦') }}</div>
        </div>
        <div class="item">
          <div class="big-text">
            <span class="sign">2.</span
            ><span class="text">{{ $t('游戏安装成功后') }}</span>
          </div>
          <div class="small-text">
            {{ $t('若需授权信任，请打开')
            }}<span class="color green">{{
              $t('设置->通用->设备管理->信任企业级应用授权哦')
            }}</span>
          </div>
        </div>
        <div class="item">
          <div class="big-text">
            <span class="sign">3.</span
            ><span class="text">{{ $t('无法弹出安装弹窗') }}</span>
          </div>
          <div class="small-text">
            {{ $t('请复制链接后，在“Safari”浏览器打开。')
            }}<span @click="copy()" class="color blue">{{ $t('复制') }}</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
export default {
  computed: {
    ...mapGetters({
      getUdidPopupShow: 'game/getUdidPopupShow', //是否显示UDID获取弹窗,
      downloadGrqDialogShow: 'game/downloadGrqDialogShow', //是否显示个人签下载弹窗
      grqStatus: 'game/grqStatus', //签名状态
      grqLoading: 'game/grqLoading', //个人签loading
      noSafariShow: 'game/noSafariShow', //是ios但是非safari弹窗显示
      downloadPopupShow: 'game/downloadPopupShow', //IOS下载弹窗
    }),
    _getUdidPopupShow: {
      //是否显示UDID获取弹窗,
      get() {
        return this.getUdidPopupShow;
      },
      set(value) {
        this.setGetUdidPopupShow(value);
      },
    },
    _downloadGrqDialogShow: {
      //是否显示个人签下载弹窗
      get() {
        return this.downloadGrqDialogShow;
      },
      set(value) {
        this.setDownloadGrqDialogShow(value);
      },
    },
    _downloadPopupShow: {
      //IOS下载弹窗
      get() {
        return this.downloadPopupShow;
      },
      set(value) {
        this.setDownloadPopupShow(value);
      },
    },
    // 个人签进度状态名
    grqStatusName() {
      switch (this.grqStatus) {
        case 0:
          return [this.$t('打包中'), this.$t('签名'), this.$t('下载')];
        case 1:
          return [this.$t('打包完成'), this.$t('签名中'), this.$t('下载')];
        case 2:
          return [this.$t('打包完成'), this.$t('签名成功'), this.$t('下载中')];
        case -1:
          return [this.$t('打包完成'), this.$t('签名失败'), this.$t('下载')];
        default:
          return [this.$t('打包完成'), this.$t('签名成功'), this.$t('下载中')];
      }
    },
  },
  methods: {
    ...mapMutations({
      setGetUdidPopupShow: 'game/setGetUdidPopupShow',
      setDownloadGrqDialogShow: 'game/setDownloadGrqDialogShow',
      setDownloadPopupShow: 'game/setDownloadPopupShow',
      setGrqStatus: 'game/setGrqStatus',
      setGrqLoading: 'game/setGrqLoading',
    }),
    // 检查udid是否获取
    async checkUdid() {
      this.setGrqLoading(true);
      try {
        await ApiCheckUdid({ game_id: this.detail.id });
      } finally {
        this.setGrqLoading(false);
      }
      this.setGetUdidPopupShow(false);
      this.setDownloadGrqDialogShow(true);
      this.handleGrqDownload();
    },
    // 个人签弹窗处理
    async handleGrqDownload() {
      // 循环调用接口，更改各个状态
      const res = await ApigrqGameList({ game_id: this.detail.id });
      switch (parseInt(res.data.list[0].grq_status)) {
        case 0:
          this.setGrqStatus(0);
          setTimeout(this.handleGrqDownload, 4000);
          break;
        case 1:
          this.setGrqStatus(1);
          setTimeout(this.handleGrqDownload, 4000);
          break;
        case 2:
          this.setDownloadGrqDialogShow(false);
          // 下载游戏
          BOX_openInBrowser({ h5_url: res.data.list[0].grq_down_ip });
          this.setGrqStatus(2);
          break;
        case 3:
        case 4:
          this.setDownloadGrqDialogShow(false);
          this.$toast(this.$t('签名失败'));
          this.setGrqStatus(-1);
          break;
      }
    },
    // copy
    copy() {
      this.$copyText(window.location.href).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.grqPopup {
  box-sizing: border-box;
  padding: 20 * @rem 14 * @rem;
  .top-right {
    position: absolute;
    right: 14 * @rem;
    font-size: 14 * @rem;
    color: #666;
  }
  .container {
    margin-top: 50 * @rem;
    font-size: 16 * @rem;
    line-height: 28 * @rem;
    .color2 {
      color: #ff475d;
    }
    .color1 {
      color: @themeColor;
    }
  }
  .button {
    width: 200 * @rem;
    height: 40 * @rem;
    margin: 20 * @rem auto 5 * @rem;
    text-align: center;
    line-height: 40 * @rem;
    background: @themeBg;
    color: #fff;
    border-radius: 25px;
    &.loading {
      position: relative;
      font-size: 0;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: block;
        width: 16 * @rem;
        height: 16 * @rem;
        background-size: 16 * @rem 16 * @rem;
        background-image: url(~@/assets/images/downloadLoading.png);
        animation: rotate 1s infinite linear;
      }
    }
  }
}
.downloadGrqDialog {
  box-sizing: border-box;
  width: 280 * @rem;
  padding: 16 * @rem;
  border-radius: 10 * @rem;
  .item {
    display: flex;
    justify-content: space-between;
    margin-top: 15 * @rem;
    .right {
      width: 16 * @rem;
      height: 16 * @rem;
      background-image: url(~@/assets/images/games/success2.png);
      background-size: 100%;
      &.loading {
        background-image: url(~@/assets/images/games/refresh.png);
        animation: roll 1s linear infinite;
      }
      &.success {
        background-image: url(~@/assets/images/games/success.png);
      }
      &.fail {
        background-image: url(~@/assets/images/games/success2.png);
      }
    }
  }
  .text {
    margin-top: 30 * @rem;
    text-align: center;
    font-size: 14 * @rem;
    color: #ff8a00;
  }
  .button {
    width: 180 * @rem;
    height: 37 * @rem;
    margin: 15 * @rem auto 0;
    text-align: center;
    line-height: 37 * @rem;
    background: #fff4e7;
    color: #ff8a00;
    border-radius: 6 * @rem;
    .icon {
      position: relative;
      top: -1 * @rem;
      left: 3 * @rem;
      display: inline-block;
      width: 6 * @rem;
      height: 8 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/games/arrow-right.png);
      background-repeat: no-repeat;
    }
  }
}
.no-safari-popup {
  .title {
    height: 44 * @rem;
    text-align: center;
    line-height: 44 * @rem;
    font-size: 16 * @rem;
    border-bottom: 1px solid #dedede;
  }
  .close {
    position: absolute;
    top: 14 * @rem;
    right: 14 * @rem;
    width: 14 * @rem;
    height: 14 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-size: no-repeat;
  }
  .content {
    padding: 14 * @rem;
    .item {
      margin-bottom: 20 * @rem;
    }
    .big-text {
      line-height: 21 * @rem;
      font-size: 15 * @rem;
      .sign {
        font-weight: bolder;
      }
    }
    .small-text {
      margin-top: 5 * @rem;
      line-height: 16 * @rem;
      font-size: 12 * @rem;
      i.icon {
        position: relative;
        top: 3 * @rem;
        display: inline-block;
        width: 16 * @rem;
        height: 16 * @rem;
        margin: 0 5 * @rem;
        background-image: url(~@/assets/images/safari-icon-32x32.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
      .color {
        &.red {
          color: #f63838;
        }
        &.blue {
          color: #51adff;
        }
        &.green {
          color: @themeColor;
        }
      }
    }
    .button {
      width: 240 * @rem;
      height: 40 * @rem;
      margin: 20 * @rem auto;
      text-align: center;
      line-height: 40 * @rem;
      font-size: 14 * @rem;
      color: #fff;
      background: @themeBg;
      border-radius: 10 * @rem;
    }
    .explain {
      margin-bottom: 10 * @rem;
      text-align: center;
      color: #999;
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes roll {
  0% {
    transform: rotate(0deg) translate(0, 1px);
  }
  25% {
    transform: rotate(90deg) translate(1px, 0);
  }
  50% {
    transform: rotate(180deg) translate(0, -1px);
  }
  75% {
    transform: rotate(270deg) translate(-1px, 0);
  }
  100% {
    transform: rotate(360deg) translate(0, 1px);
  }
}
</style>
