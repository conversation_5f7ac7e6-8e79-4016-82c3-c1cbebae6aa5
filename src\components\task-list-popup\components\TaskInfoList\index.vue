<template>
  <div>
    <template v-for="item in TaskInfoList">
      <div class="task-info-item" :key="item.id">
        <div class="left-box" @click="toPageDetail(item)">
          <div class="task-icon">
            <img v-show="item.icon" :src="item.icon" alt="" />
          </div>
          <div class="task-info">
            <div class="signIn">
              <div class="name">{{ item.title }}</div>
              <div class="time-icon" v-if="item.duration">
                <div class="icon">
                  <img src="~@/assets/images/games/task-item-icon.png" alt="" />
                </div>
                <div class="text">+{{ item.duration }}分钟</div>
              </div>
            </div>
            <div class="describe">{{ item.desc }}</div>
          </div>
        </div>
        <div
          class="right-btn"
          v-if="item.type === 0"
          :class="{ signIn: item.is_done === 1 }"
        >
          <div v-if="item.is_done === 0" @click="clickSignIn()">签到</div>
          <div v-if="item.is_done === 1">已完成</div>
        </div>
        <div
          v-if="item.type !== 0"
          class="right-btn"
          :class="{ receive: item.is_done === 1, signIn: item.is_done === 2 }"
        >
          <div v-if="item.is_done === 0" @click="clickGoComplete(item)"
            >去完成<span v-if="item.is_circle"
              >{{ item.used_num }}/{{ item.num }}</span
            >
          </div>
          <div v-if="item.is_done === 1" @click="clickReceive(item)">领取</div>
          <div v-if="item.is_done === 2">已完成</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  ApiCloudCloudTaskDaySign,
  ApiCloudCloudTaskTake,
  ApiCloudCloudTaskReward,
} from '@/api/views/game.js';
import useTaskToast from '../TaskToast/index.js';
import { triggerIncentiveVideo, navigateToGameDetail } from '@/utils/function';
import { mapMutations } from 'vuex';
import { getPcCloudGameInfoCallback } from '@/utils/function.js';
export default {
  name: 'taskInfoList',
  props: {
    TaskInfoList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      watchTheVideo: false,
    };
  },
  methods: {
    toPageDetail(item) {
      if (item.type !== 2) return;
      const itemTask = JSON.parse(JSON.stringify(item));
      const updatedTask = {
        ...itemTask,
        id: itemTask.game_id,
        index: itemTask.id,
      };
      navigateToGameDetail(updatedTask);
    },
    // 签到
    async clickSignIn() {
      try {
        const res = await ApiCloudCloudTaskDaySign();
        useTaskToast(
          {
            msg: res.msg,
          },
          1000,
        );
        await this.refreshDataType();
        await getPcCloudGameInfoCallback();
      } catch (error) {}
    },
    // 去完成
    async clickGoComplete(item) {
      await this.takeTask(item);
    },
    async takeTask(item) {
      try {
        const res = await ApiCloudCloudTaskTake({ task_id: item.id });
        if ((res.code == 3 || res.code == 1) && item.type == 1) {
          // 激励视频观看完毕后 需根据ios马甲包回调 手动触发clickReceive
          await triggerIncentiveVideo(item);
        } else if ((res.code == 3 || res.code == 1) && item.type == 2) {
          await this.toPageDetail(item);
        }
        await this.refreshDataType();
      } catch (error) {
        // 处理错误
      }
    },
    // 领取
    async clickReceive(item) {
      try {
        const res = await ApiCloudCloudTaskReward({ task_id: item.id });
        useTaskToast(
          {
            msg: res.msg,
          },
          1000,
        );
        await this.refreshDataType();
        await getPcCloudGameInfoCallback();
      } catch (error) {}
    },
    // 更新任务状态
    refreshDataType() {
      this.$nextTick(() => {
        this.$emit('refreshDataType');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.task-info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left-box {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    width: 250 * @rem;
    flex-shrink: 0;
    .task-icon {
      width: 36 * @rem;
      height: 36 * @rem;
      border-radius: 4 * @rem;
      overflow: hidden;
      background: #eee;
    }
    .task-info {
      margin-left: 8 * @rem;
      flex: 1;
      min-width: 0;
      .signIn {
        display: flex;
        align-items: center;
        .name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          height: 20 * @rem;
          font-weight: 500;
          font-size: 14 * @rem;
          color: #222222;
          line-height: 20 * @rem;
        }
        .time-icon {
          margin-left: 4 * @rem;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          .icon {
            width: 16 * @rem;
            height: 16 * @rem;
          }
          .text {
            margin-left: 1 * @rem;
            height: 15 * @rem;
            font-weight: 500;
            font-size: 11 * @rem;
            color: #fa9543;
            line-height: 15 * @rem;
          }
        }
      }
      .describe {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-top: 4 * @rem;
        height: 14 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #7a7a7a;
        line-height: 14 * @rem;
      }
    }
  }
  .right-btn {
    margin-left: 12 * @rem;
    min-width: 72 * @rem;
    max-width: 120 * @rem;
    height: 28 * @rem;
    line-height: 28 * @rem;
    text-align: center;
    color: #fff;
    border-radius: 25 * @rem;
    background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
    font-size: 12 * @rem;
    font-weight: normal;
    white-space: nowrap;
    div {
      padding: 0 8 * @rem;
    }
    &.signIn {
      background: #cccccc;
      color: #ffffff;
    }
    &.receive {
      background: #ecfbf4;
      color: #21b98a;
    }
  }
  &:not(:first-child) {
    margin-top: 35 * @rem;
  }
  &:last-child {
    margin-bottom: 35 * @rem;
  }
}
</style>
