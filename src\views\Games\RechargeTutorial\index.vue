<template>
  <div class="recharge-tutorial" :class="{ centered: isLoading }">
    <nav-bar-2 title="充值教程" :border="true" :azShow="true"></nav-bar-2>
    <loading-indicator v-if="isLoading"></loading-indicator>
    <div class="main" v-else>
      <div class="top-area">
        <div class="title">{{ title }}</div>
        <div class="time">{{ time }}</div>
      </div>
      <div class="content" v-html="content"></div>
      <load-more
        v-if="content.length"
        v-model="loading"
        :finished="finished"
        finishedText="已经到底~~"
      >
      </load-more>
    </div>
  </div>
</template>
<script>
import LoadingIndicator from '@/components/loading-Indicator';
export default {
  name: 'RechargeTutorial',
  components: {
    LoadingIndicator,
  },
  data() {
    return {
      isLoading: true,
      tutorial_id: '',
      title: '',
      time: '',
      content: '',
      loading: false,
      finished: true,
    };
  },
  computed: {},
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      this.tutorial_id = this.$route.params.id;
      this.isLoading = false;
    },
    returnTop() {
      window.scrollTo(0, 0);
    },
  },
};
</script>
<style lang="less" scoped>
.recharge-tutorial {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
  &.centered {
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
  }
  .main {
    margin-top: 16 * @rem;
    padding: 0 12 * @rem;
    .top-area {
      .title {
        font-weight: 600;
        font-size: 18 * @rem;
        color: #191b1f;
        line-height: 27 * @rem;
      }
      .time {
        margin-top: 12 * @rem;
        font-weight: 400;
        font-size: 13 * @rem;
        color: #93999f;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
</style>
