<template>
  <van-popup
    v-model="popupShow"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    position="bottom"
    round
    get-container="body"
    class="fanli-popup"
  >
    <div class="title">返利活动</div>
    <div class="close-btn" @click="popupShow = false"></div>
    <div class="popup-content">
      <content-empty v-if="empty" :tips="tips"></content-empty>
      <load-more
        v-else
        class="list-container"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
      >
        <div class="active-list">
          <div
            class="active-item"
            v-for="(active, activeIndex) in fanliList"
            :key="activeIndex"
          >
            <div class="info">
              <div class="name">{{ active.title }}</div>
              <div class="time">{{ active.time_text }}</div>
            </div>
            <div class="kefu" @click="openKefu">联系客服</div>
          </div>
        </div>
      </load-more>
    </div>
  </van-popup>
</template>
<script>
import { ApiGetGameAct } from '@/api/views/game.js';
export default {
  name: 'activeList',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      required: true,
    },
  },
  data() {
    return {
      fanliList: [],
      finished: false,
      loading: false,
      empty: false,
      tips: '',
    };
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },
  methods: {
    async getRebateActivity() {
      const res = await ApiGetGameAct({
        id: this.id,
      });

      if (!res.data.list.length) {
        this.empty = true;
        this.tips = res.msg;
      } else {
        this.fanliList = res.data.list;
      }
      this.loading = false;
      this.finished = true;
    },
    async loadMore() {
      if (this.finished) return false;
      await this.getRebateActivity();
      this.loading = false;
    },
  },
};
</script>
<style lang="less" scoped>
.fanli-popup {
  display: flex;
  flex-direction: column;
  background-color: #fbfbfe;
  padding: 0 18 * @rem 20 * @rem;
  box-sizing: border-box;
  height: 60vh;

  .title {
    flex-shrink: 0;
    padding: 26 * @rem 0 24 * @rem;
    text-align: center;
    height: 22 * @rem;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #000000;
    line-height: 22 * @rem;
  }
  .close-btn {
    display: block;
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/my-game/close.png) no-repeat center;
    background-size: 16 * @rem 16 * @rem;
    padding: 10 * @rem;
    position: absolute;
    top: 15 * @rem;
    right: 8 * @rem;
  }
  .popup-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    
    .active-list {
      .active-item {
        display: flex;
        align-items: center;
        margin-top: 33 * @rem;

        &:first-of-type {
          margin-top: 0;
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            display: block;
            height: 20 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #222222;
            line-height: 20 * @rem;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .time {
            display: block;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 10 * @rem;
            color: #9a9a9a;
            line-height: 14 * @rem;
            text-align: left;
            margin-top: 4 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .kefu {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72 * @rem;
          height: 28 * @rem;
          line-height: 28 * @rem;
          background: @themeBg;
          border-radius: 19 * @rem;
          font-size: 12 * @rem;
          color: #ffffff;
          text-align: center;
          margin-left: 10 * @rem;
        }
      }
    }
  }
}
</style>
