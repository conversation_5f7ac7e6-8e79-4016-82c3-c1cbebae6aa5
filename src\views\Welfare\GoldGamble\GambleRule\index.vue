<template>
  <div class="gamble-rule-page">
    <nav-bar-2
      :title="$t('夺宝规则')"
      :border="true"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="content" v-html="content"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GambleRule',
  computed: {
    content() {
      return `
        ${this.appName}提醒您注意以下活动规则，参与活动即视为您已阅读并认可本活动规则，理解并愿意接受本活动的约束<br>
        1.用户须知<br>
        （1）每期商品设置固定数量夺宝码，所有夺宝码兑换完成后进入开奖阶段。<br>
        （2）夺宝码购买完成后，可在“我的夺宝”查看个人夺宝记录。<br>
        （3）开奖后，用户可在商品的“商品详情页”-“往期揭秘”查看商品开奖信息。<br>
        （4）中奖用户在中奖信息公示后，系统会自动发放对应的奖励给您，请及时关注。<br>
        2.开奖规则<br>
        （1）每期奖品的最后一个夺宝码兑换完后，系统自动取出该期商品的最后40个参与时间。<br>
        （2）将这40个时间(不够40则有多少取多少)的数值进行求和（得出数值A）（每个时间按时、分、秒、毫秒的顺序组合，如23:18:57:456则为231857456）<br>
        （3）夺数值A除以此奖品总夺宝码数得到的余数加原始数10000001，得到最终的中奖号码，拥有该中奖号码者获得该奖品<br>
        3.特别申明<br>
        （1）出于风险控制的原因，如用户存在恶意作弊、恶意刷号、恶意攻击，或其他非正规途径获取奖品等，${this.appName}有权拒绝提供奖品，情节严重者将有权追究其法律责任。<br>
        （2）${this.appName}可根据活动的实际情况，在法律允许的范围内，对本活动规则进行变动或调整，相关变动或调整将公布在活动规则页面上，并于公布时间即时生效。<br>
        （3）因用户操作不当或用户所在地区网络故障、电信运营商故障等${this.appName}所不能控制的原因导致用户无法参与活动或失败，${this.appName}不负法律或经济责任。<br>
        （4）如出现不可抗力或情势变更的情况（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动遭受严重网络攻击或因系统故障需要暂停举办的）则${this.appName}可依相关法律法规的规定主张免责。
      `;
    },
  },
};
</script>

<style lang="less" scoped>
.gamble-rule-page {
  background-color: #fff;
  .main {
    padding: 13 * @rem 18 * @rem 35 * @rem;
    .content {
      font-size: 14 * @rem;
      color: #000000;
      line-height: 22 * @rem;
    }
  }
}
</style>
