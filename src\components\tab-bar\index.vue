<template>
  <div
    class="tab-bar-component"
    v-show="platform != 'android' && platform != 'ios'"
  >
    <div class="tab-bar-fixed">
      <div class="tab-list">
        <template v-for="(item, index) in tabBarList">
          <div
            class="tab-item-container"
            :key="index"
            v-if="
              !(item.name == 'Welfare' && hideJfq == 1) &&
              !(item.name == 'MyGame' && (dlConfig == 1 || showMyGame == 0))
            "
          >
            <div
              v-show="showReturnTop && $route.path.includes(item.route)"
              class="tab-item btn"
              @click="handleReturnTop"
            >
              <div class="tab-icon-container">
                <div class="tab-icon active top-icon"></div>
              </div>
              <span class="tab-text active">返回顶部</span>
            </div>
            <div
              class="tab-item btn"
              @click="switchTab(item.name, item.params, index, item.click_id)"
              v-show="!(showReturnTop && $route.path.includes(item.route))"
            >
              <div class="tab-icon-container">
                <div
                  class="dot"
                  v-if="item.name == 'Mine' && unreadCount.sum > 0"
                >
                  {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
                </div>
                <yy-lottie
                  class="lottie-icon"
                  :width="48"
                  :height="48"
                  :options="{
                    autoplay: true,
                    loop: false,
                    path: item.icon_path,
                  }"
                  v-if="$route.path.includes(item.route)"
                ></yy-lottie>
                <div
                  class="tab-icon"
                  :class="[
                    { active: $route.path.includes(item.route) },
                    item.desc,
                  ]"
                  v-else
                >
                </div>
              </div>
              <span
                class="tab-text"
                :class="[{ active: $route.path.includes(item.route) }]"
                >{{ item.text }}</span
              >
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { platform } from '@/utils/box.uni.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { mapGetters, mapActions } from 'vuex';
import { getDayZeroTimestamp } from '@/utils/function.js';
export default {
  name: 'tabBar',
  data() {
    return {
      platform,
      themeColorLess,
      showReturnTop: false,
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      hideJfq: 'system/hideJfq',
      tabBarAngle: 'system/tabBarAngle',
      clickedTabBarAngle: 'system/clickedTabBarAngle',
      dlConfig: 'system/dlConfig',
      initData: 'system/initData',
      isUpApp: 'system/isUpApp',
      showMyGame: 'system/showMyGame',
    }),
    tabBarList() {
      let formaTabBar = [
        {
          text: this.$t('首页'),
          name: 'QualitySelect',
          route: 'home',
          desc: 'home-icon',
          params: {},
          click_id: 'M1',
          icon_path:
            'https://static-hw.3733.com/wa/lottie/zhuye/zhuye/data.json',
        },
        {
          text: this.$t('分类'),
          name: 'Category',
          route: 'category',
          desc: 'category-icon',
          params: {},
          click_id: 'M6',
          icon_path:
            'https://static-hw.3733.com/wa/lottie/fenlei/fenlei/data.json',
        },
        {
          text: this.$t('在玩'),
          name: 'MyGame',
          route: 'my_game',
          desc: 'my-game-icon',
          params: {},
          click_id: 'M7',
          icon_path:
            'https://static-hw.3733.com/wa/lottie/wodeyouxi/wodeyouxi/data.json',
        },
        {
          text: this.$t('福利中心'),
          name: 'Welfare',
          route: 'welfare',
          desc: 'welfare-icon',
          params: {},
          click_id: 'M4',
          icon_path:
            'https://static-hw.3733.com/wa/lottie/fulizhongxin/fulizhongxin/data.json',
        },
        {
          text: this.$t('我的'),
          name: 'Mine',
          route: 'mine',
          desc: 'mine-icon',
          params: {},
          click_id: 'M5',
          icon_path: 'https://static-hw.3733.com/wa/lottie/wode/wode/data.json',
        },
      ];
      if (this.isUpApp) {
        formaTabBar.splice(
          1,
          2,
          {
            text: '游戏库',
            name: 'GameLibrary',
            route: 'game_library',
            desc: 'game-library-icon',
            params: {},
            icon_path:
              'https://static-hw.3733.com/wa/lottie/youxiku/youxiku/data.json',
          },
          {
            text: '排行',
            name: 'UpRank',
            route: 'up_rank',
            desc: 'rank-icon',
            params: {},
            click_id: 'M2',
            icon_path:
              'https://static-hw.3733.com/wa/lottie/paihangbang/paihangbang/data.json',
          },
        );
      }
      return formaTabBar;
    },
  },
  created() {
    this.addScrollEvent();
  },
  methods: {
    ...mapActions({
      SET_CLICKED_TAB_BAR_ANGLE: 'system/SET_CLICKED_TAB_BAR_ANGLE',
    }),
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 100) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 100) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    showTag(index) {
      let findResult = this.tabBarAngle.findIndex(item => {
        return item.navigation == index + 1;
      });
      let clicked = this.clickedTabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult != -1) {
        if (
          clicked &&
          clicked.dateTimeStamp ==
            getDayZeroTimestamp(new Date().getTime() / 1000)
        ) {
          return false;
        }
        return true;
      } else {
        return false;
      }
    },
    tagContent(index) {
      let findResult = this.tabBarAngle.find(item => {
        return item.navigation == index + 1;
      });
      if (findResult) {
        return findResult.angle;
      } else {
        return false;
      }
    },
    switchTab(name, params = {}, index, click_id) {

      // 神策埋点
      if(name  == 'Mine') {
        this.$sensorsTrack('my_profile_page_click')
      }
      
      this.showReturnTop = false;
      // 根据dl_config隐藏福利中心的按钮
      if (name == 'Welfare' && this.hideJfq == 1) {
        return false;
      }
      // 点击tabbar消除角标
      this.SET_CLICKED_TAB_BAR_ANGLE(index + 1);
      if (click_id) {
        this.CLICK_EVENT(click_id);
      }
      //跳转页面
      this.$router.push({ name: name, params: params });
      setTimeout(() => {
        this.addScrollEvent();
      }, 5000);
    },

    // 自定义节流函数
    throttle(func, delay) {
      let timeoutId;
      return function (...args) {
        if (!timeoutId) {
          timeoutId = setTimeout(() => {
            func.apply(this, args);
            timeoutId = null;
          }, delay);
        }
      };
    },
  },
};
</script>

<style lang="less" scoped>
.tab-bar-component {
  width: 100%;
  // height: 50 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  flex-shrink: 0;
}
.tab-bar-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  .fixed-center;
  border-top: 1px solid #f5f5f6;
  background: #fff;
  z-index: 2000;
  .tab-list {
    height: 50 * @rem;
    display: flex;
    .tab-item-container {
      flex: 1;
      min-width: 0;
    }
    .tab-item {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .tab-icon-container {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: -10 * @rem auto 0;
      }
      .lottie-icon {
        width: 48 * @rem;
        width: 48 * @rem;
      }
      .dot {
        position: absolute;
        left: 60%;
        top: 5 * @rem;
        padding: 0 5 * @rem;
        height: 14 * @rem;
        border-radius: 7 * @rem;
        background-color: #fe4a55;
        color: #fff;
        font-size: 10 * @rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99;
      }
      .tab-icon {
        width: 48 * @rem;
        height: 48 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 24 * @rem 24 * @rem;
        position: relative;
        &.top-icon {
          background-image: url(../../assets/images/tab-bar/return-top.png);
        }
        &.home-icon {
          background-image: url(../../assets/images/tab-bar/home.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/home-selected.png);
          }
        }
        &.category-icon {
          background-image: url(../../assets/images/tab-bar/category.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/category-selected.png);
          }
        }
        &.my-game-icon {
          background-image: url(../../assets/images/tab-bar/my-game-icon.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/my-game-icon-selected.png);
          }
        }
        &.welfare-icon {
          background-image: url(../../assets/images/tab-bar/welfare-icon.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/welfare-icon-selected.png);
          }
        }
        &.mine-icon {
          background-image: url(../../assets/images/tab-bar/mine.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/mine-selected.png);
          }
        }
        &.rank-icon {
          background-image: url(../../assets/images/tab-bar/rank.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/rank-selected.png);
          }
        }
        &.game-library-icon {
          background-image: url(../../assets/images/tab-bar/library.png);
          &.active {
            background-image: url(../../assets/images/tab-bar/library-selected.png);
          }
        }
      }
      .tab-text {
        margin-top: -8 * @rem;
        color: #a4a4a4;
        font-size: 11 * @rem;
        line-height: 12 * @rem;
        &.active {
          color: #21b98a;
        }
      }
    }
  }
}
</style>
