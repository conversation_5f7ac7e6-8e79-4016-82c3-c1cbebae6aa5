<template>
  <div :style="style" ref="lottieContainer"></div>
</template>

<script>
import lottie from 'lottie-web';
import { remNumberLess } from '@/common/styles/_variable.less';

export default {
  name: 'lottie',
  props: {
    options: {
      type: Object,
      required: true,
    },
    height: Number, // 单位为@rem，按设计稿尺寸配置即可
    width: Number, // 单位为@rem，按设计稿尺寸配置即可
  },
  data() {
    return {
      remNumberLess,
      lottieAnimation: null,
    };
  },

  computed: {
    style() {
      return {
        width: this.width ? `${this.width * this.remNumberLess}rem` : '100%',
        height: this.height ? `${this.height * this.remNumberLess}rem` : '100%',
      };
    },
  },

  mounted() {
    /**
     *  animationData: 包含导出的动画数据的 Object。
     *  path: 动画对象的相对路径。（animationData 和 path 互斥）
     *  loop: 动画循环次数，可选值 true/false/number
     *  autoplay: 准备就绪后自动开始播放，可选值 true/false
     *  name: 动画名称，供将来参考
     *  renderer: 设置渲染器，可选值 svg/canvas/html
     *  container: 用于渲染动画的 DOM 元素
     */
    this.lottieAnimation = lottie.loadAnimation({
      container: this.$refs.lottieContainer,
      renderer: 'svg',
      loop: this.options.loop !== false,
      autoplay: this.options.autoplay !== false,
      animationData: this.options.animationData ?? '',
      path: this.options.path ?? '',
    });
    this.$emit('animationCreated', this.lottieAnimation);
  },

  beforeDestroy() {
    this.lottieAnimation && this.lottieAnimation.destroy();
  },
};
</script>
