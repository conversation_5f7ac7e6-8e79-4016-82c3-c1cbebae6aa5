import SwitchPages from '@/views/SwitchPages';
import Home from '@/views/SwitchPages/Home';
import QualitySelect from '@/views/SwitchPages/Home/QualitySelect';
import WelfareCenter from '@/views/Welfare/WelfareCenter';
import WelfareGoldCoin from '@/views/Welfare/WelfareGoldCoin';

export default [
  {
    path: '/',
    name: 'SwitchPages',
    component: SwitchPages,
    redirect: '/home/<USER>',
    meta: {
      keepAlive: true,
    },
    children: [
      {
        path: '/home',
        name: 'Home',
        component: Home,
        meta: {
          keepAlive: true,
          pageTitle: '首页',
        },
        children: [
          {
            path: 'quality_select',
            name: 'QualitySelect',
            component: QualitySelect,
            meta: {
              keepAlive: true,
              pageTitle: '首页-精选',
            },
          },
          {
            path: 'special_topic/:id',
            name: 'HomeSpecialTopic',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SpecialTopic'
              ),
            meta: {
              keepAlive: true,
              pageTitle: '首页-专题',
            },
          },
          {
            path: 'rank',
            name: 'Rank',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/Rank2'
              ),
            meta: {
              keepAlive: true,
              pageTitle: '首页-排行榜',
            },
          },
          {
            path: 'up_collection',
            name: 'UpCollection',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/Up/UpCollection'
              ),
            meta: {
              keepAlive: true,
            },
          },
          {
            path: 'game_avtivity/:id',
            name: 'GameActivity',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/GameActivity'
              ),
            meta: {
              keepAlive: true,
            },
          },
          {
            path: 'game_avtivity2/:id',
            name: 'GameActivity2',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/GameActivity2'
              ),
            meta: {
              keepAlive: true,
            },
          },
          {
            path: 'weekly_rewards',
            name: 'WeeklyRewards',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/WeeklyRewards'
              ),
            meta: {
              keepAlive: true,
              pageTitle: '周周领福利',
            },
          },
          {
            path: 'exclusive_activity/:id',
            name: 'ExclusiveActivityHome',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/Active/ExclusiveActivity'
              ),
            meta: {
              keepAlive: true,
              pageTitle: '首页-专服活动',
            },
          },
          {
            path: 'new_game',
            name: 'HomeNewGame',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/NewGame'
              ),
            meta: {
              keepAlive: true,
              pageTitle: '首页-新游',
            },
          },
          {
            path: 'pc_cloud_game',
            name: 'PcCloudGame',
            component: () =>
              import(
                /* webpackChunkName: "switch_pages" */
                '@/views/SwitchPages/Home/PcCloudGame'
              ),
            meta: {
              keepAlive: true,
            },
          },
        ],
      },
      {
        path: '/category',
        name: 'Category',
        component: () =>
          import(
            /* webpackChunkName: "switch_pages" */
            '@/views/SwitchPages/Category4'
          ),
        meta: {
          keepAlive: true,
          pageTitle: '分类',
        },
      },
      {
        path: '/my_game',
        name: 'MyGame',
        component: () =>
          import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
        meta: {
          requiresAuth: true,
          keepAlive: true,
          pageTitle: '在玩',
        },
      },
      {
        path: 'wel',
        redirect: '/welfare',
        component: () =>
          import(/* webpackChunkName: "switch_pages" */ '@/views/Welfare'),
        meta: {
          keepAlive: true,
        },
        children: [
          {
            path: '/welfare',
            name: 'Welfare',
            component: WelfareCenter,
            meta: {
              keepAlive: true,
              pageTitle: '福利中心-赚金币',
            },
          },
          {
            path: '/welfare_gold_coin_exchange',
            name: 'WelfareGoldCoinExchange',
            component: WelfareGoldCoin,
            meta: {
              keepAlive: true,
              pageTitle: '福利中心-金币商城',
            },
          },
        ],
      },
      {
        path: '/mine',
        name: 'Mine',
        component: () =>
          import(
            /* webpackChunkName: "switch_pages" */
            '@/views/SwitchPages/Mine'
          ),
        meta: {
          keepAlive: true,
          pageTitle: '我的',
        },
      },
      {
        path: '/game_library',
        name: 'GameLibrary',
        component: () =>
          import(
            /* webpackChunkName: "switch_pages" */
            '@/views/SwitchPages/GameLibrary'
          ),
        meta: {
          keepAlive: true,
          pageTitle: '游戏库',
        },
      },
      {
        path: '/up_rank',
        name: 'UpRank',
        component: () =>
          import(/* webpackChunkName: "switch_pages" */ '@/views/Up/UpRank'),
        meta: {
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: '/hot_game_list',
    name: 'HotGameList',
    component: () =>
      import(
        /* webpackChunkName: "games" */
        '@/views/SwitchPages/Home/PcCloudGame/HotGameList'
      ),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/new_game_list',
    name: 'NewGameList',
    component: () =>
      import(
        /* webpackChunkName: "games" */
        '@/views/SwitchPages/Home/PcCloudGame/NewGameList'
      ),
    meta: {
      keepAlive: true,
    },
  },
];