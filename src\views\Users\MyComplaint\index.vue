<template>
  <div class="my-complaint">
    <nav-bar-2
      :border="true"
      :title="$t('我的投诉')"
      :azShow="true"
    ></nav-bar-2>
    <content-empty v-if="!complaintList.length"></content-empty>
    <section v-else v-for="(item, index) of complaintList" :key="index">
      <div class="container">
        <header>
          <div class="left">{{ $t('投诉对象') }}：{{ item.object }}</div>
          <div class="right">{{ time(item.create_time) }}</div>
        </header>
        <main>
          <div class="content">{{ item.content }}</div>
          <div class="image-list" v-if="item.photos">
            <img
              v-for="(item2, index2) in imageList(item)"
              :key="index2"
              :src="item2"
              @click="imagePreview(item2)"
            />
          </div>
        </main>
        <footer :class="{ color: !item.editor_remark }">
          <div class="content">
            <span>{{ $t('官方回复') }}：</span
            >{{
              item.editor_remark ? item.editor_remark : $t('您的投诉待处理')
            }}
          </div>
        </footer>
      </div>
    </section>
  </div>
</template>
<script>
import { ApiMyComplaint } from '@/api/views/users';
import { ImagePreview } from 'vant';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'MyComplaint',
  data() {
    return {
      complaintList: [], //反馈列表
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('我的投诉');
    }
    const res = await ApiMyComplaint();
    this.complaintList = res.data.complainList;
  },
  methods: {
    time(t) {
      let { year, date } = this.$handleTimestamp(t);
      return `${year}-${date}`;
    },
    imageList(item) {
      return item.photos.split(',');
    },
    imagePreview(item2) {
      ImagePreview([item2]);
    },
  },
};
</script>

<style lang="less" scoped>
.my-complaint {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 30 * @rem;
}
section {
  position: relative;
  overflow: hidden;
}
section:last-of-type {
  margin-bottom: 1.869rem;
}
section .container {
  float: right;
  width: 8.6775rem;
  margin: 0.4005rem 0.4005rem 0 0.4005rem;
  padding: 0.267rem 0.267rem 0 0.267rem;
  background-color: #ffffff;
  border-radius: 5px;
}
section .container header {
  overflow: hidden;
}
section .container header .left {
  float: left;
  font-size: 0.3738rem;
  color: #666666;
}
section .container header .right {
  float: right;
  font-size: 0.3204rem;
  color: #888888;
}
section .container main {
  padding: 0.4005rem 0;
  border-bottom: 1px dashed #e5e5e5;
}
section .container main .content {
  line-height: 0.4806rem;
  font-size: 0.3738rem;
}
.image-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  img {
    flex: 0 0 30%;
    width: 100 * @rem;
    height: 110 * @rem;
    margin-top: 15 * @rem;
    border-radius: 5 * @rem;
    overflow: hidden;
  }
}
section .container footer {
  height: 1.1748rem;
  line-height: 1.1748rem;
  font-size: 0.3738rem;
  color: #666666;
  &.color {
    color: #158af3;
  }
  span {
    color: #666666;
  }
}
.bottom {
  position: fixed;
  display: none;
  bottom: 0;
  width: 100%;
  height: 1.2015rem;
  background-color: #ffffff;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
}
.bottom .left {
  float: left;
  color: #333333;
  line-height: 1.2015rem;
  font-size: 0.3738rem;
}
.bottom .left .icon {
  display: inline-block;
  vertical-align: -0.4806rem;
  padding: 0.36045rem 0.267rem;
  background-position: 0.267rem 0.3471rem;
}
.bottom .right {
  float: right;
  width: 1.7355rem;
  height: 0.7476rem;
  margin: 0.22695rem 0.267rem;
  text-align: center;
  line-height: 0.7476rem;
  background: @themeBg;
  border-radius: 14px;
  font-size: 0.3738rem;
  color: #ffffff;
}
</style>
