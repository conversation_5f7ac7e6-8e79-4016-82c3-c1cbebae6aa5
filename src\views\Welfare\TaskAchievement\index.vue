<template>
  <div class="task-achievement-page">
    <nav-bar-2
      bgStyle="transparent-white"
      :placeholder="false"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bar"></div>
      <div class="svip-info" @click="toSvip">
        <img src="@/assets/images/welfare/achievement-svip-ad-new.png" alt="" />
      </div>
      <pull-refresh
        class="pull-refresh"
        @refresh="onRefresh"
        v-model="isLoading"
      >
        <div class="task-list">
          <div class="task-item" v-if="taskList.sign">
            <div class="icon">
              <img :src="taskList.sign.mission_info.icon1" alt="" />
            </div>
            <div class="task-info">
              <div class="title">
                累计签到 {{ taskList.sign.mission_info.need_value }}次
              </div>
              <div class="subtitle">
                ({{ taskList.sign.total }}/{{
                  taskList.sign.mission_info.need_value
                }})
              </div>
              <div class="line">
                <div class="gold" v-if="taskList.sign.mission_info.reward">
                  +{{ taskList.sign.mission_info.reward }}金币
                </div>
              </div>
              <div class="extra" v-if="taskList.sign.mission_info.extra_exp">
                会员加成：{{ taskList.sign.mission_info.extra_exp }}
              </div>
            </div>
            <template v-if="!Boolean(taskList.sign.mission_info.all_finish)">
              <div
                class="task-btn btn get"
                v-if="
                  taskList.sign.total >= taskList.sign.mission_info.need_value
                "
                @click="handleGet(taskList.sign.mission_info, 'sign')"
              >
                {{ $t('领取') }}
              </div>
              <div
                class="task-btn btn"
                v-else
                @click.stop="handleGo(taskList.sign.mission_info, 'sign')"
              >
                {{ $t('去完成') }}
              </div>
            </template>
            <div class="task-btn btn had" v-else>{{ $t('已完成') }}</div>
          </div>

          <template v-if="taskList.pay">
            <div
              class="task-item"
              v-for="(item, index) in taskList.pay.mission_info"
              :key="index"
            >
              <div class="icon">
                <img :src="item.icon1" alt="" />
              </div>
              <div class="task-info">
                <div class="title">
                  {{ item.name }}
                </div>
                <div class="subtitle">
                  ({{ taskList.pay.total }}/{{ item.need_value }})
                </div>
                <div class="line">
                  <div class="gold" v-if="item.reward">
                    +{{ item.reward }}金币
                  </div>
                </div>
                <div class="extra" v-if="item.extra_exp">
                  会员加成：{{ item.extra_exp }}
                </div>
              </div>
              <template>
                <div
                  class="task-btn btn get"
                  v-if="item.is_finish == 1"
                  @click="handleGet(item, 'pay')"
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="task-btn btn"
                  v-else
                  @click.stop="handleGo(item, 'pay')"
                >
                  {{ $t('去完成') }}
                </div>
              </template>
            </div>
          </template>
        </div>
      </pull-refresh>
    </div>
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      @confirm="popupConfirm"
    ></task-popup>
  </div>
</template>
<script>
import {
  ApiMissionGetAchievement,
  ApiMissionGetAchievementReward,
} from '@/api/views/mission.js';
import {
  BOX_openInNewNavWindow,
  BOX_openInNewWindow,
  BOX_openInNewNavWindowRefresh,
  BOX_showActivity,
  BOX_showActivityByAction,
  platform,
} from '@/utils/box.uni.js';
import TaskPopup from '../comonents/task-popup';

export default {
  name: 'TaskAchievement',
  components: {
    TaskPopup,
  },
  data() {
    return {
      taskPopup: false,
      popupContent: '',
      isSvip: false,
      taskList: {},
      ptbUrl: '',
      vipUrl: '',
      isLoading: false,
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('成就任务');
    }
    await this.getAchievementData();
  },
  methods: {
    async onRefresh() {
      await this.getAchievementData();
      this.isLoading = false;
    },
    toSvip() {
      BOX_openInNewWindow({ name: 'Svip' }, { url: this.vipUrl });
    },
    popupConfirm() {
      this.taskPopup = false;
      BOX_openInNewWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    async getAchievementData() {
      const res = await ApiMissionGetAchievement();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList = list;
    },
    async handleGet(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetAchievementReward({
        mission: key,
        mission_level: item.mission_level,
      });
      this.$toast(res.msg);
      await this.getAchievementData();
    },
    handleGo(item, key) {
      switch (key) {
        case 'pay':
          this.popupContent = `${this.$t(
            '3733平台游戏内使用微信支付/支付宝支付充值满',
          )}${item.need_value}${this.$t(
            '元，或累计充值平台币、开通svip会员满',
          )}${item.need_value}元！`;
          this.taskPopup = true;
          break;
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        default:
          break;
      }
    },
    // 接口写的乱七八糟，同一个字段，一会儿数组一会儿对象的
    formatTitle(item, key) {
      let { mission_info } = item;
      let title = '';
      switch (key) {
        case 'pay':
          title = `${this.$t('累计充值')}${
            mission_info.need_value
          }元（${Math.floor(item.total)}/${mission_info.need_value}）`;
          break;
        case 'sign':
          title = `${this.$t('累计签到')}${mission_info.need_value}${this.$t(
            '次',
          )}（${item.total}/${mission_info.need_value}）`;
          break;
        default:
          break;
      }
      return title;
    },
  },
};
</script>
<style lang="less" scoped>
.task-achievement-page {
  background-color: #fff;
  font-size: 14 * @rem;
  min-height: 100vh;
  .pull-refresh {
    background: #fff;
  }
  .main {
    box-sizing: border-box;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  .top-bar {
    flex-shrink: 0;
    width: 100%;
    height: 228 * @rem;
    .image-bg('~@/assets/images/welfare/task-achievement-bg-new.png');
    background-size: 100% 228 * @rem;
  }
  .svip-info {
    width: 100%;
    height: 76 * @rem;
    margin: -21 * @rem auto 0;
    flex-shrink: 0;
  }
  .task-list {
    padding-bottom: 40 * @rem;
    .task-item {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 337 * @rem;
      height: 80 * @rem;
      background: #fafafa;
      border-radius: 8 * @rem;
      margin: 12 * @rem auto 0;
      padding: 0 12 * @rem;
      .icon {
        width: 50 * @rem;
        height: 50 * @rem;
        background-color: #ffffff;
        border-radius: 8 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 32 * @rem;
          height: 32 * @rem;
        }
      }
      .task-info {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .title {
          font-size: 14 * @rem;
          color: #333333;
          line-height: 18 * @rem;
          font-weight: 500;
          text-align: left;
          word-break: break-all;
        }

        .subtitle {
          font-size: 12 * @rem;
          color: #333333;
          line-height: 18 * @rem;
          font-weight: 500;
          text-align: left;
          word-break: break-all;
          margin-top: 2 * @rem;
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 6 * @rem;
          .gold {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-gold.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .exp {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-exp.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .coupon {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-coupon.png) left
              center no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
        }
        .extra {
          font-size: 11 * @rem;
          color: #777777;
          line-height: 14 * @rem;
          margin-top: 6 * @rem;
        }
      }
      .task-btn {
        width: 72 * @rem;
        height: 32 * @rem;
        border-radius: 16 * @rem;
        background: #7d54ff;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        &.had {
          background: #e4e4e4;
        }
        &.get {
          background: #7d54ff;
        }
      }
    }
  }
}
</style>
