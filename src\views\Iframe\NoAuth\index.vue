<template>
  <div class="iframe">
    <nav-bar-2 :title="title" :border="true" ref="topNavBar"></nav-bar-2>
    <iframe id="main" :src="url" frameborder="0"></iframe>
    <div class="kefu-btn" @click="handleKefu" v-if="showKefu"></div>
  </div>
</template>

<script>
export default {
  name: 'Iframe',
  data() {
    return {
      title: '',
      url: '',
      showKefu: 0,
    };
  },
  created() {
    this.title = this.$route.params.title;
    this.url = this.$route.params.url;
    this.showKefu = this.$route.params.showKefu ?? 0;
    this.$toast.loading({
      message: this.$t('加载中...'),
    });
  },
  mounted() {
    let iframe = document.querySelector('#main');
    iframe.onload = () => {
      this.$toast.clear();
    };
    // window.addEventListener(
    //   "message",
    //   (e) => {
    //     this.handleMessage(e);
    //   },
    //   false
    // );
    // // this.postMessage();
    // let iframe = document.getElementById("main");
    // iframe.onload = function () {
    // };
  },
  methods: {
    handleKefu() {
      this.openKefu({
        from: 'fanli',
        gameName: '',
        activityName: this.title,
      });
    },
    // handleMessage(e) {
    //   switch (e.data.type) {
    //     case "login":
    //       this.$dialog
    //         .alert({
    //           message: "您还未登录，请登录",
    //         })
    //         .then(() => {
    //           this.$router.replace({ name: "PhoneLogin" });
    //         });
    //       break;
    //     case "goToGame":
    //       this.$router.push({
    //         name: "GameDetail",
    //         params: { id: e.data.gameid },
    //       });
    //       break;
    //     case "openInNewWindow":
    //       this.$router.replace({
    //         name: "AuthIframe",
    //         params: { url: e.data.url },
    //       });
    //       break;
    //     case "showPlatformIntroduced":
    //       this.$router.push({
    //         name: "AuthIframe",
    //         params: { url: e.data.url },
    //       });
    //       break;
    //     case "bindPhone":
    //       this.$router.push({ name: "ChangePhone" });
    //       break;
    //     case "openInBrowser":
    //       window.location.href = e.data.url;
    //       break;
    //     case "close":
    //       this.$router.go(-1);
    //       break;
    //     case "memAuth":
    //       this.$router.push({ name: "IdCard" });
    //       break;
    //     case "popToRecommend":
    //       break;
    //     case "showActivity":
    //       break;
    //     case "openApp":
    //       break;
    //     case "setScreenOrientation":
    //       break;
    //     case "showVideoAd":
    //       break;
    //     case "setCanScreenShots":
    //       break;
    //     case "setHideToolBar":
    //       break;
    //     case "receiveFirstChargeSuccess":
    //       break;
    //     case "wxOAuth2":
    //       break;
    //     case "createShortcut":
    //       break;
    //     case "share":
    //       break;
    //     default:
    //       break;
    //   }
    // },
    // postMessage() {
    //   let data = {};
    //   data.from = BASEPARAMS.from;
    //   data.token = this.userInfo.token ? this.userInfo.token : "";
    //   data.schemes = "";
    //   data.packageName = "";
    //   if (this.$route.params.gameId) data.gameid = this.$route.params.gameId;
    //   let iframe = document.getElementById("main");
    //   iframe.onload = function () {
    //     iframe.contentWindow.postMessage(`(${JSON.stringify(data)})`, "*");
    //   };
    // },
  },
};
</script>

<style lang="less" scoped>
.iframe {
  height: 100vh;
  overflow: hidden;
  iframe {
    width: 100%;
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    // height: 100vh;
  }
}
.kefu-btn {
  position: fixed;
  bottom: 300 * @rem;
  right: 10 * @rem;
  width: 60 * @rem;
  height: 60 * @rem;
  background: #fff;
  z-index: 999;
  background: url('~@/assets/images/kefu-fixed.png') center center no-repeat;
  background-size: 60 * @rem 60 * @rem;
}
</style>
