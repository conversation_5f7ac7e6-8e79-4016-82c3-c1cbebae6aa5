<template>
  <div class="page">
    <nav-bar-2
      bgStyle="white"
      :placeholder="true"
      :azShow="true"
      :title="title"
    >
      <template #right>
        <div class="search-icon" @click="toPage('Search')"> </div>
        <div class="my-game-icon" @click="toPage('MyGame')"> </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="nav-container">
        <div class="sort-bar">
          <div
            class="sort-item"
            v-for="item in sortList"
            :key="item.id"
            :class="{ on: sortId == item.id }"
            @click="clickSort(item)"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="size-bar">
          <van-popover
            v-model="showPopover"
            trigger="click"
            placement="bottom-end"
            :actions="popoverActions"
            @select="onPopoverSelect"
          >
            <template #reference>
              <div class="size-show"
                >{{ !sizeId ? '全部大小' : sizeList[sizeId].title }}
                <div class="arrow" :class="{ on: showPopover }"></div
              ></div>
            </template>
          </van-popover>
        </div>
      </div>

      <van-loading class="loading-box" v-if="pageLoading">{{
        $t('加载中...')
      }}</van-loading>
      <yy-list
        v-else
        class="yy-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="game-list">
          <div class="game-item" v-for="item in gameList" :key="item.id">
            <game-item-4 :gameInfo="item" :showHot="true"></game-item-4>
                <yy-download-btn
                  :gameInfo="item"
                  @click="goToGame(item)"
                ></yy-download-btn>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import {
  ApiV2024CateGetCateList,
} from '@/api/views/game.js';
import { ApiCwbIndexGetGameListByKey } from '@/api/views/specialTopic.js'
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'filterGameList',
  data() {
    return {
      id: 0,

      title: '分类',

      pageLoading: true,

      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,

      sortList: [],
      sizeList: [],
      sortId: 2,
      sizeId: 0,

      showPopover: false,
    };
  },
  computed: {
    popoverActions() {
      return this.sizeList.map(item => {
        return {
          text: item.title,
          id: item.id,
        };
      });
    },
  },
  async created() {
    this.pageLoading = true;
    this.id = this.$route.params.id;
    this.title = this.$route.params.title;
    try {
      await this.getCateList();
      await this.getGameList();
    } finally {
      this.pageLoading = false;
    }
  },
  activated() {
    this.$sensorsPageSet(`更多游戏列表页-${this.title}`);
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCwbIndexGetGameListByKey({
        id: this.id,
        page: this.page,
        listRows: this.listRows,
        size: this.sizeId,
        sort: this.sortId,
      });
      const list = res.data.game_list
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async getCateList() {
      const res = await ApiCwbIndexGetGameListByKey();
      this.sortList = res.data.sort_list;
      this.sizeList = res.data.size_list;
      this.sortId = this.sortList[0].id;
      this.sizeId = this.sizeList[0].id;
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },

    async onPopoverSelect(e) {
      this.sizeId = e.id;
      this.pageLoading = true;
      await this.getGameList();
      this.pageLoading = false;
    },
    async clickSort(item) {
      this.sortId = item.id;
      this.pageLoading = true;
      await this.getGameList();
      this.pageLoading = false;
    },
    goToGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  .search-icon {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url('~@/assets/images/games/search-icon.png') center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
  }
  .my-game-icon {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 10 * @rem;
    background-image: url(~@/assets/images/my-game.png);
    background-size: 28 * @rem;
    background-repeat: no-repeat;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .main {
    flex: 1;
    .nav-container {
      box-sizing: border-box;
      width: 100%;
      position: fixed;
      z-index: 10000;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 18 * @rem;
      height: 40 * @rem;
      background: #ffffff;
      .fixed-center;
      .sort-bar {
        display: flex;
        align-items: center;
        background: #f6f6f6;
        height: 26 * @rem;
        padding: 0 2 * @rem;
        border-radius: 5 * @rem;
        .sort-item {
          height: 22 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 8 * @rem;
          font-size: 12 * @rem;
          color: #979797;
          &.on {
            font-weight: bold;
            color: #333333;
            background: #ffffff;
            border-radius: 4 * @rem;
          }
        }
      }
      .size-bar {
        position: relative;
        .size-show {
          font-size: 12 * @rem;
          color: #333333;
          padding: 0 8 * @rem;
          background: #f6f6f6;
          height: 26 * @rem;
          border-radius: 5 * @rem;
          display: flex;
          align-items: center;
          font-weight: bold;
          .arrow {
            width: 8 * @rem;
            height: 26 * @rem;
            margin-left: 5 * @rem;
            background: url('~@/assets/images/arrow-to-top.png') center center
              no-repeat;
            background-size: 8 * @rem 5 * @rem;
            transform: rotateX(180deg);
            transition: 0.2s;
            &.on {
              transform: rotateX(0deg);
            }
          }
        }
      }
    }
    .yy-list {
      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 40 * @rem;
        background: #ffffff;
      }
      .game-list {
        padding: 0 18 * @rem;
        .game-item{
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
