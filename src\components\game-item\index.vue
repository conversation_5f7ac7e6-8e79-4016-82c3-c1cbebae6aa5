<template>
  <div
    :class="{ type4: type === 4 }"
    class="game-item-components"
    @click="toDetail(gameInfo)"
  >
    <div class="game-icon">
      <div v-if="subscript" class="subscript">{{ subscript }}</div>
      <img :src="gameInfo.titlepic" alt="" />
    </div>
    <div class="game-info">
      <div class="game-name">{{ gameInfo.title }}</div>
      <div class="game-center" v-if="type === 1">
        <div class="score">
          {{ gameInfo.rating ? gameInfo.rating.rating : '10' }}{{ $t('分') }}
        </div>
        <div class="types">
          <template v-for="(type, typeIndex) in gameInfo.type">
            <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
              type
            }}</span>
          </template>
        </div>
      </div>
      <div class="game-center" v-if="type === 2">
        <div class="date">
          {{ $handleTimestamp(kaifuDate).date }}
          ({{ $getDateDiff(kaifuDate) }})
        </div>
      </div>
      <div class="game-center grq" v-if="type === 3">
        {{ gameInfo.yxftitle }}
      </div>
      <div class="tags">
        <template v-if="isIos">
          <div
            class="tag-type2 tag"
            v-for="(tag, tagIndex) in gameInfo.extra_tag"
            :key="tagIndex"
            :style="{ borderColor: tag.color, color: tag.color }"
          >
            <template v-if="tagIndex < 3">
              <div class="tag-name">{{ tag.title }}</div>
            </template>
          </div>
        </template>
        <template v-else>
          <div
            class="tag"
            v-for="(tag, tagIndex) in gameInfo.app_tag"
            :key="tagIndex"
            :style="{ color: tag.color }"
          >
            <template v-if="tagIndex < 3">
              <img class="tag-icon" :src="tag.icon" alt="" />
              <div class="tag-name">{{ tag.name }}</div>
            </template>
          </div>
        </template>
      </div>
    </div>
    <div class="game-right" v-if="showRight">
      <template v-if="type !== 3">
        <!-- <div :class="{h5: gameInfo.h5_url, cloud: showCloud}" class="start" @click.stop="handleDownload()">
          {{ buttonText }}
        </div> -->
        <div class="kaifu-tip" v-if="isHadKaifu">{{ $t('已开服') }}</div>
      </template>
      <template v-if="type === 3">
        <div class="grq-right">
          <div v-if="gameInfo.grq_status == 0" class="button button1">
            {{ $t('排队中') }}
          </div>
          <div v-if="gameInfo.grq_status == 1" class="button button1">
            {{ $t('正在签名') }}
          </div>
          <div
            v-if="gameInfo.grq_status == 2"
            @click.stop="downloadGrq(gameInfo.grq_down_ip)"
            :class="{ loading: loading }"
            class="button button2"
          >
            {{ $t('安装') }}
          </div>
          <div
            v-if="gameInfo.grq_status == 2 || gameInfo.grq_status == 3"
            @click.stop="RestGrqGame(gameInfo.id, gameInfo.grq_status)"
            class="button button3"
          >
            {{ $t('重新获取') }}
          </div>
          <div v-if="gameInfo.grq_status == 4" class="button button1">
            {{ $t('暂停签名') }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { isIos } from '@/utils/userAgent';
import { ApiRestGrqGame } from '@/api/views/game';
import {
  startCloudGame,
  startH5Game,
  navigateToGameDetail,
} from '@/utils/function';
import { globalOpenDownload } from '@/utils/box';

/**
 * @param type 1普通，2开服，3个人签, 4个人中心
 *  */

export default {
  name: 'GameItem',
  props: {
    gameInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    showRight: {
      type: Boolean,
      default: true,
    },
    isHadKaifu: {
      type: Boolean,
      default: false,
    },
    kaifuDate: {
      type: Number,
      default: 0,
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loading: false,
      isIos,
    };
  },
  computed: {
    // 是否显示云玩
    showCloud() {
      return this.gameInfo.yyx_type
        ? parseInt(this.gameInfo.yyx_type.code) !== 0 && isIos
        : false;
    },
    // 按钮文案
    buttonText() {
      if (this.gameInfo.h5_url) {
        return this.$t('开始玩');
      } else if (this.showCloud) {
        return this.$t('云玩');
      } else {
        return this.$t('下载');
      }
    },
    // 按钮url
    downloadUrl() {
      if (this.gameInfo.h5_url) {
        return this.gameInfo.h5_url;
      } else if (isIos && this.gameInfo.down_ip) {
        return this.gameInfo.down_ip;
      } else if (!isIos && this.gameInfo.down_a) {
        return this.gameInfo.down_a;
      } else {
        return false;
      }
    },
    // 游戏角标
    subscript() {
      if (
        this.gameInfo.classid == 107 &&
        this.gameInfo.f_pay_rebate &&
        this.gameInfo.f_pay_rebate != 0 &&
        this.gameInfo.f_pay_rebate != 100
      ) {
        return `${parseFloat(this.gameInfo.f_pay_rebate) / 10}${this.$t('折')}`;
      } else if (
        this.gameInfo.classid == 107 &&
        this.gameInfo.pay_rebate &&
        this.gameInfo.pay_rebate != 100
      ) {
        if (parseFloat(this.gameInfo.pay_rebate) == 100) {
          return `10${this.$t('折')}`;
        } else {
          return `${parseFloat(this.gameInfo.pay_rebate) / 10}${this.$t('折')}`;
        }
      } else if (this.gameInfo.first_pay_icon) {
        return this.$t('无门槛');
      } else if (this.gameInfo.has_coupon) {
        return this.$t('代金券');
      } else {
        return false;
      }
    },
  },
  methods: {
    // 下载逻辑
    handleDownload() {
      if (this.gameInfo.h5_url) {
        startH5Game(this.gameInfo.h5_url);
      } else if (this.showCloud) {
        startCloudGame(this.gameInfo.id);
      } else {
        isIos
          ? this.toDetail(this.gameInfo)
          : (window.location.href = this.downloadUrl);
      }
    },
    // 打开详情页
    toDetail(gameInfo) {
      navigateToGameDetail(gameInfo);
    },
    // 已经签完
    downloadGrq(url) {
      this.loading = true;
      globalOpenDownload(url);
      setTimeout(() => {
        this.loading = false;
      }, 2000);
    },
    // 重签
    async RestGrqGame(id, status) {
      const res = await ApiRestGrqGame({ game_id: id, status: status });
      this.$toast(res.msg);
      this.$emit('refreshList');
    },
  },
};
</script>

<style lang="less" scoped>
.game-item-components {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10 * @rem 0;
  &.type4 {
    .game-icon {
      flex: 0 0 40 * @rem;
      width: 40 * @rem;
      height: 40 * @rem;
    }
    .game-info {
      .game-name {
        font-weight: 500;
      }
    }
    .tags {
      display: none;
    }
  }
  .game-icon {
    position: relative;
    flex: 0 0 65 * @rem;
    width: 65 * @rem;
    height: 65 * @rem;
    border-radius: 12 * @rem;
    background-color: #eeeeee;
    .subscript {
      position: absolute;
      top: -9 * @rem;
      right: -13 * @rem;
      padding: 3 * @rem 5 * @rem;
      background: rgba(255, 117, 84, 1);
      font-size: 12 * @rem;
      color: #fff;
      border-radius: 10 * @rem 10 * @rem 10 * @rem 0;
      transform: scale(0.75);
    }
    img {
      border-radius: 12 * @rem;
      background-color: #eeeeee;
      overflow: hidden;
    }
  }
  .game-info {
    margin-left: 11 * @rem;
    flex: 1;
    min-width: 0;
    height: 65 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .game-name {
      margin-top: 2 * @rem;
      font-size: 15 * @rem;
      font-weight: bold;
      color: #000000;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .game-center {
    flex: 1;
    min-width: 0;
    font-size: 11 * @rem;
    color: #999999;
    display: flex;
    align-items: center;
    margin: 5 * @rem 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .types {
      display: flex;
      margin-left: 5 * @rem;
      .type {
        padding: 0 5 * @rem;
        position: relative;
        &:not(:first-child) {
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1 * @rem;
            height: 10 * @rem;
            background-color: #999999;
          }
        }
      }
    }
    &.grq {
      display: block;
    }
  }
  .tags {
    display: flex;
    height: 18 * @rem;
    overflow: hidden;
    flex-wrap: wrap;
    .tag {
      margin-right: 9 * @rem;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      color: #999;
      .tag-icon {
        width: 13 * @rem;
        height: 13 * @rem;
      }
      .tag-name {
        font-size: 11 * @rem;
        white-space: nowrap;
        margin-left: 2 * @rem;
      }
    }
    .tag-type2 {
      height: 18 * @rem;
      box-sizing: border-box;
      padding: 1 * @rem 5 * @rem;
      border-radius: 3 * @rem;
      border: 1px solid #000;
      color: #fff;
      .tag-name {
        font-size: 9 * @rem;
      }
    }
  }
  .game-right {
    .start {
      width: 70 * @rem;
      height: 30 * @rem;
      font-size: 14 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
      color: @themeColor;
      border-radius: 15 * @rem;
      border: 1px solid @themeColor;
      &.h5 {
        background: @themeBg;
        color: #fff;
        border: none;
      }
      &.cloud {
        background: #f3ac40;
        color: #fff;
        border: 1px solid #f3ac40;
      }
    }
    .kaifu-tip {
      margin-top: 10 * @rem;
      font-size: 12 * @rem;
      color: #999999;
      text-align: center;
    }
    .grq-right {
      margin-left: 15 * @rem;
    }
    .button {
      width: 65 * @rem;
      height: 27 * @rem;
      box-sizing: border-box;
      border: 1px solid @themeColor;
      font-size: 13 * @rem;
      color: @themeColor;
      text-align: center;
      line-height: 27 * @rem;
      border-radius: 14 * @rem;
      background: #fff;
      &.button2 {
        margin-bottom: 5 * @rem;
        background: @themeBg;
        color: #fff;
      }
      &.button3 {
        border: 1px solid #ff7d0a;
        color: #ff7d0a;
      }
      &.loading {
        position: relative;
        font-size: 0;
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          animation: rotate 1s infinite linear;
        }
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
