<template>
  <div class="game-recharge-order">
    <nav-bar-2 :title="'消费记录'" :border="true" :azShow="true"> </nav-bar-2>
    <div class="tab-list">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        :class="{ active: active === index }"
        @click="changeList(index)"
        class="item"
      >
        {{ item.title }}
      </div>
    </div>
    <main>
      <template v-for="(item, index) in tabList">
        <order-list
          :key="index"
          v-if="active === index"
          :status="item.status"
        ></order-list>
      </template>
    </main>
  </div>
</template>

<script>
import OrderList from './OrderList';
export default {
  name: 'RechargeRecord',
  components: {
    OrderList,
  },
  data() {
    return {
      active: 0,
      tabList: [
        {
          status: 2,
          title: '支付成功',
        },
        {
          status: 1,
          title: '未支付',
        },
      ],
    };
  },
  methods: {
    changeList(index) {
      this.active = index;
    },
  },
};
</script>

<style lang="less" scoped>
.game-recharge-order {
  background: #f2f2f2;
  min-height: 100vh;
  .tab-list {
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    width: 100%;
    z-index: 100;
    height: 44 * @rem;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #f2f2f2;
    background: #f2f2f2;
    .item {
      flex: 0 0 50%;
      font-size: 16 * @rem;
      font-family:
        PingFangSC-Medium,
        PingFang SC;
      font-weight: 500;
      line-height: 44 * @rem;
      color: #797979;
      text-align: center;
      &.active {
        position: relative;
        color: #000;
        font-weight: 600;
        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translate(-50%, 0);
          display: block;
          width: 19 * @rem;
          height: 4 * @rem;
          background: @themeColor;
          border-radius: 10 * @rem;
        }
      }
    }
  }
}
</style>
