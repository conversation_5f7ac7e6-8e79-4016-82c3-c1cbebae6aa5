<template>
  <div
    class="top-bar-component"
    :class="{ scrolled: !scrolled }"
    v-if="platform != 'android' && platform != 'ios'"
  >
    <div class="bg" :style="{ background: homeNavBg }"></div>
    <div class="fixed">
      <status-bar bgColor="transparent"></status-bar>
      <SearchContainer :white="!scrolled" />
      <div class="nav-container">
        <div class="nav-list">
          <div
            class="nav-item"
            :class="{
              active: current == index,
              notBg: item.text_icon || (item.id == 5 && item.act) || item.icon,
            }"
            v-for="(item, index) in newNavList"
            :key="index"
            @click="handleClick(item, index)"
          >
            <img
              v-if="item.id == 5 && item.act"
              class="nav-item-font-img"
              :src="item.act.font_img"
              alt=""
              onclick="return false"
            />
            <img
              v-else-if="item.icon"
              class="nav-item-font-img"
              :src="item.icon"
              alt=""
              onclick="return false"
            />
            <template v-else>
              <img
                class="nav-item-font-img active-img"
                :src="item.text_icon"
                alt=""
                v-if="item.text_icon && current == index"
              />
              <span v-else>{{ item.name }}</span>
            </template>

            <div class="beta_icon" v-if="item.id == 9 && newNavList.length">
              <img src="~@/assets/images/cloud-game/beta_icon.png" alt="" />
            </div>
          </div>
          <!-- <div
            class="line"
            :style="{
              left: `${current * 70 * remNumberLess}rem`,
              width: `${12 * remNumberLess}rem`,
            }"
          ></div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import StatusBar from '@/components/status-bar';
import { mapGetters, mapMutations } from 'vuex';
import SearchContainer from '@/components/search-container';
import { platform } from '@/utils/box.uni.js';

let blackWhiteList = [
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
  '/home/<USER>',
]; // 黑字白底名单
export default {
  name: 'Topbar',
  components: {
    StatusBar,
    SearchContainer,
  },
  data() {
    return {
      remNumberLess, //安全区大小
      bgc: '#ffffff', //当前背景
      lineBg: 'rgba(254, 102, 0, 1)', //线颜色
      current: 0,
      scrolled: false,
      platform,
    };
  },
  computed: {
    ...mapGetters({
      homeNav: 'system/homeNav',
      homeNavBg: 'system/homeNavBg',
      configs: 'system/configs',
      isUpApp: 'system/isUpApp',
    }),
    newNavList() {
      return [...this.homeNav];
    },
  },
  watch: {
    $route(route) {
      this.refreshCurrent();
    },
  },
  mounted() {
    this.refreshCurrent();
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    ...mapMutations({
      setHomeNavBg: 'system/setHomeNavBg',
    }),
    handleScroll() {
      // 白名单的不需要监听滚动
      if (
        blackWhiteList.includes(this.$route.path) ||
        blackWhiteList.find(item => this.$route.path.includes(item))
      ) {
        return false;
      }
      // 全屏的不用监听滚动
      if (
        this.newNavList[this.current]?.is_full_screen !== undefined &&
        !this.newNavList[this.current]?.is_full_screen
      ) {
        return false;
      }

      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 10) {
        this.setHomeNavBg(`rgba(255,255,255,1)`);
        this.scrolled = true;
      } else {
        this.setHomeNavBg(`rgba(255,255,255,0)`);
        this.scrolled = false;
      }
    },
    refreshCurrent() {
      this.current = this.newNavList.findIndex(item => {
        if (item.url) {
          return item.url == this.$route.path;
        }
        if (item.id == 5) {
          let act = item.act;
          if ([3, 5].includes(act.act_type)) {
            if (act.jump_url.indexOf('/exclusive_activity/') > -1) {
              return (
                act.jump_url.split('exclusive_activity/')[1] ==
                decodeURIComponent(this.$route.path).split(
                  'exclusive_activity/',
                )[1]
              );
            } else {
              return (
                decodeURIComponent(this.$route.path).includes(act.jump_url) ||
                act.jump_url.includes(decodeURIComponent(this.$route.path))
              );
            }
          } else {
            return this.$route.params.id == act.id;
          }
        }
      });
      if (
        blackWhiteList.includes(this.$route.path) ||
        blackWhiteList.find(item => this.$route.path.includes(item))
      ) {
        this.setHomeNavBg(`rgba(255,255,255,1)`);
        this.scrolled = true;
      } else if (
        this.newNavList[this.current]?.bg_color &&
        !this.newNavList[this.current]?.is_full_screen
      ) {
        this.setHomeNavBg(this.newNavList[this.current]?.bg_color);
        this.scrolled = false;
      } else {
        this.setHomeNavBg(`rgba(255,255,255,0)`);
        this.scrolled = false;
      }
    },
    handleClick(item, index) {
      this.CLICK_EVENT(item.click_id);
      switch (item.id) {
        case 5: // 落地页
          let act = item.act;
          switch (act.act_type) {
            case 1: // 模板1
              this.toPage('GameActivity', { id: act.id });
              break;
            case 2: // 模板2
              this.toPage('GameActivity2', { id: act.id });
              break;
            case 3: // url
            case 5: // 模板4
              if (act.jump_url.indexOf('activity.3733.com') > -1) {
                this.toPage('Activity', { url: act.jump_url });
              } else if (act.jump_url.indexOf('/exclusive_activity/') > -1) {
                this.toPage('ExclusiveActivityHome', {
                  id: act.jump_url.split('exclusive_activity/')[1],
                });
              } else {
                // 除活动以外先暂时url跳转 看具体需求再修改
                window.location.href = act.jump_url;
              }
              break;
            case 4: // 模板3
              this.toPage('GameDownActivity', { id: act.id });
              break;
          }
          break;
        case 0:
        case 1:
        case 2:
        case 3:
        case 9:
        default:
          this.$router.push(item.url);
          break;
      }
      this.$nextTick(() => {
        setTimeout(() => {
          if (document.querySelector('.nav-container .nav-list')) {
            document
              .querySelector('.nav-container .nav-list .active')
              ?.scrollIntoView();
          }
        }, 0);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.top-bar-component {
  .bg {
    position: fixed;
    left: 0;
    .fixed-center;
    top: 0;
    width: 100%;
    height: calc(85 * @rem + @safeAreaTop);
    height: calc(85 * @rem + @safeAreaTopEnv);
    z-index: 1999;
  }
  .fixed {
    box-sizing: border-box;
    height: calc(85 * @rem + @safeAreaTop);
    height: calc(85 * @rem + @safeAreaTopEnv);
    position: fixed;
    left: 0;
    .fixed-center;
    top: 0;
    width: 100%;
    z-index: 2000;
  }
  .nav-container {
    display: flex;
    align-items: center;
    height: 35 * @rem;
    padding: 0 10 * @rem;
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .nav-list {
    display: flex;
    align-items: center;
    position: relative;
    .nav-item {
      padding: 0 10 * @rem;
      color: #000000;
      text-align: center;
      font-size: 16 * @rem;
      flex-grow: 0;
      flex-shrink: 0;
      min-width: 0;
      display: flex;
      align-items: center;
      position: relative;
      height: 25 * @rem;
      .nav-item-font-img {
        display: block;
        width: auto;
        height: 24 * @rem;
        object-fit: contain;
      }
      .beta_icon {
        position: absolute;
        top: -4 * @rem;
        right: -6 * @rem;
        width: 36 * @rem;
        height: 16 * @rem;
      }
      span {
        font-weight: 500;
        text-shadow: none;
      }
      &.active {
        font-size: 18 * @rem;
        background: url(~@/assets/images/home-nav-active-bg.png) no-repeat
          center;
        background-size: 90% 21 * @rem;

        &.notBg {
          background: none;
        }

        span {
          font-weight: bold;
          position: relative;

          &::before {
            content: '';
            display: block;
            width: 10px;
            height: 10px;
            background: url(~@/assets/images/home-nav-active-dot.png) no-repeat;
            background-size: 10 * @rem 10 * @rem;
            position: absolute;
            top: -2 * @rem;
            right: -10 * @rem;
          }

          // &::after {
          //   content: '';
          //   width: 12 * @rem;
          //   height: 5 * @rem;
          //   border-radius: 16 * @rem;
          //   background-color: @themeColor;
          //   position: absolute;
          //   bottom: -10 * @rem;
          //   left: 50%;
          //   transform: translateX(-50%);
          // }
        }
        .nav-item-font-img {
          height: 28 * @rem;

          &.active-img {
            height: 32 * @rem;
          }
        }
      }
    }
    .line {
      position: absolute;
      left: 0;
      bottom: -5 * @rem;
      width: 18 * @rem;
      height: 3 * @rem;
      border-radius: 2 * @rem;
      transform: translateX(29 * @rem);
      background: @themeColor;
      // transition: 0.3s;
    }
  }
  &.scrolled {
    .nav-list {
      .nav-item {
        color: #fff;
        position: relative;
        span {
          text-shadow: 0 * @rem 1 * @rem 1 * @rem rgba(0, 0, 0, 0.3);
        }
        &.active span::after {
          background-color: #fff;
        }
      }
      .line {
        background: #fff;
      }
    }
  }
}
</style>
