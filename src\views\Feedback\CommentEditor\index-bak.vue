<template>
  <div class="comment-editor">
    <nav-bar-2 :title="$t('发表评论')" :border="true">
      <template #right>
        <div class="send btn" :class="{ on: canSend }" @click="handleSend">
          {{ $t('发表') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="editor-container">
      <div class="input-container">
        <textarea
          id="inputText"
          class="input-text"
          v-model.trim="inputText"
          rows="10"
          :placeholder="placeholder"
        ></textarea>
      </div>
      <div class="img-container">
        <van-uploader
          v-model="imageFileList"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="9"
          :preview-size="100"
          accept="image/*"
          :before-read="beforeRead"
          :multiple="true"
          class="uploader"
        >
          <div class="upload-btn">
            <img src="@/assets/images/deal/upload-icon.png" />
          </div>
        </van-uploader>
      </div>
    </div>
    <div v-if="class_id != 101" class="score-container">
      <div class="score-title">{{ $t('评分') }}：</div>
      <van-rate
        v-model="score"
        color="#FE6600"
        void-icon="star"
        void-color="#E0E0E0"
        :size="14"
      />
    </div>
    <div
      class="quick-comment"
      v-if="quick_comment_list && quick_comment_list.length && class_id != 101"
    >
      <div class="title">快速点评<span>（请点击下面条目）</span></div>
      <div class="list">
        <div
          v-for="(item, index) in quick_comment_list"
          :key="index"
          @click="handleQuickComment(index, item.text)"
          :class="{ current: comment_current === index }"
          class="item"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiCommentSubmit, ApiCommentQuick } from '@/api/views/comment.js';
import md5 from 'js-md5';
export default {
  name: 'CommentEditor',
  data() {
    return {
      score: 5,
      inputText: '', // 评论内容
      imageFileList: [],
      images: [],
      quick_comment_list: [], //快速点评列表
      isSending: false,
      comment_current: -1, //当前选中快速评论
      class_id: 0,
      source_id: 0,
    };
  },
  computed: {
    placeholder() {
      return this.class_id == 101
        ? '说点什么...'
        : '写出你对游戏玩法、操作、攻略等方面的可观评价吧，优质评论将给予金币奖励与前排展示~';
    },
    canSend() {
      if (this.inputText.length < 15) return false;
      if (this.score < 1) return false;
      return true;
    },
    formatContent() {
      return this.inputText.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  watch: {
    inputText() {
      if (
        this.comment_current !== -1 &&
        this.inputText !== this.quick_comment_list[this.comment_current].text
      ) {
        this.comment_current = -1;
      }
    },
  },
  async created() {
    this.class_id = this.$route.params.class_id;
    this.source_id = this.$route.params.source_id;
    const res = await ApiCommentQuick();
    this.quick_comment_list = res.data.list;
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelector('#inputText').focus();
    });
  },
  methods: {
    async handleSend() {
      if (this.isSending) {
        return false;
      }
      if (this.inputText.length < 15) {
        this.$toast(this.$t('评论的字数不得小于15个字'));
        return false;
      }
      if (this.score < 1) {
        this.$toast(this.$t('请选择你的评分'));
        return false;
      }
      this.isSending = true;
      let params = {
        sourceId: this.source_id,
        classId: this.class_id,
        model: 'iPhone X',
        content: this.formatContent,
        rating: this.score,
      };
      if (this.images.length) {
        params.images = JSON.stringify(this.images);
      }
      this.$toast.loading({
        message: this.$t('正在发布评论...'),
      });
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
        setTimeout(() => {
          this.$router.go(-1);
        }, 500);
      } finally {
        this.isSending = false;
      }
    },
    handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      ApiUploadImage(data).then(
        res => {
          this.images.push(res.data.url);
          file.status = 'done';
          file.message = this.$t('上传成功');
        },
        err => {
          file.status = 'failed';
          file.message = this.$t('上传失败');
        },
      );
    },
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
    // 点击快速评论
    handleQuickComment(index, text) {
      this.comment_current = index;
      this.inputText = text;
    },
  },
};
</script>

<style lang="less" scoped>
.comment-editor {
  .send {
    width: 52 * @rem;
    height: 26 * @rem;
    border-radius: 13 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    color: #ffffff;
    font-weight: 600;
    background-color: #c1c1c1;
    &.on {
      background-color: @themeColor;
    }
  }
  .editor-container {
    border-bottom: 0.5 * @rem solid #e8e8e8;
    padding-bottom: 20 * @rem;
    .input-container {
      box-sizing: border-box;
      width: 100%;
      height: 162 * @rem;
      padding-bottom: 20 * @rem;
      .input-text {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 22 * @rem 18 * @rem;
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
        line-height: 20 * @rem;
        border: 0;
        outline: none;
      }
    }
    .img-container {
      box-sizing: border-box;
      width: 100%;
      min-height: 100 * @rem;
      padding: 0 18 * @rem;
      .uploader {
        /deep/ .van-uploader__preview-image {
          border-radius: 4 * @rem;
        }
      }
      .upload-btn {
        width: 100 * @rem;
        height: 100 * @rem;
      }
    }
  }
  .score-container {
    display: flex;
    align-items: center;
    padding: 14 * @rem 18 * @rem;
    border-bottom: 0.5 * @rem solid #e8e8e8;
    .score-title {
      font-size: 15 * @rem;
      color: #000000;
      font-weight: 400;
    }
  }
  .quick-comment {
    border-top: 10 * @rem solid #f5f5f6;
    padding: 18 * @rem;
    .title {
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      span {
        font-size: 14 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .list {
      margin-top: 22 * @rem;
      .item {
        display: table;
        margin-bottom: 10 * @rem;
        padding: 8 * @rem 15 * @rem;
        background: #fff6e9;
        border-radius: 20 * @rem;
        color: @themeColor;
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        box-sizing: border-box;
        border: 1px solid #fff6e9;
        &.current {
          border: 1px solid @themeColor;
          color: @themeColor;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
