import Vue from 'vue';
import activityDialog from './index.vue';
import router from '@/router';
import store from '@/store';

const ActivityDialog = Vue.extend(activityDialog);

// const options = {
//   // 配置项
//   title: '',
//   content: '',
//   desc: '',
//   tips: '',
//   cancelText: '',
//   confirmText: '',
//   showCancel: false,
//   showConfirm: true,
//   onCancel: () => {},
//   onConfirm: () => {},
// }

function useActivityDialog(options) {
  return new Promise((resolve, reject) => {
    const dialog = new ActivityDialog({
      router,
      store,
    });

    dialog.$mount(document.createElement('div'));
    document.body.appendChild(dialog.$el);

    dialog.$el.addEventListener(
      'animationend',
      () => {
        if (dialog.show == false) {
          dialog.$destroy();
          dialog.$el.parentNode.removeChild(dialog.$el);
        }
      },
      false,
    );

    dialog.title = options.title || '提示';
    dialog.content = options.content || '';
    dialog.desc = options.desc || '';
    dialog.cardpass = options.cardpass || '';
    dialog.tips = options.tips || '';
    dialog.cancelText = options.cancelText || '取消';
    dialog.confirmText = options.confirmText || '确定';
    dialog.showCancel = options.showCancel || false;
    dialog.showConfirm = options.showConfirm || true;
    if (options.onCancel) {
      dialog.onCancel = () => {
        options.onCancel();
        dialog.show = false;
      };
    }
    if (options.onConfirm) {
      dialog.onConfirm = () => {
        options.onConfirm();
        dialog.show = false;
      };
    }
  });
}

export default useActivityDialog;
