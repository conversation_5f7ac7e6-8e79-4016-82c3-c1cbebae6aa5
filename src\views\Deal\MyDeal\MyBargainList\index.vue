<template>
  <div class="my-bargain-list-page">
    <van-tabs
      v-model="active"
      swipeable
      animated
      title-active-color="#000000"
      title-inactive-color="#797979"
      :border="true"
      color="#21b98a"
      line-height="4px"
      class="tab-bar"
    >
      <van-tab
        v-for="(item, index) in tabList"
        :key="index"
        :title="item.title"
      >
        <bargain-list :type="item.type"></bargain-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import bargainList from '../../components/bargain-list';
import { TYPE_TRADE_BARGAIN, TYPE_TRADE_APPOINT } from '@/utils/noticeCode.js';
export default {
  name: 'MyBuyList',
  components: {
    bargainList,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          title: this.$t('我的出售'),
          type: 1,
        },
        {
          title: this.$t('我的出价'),
          type: 2,
        },
      ],
    };
  },
  created() {
    switch (Number(this.$route.params.type)) {
      case TYPE_TRADE_BARGAIN:
        this.active = 0;
        break;
      case TYPE_TRADE_APPOINT:
        this.active = 1;
        break;
      default:
        this.active = 0;
        break;
    }
  },
};
</script>

<style lang="less" scoped>
.my-bargain-list-page {
  border-radius: 20 * @rem 20 * @rem 0 0;
  overflow: hidden;
  height: calc(100vh - 100 * @rem - @safeAreaTop);
  height: calc(100vh - 100 * @rem - @safeAreaTopEnv);
  background-color: #fff;
  /deep/ .van-tabs--line .van-tabs__wrap {
    height: 50 * @rem;
  }
  .tab-bar {
    height: 50 * @rem;
    /deep/ .van-tab {
      font-size: 15 * @rem;
    }
    /deep/ .van-tabs__line {
      width: 14 * @rem;
      background-color: @themeColor;
      bottom: 8 * @rem;
      height: 4 * @rem;
    }
    /deep/ .van-tabs__nav--line {
      padding-bottom: 0 * @rem;
      height: 50 * @rem;
    }
  }
}
</style>
