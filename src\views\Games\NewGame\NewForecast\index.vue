<template>
  <div class="new-forecast-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <div class="game-container">
        <template v-for="(game, index) in gameList">
          <div class="game-section" v-if="game.length" :key="index">
            <div class="section-title">
              <div class="title-icon"></div>
              <div class="time-bar">{{ formatDate(game[0].start_time) }}</div>
            </div>
            <div class="game-list">
              <game-item-new
                v-for="(item, index) in game"
                :key="item.id"
                :info="item"
                :index="index"
                :type="2"
              ></game-item-new>
            </div>
          </div>
        </template>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiGameSubscribeGames } from '@/api/views/game.js';
export default {
  name: 'NewForecast',
  data() {
    return {
      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
    };
  },
  activated() {
    this.getGameList();
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameSubscribeGames({
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      let formatArr = this.classify(list, 'start_time');
      if (formatArr.length) {
        // 这个一定要判断，可能是一个空数组[]
        this.gameList.push(...formatArr);
      }

      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },
    // 游戏按key值分类 (因为时间戳都不一样，先转成0点时的时间戳)
    classify(arr, key) {
      let obj = {};
      arr.forEach(e => {
        if (typeof obj[this.getDayZeroTimestamp(e[key])] == 'undefined') {
          obj[this.getDayZeroTimestamp(e[key])] = [];
        }
        if (
          this.gameList.length &&
          this.getDayZeroTimestamp(
            this.gameList[this.gameList.length - 1][0][key],
          ) == this.getDayZeroTimestamp(e[key])
        ) {
          // 如果该项的时间和gameList最后一项的时间一样，则push到gameList的最后一项
          this.gameList[this.gameList.length - 1].push(e);
          delete obj[this.getDayZeroTimestamp(e[key])];
        } else {
          obj[this.getDayZeroTimestamp(e[key])].push(e);
        }
      });
      return Object.values(obj);
    },
    // 获取当天0点的时间戳
    getDayZeroTimestamp(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return new Date(new Date(timestamp).toLocaleDateString()).getTime();
    },
    // 数字0-6转星期x
    formatWeekDay(num) {
      let dayStr;
      switch (num) {
        case 1:
          dayStr = this.$t('星期一');
          break;
        case 2:
          dayStr = this.$t('星期二');
          break;
        case 3:
          dayStr = this.$t('星期三');
          break;
        case 4:
          dayStr = this.$t('星期四');
          break;
        case 5:
          dayStr = this.$t('星期五');
          break;
        case 6:
          dayStr = this.$t('星期六');
          break;
        case 0:
          dayStr = this.$t('星期日');
          break;
        default:
          break;
      }
      return dayStr;
    },
    // 格式化日期显示 xx月xx日 星期x（模板显示用）
    formatDate(timestamp) {
      if (Number(timestamp) <= 0) {
        return this.$t('即将发布');
      }
      let date = new Date(this.getDayZeroTimestamp(timestamp));
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let week = this.formatWeekDay(date.getDay());
      // return `${month}月${day}日 ${week}`;
      return `${month}${this.$t('月')}${day}${this.$t('日')}`;
    },
  },
};
</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  display: none;
}
.new-forecast-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }
  .game-container {
    background-color: #fff;
    .game-section {
      box-sizing: border-box;
      background-color: #fff;
      width: 100%;
      margin: 0 auto;
      padding: 15 * @rem 18 * @rem 6 * @rem;
      .section-title {
        display: flex;
        align-items: center;
        .title-icon {
          width: 4 * @rem;
          height: 12 * @rem;
          background-color: @themeColor;
          border-radius: 12 * @rem;
        }
        .time-bar {
          height: 30 * @rem;
          display: flex;
          align-items: center;
          background-color: #fff;
          font-size: 18 * @rem;
          font-weight: 600;
          color: #000000;
          margin-left: 10 * @rem;
        }
      }
    }
    .game-list {
    }
  }
}
</style>
