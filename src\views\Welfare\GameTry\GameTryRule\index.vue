<template>
  <div>
    <nav-bar-2 :border="true" title="试玩规则"></nav-bar-2>
    <div class="main">
      <div class="rule-list">
        <div class="rule-item" v-for="(item, index) in ruleList" :key="index">
          <div class="title">{{ item.title }}</div>
          <div class="content" v-html="item.content"></div>
          <div class="tip" v-if="item.tip">{{ item.tip }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { platform } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      platform,
      ruleList: [
        {
          title: `如何试玩赚礼金？`,
          content: `在试玩页面，领取游戏任务；<br>数量有限，先到先得！下载安装游戏，在截至日期之前，达到任务要求即可提交任务获得奖励。`,
          tip: `注：若提交失败，请退出并重新登录一次游戏再前往试玩页面提交任务。`,
        },
        {
          title: `完成任务却无法领取奖励？`,
          content: `请确认未创建多个小号；<br>
                  退出并重新登录游戏后再次提交；<br>
                  偶尔存在延迟，请稍后片刻后再提交；<br>
                  请在有效期内领取，任务结束则无法领取；<br>
                  其他原因请前往“我的”联系客服`,
        },
        {
          title: `试玩有限制吗？`,
          content: `试玩当日可领取多个游戏任务。活动期间，单一游戏任务限领一次。`,
        },
        {
          title: `有什么重要提示？`,
          content: `禁止使用任何手段刷取礼金，一经发现封号处理！`,
        },
      ],
    };
  },
  created() {
    if (this.platform == 'android') {
      document.title = '试玩规则';
    }
  },
};
</script>

<style lang="less" scoped>
.main {
  .rule-list {
    padding: 0 20 * @rem;
    .rule-item {
      margin-top: 20 * @rem;
      .title {
        padding-left: 23 * @rem;
        font-size: 16 * @rem;
        font-weight: bold;
        color: #333438;
        background: url('~@/assets/images/welfare/game-try/question-icon.png')
          left center no-repeat;
        background-size: 18 * @rem 20 * @rem;
      }
      .content {
        font-size: 14 * @rem;
        color: #333438;
        line-height: 20 * @rem;
        margin-top: 14 * @rem;
      }
      .tip {
        font-size: 14 * @rem;
        color: #f65f34;
        line-height: 20 * @rem;
      }
    }
  }
}
</style>
