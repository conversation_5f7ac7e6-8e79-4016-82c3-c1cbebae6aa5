<template>
  <div class="developer-games-container">
    <nav-bar-2 :title="company_name" :placeholder="false" :border="true">
    </nav-bar-2>

    <yy-list
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh()"
      @loadMore="loadMore()"
      :empty="empty"
      :tips="tips"
    >
      <div class="other-game-list">
        <div
          class="other-game-item"
          v-for="(item, index) in resultList"
          :key="index"
        >
          <other-game-item :couponItem="item"></other-game-item>
        </div>
      </div> </yy-list
  ></div>
</template>

<script>
import { ApiGameGetCPOtherGame } from '@/api/views/game.js';
import OtherGameItem from './Components/other-game-item';
export default {
  name: 'DevelopersOtherGames',
  components: { OtherGameItem },
  data() {
    return {
      company_name: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无游戏',
    };
  },
  async mounted() {},
  async activated() {
    await this.getGameGetCPOtherGame();
  },
  methods: {
    async getGameGetCPOtherGame(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiGameGetCPOtherGame({
        id: this.$route.params.game_id,
        page: this.page,
        listRows: this.listRows,
      });
      this.company_name = res.data.list[0].company_name;
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getGameGetCPOtherGame();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGameGetCPOtherGame(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.developer-games-container {
  padding: 0 18 * @rem;
  margin-top: 50 * @rem;
  .game-list-box {
    .other-game-list {
      .other-game-item {
        margin-bottom: 20 * @rem;
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
