<template>
  <swiper
    v-if="info.tab_action.length"
    class="top-banner"
    :options="bannerSwiperOption"
  >
    <swiper-slide
      class="swiper-slide"
      v-for="(banner, index) in info.tab_action"
      :key="index"
      :style="{ backgroundColor: banner.bg_color }"
      v-sensors-exposure="bannerExposure(banner, index)"
    >
      <div class="banner-item">
        <img class="banner-bg" :src="banner.bg_img_url" alt="" />
        <div
          class="banner-top-tips"
          :style="{ background: banner.bg_color || '#000' }"
          v-if="banner.text2"
          >{{ banner.text2 }}</div
        >
        <div
          class="game-card-type2"
          :style="`background: linear-gradient(180deg, ${banner.bg_color}00 1%,${banner.bg_color}A3 41%, ${banner.bg_color}FF 98%);`"
        >
          <div class="bottom">
            <div class="game-info">
              <div class="game-name" v-if="banner.text1">{{
                banner.text1
              }}</div>
              <div class="game-title" v-if="banner.title || banner.game">{{
                banner.title || banner.game?.main_title + banner.game?.subtitle
              }}</div>
            </div>
            <div class="right-bottom-img" v-if="banner.icon_url">
              <img :src="banner.icon_url" alt="" />
            </div>
          </div>
        </div>
      </div>
    </swiper-slide>
  </swiper>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { ApiStatisticsBanner } from '@/api/views/home.js';
import { devLog, clickBanner } from '@/utils/function.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'fragmentBannerList',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    let that = this;
    return {
      bannerIndex: 0,
      bannerSwiperOption: {
        slidesPerView: 1,
        centeredSlides: true,
        spaceBetween: 12,
        observer: true,
        observeSlideChildren: true,
        loop: true,
        autoplay: {
          delay: 3000,
        },
        on: {
          transitionEnd() {
            that.bannerIndex = this.realIndex % that.info.tab_action.length;
          },
          click() {
            that.bannerIndex = this.realIndex % that.info.tab_action.length;
            that.tapBanner(
              that.info.tab_action[that.bannerIndex],
              that.bannerIndex,
            );
          },
        },
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      const el = document?.querySelector('.top-banner');
      // 解决金刚区点击穿透的问题
      if (el) {
        el.addEventListener(
          'touchstart',
          function (e) {
            e.preventDefault();
          },
          { passive: false },
        );
      }
    });
  },
  methods: {
    bannerExposure(banner, index) {
      return {
        'event-name': 'banner_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-banner_index': `${index}`,
        'property-banner_name':
          banner.title || banner.game?.title || banner.heji?.title,
      };
    },
    tapBanner(banner, index) {
      // 神策埋点
      this.$sensorsTrack('banner_click', {
        page_name: this.$sensorsPageGet(),
        banner_index: index,
        banner_name: banner.title || banner.game?.title || banner.heji?.title,
      });

      this.$sensorsModuleSet(this.info.header_title);

      this.CLICK_EVENT(banner.click_id);
      try {
        if (banner.id) {
          ApiStatisticsBanner({ id: banner.id });
        }
      } catch (e) {}
      handleActionCode(banner);
    },
    // rgbStringToRgba(rgbString, alpha = 1) {
    //   console.log(rgbString);
    //   // 使用正则表达式提取RGB值
    //   const matches = rgbString.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    //   if (!matches) {
    //     throw new Error('Invalid RGB string format');
    //   }

    //   // 提取R、G、B值
    //   const r = parseInt(matches[1], 10);
    //   const g = parseInt(matches[2], 10);
    //   const b = parseInt(matches[3], 10);

    //   // 确保透明度在0到1之间
    //   alpha = Math.min(1, Math.max(0, alpha));

    //   // 返回RGBA格式字符串
    //   return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    // },
  },
};
</script>

<style lang="less" scoped>
.top-banner {
  padding: 0 12 * @rem;
  .swiper-slide {
    width: 339 * @rem;
    height: 252 * @rem;
    border-radius: 12 * @rem;
    overflow: hidden;
    padding: 4 * @rem;
    box-sizing: border-box;
  }

  .banner-item {
    position: relative;
    width: 100%;
    height: 100%;

    .banner-bg {
      width: 100%;
      height: 100%;
      border-radius: 10 * @rem;
    }

    .banner-top-tips {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 22 * @rem;
      padding: 0 10 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 12 * @rem;
      color: #ffffff;
      line-height: 12 * @rem;
      text-align: center;
      border-top-left-radius: 0;
      border-bottom-right-radius: 15 * @rem;
      position: absolute;
      top: -1 * @rem;
      left: -1 * @rem;
      z-index: 10;
    }
    .game-card-type2 {
      display: flex;
      align-items: center;
      width: 100%;
      height: 68 * @rem;
      padding: 0 8 * @rem;
      box-sizing: border-box;
      border-radius: 0 0 10 * @rem 10 * @rem;
      background: linear-gradient(
        180deg,
        rgba(3, 0, 20, 0) 0%,
        rgba(3, 0, 20, 0.5) 34%,
        #030014 82%
      );
      position: absolute;
      bottom: 0;
      left: 0;

      .bottom {
        display: flex;
        align-items: center;
        width: 100%;
        padding-top: 13 * @rem;

        .game-info {
          flex: 1;
          min-width: 0;
          .game-name {
            height: 12 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #ffffff;
            line-height: 12 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .game-title {
            height: 18 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 600;
            font-size: 18 * @rem;
            color: #ffffff;
            line-height: 18 * @rem;
            margin-top: 8 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .game-tags {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            width: 100%;
            height: 20 * @rem;
            margin-top: 3 * @rem;

            .tag {
              flex-shrink: 0;
              padding: 0 6 * @rem;
              height: 20 * @rem;
              background: rgba(255, 255, 255, 0.3);
              border-radius: 30 * @rem;
              margin-right: 10 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #ffffff;
              line-height: 20 * @rem;
            }
          }
        }

        .right-bottom-img {
          flex-shrink: 0;
          width: 61 * @rem;
          margin-left: 5 * @rem;
          margin-right: 19 * @rem;
        }
      }
    }
  }
}
</style>
