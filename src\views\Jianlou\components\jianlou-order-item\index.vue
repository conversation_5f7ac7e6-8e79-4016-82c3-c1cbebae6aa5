<template>
  <div class="jianlou-order-item-component">
    <div class="jianlou-order-item" v-if="jianlouInfo.xh_id">
      <div class="top-content">
        <div class="top-left">
          <div
            class="status"
            :style="{ color: `${jianlouInfo.status_info.color}` }"
          >
            {{ jianlouInfo.status_info.str || '' }}
          </div>
          <div
            class="countdown"
            v-if="jianlouInfo.countdown && jianlouInfo.status == 0"
          >
            ({{ $t('将于') }}{{ jianlouInfo.countdown | formatCountDown
            }}{{ $t('后关闭') }})
          </div>
        </div>

        <div class="top-right">
          <div
            class="operate-btn recharge-btn"
            v-if="statusIn([0])"
            @click="handlePay()"
          >
            {{ $t('支付') }}
          </div>
          <div
            class="operate-btn help-btn"
            v-if="statusIn([1])"
            @click="handleHelp()"
          >
            {{ $t('如何登录') }}
          </div>
          <div
            class="operate-btn cancel-btn"
            v-if="statusIn([0])"
            @click="handleCancel()"
          >
            {{ $t('取消') }}
          </div>
          <div
            class="operate-btn delete-btn"
            v-if="statusIn([1, 2])"
            @click="handleDelete()"
          >
            {{ $t('删除') }}
          </div>
        </div>
      </div>
      <div class="jianlou-item" @click="toOrderDetail">
        <div class="left-info">
          <div class="game-icon">
            <img :src="jianlouInfo.game.titlepic" />
          </div>
        </div>
        <div class="center-info">
          <div class="game-name">
            {{ jianlouInfo.game.main_title
            }}<span class="game-subtitle" v-if="jianlouInfo.game.subtitle">{{
              jianlouInfo.game.subtitle
            }}</span>
          </div>
          <div class="game-area">
            {{ $t('区服') }}：{{ jianlouInfo.game_area }}
          </div>
          <div class="xh-id">{{ $t('小号ID') }}：{{ jianlouInfo.xh_id }}</div>
        </div>
        <div class="right-info">
          <div class="price">
            ¥<span>{{ Number(jianlouInfo.rmb).toFixed(1) }}</span>
          </div>
          <div class="platforms">
            <div
              class="plat"
              v-for="(item, index) in jianlouInfo.platforms"
              :key="index"
            >
              <img :src="item.icon" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 如何登录 -->
    <van-dialog
      v-model="isHelpShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      get-container="body"
    >
      <div class="help-container">
        <div class="title">{{ $t('登录说明') }}</div>
        <div class="help-content">
          <div class="help-p">
            {{ $t('1、直接使用您当前登录APP的账号进入游戏即可。') }}
          </div>
          <div class="help-p">{{ $t('2、登录游戏后选择您购买的小号。') }}</div>
          <img
            class="help-img"
            src="@/assets/images/deal/login-help.png"
            alt=""
          />
          <div class="help-p">
            {{ $t('3、如有任何疑问，可联系') }}<span>{{ $t('客服咨询') }}</span
            >！
          </div>
        </div>
        <div class="confirm-btn btn" @click="isHelpShow = false">
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiXiaohaoChangeOrderStatusJL } from '@/api/views/xiaohao.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'JianlouOrderItem',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      jianlouInfo: this.info,
      isHelpShow: false,
    };
  },
  watch: {
    info(val) {
      this.jianlouInfo = val;
    },
  },
  filters: {
    formatCountDown(val) {
      if (val >= 60) {
        let minute = parseInt(val) / 60;
        return `${Math.floor(minute)}分钟`;
      } else {
        return `${val}秒钟`;
      }
    },
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  methods: {
    toOrderDetail() {
      this.toPage('JianlouOrderDetail', {
        info: this.jianlouInfo,
      });
    },
    statusIn(statusArr) {
      let index = statusArr.findIndex(item => {
        return Number(this.jianlouInfo.status) == item;
      });
      return index == -1 ? false : true;
    },
    handleHelp() {
      this.isHelpShow = true;
    },
    async changeOrderStatus(status) {
      try {
        this.$toast({
          type: 'loading',
          duration: 0,
          message: this.$t('加载中...'),
        });
        // status   2 = 已取消 100 删除
        const res = await ApiXiaohaoChangeOrderStatusJL({
          status: status,
          orderId: this.jianlouInfo.order_id,
        });
      } finally {
        this.$toast.clear();
      }
    },
    handlePay() {
      this.toPage('XiaohaoOrderRecharge', {
        info: this.jianlouInfo,
        order_type: 202, // 小号捡漏
        xhInfo: this.jianlouInfo,
        back: 'MyJianlou',
      });
    },
    handleCancel() {
      this.$dialog
        .confirm({
          message: this.$t(
            '取消支付后，该角色可能会被抢走哦~是否确认取消支付?',
          ),
          confirmButtonText: this.$t('立即付款'),
          cancelButtonText: this.$t('取消支付'),
          closeOnClickOverlay: false,
          lockScroll: false,
        })
        .then(async () => {
          // 立即付款
          this.handlePay();
        })
        .catch(async () => {
          // 取消支付
          await this.changeOrderStatus(2);
          this.jianlouInfo.status = 2;
          this.$set(this.jianlouInfo, 'status_info', {
            str: this.$t('已取消'),
            color: 'rgb(136, 136, 136)',
          });
        });
    },
    handleDelete() {
      this.$dialog
        .confirm({
          message: this.$t('删除后无法恢复，确认删除？'),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeOrderStatus(100);
          this.jianlouInfo = {};
          this.$emit('afterDelete');
        });
    },
    canSellDialog() {
      this.$dialog.alert({
        message: this.$t('该游戏支持小号出售服务'),
        confirmButtonText: this.$t('知道了'),
        confirmButtonColor: '#47A83A',
        lockScroll: false,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-order-item-component {
  .jianlou-order-item {
    box-sizing: border-box;
    margin: 0 auto;
    border-bottom: 0.5 * @rem solid #ebebeb;
    padding: 20 * @rem 0;
    width: 343 * @rem;
    .top-content {
      display: flex;
      justify-content: space-between;
      .top-left {
        display: flex;
        align-items: center;

        margin-left: 6 * @rem;
        .current-status {
          font-size: 14 * @rem;
          color: #000000;
        }
        .status {
          font-size: 14 * @rem;
        }
        .remark {
          font-size: 11 * @rem;
          color: #999;
          margin-top: 5 * @rem;
          margin-bottom: 8 * @rem;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .countdown {
          font-size: 12 * @rem;
          color: #999;
        }
      }
      .top-right {
        display: flex;
        align-items: center;
        .operate-btn {
          padding: 0 10 * @rem;
          height: 20 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          border-radius: 12 * @rem;
          margin-left: 10 * @rem;
          border: 1px solid#BFBFBF;
          color: #bfbfbf;
        }
        .edit-btn {
          border: 1px solid #2486b3;
          color: #2486b3;
        }
        .publish-btn {
          border: 1px solid #ffa811;
          color: #ffa811;
        }
        .unpublish-btn {
          border: 1px solid #ffa811;
          color: #ffa811;
        }
        .cancel-btn {
          border: 1px solid #bfbfbf;
          color: #bfbfbf;
        }
        .delete-btn {
          border: 1px solid #bfbfbf;
          color: #bfbfbf;
        }
        .recharge-btn {
          border: 1px solid #ffa811;
          color: #ffa811;
        }
        .help-btn {
          border: 1px solid #ffa811;
          color: #ffa811;
        }
      }
    }
    .jianlou-item {
      background: #ffffff;
      display: flex;
      align-items: center;
      margin-top: 10 * @rem;
      .left-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .game-icon {
          width: 70 * @rem;
          height: 70 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
        }
      }
      .center-info {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .game-name {
          font-size: 16 * @rem;
          color: #000000;
          font-weight: 600;
          line-height: 22 * @rem;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: flex;
          align-items: center;

          .game-subtitle {
            box-sizing: border-box;
            border: 1 * @rem solid fade(@themeColor, 80);
            border-radius: 3 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 3 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            vertical-align: middle;
            line-height: 1;
          }
        }
        .game-area {
          font-size: 13 * @rem;
          color: @themeColor;
          line-height: 18 * @rem;
          margin-top: 4 * @rem;
        }
        .xh-id {
          font-size: 13 * @rem;
          color: #797979;
          line-height: 18 * @rem;
          margin-top: 8 * @rem;
        }
      }
      .right-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .price {
          font-size: 12 * @rem;
          font-weight: 600;
          color: @themeColor;
          margin-top: -5 * @rem;
          span {
            font-size: 20 * @rem;
            font-weight: 600;
            color: @themeColor;
          }
        }
        .platforms {
          display: flex;
          align-items: center;
          margin-top: 22 * @rem;
          .plat {
            width: 17 * @rem;
            height: 17 * @rem;
            margin-left: 7 * @rem;
          }
        }
      }
    }
  }
}
.help-container {
  box-sizing: border-box;
  width: 320 * @rem;
  padding: 20 * @rem 15 * @rem;
  .title {
    font-size: 20 * @rem;
    color: #000000;
    text-align: center;
  }
  .help-content {
    font-size: 15 * @rem;
    color: #000000;
    line-height: 20 * @rem;
    .help-p {
      margin-top: 15 * @rem;
      span {
        color: #1795ff;
      }
    }
    .help-img {
      width: 260 * @rem;
      height: 168 * @rem;
      margin: 10 * @rem auto 0;
    }
  }
  .confirm-btn {
    height: 45 * @rem;
    background: linear-gradient(90deg, #68cd56, #45cd3c);
    border-radius: 5 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16 * @rem;
    color: #ffffff;
    margin-top: 20 * @rem;
  }
}
</style>
