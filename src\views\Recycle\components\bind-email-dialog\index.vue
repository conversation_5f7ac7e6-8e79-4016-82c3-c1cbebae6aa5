<template>
  <div>
    <!-- 绑定手机 -->
    <van-dialog
      v-model="show"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-title">{{ title }}</div>

      <div class="popup-content">
        <div class="popup-close" @click="onCancel">
          <img src="~@/assets/images/close-dialog.png" alt="" />
        </div>
        <div class="change-content">
          <div class="popup-prompt"> 为了保障您的账号安全，请先绑定邮箱。 </div>
          <div class="field">
            <input
              type="text"
              v-model="email"
              :disabled="disabled"
              placeholder="请输入邮箱地址"
            />
          </div>
          <div class="field">
            <input
              type="text"
              v-model="authCode"
              :placeholder="$t('请输入验证码')"
            />
            <div class="text" v-if="!ifCount" @click="getAuthCode()">
              {{ $t('获取验证码') }}
            </div>
            <div class="text" v-else>
              {{ `${$t('重新获取')}${countdown}s` }}
            </div>
          </div>
        </div>
      </div>

      <div class="operation-bar">
        <div class="btn operation-btn cancel" @click="onCancel">
          {{ cancelText }}
        </div>
        <div class="btn operation-btn confirm" @click="onConfirm">
          {{ confirmText }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiMailSend, ApiUserBindEmail } from '@/api/views/users';
import { mapActions } from 'vuex';
export default {
  name: 'BindEmailDialog',
  model: {
    prop: 'dialogShow',
    event: 'change',
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: this.dialogShow,
      title: '绑定邮箱',
      cancelText: '取消',
      confirmText: '确定',
      showCancel: false,
      showConfirm: true,
      disabled: false,
      step: 1,
      email: '', //邮箱
      authCode: '', //验证码
      countdown: 60, //倒计时
      ifCount: false, //倒计时开关
    };
  },
  created() {
    this.email = '';
  },
  watch: {
    show(val) {
      this.$emit('change', val);
    },
    dialogShow(val) {
      this.show = val;
    },
    authCode() {
      if (this.authCode > 1000000)
        this.authCode = Math.floor(this.authCode / 10);
    },
  },
  methods: {
    onCancel() {
      this.show = false;
      setTimeout(() => {
        this.email = '';
        this.authCode = '';
      }, 200);
    },
    async onConfirm() {
      if (!this.email) {
        this.$toast('请输入邮箱');
        return;
      }
      if (!this.authCode) {
        this.$toast('请输入验证码');
        return;
      }
      this.$toast.loading('加载中');
      const params = {
        email: this.email,
        code: this.authCode,
        step: this.step,
      };
      try {
        const res = await ApiUserBindEmail(params);
        if (res) {
          await this.SET_USER_INFO();
          this.$toast(res.msg);
          this.show = false;
        }
      } catch (error) {
      } finally {
        this.disabled = !this.disabled;
      }
    },
    // 获取邮箱验证码
    getAuthCode() {
      if (this.email === '') {
        this.$toast('请输入邮箱');
        return false;
      }
      // 发送axios请求
      let params = {
        email: this.email,
        type: 11,
      };
      if (this.step) {
        params.step = this.step;
      }
      ApiMailSend(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 355 * @rem;
  padding: 20 * @rem 0;

  .popup-title {
    font-size: 16 * @rem;
    color: #333333;
    text-align: center;
    font-weight: 600;
    line-height: 40 * @rem;
    overflow: hidden;
    white-space: nowrap;
    border-bottom: 1px solid #f3f3f8;
  }

  .popup-content {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    // text-align: center;
    padding: 0 31 * @rem;
    position: relative;
    .popup-close {
      position: absolute;
      top: -64 * @rem;
      right: 14 * @rem;
      img {
        width: 15 * @rem;
        height: 15 * @rem;
      }
    }
    .change-content {
      .popup-prompt {
        margin: 18 * @rem 0 19 * @rem 0;
      }
      //   margin-top: 19 * @rem;
      //   padding: 0 17 * @rem;
      .field {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 44 * @rem;
        border-radius: 6 * @rem;
        overflow: hidden;
        position: relative;
        &:not(:first-of-type) {
          margin-top: 15 * @rem;
        }
        .tel-right {
          width: 113 * @rem;
          height: 42 * @rem;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          .clear {
            width: 16 * @rem;
            height: 42 * @rem;
            padding: 0 10 * @rem;
            background-image: url(~@/assets/images/users/keyword-clear.png);
            background-size: 14 * @rem 14 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.transparent {
              opacity: 0;
            }
          }
          .country-code {
            display: flex;
            height: 42 * @rem;
            align-items: center;
            padding-left: 9 * @rem;
            position: relative;
            &::before {
              content: '';
              width: 1 * @rem;
              height: 11 * @rem;
              background-color: #dadada;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .country-code-text {
              font-size: 16 * @rem;
              color: #000000;
            }
            .arrow-down {
              width: 10 * @rem;
              height: 6 * @rem;
              .image-bg('~@/assets/images/users/arrow-down.png');
              margin-left: 5 * @rem;
              margin-top: 2 * @rem;
            }
          }
        }
        input {
          box-sizing: border-box;
          flex: 1;
          min-width: 0;
          height: 100%;
          padding: 0 5 * @rem;
          line-height: 44 * @rem;
          font-size: 14 * @rem;
          letter-spacing: 1 * @rem;
          background-color: #f4f4f4;
          padding: 0 20 * @rem;
          border-radius: 6 * @rem;
          &.pdr115 {
            padding-right: 115 * @rem;
          }
          &.pdr40 {
            padding-right: 40 * @rem;
          }
          &[disabled] {
            color: #000;
            opacity: 1;
          }
        }
        .text {
          box-sizing: border-box;
          border: 1 * @rem solid @themeColor;
          font-size: 14 * @rem;
          height: 42 * @rem;
          width: 116 * @rem;
          border-radius: 6 * @rem;
          color: @themeColor;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 10 * @rem;
          margin-right: 1 * @rem;
          &.text2 {
            color: #a4a4a4;
          }
        }
        .eyes {
          width: 18 * @rem;
          height: 44 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          position: absolute;
          right: 20 * @rem;
          top: 50%;
          transform: translateY(-50%);
          &.open {
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
    }
  }
  .operation-bar {
    display: flex;
    align-items: center;
    margin: 21 * @rem 0 0;
    padding: 0 31 * @rem;
    gap: 20 * @rem;
    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 36 * @rem;
      border-radius: 22 * @rem;
      font-size: 14 * @rem;
      &.confirm {
        background: @themeBg;
        color: #fff;
      }
      &.cancel {
        background: #f5f5f5;
        color: #7d7d7d;
      }
    }
  }
}
</style>
