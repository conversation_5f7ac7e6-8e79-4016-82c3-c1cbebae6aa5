<template>
  <div class="bargain-item">
    <user-avatar
      class="avatar"
      :src="info.user.avatar"
      :self="false"
    ></user-avatar>
    <div class="bargain-info">
      <div class="user-info">
        <div class="nickname" :class="{ orange: info.user.is_official == 1 }">
          {{ info.user.nickname }}
        </div>
        <div class="exp-level" v-if="info.user.exp_level_name">
          {{ info.user.exp_level_name }}
        </div>
        <div
          v-if="info.user.pay_level_name"
          class="pay-level"
          :style="{ backgroundColor: info.user.pay_level_color }"
        >
          {{ info.user.pay_level_name }}
        </div>
      </div>
      <div class="bargain-price">{{ $t('出价') }}：{{ info.amount }}</div>
    </div>
    <template v-if="info.is_show">
      <div class="is-appoint" v-if="!!info.is_appoint" @click="clickAppoint">
        {{ $t('指定TA出售') }}
      </div>
      <div class="is-appoint no" v-else-if="Number(info.status) == 0">
        {{ $t('指定TA出售') }}
      </div>
      <div
        class="is-appoint"
        :class="{ no: trade_status == 7 }"
        v-else
        @click="clickCancelAppoint"
      >
        {{ $t('已指定') }}
      </div>
    </template>
    <template v-else>
      <div class="is-mine" v-if="!!info.is_mine"></div>
    </template>
    <!-- 确认指定 -->
    <appoint-dialog
      :text="$t('您确定要进行指定出售吗？')"
      :isShow.sync="confirmShow"
      :tradeId="info.trade_id"
      :bargainId="info.id"
      :amount="info.amount"
      @refresh="$emit('refresh')"
    ></appoint-dialog>
    <!-- 取消指定 -->
    <appoint-dialog
      :text="$t('您确定要取消指定出售吗？')"
      :isShow.sync="cancelShow"
      :tradeId="info.trade_id"
      :bargainId="info.id"
      :cancelAppoint="true"
      @refresh="$emit('refresh')"
    ></appoint-dialog>
  </div>
</template>

<script>
import appointDialog from '../appoint-dialog/index.vue';
export default {
  name: 'bargainItem',
  components: {
    appointDialog,
  },
  props: {
    info: {
      type: Object,
      requited: true,
    },
    trade_status: {
      type: Number,
      default: 3, // 默认交易中...
    },
  },
  data() {
    return {
      confirmShow: false,
      cancelShow: false,
    };
  },
  computed: {
    isSelf() {
      if (
        this.userInfo.user_id &&
        this.info.trade_mem_id == this.userInfo.user_id
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  methods: {
    clickAppoint() {
      this.confirmShow = true;
    },
    clickCancelAppoint() {
      if (this.trade_status == 7) {
        return;
      }
      this.cancelShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.bargain-item {
  display: flex;
  align-items: center;
  padding: 10 * @rem 0 15 * @rem;
  &:not(:last-of-type) {
    border-bottom: 1 * @rem solid #f3f3f8;
  }
  .avatar {
    width: 36 * @rem;
    height: 36 * @rem;
  }
  .bargain-info {
    flex: 1;
    min-width: 0;
    margin-left: 9 * @rem;
    margin-right: 10 * @rem;
    .user-info {
      display: flex;
      align-items: center;
      margin-top: 5 * @rem;
      .nickname {
        font-size: 15 * @rem;
        color: #000000;
        font-weight: 500;
        line-height: 21 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.orange {
          color: @themeColor;
        }
      }
      .exp-level {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 2 * @rem;
        background-color: #309af8;
        padding: 0 5 * @rem;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #ffffff;
        margin-left: 8 * @rem;
        flex-shrink: 0;
      }
      .pay-level {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 2 * @rem;
        background-color: #ed9239;
        padding: 0 5 * @rem;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #ffffff;
        margin-left: 6 * @rem;
        flex-shrink: 0;
      }
    }
    .bargain-price {
      font-size: 13 * @rem;
      color: @themeColor;
      line-height: 18 * @rem;
      margin-top: 4 * @rem;
    }
  }
  .is-appoint {
    box-sizing: border-box;
    width: 80 * @rem;
    height: 28 * @rem;
    border-radius: 14 * @rem;
    border: 1 * @rem solid @themeColor;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11 * @rem;
    color: @themeColor;
    &.no {
      background-color: #ebebeb;
      color: #797979;
      border: none;
    }
  }
  .is-mine {
    margin-left: 10 * @rem;
    width: 32 * @rem;
    height: 20 * @rem;
    .image-bg('~@/assets/images/deal/is-mine.png');
  }
}
</style>
