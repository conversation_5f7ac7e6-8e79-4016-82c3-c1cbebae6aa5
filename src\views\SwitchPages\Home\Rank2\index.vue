<template>
  <div class="rank-page">
    <!-- <div class="top-banner"></div> -->
    <div class="tab-container">
      <div class="tab-list">
        <div
          class="tab-item"
          :class="{ on: activeOrder == item.order }"
          v-for="item in tabList"
          :key="item.order"
          @click="tapNav(item)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <yy-list
      class="rank-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
      :tips="$t('暂无数据')"
    >
      <div class="rank-list">
        <div
          class="rank-item"
          v-sensors-exposure="gameExposure(item, index)"
          v-for="(item, index) in rankList"
          :key="index"
        >
          <div class="game-info" :class="{ 'hot-top-info': index < 3 }">
            <div class="rank-num">
              {{ index + 1 }}
            </div>
            <div
              class="game-info-container"
            >
              <game-item-4 :gameInfo="item" :showHot="true" @clickWith="clickRankItem(item, index)"></game-item-4>
            </div>
            <yy-download-btn
              :gameInfo="item"
              @clickWith="clickRankItemBtn(item, index)"
            ></yy-download-btn>
          </div>
        </div>
      </div>
    </yy-list>
    <footer-to-category
      v-if="rankList.length > 0 && finished"
    ></footer-to-category>
  </div>
</template>

<script>
import {
  ApiGameIndex,
  ApiGameTabRanking,
  ApiGameWebRanking,
} from '@/api/views/game.js';
import footerToCategory from '../components/footer-to-category';
export default {
  name: 'Rank',
  components: {
    footerToCategory,
  },
  data() {
    return {
      rankList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      activeOrder: 0,
      tabList: [],
      finished: false,
      empty: false,
      page: 1,
      listRows: 20,
    };
  },
  async activated() {
    await this.getTabList();
    if (this.$route.query.tab) {
      this.activeOrder = this.$route.query.tab;
    }
    this.loadingObj.loading = true;
    await this.getRankList();
  },
  methods: {
    clickRankItem(item, index) {
      this.$sensorsTrack('top_list_click', {
        page_name: this.$sensorsPageGet(),
        tab_name: this.tabList.find(order => order.order === this.activeOrder)
          .title,
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
        button_name: item.title,
      });
    },
    clickRankItemBtn(item, index) {
      this.$sensorsTrack('top_list_click', {
        page_name: this.$sensorsPageGet(),
        tab_name: this.tabList.find(order => order.order === this.activeOrder)
          .title,
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
        button_name: item.h5_url ? '开始玩' : '下载',
      });
    },
    gameExposure(item, index) {
      return {
        'event-name': 'top_list_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-tab_name': this.tabList.find(
          order => order.order === this.activeOrder,
        ).title,
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    async tapNav(item) {
      if (this.activeOrder === item.order) return;
      this.CLICK_EVENT(item.click_id);
      this.empty = false;
      this.activeOrder = item.order;
      this.rankList = [];
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getRankList();
    },
    async getTabList() {
      const res = await ApiGameTabRanking();
      this.tabList = res.data.tab_list;
      if (this.activeOrder == 0 && this.tabList.length > 0) {
        this.activeOrder = this.tabList[0].order;
      }
    },
    async getRankList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameWebRanking({
        type: Number(this.activeOrder),
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page == 1) {
        this.rankList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.rankList.push(...list);
      this.loadingObj.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getRankList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getRankList(2);
    },
  },
};
</script>
<style lang="less" scoped>
.rank-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f7f8fa;
  /deep/ .van-list__finished-text {
    display: none;
  }
  &:before {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(80 * @rem + @safeAreaTop);
    height: calc(80 * @rem + @safeAreaTopEnv);
    background: #fff;
  }

  .top-banner {
    width: 100%;
    height: 155 * @rem;
    background: url(~@/assets/images/home/<USER>
      no-repeat;
    background-size: 100% auto;
    margin-top: 10 * @rem;
  }
  .tab-container {
    box-sizing: border-box;
    .fixed-center;
    width: 100%;
    height: 48 * @rem;
    position: fixed;
    background-color: #fdfefe;
    z-index: 1000;
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none !important;
    }
    .tab-list {
      display: flex;
      align-items: center;
      height: 48 * @rem;
      padding-left: 12 * @rem;
      .tab-item {
        position: relative;
        flex-shrink: 0;
        flex-grow: 0;
        font-size: 13 * @rem;
        color: #333333;
        height: 29 * @rem;
        padding: 0 15 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        border-radius: 8 * @rem;
        letter-spacing: 0.02em;
        &:not(:first-of-type) {
          margin-left: 10 * @rem;
        }
        &.on {
          background-color: #ecfbf4;
          color: #2bbe88;
          font-weight: 600;
          letter-spacing: 0;
        }
      }
    }
  }
  .rank-container {
    padding-top: 55 * @rem;
    // background-color: #f8f9fb;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0,
      #fbfbfc 55 * @rem,
      #f9fafb 10%,
      #f8f9fb 20%,
      #f7f8fa 100%
    );
  }
  .rank-list {
    .rank-item {
      box-sizing: border-box;
      margin: 0 auto;
      width: 351 * @rem;
      display: flex;
      align-items: center;
      background: #ffffff;
      border-radius: 12 * @rem;
      overflow: hidden;
      &:not(:first-of-type) {
        margin-top: 10 * @rem;
      }

      .game-info {
        position: relative;
        padding: 5 * @rem 10 * @rem 5 * @rem 0;
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        .rank-num {
          position: relative;
          color: #9a9a9a;
          font-size: 14 * @rem;
          font-weight: 400;
          width: 39 * @rem;
          height: 39 * @rem;
          overflow: hidden;
          flex-shrink: 0;
          flex-grow: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          // margin-right: 14 * @rem;
          background-repeat: no-repeat;
          background-size: 39 * @rem 39 * @rem;
          background-position: center center;
        }
        .game-info-container {
          flex: 1;
          min-width: 0;
        }
      }
      .hot-top-info {
        padding-left: 39 * @rem;
      }
      // &:not(:last-of-type) {
      //   .game-info {
      //     border-bottom: 0.5px solid #ebebeb;
      //   }
      // }
      &:nth-of-type(1) {
        .hot-top-info {
          background-image: url(~@/assets/images/home/<USER>
          background-size: 351 * @rem 100%;
        }
        .rank-num {
          position: absolute;
          left: 2 * @rem;
          top: 2 * @rem;
          text-indent: -99999px;
          background-image: url(~@/assets/images/home/<USER>
          width: 30 * @rem;
          height: 33 * @rem;
          background-size: 30 * @rem 33 * @rem;
        }
      }
      &:nth-of-type(2) {
        .hot-top-info {
          background-image: url(~@/assets/images/home/<USER>
          background-size: 351 * @rem 100%;
        }
        .rank-num {
          position: absolute;
          left: 2 * @rem;
          top: 2 * @rem;
          text-indent: -99999px;
          background-image: url(~@/assets/images/home/<USER>
          width: 30 * @rem;
          height: 33 * @rem;
          background-size: 30 * @rem 33 * @rem;
        }
      }
      &:nth-of-type(3) {
        .hot-top-info {
          background-image: url(~@/assets/images/home/<USER>
          background-size: 351 * @rem 100%;
        }
        .rank-num {
          position: absolute;
          left: 2 * @rem;
          top: 2 * @rem;
          text-indent: -99999px;
          background-image: url(~@/assets/images/home/<USER>
          width: 30 * @rem;
          height: 33 * @rem;
          background-size: 30 * @rem 33 * @rem;
        }
      }
    }
  }
}
</style>
