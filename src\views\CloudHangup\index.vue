<template>
  <div class="page" ref="page">
    <nav-bar-2
      :border="false"
      :title="$t('云挂机')"
      :azShow="true"
      :placeholder="false"
    >
      <template #right>
        <div class="right-FAQ" @click="toCloudHangupFAQ()"></div>
      </template>
    </nav-bar-2>
    <van-loading class="loading-box" v-if="!loadSuccess" size="24* @rem">{{
      $t('加载中...')
    }}</van-loading>
    <div class="container" v-if="loadSuccess">
      <div class="equipment-cloud" v-if="cloudDeviceList.length == 0">
        <div class="cloud-box">
          <div class="cloud-content">
            <div class="cloud-title">{{ text1 }}</div>
            <div class="cloud-describe">{{ text2 }}</div>
            <div class="cloud-function">
              {{ $t('功能特点') }}
            </div>
            <div class="cloud-trait">
              <div
                v-for="(trait, index) in traitList"
                :key="index"
                class="trait-item"
              >
                <div class="item-logo">
                  <img :src="trait.icon_url" alt="" />
                </div>
                <div class="item-describe">
                  <div class="item-describe-first">
                    {{ trait.text1 }}
                  </div>
                  <div class="item-describe-second">
                    {{ trait.text2 }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="cloud-btn">
            <!-- <div
              class="trial-btn"
              v-if="new_user"
              @click="trialFreeCloudOnHook"
            >
              新人免费试用{{ free_time }}小时
            </div> -->
            <div
              class="trial-btn"
              v-if="new_user && available_devices"
              @click="buyCloudOnHook"
            >
              {{ btn_text }}
            </div>
            <div
              class="trial-btn"
              v-else-if="!new_user && available_devices"
              @click="buyCloudOnHook"
            >
              {{ btn_text }}
            </div>
            <div class="trial-btn out" v-else-if="!available_devices">
              {{ btn_text }}
            </div>
            <div class="trial-text" @click="selectGameCloudHangUp">
              {{ $t('点击查看支持云挂机的游戏') }}
            </div>
          </div>
        </div>
      </div>
      <div class="cloud-box-onHook" v-if="cloudDeviceList.length > 0">
        <div class="cloud-box-onHook-num">
          <div class="number">
            {{ $t('已购买') }}:&nbsp;&nbsp;{{ cloudDeviceList.length
            }}{{ $t('台设备') }}
          </div>
          <div class="name" ref="selectList">
            <div @click.stop="toggleSelectList">
              <span class="title">{{ checkCloudDeviceItem.title }}</span>
              <span class="logo" :class="{ active: showSelectList }">
                <img src="@/assets/images/cloudHangup/down-img.png" alt="" />
              </span>
            </div>
            <transition name="slide-fade">
              <div class="select-list" v-if="showSelectList">
                <div class="select-list-content">
                  <div
                    v-for="(item, index) in cloudDeviceList"
                    :key="index"
                    class="select-list-title"
                    :class="{ active: checkCloudDeviceItem.id === item.id }"
                    @click="selectDevice(item)"
                  >
                    {{ item.title }}
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>
        <div
          class="cloud-box-onHook-content"
          :style="{
            backgroundImage:
              Number(checkCloudDeviceItem.expire_code) == 1 ||
              Number(checkCloudDeviceItem.expire_code) == 4
                ? `url('${checkCloudDeviceItem.game.video_thumb}') `
                : 'linear-gradient(220deg, #09794d 0%, #09794d 100%)',
          }"
        >
          <div
            class="onHook-content-item"
            :class="{
              default:
                Number(checkCloudDeviceItem.expire_code) !== 1 &&
                Number(checkCloudDeviceItem.expire_code) !== 4,
            }"
          >
            <div
              class="item-info"
              :class="{
                active: Number(checkCloudDeviceItem.expire_code) == 1,
              }"
            >
              <div class="item-name">
                <span class="item-name-title">{{
                  checkCloudDeviceItem.title
                }}</span>
                <span
                  class="item-name-modify"
                  @click="modifyDeviceName(checkCloudDeviceItem.title)"
                  ><img src="@/assets/images/cloudHangup/modify-img.png" alt=""
                /></span>
              </div>
              <div class="item-time">
                <div>{{ $t('到期时间') }}:</div>
                <span>{{ formatTime(checkCloudDeviceItem.expire_time) }}</span>
              </div>
              <div class="item-game-name" v-if="checkCloudDeviceItem.game">
                <div>{{ $t('游戏名称') }}:</div>
                <span>{{ checkCloudDeviceItem.game.title }}</span>
              </div>
              <div
                class="item-status"
                :class="getStatusClass(checkCloudDeviceItem.expire_code)"
              >
                {{ getStatusText(checkCloudDeviceItem.expire_code) }}
              </div>
            </div>
            <div
              class="no-games-box"
              v-if="!checkCloudDeviceItem.game"
              @click.stop="selectGameCloudHangUp()"
            >
              <div class="box-logo">
                <img
                  src="@/assets/images/cloudHangup/no-games-logo.png"
                  alt=""
                />
              </div>
              <div class="box-text1">{{ $t('暂无挂机游戏') }}</div>
              <div class="box-text2">{{ $t('点击此处选择游戏云挂机') }}</div>
            </div>
            <div
              v-else-if="
                checkCloudDeviceItem.game &&
                checkCloudDeviceItem.expire_code == 1
              "
              class="no-games-box"
              @click="toCloudHangup()"
            >
              <div class="box-text3">{{ $t('点击前往挂机页面') }}</div>
            </div>
            <div
              v-else-if="
                checkCloudDeviceItem.game &&
                checkCloudDeviceItem.game.cloud_status == 1
              "
              class="no-games-box"
              @click="toCloudHangup()"
            >
              <div class="box-text3">
                {{ $t('当前游戏正在更新中，请耐心等待') }}
              </div>
            </div>
            <div
              class="item-btn"
              :class="{
                active: Number(checkCloudDeviceItem.expire_code) == 1,
              }"
            >
              <div class="item-btn-nr" @click="reacquireApiInfo()">
                <span
                  ><img src="@/assets/images/cloudHangup/sx-img.png" alt=""
                /></span>
                <span>{{ $t('刷新') }}</span>
              </div>
              <div
                class="item-btn-nr"
                v-if="
                  Number(checkCloudDeviceItem.expire_code) == 0 ||
                  Number(checkCloudDeviceItem.expire_code) == 3
                "
                @click="selectGameCloudHangUp()"
              >
                <span
                  ><img src="@/assets/images/cloudHangup/xzyx-img.png" alt=""
                /></span>
                <span>{{ $t('选择游戏') }}</span>
              </div>
              <div class="item-btn-nr" @click="toRenewYour()">
                <span
                  ><img src="@/assets/images/cloudHangup/xf-img.png" alt=""
                /></span>
                <span>{{ $t('续费') }}</span>
              </div>
              <div
                class="item-btn-nr"
                v-if="
                  Number(checkCloudDeviceItem.expire_code) == 1 ||
                  Number(checkCloudDeviceItem.expire_code) == 4
                "
                @click="toCloudHangup()"
              >
                <span
                  ><img src="@/assets/images/cloudHangup/jr-img.png" alt=""
                /></span>
                <span>{{ $t('进入游戏') }}</span>
              </div>
              <div
                class="item-btn-nr"
                v-if="
                  Number(checkCloudDeviceItem.expire_code) == 1 ||
                  Number(checkCloudDeviceItem.expire_code) == 4
                "
                @click="toExitOnHookGame()"
              >
                <span
                  ><img src="@/assets/images/cloudHangup/tc-img.png" alt=""
                /></span>
                <span>{{ $t('退出挂机') }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="cloud-box-onHook-btn">
          <div
            class="trial-btn"
            @click="buyCloudOnHook"
            v-if="available_devices"
          >
            {{ $t('+购买新设备') }}
          </div>
        </div>
      </div>
    </div>
    <!-- 查看云挂机游戏 -->
    <cloud-game-popup
      :show.sync="cloudGamePopupShow"
      :equip_id="selectedId"
      :update-trigger="updateTrigger"
      @send-data="receiveDataFromChild"
      @send-removedData="removedInsGame"
    ></cloud-game-popup>
    <!-- 弹窗 -->
    <van-popup
      v-model="popupShow"
      :lock-scroll="false"
      position="bottom"
      :safe-area-inset-bottom="true"
      class="vant-popup"
    >
      <div class="popup-box">
        <div class="popup-title" v-if="isModifyNameShow">
          {{ $t('修改设备名称') }}
        </div>
        <div class="popup-title" v-else>{{ $t('温馨提示') }}</div>
        <div class="game-list-search" v-if="isModifyNameShow">
          <div class="search-bar">
            <div class="search-input">
              <form @submit.prevent="submitSearch(modifyName)">
                <input
                  v-model.trim="modifyName"
                  type="text"
                  :placeholder="$t('请输入8个字符以内的名称')"
                  @input="inputListener"
                  :maxlength="8"
                />
              </form>
            </div>
            <div
              class="input-clear"
              v-if="inputClearShow"
              @click="clearInput"
            ></div>
          </div>
        </div>
        <div class="popup-text" v-else>
          {{ quit_tip }}
        </div>

        <div class="popup-btn">
          <div class="cancel" @click.stop="popupShow = false">
            {{ $t('取消') }}
          </div>
          <div
            class="exit"
            :class="{ 'disable-btn': !modifyName }"
            v-if="isModifyNameShow"
            @click.stop="confirmUpdateName()"
          >
            {{ $t('确认') }}
          </div>
          <div class="exit" v-else @click.stop="confirmExitAndHangUp()">
            {{ $t('确认退出') }}
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 续费/购买 设备弹窗 -->
    <van-dialog
      v-model="devicePayPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :overlay-style="{ 'z-index': '2996' }"
      class="onhook-popup"
    >
      <div class="title">{{ $t('温馨提示') }}</div>
      <div class="content" v-if="!cloudDeviceList.length">
        {{ $t('请先前购买设备，方可使用哦~') }}
      </div>
      <div class="content" v-else>
        {{ $t('当前设备已过期，请先前往续费') }}
      </div>
      <div class="btn-info">
        <div class="cancel" @click.stop="devicePayCancel()">
          {{ $t('取消') }}
        </div>
        <div class="confirm" @click.stop="devicePayConfirm()">
          {{
            !cloudDeviceList.length ? `${$t('前往购买')}` : `${$t('前往续费')}`
          }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiCloudList,
  ApiCloudEquipDetail,
  ApiCloudEquipList,
  ApiCloudOutLine,
  ApiCloudUpdateName,
  ApiCloudScreenshot,
  ApiCloudUninstallApp,
} from '@/api/views/upCloud.js';
import CloudGamePopup from './components/cloud-game-popup/index.vue';
import OnHookPopup from './components/on-hook-popup';
import { BOX_openInNewWindow } from '@/utils/box.uni.js';
import { mapMutations, mapGetters } from 'vuex';
export default {
  name: 'CloudHangup',
  components: {
    CloudGamePopup,
    OnHookPopup,
  },
  data() {
    return {
      loadSuccess: false,
      popupShow: false,
      devicePayPopupShow: false, // 续费购买设备跳转
      isModifyNameShow: false,
      modifyName: '', //修改设备名称
      traitList: [],
      quit_tip: '',
      btn_text: '',
      text1: '',
      text2: '',
      count: 0,
      cloudGamePopupShow: false,
      onHookGameInfo: [], // 需要挂机游戏信息
      new_user: true, // 是否新用户
      available_devices: 0, // 1 有设备 0 没有设备
      free_time: 0, // 免费时长
      cloudDeviceList: [], // 云挂机设备列表
      checkCloudDeviceItem: [], // 当前设备
      selectedId: null, // 当前设备Id
      showSelectList: false,
      errMsg: '',
      receiveData: [], // 云挂机游戏
      updateTrigger: 0, // 卸载更新的计数器
    };
  },
  created() {
    // this.GetCloudMountedList();
    // this.trialFreeCloudOnHook()
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.query.replace) {
        vm.$router.go(-1);
      }
    });
  },
  activated() {
    // 神策埋点
    this.$sensorsTrack('AFK_pageView');
    
    this.GetCloudMountedList();
    this.devicePayPopupShow = false;
    this.cloudGamePopupShow = false;
  },
  mounted() {
    document.addEventListener(
      'click',
      e => {
        let selectList = this.$refs.selectList;
        if (!selectList?.contains(e.target)) {
          this.showSelectList = false;
        }
      },
      true,
    );
  },
  methods: {
    toCloudHangupFAQ() {
      BOX_openInNewWindow(
        { name: 'CloudHangupFAQ' },
        {
          url: `https://${this.$h5Page.env}game.3733.com/#/cloud_hangup_FAQ`,
        },
      );
    },
    devicePayCancel() {
      this.devicePayPopupShow = false;
    },
    devicePayConfirm() {
      this.devicePayPopupShow = false;
      if (!this.cloudDeviceList.length) {
        this.toPage('BuyCloudDevices');
      } else {
        this.toPage('BuyCloudDevices', { equip_id: this.selectedId });
      }
    },
    // 刷新
    reacquireApiInfo() {
      this.$toast.loading(this.$t('加载中...'));
      this.getCloudDeviceDetail();
      if (this.checkCloudDeviceItem?.expire_code == 1) {
        this.refreshClickToGetScreenshots();
      }
    },
    // 续费
    toRenewYour() {
      this.toPage('BuyCloudDevices', { equip_id: this.selectedId });
    },
    // 退出挂机弹窗
    async toExitOnHookGame() {
      this.isModifyNameShow = false;
      this.popupShow = true;
    },
    // 确认退出
    async confirmExitAndHangUp() {
      this.popupShow = false;
      this.$toast.loading(this.$t('加载中...'));
      await ApiCloudOutLine({
        equip_id: this.selectedId,
        app_id: this.checkCloudDeviceItem.game.id,
      });
      this.GetCloudMountedList();
    },
    // 修改名称
    async confirmUpdateName() {
      if (!this.modifyName.length) {
        this.$toast(this.$t('请输入设备名称'));
        return;
      }
      this.popupShow = false;
      if (this.modifyName && this.selectedId) {
        await ApiCloudUpdateName({
          title: this.modifyName,
          equip_id: this.selectedId,
        });
        this.GetCloudMountedList();
      }
    },
    // 修改设备名称
    modifyDeviceName(name) {
      this.modifyName = name;
      this.isModifyNameShow = true;
      this.popupShow = true;
    },
    inputListener() {},
    toggleSelectList() {
      this.showSelectList = !this.showSelectList;
    },
    selectDevice(device) {
      if (this.checkCloudDeviceItem.id == device.id) {
        this.showSelectList = false;
        return;
      }
      this.$toast.loading(this.$t('加载中...'));
      this.selectedId = device.id;
      this.getCloudDeviceDetail();
      this.showSelectList = false;
    },
    clearInput() {
      this.modifyName = '';
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
    },
    async GetCloudMountedList() {
      try {
        const res = await ApiCloudList();
        const {
          quit_tip,
          btn_text,
          text1,
          text2,
          count,
          new_user,
          available_devices,
          free_time,
          text: traitList,
          list,
        } = res.data;
        this.quit_tip = quit_tip;
        this.btn_text = btn_text;
        this.text1 = text1;
        this.text2 = text2;
        this.count = count;
        this.new_user = new_user;
        this.available_devices = available_devices;
        this.free_time = free_time;
        this.traitList = traitList;
        if (list) {
          this.cloudDeviceList = list;
          // 设置云类型并加载相应的SDK 默认值给0 0百度云 2臂云
          if (this.cloudDeviceList.length) {
            const cloudType =
              list[0]?.cloud_type !== undefined ? list[0]?.cloud_type : 0;
            this.setCloudType(cloudType);
          }
          const selectedItem = list.find(item => item.id === this.selectedId);
          if (selectedItem) {
            this.checkCloudDeviceItem = selectedItem;
          } else {
            //selectedItem undefined 第一次走这
            this.selectedId = list[0]?.id;
            this.checkCloudDeviceItem = list[0];
          }
        } else {
          this.cloudDeviceList = [];
        }
        this.loadSuccess = true;
      } catch (error) {
        this.loadSuccess = true;
      } finally {
        this.loadSuccess = true;
      }
    },
    getStatusText(code) {
      if (
        this.checkCloudDeviceItem?.game?.cloud_status &&
        this.checkCloudDeviceItem?.game?.cloud_status == 1
      ) {
        return this.$t('正在更新');
      } else {
        return code === 0
          ? this.$t('闲置中')
          : code === 1
          ? this.$t('挂机中')
          : code === 2
          ? this.$t('已过期')
          : code === 3
          ? this.$t('加载中')
          : '';
      }
    },
    getStatusClass(code) {
      if (
        this.checkCloudDeviceItem?.game?.cloud_status &&
        this.checkCloudDeviceItem?.game?.cloud_status == 1
      ) {
        return 'zzgx';
      } else {
        return code === 0
          ? 'xzz'
          : code === 1
          ? 'gj'
          : code === 2
          ? 'ygq'
          : code === 3
          ? 'jzz'
          : '';
      }
    },
    formatTime(val) {
      let { year, date, time } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
    // 查看设备列表
    async getCloudDeviceList() {
      const res = await ApiCloudEquipList();
    },
    // 刷新点击获取截图
    async refreshClickToGetScreenshots() {
      const res = await ApiCloudScreenshot({
        equip_id: this.selectedId,
        app_id: this.checkCloudDeviceItem.game.id,
      });

      if (res.data.screenshot) {
        this.checkCloudDeviceItem.game.video_thumb = res.data.screenshot;
      }
    },
    // 查看设备详情
    async getCloudDeviceDetail() {
      const res = await ApiCloudEquipDetail({
        equip_id: this.selectedId,
      });
      this.$toast.clear();
      this.checkCloudDeviceItem = res.data.info;
    },
    // 免费试用云挂机
    // async trialFreeCloudOnHook() {
    //   this.$toast.loading(this.$t("加载中..."))
    //   const res = await ApiCloudMountedBuyFree()
    //   if (res.data.status == 1) {
    //     this.GetCloudMountedList()
    //   }
    // },
    // 购买云挂机设备
    buyCloudOnHook() {
      this.toPage('BuyCloudDevices');
    },
    // 查看云挂机游戏
    selectGameCloudHangUp() {
      this.cloudGamePopupShow = true;
    },
    async toCloudHangup() {
      await this.getCloudDeviceDetail();
      if (this.checkCloudDeviceItem.expire_code == 2) {
        this.devicePayPopupShow = true;
        return;
      } else if (
        this.checkCloudDeviceItem.game &&
        this.checkCloudDeviceItem.expire_code == 1
      ) {
        this.setReceiveData(this.checkCloudDeviceItem.game);
        this.setCheckCloudDeviceItem(this.checkCloudDeviceItem);
        this.toPage('CloudHangupInterface', {
          receiveData: this.checkCloudDeviceItem.game,
          checkCloudDeviceItem: this.checkCloudDeviceItem,
        });
      } else if (
        this.checkCloudDeviceItem?.game?.cloud_status &&
        this.checkCloudDeviceItem?.game?.cloud_status == 1
      ) {
        this.$toast.loading(this.$t('请稍等...'));

        if (!this.checkCloudDeviceItem.game.cloud_status) {
          this.setReceiveData(this.checkCloudDeviceItem.game);
          this.setCheckCloudDeviceItem(this.checkCloudDeviceItem);
          this.toPage('CloudHangupInterface', {
            receiveData: this.checkCloudDeviceItem.game,
            checkCloudDeviceItem: this.checkCloudDeviceItem,
          });
        }
      } else {
        this.$toast.loading(this.$t('网络不好请稍后重试'));
      }
    },
    // 云挂机
    async receiveDataFromChild(data) {
      // 用户无设备&& 无后台云设备
      if (!this.available_devices && !this.cloudDeviceList.length) {
        // this.$toast(`${this.btn_text}`)
        this.$toast('暂时没有可购买的设备了');
        return;
      }

      // 用户无设备
      if (!this.cloudDeviceList.length) {
        this.devicePayPopupShow = true;
        return;
      }

      // 用户设备过期
      if (
        this.checkCloudDeviceItem &&
        this.checkCloudDeviceItem?.expire_code == 2
      ) {
        this.devicePayPopupShow = true;
        return;
      }
      this.receiveData = data;
      if (data && this.selectedId !== null) {
        this.cloudGamePopupShow = false;
        if (
          JSON.stringify(this.receiveDataInfo) !==
          JSON.stringify(this.receiveData)
        ) {
          this.setReceiveData(data);
        }
        if (
          JSON.stringify(this.checkCloudDeviceItemInfo) !==
          JSON.stringify(this.checkCloudDeviceItem)
        ) {
          this.setCheckCloudDeviceItem(this.checkCloudDeviceItem);
        }
        this.toPage('CloudHangupInterface', {
          receiveData: data,
          checkCloudDeviceItem: this.checkCloudDeviceItem,
        });
      }
    },
    // 卸载云挂机游戏
    async removedInsGame(data) {
      // 用户设备过期
      if (
        this.checkCloudDeviceItem &&
        this.checkCloudDeviceItem?.expire_code == 2
      ) {
        this.devicePayPopupShow = true;
        return;
      }
      const res = await ApiCloudUninstallApp({
        equip_id: this.checkCloudDeviceItem.id,
        app_id: data.id,
      });
      if (res.code == 1) {
        this.updateTrigger += 1; // 修改 updateTrigger 触发子组件更新
      }
      // this.cloudGamePopupShow = false;
    },
    ...mapMutations({
      setReceiveData: 'cloud_hangup/setReceiveData',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
      setCloudType: 'system/setCloudType',
    }),
  },
  computed: {
    inputClearShow() {
      return this.modifyName.length ? true : false;
    },
    obtainSelectedId() {
      return this.checkCloudDeviceItem ? this.checkCloudDeviceItem.id : null;
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
      receiveDataInfo: 'cloud_hangup/receiveData',
      checkCloudDeviceItemInfo: 'cloud_hangup/checkCloudDeviceItem',
    }),
  },
};
</script>

<style lang="less" scoped>
html,
body {
  height: 100%;
  max-width: 450px;
  margin: 0 auto;
}
.page {
  position: relative;
  .right-FAQ {
    background: url('~@/assets/images/cloudHangup/cloud_FAQ.png') no-repeat 0 0;
    background-size: 18 * @rem 18 * @rem;
    width: 18 * @rem;
    height: 18 * @rem;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .container {
    padding: 57 * @rem 20 * @rem 20 * @rem;
    height: 100vh;
    display: flex;
    justify-content: center;
    // margin-top: calc(50 * @rem + @safeAreaTop);
    // margin-top: calc(50 * @rem + @safeAreaTopEnv);
    margin-top: @safeAreaTopEnv;
    .equipment-cloud {
      // margin-top: 57 * @rem;
      width: 335 * @rem;
      //   background: linear-gradient(220deg, #09794d 0%, #09794d 100%);
      background: #09794d;
      border-radius: 18 * @rem;
      display: flex;
      flex-direction: column;

      box-sizing: border-box;
      .cloud-box {
        background: url('~@/assets/images/cloudHangup/cloud_bg.png') no-repeat 0
          0;
        background-size: 335 * @rem 427 * @rem;
        padding: 46 * @rem 24 * @rem 0 24 * @rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .cloud-content {
          .cloud-title {
            height: 23 * @rem;
            font-size: 23 * @rem;
            color: #ffffff;
            line-height: 23 * @rem;
            text-shadow: 0 * @rem 1 * @rem 2 * @rem rgba(0, 107, 43, 0.27);
            text-align: center;
            font-style: normal;
            text-transform: none;
            text-align: center;
          }
          .cloud-describe {
            margin-top: 14 * @rem;
            height: 14 * @rem;
            font-size: 14 * @rem;
            color: rgba(244, 255, 249, 0.85);
            line-height: 14 * @rem;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
          .cloud-function {
            text-align: center;
            margin-top: 30 * @rem;
            height: 13 * @rem;
            line-height: 13 * @rem;
            font-size: 13 * @rem;
            color: #f4fff9;
            text-align: center;
            font-style: normal;
            text-transform: none;
            position: relative;
            &::before,
            &::after {
              position: absolute;
              content: '';
              display: block;

              height: 7 * @rem;
              line-height: 7 * @rem;
              top: 50%;
              transform: translateY(-50%);
            }

            &::before {
              background: url('~@/assets/images/cloudHangup/left-func.png')
                no-repeat -5 * @rem 0;

              background-size: 121 * @rem 7 * @rem;
              width: 116 * @rem;
              left: calc(50% - 150 * @rem);
            }
            &::after {
              background: url('~@/assets/images/cloudHangup/right-func.png')
                no-repeat 0 0;
              background-size: 121 * @rem 7 * @rem;
              width: 116 * @rem;
              right: calc(50% - 150 * @rem);
            }
          }
          .cloud-trait {
            width: 100%;

            .trait-item {
              margin-top: 18 * @rem;
              display: flex;
              align-items: center;
              padding: 15 * @rem 0 15 * @rem 25 * @rem;
              height: 70 * @rem;
              background: rgba(244, 255, 249, 0.12);
              border-radius: 12 * @rem;
              border: 1 * @rem solid rgba(255, 255, 255, 0.4);
              box-sizing: border-box;
              &:not(:first-child) {
                margin-top: 16 * @rem;
              }
              .item-logo {
                width: 40 * @rem;
                height: 40 * @rem;
              }
              .item-describe {
                height: 100%;
                margin-left: 16 * @rem;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                align-items: flex-start;
                .item-describe-first {
                  height: 16 * @rem;
                  font-size: 16 * @rem;
                  font-weight: bold;
                  color: #ffffff;
                  line-height: 16 * @rem;
                  text-shadow: 0 * @rem 0 * @rem 2 * @rem rgba(0, 107, 43, 0.27);
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                }
                .item-describe-second {
                  font-weight: bold;
                  height: 11 * @rem;
                  font-size: 11 * @rem;
                  color: rgba(244, 255, 249, 0.85);
                  line-height: 11 * @rem;
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                }
              }
            }
          }
        }
        .cloud-btn {
          // margin-top: 169 * @rem;
          .trial-btn {
            text-align: center;
            height: 38 * @rem;
            line-height: 38 * @rem;
            background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%),
              #08c26c;
            border-radius: 29 * @rem;
            font-size: 15 * @rem;
            color: #ffffff;
            &.out {
              background: #005939;
            }
          }
          .trial-text {
            margin: 16 * @rem 0 34 * @rem 0;
            height: 12 * @rem;
            font-size: 12 * @rem;
            color: rgba(244, 255, 249, 0.85);
            line-height: 12 * @rem;
            text-align: center;
          }
        }
      }
    }
    .cloud-box-onHook {
      display: flex;
      flex-direction: column;
      // margin-top: 57 * @rem;
      width: 335 * @rem;
      border-radius: 18 * @rem;
      box-sizing: border-box;
      .cloud-box-onHook-num {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6 * @rem;
        .number {
          height: 15 * @rem;
          font-size: 15 * @rem;
          color: #222222;
          line-height: 15 * @rem;
        }
        .name {
          display: flex;
          align-items: center;
          position: relative;
          > div {
            display: flex;
            align-items: center;
            .title {
              height: 15 * @rem;
              font-size: 15 * @rem;
              color: #222222;
              line-height: 15 * @rem;
              text-align: center;
            }
            .logo {
              width: 24 * @rem;
              height: 24 * @rem;
              transition: all 0.3s ease;
              &.active {
                transform: rotate(-180deg);
              }
            }
          }
          .slide-fade-enter-active,
          .slide-fade-leave-active {
            transition: all 0.1s ease;
            overflow: hidden;
          }

          .slide-fade-enter,
          .slide-fade-leave-to {
            height: 0;
            opacity: 0;
          }
          .select-list {
            position: absolute;
            top: 30 * @rem;
            right: -5 * @rem;
            // width: 78 * @rem;
            z-index: 2;
            .select-list-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-start;
              // overflow: hidden;
              position: relative;
              max-height: 322 * @rem;
              overflow-y: auto;
              .select-list-title {
                position: relative;
                min-width: 78 * @rem;
                width: 100%;
                min-height: 46 * @rem;
                line-height: 46 * @rem;
                text-align: center;
                background: #fff;
                overflow: hidden;
                white-space: nowrap;
                // box-shadow: 0 0 10 * @rem 0 rgba(0, 0, 0, 0.2);
                &:first-of-type {
                  border-top-left-radius: 10 * @rem 10 * @rem;
                  border-top-right-radius: 10 * @rem 10 * @rem;
                }
                &:last-of-type {
                  border-bottom-right-radius: 10 * @rem 10 * @rem;
                  border-bottom-left-radius: 10 * @rem 10 * @rem;
                }
                &:not(:first-of-type)::before {
                  content: '';
                  position: absolute;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 60 * @rem;
                  height: 0;
                  border: 1 * @rem solid #d9d9d9;
                }
                &.active {
                  background: #22ae6b;
                  color: #fff;
                  &::before {
                    display: none;
                  }
                  &.active + .select-list-title::before {
                    display: none;
                  }
                }
              }
            }
            &::before {
              content: '';
              width: 31 * @rem;
              height: 14 * @rem;
              background: url(~@/assets/images/recharge/buy-cloud-devices/triangle.png)
                no-repeat center center;
              background-size: 31 * @rem 14 * @rem;
              position: absolute;
              top: -14 * @rem;
              left: 50%;
              transform: translateX(-50%);
              z-index: 2;
            }
          }
        }
      }
      .cloud-box-onHook-content {
        width: 335 * @rem;
        min-height: 581 * @rem;
        flex: 1;
        background: rgba(0, 0, 0, 0.46);
        border-radius: 18 * @rem;
        position: relative;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        .onHook-content-item {
          height: 100%;
          box-sizing: border-box;
          background: rgba(0, 0, 0, 0.46);
          overflow: hidden;
          border-radius: 18 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .item-info {
            padding: 24 * @rem 25 * @rem;
            background: rgba(0, 0, 0, 0.2);
            // border: 1px solid rgba(255, 255, 255, 0.21);
            .item-name {
              display: flex;
              align-items: center;
              .item-name-title {
                height: 18 * @rem;
                font-size: 18 * @rem;
                color: #ffffff;
                line-height: 18 * @rem;
                text-shadow: 0 1 * @rem 2 * @rem rgba(0, 107, 43, 0.27);
                text-align: center;
              }
              .item-name-modify {
                margin-left: 6 * @rem;
                width: 18 * @rem;
                height: 18 * @rem;
              }
            }
            .item-time,
            .item-game-name {
              margin-top: 10 * @rem;
              height: 14 * @rem;
              font-size: 14 * @rem;
              color: rgba(244, 255, 249, 0.85);
              line-height: 14 * @rem;
              text-align: left;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              > span {
                margin-left: 5 * @rem;
                display: inline-block;
                width: 200 * @rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .item-status {
              margin-top: 12 * @rem;
              width: 80 * @rem;
              height: 24 * @rem;
              line-height: 24 * @rem;
              border-radius: 5 * @rem;
              font-size: 14 * @rem;

              text-align: center;
              &.gj {
                border: 1px solid #03d272;
                color: #03d272;
                background: rgba(32, 32, 32, 0.5);
              }
              &.xzz {
                color: #1c84ff;
                background: rgba(255, 255, 255, 0.9);
              }
              &.ygq {
                color: #ffffff;
                background: #ff4c4c;
              }
              &.jzz {
                color: #03d272;
                background: rgba(32, 32, 32, 0.5);
              }
              &.zzgx {
                color: #ffffff;
                background: #ff9c28;
              }
            }
            &.active {
              background: rgba(0, 0, 0, 0.77);
            }
          }
          &.default {
            background: url('~@/assets/images/cloudHangup/cloud_bg.png')
              no-repeat 0 0;
            background-size: 335 * @rem 427 * @rem;
          }
        }
        .no-games-box {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          .box-logo {
            height: 96 * @rem;
            width: 96 * @rem;
          }
          .box-text1 {
            margin: 6 * @rem 0 12 * @rem 0;
            height: 24 * @rem;
            font-size: 24 * @rem;
            color: rgba(244, 255, 249, 0.6);
            line-height: 24 * @rem;
            text-align: center;
          }
          .box-text2 {
            height: 12 * @rem;
            font-size: 12 * @rem;
            color: #ffffff;
            line-height: 12 * @rem;
            text-align: center;
          }
          .box-text3 {
            font-size: 18 * @rem;
            color: #fff;
            padding: 10 * @rem;
          }
        }
        .item-btn {
          // position: absolute;
          // left: 0;
          // bottom: 0;
          width: 100%;
          height: 56 * @rem;
          background: rgba(0, 0, 0, 0.2);
          border: 1 * @rem solid rgba(255, 255, 255, 0.21);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .item-btn-nr {
            flex: 1;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            &:not(:first-of-type)::before {
              content: '';
              position: absolute;
              left: 0;
              width: 1 * @rem;
              height: 32 * @rem;
              background: rgba(255, 255, 255, 0.35);
              top: 50%;
              transform: translateY(-50%);
            }
            > span:first-of-type {
              width: 14 * @rem;
              height: 14 * @rem;
            }
            > span:last-of-type {
              margin-left: 3 * @rem;
              height: 14 * @rem;
              font-size: 13 * @rem;
              color: #ffffff;
              line-height: 14 * @rem;
              text-align: center;
            }
          }
          &.active {
            background: rgba(0, 0, 0, 0.77);
          }
        }
      }
      .cloud-box-onHook-btn {
        margin: 16 * @rem 0 0 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .trial-btn {
          text-align: center;
          width: 247 * @rem;
          height: 38 * @rem;
          line-height: 38 * @rem;
          background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%), #08c26c;
          border-radius: 29 * @rem;
          font-size: 15 * @rem;
          color: #ffffff;
        }
      }
    }
  }

  .onhook-popup {
    box-sizing: border-box;
    width: 300 * @rem;
    padding: 20 * @rem 15 * @rem 25 * @rem 15 * @rem;
    background-size: 300 * @rem auto;
    border-radius: 20 * @rem;
    z-index: 2996 !important;
    .title {
      text-align: center;
      font-weight: bold;
    }
    .content {
      line-height: 17 * @rem;
      font-size: 14 * @rem;
      color: #777777;
      padding: 0 25 * @rem;
      margin-top: 22 * @rem;
    }
    .btn-info {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin: 12 * @rem 0 0;
      .cancel,
      .confirm {
        width: 126 * @rem;
        height: 40 * @rem;
        border-radius: 30 * @rem;

        font-size: 13 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cancel {
        color: #7d7d7d;
        background: #f2f2f2;
      }
      .confirm {
        margin-left: 17 * @rem;
        color: #ffffff;
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      }
    }

    .tips {
      display: flex;
      align-items: center;
      margin: 10 * @rem auto 0;
      padding: 0 25 * @rem;
      .gou {
        width: 12 * @rem;
        height: 12 * @rem;
        border-radius: 12 * @rem;
        border: 1px solid #7d7d7d;
        &.remember {
          background: url('~@/assets/images/cloudHangup/bzts-img.png') no-repeat
            0 0;
          background-size: 14 * @rem 14 * @rem;
          width: 14 * @rem;
          height: 14 * @rem;
          border: none;
        }
      }
      .tips-text {
        font-size: 12 * @rem;
        color: #999999;
        margin-left: 6 * @rem;
      }
    }
  }
  .vant-popup {
    border-radius: 24 * @rem 24 * @rem 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .popup-title {
      width: 375 * @rem;
      height: 61 * @rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 18 * @rem;
      box-sizing: border-box;
      font-size: 16 * @rem;
      color: #333333;
      font-weight: bold;
    }
    .popup-text {
      padding: 0 18 * @rem;
      box-sizing: border-box;
      font-size: 14 * @rem;
      color: #333333;
      text-align: justify;
    }
    .game-list-search {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 0 18 * @rem;
      box-sizing: border-box;
      .search-bar {
        box-sizing: border-box;
        padding: 0 16 * @rem 0 16 * @rem;
        width: 100%;
        height: 33 * @rem;
        border-radius: 17 * @rem;
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        .search-input {
          flex: 1;
          height: 33 * @rem;
          margin-left: 7 * @rem;
          form {
            border: 0;
            outline: 0;
            display: block;
            width: 100%;
            height: 100%;
          }
          input {
            border: 0;
            outline: 0;
            display: block;
            width: 100%;
            height: 100%;
            background-color: transparent;
            font-size: 14 * @rem;
            color: #333;
          }
        }
        .input-clear {
          width: 18 * @rem;
          height: 18 * @rem;
          .image-bg('~@/assets/images/input-clear.png');
          margin: 0 12 * @rem;
        }
      }
    }
    .popup-btn {
      padding: 0 18 * @rem;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .cancel,
      .exit {
        margin: 36 * @rem 0;
        width: 126 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        border-radius: 30 * @rem;
        font-size: 15 * @rem;
        text-align: center;
      }
      .cancel {
        background: #f2f2f2;
        color: #7d7d7d;
      }
      .exit {
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
        color: #ffffff;
        &.disable-btn {
          background: #c1c1c1;
        }
      }
    }
  }
}
.vdr.active:before {
  outline: none;
}
</style>
