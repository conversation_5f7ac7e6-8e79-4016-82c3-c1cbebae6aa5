import {
  ApiUserInfo,
  ApiUserRegressionList,
  ApiUserSubmitUserInfo,
  ApiUserCheckBirthday,
  ApiUserGetVipWx,
} from '@/api/views/users.js';
import { ApiIndexAdAndCoupon } from '@/api/views/home.js';
import { ApiCouponIsReceiveFirstC } from '@/api/views/coupon.js';
import { setLanguage } from '@/utils/function.js';
import router from '@/router';
import { Dialog } from 'vant';
import { getToken } from '@/utils/box.uni.js';

export default {
  state: {
    userInfo: {},
    userInfoEx: {},
    areaCode: 86, // 手机区号
    showFirstCouponIcon: true, // 首充福利弹窗的icon
    showLCPopup: false, //实名认证弹窗Popup
    showAdPopup: false, //游戏广告弹窗Popup
    showChannelAdPopup: false, //渠道游戏广告弹窗Popup
    showFCPopup: false, //首充福利弹窗Popup
    showRegressionPopup: false, //回归奖励弹窗Popup
    showBirthdayPopup: false, //用户生日弹窗Popup
    adData: {}, //游戏广告数据
    channelAdData: {}, //渠道游戏广告数据
    regressionData: {}, // 回归奖励数据
    birthdayData: '', // 用户生日数据
    vipWxList: [], //vip微信客服号列表
    unReadMesNumber: 0, //未读消息数量
    userDownload: [], //安卓用户当前下载列表
  },
  mutations: {
    setUserInfo(state, userInfo) {
      if (!userInfo) {
        state.userInfo = {};
      } else {
        state.userInfo = userInfo;
      }
    },
    freshAndroidToken(state) {
      state.userInfo.token = getToken();
    },
    setUserInfoEx(state, userInfoEx) {
      if (!userInfoEx) {
        state.userInfoEx = {};
      } else {
        state.userInfoEx = userInfoEx;
      }
    },
    setAreaCode(state, areaCode) {
      state.areaCode = areaCode || 86;
    },
    setShowFirstCouponIcon(state, showFirstCouponIcon) {
      state.showFirstCouponIcon = showFirstCouponIcon;
    },
    setShowChannelAdPopup(state, showChannelAdPopup) {
      if (showChannelAdPopup) {
        this.commit('user/setShowAdPopup', false);
        this.commit('user/setShowFCPopup', false);
        this.commit('user/setShowLCPopup', false);
        // this.commit('user/setShowRegressionPopup', false);
      }
      state.showChannelAdPopup = showChannelAdPopup;
    },
    setShowAdPopup(state, showAdPopup) {
      if (showAdPopup) {
        if (!this.getters['user/showChannelAdPopup']) {
          state.showAdPopup = showAdPopup;
          this.commit('user/setShowFCPopup', false);
          this.commit('user/setShowLCPopup', false);
          // this.commit('user/setShowRegressionPopup', false);
        }
      } else {
        state.showAdPopup = showAdPopup;
      }
    },
    setShowFCPopup(state, showFCPopup) {
      if (showFCPopup) {
        if (
          !this.getters['user/showChannelAdPopup'] &&
          !this.getters['user/showAdPopup']
        ) {
          state.showFCPopup = showFCPopup;
          this.commit('user/setShowLCPopup', false);
          // this.commit('user/setShowRegressionPopup', false);
        }
      } else {
        state.showFCPopup = showFCPopup;
      }
    },
    setShowLCPopup(state, showLCPopup) {
      if (showLCPopup) {
        // 全局弹窗需要在首页多一个判断
        if (router.history.current.name === 'QualitySelect') {
          if (
            this.getters['user/showChannelAdPopup'] ||
            this.getters['user/showAdPopup'] ||
            this.getters['user/showFCPopup']
          ) {
            return false;
          }
          state.showLCPopup = showLCPopup;
          // this.commit('user/setShowRegressionPopup', false);
        } else {
          state.showLCPopup = showLCPopup;
          // this.commit('user/setShowRegressionPopup', false);
        }
      } else {
        state.showLCPopup = showLCPopup;
      }
    },
    setShowRegressionPopup(state, showRegressionPopup) {
      // if (showRegressionPopup) {
      // 全局弹窗需要在首页多一个判断
      // 	if (router.history.current.name === 'QualitySelect') {
      // 		if (this.getters['user/showChannelAdPopup'] || this.getters['user/showAdPopup'] || this.getters['user/showFCPopup'] || this.getters['user/showLCPopup']) {
      // 			return false;
      // 		}
      // 		state.showRegressionPopup = showRegressionPopup;
      // 	} else {
      // 		state.showRegressionPopup = showRegressionPopup;
      // 	}
      // } else {
      state.showRegressionPopup = showRegressionPopup;
      // }
    },
    setShowBirthdayPopup(state, showBirthdayPopup) {
      state.showBirthdayPopup = showBirthdayPopup;
    },
    setAdData(state, adData) {
      state.adData = adData;
    },
    setChannelAdData(state, channelAdData) {
      state.channelAdData = channelAdData;
    },
    setRegressionData(state, payload) {
      state.regressionData = payload;
    },
    setBirthdayData(state, payload) {
      state.birthdayData = payload;
    },
    setVipWxList(state, payload) {
      state.vipWxList = payload;
    },
    setUnReadMesNumber(state, payload) {
      state.unReadMesNumber = payload;
    },
    setUserDownload(state, payload) {
      state.userDownload = payload;
    },
  },
  getters: {
    userInfo(state) {
      return state.userInfo;
    },
    userInfoEx(state) {
      return state.userInfoEx;
    },

    areaCode(state) {
      return state.areaCode;
    },

    showFirstCouponIcon(state) {
      return state.showFirstCouponIcon;
    },
    showAdPopup(state) {
      return state.showAdPopup;
    },
    showChannelAdPopup(state) {
      return state.showChannelAdPopup;
    },
    showLCPopup(state) {
      return state.showLCPopup;
    },
    showFCPopup(state) {
      return state.showFCPopup;
    },
    showRegressionPopup(state) {
      return state.showRegressionPopup;
    },
    showBirthdayPopup(state) {
      return state.showBirthdayPopup;
    },
    adData(state) {
      return state.adData;
    },
    channelAdData(state) {
      return state.channelAdData;
    },
    regressionData(state) {
      return state.regressionData;
    },
    birthdayData(state) {
      return state.birthdayData;
    },
    vipWxList(state) {
      return state.vipWxList;
    },
    unReadMesNumber(state) {
      return state.unReadMesNumber;
    },
    userDownload(state) {
      return state.userDownload;
    },
  },
  actions: {
    // returnErr 默认不发出未登录提醒
    async SET_USER_INFO({ commit, rootState }, returnErr = false) {
      const res = await ApiUserInfo({ returnErr });
      commit('setUserInfo', res.data);

      // 设置语言
      setLanguage();

      localStorage.setItem('STORE', JSON.stringify(rootState));

      // 没有密码/密保的时候的弹窗 启动时只展示一次
      const security_pwd_msg = res.data?.security_pwd_msg;
      if (security_pwd_msg) {
        Dialog.alert({
          message: security_pwd_msg,
          allowHtml: true,
          confirmButtonText: '我知道了',
          confirmButtonColor: '@themeColor',
        });
        sessionStorage.setItem('NO_PASSWORD_HW_TIP', true);
      }
    },

    async SET_REGRESSION_POPUP({ commit }) {
      const res = await ApiUserRegressionList();
      let { is_regression } = res.data;
      commit('setRegressionData', res.data);
      let regressionDate = localStorage.getItem('regressionDate');
      let today = new Date().getDate();
      switch (res.data.pop_type) {
        case 1: // 一天一次
          if (today != regressionDate) {
            commit('setShowRegressionPopup', is_regression);
            localStorage.setItem('regressionDate', today);
          }
          break;
        case 2: // 每次启动
          commit('setShowRegressionPopup', is_regression);
          break;
        case 3: // 仅一次
          if (data.update_time != regressionDate) {
            commit('setShowRegressionPopup', is_regression);
            localStorage.setItem('regressionDate', data.update_time);
          }
          break;
        default:
          break;
      }
    },

    async SET_BIRTHDAY_POPUP({ commit }, is_close = 0) {
      const res = await ApiUserCheckBirthday({ is_close });
      let { content, is_birth } = res.data;
      commit('setBirthdayData', content);
      commit('setShowBirthdayPopup', is_birth == -1 ? false : true);
    },

    async SET_FIRST_COUPON_ICON({ commit, getters }) {
      if (!getters.userInfo.token) {
        commit('setShowFirstCouponIcon', true);
      } else {
        const res = await ApiCouponIsReceiveFirstC();
        let { is_receive } = res.data;
        commit('setShowFirstCouponIcon', !is_receive);
      }
    },
    async GET_VIP_WX({ commit, getters }) {
      try {
        const res = await ApiUserGetVipWx({
          pay_level: getters.userInfo.pay_level,
        });
        commit('setVipWxList', res.data.weixin_list);
      } catch { }
    },

    // 渠道游戏广告数据，从initData中拿，要保证initData先获取到再拿
    async SET_CHANNEL_AD_POPUP({ commit }) {
      const data = this.getters['system/configs'].channel_ad_info || {};
      const show = this.getters['system/configs'].channel_show_ad || 0;
      if (!show) {
        commit('setShowChannelAdPopup', false);
        return false;
      }
      commit('setChannelAdData', data);
      if (data.pop_type) {
        let channelAdDate = localStorage.getItem('CHANNEL_AD_DATE');
        let today = new Date().getDate();
        switch (data.pop_type) {
          case 1: // 一天一次
            if (today != channelAdDate) {
              commit('setShowChannelAdPopup', true);
              localStorage.setItem('CHANNEL_AD_DATE', today);
            }
            break;
          case 2: // 每次启动
            commit('setShowChannelAdPopup', true);
            break;
          case 3: // 仅一次
            if (data.update_time != channelAdDate) {
              commit('setShowChannelAdPopup', true);
              localStorage.setItem('CHANNEL_AD_DATE', data.update_time);
            }
            break;
          default:
            break;
        }
      }
    },
    async SET_ADANDCOUPON_POPUP({ commit }) {
      const res = await ApiIndexAdAndCoupon();
      const data = res.data.pop_ad || {};
      commit('setAdData', data);
      if (data.pop_type) {
        let adDate = localStorage.getItem('AD_DATE');
        let today = new Date().getDate();
        switch (data.pop_type) {
          case 1: // 一天一次
            if (today != adDate) {
              commit('setShowAdPopup', true);
              localStorage.setItem('AD_DATE', today);
            }
            break;
          case 2: // 每次启动
            commit('setShowAdPopup', true);
            break;
          case 3: // 仅一次
            if (data.update_time != adDate) {
              commit('setShowAdPopup', true);
              localStorage.setItem('AD_DATE', data.update_time);
            }
            break;
          default:
            break;
        }
      }
    },
  },
};
