export default [
  {
    path: '/kefu',
    name: 'Ke<PERSON>',
    component: () => import(/* webpackChunkName: "kefu" */ '@/views/Kefu'),
    meta: {
      keepAlive: false,
      requiresAuth: false,
      pageTitle: '客服中心'
    },
  },
  {
    path: '/kefu_chat',
    name: 'KefuChat',
    component: () =>
      import(/* webpackChunkName: "kefu" */ '@/views/Kefu/KefuChat'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/kefu_qa/:id',
    name: 'KefuQA',
    component: () =>
      import(/* webpackChunkName: "kefu" */ '@/views/Kefu/KefuQA'),
    meta: {
      keepAlive: false,
      requiresAuth: false,
    },
  },
  {
    path: '/question_detail/:id',
    name: 'QuestionDetail',
    component: () =>
      import(/* webpackChunkName: "kefu" */ '@/views/Kefu/QuestionDetail'),
    meta: {
      keepAlive: false,
      requiresAuth: false,
    },
  },
  {
    path: '/bind_we_chat',
    name: 'BindWeChat',
    component: () =>
      import(/* webpackChunkName: "kefu" */ '@/views/Kefu/BindWeChat'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
];
