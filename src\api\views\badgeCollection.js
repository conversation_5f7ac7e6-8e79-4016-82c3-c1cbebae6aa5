import { request } from '../index';

/**
 * 徽章 - 徽章首页
 */
export function ApiUserBadgeBadge(params = {}) {
    return request('/api/user_badge/badge', params);
}

/**
 * 徽章 - 获取徽章记录
* @param {page} 
 * @param {listRows} 
 */
export function ApiUserBadgeMyBadgeLog(params = {}) {
    return request('/api/user_badge/my_badge_log', params);
}

/**
 * 徽章 - 徽章详情
 * @param {type_id} 类型ID
 * @param {record_id} 记录id
 */
export function ApiUserBadgeBadgeInfo(params = {}) {
    return request('/api/user_badge/badge_info', params);
}

/**
 * 徽章 - 徽章H5分享
 * @param {id} 徽章id
 * @param {share_type} //14详情 15列表
 * @param {record_id} 30 share_type为14必填
 * @param {listRows} 30 默认值
 * @param {mem_id} 用户id share_type为15必填
 */
export function ApiUserBadgeShare(params = {}) {
    return request('/api/user_badge/share', params);
}

/**
 * 徽章 - 徽章佩戴
 * @param {record_id} 记录ID
 * @param {type} 1 佩戴  2 取消佩戴
 */
export function ApiUserBadgeWear(params = {}) {
    return request('/api/user_badge/wear', params);
}