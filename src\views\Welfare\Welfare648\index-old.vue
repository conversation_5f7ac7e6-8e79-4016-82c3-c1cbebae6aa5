<template>
  <div class="welfare-page page">
    <nav-bar-2
      :title="'648福利'"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :bgStyle="bgStyle"
    >
    </nav-bar-2>
    <div class="ruler-btn" @click="rulerPopupShow = true">规则</div>
    <div
      class="main"
      :style="{
        backgroundImage: `url(${topInfo.configs.titleimg})`,
        backgroundColor: topInfo.configs.bg_color,
      }"
    >
      <div
        class="top-info"
        :style="{ backgroundImage: `url(${topInfo.configs.get_img})` }"
      >
        <div class="get-number">今日已有{{ topInfo.num }}人领</div>
        <div class="get-swiper">
          <swiper :options="getSwiperOption">
            <swiper-slide
              class="swiper-slide"
              v-for="(item, index) in topInfo.list"
              :key="index"
            >
              <div
                class="info"
                :style="{ color: topInfo.configs.get_time_color }"
              >
                <span :style="{ color: topInfo.configs.get_game_color }">{{
                  item.nickname
                }}</span>
                领取了
                <span :style="{ color: topInfo.configs.get_game_color }">{{
                  item.game_title
                }}</span>
                648充值卡
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="coupon-box">
        <div class="title"></div>
        <div class="loading-box" v-if="couponLoading">
          <van-loading />
        </div>
        <div
          class="coupon-swiper"
          :class="{ one: couponList.length == 1 }"
          v-else
        >
          <template v-if="couponList.length && couponList[0].length">
            <swiper :options="couponSwiperOption">
              <swiper-slide
                class="swiper-slide"
                v-for="(item, index) in couponList"
                :key="index"
              >
                <div class="list">
                  <div
                    class="item"
                    v-for="(coupon, index1) in item"
                    :key="index1"
                  >
                    <div class="item-top">
                      <game-item-4
                        :gameInfo="coupon.game"
                        :iconSize="56"
                        :showHot="false"
                      ></game-item-4>
                    </div>
                    <div class="item-bottom">
                      <div class="residue">
                        <div class="value"
                          ><span>¥</span>{{ coupon.money }}</div
                        >
                        <div class="price">({{ coupon.use_condition }})</div>
                      </div>
                      <div class="get-btn" @click.stop="takeCoupon(coupon)"
                        >领取</div
                      >
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
            <div class="swiper-pagination pagination3"></div>
          </template>
          <content-empty v-else></content-empty>
        </div>
      </div>
      <div class="main-card-box">
        <div class="title"></div>
        <div class="loading-box" v-if="cardLoading">
          <van-loading />
        </div>
        <div class="card-content" v-else>
          <div class="card-swiper" :class="{ one: cardList.length == 1 }">
            <template v-if="cardList.length && cardList[0].length">
              <swiper :options="cardSwiperOption1">
                <swiper-slide
                  class="swiper-slide"
                  v-for="(item, index) in cardList"
                  :key="index"
                >
                  <div class="list">
                    <div
                      class="card-item"
                      v-for="(card, index1) in item"
                      :key="index1"
                    >
                      <div class="card-top">
                        <game-item-4
                          :gameInfo="card.game"
                          :iconSize="56"
                          :showHot="false"
                        ></game-item-4>
                      </div>
                      <div class="card-bottom">
                        <div class="value"
                          ><span>{{ card.card_type_text }}</span></div
                        >
                        <div class="get-btn" @click="take648card(card)"
                          >领取</div
                        >
                      </div>
                      <div class="card-body">
                        {{ card.cardbody }}
                      </div>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
              <div class="swiper-pagination pagination1"></div>
            </template>
            <content-empty v-else></content-empty>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动规则 -->
    <van-dialog
      v-model="rulerPopupShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="popup-box"
    >
      <div class="content-box">
        <div class="title">活动规则</div>
        <div class="cont" v-html="topInfo.ruleText"></div>
        <div class="close-btn" @click="rulerPopupShow = false">关闭</div>
      </div>
    </van-dialog>

    <!-- 礼包领取结果弹窗 -->
    <cardpass-copy-popup
      :show.sync="cardCopyPopupShow"
      :info="cardSelected"
      :autoXh="1"
    ></cardpass-copy-popup>

    <!-- 小号选择弹窗 -->
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="Number(selectCoupon.game_id)"
      :autoXh="1"
      @onSelectSuccess="getCoupon"
    ></yy-xh-select>
    <!-- 代金券领取成功弹窗 -->
    <coupon-get-popup :info="selectCoupon" :show.sync="getCouponShow">
    </coupon-get-popup>
  </div>
</template>

<script>
import {
  ApiCardGet648CardList,
  ApiCardGetMemGet648CardLog,
  ApiCardGet,
} from '@/api/views/gift.js';
import {
  ApiGetSpecialOrderCouponList,
  ApiCouponTake,
} from '@/api/views/coupon.js';
import yyXhSelect from '@/components/yy-xh-select/index.vue';
import couponGetPopup from '@/components/coupon-get-popup/index.vue';
export default {
  components: {
    yyXhSelect,
    couponGetPopup,
  },
  data() {
    return {
      topInfo: {
        num: 0,
        list: {},
        ruleText: '',
        configs: {},
      },
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      rulerPopupShow: false,
      getSwiperOption: {
        slidesPerView: 1,
        direction: 'vertical',
        loop: true,
        autoplay: true,
        allowTouchMove: false,
      },
      cardSwiperOption1: {
        slidesPerView: 'auto',
        observer: true,
        observerSlideChildren: true,
        observerParents: true,
        pagination: {
          el: '.pagination1',
        },
      },
      couponSwiperOption: {
        slidesPerView: 'auto',
        observer: true,
        observerSlideChildren: true,
        observerParents: true,
        pagination: {
          el: '.pagination3',
        },
      },
      cardList: [],
      myGameList: [],
      couponList: [],
      cardSelected: {},
      cardCopyPopupShow: false,
      cardLoading: false,
      couponLoading: false,
      xhDialogShow: false,
      selectCoupon: {},
      getCouponShow: false, //领取成功代金券弹窗
    };
  },
  async created() {
    this.getMemberList();
    this.get648CardList();
    this.getCouponList();
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    async getMemberList() {
      const res = await ApiCardGetMemGet648CardLog();
      if (res.data.num > 9999) {
        this.topInfo.num = res.data.num.tofixed(1) + 'W';
      } else {
        this.topInfo.num = res.data.num;
      }
      this.topInfo.list = res.data.list;
      this.topInfo.ruleText = res.data.rule_text.replaceAll('\n', '<br>');
      this.topInfo.configs = res.data.configs;
    },
    async get648CardList() {
      this.cardList = [];
      this.cardLoading = true;
      try {
        const res = await ApiCardGet648CardList();
        let list = [...res.data.hot_game_card];
        if (list.length > 3) {
          let arr = [];
          list.forEach((item, index) => {
            arr.push(item);
            if (index == list.length - 1) {
              this.cardList.push(arr);
              return false;
            }
            if ((index + 1) % 3 == 0) {
              this.cardList.push(arr);
              arr = [];
            }
          });
        } else {
          this.cardList.push(list);
          this.cardSwiperOption1.loop = false;
        }
      } finally {
        this.cardLoading = false;
      }
    },
    async take648card(item) {
      this.$toast.loading('加载中');
      if (!item.cardpass) {
        const res = await ApiCardGet({
          cardId: item.id,
          autoXh: 1,
        });
        this.cardSelected = res.data;

        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${item.game_id}`,
          adv_id: '暂无',
          game_name: item.titlegame,
          game_type: `${item.classid}`,
          game_size: '暂无',
          reward_type: item.title, // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });

        await this.get648CardList();
      } else {
        this.cardSelected = item;
      }
      this.$toast.clear();
      this.$nextTick(() => {
        this.cardCopyPopupShow = true;
      });
    },
    async getCouponList() {
      this.couponList = [];
      this.couponLoading = true;
      try {
        const res = await ApiGetSpecialOrderCouponList({
          is_list: 1,
          is_648: 1,
          type: 0,
          listRows: 12,
        });
        let list = [...res.data.coupon_list];
        if (list.length > 3) {
          let arr = [];
          list.forEach((item, index) => {
            arr.push(item);
            if (index == list.length - 1) {
              this.couponList.push(arr);
              return false;
            }
            if ((index + 1) % 3 == 0) {
              this.couponList.push(arr);
              arr = [];
            }
          });
        } else {
          this.couponList.push(list);
        }
      } finally {
        this.couponLoading = false;
      }
    },
    async takeCoupon(item) {
      this.selectCoupon = item;
      this.xhDialogShow = true;
    },
    async getCoupon(xhInfo) {
      this.xhDialogShow = false;
      const res = await ApiCouponTake({
        couponId: this.selectCoupon.id,
        xhId: xhInfo.xhId,
      });
      this.$toast.clear();
      this.getCouponShow = true;

      // 神策埋点
      this.$sensorsTrack('get_voucher', {
        voucher_id: `${this.selectCoupon.id}`,
        voucher_name: `${this.selectCoupon.title}`,
        voucher_amount: `${this.selectCoupon.money}`,
      });

      await this.getCouponList();
    },
  },
};
</script>

<style lang="less" scoped>
.welfare-page {
  position: relative;

  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20 * @rem;
  }

  .ruler-btn {
    width: 26 * @rem;
    height: 56 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 13 * @rem;
    line-height: 18 * @rem;
    letter-spacing: 1 * @rem;
    text-align: center;
    color: #ffffff;
    background-color: #fe6600;
    border-radius: 5 * @rem 0 0 5 * @rem;
    position: absolute;
    top: calc(39 * @rem + @safeAreaTop);
    top: calc(39 * @rem + @safeAreaTopEnv);
    right: 0;
    cursor: pointer;
  }

  .main {
    flex: 1;
    background: #fbb068 url(~@/assets/images/welfare/welfare648/top-bg2.png)
      no-repeat top center;
    background-size: 100% auto;
    padding-bottom: 18 * @rem;
  }

  .top-info {
    display: flex;
    align-items: center;
    width: 339 * @rem;
    height: 32 * @rem;
    margin: 282 * @rem 18 * @rem 68 * @rem;
    background: url(~@/assets/images/welfare/welfare648/info-bg.png) no-repeat;
    background-size: 339 * @rem 32 * @rem;

    .get-number {
      flex-shrink: 0;
      width: 115 * @rem;
      height: 32 * @rem;
      line-height: 32 * @rem;
      font-size: 11 * @rem;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      background: url(~@/assets/images/welfare/welfare648/sp-info-bg.png)
        no-repeat;
      background-size: 115 * @rem 32 * @rem;
    }

    .get-swiper {
      flex: 1;
      min-width: 0;
      padding: 0 13 * @rem;
      height: 36 * @rem;
      line-height: 36 * @rem;
      .swiper-container {
        height: 100%;
      }
      .info {
        display: block;
        line-height: 36 * @rem;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #cb3344;

        span {
          color: #c106f0;
        }
      }
    }
  }

  .main-card-box {
    width: 355 * @rem;
    min-height: 200 * @rem;
    background: #ffffff;
    border-radius: 16 * @rem;
    margin: 30 * @rem auto 0;
    padding-bottom: 20 * @rem;

    .title {
      width: 255 * @rem;
      height: 36 * @rem;
      background: url(~@/assets/images/welfare/welfare648/box2-title.png)
        no-repeat;
      background-size: 255 * @rem 36 * @rem;
      margin: 0 auto;
      position: relative;
      top: -8 * @rem;
      z-index: 1;
    }

    .card-content {
      .card-swiper {
        padding: 16 * @rem 0 17 * @rem 15 * @rem;
        position: relative;
        &.one {
          padding-right: 15 * @rem;
          padding-bottom: 0;
          .swiper-slide {
            width: 100%;
          }

          .swiper-pagination {
            display: none;
          }
        }
      }
      .swiper-slide {
        width: 306 * @rem;
        margin-right: 10 * @rem;
      }

      .card-item {
        width: 100%;
        background: #fdfdff;
        border-radius: 8 * @rem;
        border: 1 * @rem solid #f1f2fa;
        margin-bottom: 16 * @rem;
        box-sizing: border-box;

        &:last-of-type {
          margin-bottom: 0;
        }

        .card-top {
          padding: 0 10 * @rem;
        }

        .card-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 42 * @rem;
          padding: 0 10 * @rem;
          box-sizing: border-box;
          background-color: #f6f7ff;

          .value {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;

            &::before {
              flex-shrink: 0;
              content: '';
              display: block;
              width: 18 * @rem;
              height: 18 * @rem;
              background: url(~@/assets/images/welfare/welfare648/gift-icon.png)
                no-repeat;
              background-size: 18 * @rem 18 * @rem;
              margin-right: 4 * @rem;
            }

            span {
              flex: 1;
              min-width: 0;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #333333;
              line-height: 16 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .get-btn {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64 * @rem;
            height: 28 * @rem;
            background: #fe6600;
            border-radius: 23 * @rem;
            font-weight: 400;
            font-size: 13 * @rem;
            color: #ffffff;
            line-height: 16 * @rem;
            text-align: center;
          }
        }

        .card-body {
          height: 28 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #808080;
          line-height: 12 * @rem;
          padding: 11 * @rem 10 * @rem;
          overflow: hidden;
        }
      }
    }
  }

  .coupon-box {
    width: 355 * @rem;
    min-height: 200 * @rem;
    background: #ffffff;
    border-radius: 16 * @rem;
    margin: 0 auto;
    padding-bottom: 20 * @rem;

    .title {
      width: 255 * @rem;
      height: 36 * @rem;
      background: url(~@/assets/images/welfare/welfare648/box1-title.png)
        no-repeat;
      background-size: 255 * @rem 36 * @rem;
      margin: 0 auto;
      position: relative;
      top: -8 * @rem;
      z-index: 1;
    }

    .coupon-swiper {
      padding: 16 * @rem 0 17 * @rem 15 * @rem;
      position: relative;

      &.one {
        padding-right: 15 * @rem;
        padding-bottom: 0;
        .swiper-slide {
          width: 100%;
        }

        .swiper-pagination {
          display: none;
        }
      }
    }
    .swiper-slide {
      width: 306 * @rem;
      margin-right: 10 * @rem;
    }

    .item {
      display: block;
      width: 100%;
      background: #fff;
      border-radius: 8 * @rem;
      margin-bottom: 16 * @rem;
      box-sizing: border-box;
      border: 1 * @rem solid #f1f2fa;

      &:last-of-type {
        margin-bottom: 0;
      }

      .item-top {
        display: flex;
        align-items: center;
        padding: 2 * @rem 10 * @rem;
      }

      .item-bottom {
        display: flex;
        align-items: center;
        height: 42 * @rem;
        padding: 0 10 * @rem 0 16 * @rem;
        background-color: #f6f7ff;

        .get-btn {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64 * @rem;
          height: 28 * @rem;
          line-height: 28 * @rem;
          text-align: center;
          background: #fe6600;
          color: #fff;
          border-radius: 23 * @rem;
          font-size: 13 * @rem;
        }

        .residue {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;
          height: 42 * @rem;
          font-size: 13 * @rem;
          font-weight: 600;
          color: #ff6649;
          line-height: 42 * @rem;
          text-align: center;

          .value {
            display: flex;
            align-items: center;
            font-size: 18 * @rem;
            span {
              flex-shrink: 0;
              margin-right: 2 * @rem;
              font-size: 12 * @rem;
            }
          }

          .price {
            font-weight: 400;
            font-size: 11 * @rem;
            color: #666666;
            line-height: 13 * @rem;
            margin-left: 6 * @rem;
          }
        }
      }
    }
  }

  /deep/ .swiper-pagination {
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 325 * @rem;
    height: 5 * @rem;

    .swiper-pagination-bullet {
      display: block;
      width: 5 * @rem;
      height: 5 * @rem;
      margin-right: 5 * @rem;
      border-radius: 10 * @rem;

      &:last-of-type {
        margin-right: 0;
      }

      &.swiper-pagination-bullet-active {
        width: 15 * @rem;
        background-color: @themeColor;
      }
    }
  }

  .content-box {
    padding: 30 * @rem 20 * @rem 20 * @rem;

    .title {
      display: block;
      height: 20 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
    }

    .cont {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #777777;
      line-height: 25 * @rem;
      margin-top: 23 * @rem;
    }

    .close-btn {
      width: 238 * @rem;
      height: 38 * @rem;
      line-height: 38 * @rem;
      background: #fe6600;
      border-radius: 22 * @rem;
      text-align: center;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #ffffff;
      margin: 20 * @rem auto 0;
      cursor: pointer;
    }
  }
}
</style>
