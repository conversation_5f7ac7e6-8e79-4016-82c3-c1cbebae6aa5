<template>
  <div class="mm-ellipsis-container">
    <div class="shadow">
      <textarea :rows="rows" readonly></textarea>
      <div class="shadow-box" ref="box">
        <div v-html="showContent" ref="shadowContent"></div>
        <span ref="tail"></span>
      </div>
    </div>
    <div class="real-box">
      <div v-html="realContent"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: '',
    },
    btnText: {
      type: String,
      default: '展开',
    },
    ellipsisText: {
      type: String,
      default: '...',
    },
    rows: {
      type: Number,
      default: 3,
    },
    btnShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textLength: 0,
      boxWidth: 0,
      boxHeight: 0,
      observer: null,
      ellipsisHtml: '',
    };
  },
  computed: {
    showContent() {
      return this.content.substr(0, this.textLength);
    },
    realContent() {
      if (!this.isEllipsisActive) {
        return this.content;
      }
      return this.showContent + this.ellipsisHtml;
    },
    isEllipsisActive() {
      return this.textLength < this.content.length || this.btnShow;
    },
    watchData() {
      return [
        this.content,
        this.btnText,
        this.ellipsisText,
        this.rows,
        this.btnShow,
      ];
    },
  },
  watch: {
    watchData: {
      immediate: true,
      handler() {
        this.refresh();
      },
    },
  },
  mounted() {
    // 创建省略号和按钮的HTML
    this.ellipsisHtml = `<span class="inline-ellipsis">${this.ellipsisText}<span class="ellipsis-btn" onclick="event.stopPropagation(); this.dispatchEvent(new CustomEvent('ellipsis-click', { bubbles: true }))">&nbsp;${this.btnText}</span></span>`;

    this.observer = new ResizeObserver(() => {
      const el = this.$refs.box;
      if (
        el.offsetWidth === this.boxWidth &&
        el.offsetHeight === this.boxHeight
      )
        return;
      this.boxWidth = el.offsetWidth;
      this.boxHeight = el.offsetHeight;
      this.refresh();
    });

    this.observer.observe(this.$refs.box);

    // 为real-box添加ellipsis-click事件监听
    this.$nextTick(() => {
      const realBox = this.$el.querySelector('.real-box');
      if (realBox) {
        realBox.addEventListener('ellipsis-click', event => {
          // 阻止事件继续冒泡
          event.stopPropagation();
          this.clickBtn(event);
        });
      }
    });
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.unobserve(this.$refs.box);
    }
    // 移除事件监听
    const realBox = this.$el.querySelector('.real-box');
    if (realBox) {
      // 由于我们使用了匿名函数添加事件监听，无法直接移除
      // 在组件销毁时，这个监听器也会自动被清理
      // 这里不再需要手动移除
    }
  },
  methods: {
    refresh() {
      let start = 0;
      let end = this.content.length;
      const checkLoop = () => {
        const boxRect = this.$refs.box.getBoundingClientRect();
        const tailRect = this.$refs.tail.getBoundingClientRect();
        const overflow = tailRect.bottom > boxRect.bottom;

        if (overflow) {
          end = this.textLength;
        } else {
          start = this.textLength;
        }

        this.textLength = Math.floor((start + end) / 2);

        if (start + 1 < end) {
          this.$nextTick(checkLoop);
        }
      };

      this.textLength = this.content.length;
      this.$nextTick(checkLoop);
    },
    clickBtn(event) {
      this.$emit('click-btn', event);
    },
  },
};
</script>

<style lang="less" scoped>
.mm-ellipsis-container {
  text-align: left;
  position: relative;
  line-height: 1.5;

  .shadow {
    width: 100%;
    display: flex;
    pointer-events: none;
    opacity: 0;
    user-select: none;
    position: absolute;
    outline: green solid 1px;

    textarea {
      border: none;
      flex: auto;
      padding: 0;
      resize: none;
      overflow: hidden;
      font-size: inherit;
      line-height: inherit;
      outline: none;
    }

    .shadow-box {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
  }
  .real-box {
    /deep/.inline-ellipsis {
      display: inline;
      .ellipsis-btn {
        cursor: pointer;
        color: #2bbe88;
        display: inline;
      }
    }
  }
}
</style>
