<template>
  <div class="verify-security-component">
    <div class="title">{{ step == 1 ? '绑定邮箱校验' : '绑定新的邮箱' }}</div>
    <div class="content">
      <form class="form">
        <div class="field">
          <div class="field-icon email-icon"></div>
          <div v-if="hadBind && step == 1" class="field-text">{{ email }}</div>
          <input
            v-else
            type="text"
            v-model="email"
            placeholder="请输入新邮箱地址"
          />
        </div>
        <!-- 验证码 -->
        <div class="field">
          <div class="field-icon authcode-icon"></div>
          <input type="text" v-model="authCode" placeholder="输入验证码" />
          <div class="right">
            <div
              class="code-btn"
              :class="{ disabled: !email }"
              v-if="!ifCount"
              @click="getAuthCode()"
            >
              {{ $t('获取验证码') }}
            </div>
            <div class="code-btn disabled" v-else>
              {{ `${$t('重新获取')}${countdown}s` }}
            </div>
          </div>
        </div>
      </form>
    </div>
    <div
      class="comfirn btn"
      :class="{ on: email && authCode }"
      @click="confirm"
    >
      {{ hadBind && step == 1 ? '下一步' : '确定' }}
    </div>
  </div>
</template>

<script>
import {
  ApiMailSend,
  ApiUserReplaceEmail,
  ApiUserBindEmail,
} from '@/api/views/users';
import { mapActions } from 'vuex';
export default {
  props: {
    step: {
      type: Number,
      default: 0,
    },
    oldEmail: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      question: '',
      email: '',
      authCode: '',
      countdown: 60,
      ifCount: false,
    };
  },
  computed: {
    hadBind() {
      // 是否已绑定过邮箱
      return this.userInfo.email ? true : false;
    },
  },
  created() {
    if (this.hadBind && this.step == 1) {
      this.email = this.userInfo.email;
    } else {
      this.email = '';
    }
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async confirm() {
      if (!this.email) {
        this.$toast('请输入邮箱');
        return;
      }
      if (!this.authCode) {
        this.$toast('请输入验证码');
        return;
      }
      this.$toast.loading('加载中');
      const params = {
        email: this.email,
        code: this.authCode,
        step: this.step,
      };
      if (!this.hadBind) {
        // 绑定邮箱 (之前没有邮箱的)
        const res = await ApiUserBindEmail(params);
        await this.SET_USER_INFO();
        setTimeout(() => {
          this.back();
        }, 500);
        return;
      }
      if (this.step == 1) {
        //换绑验证
        const res = await ApiUserReplaceEmail(params);
        this.$emit('verifySuccess');
      } else if (this.step == 2) {
        // 换绑新邮箱
        params.oldEmail = this.oldEmail;
        const res = await ApiUserReplaceEmail(params);
        await this.SET_USER_INFO();
        setTimeout(() => {
          this.back();
        }, 500);
      }
    },

    // 获取邮箱验证码
    getAuthCode() {
      if (this.email === '') {
        this.$toast('请输入邮箱');
        return false;
      }
      // 发送axios请求
      let params = {
        email: this.email,
        type: this.hadBind ? 10 : 11,
      };
      if (this.step) {
        params.step = this.step;
      }
      ApiMailSend(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
  },
};
</script>

<style lang="less" scoped>
.verify-security-component {
  padding: 32 * @rem 16 * @rem;
  .title {
    font-size: 16 * @rem;
    color: #333438;
    line-height: 20 * @rem;
    font-weight: 600;
  }
  .content {
    box-sizing: border-box;
    width: 343 * @rem;
    margin: 12 * @rem auto 0;
    background: #ffffff;
    border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
    padding: 7 * @rem 20 * @rem;
    .form {
      .field {
        display: flex;
        align-items: center;
        width: 100%;
        height: 50 * @rem;
        position: relative;
        background: #ffffff;
        padding: 9 * @rem 0;
        &:not(:first-of-type) {
          border-top: 1 * @rem solid #efefef;
        }
        .field-icon {
          width: 24 * @rem;
          height: 24 * @rem;
          background-size: 24 * @rem 24 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.email-icon {
            background-image: url('~@/assets/images/users/login-email-icon.png');
          }
          &.authcode-icon {
            background-image: url('~@/assets/images/users/login-verify-icon.png');
          }
        }
        .field-text,
        input {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          width: 100%;
          height: 50 * @rem;
          position: relative;
          background: #ffffff;
          padding: 0 16 * @rem;
          border-radius: 25 * @rem;
          font-size: 14 * @rem;
          color: #333333;
          font-weight: 600;
          flex: 1;
          min-width: 0;
          &::placeholder {
            font-weight: 400;
          }
        }
        .code-btn {
          width: 100 * @rem;
          height: 32 * @rem;
          border-radius: 16 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12 * @rem;
          background: @themeColor;
          margin-right: -7 * @rem;
          &.disabled {
            opacity: 0.5;
          }
        }
      }
    }
  }
  .comfirn {
    height: 44 * @rem;
    margin: 52 * @rem 0 0;
    text-align: center;
    line-height: 44 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: 500;
    opacity: 0.5;
    transition: opacity 0.2s;
    &.on {
      opacity: 1;
    }
  }
  .tips {
    text-align: center;
    margin-top: 16 * @rem;
    font-size: 12 * @rem;
    color: @themeColor;
    line-height: 20 * @rem;
  }
}
</style>
