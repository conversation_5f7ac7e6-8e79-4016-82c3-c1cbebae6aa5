<template>
  <div class="comment-editor">
    <nav-bar-2 :title="$t('发表评论')" :azShow="true"></nav-bar-2>
    <div class="editor-container">
      <div class="title">
        <span>意见与建议</span>
        <span class="txt-count">{{ inputText.length }}/200字</span>
      </div>
      <div class="input-container">
        <textarea
          id="inputText"
          class="input-text"
          v-model.trim="inputText"
          rows="10"
          :placeholder="placeholder"
        ></textarea>
      </div>
      <div class="img-container">
        <div class="title">
          <span>上传图片</span>
          <div class="format">支持PNG，JPEG格式，{{ images.length }}/6</div>
        </div>
        <van-uploader
          v-model="imageFileList"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="6"
          :preview-size="100"
          accept="image/*"
          :multiple="true"
          class="uploader"
        >
          <div class="upload-btn">
            <img src="@/assets/images/player-voice/upload-icon.png" />
            <span>上传</span>
          </div>
        </van-uploader>
      </div>
      <div class="btn" @click="handleSend">提交</div>
    </div>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import { ApiCommentSubmit } from '@/api/views/comment.js';
import { platform } from '@/utils/box.uni.js';
import md5 from 'js-md5';
export default {
  name: 'CommentEditor',
  data() {
    return {
      score: 5,
      inputText: '', // 评论内容
      imageFileList: [],
      images: [],
      class_id: 0,
      source_id: 0,
      placeholder: '请输入',
    };
  },
  computed: {
    formatContent() {
      return this.inputText.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },
  watch: {
    inputText(newVal, oldVal) {
      this.inputText = newVal.slice(0, 200);
    },
  },
  async created() {
    this.class_id = 106;
    this.source_id = this.$route.params.id;
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelector('#inputText').focus();
    });
  },
  methods: {
    async handleSend() {   
      if (this.isSending) {
        return false;
      }
      if (this.inputText.length == 0) {
        this.$toast('请输入您要回复的内容');
        return false;
      }
      if (this.inputText.length < 4) {
        this.$toast('无意义评论');
        return false;
      }
      if (this.imageFileList.length != this.images.length) {
        this.$toast('图片上传中请稍等');
        return false;
      }
      this.isSending = true;
      let params = {
        sourceId: this.source_id,
        classId: this.class_id,
        model: 'iPhone',
        content: this.formatContent,
        ua: navigator.userAgent,
      };
      if (platform == 'android') {
        params.model = this.modelName ? this.modelName : '安卓';
      }
      if (this.images.length) {
        params.images = JSON.stringify(this.images);
      }
      this.$toast.loading({
        message: this.$t('正在发布评论...'),
      });
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
        setTimeout(() => {
          this.$router.go(-1);
        }, 500);
      } finally {
        this.isSending = false;
      }
    },
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.images.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        console.log(err);
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);       
      } else {
        for (var item of file) {
          item.status = 'loading';
          this.handleUpload(item);
        }        
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
  },
};
</script>

<style lang="less" scoped>
.comment-editor {
  .editor-container {
    padding: 0 18 * @rem;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20 * @rem;

      span {
        height: 18 * @rem;
        font-weight: 600;
        font-size: 14 * @rem;
        color: #000000;
        line-height: 18 * @rem;
      }

      .txt-count {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #999;
        line-height: 15 * @rem;
        text-align: right;
      }
    }

    .input-container {
      .input-text {
        width: 339 * @rem;
        height: 160 * @rem;
        border-radius: 6 * @rem;
        background-color: #f5f5f6;
        outline: none;
        border: none;
        margin-top: 10 * @rem;
        padding: 10 * @rem;
        box-sizing: border-box;
        font-weight: 400;
        font-size: 13 * @rem;
        color: #333;
        line-height: 16 * @rem;
        resize: none;
      }
    }

    .img-container {
      margin-top: 4 * @rem;
      .format {
        flex: 1;
        text-align: left;
        margin-left: 5 * @rem;
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #c1c1c1;
        line-height: 15 * @rem;
      }
      .uploader {
        margin-top: 17 * @rem;

        /deep/ .van-uploader__preview {
          margin-right: 20 * @rem;
          margin-bottom: 14 * @rem;

          .van-image {
            width: 92 * @rem !important;
            height: 92 * @rem !important;
            border-radius: 6 * @rem;
          }

          .van-uploader__preview-delete {
            width: 14 * @rem;
            height: 14 * @rem;
            background: url(~@/assets/images/player-voice/icon-delete.png)
              no-repeat;
            background-size: 14 * @rem 14 * @rem;
            border-radius: 0;

            i {
              display: none;
            }
          }
        }
      }
      .upload-btn {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 92 * @rem;
        height: 92 * @rem;
        border-radius: 6 * @rem;
        border: 1 * @rem dashed #e0e0e0;

        img {
          width: 24 * @rem;
          height: 24 * @rem;
        }

        span {
          height: 15 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #999999;
          line-height: 15 * @rem;
          margin-top: 8 * @rem;
        }
      }
    }
    .btn {
      width: 339px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      border-radius: 29px;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      margin: 40px auto 0;
    }
  }
}
</style>
