<template>
  <div class="page">
    <nav-bar-2 title="开局号" :border="true">
      <template #right>
        <div class="rule-btn btn" @click="clickRule">规则</div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="detail-bar">
        <div class="game-icon">
          <img :src="selectedXiaohao.game_icon" alt="" />
        </div>
        <div class="center">
          <div class="game-name">{{ selectedXiaohao.game_name }}</div>
          <div class="game-area">{{ selectedXiaohao.area_text }}</div>
        </div>
        <div class="right">
          <div class="old-price">¥{{ selectedXiaohao.price }}</div>
        </div>
      </div>
      <div class="ptb-line">
        <div class="line-title">剩余平台币：</div>
        <div class="ptb-num">
          {{ userInfo.ptb
          }}<span v-if="userInfo.ptb_fake"
            >（含{{ userInfo.ptb_fake }}个绑定）</span
          >
        </div>
      </div>
      <div class="pay-line">
        <div class="line-title">待支付金额：</div>
        <div class="pay-num">
          {{ selectedXiaohao.current_price * 10 }}平台币
        </div>
      </div>

      <div class="tip-container">
        <div class="tip-title">注意事项：</div>
        <div class="tip-content">
          1.购买后则不可退款退货，请认真阅读《规则说明》后购买；<br />
          2.购买开局号暂不支持使用绑定平台币支付；<br />
        </div>
      </div>
      <div class="bottom-absolute">
        <div class="pay-btn" v-if="canBuy" @click="handlePay">立即支付</div>
        <div class="no-pay-btn" v-if="!canBuy">平台币不足</div>
        <div class="go-to-ptb" v-if="!canBuy" @click="toPage('PlatformCoin')">
          充值平台币&gt;&gt;
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { ApiXiaohaoPurchaseOpeningAccount } from '@/api/views/xiaohao.js';
export default {
  data() {
    return {
      id: 0,
      selectedXiaohao: {},
    };
  },
  computed: {
    canBuy() {
      if (this.userInfo.ptb > this.selectedXiaohao.current_price * 10) {
        return true;
      }
      return false;
    },
  },
  created() {
    this.id = this.$route.params.id || 0;
    if (this.$route.params.xiaohaoInfo) {
      this.selectedXiaohao = this.$route.params.xiaohaoInfo;
    } else if (localStorage.openingAccountXiaohaoInfo) {
      this.selectedXiaohao = JSON.parse(localStorage.openingAccountXiaohaoInfo);
    }
  },
  async activated() {
    await this.SET_USER_INFO();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    clickRule() {
      this.toPage('OpeningAccountExplain');
    },
    async handlePay() {
      this.$toast.loading('加载中');
      try {
        const res = await ApiXiaohaoPurchaseOpeningAccount({
          id: this.selectedXiaohao.id,
        });
        await this.SET_USER_INFO();
        await new Promise(res => setTimeout(res, 300));
        this.toPage(
          'OpeningAccountBuySuccess',
          { id: this.selectedXiaohao.game_id },
          1,
        );
      } finally {
        this.$toast.clear();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .rule-btn {
    color: #32b768;
  }
  .main {
    padding: 0 18 * @rem;
    flex: 1;
    position: relative;
    .detail-bar {
      box-sizing: border-box;
      height: 98 * @rem;
      display: flex;
      align-items: center;
      border-bottom: 1 * @rem solid #ebebeb;
      margin-bottom: 24 * @rem;
      .game-icon {
        width: 56 * @rem;
        height: 56 * @rem;
      }
      .center {
        flex: 1;
        min-width: 0;
        margin-left: 8 * @rem;

        .game-name {
          font-size: 14 * @rem;
          color: #333333;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .game-area {
          box-sizing: border-box;
          padding: 0 4 * @rem;
          font-size: 10 * @rem;
          color: #888888;
          height: 17 * @rem;
          line-height: 17 * @rem;
          border-radius: 4 * @rem;
          background: #f5f5f6;
          display: inline-block;
          margin-top: 12 * @rem;
        }
      }
      .right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-left: 4 * @rem;
        .tag {
          width: 81 * @rem;
          height: 31 * @rem;
          background: url(~@/assets/images/deal/opening-free-tag-bg.png)
            no-repeat;
          background-size: 81 * @rem 31 * @rem;
          line-height: 25 * @rem;
          font-size: 11 * @rem;
          color: #ffffff;
          text-align: center;
        }
        .old-price {
          font-size: 22 * @rem;
          color: @themeColor;
          font-weight: 600;
          line-height: 31 * @rem;
          text-decoration: line-through;
          margin-top: 22 * @rem;
        }
      }
    }
    .ptb-line,
    .pay-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16 * @rem;
      .line-title {
        font-size: 14 * @rem;
        color: #111111;
        line-height: 20 * @rem;
      }
      .ptb-num {
        font-size: 14 * @rem;
        color: @themeColor;
        line-height: 20 * @rem;
      }
      .pay-num {
        font-size: 15 * @rem;
        color: @themeColor;
        line-height: 20 * @rem;
        font-weight: 600;
      }
    }
    .tip-container {
      margin-top: 46 * @rem;
      .tip-title {
        font-size: 12 * @rem;
        line-height: 20 * @rem;
        color: #ff0000;
      }
      .tip-content {
        font-size: 12 * @rem;
        line-height: 20 * @rem;
        color: #555555;
      }
    }
  }
  .bottom-absolute {
    padding-bottom: 20 * @rem;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    .pay-btn {
      width: 295 * @rem;
      height: 40 * @rem;
      border-radius: 20 * @rem;
      margin: 20 * @rem auto 0;
      background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
      font-size: 15 * @rem;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .no-pay-btn {
      width: 295 * @rem;
      height: 40 * @rem;
      border-radius: 20 * @rem;
      margin: 20 * @rem auto 0;
      background: #c1c1c1;
      font-size: 15 * @rem;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .go-to-ptb {
      text-align: center;
      font-size: 15 * @rem;
      color: #32b768;
      font-weight: 500;
      line-height: 15 * @rem;
      margin-top: 19 * @rem;
    }
  }
}
</style>
