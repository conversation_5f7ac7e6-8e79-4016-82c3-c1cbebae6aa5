<template>
  <div class="rule-page">
    <nav-bar-2
      class="nav-bar"
      :border="true"
      :title="'签到规则'"
      :azShow="true"
    ></nav-bar-2>
    <div class="popup-content">
      <!-- <div class="popup-title">
        <img
          src="@/assets/images/welfare/welfare-center/clock-in-rule-title1.png"
          alt=""
        />
      </div> -->
      <div class="rule-content" v-html="rules" @click="clickRules"></div>
    </div>
  </div>
</template>

<script>
import { ApiUserGetSignInDesc } from '@/api/views/welfare';
import {
  platform,
  BOX_openInNewWindow,
  BOX_showActivityByAction,
} from '@/utils/box.uni.js';
export default {
  data() {
    return {
      rules: '',
    };
  },
  created() {
    this.getSignInDesc();
  },
  methods: {
    async getSignInDesc() {
      let res = await ApiUserGetSignInDesc();
      this.rules = res.data.desc;
    },
    clickRules(event) {
      event.preventDefault();
      let target = event.target;
      if (target.tagName.toLowerCase() == 'a') {
        let routeName = target.getAttribute('href');
        let code = target.getAttribute('data-code');
        if (code == 57) {
          // 金币商城
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'WelfareGoldCoinExchange',
            });
          } catch (e) {
            BOX_openInNewWindow(
              { name: 'GoldCoinExchange' },
              { url: routeName },
            );
          }
        } else if (code == 56) {
          // svip
          BOX_openInNewWindow({ name: 'Svip' }, { url: routeName });
        }
        //  this.$router.push({ name: routeName });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.nav-bar {
  /deep/ .nav-title {
    font-weight: bold !important;
  }
}
.popup-content {
  padding: 16px 18px;

  /deep/ .rule-content {
    font-size: 14 * @rem;
    line-height: 24 * @rem;
    color: #30343b;
    a {
      color: @themeColor !important;
    }
  }
}
</style>
