<template>
  <!-- 赏金任务提示 -->
  <van-dialog
    v-model="show"
    :show-confirm-button="false"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    class="create-dialog"
    :beforeClose="close"
  >
    <!-- <div class="logo-icon"></div> -->
    <div class="dialog-content">
      <div class="title">领取成功</div>
      <div class="message"
        >您当前尚未创建角色，请先下载<br />游戏并创建角色</div
      >
      <div class="dialog-bottom-bar">
        <!-- <div class="cancel btn" @click.stop="close">{{ $t('稍后下载') }}</div> -->
        <div
          class="confirm1 btn"
          :class="{ disabled: isDisabled }"
          @click.stop="confirm"
          >{{ $t('下载游戏') }}</div
        >

        <div class="btn-close" @click="close()">
          <span></span>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { BOX_goToGame, platform } from '@/utils/box.uni.js';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      // 游戏id
      type: Number,
      required: true,
    },
    detail: {
      type: Object,
      default: () => {},
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    close() {
      this.$emit('update:show', false);
    },
    confirm() {
      this.$emit('update:show', false);
      this.$nextTick(() => {
        BOX_goToGame(
          {
            params: {
              id: this.id,
            },
          },
          { id: this.id },
        );
      });
    },
  },
  // watch: {
  //   show(val) {
  //     if (val) {
  //       this.$nextTick(() => {
  //         this.$emit('update:show', true);
  //       });
  //     }
  //   },
  // },
};
</script>

<style lang="less" scoped>
.create-dialog {
  width: 300 * @rem;
  background: transparent;
  // .logo-icon {
  //   width: 300 * @rem;
  //   height: 37 * @rem;
  //   .image-bg('~@/assets/images/games/dialog-logo.png');
  //   margin: 0 auto;
  //   position: relative;
  //   z-index: 3;
  // }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 300 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    // margin-top: -4 * @rem;
    z-index: 2;
    padding: 30 * @rem 31 * @rem 20 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 20 * @rem;
    }
    .message {
      margin-top: 30 * @rem;
      height: 40 * @rem;
      font-weight: 400;
      font-size: 15 * @rem;
      color: #333333;
      line-height: 18 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      word-break: break-all;
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24 * @rem;
      padding: 0 5 * @rem;
      position: relative;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 38 * @rem;
        line-height: 38 * @rem;
        color: #ffffff;
        font-size: 15 * @rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(90deg, #8ad6ef 0%, #a1b2f5 100%);
        border-radius: 18 * @rem;
        &.disabled {
          background: @themeBg;
        }
      }
      .confirm1 {
        width: 100%;
        height: 38 * @rem;
        line-height: 38 * @rem;
        color: #ffffff;
        font-size: 15 * @rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(90deg, #8ad6ef 0%, #a1b2f5 100%);
        border-radius: 18 * @rem;
        &.disabled {
          background: @themeBg;
        }
      }
      .btn-close {
        position: absolute;
        bottom: -52 * @rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          background: url('~@/assets/images/welfare/btn-close.png') no-repeat 0
            0;
          background-size: 22 * @rem 23 * @rem;
          width: 22 * @rem;
          height: 23 * @rem;
        }
      }
    }
  }
}
</style>
