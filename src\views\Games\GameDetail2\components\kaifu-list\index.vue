<template>
  <div class="kaifu-component">
    <content-empty v-if="empty" :tips="tips"></content-empty>
    <load-more
      v-else
      class="list-container"
      v-model="loading"
      :finished="finished"
      :finishedText="finishedText"
      @loadMore="loadMore"
    >
      <div class="kaifu-list">
        <div
          class="kaifu-item"
          v-for="(kaifu, kaifuIndex) in openList"
          :key="kaifuIndex"
        >
          <div class="left">
            <div class="time">
              <div class="day">
                {{
                  kaifu.is_today
                    ? $t('今日')
                    : $handleTimestamp(kaifu.newstime).date
                }}
              </div>
              <div class="hour">
                {{ $handleTimestamp(kaifu.newstime).time }}
              </div>
            </div>
            <div class="quhao">{{ kaifu.state }}</div>
          </div>
          <div class="status had" v-if="kaifu.countdown_second < 0">
            {{ $t('已开服') }}
          </div>
          <template v-else>
            <div
              class="status btn"
              v-if="kaifu.status == 0"
              @click="remind(kaifu.id, 1)"
            >
              {{ $t('提醒我') }}
            </div>
            <div
              class="status btn"
              v-if="kaifu.status == 1"
              @click="remind(kaifu.id, 0)"
            >
              {{ $t('取消') }}
            </div>
          </template>
        </div>
      </div>
    </load-more>
    <van-dialog
      class="wx-popup"
      v-model="wx_popup.show"
      :lock-scroll="false"
      show-cancel-button
      confirmButtonText="设置微信提醒"
      @confirm="toPage('BindWeChat')"
    >
      <img class="bg" src="~@/assets/images/games/ic_subscribe_success.png" />
      <div class="big-text">设置微信提醒</div>
      <div class="small-text">
        开服提醒需先绑定微信，关注公众号后才可生效哦~
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiServerIndex, ApiServerRemind } from '@/api/views/game.js';
export default {
  name: 'kaifuList',
  props: {
    gameId: {
      required: true,
    },
    finishedText: {
      type: String,
      default: '- 没有更多了 -',
    },
  },
  data() {
    return {
      openList: [],
      finished: false,
      loading: false,
      empty: false,
      tips: '',
      wx_popup: {
        show: false,
      },
    };
  },
  methods: {
    async remind(serverId, status) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        const res = await ApiServerRemind({
          serverId,
          status,
        });
        await this.getKaifuList();
        if (res.code > 0) {
          this.$toast(res.msg);
        }
      } catch (e) {
        if (e.code == -16) {
          this.wx_popup.show = true;
        }
      }
    },
    async getKaifuList() {
      const res = await ApiServerIndex({
        gameId: this.gameId,
      });

      if (!res.data.length) {
        this.empty = true;
        this.tips = res.msg;
      } else {
        this.openList = res.data;
      }
      this.loading = false;
      this.finished = true;
    },
    async loadMore() {
      if (this.finished) return false;
      await this.getKaifuList();
      this.loading = false;
    },
  },
};
</script>
<style lang="less" scoped>
.kaifu-component {
  box-sizing: border-box;
  padding: 0 12 * @rem;
  .kaifu-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .kaifu-item {
      box-sizing: border-box;
      width: 171 * @rem;
      padding: 0 9 * @rem;
      background: #f5f5f6;
      border-radius: 6 * @rem;
      height: 64 * @rem;
      display: flex;
      align-items: center;
      &:not(:nth-of-type(-n + 2)) {
        margin-top: 10 * @rem;
      }
      .left {
        flex: 1;
        min-width: 0;
        .time {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: bold;
          display: flex;
          align-items: center;
          line-height: 20 * @rem;
          .hour {
            margin-left: 5 * @rem;
          }
        }
        .quhao {
          font-size: 12 * @rem;
          color: #9a9a9a;
          font-weight: 400;
          margin-top: 2 * @rem;
          line-height: 17 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .status {
        box-sizing: border-box;
        width: 54 * @rem;
        height: 30 * @rem;
        // border: 1 * @rem solid @themeColor;
        background: #e4fcf0;
        color: @themeColor;
        font-size: 11 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6 * @rem;
        &.had {
          border: 1 * @rem solid #c1c1c1;
          color: #fff;
          background-color: #c1c1c1;
        }
      }
    }
  }
  .wx-popup {
    .bg {
      display: block;
      width: 150 * @rem;
      margin: 25 * @rem auto;
    }
    .big-text {
      font-size: 20 * @rem;
      text-align: center;
    }
    .small-text {
      margin: 15 * @rem 20 * @rem 30 * @rem;
      font-size: 14 * @rem;
      text-align: center;
    }
  }
}
</style>
