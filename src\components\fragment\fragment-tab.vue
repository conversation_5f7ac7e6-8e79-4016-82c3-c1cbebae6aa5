<template>
  <div class="tab-fragment">
    <div
      class="tab-item"
      v-for="(item, index) in info.tab_action"
      :key="index"
      @click="goToPage(item, index)"
        
    >
      <img class="tab-icon" :src="item.icon_url" alt="" v-sensors-exposure="iconExposure(item, index)" />
      <div class="tab-title">{{ item.text1 }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FragmentTab',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    
    iconExposure(item, index) {
      return {
        'event-name': 'icon_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-icon_index': `${index}`,
        'property-icon_name': item.text1,
      }
    },
    goToPage(item, index) {
      // 神策埋点
      this.$sensorsTrack('icon_click', {
        page_name: this.$sensorsPageGet(),
        icon_index: `${index}`,
        icon_name: item.text1,
      });
      this.toPage('CateGameList', {
        basis_id: this.info.basis_id || 0,
        cate_id: item.extra_id || 0,
        title: item.text1,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.tab-fragment {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12 * @rem;
  padding: 4 * @rem 12 * @rem;
  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // width: 44 * @rem;
    .tab-icon {
      display: block;
      width: 40 * @rem;
      height: 40 * @rem;
    }
    .tab-title {
      font-size: 11 * @rem;
      color: #333333;
      line-height: 15 * @rem;
      margin-top: 6 * @rem;
      white-space: nowrap;
      overflow: hidden;
      height: 15 * @rem;
    }
  }
}
</style>
