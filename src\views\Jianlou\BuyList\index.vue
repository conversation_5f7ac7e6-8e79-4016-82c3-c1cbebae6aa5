<template>
  <div class="buy-list-page">
    <div class="top-bar">
      <div
        class="search-container btn"
        v-if="!gameInfo.id"
        @click="toSearchGame"
      >
        <div class="search-text">{{ $t('搜索游戏') }}</div>
        <div class="search-btn">
          <div class="search-icon"></div>
        </div>
      </div>
      <div class="search-container btn" v-else @click="clearGame">
        <div class="game-name">{{ gameInfo.title }}</div>
        <div class="close-icon"></div>
      </div>
      <div class="filter-container">
        <van-dropdown-menu :active-color="themeColorLess">
          <van-dropdown-item
            v-model="order_value"
            :options="order_list"
            :lock-scroll="false"
            @change="onChange"
          />
          <van-dropdown-item
            v-model="price_value"
            :options="price_list"
            :lock-scroll="false"
            @change="onChange"
          />
          <van-dropdown-item
            v-model="platform_value"
            :options="platform_list"
            :lock-scroll="false"
            @change="onChange"
          />
        </van-dropdown-menu>
      </div>
    </div>
    <content-empty v-if="empty" :tips="$t('暂无捡漏发布')"></content-empty>
    <yy-list
      v-else
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :check="false"
    >
      <jianlou-buy-list :list="buyList"></jianlou-buy-list>
    </yy-list>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import { ApiXiaohaoJianlouList } from '@/api/views/xiaohao.js';
import jianlouBuyList from '../components/jianlou-buy-list';
import { isIos } from '@/utils/userAgent.js';
export default {
  name: 'BuyList',
  components: {
    jianlouBuyList,
  },
  data() {
    return {
      themeColorLess,
      order_value: 1,
      price_value: 0,
      platform_value: isIos ? 12 : 11,
      order_list: [
        {
          text: this.$t('最新发布'),
          value: 1,
        },
        {
          text: this.$t('价格最低'),
          value: 2,
        },
        {
          text: this.$t('价格最高'),
          value: 3,
        },
        {
          text: this.$t('创号最短'),
          value: 4,
        },
        {
          text: this.$t('创号最久'),
          value: 5,
        },
      ],
      price_list: [
        {
          text: this.$t('全部价格'),
          value: 0,
        },
      ],
      platform_list: [
        {
          text: this.$t('全平台'),
          value: 0,
        },
        {
          text: this.$t('安卓'),
          value: 11,
        },
        {
          text: 'iOS',
          value: 12,
        },
      ],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      buyList: [],
      showWaterfall: false,
      page: 1,
      listRows: 10,
      gameInfo: {},
      empty: false,
      about: '',
    };
  },
  activated() {
    let info = this.$route.params.info;
    if (info) {
      this.gameInfo = info;
      this.getBuyList();
    } else {
      this.gameInfo = {};
      this.getBuyList();
    }
  },
  methods: {
    clearGame() {
      this.gameInfo = {};
      this.getBuyList();
    },
    toSearchGame() {
      this.toPage('SearchGame', {
        from: 'Jianlou',
      });
    },
    async onChange() {
      this.buyList = [];
      this.page = 1;
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getBuyList();
      this.loadingObj.loading = false;
      this.$toast.clear();
    },
    async getBuyList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let params = {
        isDone: 0,
        page: this.page,
        listRows: this.listRows,
        order: this.order_value,
        deviceFrom: this.platform_value,
      };
      if (this.price_value) {
        params = { ...params, priceId: this.price_value };
      }
      if (this.gameInfo.id) {
        params = { ...params, gameId: this.gameInfo.id };
      }
      const res = await ApiXiaohaoJianlouList(params);
      if (res.data.about) {
        this.$emit('getAbout', res.data.about);
      }
      if (res.data.price_range) {
        // 设置价格区间
        this.price_list = res.data.price_range.map(item => {
          if (item.id == 0) {
            item.title = this.$t('全部价格');
          }
          return {
            text: item.title,
            value: item.id,
          };
        });
      }
      if (action === 1 || this.page === 1) {
        this.buyList = [];
      }
      this.buyList.push(...res.data.list);
      if (this.buyList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getBuyList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getBuyList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.buy-list-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 18 * @rem;
    height: 90 * @rem;
    flex-shrink: 0;
    .search-container {
      box-sizing: border-box;
      padding-left: 10 * @rem;
      padding-right: 7 * @rem;
      width: 100%;
      height: 38 * @rem;
      border-radius: 18 * @rem;
      display: flex;
      align-items: center;
      box-shadow: 0px 6px 15px 0px rgba(150, 150, 150, 0.15);
      background-color: #ffffff;
      margin: 11 * @rem auto;
      flex-shrink: 0;

      .search-text {
        font-size: 14 * @rem;
        color: #9a9a9a;
        margin-left: 8 * @rem;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .search-btn {
        width: 38 * @rem;
        height: 22 * @rem;
        background: linear-gradient(90deg, #fdba42 0%, #fe5b5b 100%);
        border-radius: 13 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .search-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          background: url(~@/assets/images/jianlou/search-game-icon.png)
            no-repeat;
          background-size: 14 * @rem 14 * @rem;
        }
      }
      .close-icon {
        width: 15 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/close-black-oo.png) no-repeat;
        background-size: 15 * @rem 15 * @rem;
        margin-right: 10 * @rem;
      }
      .game-name {
        font-size: 13 * @rem;
        color: @themeColor;
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 10 * @rem;
      }
    }
    .filter-container {
      height: 30 * @rem;
    }
  }
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }
  // 下拉菜单
  /deep/ .van-dropdown-item {
    position: absolute;
    top: 90 * @rem !important;
    height: calc(100vh - 184 * @rem - @safeAreaTop);
    height: calc(100vh - 184 * @rem - @safeAreaTopEnv);
    z-index: 3000;
    .van-overlay {
      height: 100vh;
    }
  }
  /deep/ .van-dropdown-menu__bar {
    box-shadow: unset;
    background-color: transparent;
    height: 30 * @rem;
    .van-dropdown-menu__title--active::after {
      border-color: transparent transparent @themeColor @themeColor;
    }
  }
  /deep/ .van-dropdown-menu__item {
    margin-left: 12 * @rem;
    border-radius: 13 * @rem;
    &:nth-of-type(1) {
      margin-left: 0;
    }
  }
  /deep/ .van-dropdown-menu__title {
    position: relative;
    box-sizing: border-box;
    max-width: 100%;
    padding: 0 8px;
    color: #000;
    font-size: 13px;
    line-height: 30 * @rem;
  }
  /deep/ .van-dropdown-menu__title::after {
    border-color: transparent transparent #5d5d5d #5d5d5d;
  }
}
</style>
