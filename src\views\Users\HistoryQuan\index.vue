<template>
  <div class="my-quan-page page">
    <nav-bar-2 :border="true" :title="$t('历史代金券')"></nav-bar-2>
    <content-empty v-if="empty"></content-empty>
    <div class="my-quan-container" v-else>
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="quan-list">
          <div
            class="quan-item"
            v-for="(item, index) in couponList"
            :key="index"
          >
            <img class="bg" src="@/assets/images/quan-bg.png" alt="" />
            <div class="quan-info">
              <div class="left">
                <div class="quan-num">
                  <span>{{ item.money }}</span
                  >{{ item.unit }}
                </div>
                <div class="total-num">
                  {{ $t('满') }}{{ item.reach_money }}{{ item.unit
                  }}{{ $t('可用') }}
                </div>
              </div>
              <div class="right">
                <div class="title">{{ item.remark }}</div>
                <div class="desc">
                  <div class="date">
                    {{ $handleTimestamp(item.expire_time).date }}
                    {{ $handleTimestamp(item.expire_time).time }}
                  </div>
                </div>
              </div>
              <div class="overdue"></div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiCouponMine } from '@/api/views/coupon.js';
export default {
  name: 'HistoryQuan',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      couponList: [],
      empty: false,
    };
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiCouponMine({
        page: this.page,
        listRows: this.listRows,
        state: 1,
      });
      if (action === 1 || this.page === 1) {
        this.couponList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.couponList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.couponList.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }
      await this.getList(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.my-quan-page {
  display: flex;
  flex-direction: column;
  .my-quan-container {
    box-sizing: border-box;
    height: calc(100vh - 50 * @rem - @safeAreaTop - @safeAreaBottom);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
    /deep/ .pull-refresh {
      background-color: #f5f5f5;
    }
    .quan-list {
      padding-top: 10 * @rem;
      .quan-item {
        position: relative;
        width: 360 * @rem;
        height: 100 * @rem;
        margin: 0 auto 6 * @rem;
        .bg {
          position: absolute;
          left: 0;
          top: 0;
          display: block;
          width: 100%;
          height: 100%;
        }
        .quan-info {
          box-sizing: border-box;
          height: 100%;
          position: relative;
          padding: 8 * @rem 7 * @rem;
          display: flex;
          align-items: center;
          .left {
            width: 92 * @rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .quan-num {
              font-size: 14 * @rem;
              color: #333333;
              span {
                font-size: 25 * @rem;
                font-weight: bold;
                margin-right: 2 * @rem;
              }
            }
            .total-num {
              font-size: 11 * @rem;
              color: #666666;
              margin-top: 10 * @rem;
            }
          }
          .right {
            flex: 1;
            min-width: 0;
            padding: 0 12 * @rem 0 16 * @rem;
            position: relative;
            .title {
              font-size: 15 * @rem;
              color: #010101;
              font-weight: bold;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .desc {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 5 * @rem;
              margin-top: 10 * @rem;
              .date {
                font-size: 11 * @rem;
                color: #999999;
              }
            }
          }
          .overdue {
            position: absolute;
            right: 16 * @rem;
            top: 19 * @rem;
            width: 30 * @rem;
            height: 30 * @rem;
            background: url(~@/assets/images/users/overdue-icon.png) center
              center no-repeat;
            background-size: 30 * @rem 30 * @rem;
          }
        }
      }
    }

    .quan-history {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 30 * @rem 0;
      .btn-text {
        font-size: 14 * @rem;
        color: #666666;
      }
      .right-icon {
        width: 6 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/right-icon.png) no-repeat;
        background-size: 6 * @rem 10 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
  .more-quan-container {
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    .more-quan {
      width: 100%;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .more-text {
        font-size: 15 * @rem;
        font-weight: bold;
        color: #333333;
      }
      .right-icon-black {
        width: 6 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/users/right-icon-black.png) no-repeat;
        background-size: 6 * @rem 10 * @rem;
        margin-left: 9 * @rem;
      }
    }
  }
}
</style>
