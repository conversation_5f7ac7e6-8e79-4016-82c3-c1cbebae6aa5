<template>
  <van-popup
    v-model="popupShow"
    :lock-scroll="false"
    position="bottom"
    :style="{ height: '80%' }"
    :safe-area-inset-bottom="true"
    class="vant-more-game-popup"
  >
    <div class="game-list-title">
      <div class="title-close-btn" @click.stop="popupShow = false">
        <img src="@/assets/images/recharge/svip-ad-close.png" alt="" />
      </div>
    </div>
    <div class="game-list-search">
      <div class="search-bar">
        <div class="search-icon"></div>
        <div class="search-input">
          <form @submit.prevent="submitSearch(keyword)">
            <input
              v-model.trim="keyword"
              type="text"
              :placeholder="$t('请输入游戏名称')"
              :maxlength="15"
            />
          </form>
        </div>
        <div
          class="input-clear"
          v-if="inputClearShow"
          @click="clearInput"
        ></div>
      </div>
    </div>
    <div class="game-list-container">
      <content-empty
        v-if="empty"
        :tips="$t('咦，什么都没找到哦~')"
      ></content-empty>
      <yy-list
        v-else
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh()"
        @loadMore="loadMore()"
      >
        <div class="game-list">
          <game-item-info
            v-for="item in gameListInfo"
            :key="item.id"
            :itemInfo="item"
            @send-data="receiveDataFromGrandchild"
            @isShowClose="isClose"
          ></game-item-info>
        </div>
      </yy-list>
    </div>
  </van-popup>
</template>

<script>
import GameItemInfo from '../game-item-info/index';
import { ApiGameGetCardGameList } from '@/api/views/gold.js';
function debounce(fn, delay) {
  let timer = null;
  return function (value) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.call(this, value);
    }, delay);
  };
}
export default {
  name: 'more-game-popup',
  components: {
    GameItemInfo,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isResult: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      timer: null,
      gameListInfo: [],
      keyword: '',
      page: 1,
      listRows: 20,
    };
  },
  created() {
    this.getCardGameList(this.keyword, 1);
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    inputClearShow() {
      return this.keyword.length ? true : false;
    },
  },
  methods: {
    clearInput() {
      this.keyword = '';
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
    },
    async searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      // this.gameListInfo = []
      // await this.getGameList()
    },
    async getCardGameList(keyword, action = 1) {
      keyword = keyword.trim();
      // if (!keyword) {
      //   this.$toast(this.$t("请输入搜索词"))
      //   return
      // }
      // this.isResult = true;
      // this.empty = false;
      // this.finished = false;
      // this.loadingObj.loading = true;
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      try {
        const res = await ApiGameGetCardGameList({
          keyword,
          page: this.page,
          listRows: this.listRows,
        });
        if (action === 1 || this.page === 1) {
          this.gameListInfo = [];
        }
        this.gameListInfo.push(...res.data.list);
        if (!this.gameListInfo.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
        if (res.data.list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (error) {
        if (error.code == 500) {
          this.empty = true;
        }
      }
    },
    async onRefresh() {
      await this.getCardGameList(this.keyword, 1);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameListInfo.length) {
        await this.getCardGameList(this.keyword, 1);
      } else {
        await this.getCardGameList(this.keyword, 2);
      }
      this.loadingObj.loading = false;
    },
    receiveDataFromGrandchild(data) {
      this.$emit('send-data', data);
    },
    isClose(val) {
      this.$emit('update:show', val);
    },
    // 自定义节流函数
    throttle(func, delay) {
      let timeoutId;
      return function (...args) {
        if (!timeoutId) {
          timeoutId = setTimeout(() => {
            func.apply(this, args);
            timeoutId = null;
          }, delay);
        }
      };
    },
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.getCardGameList(this.keyword, 1);
      }, 500),
    },
  },
};
</script>
<style lang="less" scoped>
.vant-more-game-popup {
  border-radius: 10 * @rem 10 * @rem 0 0;
  display: flex;
  flex-direction: column;
  .game-list-title {
    background: url(~@/assets/images/recharge/gold-coin-game-list.png) no-repeat
      0 0;
    background-size: 375 * @rem 61 * @rem;
    width: 375 * @rem;
    height: 61 * @rem;
    position: relative;
    margin: 0 auto;
    .title-close-btn {
      position: absolute;
      top: 15 * @rem;
      right: 19 * @rem;
      width: 24 * @rem;
      height: 24 * @rem;
    }
  }
  .game-list-search {
    display: flex;
    align-items: center;
    justify-content: center;
    .search-bar {
      box-sizing: border-box;
      padding: 0 12 * @rem 0 18 * @rem;
      width: 330 * @rem;
      height: 33 * @rem;
      border-radius: 17 * @rem;
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      .search-icon {
        width: 17 * @rem;
        height: 17 * @rem;
        background: url(~@/assets/images/search-icon.png) center center
          no-repeat;
        background-size: 17 * @rem 17 * @rem;
      }
      .search-input {
        flex: 1;
        height: 33 * @rem;
        margin-left: 7 * @rem;
        form {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
      .input-clear {
        width: 18 * @rem;
        height: 18 * @rem;
        .image-bg('~@/assets/images/input-clear.png');
        margin-left: 12 * @rem;
      }
    }
  }
  .game-list-container {
    padding: 15 * @rem;
    flex: 1;
    min-height: 0;
    margin-top: 10 * @rem;
    overflow: auto;
    .game-list {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20 * @rem 5 * @rem;
    }
  }
}
/* 浏览器滚动条隐藏 */
* {
  scrollbar-width: none;
}
*::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
}
* {
  -ms-overflow-style: none;
}
* {
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}
* {
  overflow: -webkit-scrollbar;
}
</style>
