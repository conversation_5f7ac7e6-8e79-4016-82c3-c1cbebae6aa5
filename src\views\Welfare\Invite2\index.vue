<template>
  <div class="invite">
    <nav-bar-2 :title="'收益中心'" :border="true" :azShow="true"> </nav-bar-2>
    <div class="wrapper1">
      <div class="content">
        <div class="top">
          <div class="left">当前现金</div>
          <div class="right">
            <div @click="toPage('ExchangeGold')" class="btn button">兑换</div>
            <div @click="toPage('CashOut')" class="btn button">提现</div>
          </div>
        </div>
        <div class="center"><span>￥</span>{{ money_info.now }}</div>
        <div class="bottom">
          <div class="left">累计收入：{{ money_info.total }}</div>
          <div class="right">已提现：{{ money_info.exchange }}</div>
        </div>
      </div>
      <div class="bottom-button">
        <div @click="toPage('InviteCourse')" class="btn button">赚钱教程</div>
        <div @click="toPage('InviteRule')" class="btn button">邀请规则</div>
        <div @click="toPage('InviteDetail', { type: 1 })" class="btn button">
          邀请明细
        </div>
        <div @click="toPage('InviteDetail', { type: 2 })" class="btn button">
          收入明细
        </div>
      </div>
    </div>
    <van-swipe
      class="wrapper2"
      vertical
      :autoplay="2000"
      :show-indicators="false"
    >
      <van-swipe-item v-for="(item, index) in list" :key="index"
        ><div class="item">
          {{ item.nickname }}邀请获得<span>{{ item.gold }}</span
          >元
        </div></van-swipe-item
      >
    </van-swipe>
    <div class="wrapper3">
      <div class="title">我的邀请二维码</div>
      <img :src="ewm" alt="" />
    </div>
    <div class="wrapper4">
      <div class="button-container">
        <!-- <div class="btn button">分享海报邀请</div> -->
        <div @click="copy" class="btn button">复制链接邀请</div>
      </div>
      <div class="text">
        把海报、链接分享给喜欢玩游戏的朋友，邀请成功率会大大提升哦~邀请越多奖励越多,如有疑问请<span
          @click="toPage('Kefu')"
          class="btn"
          >联系客服</span
        >
      </div>
      <div class="color-text">
        注：当前活动为内测版本，仅财富值等级≥公爵的用户才可参与该活动
      </div>
    </div>
  </div>
</template>
<script>
import { ApiInvitert } from '@/api/views/weekWelfare.js';

export default {
  data() {
    return {
      is_invite: false, //是否能邀请
      list: [], //滚动列表
      ewm: '', //二维码地址
      url: '', //邀请链接
      money_info: {}, //受益信息
    };
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      const res = await ApiInvitert();
      this.is_invite = res.data.is_inviter;
      this.list = res.data.list;
      this.ewm = res.data.ewm;
      this.url = res.data.url;
      this.money_info = res.data.money_info;
    },
    copy() {
      event.stopPropagation();
      this.$copyText(this.url).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.invite {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.wrapper1 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 339 * @rem;
  height: 160 * @rem;
  margin: 10 * @rem auto;
  background: linear-gradient(100.18deg, #ffc875 1.05%, #ff7e55 98.1%);
  border-radius: 10 * @rem;
  overflow: hidden;
  color: #fff;
  box-sizing: border-box;
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16 * @rem;
  }
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      font-weight: 600;
    }
    .right {
      display: flex;
      .button {
        width: 60 * @rem;
        height: 24 * @rem;
        margin-left: 20 * @rem;
        background: #ffffff;
        box-shadow: 0 * @rem 0 * @rem 4 * @rem 0 * @rem rgba(255, 22, 22, 0.1);
        border-radius: 22 * @rem 22 * @rem 22 * @rem 22 * @rem;

        color: #fe743a;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .center {
    flex: 1;
    display: flex;
    font-size: 26 * @rem;

    font-weight: 600;
    align-items: center;
    span {
      margin-right: 5 * @rem;
      font-size: 20 * @rem;
    }
  }
  .bottom {
    display: flex;
    .left {
      margin-right: 20 * @rem;
    }
  }
  .bottom-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40 * @rem;
    padding: 0 16 * @rem;
    background: linear-gradient(136deg, #ffc875 0%, #ff7e55 100%);
    .button {
      width: 64 * @rem;
      height: 24 * @rem;
      background-color: #fff;
      color: #fe743a;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 22 * @rem;
    }
  }
}
.wrapper2 {
  width: 339 * @rem;
  height: 28 * @rem;
  margin: 0 auto 10 * @rem;
  background: rgba(250, 250, 251, 0.8);
  border-radius: 20 * @rem;
  color: #666666;
  .item {
    padding: 0 22 * @rem;
    height: 28 * @rem;
    display: flex;
    align-items: center;
  }
  span {
    color: #f04c32;
  }
}
.wrapper3 {
  width: 337 * @rem;
  height: 378 * @rem;
  margin: 0 auto;
  .image-bg('~@/assets/images/welfare/invite/invite_bg1.png');
  .title {
    margin-top: 110 * @rem;
    text-align: center;
    font-size: 18 * @rem;

    font-weight: 600;
  }
  img {
    width: 180 * @rem;
    height: 180 * @rem;
    margin: 36 * @rem auto 0;
  }
}
.wrapper4 {
  margin: 20 * @rem 18 * @rem;
  .button-container {
    margin-bottom: 10 * @rem;
    display: flex;
    justify-content: center;
    .button {
      width: 160 * @rem;
      height: 40 * @rem;
      background: @themeColor;
      border-radius: 40 * @rem;
      font-size: 16 * @rem;

      font-weight: 600;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .text {
    line-height: 18 * @rem;
    color: #666666;
    span {
      color: #305dff;
    }
  }
  .color-text {
    color: #f04c32;
  }
}
</style>
