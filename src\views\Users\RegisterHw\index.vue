<template>
  <div class="page login-page">
    <nav-bar-2 :placeholder="false" bgStyle="transparent">
      <template #right>
        <div class="phone-login" @click="goToLoginHw"> 用户登录 </div>
      </template>
    </nav-bar-2>
    <div class="top-banner">
      <div class="page-title">账号注册</div>
    </div>
    <form class="form">
      <!-- 邮箱 -->
      <div class="field">
        <div class="field-icon user-icon"></div>
        <input
          type="text"
          v-model="email"
          placeholder="请输入邮箱"
          @focus="onEmailFocus"
          @blur="onEmailBlur"
        />
        <div v-if="email != ''" class="clear" @click="email = ''"></div>

        <div class="email-tip-list" v-if="showEmailTip">
          <div
            class="email-tip-item btn"
            v-for="item in emailTipList"
            :key="item.id"
            @click.stop="selectEmailTipItem(item.content)"
          >
            <div class="email-tip-name" v-html="item.showContent"></div>
          </div>
        </div>
      </div>
      <!-- 验证码 -->
      <div class="field" v-if="use_email_code">
        <div class="field-icon authcode-icon"></div>
        <input type="text" v-model="authCode" placeholder="输入验证码" />
        <div class="right">
          <div
            class="code-btn"
            :class="{ disabled: !email }"
            v-if="!ifCount"
            @click="getAuthCode()"
          >
            {{ $t('获取验证码') }}
          </div>
          <div class="code-btn disabled" v-else>
            {{ `${$t('重新获取')}${countdown}s` }}
          </div>
        </div>
      </div>
      <!-- 密码 -->
      <div class="field">
        <div class="field-icon lock-icon"></div>
        <input
          :type="eyeOpen ? 'text' : 'password'"
          v-model="password"
          placeholder="请输入密码"
        />
        <div class="right">
          <div
            v-if="password.length > 0"
            class="eyes"
            :class="{ open: eyeOpen }"
            @click="clickEyeBtn()"
          ></div>
        </div>
      </div>
      <!-- 重复密码 -->
      <div class="field">
        <div class="field-icon lock-icon"></div>
        <input
          :type="eyeOpen ? 'text' : 'password'"
          v-model="password2"
          placeholder="请再次输入密码"
        />
        <div class="right">
          <div
            v-if="password2.length > 0"
            class="eyes"
            :class="{ open: eyeOpen }"
            @click="clickEyeBtn()"
          ></div>
        </div>
      </div>
    </form>
    <div class="button" :class="{ on: canSubmit }" @click="commit()">
      注册
    </div>

    <div class="explain">
      <input id="checkbox" type="checkbox" v-model="ifAgreement" />
      <label for="checkbox">{{ $t('登录即代表您已同意') }}</label>
      <div
        class="link"
        @click="handleLink($h5Page.yonghuxieyi, $t('用户协议'))"
      >
        《{{ $t('用户协议') }}》
      </div>
      {{ $t('与') }}
      <div
        class="link"
        @click="handleLink($h5Page.yinsizhengce, $t('隐私政策'))"
      >
        《{{ $t('隐私政策') }}》
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiUserRegister,
  ApiMailSend,
  ApiUserRegByEmailInfo,
} from '@/api/views/users';
import { loginSuccess, getQueryVariable } from '@/utils/function';

import { mapGetters } from 'vuex';

import { isWebApp } from '@/utils/userAgent.js';

import useConfirmAgreement from '@/components/yy-confirm-agreement/index.js';

export default {
  name: 'Login',
  data() {
    return {
      isWebApp,
      email: '',
      authCode: '',
      password: '',
      password2: '',
      countdown: 60,
      ifCount: false,
      ifAgreement: false, //是否勾选协议
      eyeOpen: false, //是否显示密码
      showEmailTip: false,
      email_suffix: [],
      use_email_code: 0,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    canSubmit() {
      const commonCheck =
        this.email && this.password && this.password2 && this.ifAgreement;
      if (this.use_email_code == 1) {
        return commonCheck && this.authCode;
      } else {
        return commonCheck;
      }
    },
    // 格式化和过滤邮箱后缀提示
    emailTipList() {
      if (!this.email.trim()) return;
      const email_prefix = this.email.split('@')[0];
      const email_suffix = this.email.split('@')[1] ?? '';
      return this.email_suffix
        .filter(item => {
          return item.includes(email_suffix.toLowerCase());
        })
        .map(item => {
          const showPrefix =
            email_prefix.length > 16
              ? `${email_prefix.slice(0, 8)}...${email_prefix.slice(-8)}`
              : email_prefix;
          let showContent = `${showPrefix}@${item}`;
          if (
            `${email_prefix}@${email_suffix}`.toLowerCase() ===
            `${email_prefix}@${item}`.toLowerCase()
          ) {
            showContent = `<span>${showPrefix}@${item}</span>`;
          }
          return {
            suffix: item,
            content: `${email_prefix}@${item}`,
            showContent: `${showContent}`,
          };
        });
    },
  },
  async created() {
    await this.getRegisterInfo();
  },
  methods: {
    onEmailFocus() {
      this.showEmailTip = true;
    },
    onEmailBlur() {
      setTimeout(() => {
        this.$nextTick(() => {
          this.showEmailTip = false;
        });
      }, 500);
    },
    async getRegisterInfo() {
      const res = await ApiUserRegByEmailInfo();
      this.email_suffix = res.data.email_suffix;
      this.use_email_code = res.data.use_email_code;
    },
    selectEmailTipItem(item) {
      this.email = item;
      this.showEmailTip = false;
    },
    // 获取邮箱验证码
    getAuthCode() {
      if (this.email === '') {
        this.$toast('请输入邮箱');
        return false;
      }
      // 发送axios请求
      let params = {
        email: this.email,
        type: 2,
      };
      ApiMailSend(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
    goToLoginHw() {
      this.toPage('LoginHw');
    },
    clickEyeBtn() {
      this.eyeOpen = !this.eyeOpen;
    },
    clickEyeBtn2() {
      this.eyeOpen2 = !this.eyeOpen2;
    },
    handleLink(link, title) {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: link,
          title: title,
        },
      });
    },
    toPage(page) {
      if (['LoginHw', 'RegisterHw'].includes(page)) {
        this.$router.replace({ name: page });
      } else {
        this.$router.push({ name: page });
      }
    },
    async commit() {
      if (this.email === '') {
        this.$toast('请输入邮箱!');
        return false;
      }
      if (this.password === '') {
        this.$toast('请输入密码!');
        return false;
      }
      if (this.password !== this.password2) {
        this.$toast('两次密码输入不一致');
        return false;
      }
      const hadAgree = await useConfirmAgreement(this.ifAgreement);
      if (!hadAgree) return;

      this.ifAgreement = true;

      const toast1 = this.$toast.loading({
        message: '注册中...',
        forbidClick: true,
        duration: 0,
      });
      let params = {
        email: this.email,
        password: this.password,
        code: this.authCode,
      };
      if (this.isWebApp) {
        params.is_ios_standalone = 1;
      }
      if (getQueryVariable('inviterUserId')) {
        params.inviterUserId = getQueryVariable('inviterUserId');
      }
      ApiUserRegister(params).then(res => {
        loginSuccess(res);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.login-page {
  background-color: #f6f7f8;
  .phone-login {
    font-size: 14 * @rem;
    color: #000000;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 160 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: 600;
      line-height: 40 * @rem;
      margin-top: 120 * @rem;
      text-align: center;
    }
  }
  .form {
    padding: 0 16 * @rem;
    margin-top: 66 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 50 * @rem;
      position: relative;
      background: #ffffff;
      padding: 0 16 * @rem;
      border-radius: 25 * @rem;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      .email-tip-list {
        box-sizing: border-box;
        width: 100%;
        position: absolute;
        z-index: 2;
        top: 48 * @rem;
        left: 0;
        background-color: #fff;
        border-radius: 10 * @rem;
        box-shadow: 0 2 * @rem 2 * @rem 0 rgba(0, 0, 0, 0.1);
        padding: 0 10 * @rem;
        .email-tip-item {
          padding: 10 * @rem 5 * @rem 10 * @rem 40 * @rem;
          border-bottom: 1 * @rem solid #eee;
          .email-tip-name {
            font-size: 14 * @rem;
            color: #333;
            /deep/ span {
              color: @themeColor !important;
            }
          }
        }
      }
      .field-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background-size: 24 * @rem 24 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        &.user-icon {
          background-image: url('~@/assets/images/users/login-user-icon.png');
        }
        &.lock-icon {
          background-image: url('~@/assets/images/users/login-lock-icon.png');
        }
        &.email-icon {
          background-image: url('~@/assets/images/users/login-email-icon.png');
        }
        &.authcode-icon {
          background-image: url('~@/assets/images/users/login-verify-icon.png');
        }
      }
      input {
        flex: 1;
        min-width: 0;
        height: 100%;
        line-height: 50 * @rem;
        font-size: 14 * @rem;
        letter-spacing: 1 * @rem;
        padding: 0 16 * @rem;
      }
      .right {
        height: 100%;
        display: flex;
        align-items: center;
        .text {
          font-size: 14 * @rem;
          text-align: right;
          color: @themeColor;
        }
        .eyes {
          width: 18 * @rem;
          height: 50 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.open {
            height: 12 * @rem;
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
        .code-btn {
          width: 100 * @rem;
          height: 32 * @rem;
          border-radius: 16 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12 * @rem;
          background: @themeColor;
          margin-right: -7 * @rem;
          &.disabled {
            opacity: 0.5;
          }
        }
      }
      .clear {
        width: 16 * @rem;
        height: 50 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .down-arrow {
        width: 15 * @rem;
        height: 50 * @rem;
        background: url(~@/assets/images/users/arrow-down.png) center center
          no-repeat;
        background-size: 15 * @rem 6 * @rem;
        margin-left: 10 * @rem;
        &.show {
          transform: rotateX(180deg);
        }
      }
    }
  }
  .account-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16 * @rem;
    margin-top: 12 * @rem;
    .remember-password {
      display: flex;
      align-items: center;
      font-size: 13 * @rem;
      line-height: 20 * @rem;
      color: #323233;
      padding-left: 18 * @rem;
      background-position: left center;
      background-size: 13 * @rem 13 * @rem;
      background-repeat: no-repeat;
      background-image: url(~@/assets/images/users/remember-no.png);
      &.remember {
        background-image: url(~@/assets/images/users/remember-yes.png);
      }
    }
    .forget-password {
      font-size: 13 * @rem;
      color: @themeColor;
      line-height: 20 * @rem;
    }
  }
  .button {
    height: 44 * @rem;
    margin: 52 * @rem 16 * @rem 0;
    text-align: center;
    line-height: 44 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
    opacity: 0.5;
    transition: opacity 0.2s;
    &.on {
      opacity: 1;
    }
  }
  .explain {
    box-sizing: border-box;
    padding: 0 16 * @rem;
    display: flex;
    align-items: center;
    color: #323233;
    font-size: 12 * @rem;
    margin-top: 10 * @rem;
    height: 20 * @rem;
    line-height: 20 * @rem;
    input[type='checkbox'] {
      width: 14 * @rem;
      height: 14 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center center;
      background-size: 14 * @rem auto;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
}
</style>
