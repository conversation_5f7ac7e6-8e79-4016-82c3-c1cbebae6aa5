export default [
  {
    path: '/game_detail/:id',
    name: 'GameDetail',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameDetail2'),
    meta: {
      keepAlive: true,
      pageTitle: '游戏详情页',
    },
  },
  {
    path: '/cate_game_list/:basis_id/:cate_id',
    name: 'CateGameList',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/CateGameList'),
    meta: {
      keepAlive: true,
      pageTitle: '分类游戏列表',
    },
  },
  {
    path: '/more_game_list/:id', // 更多游戏
    name: 'MoreGameList',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/MoreGameList'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/game_detail/intro/:id',
    name: 'GameDetailIntro',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameDetailIntro'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/comment_editor/:class_id/:source_id',
    name: 'CommentEditor',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Feedback/CommentEditor'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/kaifu_detail/:id',
    name: 'KaifuDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/GameDetail2/KaifuDetail'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/game_gift/:game_id/:class_id?',
    name: 'GameGift',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameGift'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/game_exchange_code/:game_id',
    name: 'GameExchangeCode',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameExchangeCode'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/gift_detail/:game_id/:gift_id',
    name: 'GiftDetail',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GiftDetail'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/game_coupon/:game_id',
    name: 'GameCoupon',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameCoupon'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/game_news/:game_id',
    name: 'GameNews',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameNews'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/fanli_detail/:url/:game_id/:id',
    name: 'FanliDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/GameNews/FanliDetail'
      ),
    meta: {
      keepAlive: false,
    },
  },

  {
    path: '/fanli_apply/:id/:game_id',
    name: 'FanliApply',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/GameNews/FanliApply'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/news_fanli_history/:game_id',
    name: 'NewsFanliHistory',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/GameNews/NewsFanliHistory'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/game_collect/:id',
    name: 'GameCollect',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GameCollect'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/grq_list',
    name: 'GrqList',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/GrqList'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/cloud_game/:game_id',
    name: 'CloudGame',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/CloudGame'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/h5game/:h5Url',
    name: 'H5Game',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/H5Game'),
    meta: {
      requiresAuth: true,
      keepAlive: false,
      pageTitle: 'H5游戏',
    },
  },
  {
    path: '/new_game',
    name: 'NewGame',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/NewGame'),
    meta: {
      requiresAuth: false,
      keepAlive: true,
    },
  },
  {
    path: '/user_recommend',
    name: 'UserRecommend',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/UserRecommend'),
    meta: {
      requiresAuth: false,
      keepAlive: true,
    },
  },
  {
    path: '/user_recommend_detail/:id',
    name: 'UserRecommendDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/UserRecommend/UserRecommendDetail'
      ),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/notice_detail/:id',
    name: 'NoticeDetail',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/NoticeDetail'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/game_sign_in_list',
    name: 'GameSignInList',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/SignInList'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/game_sign_in_detail/:id',
    name: 'GameSignInDetail',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/SignInDetail'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/external_game_detail/:id',
    name: 'ExternalGameDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/ExternalGameDetail'
      ),
    meta: {
      keepAlive: true,
      pageTitle: '外部游戏详情',
    },
  },
  {
    path: '/external_game_gift_detail/:game_id/:gift_id',
    name: 'ExternalGameGiftDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/ExternalGameGiftDetail'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/external_game_collect/:id',
    name: 'ExternalGameCollect',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/ExternalGameCollect'
      ),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/developers_other_games/:game_id',
    name: 'DevelopersOtherGames',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/DevelopersOtherGames'
      ),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/emulator_game_detail/:id',
    name: 'EmulatorGameDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/EmulatorGameDetail'
      ),
    meta: {
      keepAlive: true,
      pageTitle: '模拟器游戏详情',
    },
  },
  {
    path: '/comment_emulator_game_detail/:sourceId/:id',
    name: 'CommentDetailGameDetail',
    component: () =>
      import(
        /* webpackChunkName: "games" */ '@/views/Games/EmulatorGameDetail/CommentDetail'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/pc_cloud_game_detail/:id',
    name: 'PcCloudGameDetail',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/PcCloudGameDetail'),
    meta: {
      keepAlive: true,
      pageTitle: 'PC云游戏详情',
    },
  },
  {
    path: '/recharge_tutorial/:id',
    name: 'RechargeTutorial',
    component: () =>
      import(/* webpackChunkName: "games" */ '@/views/Games/RechargeTutorial'),
    meta: {
      requiresAuth: false,
    },
  },
];
