<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item">
      <div
        class="game-info"
        @click.stop="
          $router.push({
            name: 'ExternalGameGiftDetail',
            params: { gift_id: couponItem.id, game_id: couponItem.game_id },
          })
        "
      >
        <div class="game-item-components">
          <div class="game-icon">
            <img :src="couponItem.titlepic" alt="" />
          </div>
          <div class="game-info-item">
            <div class="game-name">
              <div class="game-title">{{ couponItem.title }}</div>
            </div>
            <div class="game-m-title">
              <div class="line-container">
                <div
                  class="line"
                  :style="{
                    width: `${Math.floor(couponItem.remain)}%`,
                  }"
                ></div>
              </div>
              <span class="remain-info">
                剩余：
                <span>{{ couponItem.remain }}</span>
                %
              </span>
            </div>
            <div class="game-describe" v-if="couponItem.cardbody">
              {{ couponItem.cardbody }}
            </div>
          </div>
        </div>
      </div>
      <div class="game-quan-get">
        <div
          v-if="couponItem.remain != 0 && !couponItem.cardpass"
          class="get btn"
          @click.stop="getGift(couponItem)"
        >
          {{ $t('领取') }}
        </div>
        <div
          v-else-if="couponItem.remain == 0 && !couponItem.cardpass"
          class="get btn"
          :class="{
            already: couponItem.remain == 0,
          }"
        >
          {{ $t('已抢光') }}
        </div>
        <div
          v-else-if="couponItem.cardpass"
          class="get btn"
          @click="copy(couponItem.cardpass)"
        >
          复制
        </div>
      </div>
    </div>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">{{ $t('礼包码') }}</div>
      </div>
      <div class="cardpass">{{ cardInfo.cardpass }}</div>
      <div class="desc">{{ introduction }}</div>
      <div class="copy-btn btn" @click="copy(cardInfo.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
      :close-on-click-overlay="true"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span v-if="xiaohaoList.length">{{
                currentXiaohao.nickname
              }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div
              class="xiaohao-list"
              :class="{ on: xiaohaoListShow }"
              v-if="xiaohaoList.length"
            >
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :isExternalGame="false"
      :id="Number(couponItem.game_id)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
import { ApiCardRead, ApiCardGet } from '@/api/views/gift.js';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
export default {
  name: 'GameQuanItem',
  components: {
    xhCreateTipDialog,
  },
  props: {
    couponItem: {
      type: Object,
      default: () => {},
    },
    isShowTitlePic: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      item: {},
      exchangeGameInfo: [],
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      currentXiaohao: {}, //当前选择小号
      cardInfo: {}, // 礼包码信息
    };
  },
  methods: {
    async getGift(item) {
      this.exchangeGameInfo = item;
      // 先判断是否登录，未登录则跳转登录页
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (this.couponItem.remain == 0) {
          this.$toast(this.$t('礼包已被抢光啦~'));
          return false;
        }
        // await this.getXhList();
        // // 判断该游戏是否有小号，没有则弹出对话框
        // if (!this.xiaohaoList.length) {
        //   this.createDialogShow = true;
        //   return false;
        // }
        // 只有一个小号直接领取
        // if (this.xiaohaoList.length) {
        // this.currentXiaohao = this.xiaohaoList[0];
        this.$toast.loading({
          message: this.$t('加载中...'),
        });
        try {
          this.$nextTick(async () => {
            const getRes = await ApiCardGet({
              cardId: this.exchangeGameInfo.id,
              xhId: null,
            });
            this.$emit('calledAgainGetGift');
            this.cardInfo = getRes.data;
            this.SET_USER_INFO();
            this.$toast.clear();
            this.copyDialogShow = true;

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${this.couponItem.game_id}`,
              adv_id: '暂无',
              game_name: this.couponItem.titlegame,
              game_type: `${this.couponItem.classid}`,
              game_size: '暂无',
              reward_type: this.couponItem.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });
          });
        } catch (e) {
          this.$toast(e.msg);
        }
        return false;
        // }
      }
    },
    // 点击选择小号
    xiaoHaoListClick(item) {
      this.currentXiaohao = item;
      this.xiaohaoListShow = false;
    },
    // 关闭选择小号弹窗
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    // 选择完小号执行领取操作
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      // 开始领取操作
      let params = {
        gameId: this.exchangeGameInfo.game_id,
        xhId: this.currentXiaohao.id,
        cardId: this.exchangeGameInfo.id,
      };
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        this.$nextTick(async () => {
          const getRes = await ApiCardGet({
            cardId: this.exchangeGameInfo.id,
            xhId: this.currentXiaohao.id,
          });
          this.cardInfo = getRes.data;
          this.SET_USER_INFO();
          this.$toast.clear();
          this.copyDialogShow = true;

          // 神策埋点
          this.$sensorsTrack('game_rewards_claim', {
            game_id: `${this.couponItem.game_id}`,
            adv_id: '暂无',
            game_name: this.couponItem.titlegame,
            game_type: `${this.couponItem.classid}`,
            game_size: '暂无',
            reward_type: this.couponItem.title, // 传礼包名称
            data_source: this.$sensorsChainGet(),
          });
        });
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    // 获取小号列表
    async getXhList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.exchangeGameInfo.game_id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
      // 如果没有选择的小号，默认选择第一个
      if (!this.currentXiaohao?.id) {
        this.currentXiaohao = this.xiaohaoList[0];
      }
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
  computed: {
    introduction() {
      return `${this.$t('使用说明')}：${this.cardInfo.cardtext}`;
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  .game-quan-item {
    height: 64 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12 * @rem;
    .game-info {
      flex: 1;
      min-width: 0;

      .game-item-components {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        .game-icon {
          position: relative;
          flex: 0 0 64 * @rem;
          width: 64 * @rem;
          height: 64 * @rem;
          border-radius: 14 * @rem;
          background-color: #eeeeee;
        }
        .game-info-item {
          margin-left: 6 * @rem;
          margin-right: 17 * @rem;
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .game-name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .game-title {
              width: 106 * @rem;
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
            .game-subtitle {
              box-sizing: border-box;
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
              border: 1 * @rem solid #e5e1ea;
              color: #888888;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          .game-m-title {
            width: 186 * @rem;
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            margin: 7 * @rem 0;
            color: #999999;
            text-align: left;
            font-style: normal;
            text-transform: none;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            align-items: center;
            .line-container {
              width: 182 * @rem;
              height: 6 * @rem;
              line-height: 6 * @rem;
              border-radius: 2 * @rem;
              background-color: #e7e7e7;
              overflow: hidden;
              .line {
                width: 0%;
                height: 6 * @rem;
                border-radius: 2 * @rem;
                background-color: green;
              }
            }
            .remain-info {
              margin-left: 5 * @rem;
              white-space: nowrap;
              min-width: 50 * @rem;
              width: auto;
              flex-shrink: 0;
              > span {
                color: #ff4f4f;
              }
            }
          }
          .game-describe {
            width: 190 * @rem;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #666666;
            line-height: 14 * @rem;
            text-align: left;
            font-style: normal;
            text-transform: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
          }
        }
      }
    }
    .game-quan-get {
      width: 58 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
      .get {
        font-weight: 500;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 58 * @rem;
        height: 28 * @rem;
        background: #5abf73;
        border-radius: 29 * @rem;
        margin-top: 4 * @rem;
        &.already {
          background-color: #e4e4e4;
          color: #9a9a9a;
          border-color: #e4e4e4;
        }
      }
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 244 * @rem;
  border-radius: 12 * @rem;
  background-color: #fff;
  padding: 20 * @rem 16 * @rem 22 * @rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .title-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      .image-bg('~@/assets/images/games/gift-title-icon.png');
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 4 * @rem;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 209 * @rem;
    height: 39 * @rem;
    background-color: #f4f4f4;
    border-radius: 6 * @rem;
    display: flex;
    align-items: center;
    padding: 0 10 * @rem;
    margin-top: 13 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    font-weight: 400;
    word-break: break-all;
  }
  .desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    font-weight: 400;
    margin-top: 13 * @rem;
    padding: 0 5 * @rem;
  }
  .copy-btn {
    width: 186 * @rem;
    height: 36 * @rem;
    margin: 20 * @rem auto 0;
    background: @themeBg;
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
