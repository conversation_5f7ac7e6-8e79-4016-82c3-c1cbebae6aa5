<template>
  <pull-refresh
    @refresh="onRefresh"
    v-model="loadingController.reloading"
    v-bind="$attrs"
    :isPullRefresh="isPullRefresh"
  >
    <content-empty
      v-if="empty"
      :tips="tips"
      :emptyImg="emptyImg"
    ></content-empty>

    <load-more
      v-else
      v-model="loadingController.loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="check"
    >
      <slot> </slot>
    </load-more>
  </pull-refresh>
</template>

<script>
import emptyImg from '@/assets/images/empty.png';
export default {
  name: 'YyList',
  model: {
    prop: 'loadingController',
    event: 'change',
  },
  props: {
    empty: {
      type: Boolean,
      default: false,
    },
    emptyImg: {
      type: String,
      default: emptyImg,
    },
    finished: {
      type: Boolean,
      default: false,
    },
    check: {
      type: Boolean,
      default: true,
    },
    isPullRefresh: {
      type: Boolean,
      default: false,
    },
    loadingController: {
      type: Object,
      default: () => {
        return {
          reloading: false,
          loading: false,
        };
      },
    },
    tips: {
      type: String,
      default: '暂无数据',
    },
  },
  data() {
    return {
      loadingObj: this.loadingController,
    };
  },
  watch: {
    loadingObj(newVal) {
      this.$emit('change', newVal);
    },
    loadingController(newVal) {
      this.loadingObj = newVal;
    },
  },
  methods: {
    onRefresh() {
      this.$emit('refresh');
    },
    loadMore() {
      this.$emit('loadMore');
    },
  },
};
</script>
