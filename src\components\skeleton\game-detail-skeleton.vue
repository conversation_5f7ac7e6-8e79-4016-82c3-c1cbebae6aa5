<template>
  <div class="game-detail-skeleton">
    <!-- 顶部封面图骨架 -->
    <div class="top-banner-skeleton">
      <div class="banner-img"></div>
      <div class="banner-tab-bar">
        <div class="tab-box"></div>
      </div>
    </div>

    <!-- 游戏信息骨架 -->
    <div class="game-info-skeleton">
      <div class="game-top-info">
        <div class="left-info">
          <div class="game-img"></div>
          <div class="game-msg">
            <div class="game-name"></div>
            <div class="game-version"></div>
            <div class="game-time"></div>
          </div>
        </div>
        <div class="right-info">
          <div class="score-box"></div>
          <div class="number-box"></div>
        </div>
      </div>

      <!-- 游戏标签骨架 -->
      <div class="game-tags">
        <div class="tag"></div>
        <div class="tag"></div>
        <div class="tag"></div>
        <div class="tag"></div>
      </div>

      <!-- 福利区域骨架 -->
      <div class="welfare-box-skeleton"></div>

      <!-- 福利列表骨架 -->
      <div class="welfare-list">
        <div class="welfare-item"></div>
        <div class="welfare-item"></div>
        <div class="welfare-item"></div>
        <div class="welfare-item"></div>
      </div>
    </div>

    <!-- 选项卡区域骨架 -->
    <div class="tab-container-skeleton">
      <div class="tabs">
        <div class="tab"></div>
        <div class="tab"></div>
        <div class="tab"></div>
        <div class="tab"></div>
      </div>

      <!-- 详情内容骨架 -->
      <div class="tab-content">
        <!-- 独家福利骨架 -->
        <div class="section">
          <div class="section-title"></div>
          <div class="benefits-list">
            <div class="benefits-item"></div>
          </div>
        </div>

        <!-- 福利公告骨架 -->
        <div class="section">
          <div class="section-title"></div>
          <div class="notice-list">
            <div class="notice-item"></div>
            <div class="notice-item"></div>
          </div>
        </div>

        <!-- 游戏介绍骨架 -->
        <div class="section">
          <div class="section-title"></div>
          <div class="introduction-text">
            <div class="text-line"></div>
            <div class="text-line"></div>
            <div class="text-line"></div>
            <div class="text-line"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮骨架 -->
    <div class="bottom-container-skeleton">
      <div class="bottom-fixed">
        <div class="comment"></div>
        <div class="download-bar">
          <div class="download-btn"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';

export default {
  name: 'GameDetailSkeleton',
  data() {
    return {
      remNumberLess,
    };
  },
};
</script>

<style lang="less" scoped>
@import '~@/common/styles/_variable.less';

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton-bg {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

.game-detail-skeleton {
  background-color: #fff;
  min-height: 100vh;

  // 顶部封面图骨架
  .top-banner-skeleton {
    width: 100%;
    height: 301 * @rem;
    position: relative;
    overflow: hidden;

    .banner-img {
      .skeleton-bg;
      width: 100%;
      height: 100%;
    }

    .banner-tab-bar {
      position: absolute;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      bottom: 21 * @rem;
      left: 14 * @rem;

      .tab-box {
        .skeleton-bg;
        width: 84 * @rem;
        height: 20 * @rem;
        border-radius: 20 * @rem;
      }
    }
  }

  // 游戏信息骨架
  .game-info-skeleton {
    position: relative;
    top: -14 * @rem;
    border-radius: 12 * @rem 12 * @rem 0 0;
    background: #ffffff;
    padding: 14 * @rem 12 * @rem 0;
    z-index: 1;

    .game-top-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left-info {
        display: flex;
        align-items: center;

        .game-img {
          .skeleton-bg;
          flex-shrink: 0;
          width: 72 * @rem;
          height: 72 * @rem;
          border-radius: 16 * @rem;
        }

        .game-msg {
          margin-left: 10 * @rem;
          width: 190 * @rem;

          .game-name {
            .skeleton-bg;
            width: 150 * @rem;
            height: 20 * @rem;
            border-radius: 4 * @rem;
          }

          .game-version {
            .skeleton-bg;
            margin-top: 8 * @rem;
            width: 150 * @rem;
            height: 14 * @rem;
            border-radius: 4 * @rem;
          }

          .game-time {
            .skeleton-bg;
            margin-top: 8 * @rem;
            width: 150 * @rem;
            height: 14 * @rem;
            border-radius: 4 * @rem;
          }
        }
      }

      .right-info {
        .score-box {
          .skeleton-bg;
          width: 40 * @rem;
          height: 28 * @rem;
          border-radius: 4 * @rem;
        }

        .number-box {
          .skeleton-bg;
          margin-top: 4 * @rem;
          width: 40 * @rem;
          height: 10 * @rem;
          border-radius: 4 * @rem;
        }
      }
    }

    .welfare-box-skeleton {
      .skeleton-bg;
      margin-top: 10 * @rem;
      width: 351 * @rem;
      height: 33 * @rem;
      border-radius: 8 * @rem;
    }

    .game-tags {
      margin-top: 14 * @rem;
      display: flex;

      .tag {
        .skeleton-bg;
        width: 60 * @rem;
        height: 22 * @rem;
        border-radius: 5 * @rem;
        margin-right: 6 * @rem;
      }
    }

    .welfare-list {
      margin-top: 14 * @rem;
      display: flex;
      justify-content: space-between;

      .welfare-item {
        .skeleton-bg;
        width: 80 * @rem;
        height: 72 * @rem;
        border-radius: 8 * @rem;
      }
    }
  }

  // 选项卡区域骨架
  .tab-container-skeleton {
    padding: 0 12 * @rem;

    .tabs {
      display: flex;
      height: 44 * @rem;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;

      .tab {
        .skeleton-bg;
        width: 70 * @rem;
        height: 20 * @rem;
        border-radius: 4 * @rem;
      }
    }

    .tab-content {
      .section {
        padding: 20 * @rem 0 0 0;

        .section-title {
          .skeleton-bg;
          width: 100 * @rem;
          height: 25 * @rem;
          border-radius: 4 * @rem;
          margin-bottom: 14 * @rem;
        }

        .benefits-list {
          .benefits-item {
            .skeleton-bg;
            width: 100%;
            height: 70 * @rem;
            border-radius: 8 * @rem;
            margin-bottom: 10 * @rem;
          }
        }

        .notice-list {
          .notice-item {
            .skeleton-bg;
            width: 100%;
            height: 40 * @rem;
            border-radius: 8 * @rem;
            margin-bottom: 10 * @rem;
          }
        }

        .introduction-text {
          .text-line {
            .skeleton-bg;
            width: 100%;
            height: 14 * @rem;
            border-radius: 4 * @rem;
            margin-bottom: 8 * @rem;

            &:last-child {
              width: 80%;
            }
          }
        }
      }
    }
  }

  // 底部按钮骨架
  .bottom-container-skeleton {
    padding: 20 * @rem 0;

    .bottom-fixed {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9;
      height: 60 * @rem;
      display: flex;
      align-items: center;
      background: #fff;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;

      .comment {
        .skeleton-bg;
        width: 40 * @rem;
        height: 40 * @rem;
        border-radius: 8 * @rem;
        margin-left: 12 * @rem;
      }

      .download-bar {
        flex: 1;
        padding: 0 12 * @rem;

        .download-btn {
          .skeleton-bg;
          width: 100%;
          height: 40 * @rem;
          border-radius: 8 * @rem;
        }
      }
    }
  }
}
</style> 