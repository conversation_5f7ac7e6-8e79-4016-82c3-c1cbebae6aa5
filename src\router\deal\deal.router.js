export default [
  {
    path: '/role_transfer',
    name: 'RoleTransfer',
    component: () =>
      import(
        /* webpackChunkName: "switch_pages" */ '@/views/SwitchPages/RoleTransfer'
      ),
    meta: {
      keepAlive: true,
      pageTitle: '交易中心',
    },
  },

  {
    path: '/deal',
    name: 'Deal',
    component: () => import(/* webpackChunkName: "deal" */ '@/views/Deal'),
    meta: {
      keepAlive: true,
      pageTitle: '账号交易',
    },
  },
  {
    path: '/deal_trends',
    name: 'DealTrends',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/DealTrends'),
    meta: {
      keepAlive: false,
      pageTitle: '成交动态',
    },
  },
  {
    path: '/xiaohao_detail/:id',
    name: '<PERSON>haoDetail',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoDetail'),
    meta: {
      keepAlive: false,
      pageTitle: '小号详情',
    },
  },
  {
    path: '/xiaohao_sell_write',
    name: '<PERSON>haoSellWrite',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoSellWrite'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/xiaohao_sell_confirm',
    name: 'XiaohaoSellConfirm',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoSellConfirm'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/my_deal',
    name: 'MyDeal',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/MyDeal'),
    redirect: '/my_deal/my_buy_list',
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
    children: [
      {
        path: 'my_buy_list',
        name: 'MyBuyList',
        component: () =>
          import(
            /* webpackChunkName: "deal" */ '@/views/Deal/MyDeal/MyBuyList'
          ),
        meta: {
          keepAlive: false,
          requiresAuth: true,
          pageTitle: '我的交易',
        },
      },
      {
        path: 'my_sell_list',
        name: 'MySellList',
        component: () =>
          import(
            /* webpackChunkName: "deal" */ '@/views/Deal/MyDeal/MySellList'
          ),
        meta: {
          keepAlive: false,
          requiresAuth: true,
          pageTitle: '我的交易',
        },
      },
      {
        path: 'my_bargain_list',
        name: 'MyBargainList',
        component: () =>
          import(
            /* webpackChunkName: "deal" */ '@/views/Deal/MyDeal/MyBargainList'
          ),
        meta: {
          keepAlive: false,
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/select_game',
    name: 'SelectGame',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/SelectGame'),
    meta: {
      keepAlive: false,
      pageTitle: '选择游戏',
    },
  },
  {
    path: '/select_xiaohao',
    name: 'SelectXiaohao',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/SelectXiaohao'),
    meta: {
      keepAlive: false,
      pageTitle: '选择小号',
    },
  },
  {
    path: '/select_port',
    name: 'SelectPort',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/SelectPort'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/select_user',
    name: 'SelectUser',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/SelectUser'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/xiaohao_order_recharge',
    name: 'XiaohaoOrderRecharge',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoOrderRecharge'
      ),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/xiaohao_order_detail',
    name: 'XiaohaoOrderDetail',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoOrderDetail'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/search_game',
    name: 'SearchGame',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/SearchGame'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/xiaohao_manage',
    name: 'XiaohaoManage',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoManage'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      pageTitle: '小号管理',
    },
  },
  {
    path: '/game_xiaohao_list/:id',
    name: 'GameXiaohaoList',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/XiaohaoManage/GameXiaohaoList'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/bargain_list/:id',
    name: 'BargainList',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/BargainList'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/opening_account/:id',
    name: 'OpeningAccount',
    component: () =>
      import(/* webpackChunkName: "deal" */ '@/views/Deal/OpeningAccount'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/opening_account_explain',
    name: 'OpeningAccountExplain',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/OpeningAccount/OpeningAccountExplain'
      ),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/opening_account_record/:id',
    name: 'OpeningAccountRecord',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/OpeningAccount/OpeningAccountRecord'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/opening_account_buy/:id',
    name: 'OpeningAccountBuy',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/OpeningAccount/OpeningAccountBuy'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/opening_account_buy_success/:id',
    name: 'OpeningAccountBuySuccess',
    component: () =>
      import(
        /* webpackChunkName: "deal" */ '@/views/Deal/OpeningAccount/OpeningAccountBuySuccess'
      ),
    meta: {
      requiresAuth: true,
    },
  },
];
