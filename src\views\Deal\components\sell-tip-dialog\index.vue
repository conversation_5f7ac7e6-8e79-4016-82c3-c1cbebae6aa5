<template>
  <div>
    <!-- 卖家须知 -->
    <van-dialog
      v-model="sellTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="sell-tip-container">
        <div class="title">{{ $t('卖家须知') }}</div>
        <div class="content">
          <img
            class="banner"
            src="@/assets/images/deal/sell-tip-banner-2.png"
          />
          <div class="content-text">
            <p>
              {{
                $t(
                  '1、提交审核后,将在1-2个工作日内审核上架。且小号在该游戏中所有区服角色都将被冻结,无法登录。',
                )
              }}
            </p>
            <p>{{ $t('2、小号卖出前，卖家可自主下架并取消出售取回小号。') }}</p>
            <p>
              {{
                $t(
                  '3、若小号出售成功，则该小号在游戏中所有区服角色都将会—同出售。',
                )
              }}
            </p>
            <p>
              {{
                $t(
                  '4、出售将收取5%手续费(最低5元)。余款以平台币形式转至您账号下。',
                )
              }}<span>{{ $t('平台币不可提现仅能用于游戏充值。') }}</span>
            </p>
            <p>{{ $t('5、交易完成后，不支持找回。') }}</p>
          </div>
          <div class="agree" @click="sellTipAgree = !sellTipAgree">
            <div class="agree-btn" :class="{ yes: sellTipAgree }"></div>
            <div class="agree-text">{{ $t('我已认真阅读买家须知') }}</div>
          </div>

          <div class="code-container">
            <input
              class="code-input"
              type="number"
              :maxlength="6"
              v-model="sellCode"
              :placeholder="$t('请输入验证码')"
            />
            <div class="get-code btn" v-if="ifCount" @click="captchaClick()">
              {{ $t('获取验证码') }}
            </div>
            <div class="get-code getting" v-else>
              {{ `${$t('已发送')}(${countdown}s)` }}
            </div>
          </div>

          <div class="bottom-btn">
            <div class="back-btn btn" @click="sellTipShow = false">
              {{ $t('返回编辑') }}
            </div>
            <div class="confirm-btn btn" @click="handleSell">
              {{ $t('确认出售') }}
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { ApiAuthCode } from '@/api/views/users';
import {
  ApiXiaohaoCreateTrade,
  ApiXiaohaoEditTrade,
} from '@/api/views/xiaohao.js';
export default {
  name: 'SellTipDialog',
  model: {
    prop: 'sellShow',
    event: 'change',
  },
  props: {
    sellShow: {
      type: Boolean,
      default: false,
    },
    isRewrite: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sellTipAgree: false, // 同意卖家须知
      sellCode: '', // 出售验证码
      countdown: 60,
      ifCount: true,
      sellTipShow: this.sellShow,
      captcha: null,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
      initData: 'system/initData',
    }),
  },
  watch: {
    sellShow(val) {
      this.sellTipShow = val;
    },
    sellTipShow(val) {
      this.$emit('change', val);
    },
  },
  created() {
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    async handleSell() {
      if (this.sellTipAgree == false) {
        this.$toast(this.$t('请认真阅读卖家须知，并勾选'));
        return false;
      }
      if (!this.sellCode) {
        this.$toast(this.$t('请输入验证码'));
        return false;
      }
      let {
        xh_id,
        trade_id,
        price,
        game_area,
        role_name,
        title,
        secret,
        desc,
        images,
        videoUrl,
        specifyUser,
        play_from,
        is_h5,
      } = this.xiaohaoSellInfo;

      if (!this.isRewrite) {
        // 发布出售
        let params = {
          xhId: xh_id,
          price: price,
          title: title,
          smsCode: this.sellCode,
          gameArea: game_area,
          secret: secret,
          roleName: role_name,
          desc: desc,
          images: JSON.stringify(images),
          videoUrl: videoUrl,
          specifyUser: specifyUser,
        };
        if (is_h5 && play_from) {
          params.playFrom = play_from.id;
        }
        try {
          const res = await ApiXiaohaoCreateTrade(params);
          if (res.code == 1) {
            this.sellTipShow = false;
            this.$toast(res.msg);
            this.toPage('MySellList');
          }
        } finally {
          this.sellTipShow = false;
        }
      } else {
        // 重新编辑
        let params = {
          tradeId: trade_id,
          price: price,
          title: title,
          smsCode: this.sellCode,
          gameArea: game_area,
          secret: secret,
          roleName: role_name,
          desc: desc,
          images: JSON.stringify(images),
          videoUrl: videoUrl,
          specifyUser: specifyUser,
        };
        if (is_h5 && play_from) {
          params.playFrom = play_from.id;
        }
        try {
          const res = await ApiXiaohaoEditTrade(params);
          if (res.code == 1) {
            this.sellTipShow = false;
            this.$toast(res.msg);
            this.toPage('MySellList');
          }
        } finally {
          this.sellTipShow = false;
        }
      }
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.userInfo.mobile === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      let params = {
        phone: this.userInfo.mobile,
        countryCode: this.userInfo.country_code,
        type: 8,
      };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
  },
};
</script>

<style lang="less" scoped>
/* 卖家须知 */
.sell-tip-container {
  padding: 20 * @rem 18 * @rem;
  .title {
    font-size: 18 * @rem;
    color: #000000;
    letter-spacing: 2 * @rem;
    font-weight: 600;
    text-align: center;
  }
  .content {
    margin-top: 15 * @rem;
    .banner {
      height: 106 * @rem;
      margin: 10 * @rem auto 0;
    }
    .content-text {
      margin-top: 15 * @rem;
      p {
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        span {
          color: @themeColor;
        }
      }
    }
    .agree {
      display: flex;
      align-items: center;
      margin-top: 10 * @rem;
      .agree-btn {
        width: 12 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/deal/buy-tip-no.png) center center
          no-repeat;
        background-size: 12 * @rem 12 * @rem;
        &.yes {
          background-image: url(~@/assets/images/deal/buy-tip-yes.png);
        }
      }
      .agree-text {
        font-size: 13 * @rem;
        color: #757575;
        margin-left: 5 * @rem;
      }
    }
    .code-container {
      display: flex;
      align-items: center;
      margin-top: 12 * @rem;
      .code-input {
        flex: 1;
        min-width: 0;
        background-color: #000;
        height: 38 * @rem;
        display: flex;
        align-items: center;
        font-size: 13 * @rem;
        color: #333;
        margin-right: 12 * @rem;
        border-radius: 6 * @rem;
        background-color: #f4f4f4;
        padding: 0 16 * @rem;
      }
      .get-code {
        box-sizing: border-box;
        width: 110 * @rem;
        height: 38 * @rem;
        background: #fff;
        border: 1 * @rem solid @themeColor;
        border-radius: 6 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        color: @themeColor;
        &.getting {
          background: #ccc;
          border: 1 * @rem solid #ccc;
          color: #fff;
        }
      }
    }
    .bottom-btn {
      display: flex;
      align-items: center;
      margin: 20 * @rem auto 0;
      .back-btn {
        box-sizing: border-box;
        width: 50%;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid @themeColor;
        border-radius: 20 * @rem;
        color: @themeColor;
        font-size: 16 * @rem;
        font-weight: 600;
        margin-right: 20 * @rem;
      }
      .confirm-btn {
        width: 50%;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        font-weight: 600;
        color: #ffffff;
        background: @themeBg;
        border-radius: 20 * @rem;
      }
    }
  }
}
</style>
