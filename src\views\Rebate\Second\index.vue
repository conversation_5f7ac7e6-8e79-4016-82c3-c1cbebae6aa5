<template>
  <div class="rebate2">
    <nav-bar-2 :border="true" :title="$t('返利申请')">
      <template #right>
        <div class="kefu-icon" @click="toPage('Kefu')"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="tips">
        <div class="icon"></div>
        <div class="text">
          {{
            $t(
              '温馨提示：请准确填写信息，便于奖励发放。部分游戏不支持自助申请，请联系客服~',
            )
          }}
        </div>
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('游戏名') }}：</div>
        </div>
        <input :value="gameInfo.title" type="text" class="center" disabled />
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('游戏小号') }}：</div>
        </div>
        <input :value="smallAccountName" type="text" class="center" disabled />
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('角色名') }}：</div>
        </div>
        <input
          v-model="nickName"
          :placeholder="$t('请填写您充值的角色名')"
          type="text"
          class="center"
        />
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('游戏区服') }}：</div>
        </div>
        <input
          v-model="area"
          :placeholder="$t('请填写您充值的角色区服')"
          type="text"
          class="center"
        />
      </div>
      <div class="info-item">
        <div class="left">
          <div class="item-title">{{ $t('角色ID') }}：</div>
        </div>
        <input
          v-model="id"
          :placeholder="$t('请输入您充值的角色ID（有则必填）')"
          type="text"
          class="center"
        />
      </div>
      <div @click="toFanli()" class="question">
        <div class="text">{{ $t('如何查看角色ID') }}</div>
        <div class="icon"></div>
      </div>
      <section>
        <div class="text">{{ $t('申请备注') }}</div>
        <textarea
          v-model="textarea"
          :placeholder="
            $t('如有返利道具需要选择，请填写您所要的奖励(如：礼包B)')
          "
        ></textarea>
        <div class="small-text">
          {{ $t('充值金额') }}：<span class="color">{{ money }}</span>
        </div>
      </section>
    </div>
    <div class="bottom">
      <div @click="submit()" class="button">{{ $t('提交') }}</div>
      <div class="explain">
        {{
          $t(
            '提交返利申请后我们会第一时间为您审核发放，若您提交后再次充值需提交返利申请，可联系QQ',
          )
        }}：{{ initData.configs.kefu.qq }}。
      </div>
    </div>
  </div>
</template>
<script>
import { ApiRebateMyDayPay, ApiRebateSubmit } from '@/api/views/rebate';
import { mapGetters } from 'vuex';

export default {
  name: 'RebateSecond',
  data() {
    return {
      nickName: '',
      area: '',
      id: '',
      textarea: '',
      money: 0,
    };
  },
  activated() {
    this.init();
  },
  computed: {
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
      gameInfo: 'rebate/gameInfo',
      smallAccountName: 'rebate/smallAccountName',
      smallAccountId: 'rebate/smallAccountId',
      time: 'rebate/time',
      initData: 'system/initData',
    }),
  },
  methods: {
    async init() {
      try {
        const res = await ApiRebateMyDayPay({
          gameId: this.gameInfo.id,
          appId: this.gameInfo.app_id,
          date: this.time,
          xhId: this.smallAccountId,
        });
        this.money = res.data.sum;
      } catch {}
    },
    async submit() {
      if (!this.nickName) {
        this.$toast.fail(this.$t('请填写您充值的角色名'));
        return false;
      }
      if (!this.area) {
        this.$toast.fail(this.$t('请填写您充值的角色区服'));
        return false;
      }
      const res = await ApiRebateSubmit({
        gameId: this.gameInfo.id,
        date: this.time,
        xhId: this.smallAccountId,
        gameArea: this.area,
        gameRoleName: this.nickName,
        gameRoleId: this.id,
        contact: this.userInfoEx.qq ? this.userInfoEx.qq : '',
        remark: this.textarea,
      });
      this.$toast(res.msg);
    },
    toFanli() {
      this.toPage('Iframe', {
        title: this.$t('返利指南'),
        url: this.$h5Page.fanlizhinan,
      });
    },
  },
};
</script>
<style lang="less" scoped>
.kefu-icon {
  width: 18 * @rem;
  height: 20 * @rem;
  background-image: url(~@/assets/images/mine/icon_kefu_black.png);
  background-size: 100%;
}
.main {
  padding: 14 * @rem;
  .tips {
    display: flex;
    align-items: center;
    .icon {
      flex: 0 0 25.5 * @rem;
      width: 25.5 * @rem;
      height: 23 * @rem;
      margin-right: 10 * @rem;
      background-image: url(~@/assets/images/rebate/heart.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .text {
      line-height: 18 * @rem;
      font-size: 13 * @rem;
      color: #ff3158;
    }
  }
  .info-item {
    border-bottom: 1 * @rem solid #eeeeee;
    padding: 17 * @rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 0 0 100 * @rem;
      .item-title {
        font-size: 15 * @rem;
      }
    }
    .center {
      font-size: 14 * @rem;
      text-align: left;
      flex: 1;
    }
    input:disabled {
      color: #999999;
    }
    .right {
      font-size: 15 * @rem;
      color: #666666;
      display: flex;
      align-items: center;
      .right-icon {
        width: 6 * @rem;
        height: 10 * @rem;
        margin-left: 5 * @rem;
        background: url(~@/assets/images/right-icon.png) no-repeat;
        background-size: 6 * @rem 10 * @rem;
      }
      &.qq {
        font-size: 12 * @rem;
        color: @themeColor;
      }
    }
  }
  .question {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    margin-top: 10 * @rem;
    .icon {
      width: 16 * @rem;
      height: 16 * @rem;
      margin-right: 5 * @rem;
      background-image: url(~@/assets/images/rebate/question.png);
      background-size: 100%;
    }
    .text {
      color: #666666;
    }
  }
  section {
    .text {
      margin: 20 * @rem 0 10 * @rem;
      font-size: 15 * @rem;
    }
    textarea {
      width: 100%;
      height: 120 * @rem;
      padding: 10 * @rem;
      line-height: 21 * @rem;
      font-size: 15 * @rem;
      letter-spacing: 0.033375rem;
      color: #999;
      outline: none;
      resize: none;
      box-sizing: border-box;
      border: 1 * @rem solid #e5e5e5;
      border-radius: 2.5 * @rem;
    }
    .small-text {
      margin: 8 * @rem 0;
      font-size: 12 * @rem;
      color: #666666;
      .color {
        color: #ff0000;
      }
    }
  }
}
.bottom {
  box-sizing: border-box;
  width: 100%;
  margin-top: 30 * @rem;
  padding: 0 15 * @rem;
  background-color: #ffffff;
  overflow: hidden;
  .button {
    width: 100%;
    height: 45 * @rem;
    line-height: 45 * @rem;
    text-align: center;
    background: @themeBg;
    font-size: 16 * @rem;
    border-radius: 6 * @rem;
    color: #ffffff;
  }
  .explain {
    margin: 25 * @rem 0 20 * @rem;
    line-height: 21 * @rem;
    color: #999999;
    word-break: break-all;
  }
}
</style>
