// 引入神策分析 SDK
import Vue from 'vue';

import store from '@/store/index.js';

import { devLog, getQueryVariable } from '@/utils/function.js';

import BASEPARAMS from '@/utils/baseParams.js';

import { platform, authInfo } from '@/utils/box.uni.js';

import sensors from 'sa-sdk-javascript';

import Exposure from 'sa-sdk-javascript/dist/web/plugin/exposure/index.es6';

import h5Page from '@/utils/h5Page.js';

// 神策数据接收地址
const projectName = {
  'aa': 'default',
  'cc': 'production',
  '': 'production',
};
const serverUrl = `https://shence-data.a3733.com/sa?project=${projectName[h5Page.env]}`;

export const sensorsInit = function () {
  // 初始化神策分析
  sensors.init({
    server_url: serverUrl,
    heatmap: false,
    send_type: 'beacon', // 数据发送方式，默认为 image，可以设置为 beacon
    useClientTime: true, // 是否使用客户端时间，默认为 false

    show_log: getQueryVariable('sensors_log') == 1 ? true : false, // 是否在控制台打印日志，默认为 false
    max_string_length: 500, // 字符串类型数据最大长度，默认 500
    max_array_length: 100, // 数组类型数据最大长度，默认 100
    datasend_timeout: 3000, // 数据发送超时时间，默认 3000ms
    app_js_bridge: platform == 'android' ? true : false, // 是否启用 app_js_bridge，默认为 true，App打通H5
    use_app_track: false, // 是否开启应用内打点，默认为 false
    batch_send:
      platform == 'android'
        ? false
        : {
            // 批量发送配置，默认关闭
            datasend_timeout: 6000, //一次请求超过多少毫秒的话自动取消，防止请求无响应。
            send_interval: 6000, //间隔多少毫秒发一次数据。
            storage_length: 200, // 存储 localStorage 条数最大值，默认：200 。如 localStorage 条数超过该值，则使用 image 方式立即发送数据。v1.24.8 以上支持。
          },
  });

  sensors.use(Exposure, {
    area_rate: 1,
    stay_duration: 0,
    repeated: false, // 神策2.0修改为不重复曝光
  });

  // 注册公共属性
  sensors.registerPage({
    is_vip: function () {
      return store.getters['user/userInfo']?.is_svip ? true : false;
    },
    is_open_lqk: function () {
      return store.getters['user/userInfo']?.is_sqk_member ? true : false;
    },
    channel_source: function () {
      if (['android', 'ios'].includes(platform) && authInfo) {
        return authInfo.channel;
      }
      return BASEPARAMS.channel;
    },
  });
};

// 神策初始化后生成的数据
let presetProperties = {};
sensors.quick('isReady', function () {
  presetProperties = sensors.getPresetProperties();
});

/**
 *
 * @param {埋点事件} eventName
 * @param {埋点参数} properties
 * @param {前向对象/数组} sourceAgument [{type: 1, sourceName: 'source'}]
 *  -@param {安卓端时的来源（前向）字段} scouceName
 *  -@param {安卓前向类型} type 1:前向页 2:前向模块 2:先前向模块，没有再前向页
 * @returns
 */
function sensorsTrack(eventName, properties = {}, sourceAgument) {
  if (typeof eventName !== 'string') {
    devLog('==========SensorsTrack=============:埋点事件名必须为字符串');
    return;
  }
  if (platform === 'android') {
    if (sourceAgument) {
      // 判断sourceAgument是否为数组
      if (!Array.isArray(sourceAgument)) {
        sourceAgument = [sourceAgument];
      }
      sourceAgument.forEach(item => {
        if (item.type && item.sourceName) {
          properties[item.sourceName] = getAndroidSourceName(item.type);
        }
      });
    }
  }

  try {
    sensors.track(eventName, properties);
    devLog(`==========SensorsTrack=============:${eventName}`, properties);
  } catch (error) {
    devLog(
      `==========SensorsTrack=============:${eventName}事件埋点上报失败`,
      error,
    );
  }
}

// 获取安卓端前向页或前向模块
function getAndroidSourceName(type) {
  let result = '';
  try {
    switch (type) {
      // 安卓端前向页获取
      case 1:
        result = BOX.getSourceName();
        break;
      // 安卓端前向模块获取
      case 2:
        result = BOX.getSourceModuleName();
        break;
      // 先前向模块，没有再前向页
      case 3:
        result = BOX.getSourceModuleName();
        if (!result) {
          result = BOX.getSourceName();
        }
        break;
    }
  } catch (e) {
    result = '';
  }
  return result;
}

/**
 * chain设置，用于想sensors传递前向页面，如果有传参数newPage,则覆盖chain，
 * @param {string} newPage 新的页面名称，可选参数
 */
function sensorsChainSet(newPage) {
  if (newPage) {
    store.commit('sensors/setChainName', newPage);
  } else {
    store.commit('sensors/setChainName', '');
  }
}

function sensorsChainGet() {
  return store.getters['sensors/chainName'];
}

/**
 * Page设置，用于想sensors传递当前页面名称，
 * @param {string} newPage 新的页面名称，可选参数
 */
function sensorsPageSet(newPage) {
  if (newPage) {
    store.commit('sensors/setPageName', newPage);
  } else {
    store.commit('sensors/setPageName', '');
  }
}

function sensorsPageGet() {
  return store.getters['sensors/pageName'];
}

/**
 * 模块设置
 * @returns 模块名称
 */
function sensorsModuleGet() {
  let module = sessionStorage.getItem('SENSORS_MODULE') ?? '';
  if (module) {
    sessionStorage.removeItem('SENSORS_MODULE');
  }
  return module;
}

function sensorsModuleSet(newModule) {
  if (newModule) {
    sessionStorage.setItem('SENSORS_MODULE', newModule);
  }
}

// sensors实例
Vue.prototype.$sensors = sensors;

// sensors埋点方法
Vue.prototype.$sensorsTrack = sensorsTrack;

// sensors chain设置方法(设置前向页)
Vue.prototype.$sensorsChainSet = sensorsChainSet;

Vue.prototype.$sensorsChainGet = sensorsChainGet;

// sensors page设置方法(设置当前页)
Vue.prototype.$sensorsPageSet = sensorsPageSet;

Vue.prototype.$sensorsPageGet = sensorsPageGet;

Vue.prototype.$sensorsModuleSet = sensorsModuleSet;

Vue.prototype.$sensorsModuleGet = sensorsModuleGet;

Vue.directive('sensors-exposure', {
  bind (el, binding) {
    const prefix = binding.arg || 'data-sensors-exposure-'; // 使用指令参数作为前缀，默认为data-

    if (typeof binding.value === 'object' && binding.value !== null) {
      for (const [attrName, attrValue] of Object.entries(binding.value)) {
        el.setAttribute(`${prefix}${attrName}`, attrValue);
      }
    }
  },
});
export {
  sensors,
  sensorsTrack,
  sensorsChainSet,
  sensorsChainGet,
  sensorsPageSet,
  sensorsPageGet,
  presetProperties,
};
