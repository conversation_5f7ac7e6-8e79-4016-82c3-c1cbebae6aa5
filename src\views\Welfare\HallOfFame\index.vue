<template>
  <div class="hall-of-fame">
    <nav-bar-2
      title="名人堂"
      :bgStyle="navbarOpacity < 0.5 ? 'transparent-white' : 'transparent'"
      :placeholder="false"
      :azShow="true"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    >
    </nav-bar-2>
    <main>
      <section v-if="list1.length > 0" class="section1">
        <div class="small-text">（财富值总榜前10）</div>
        <div class="list">
          <div class="item th">
            <div class="left">排名</div>
            <div class="center">用户昵称</div>
            <div class="right">财富值称号</div>
          </div>
          <div v-for="(item, index) in list1" :key="index" class="item">
            <div class="left">{{ index + 1 }}</div>
            <div class="center">{{ item.nickname }}</div>
            <div class="right">{{ item.level_name }}</div>
          </div>
        </div>
      </section>
      <div v-if="list2.length > 0" class="title"></div>
      <section v-if="list2.length > 0" class="section2">
        <div class="small-text">（当周实付排行前10）</div>
        <div class="list list2">
          <div class="item th">
            <div class="left">排名</div>
            <div class="center">用户昵称</div>
            <div class="right">财富值称号</div>
          </div>
          <div v-for="(item, index) in list2" :key="index" class="item">
            <div class="left">{{ index + 1 }}</div>
            <div class="center">{{ item.nickname }}</div>
            <div class="right">{{ item.level_name }}</div>
          </div>
        </div>
      </section>
      <section v-if="list3.length > 0" class="section3">
        <div class="list list3">
          <div class="item th">
            <div class="left">排名</div>
            <div class="center">用户昵称</div>
            <div class="right">财富值称号</div>
          </div>
          <div v-for="(item, index) in list3" :key="index" class="item">
            <div class="left">{{ index + 1 }}</div>
            <div class="center">{{ item.nickname }}</div>
            <div class="right">{{ item.level_name }}</div>
          </div>
        </div>
      </section>
      <div class="explain">
        <div class="small-title"><i class="icon"></i>说明</div>
        <div class="text">
          1.名人堂将展示财富值总榜前10的用户，每周都将为在榜用户发放财富值奖励，奖励如下所示：<br />
          第1名：3733财富值<br />
          第2-3名：2888财富值<br />
          第4-10名：1888财富值<br />
          2.充值周榜将展示当周实付排行前10的用户，每周都将为在榜用户发放礼金奖励，奖励如下所示：<br />
          第1名：3733礼金<br />
          第2-3名：2888礼金<br />
          第4-10名：1888礼金<br />
          3.榜单每小时都将进行刷新，发放奖励人员名单取每周日23:59:59的榜上人员为最终人员名单，每周一0点将自动发放上周排行榜奖励，财富值奖励可在“财富值明细”里面查看，礼金奖励可在“我的礼金”里面查看<br />
          4.温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { ApiUserHallOfFame } from '@/api/views/users.js';

export default {
  name: 'HallOfFame',
  data() {
    return {
      navbarOpacity: 0,
      list1: [],
      list2: [],
      list3: [],
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    const res = await ApiUserHallOfFame();
    this.list1 = res.data.payLevelList;
    this.list2 = res.data.payList;
    this.list3 = res.data.previous_top;
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hall-of-fame {
  .image-bg('~@/assets/images/welfare/hall-of-fame/hof_bg1.png');
  background-color: rgba(255, 182, 106, 1);
}
main {
  padding-top: 300 * @rem;
  overflow: hidden;
  section {
    height: auto;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow: 0 * @rem 0 * @rem 20 * @rem 0 * @rem rgba(255, 0, 0, 0.2);
    opacity: 0.75;
    border-radius: 15 * @rem;
    margin: 15 * @rem 25 * @rem 25 * @rem;
    padding: 25 * @rem 15 * @rem 15 * @rem;
    box-sizing: border-box;
    position: relative;
    &::before {
      position: absolute;
      top: -20 * @rem;
      left: 50%;
      transform: translate(-50%);
      content: '';
      width: 176.46 * @rem;
      height: 41.78 * @rem;
    }
    &.section1::before {
      .image-bg('~@/assets/images/welfare/hall-of-fame/hof_title1.png');
    }
    &.section2::before {
      .image-bg('~@/assets/images/welfare/hall-of-fame/hof_title2.png');
    }
    &.section3::before {
      .image-bg('~@/assets/images/welfare/hall-of-fame/hof_title3.png');
    }
    .small-text {
      color: rgba(255, 98, 0, 0.7);
      line-height: 15 * @rem;
      text-align: center;
    }
    .list {
      .item {
        display: flex;
        justify-content: space-between;
        line-height: 30 * @rem;
        align-items: center;
        font-size: 15 * @rem;
        color: #ff8e48;
        &.th {
          margin-top: 12 * @rem;
          .left,
          .center,
          .right {
            font-weight: 600;
            color: #a22819;
          }
        }
        .left {
          flex: 0 0 50 * @rem;
          text-align: center;
        }
        .center {
          flex: 1;
          margin: 0 5 * @rem;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
        }
        .right {
          flex: 0 0 90 * @rem;
          text-align: center;
        }
      }
    }
  }
  .title {
    width: 342 * @rem;
    height: 27 * @rem;
    margin: 20 * @rem auto 25 * @rem;
    .image-bg('~@/assets/images/welfare/hall-of-fame/hof_title4.png');
  }
  .explain {
    margin: 15 * @rem 25 * @rem 65 * @rem;
    .small-title {
      font-size: 15 * @rem;
      font-weight: 600;
      color: #a22819;
      line-height: 21 * @rem;
      display: flex;
      align-items: center;
      margin-bottom: 5 * @rem;
      .icon {
        width: 14 * @rem;
        height: 14 * @rem;
        margin-right: 5 * @rem;
        .image-bg('~@/assets/images/welfare/hall-of-fame/hof_icon1.png');
      }
    }
    font-size: 14 * @rem;
    color: rgba(162, 40, 25, 0.7);
    line-height: 21 * @rem;
  }
}
</style>
