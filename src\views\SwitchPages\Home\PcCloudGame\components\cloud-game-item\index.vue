<template>
  <div class="simulator-zone-tabs-item">
    <div
      class="game-item-components"
      @click="toPage('GameDetail', { id: cloudGameInfo.game_id })"
    >
      <div class="game-icon">
        <img
          v-show="cloudGameInfo.verticalpic"
          :src="cloudGameInfo.verticalpic"
          alt=""
        />
      </div>
      <div class="game-info">
        <div class="game-name">{{ cloudGameInfo.title }}</div>
        <div class="tag-list" v-if="cloudGameInfo.new_cate_list">
          <div class="tags">
            <div
              class="tag"
              v-for="(item, index) in cloudGameInfo.new_cate_list"
              :key="index"
              >{{ item.title }}</div
            >
          </div>
        </div>
        <div class="info-bottom" v-if="cloudGameInfo.smalltext">{{
          cloudGameInfo.smalltext
        }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CloudGameItem',
  data() {
    return {
      cloudGameInfo: {},
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  watch: {
    info(val) {
      this.cloudGameInfo = val;
    },
  },
  created() {
    this.cloudGameInfo = this.info;
  },
};
</script>

<style lang="less" scoped>
.simulator-zone-tabs-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .game-item-components {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex: 1;
    min-width: 0;
    .game-icon {
      width: 72 * @rem;
      height: 97 * @rem;
      border-radius: 8 * @rem;
      background-color: #eee;
      overflow: hidden;
    }
    .game-info {
      margin-left: 12 * @rem;
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
      justify-content: space-evenly;
      .game-name {
        width: 254 * @rem;
        font-weight: 600;
        font-size: 15 * @rem;
        color: #111111;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .tag-list {
        display: flex;
        align-items: center;

        .tags {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-wrap: wrap;
          overflow: hidden;
          margin-top: 8 * @rem;
          height: 22 * @rem;
          line-height: 22 * @rem;
          .tag {
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4 * @rem;
            padding: 2 * @rem 4 * @rem;
            color: #868686;
            height: 18 * @rem;
            line-height: 18 * @rem;
            &:not(:first-child) {
              margin-left: 6 * @rem;
            }
          }
        }
      }
      .info-bottom {
        margin-top: 8 * @rem;
        height: 18 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #666666;
        line-height: 18 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &.score {
          font-weight: 600;
          color: #21b98a;
        }
        span {
          font-weight: 600;
          color: #21b98a;
        }
      }
    }
  }
  .game-btn {
    .btn {
      width: 58 * @rem;
      height: 28 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
      border-radius: 19 * @rem;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #ffffff;
      &.loading {
        position: relative;
        font-size: 0;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          animation: rotate 1s infinite linear;
        }
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
