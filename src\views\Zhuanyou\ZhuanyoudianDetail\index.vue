<template>
  <div class="zhuanyoudian-detail-page page">
    <nav-bar-2 :border="true" :title="$t('转游明细')"></nav-bar-2>
    <content-empty v-if="empty"></content-empty>
    <div class="list-container" v-else>
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="list">
          <div class="item" v-for="(item, index) in list" :key="index">
            <div class="first-line">
              <div class="title" :style="{ color: item.reason.color }">
                {{ item.reason.str }}
              </div>
              <div class="num" :class="{ get: Number(item.num) > 0 }">
                {{ Number(item.num) > 0 ? '+' + item.num : item.num }}
              </div>
            </div>
            <div class="second-line">
              <div class="date">{{ item.create_time | formatTime }}</div>
              <div class="game-name">{{ item.game_title }}</div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiZhuanyouZydRecord } from '@/api/views/zhuanyou.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'ZhuanyoudianDetail',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 20,
      empty: false,
      list: [],
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiZhuanyouZydRecord({
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.list = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.list.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.list.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.zhuanyoudian-detail-page {
  display: flex;
  flex-direction: column;
  .list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .list {
      .item {
        padding: 15 * @rem 0;
        margin: 0 18 * @rem;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        .first-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title {
            font-size: 15 * @rem;
            color: #000000;
            font-weight: 600;
          }
          .num {
            font-size: 15 * @rem;
            font-weight: 600;
            color: #47a83a;
            &.get {
              color: @themeColor;
            }
          }
        }
        .second-line {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 10 * @rem;
          .date,
          .game-name {
            font-size: 13 * @rem;
            color: #9a9a9a;
          }
        }
      }
    }
  }
}
</style>
