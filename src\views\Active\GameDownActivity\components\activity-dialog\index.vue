<template>
  <!-- 隐私政策未同意弹窗 -->
  <div>
    <van-dialog
      v-model="show"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-title" v-if="title">{{ title }}</div>
      <div class="popup-desc" v-if="desc" v-html="desc"></div>
      <div class="popup-content" v-if="content">
        {{ content }}
      </div>
      <div class="card-container" v-if="cardpass">
        <div class="card-title">礼包兑换码：</div>
        <div class="card-pass">{{ cardpass }}</div>
      </div>
      <div class="popup-tips" v-if="tips">
        {{ tips }}
      </div>
      <div class="operation-bar">
        <div
          class="btn operation-btn cancel"
          @click="onCancel"
          v-if="showCancel"
        >
          {{ cancelText }}
        </div>
        <div
          class="btn operation-btn confirm"
          @click="onConfirm"
          v-if="showConfirm"
        >
          {{ confirmText }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true,
      title: '提示',
      desc: '',
      content: '',
      cardpass: '',
      tips: '',
      cancelText: '取消',
      confirmText: '确定',
      showCancel: false,
      showConfirm: true,
    };
  },
  methods: {
    onCancel() {
      this.show = false;
    },
    onConfirm() {
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 300 * @rem;
  padding: 20 * @rem 31 * @rem;
  .popup-title {
    font-size: 16 * @rem;
    color: #333333;
    text-align: center;
    font-weight: 600;
    line-height: 40 * @rem;
    overflow: hidden;
    white-space: nowrap;
  }
  .popup-desc {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    text-align: center;
    margin-top: 10 * @rem;
  }
  .popup-content {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 70 * @rem;
    background-color: #f5f5f5;
    font-size: 15 * @rem;
    color: #333333;
    line-height: 20 * @rem;
    text-align: center;
    margin-top: 21 * @rem;
    border-radius: 8 * @rem;
    padding: 10 * @rem 10 * @rem;
  }
  .card-container {
    display: flex;
    align-items: center;
    margin-top: 20 * @rem;
    .card-title {
      font-size: 14 * @rem;
      color: #777777;
    }
    .card-pass {
      flex: 1;
      min-width: 0;
      margin-left: 2 * @rem;
      height: 30 * @rem;
      border-radius: 4 * @rem;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #000000;
    }
  }
  .popup-tips {
    box-sizing: border-box;
    font-size: 11 * @rem;
    color: #777777;
    line-height: 15 * @rem;
    text-align: center;
    margin-top: 10 * @rem;
  }
  .operation-bar {
    display: flex;
    align-items: center;
    margin: 29 * @rem 0 0;
    gap: 20 * @rem;
    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      font-size: 14 * @rem;
      &.confirm {
        background: linear-gradient(180deg, #ffb26b 0%, #ff3f4a 99%);
        color: #fff;
      }
      &.cancel {
        background: #f5f5f5;
        color: #7d7d7d;
      }
    }
  }
}
</style>
