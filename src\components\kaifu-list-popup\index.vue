<template>
  <div>
    <!-- 开服动态弹窗 -->
    <van-popup
      v-model="popupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      class="kaifu-list-popup"
    >
      <div class="container">
        <div class="title">开服动态</div>
        <div class="tips">开服动态仅供参考，以游戏内开服信息为准</div>
        <div class="kefu-btn">
          <div class="left-info">
            <div class="msg1">动态开服</div>
            <div class="msg2">联系客服获取最新的开服动态</div>
          </div>
          <div class="right-btn" @click="openKefu">
            <div class="icon"> </div>
            <span>联系客服</span>
          </div>
        </div>
        <div class="server-box">
          <div class="soon-server-list" v-if="soonOpenInfoList.length">
            <div class="title"> 即将开服 </div>
            <div class="kaifu-list">
              <div
                class="kaifu-item"
                v-for="(kaifu, kaifuIndex) in soonOpenInfoList"
                :key="kaifuIndex"
              >
                <div class="left">
                  <div class="time">
                    <div class="day">
                      {{
                        kaifu.is_today
                          ? $t('今日')
                          : $handleTimestamp(kaifu.newstime).date
                      }}
                    </div>
                    <div class="hour">
                      {{ $handleTimestamp(kaifu.newstime).time }}
                    </div>
                  </div>
                  <div class="quhao">{{ kaifu.state }}</div>
                </div>
                <div class="status had" v-if="kaifu.countdown_second < 0">
                  {{ $t('已开服') }}
                </div>
                <template v-else>
                  <div
                    class="status btn"
                    v-if="kaifu.status == 0"
                    @click="remind(kaifu.id, 1)"
                  >
                    {{ $t('提醒我') }}
                  </div>
                  <div
                    class="status btn"
                    v-if="kaifu.status == 1"
                    @click="remind(kaifu.id, 0)"
                  >
                    {{ $t('取消') }}
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="opened-server" v-if="openOverInfoList.length">
            <div class="title"> 已开服 </div>
            <div class="kaifu-list">
              <div
                class="kaifu-item"
                v-for="(kaifu, kaifuIndex) in openOverInfoList"
                :key="kaifuIndex"
              >
                <div class="left">
                  <div class="time">
                    <div class="day">
                      {{
                        kaifu.is_today
                          ? $t('今日')
                          : $handleTimestamp(kaifu.newstime).date
                      }}
                    </div>
                    <div class="hour">
                      {{ $handleTimestamp(kaifu.newstime).time }}
                    </div>
                  </div>
                  <div class="quhao">{{ kaifu.state }}</div>
                </div>
                <div class="status had" v-if="kaifu.countdown_second < 0">
                  {{ $t('已开服') }}
                </div>
                <template v-else>
                  <div
                    class="status btn"
                    v-if="kaifu.status == 0"
                    @click="remind(kaifu.id, 1)"
                  >
                    {{ $t('提醒我') }}
                  </div>
                  <div
                    class="status btn"
                    v-if="kaifu.status == 1"
                    @click="remind(kaifu.id, 0)"
                  >
                    {{ $t('取消') }}
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <van-dialog
      class="wx-popup"
      v-model="wx_popup.show"
      :lock-scroll="false"
      show-cancel-button
      confirmButtonText="设置微信提醒"
      @confirm="toPage('BindWeChat')"
    >
      <img class="bg" src="~@/assets/images/games/ic_subscribe_success.png" />
      <div class="big-text">设置微信提醒</div>
      <div class="small-text">
        开服提醒需先绑定微信，关注公众号后才可生效哦~
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiServerIndex, ApiServerRemind } from '@/api/views/game.js';
export default {
  name: 'kaifuListPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    gameId: {
      required: true,
    },
    soonOpenList: {
      type: Array,
      default: [],
    },
    openOverList: {
      type: Array,
      default: [],
    },
  },
  components: {},
  data() {
    return {
      openList: [],
      soonOpenInfoList: [], //即将开服
      openOverInfoList: [], //已开服
      finished: false,
      loading: false,
      empty: false,
      tips: '',
      wx_popup: {
        show: false,
      },
    };
  },
  async mounted() {
    this.soonOpenInfoList = this.soonOpenList;
    this.openOverInfoList = this.openOverList;
  },

  methods: {
    async remind(serverId, status) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        const res = await ApiServerRemind({
          serverId,
          status,
        });
        await this.getKaifuList();
        if (res.code > 0) {
          this.$toast(res.msg);
        }
      } catch (e) {
        if (e.code == -16) {
          this.wx_popup.show = true;
        }
      }
    },
    async getKaifuList() {
      const res = await ApiServerIndex({
        gameId: this.gameId,
      });
      if (!res.data.length) {
        this.empty = true;
      } else {
        this.openList = res.data;
        //根据countdown_second过滤对应的数据
        this.soonOpenInfoList = this.openList.filter(item => {
          return item.countdown_second > 0;
        });
        this.openOverInfoList = this.openList.filter(item => {
          return item.countdown_second < 0;
        });
      }
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.kaifu-list-popup {
  padding: 22 * @rem 12 * @rem 47 * @rem;
  max-height: 662 * @rem;
  overflow: hidden;
  box-sizing: border-box;
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      font-weight: 600;
      font-size: 16 * @rem;
      color: #191b1f;
    }
    .tips {
      margin-top: 12 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #60666c;
    }
    .kefu-btn {
      margin-top: 12 * @rem;
      width: 351 * @rem;
      height: 78 * @rem;
      background: #fafafa;
      border-radius: 8 * @rem;
      padding: 0 12 * @rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-info {
        display: flex;
        flex-direction: column;
        .msg1 {
          font-weight: 600;
          font-size: 15 * @rem;
          color: #191b1f;
        }
        .msg2 {
          margin-top: 8 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #93999f;
        }
      }
      .right-btn {
        min-width: 88 * @rem;
        height: 30 * @rem;
        background: #1cce94;
        border-radius: 8 * @rem;
        padding: 0 12 * @rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .icon {
          flex-shrink: 0;
          background: url('~@/assets/images/games/kefu-icon.png');
          width: 12 * @rem;
          height: 12 * @rem;
          background-size: 12 * @rem 12 * @rem;
        }
        span {
          flex-shrink: 0;
          min-width: 0;
          margin-left: 4 * @rem;
          font-weight: 500;
          font-size: 12 * @rem;
          color: #ffffff;
        }
      }
    }
    .server-box {
      width: 100%;
      max-height: 300 * @rem;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: none;
      }
      .soon-server-list,
      .opened-server {
        .title {
          font-weight: 600;
          font-size: 14 * @rem;
          color: #191b1f;
          margin: 20 * @rem 0 12 * @rem;
        }
      }
    }
  }
  .kaifu-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .kaifu-item {
      box-sizing: border-box;
      width: 169 * @rem;
      padding: 0 12 * @rem;
      background: #f5f5f6;
      border-radius: 8 * @rem;
      height: 64 * @rem;
      display: flex;
      align-items: center;
      &:not(:nth-of-type(-n + 2)) {
        margin-top: 10 * @rem;
      }
      .left {
        flex: 1;
        min-width: 0;
        .time {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: bold;
          display: flex;
          align-items: center;
          line-height: 20 * @rem;
          .hour {
            margin-left: 5 * @rem;
          }
        }
        .quhao {
          font-size: 12 * @rem;
          color: #9a9a9a;
          font-weight: 400;
          margin-top: 2 * @rem;
          line-height: 17 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .status {
        box-sizing: border-box;
        width: 54 * @rem;
        height: 30 * @rem;
        // border: 1 * @rem solid @themeColor;
        background: #e4fcf0;
        color: @themeColor;
        font-size: 11 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6 * @rem;
        &.had {
          border: 1 * @rem solid #c1c1c1;
          color: #fff;
          background-color: #c1c1c1;
        }
      }
    }
  }
}
.wx-popup {
  .bg {
    display: block;
    width: 150 * @rem;
    margin: 25 * @rem auto;
  }
  .big-text {
    font-size: 20 * @rem;
    text-align: center;
  }
  .small-text {
    margin: 15 * @rem 20 * @rem 30 * @rem;
    font-size: 14 * @rem;
    text-align: center;
  }
}
</style>
