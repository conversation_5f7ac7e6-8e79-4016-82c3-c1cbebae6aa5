<script>
import { ApiUserImgCertification, ApiUserInfoEx } from '@/api/views/users';
import { ApiUploadImage } from '@/api/views/system';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import md5 from 'js-md5';

export default {
  name: 'UploadIdCard',
  data() {
    return {
      realName: '',
      idCardNumber: '',
      auth_status: 0,
      active: false,
      dialogShow: false,
      authReward: '',
      imageFileList: [], // 上传截图资源
      images: [], // 上传截图链接
    };
  },
  computed: {
    activeText() {
      let str = '';
      switch (parseInt(this.auth_status)) {
        case 1:
          str = this.$t('审核中');
          break;
        case 2:
          str = this.$t('已认证');
          break;
        default:
          str = this.$t('确认');
          break;
      }
      return str;
    },
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
    }),
  },
  created() {
    this.auth_status = this.userInfoEx.auth_status;
    if (parseInt(this.auth_status) === 2 || parseInt(this.auth_status) === 1)
      this.active = true;
  },
  mounted() {
    if (this.userInfoEx.real_name != '') {
      this.realName = this.userInfoEx.real_name;
      this.idCardNumber = this.userInfoEx.id_card;
    }
  },
  methods: {
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
           this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    ...mapMutations({
      setUserInfoEx: 'user/setUserInfoEx',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    save() {
      if (this.active) {
        return false;
      }
      if (!this.images.length) {
        this.$toast(this.$t('请上传图片'));
        return false;
      }
      if (!this.realName) {
        this.$toast(this.$t('请输入姓名'));
        return false;
      }
      if (!this.idCardNumber) {
        this.$toast(this.$t('请输入身份证号码'));
        return false;
      }
      ApiUserImgCertification({
        realName: this.realName,
        idCard: this.idCardNumber,
        cardImg: JSON.stringify(this.images),
      }).then(res => {
        // this.$toast(res.msg);
        //返回的奖励图
        this.authReward = res.data.img;
        this.dialogShow = true;
      });
    },
    closeDialog() {
      this.dialogShow = false;
      ApiUserInfoEx().then(res2 => {
        this.SET_USER_INFO();
        this.setUserInfoEx(res2.data);
        this.$router.go(-1);
      });
    },
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.images.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
  },
};
</script>
<template>
  <div class="upload-idcard">
    <nav-bar-2 :border="true" :title="$t('图片认证')"> </nav-bar-2>
    <div class="main">
      <div class="text">{{ $t('请上传本人有效证件照片') }}</div>
      <div class="upload-container">
        <div :class="{ image: imageFileList.length === 0 }" class="photo">
          <van-uploader
            v-model="imageFileList"
            :after-read="afterRead"
            @delete="deletePic"
            :max-count="2"
            accept="image/*"
            :before-read="beforeRead"
            :multiple="true"
            :disabled="
              parseInt(auth_status) === 1 || parseInt(auth_status) === 2
            "
          >
          </van-uploader>
        </div>
        <div class="small-text">
          {{ $t('点击上传身份证/护照/其他有效证件照片') }}
        </div>
      </div>
      <div class="info">
        <div class="text">{{ $t('请填写本人信息') }}</div>
        <div class="change-content">
          <div class="form">
            <div class="field">
              <input
                type="text"
                v-model="realName"
                :placeholder="$t('请输入真实姓名')"
                :disabled="active"
              />
            </div>
            <div class="field">
              <input
                type="text"
                v-model="idCardNumber"
                :placeholder="$t('请输入身份证号码')"
                :disabled="active"
              />
            </div>
          </div>
        </div>
        <div class="save btn" @click="save" :class="{ active: active }">
          {{ activeText }}
        </div>
      </div>
    </div>
    <!-- 提交成功弹窗 -->
    <van-dialog
      v-model="dialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="false"
      class="dialog-show"
    >
      <div class="title">{{ $t('提交成功') }}</div>
      <div class="reward-pic">
        <img :src="authReward" alt="" />
      </div>
      <div class="tips">{{ $t('审核成功将自动发放认证奖励') }}</div>
      <div class="confirm-btn btn" @click="closeDialog">
        {{ $t('我知道了') }}
      </div>
    </van-dialog>
  </div>
</template>
<style lang="less" scoped>
.main {
  padding-bottom: 50 * @rem;
}
.upload-container {
  width: 319 * @rem;
  height: auto;
  margin: 20 * @rem auto 36 * @rem;
  box-shadow: 0 * @rem 0 * @rem 16 * @rem 1 * @rem rgba(2, 0, 116, 0.08);
  border-radius: 15 * @rem;
  overflow: hidden;
  .photo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 261 * @rem;
    margin: 20 * @rem auto 0;
    &.image {
      height: 192 * @rem;
      background-image: url(~@/assets/images/users/uploadidcard.png);
      background-repeat: no-repeat;
      background-size: 261 * @rem 192 * @rem;
    }
    /deep/ .van-uploader__upload {
      width: 220 * @rem;
      height: 160 * @rem;
      font-size: 74 * @rem;
      border-radius: 15 * @rem;
      background-color: rgb(246, 246, 246);
      margin: 0;
    }
    /deep/ .van-uploader__wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    /deep/ .van-uploader__preview {
      margin: 0 0 16 * @rem;
      border-radius: 15 * @rem;
      overflow: hidden;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
    /deep/ .van-uploader__preview-image {
      width: 220 * @rem;
      height: 160 * @rem;
    }
  }
  .small-text {
    margin: 20 * @rem 0;
    text-align: center;
    font-size: 14 * @rem;
    font-weight: 400;
    color: #666666;
    line-height: 18 * @rem;
  }
}
.info {
  border-top: 10 * @rem solid #f5f5f6;
  padding: 0 28 * @rem;
  .text {
    padding-left: 0;
  }
}
.text {
  margin-top: 24 * @rem;
  padding-left: 28 * @rem;
  font-size: 18 * @rem;
  font-family:
    PingFang SC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #444444;
  line-height: 23 * @rem;
}
.change-content {
  .form {
    margin-top: 10 * @rem;
  }
  .field {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 45 * @rem;
    margin-bottom: 20 * @rem;
    border-bottom: 1 * @rem solid #d5d3d3;
    input {
      width: 100%;
      height: 100%;
      padding: 0 10 * @rem;
      line-height: 44 * @rem;
      font-size: 16 * @rem;
      letter-spacing: 1 * @rem;
    }
  }
}
.save {
  width: 315 * @rem;
  height: 44 * @rem;
  font-size: 16 * @rem;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8 * @rem;
  margin: 50 * @rem auto 0;
  background: @themeBg;
  &.active {
    background: #c1c1c1;
  }
}
.dialog-show {
  box-sizing: border-box;
  width: 287 * @rem;
  background: #ffffff;
  border-radius: 16 * @rem;
  background-color: #fff;
  padding: 20 * @rem 0 24 * @rem;
  .title {
    font-size: 25 * @rem;
    color: @themeColor;
    font-weight: 600;
    text-align: center;
    line-height: 36 * @rem;
  }
  .reward-pic {
    width: 100%;
    height: 151 * @rem;
    margin-top: 8 * @rem;
  }
  .tips {
    font-size: 14 * @rem;
    color: #000000;
    font-weight: 500;
    text-align: center;
    margin-top: 22 * @rem;
  }
  .confirm-btn {
    width: 238 * @rem;
    height: 44 * @rem;
    background: @themeColor;
    border-radius: 22 * @rem;
    margin: 25 * @rem auto 0;
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
