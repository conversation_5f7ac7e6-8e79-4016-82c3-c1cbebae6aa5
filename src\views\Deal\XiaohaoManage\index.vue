<template>
  <div class="xiaohao-manage-page">
    <nav-bar-2 :border="true" :title="$t('小号管理')"></nav-bar-2>
    <main>
      <van-tabs
        v-model="active"
        swipeable
        animated
        :title-active-color="themeColorLess"
        title-inactive-color="#666666"
        :border="true"
        line-width="50%"
        :color="themeColorLess"
        line-height="2px"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :title="item.title"
        >
          <by-xiaohao v-if="index === 0"></by-xiaohao>
          <by-game v-else></by-game>
        </van-tab>
      </van-tabs>
    </main>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import byXiaohao from './ManageByXiaohao';
import byGame from './ManageByGame';
export default {
  name: 'XiaohaoManage',
  components: {
    byXiaohao,
    byGame,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
          title: this.$t('全部'),
        },
        {
          name: 'ByGame',
          title: this.$t('按游戏'),
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.xiaohao-manage-page {
  /deep/ .van-tab__pane {
    height: 100%;
  }
}
main {
  height: calc(100vh - 50 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
  /deep/ .van-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    .van-tabs__content {
      flex-grow: 1;
      flex-shrink: 0;
      min-height: 0;
    }
  }
  /deep/ .van-tabs--line .van-tabs__wrap {
    flex-shrink: 0;
    width: 100%;
    height: 44 * @rem;
    position: fixed;
    z-index: 2000;
    .fixed-center;
  }
}
</style>
