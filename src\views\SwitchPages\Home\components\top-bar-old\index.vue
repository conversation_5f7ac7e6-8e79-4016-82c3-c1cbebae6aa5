<template>
  <div class="top-bar-component">
    <div class="bg" :style="{ background: bgc }"></div>
    <div class="fixed">
      <status-bar
        bgColor="linear-gradient(to bottom, rgba(225, 225, 225, 1) 0%, rgba(255, 255, 255, 1) 100%)"
      ></status-bar>
      <div class="search-container">
        <div class="search-bar" @click="$router.push('/search')">
          <div class="icon"></div>
          <div class="search-text">{{ $t('搜你想玩的游戏') }}</div>
        </div>
        <div class="grq" @click="toDownManagement"></div>
        <div
          @click="toPage('Notice')"
          class="message"
          :class="{ dot: hasMessageDot }"
        ></div>
      </div>
      <div class="nav-list">
        <div
          class="nav-item"
          :class="{ active: current == index }"
          v-for="(item, index) in navList"
          :key="index"
          @click="handleClick(item.url, index)"
        >
          <span>{{ item.name }}</span>
        </div>
        <div
          class="line"
          :style="{
            left: `${current * 70 * remNumberLess}rem`,
            background: lineBg,
            width: `${12 * remNumberLess}rem`,
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import StatusBar from '@/components/status-bar';
import { BOX_showActivity } from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'Topbar',
  components: {
    StatusBar,
  },
  data() {
    return {
      remNumberLess, //安全区大小
      bgc: 'rgba(255,255,255,1)', //当前背景
      lineBg: 'rgba(254, 102, 0, 1)', //线颜色
      navList: [
        {
          name: this.$t('推荐'),
          url: '/home/<USER>',
        },
        {
          name: this.$t('排行榜'),
          url: '/home/<USER>',
        },
      ],
      current: 0,
    };
  },
  computed: {
    ...mapGetters({
      dotInfo: 'system/dotInfo',
    }),
    hasMessageDot() {
      let { feedback_read, reply_read, inform_read } = this.dotInfo;
      return !feedback_read && !reply_read && !inform_read ? false : true;
    },
  },
  watch: {
    $route(route) {
      this.current = this.navList.findIndex(item => {
        return item.url == route.path;
      });
    },
  },
  mounted() {
    this.current = this.navList.findIndex(item => {
      return item.url == this.$route.path;
    });
    // window.addEventListener("scroll", (e) => {
    //   let scrollTop =
    //     document.documentElement.scrollTop ||
    //     window.pageYOffset ||
    //     document.body.scrollTop;
    //   if (scrollTop > 10) {
    //     this.bgc = `rgba(255, 255, 255,${scrollTop / 80})`;
    //   } else {
    //     this.bgc = `rgba(255,255,255,0)`;
    //   }
    // });
  },
  methods: {
    handleClick(url, index) {
      this.$router.push(url);
    },
    toDownManagement() {
      BOX_showActivity(
        { name: 'GrqList', isAndroidBoxToNative: true },
        { page: 'yygl' },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.top-bar-component {
  .bg {
    position: fixed;
    left: 0;
    .fixed-center;
    top: 0;
    width: 100%;
    height: calc(90 * @rem + @safeAreaTop);
    height: calc(90 * @rem + @safeAreaTopEnv);
    z-index: 1999;
  }
  .fixed {
    box-sizing: border-box;
    height: calc(90 * @rem + @safeAreaTop);
    height: calc(90 * @rem + @safeAreaTopEnv);
    position: fixed;
    left: 0;
    .fixed-center;
    top: 0;
    width: 100%;
    z-index: 2000;
  }
  .search-bar {
    flex: 1;
    min-width: 0;
    box-sizing: border-box;
    padding: 0 19 * @rem;
    width: 310 * @rem;
    height: 35 * @rem;
    border-radius: 18 * @rem;
    background-color: #f5f5f6;
    display: flex;
    align-items: center;
    .icon {
      width: 14 * @rem;
      height: 14 * @rem;
      background: url(~@/assets/images/home/<USER>
        no-repeat;
      background-size: 14 * @rem 14 * @rem;
    }
    .search-text {
      font-size: 14 * @rem;
      color: #9a9a9a;
      margin-left: 7 * @rem;
    }
  }
  .nav-list {
    display: flex;
    align-items: flex-end;
    position: relative;
    margin: 7 * @rem 10 * @rem 0;
    .nav-item {
      padding: 5 * @rem 0;
      color: #000000;
      width: 70 * @rem;
      text-align: center;
      font-size: 16 * @rem;

      span {
        font-weight: 500;
      }
      &.active {
        font-size: 20 * @rem;
        span {
          font-weight: 600;
        }
      }
    }
    .line {
      position: absolute;
      left: 0;
      bottom: -3 * @rem;
      width: 18 * @rem;
      height: 3 * @rem;
      border-radius: 2 * @rem;
      transform: translateX(29 * @rem);
      // transition: 0.3s;
    }
  }
}
.search-container {
  display: flex;
  align-items: center;
  padding: 10 * @rem 18 * @rem 0;
  .grq {
    width: 17 * @rem;
    height: 18 * @rem;
    padding: 5 * @rem 5 * @rem 5 * @rem 15 * @rem;
    background-image: url(~@/assets/images/download-black.png);
    background-size: 17 * @rem;
    background-position: 16 * @rem 5 * @rem;
    background-repeat: no-repeat;
  }
  .message {
    padding: 5 * @rem;
    width: 17 * @rem;
    height: 18 * @rem;
    margin-left: 10 * @rem;
    background-size: 17 * @rem 18 * @rem;
    background-repeat: no-repeat;
    background-image: url(~@/assets/images/notice.png);
    background-position: center center;
    &.dot {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        right: 2 * @rem;
        top: 0 * @rem;
        width: 5 * @rem;
        height: 5 * @rem;
        border-radius: 50%;
        background-color: #fe4a55;
      }
    }
  }
}
</style>
