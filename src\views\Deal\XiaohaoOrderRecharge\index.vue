<template>
  <div class="xiaohao-order-recharge-page">
    <nav-bar-2 :border="true" :title="$t('支付中心')"></nav-bar-2>
    <div class="main">
      <div class="order-bar">
        <div class="order-item">
          <div class="order-title">{{ $t('订单金额') }}</div>
          <div class="order-value">
            <span><em>¥</em>{{ info.rmb }}</span>
          </div>
        </div>
        <div class="order-item">
          <div class="order-title">{{ $t('游戏名称') }}</div>
          <div class="order-value">{{ info.game_name }}</div>
        </div>
        <div class="order-item">
          <div class="order-title">{{ $t('区服') }}</div>
          <div class="order-value">{{ info.game_area }}</div>
        </div>
        <div class="order-item">
          <div class="order-title">{{ $t('小号ID') }}</div>
          <div class="order-value">{{ info.xh_id }}</div>
        </div>
        <div class="order-item">
          <div class="order-title">{{ $t('小号角色') }}</div>
          <div class="order-value">{{ info.role_name }}</div>
        </div>
      </div>
      <div class="recharge-container">
        <div class="pay-list" ref="payList" :class="{ showAll: showAll }">
          <div
            class="pay-item"
            v-for="(item, index) in showList"
            :key="index"
            @click="selectedPayType = item"
          >
            <div class="pay-center">
              <div class="line">
                <img class="item-icon" :src="item.icon" alt="" />
                <div class="pay-name">{{ item.name }}</div>
                <div class="recommend-icon" v-if="item.recommend">{{
                  $t('推荐')
                }}</div>
              </div>
              <div
                v-if="item.activity && item.activity_info?.act_tag"
                class="subtitle"
              >
                <span
                  v-for="(tag, tagIndex) in item.activity_info.act_tag"
                  :key="tagIndex"
                  >{{ tag }}</span
                >
              </div>
            </div>
            <div
              class="choose"
              :class="{ on: selectedPayType.symbol == item.symbol }"
            ></div>
          </div>
        </div>
        <div
          class="more-pay-list"
          @click="showAll = true"
          v-if="payList.length > 2 && !showAll"
        >
          {{ $t('更多支付方式') }}
        </div>
      </div>
      <div class="pay-btn btn" @click="handlePay" v-if="info.rmb">
        {{ $t('确认支付') }}{{ info.rmb }}元
      </div>
    </div>
  </div>
</template>

<script>
import {
  ApiXiaohaoPayUrl,
  ApiXiaohaoGetOrderRoleInfo,
} from '@/api/views/xiaohao.js';
import {
  ApiGetPayUrl,
  ApiGetPaymentMethod,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
import { getQueryVariable } from '@/utils/function.js';
import { mapGetters } from 'vuex';
import { platform, BOX_close } from '@/utils/box.uni.js';
export default {
  name: 'XiaohaoOrderRecharge',
  data() {
    return {
      payList: [],
      selectedPayType: '',
      orderInfo: {},
      orderId: '',
      order_type: 201, // 小号交易为201(默认)，小号捡漏为202
      backName: '',
      showAll: false,
      showList: [],
      xhInfo: {},
      gameInfo: {},
      info: {},
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  async activated() {
    // 传入订单信息
    if (platform === 'android') {
      this.orderId = Number(getQueryVariable('orderId')) || '';
      this.order_type = Number(getQueryVariable('orderType')) || 201;
    } else {
      if (this.$route.params.info) {
        this.orderInfo = this.$route.params.info;
        this.orderId = this.orderInfo.order_id;
      }
      // 传入返回页面的name
      if (this.$route.params.back) {
        this.backName = this.$route.params.back;
      }
      // 传入支付类型 ==> 小号交易为201，小号捡漏为202
      if (this.$route.params.order_type) {
        this.order_type = this.$route.params.order_type;
      }
      // 小号及游戏信息
      if (this.$route.params.xhInfo) {
        this.xhInfo = this.$route.params.xhInfo;
        this.gameInfo = this.xhInfo.game;
      }
    }
    if (!this.orderId) {
      this.$toast('订单不存在,请返回上一页重试');
      return false;
    }
    await this.getPayMethod();
    this.getOrderJLRoleInfo();

    // 神策埋点
    this.$sensorsTrack('altAccount_order_details_pageView');
  },
  beforeRouteLeave(to, from, next) {
    if (!this.backName || to.name == this.backName) {
      next();
    } else {
      next({ name: this.backName, replace: true });
    }
  },
  watch: {
    showAll(val) {
      if (val) {
        this.showList = this.payList;
      }
    },
  },
  methods: {
    async getOrderJLRoleInfo() {
      this.$toast.loading();
      let res = await ApiXiaohaoGetOrderRoleInfo({
        orderId: this.orderId,
        orderType: this.order_type,
      });
      this.info = res.data;
      this.$toast.clear();
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: this.order_type,
      });
      this.payList = res.data;
      this.showList = this.payList.slice(0, 2);
      this.selectedPayType =
        this.payList.find(item => {
          return item.recommend;
        }) || this.payList[0];
    },
    async handlePay() {
      // 神策埋点
      this.$sensorsTrack('altAccount_payment_submit', {
        game_id: `${this.info.game_id ?? ''}`,
        game_name: this.info.game_name,
        game_type: `${this.info.classid ?? ''}`,
        alt_account_price: Number(this.info.rmb),
      });

      // 接口有问题，只能用来判断是否可购买(实际请求最原始的payUrl接口)
      const res = await ApiXiaohaoPayUrl({
        orderId: this.orderId,
        orderType: this.order_type,
      });
      if (!res.data.pay_url) {
        this.$toast(res.msg);
        return false;
      }
      // 正式支付接口
      await ApiGetPayUrl({
        orderId: this.orderId,
        orderType: this.order_type,
        packageName: '',
        payWay: this.selectedPayType.symbol,
      }).finally(async () => {
        await ApiGetOrderStatus({
          order_id: this.orderId,
          order_type: this.order_type,
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-order-recharge-page {
  .main {
    background-color: #f7f8fa;
    overflow: hidden;
    min-height: calc(100vh - 50 * @rem - @safeAreaTop);
    min-height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    padding-bottom: 74 * @rem;
    box-sizing: border-box;

    .order-bar {
      margin: 16 * @rem 18 * @rem;
      padding: 14 * @rem 12 * @rem;
      background-color: #fff;
      border-radius: 8 * @rem;

      .order-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 14 * @rem;

        &:last-of-type {
          margin-bottom: 0;
        }

        .order-title {
          font-weight: 500;
          line-height: 20 * @rem;
          font-size: 14 * @rem;
          color: #444;
        }

        .order-value {
          font-size: 14 * @rem;
          line-height: 18 * @rem;
          font-weight: bold;
          text-align: right;
          color: #333;

          span {
            display: flex;
            align-items: center;
            color: #ff6649;
            em {
              font-size: 11 * @rem;
              margin-right: 4 * @rem;
            }
          }
        }

        &:nth-of-type(1) {
          border-top: 0;
        }
      }
    }

    .recharge-container {
      background-color: #fff;
      margin: 16 * @rem 18 * @rem;
      padding: 14 * @rem 12 * @rem;

      .pay-list {
        overflow: hidden;

        .pay-item {
          width: 100%;
          display: flex;
          box-sizing: border-box;
          margin: 0 auto 30 * @rem;

          &:last-of-type {
            margin-bottom: 0;
          }

          .pay-center {
            flex: 1;

            .line {
              display: flex;
              align-items: center;
              width: 100%;

              .item-icon {
                flex-shrink: 0;
                width: 20 * @rem;
                height: 20 * @rem;
                margin-right: 8 * @rem;
              }

              .pay-name {
                height: 21 * @rem;
                font-weight: 600;
                font-size: 15 * @rem;
                color: #222222;
                line-height: 21 * @rem;
                overflow: hidden;
              }

              .recommend-icon {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 34 * @rem;
                height: 18 * @rem;
                line-height: 18 * @rem;
                margin-left: 3 * @rem;
                background: #ff6649;
                border-radius: 4 * @rem;
                font-weight: 600;
                font-size: 11 * @rem;
                color: #ffffff;
                text-align: center;
              }
            }

            .subtitle {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              height: 18 * @rem;
              overflow: hidden;
              margin-left: 30 * @rem;
              margin-top: 4 * @rem;

              span {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 16 * @rem;
                line-height: 16 * @rem;
                font-weight: 400;
                font-size: 10 * @rem;
                color: #ff6649;
                text-align: center;
                border-radius: 4 * @rem;
                border: 1 * @rem solid rgba(252, 58, 59, 0.4);
                padding: 0 4 * @rem;
                margin-right: 6 * @rem;
              }
            }
          }

          .choose {
            flex-shrink: 0;
            width: 20 * @rem;
            height: 20 * @rem;
            border-radius: 50%;
            border: 1 * @rem solid #e5e6eb;
            box-sizing: border-box;

            &.on {
              background: url(~@/assets/images/recharge/pay-selected-circle.png)
                no-repeat;
              background-size: 20 * @rem 20 * @rem;
              border: none;
            }
          }
        }
      }

      .more-pay-list {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 13 * @rem;
        color: #666666;
        line-height: 18 * @rem;
        margin: 20 * @rem auto 0;

        &::after {
          content: '';
          display: block;
          width: 12 * @rem;
          height: 7 * @rem;
          background: url(~@/assets/images/recharge/gray-bottom-arrow.png)
            no-repeat;
          background-size: 12 * @rem 7 * @rem;
          margin-left: 4 * @rem;
          transition: all 0.2s linear;
        }
      }
    }

    .pay-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 355 * @rem;
      height: 44 * @rem;
      line-height: 44 * @rem;
      text-align: center;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #ffffff;
      background: #3cd279;
      border-radius: 6 * @rem;
      position: fixed;
      bottom: 30 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
