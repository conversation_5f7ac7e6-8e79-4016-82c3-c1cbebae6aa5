<template>
  <div class="game-item-components" @click="toDetail(gameInfo)">
    <div class="game-icon">
      <img :src="gameInfo.titlepic" :alt="gameInfo.title" />
    </div>
    <div class="game-info">
      <div class="game-title">
        <MarqueeContent>
          <div class="title-content">
            <div class="title-text">{{ gameInfo.main_title }}</div>
            <div class="title-tag" v-if="gameInfo.subtitle">{{
              gameInfo.subtitle
            }}</div>
          </div>
        </MarqueeContent>
      </div>
      <div class="game-center">
        <div class="game-hot">
          <img class="hot-icon" src="@/assets/images/games/hot-icon.png" />
          <div class="hot-num">{{ gameInfo.totaldown }}</div>
        </div>
        <div class="types" v-if="gameInfo.type?.length">
          <span
            class="type"
            v-for="(type, index) in gameInfo.type"
            :key="index"
            >{{ type }}</span
          >
        </div>
      </div>
      <div class="game-bottom">
        <div class="discount-tag" v-if="discountTag">
          <img
            class="discount-icon"
            src="@/assets/images/games/discount-normal.png"
          />
          <div class="discount-text"
            ><span>{{ discountTag }}</span
            >折直充</div
          >
        </div>
        <div class="tags">
          <div
            class="tag"
            v-for="(tag, index) in gameInfo.extra_tag"
            :key="index"
            >{{ tag.name }}</div
          >
        </div>
      </div>
    </div>
    <div class="game-btn" v-if="isDownload" @click.stop="openGame(gameInfo)"
      >打开</div
    >
    <div
      class="game-btn downloading"
      v-else-if="isDownloading.gameId && isDownloading.state == 3"
      @click.stop="downloadGame(gameInfo)"
      >{{ Math.ceil(Number(isDownloading.progress).toFixed(2) * 100) }}%</div
    >
    <div
      class="game-btn"
      v-else-if="
        isDownloading.gameId &&
        (isDownloading.state == 1 || isDownloading.state == 2)
      "
      >等待中</div
    >
    <div
      class="game-btn"
      v-else-if="isDownloading.gameId && isDownloading.state == 4"
      @click.stop="downloadGame(gameInfo)"
      >继续</div
    >
    <div
      class="game-btn"
      v-else-if="isDownloading.gameId && isDownloading.state == 5"
      @click.stop="downloadGame(gameInfo)"
      >安装</div
    >
    <div class="game-btn" v-else @click.stop="downloadGame(gameInfo)">下载</div>
  </div>
</template>

<script>
import {
  platform,
  BOX_openApp,
  BOX_goToGame,
  BOX_goToGameFanli,
  BOX_checkInstall,
  isSdk,
  isIosSdk,
  isAndroidSdk,
  BOX_showActivityByAction,
  BOX_startDownloadWithCallBack,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'GameItem',
  props: {
    gameInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isShowFanli: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      isSdk,
      isIosSdk,
      isAndroidSdk,
    };
  },
  computed: {
    discountTag() {
      if (this.gameInfo.classid != 107) {
        return '';
      }
      if (this.gameInfo.f_pay_rebate && this.gameInfo.f_pay_rebate != 100) {
        return `${parseFloat(this.gameInfo.f_pay_rebate) / 10}`;
      } else if (this.gameInfo.pay_rebate && this.gameInfo.pay_rebate != 100) {
        return `${parseFloat(this.gameInfo.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
    isDownload() {
      if (platform == 'android') {
        return BOX_checkInstall(this.gameInfo.package_name) ? true : false;
      } else {
        return false;
      }
    },
    isDownloading() {
      // state：  0、无状态  1、等待中 2、准备中 3、下载中 4、暂停 5、完成 6、错误 7、移除
      return (
        this.userDownload.find(item => {
          return item.gameId == this.gameInfo.id;
        }) || {}
      );
    },
    ...mapGetters({
      userDownload: 'user/userDownload',
    }),
  },
  methods: {
    // 打开详情页
    toDetail(gameInfo, isDownloadBtn = false) {
      //  BOX_showActivityByAction type 0、只打开游戏详情 1、打开游戏详情+返利  2、打开游戏详情+礼包  3、打开游戏详情+代金券
      if (this.isAndroidSdk) {
        let type = 0;
        if (this.isShowFanli && !isDownloadBtn) {
          type = 1;
        }
        try {
          BOX_showActivityByAction({
            action_code: 1000,
            extra_id: gameInfo.id,
            cate_id: gameInfo.class_id,
            type,
          });
        } catch (e) {
          BOX_goToGame({ params: { id: gameInfo.id } }, { id: gameInfo.id });
        }
      } else if (this.isIosSdk) {
        this.$toast('因iOS系统受限，请手动打开3733游戏盒查找对应游戏');
      } else if (this.isShowFanli && !isDownloadBtn) {
        let res = BOX_goToGameFanli(
          { params: { id: gameInfo.id } },
          { id: gameInfo.id, classid: gameInfo.classid },
        );
        if (!res) {
          BOX_goToGame({ params: { id: gameInfo.id } }, { id: gameInfo.id });
        }
      } else {
        BOX_goToGame({ params: { id: gameInfo.id } }, { id: gameInfo.id });
      }
    },
    openGame(gameInfo) {
      BOX_openApp(gameInfo.package_name);
    },
    downloadGame(gameInfo) {
      if (platform == 'android') {
        if (this.isAndroidSdk) {
          this.toDetail(gameInfo, true);
          return false;
        }
        let res = BOX_startDownloadWithCallBack(gameInfo);
        if (!res) {
          BOX_goToGame({ params: { id: gameInfo.id } }, { id: gameInfo.id });
        }
      } else {
        this.toDetail(gameInfo, true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-item-components {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .game-icon {
    width: 72 * @rem;
    height: 72 * @rem;
    border-radius: 10 * @rem;
    overflow: hidden;
  }
  .game-info {
    margin-left: 10 * @rem;
    min-width: 0;
    flex: 1;
    .game-title {
      display: flex;
      align-items: center;
      overflow: hidden;
      .title-content {
        display: flex;
        align-items: center;
      }
      .title-text {
        flex-shrink: 0;
        font-size: 15 * @rem;
        line-height: 21 * @rem;
        color: #191b1f;
        font-weight: 600;
        overflow: hidden;
        white-space: nowrap;
      }
      .title-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        border-radius: 4 * @rem;
        height: 18 * @rem;
        margin-left: 6 * @rem;
        font-size: 11 * @rem;
        color: #93999f;
        white-space: nowrap;
        padding: 0 4 * @rem;
        border: 1 * @rem solid #e3e5e8;
      }
    }
    .game-center {
      display: flex;
      align-items: center;
      margin-top: 6 * @rem;
      .game-hot {
        display: flex;
        align-items: center;
        margin-right: 6 * @rem;
        .hot-icon {
          width: 10 * @rem;
          height: 10 * @rem;
          margin-right: 3 * @rem;
        }
        .hot-num {
          font-size: 11 * @rem;
          color: #60666c;
          line-height: 14 * @rem;
        }
      }
      .types {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        height: 14 * @rem;
        line-height: 14 * @rem;
        overflow: hidden;
        .type {
          padding: 0 5 * @rem;
          position: relative;
          display: flex;
          align-items: center;
          color: #93999f;
          line-height: 14 * @rem;
          font-size: 11 * @rem;
          &:first-of-type {
            padding-left: 0;
          }
          &:not(:first-child) {
            &:before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1 * @rem;
              height: 10 * @rem;
              background-color: #93999f;
            }
          }
        }
      }
    }
    .game-bottom {
      display: flex;
      align-items: center;
      margin-top: 6 * @rem;

      .discount-tag {
        display: flex;
        align-items: center;
        width: fit-content;
        flex-shrink: 0;
        margin-right: 6 * @rem;

        .discount-icon {
          width: 27 * @rem;
          height: 18 * @rem;
          position: relative;
          z-index: 1;
        }
        .discount-text {
          display: flex;
          align-items: center;
          height: 18 * @rem;
          padding-right: 3 * @rem;
          flex: 1;
          min-width: 0;
          font-size: 11 * @rem;
          color: #ff6649;
          font-weight: 500;
          white-space: nowrap;
          background-color: #fff5ed;
          border-radius: 0 2 * @rem 2 * @rem 0;
          margin-left: -8 * @rem;
          padding-left: 8 * @rem;
        }
      }

      .tags {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        height: 18 * @rem;
        overflow: hidden;
        .tag {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 18 * @rem;
          background: #f7f8fa;
          border-radius: 2 * @rem;
          padding: 0 4 * @rem;
          margin-right: 6 * @rem;
          margin-bottom: 3 * @rem;
          white-space: nowrap;
          font-size: 11 * @rem;
          line-height: 18 * @rem;
          color: #93999f;
          text-align: center;
        }
      }
    }
  }
  .game-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64 * @rem;
    height: 30 * @rem;
    background: linear-gradient(75deg, #ff7a00 0%, #ffb03a 100%);
    border-radius: 29 * @rem;
    font-weight: 500;
    font-size: 14 * @rem;
    color: #ffffff;
    text-align: center;
    margin-left: 14 * @rem;

    &.downloading {
      background: #fff6ea;
      color: #ff840a;
    }
  }
}
</style>
