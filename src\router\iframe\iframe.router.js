export default [
  {
    path: '/iframe/:title/:url',
    name: 'Iframe',
    component: () =>
      import(/* webpackChunkName: "iframe" */ '@/views/Iframe/NoAuth'),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/auth_iframe/:title/:url',
    name: 'AuthIframe',
    component: () =>
      import(/* webpackChunkName: "iframe" */ '@/views/Iframe/Auth'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/activity/:url',
    name: 'Activity',
    component: () =>
      import(/* webpackChunkName: "iframe" */ '@/views/Iframe/Activity'),
    meta: {
      keepAlive: false,
      pageTitle: '活动页'
    },
  },
];
