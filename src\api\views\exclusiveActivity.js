import { request } from '../index';

/**
 * 专服活动首页
 */
export function ApiV2024ZfIndex(params = {}) {
  return request('/v2024/zf/index', params);
}

/**
 * 专服活动分享成功上报
 */
export function ApiV2024ZfShare(params = {}) {
  return request('/v2024/zf/share', params);
}

/**
 * 领取任务
 * @param {number} task_id 任务id
 */
export function ApiV2024ZfTake(params = {}) {
  return request('/v2024/zf/take', params);
}

/**
 * 抽奖
 */
export function ApiV2024ZfLottery(params = {}) {
  return request('/v2024/zf/lottery', params);
}

/**
 * 中奖记录
 */
export function ApiV2024PrizeLog(params = {}) {
  return request('/v2024/zf/prizeLog', params);
}
