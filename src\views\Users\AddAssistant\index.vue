<template>
  <div class="add-assistant">
    <nav-bar-2
      :placeholder="false"
      bgStyle="transparent-white"
      :bgColor="`rgba(255, 255, 255, 0)`"
      :azShow="true"
      v-if="navBgTransparent"
    >
    </nav-bar-2>
    <nav-bar-2
      title="添加福利官"
      :border="true"
      :placeholder="false"
      :azShow="true"
      v-else
    >
    </nav-bar-2>
    <status-bar></status-bar>
    <div class="introduce">
      <div class="big-text">添加福利官</div>
      <div class="text">首次添加福利官还有金币奖励哦~</div>
      <div class="process">
        <div class="item">
          <img
            src="~@/assets/images/users/add-assistant/assistant-icon1.png"
            class="icon"
          />
          <div class="item-text">绑定微信</div>
        </div>
        <div class="item2"></div>
        <div class="item">
          <img
            src="~@/assets/images/users/add-assistant/assistant-icon2.png"
            class="icon"
          />
          <div class="item-text">添加福利官</div>
        </div>
        <div class="item2"></div>
        <div class="item">
          <img
            src="~@/assets/images/users/add-assistant/assistant-icon3.png"
            class="icon"
          />
          <div class="item-text">获取金币奖励</div>
        </div>
      </div>
    </div>
    <div v-if="process_list.length > 0" class="process2">
      <div v-html="process_text" class="title"></div>
      <div
        v-if="
          ['android', 'androidBox'].includes(platform) &&
          initData.share_info.length > 0
        "
        class="content"
      >
        <div class="step">
          <div class="left-content">
            <div
              :class="{ already: process_list[0].status == 1 }"
              class="icon icon1"
            ></div>
            <div class="line"></div>
          </div>
          <div
            :class="{ on: process_list[0].status == 0 }"
            class="right-content"
          >
            <div class="right-title">
              <span>绑定微信</span>
              <div
                v-if="process_list[0].status == 0"
                @click="bindWx()"
                class="right-button btn"
              >
                立即绑定
              </div>
            </div>
            <div
              @click="bindQuestionShow = true"
              v-if="process_list[0].status == 0"
              class="text color btn"
            >
              绑定微信常见问题>
            </div>
            <div v-else class="text">
              已绑定微信：{{ process_list[0].nickname }}
            </div>
          </div>
        </div>
        <div class="step">
          <div class="left-content">
            <div class="icon icon2"></div>
            <div class="line"></div>
          </div>
          <div
            :class="{ on: process_list[0].status == 1 }"
            class="right-content"
          >
            <div class="right-title">
              <span>添加3733游戏盒福利官</span>
              <div
                v-if="process_list[0].status == 1"
                :class="{ already: process_list[1].status == 1 }"
                @click="savePic()"
                class="right-button btn"
              >
                {{ process_list[1].status == 1 ? '已添加' : '保存二维码' }}
              </div>
            </div>
            <div class="text">1.点击保存二维码</div>
            <div class="text">
              2.打开微信，使用所绑定微信号通过扫一扫，选择相册，识别二维码即可添加
            </div>
            <div class="bottom">
              <div class="icon"></div>
              <div class="small-text">3733游戏盒福利官</div>
            </div>
            <template v-if="process_list[0].status == 1">
              <img :src="qrcode" class="ewm" />
              <div class="text center">PS：若是保存失败，请截屏</div>
            </template>
          </div>
        </div>
      </div>
      <div v-else class="content">
        <div class="step">
          <div class="left-content">
            <div
              :class="{ already: process_list[0].status == 1 }"
              class="icon icon1"
            ></div>
            <div class="line"></div>
          </div>
          <div class="right-content">
            <div class="right-title">
              <span>关注微信公众号</span>
              <div
                :class="{ already: process_list[0].status == 1 }"
                @click="copy()"
                class="right-button btn"
              >
                {{ process_list[0].status == 0 ? '复制公众号' : '已关注' }}
              </div>
            </div>
            <div class="text">1.前往微信 - 顶部 [搜索]</div>
            <div class="text">2.搜索并关注“3733游戏”公众号</div>
            <div class="bottom">
              <div class="icon"></div>
              <div class="small-text">3733游戏盒</div>
            </div>
          </div>
        </div>
        <div class="step">
          <div class="left-content">
            <div
              :class="{ already: process_list[0].status == 1 }"
              class="icon icon2"
            ></div>
            <div class="line"></div>
          </div>
          <div class="right-content">
            <div class="right-title">
              <span>绑定微信</span>
              <div
                v-if="process_list[0].status == 0"
                @click="toWechat"
                class="right-button btn"
              >
                打开微信
              </div>
            </div>
            <div v-if="process_list[0].status == 0" class="bottom-container">
              <div class="left-container">
                <div class="text">
                  ①点击关注后默认消息中的“点我绑定3733账号”进入绑定页面
                </div>
                <img
                  src="~@/assets/images/users/add-assistant/assistant-pic1.png"
                  alt=""
                />
              </div>
              <div class="right-container">
                <div class="text">
                  ②输入3733游戏盒账号绑定的手机号+验证码进行微信绑定
                </div>
                <img
                  src="~@/assets/images/users/add-assistant/assistant-pic2.png"
                  alt=""
                />
              </div>
            </div>
            <div v-else class="text">
              已绑定微信：{{ process_list[0].nickname }}
            </div>
          </div>
        </div>
        <div class="step">
          <div class="left-content">
            <div class="icon icon3"></div>
            <div class="line"></div>
          </div>
          <div class="right-content">
            <div class="right-title">
              <span>添加3733游戏盒福利官</span>
              <div
                v-if="
                  process_list[0].status == 1 && process_list[1].status == 1
                "
                :class="{ already: process_list[1].status == 1 }"
                class="right-button btn"
              >
                已添加
              </div>
              <!-- <div
                v-if="process_list[0].status == 1"
                :class="{ already: process_list[1].status == 1 }"
                @click="savePic()"
                class="right-button btn"
              >
                {{ process_list[1].status == 1 ? "已添加" : "保存二维码" }}
              </div> -->
            </div>
            <div class="text">1.点击保存二维码</div>
            <div class="text">
              2.打开微信，使用所绑定微信号通过扫一扫，选择相册，识别二维码即可添加
            </div>
            <div class="bottom">
              <div class="icon"></div>
              <div class="small-text">3733游戏盒福利官</div>
            </div>
            <template v-if="process_list[0].status == 1">
              <img :src="qrcode" class="ewm" />
              <div class="text bolder center">长按二维码进行保存</div>
              <div class="text center">PS：若是保存失败，请截屏</div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="explain">
      <div class="title">福利官介绍</div>
      <div class="text">1.绑定微信，首次添加福利官即可获得金币奖励哦~</div>
      <div class="text">2.惊喜不断，福利官会不定期发放一些游戏福利；</div>
      <div class="text">3.将为您提供最新的游戏发布消息及福利消息；</div>
      <div class="text">
        4.如您有关于游戏的问题，可在游戏盒内通过在线客服联系我们；
      </div>
    </div>
    <bind-question :show.sync="bindQuestionShow"></bind-question>
  </div>
</template>
<script>
import { ApiAddAssistant, ApibindWx } from '@/api/views/users.js';
import { BOX_openWx, Box_wxOAuth2, platform } from '@/utils/box.uni.js';
import bindQuestion from '@/components/bind-question/index.vue';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      platform,
      process_list: [],
      qrcode: '',
      qrcode2: '',
      bindQuestionShow: false,
      navBgTransparent: true,
    };
  },
  computed: {
    process_text() {
      let count = 0;
      this.process_list.forEach(ele => {
        if (ele.status) count++;
      });
      if (count >= 2) {
        return '您已添加福利官';
      } else {
        if (!this.initData.share_info?.length) {
          return '添加福利官进度';
        } else {
          return `添加福利官进度<span>${count}</span>/2`;
        }
      }
    },
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  async created() {
    await this.init();
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
      window.getWxUserInfo = async params => {
        this.$toast.loading('加载中');
        const res = await ApibindWx({
          wxCode: params.wxCode,
        });
        await this.init();
      };
    }
    // window.addEventListener("scroll", this.handleScroll);
  },
  methods: {
    async onResume() {
      await this.init();
    },
    async init() {
      const res = await ApiAddAssistant();
      this.qrcode = res.data.qr_code;
      this.qrcode2 = res.data.qr_code2;
      this.process_list = res.data.info;
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    copy() {
      if (!this.process_list[0].status) {
        this.$copyText('3733游戏').then(res => {
          this.$toast('复制成功');
        });
      }
    },
    toWechat() {
      BOX_openWx();
    },
    bindWx() {
      Box_wxOAuth2();
    },
    savePic() {
      if (!this.process_list[1].status) {
        // 创建一个canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        let img = new Image();
        img.src = this.qrcode2;
        img.setAttribute('crossOrigin', 'Anonymous');
        img.onload = function () {
          // 设置canvas的宽高与img标签的宽高一致
          canvas.width = img.width;
          canvas.height = img.height;
          // 将img标签绘制到canvas上
          ctx.drawImage(img, 0, 0);
          // 将canvas转换为base64格式的图片数据
          const base64Image = canvas.toDataURL('image/jpg');
          let link = document.createElement('a');
          link.download = 'image.jpg';
          link.href = base64Image;
          link.click();
        };
      }
    },
  },
  components: {
    bindQuestion,
  },
};
</script>
<style lang="less" scoped>
.add-assistant {
  .image-bg('~@/assets/images/users/add-assistant/assistant-bg.png');
  overflow: hidden;
  .introduce {
    .big-text {
      margin-top: 50 * @rem;
      text-align: center;
      font-size: 24 * @rem;
      font-family:
        Dream Han Sans CN,
        Dream Han Sans CN;
      font-weight: bolder;
      color: #ffffff;
      line-height: 24 * @rem;
    }
    .text {
      text-align: center;
      margin-top: 18 * @rem;
      font-size: 16 * @rem;
      font-family:
        Dream Han Sans CN,
        Dream Han Sans CN;
      font-weight: normal;
      color: #ffffff;
      line-height: 20 * @rem;
    }
  }
  .process {
    width: 325 * @rem;
    height: 120 * @rem;
    margin: 24 * @rem auto 0;
    background: #ffffff;
    box-shadow: 0 4 * @rem 8 * @rem 0 rgba(0, 0, 0, 0.05);
    border-radius: 14 * @rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 25 * @rem;
    .item {
      .icon {
        width: 44 * @rem;
        height: 44 * @rem;
        margin: 0 auto 8 * @rem;
      }
      .item-text {
        font-size: 11 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 14 * @rem;
      }
    }
    .item2 {
      width: 14 * @rem;
      height: 17 * @rem;
      position: relative;
      top: -10 * @rem;
      .image-bg('~@/assets/images/users/add-assistant/assistant-icon4.png');
    }
  }
  .process2 {
    margin: 32 * @rem 25 * @rem 0;
    .title {
      margin-bottom: 24 * @rem;
      line-height: 18 * @rem;
      font-weight: 600;
      font-size: 14 * @rem;
      /deep/ span {
        margin-left: 8 * @rem;
        color: #32b768;
      }
    }
    .step {
      display: flex;
      justify-content: space-between;
      &:last-of-type {
        .left-content {
          .line {
            display: none;
          }
        }
      }
      .left-content {
        margin-right: 12 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .icon {
          width: 20 * @rem;
          height: 20 * @rem;
          &.icon1 {
            .image-bg(
              '~@/assets/images/users/add-assistant/assistant-icon5.png'
            );
          }
          &.icon2 {
            .image-bg(
              '~@/assets/images/users/add-assistant/assistant-icon9.png'
            );
          }
          &.icon3 {
            .image-bg(
              '~@/assets/images/users/add-assistant/assistant-icon7.png'
            );
          }
          &.already {
            .image-bg(
              '~@/assets/images/users/add-assistant/assistant-icon8.png'
            );
          }
        }
        .line {
          flex: 1;
          width: 1 * @rem;
          margin: 8 * @rem 0 8 * @rem 9 * @rem;
          border-left: 1 * @rem dashed #32b768;
        }
      }
      .right-content {
        flex: 1;
        background: #ffffff;
        border-radius: 12 * @rem;
        padding: 14 * @rem 16 * @rem;
        border: 1px solid #e0f1e0;
        margin-bottom: 29 * @rem;
        &.on {
          background: #f2fbf2;
          border: 1px solid #32b768;
        }
        .right-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          span {
            font-size: 14 * @rem;
            font-weight: 600;
            color: #333333;
            line-height: 18 * @rem;
          }
          .right-button {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 70 * @rem;
            height: 24 * @rem;
            background: #32b768;
            border-radius: 30 * @rem;
            font-size: 12 * @rem;
            font-weight: 600;
            color: #ffffff;
            &.already {
              color: #ffffff;
              background: #cae7ca;
            }
          }
        }
        .text {
          font-size: 12 * @rem;
          color: #777777;
          line-height: 15 * @rem;
          margin-top: 10 * @rem;
        }
        .bottom {
          width: 131 * @rem;
          height: 24 * @rem;
          display: flex;
          justify-content: left;
          align-items: center;
          background: #f2fbf2;
          border-radius: 10 * @rem;
          box-sizing: border-box;
          margin-top: 10 * @rem;
          .icon {
            width: 18 * @rem;
            height: 18 * @rem;
            .image-bg('~@/assets/images/app-icon2.png');
          }
          .small-text {
            margin-left: 5 * @rem;
            color: #32b768;
          }
        }
        .bottom-container {
          display: flex;
          justify-content: space-between;
          .left-container,
          .right-container {
            flex: 0 0 120 * @rem;
            img {
              width: 120 * @rem;
              height: 154 * @rem;
              display: block;
              margin-top: 10 * @rem;
            }
          }
        }
        .ewm {
          width: 160 * @rem;
          height: 160 * @rem;
          display: block;
          margin: 20 * @rem auto 10 * @rem;
        }
        .bolder {
          font-weight: 600;
          color: #333333;
        }
        .center {
          text-align: center;
        }
        .color {
          color: #27bb3f;
        }
      }
    }
  }
  .explain {
    padding: 20 * @rem 20 * @rem 20 * @rem;
    .title {
      margin-bottom: 8 * @rem;
      font-size: 14 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 18 * @rem;
    }
    .text {
      font-size: 13 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 20 * @rem;
    }
  }
}
</style>
