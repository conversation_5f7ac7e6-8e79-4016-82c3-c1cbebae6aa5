/* scss全局变量（定制主题） */
/* 状态栏高度 */
/* 设置rem倍数 */
/* 主题色相关 */
/*背景*/
/* 文字颜色 */
/* 兼容全面屏 */
.fixed-center {
  left: 0;
  right: 0;
  margin: 0 auto;
  max-width: 450px;
}
:export {
  remLess: 0.0267rem;
  themeColorLess: #2BBE88;
  bgColorLess: #fff;
  cbgColorLess: #fff;
  textColorLess: #333;
  titleColorLess: #000;
  tipColorLess: #999;
  remNumberLess: 0.0267;
  safeAreaTop: calc(var(--statusHeight) + constant(safe-area-inset-top));
  safeAreaTopEnv: calc(var(--statusHeight) + env(safe-area-inset-top));
  safeAreaBottom: constant(safe-area-inset-bottom);
  safeAreaBottomEnv: env(safe-area-inset-bottom);
}
