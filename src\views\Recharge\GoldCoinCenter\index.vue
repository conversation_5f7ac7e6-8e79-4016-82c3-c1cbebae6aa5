<template>
  <div class="page gold-coin-center">
    <nav-bar-2
      :border="false"
      title="金币中心"
      :bgStyle="bgStyle"
      class="nav-bar-2-container"
      :azShow="true"
      :placeholder="false"
      :bgColor="`rgba(255,255,255,${navbarOpacity}`"
      :style="{ color: `rgba(0, 0, 0, ${navbarOpacity})` }"
    >
      <template #right>
        <div
          class="coin-tips"
          :class="{ black: bgStyle == 'transparent' }"
          @click="toPage('GoldCoinTips')"
        >
          金币小贴士
        </div>
      </template>
    </nav-bar-2>
    <van-notice-bar
      v-if="hasNotice"
      class="notice-bar"
      :left-icon="noticeIcon"
      text="自2022年7月7日12:00起，购买账号将不再返还金币。"
      background="#FEE7DB"
      color="#FE6600"
      mode="closeable"
      @close="hasNotice = false"
      @click="hasNotice = true"
    />
    <!-- <van-loading class="loading-box" v-if="!loadSuccess" size="24* @rem">{{
      $t('加载中...')
    }}</van-loading> -->
    <div class="main">
      <!-- 明细/背景 -->
      <div class="top-bg">
        <div class="top-bg-img">
          <img src="@/assets/images/recharge/gold-coin-top-bg.png" alt="" />
        </div>
      </div>
      <div class="gold-bar">
        <div class="gold-info">
          <div class="title">当前金币</div>
          <div class="gold-balance"
            ><div class="gold-num">{{ userInfo.gold }}</div
            ><div class="gold-detail" @click="toPage('GoldCoinDetail')">
              明细
            </div>
          </div>
        </div>
      </div>
      <template>
        <!-- 每日签到 -->
        <div class="signIn-container">
          <div class="signIn-container-top">
            <span class="title">{{ gold_title }}</span>
            <img class="icon" v-if="subtitle_img" :src="subtitle_img" />
          </div>
          <div class="signIn-container-tips">
            <div v-if="next_reward_diff_day">
              再连续签到
              <span class="days">{{ next_reward_diff_day }}</span>
              天可领取
              <span class="svip" :class="{ gold: next_reward_type == 'gold' }"
                >{{ next_reward_num
                }}<template v-if="next_reward_type == 'svip'"
                  >天SVIP</template
                ></span
              >
              奖励
            </div>
            <div v-else>已全部领取奖励，赞一个</div>
            <span v-if="shouldShowFixSign" @click="toClockIn()">去补签</span>
            <span v-else @click="toClockIn()">全部</span>
            <i></i>
          </div>
          <div class="signIn-container-speed">
            <div class="speed-list" v-if="goldBoxList.length">
              <div class="progress-container">
                <div class="days">
                  <div
                    v-for="(item, index) in goldBoxList"
                    :key="index"
                    class="day"
                    :class="{
                      filled: isDayFilled(item),
                    }"
                  >
                    <div
                      class="day-icon"
                      :class="{
                        'marin-left': !(index == goldBoxList.length - 1),
                      }"
                    >
                      <!-- 不可领取 -->
                      <div
                        class="day-icon-img"
                        :class="{ svip: item.type == 'svip' }"
                        v-if="
                          item.box_num != 1 &&
                          !isDayFilled(item) &&
                          item.state == 0
                        "
                      >
                      </div>
                      <!-- 可领取 金币-->
                      <yy-lottie
                        class="day-icon-img1"
                        v-else-if="
                          item.box_num != 1 &&
                          item.state == 1 &&
                          item.type != 'svip'
                        "
                        :width="115"
                        :height="115"
                        :options="{
                          autoplay: item.state == 1 ? true : false,
                          loop: true,
                          path: icon_path,
                        }"
                      ></yy-lottie>
                      <!-- 可领取 svip 暂时没动图 -->
                      <div
                        class="day-icon-img3"
                        :class="{ svip: item.type == 'svip' }"
                        v-else-if="
                          item.box_num != 1 &&
                          item.state == 1 &&
                          item.type == 'svip'
                        "
                      >
                      </div>
                      <div
                        class="day-icon-img2"
                        :class="{ svip: item.type == 'svip' }"
                        v-else-if="item.box_num != 1 && item.state == 2"
                      >
                      </div>
                      <div class="day-icon-first" v-else-if="item.box_num == 1">
                        <img
                          v-if="hasStateActive"
                          src="@/assets/images/recharge/gold-signln-day-first-active.png"
                          alt=""
                        />
                        <img
                          v-else
                          src="~@/assets/images/recharge/gold-signln-day-first.png"
                          alt=""
                        />
                      </div>
                    </div>
                    <div class="day-icon-btn" @click="handleGet(item)">
                      <div
                        class="gold"
                        v-if="
                          item.gray_gold_icon &&
                          item.box_num != 1 &&
                          item.state == 0
                        "
                      >
                        <img :src="item.gray_gold_icon" alt="" />
                      </div>
                      <div
                        class="gold"
                        v-if="
                          item.gold_icon && item.box_num != 1 && item.state == 1
                        "
                      >
                        <img :src="item.gold_icon" alt="" />
                      </div>
                    </div>
                    <span
                      v-if="!(index == goldBoxList.length - 1)"
                      class="day-icon-xian"
                    ></span>
                    <div class="day-text" v-if="item.box_num != 1">
                      连签{{ item.box_num }}天</div
                    >
                  </div>
                  <div class="progress-line">
                    <div class="line" :style="{ width: lineWidth }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="signIn-container-btn"
            @click="rightOffSignIn()"
            :class="{ svip: userInfo.is_svip, had: isSignIn(currentDay) }"
          >
            {{ clockInBtnText }}
            <span v-if="isSignIn(currentDay)"></span>
          </div>
        </div>
        <!-- achievement daily new_user -->
        <!-- 任务中心 -->
        <div class="task-container" v-if="missionInfo.list.length">
          <div class="task-title"> {{ missionInfo.title }} </div>
          <div class="task-list">
            <template>
              <div
                class="task-item"
                v-for="(item, key) in missionInfo.list"
                :key="key"
              >
                <div class="icon">
                  <img :src="item.icon" />
                </div>
                <div class="task-info">
                  <div class="title" v-if="item.type == 'new_user'">{{
                    item.title
                  }}</div>
                  <!-- 成就任务 充值pay -->
                  <div
                    class="title"
                    v-if="item.type == 'achievement' && item.key == 'pay'"
                  >
                    {{ item.name }} ({{ item.total }}/{{ item.need_value }})
                  </div>
                  <!-- 成就任务 签到sign -->
                  <div
                    class="title"
                    v-if="item.type == 'achievement' && item.key == 'sign'"
                  >
                    累计签到 {{ item.need_value }}次 ({{ item.total }}/{{
                      item.need_value
                    }})
                  </div>
                  <div class="title" v-if="item.type == 'daily'">
                    {{ item.title }}
                  </div>
                  <div class="line" v-if="item.type == 'new_user'">
                    完成得
                    <div v-if="item.gold_num">
                      <span>金币</span>
                      <span class="reward-info">+{{ item.gold_num }}</span>
                      <span v-if="item.coupon || item.exp_num">、</span>
                    </div>
                    <div v-if="item.coupon">
                      <span class="reward-info">{{ item.coupon }}</span>
                      <span v-if="item.exp_num">、</span>
                    </div>
                    <div v-if="item.exp_num">
                      <span>经验</span>
                      <span class="reward-info">+{{ item.exp_num }}</span>
                    </div>
                  </div>
                  <div class="line" v-if="item.type == 'achievement'">
                    <div v-if="item.reward">
                      完成得<span>金币</span>
                      <span class="reward-info">+{{ item.reward }}</span>
                    </div>
                  </div>
                  <div class="line" v-if="item.type == 'daily'">
                    完成得
                    <div v-if="item.gold_num">
                      <span>金币</span>
                      <span class="reward-info">+{{ item.gold_num }}</span>
                      <span v-if="item.coupon || item.exp_num">、</span>
                    </div>
                    <div v-if="item.coupon">
                      <span class="reward-info">+ {{ item.coupon }}</span>
                      <span v-if="item.exp_num">、</span>
                    </div>
                    <div v-if="item.exp_num">
                      <span>经验</span>
                      <span class="reward-info">+{{ item.exp_num }}</span>
                    </div>
                  </div>
                </div>
                <template v-if="item.type != 'achievement'">
                  <div
                    class="task-btn btn"
                    v-if="item.is_finish == 0"
                    @click.stop="completeTheTask(item)"
                  >
                    {{ $t('去完成') }}
                  </div>
                  <div
                    class="task-btn btn get"
                    v-if="item.is_finish == 1"
                    @click.stop="taskToCollect(item)"
                  >
                    {{ $t('领取') }}
                  </div>
                  <div class="task-btn btn had" v-else-if="item.is_finish == 2">
                    {{ $t('已领取') }}
                  </div>
                </template>
                <template v-if="item.type == 'achievement'">
                  <template v-if="!Boolean(item.all_finish)">
                    <div
                      class="task-btn btn get"
                      v-if="item.total >= item.need_value"
                      @click.stop="taskToCollect(item)"
                    >
                      {{ $t('领取') }}
                    </div>
                    <div
                      class="task-btn btn"
                      v-else
                      @click.stop="completeTheTask(item)"
                    >
                      {{ $t('去完成') }}
                    </div>
                  </template>
                  <div class="task-btn btn had" v-else>{{ $t('已领取') }}</div>
                </template>
              </div>
            </template>
            <template>
              <div
                class="exchange-down"
                v-if="!isExpand"
                @click="viewAllTasks()"
              >
                <span>查看全部任务</span>
                <i>
                  <img
                    src="~@/assets/images/recharge/exchange-left-icon.png"
                    alt=""
                  />
                </i>
              </div>
            </template>
          </div>
        </div>

        <!-- SVIP专属福利 -->
        <div
          class="svip-exclusive-container"
          v-if="svipWelfareInfo.list.length"
        >
          <div class="task-title">
            <div class="task-title-left">
              <div class="title">{{ svipWelfareInfo.title }}</div>
              <img
                class="sub-title"
                v-if="svipWelfareInfo.subtitle_img"
                :src="svipWelfareInfo.subtitle_img"
              />
            </div>
            <div class="task-title-right" @click="toPage('SvipWelfare')">
              更多
            </div>
          </div>
          <div class="task-content">
            <div class="svip-exchange-list">
              <div
                class="exchange-item"
                v-for="item in svipWelfareInfo.list.slice(0, 3)"
                :key="item.id"
                @click="toPage('SvipWelfare')"
              >
                <div class="item-img">
                  <img :src="item.img" />
                </div>
                <div class="item-title">{{ item.gold }}金币</div>
              </div>
            </div>
            <div
              class="svip-exchange-btn"
              v-if="!userInfo.is_svip"
              @click="toPage('Svip')"
            >
              <img
                src="~@/assets/images/recharge/svip-exchange-btn.png"
                alt=""
              />
              <span>开通SVIP立即兑换</span>
            </div>
          </div>
        </div>
        <!-- 道具 -->
        <div class="prop-container" v-if="svip_propList.length">
          <div
            class="prop-list"
            :class="svipPropListLength == 1 ? 'justify_center' : ''"
          >
            <div
              class="prop-item"
              v-for="(box, index) in svip_propList"
              :key="index"
              @click="toPage('SvipWelfare')"
            >
              <div class="prop-title" v-if="box.list.length && isKeep">{{
                box.title
              }}</div>
              <swiper
                class="prop-center"
                :class="
                  svipPropListLength == 1 || svipPropListLength == 2
                    ? 'prop-center2'
                    : ''
                "
                :options="swiperOptions0"
                :auto-update="true"
                v-if="index == 0 && box.list.length && isKeep"
              >
                <swiper-slide
                  class="prop-item-box"
                  v-for="item in box.list"
                  :key="item.id"
                >
                  <template>
                    <div class="logo-box">
                      <img class="big-logo" :src="item.titlepic" alt="" />
                      <div class="small-logo">
                        <img :src="item.game_titlepic" alt="" />
                      </div>
                    </div>
                    <div class="name">{{ item.title }}</div>
                    <div class="price">{{ item.need_gold }}金币</div>
                  </template>
                </swiper-slide>
              </swiper>
              <swiper
                class="prop-center"
                :class="
                  svipPropListLength == 1 || svipPropListLength == 2
                    ? 'prop-center2'
                    : ''
                "
                :options="swiperOptions1"
                :auto-update="true"
                v-if="index == 1 && box.list.length && isKeep"
              >
                <swiper-slide
                  class="prop-item-box"
                  v-for="item in box.list"
                  :key="item.id"
                >
                  <template>
                    <div class="logo-box">
                      <img class="big-logo" :src="box.icon" alt="" />
                      <div class="small-logo">
                        <img :src="item.game.titlepic" alt="" />
                      </div>
                    </div>
                    <div class="name">{{ item.title }}</div>
                    <div class="price">{{ raffleInfo.prompt }}</div>
                  </template>
                </swiper-slide>
              </swiper>
              <swiper
                v-if="index == 2 && box.list.length && isKeep"
                :class="
                  svipPropListLength == 1 || svipPropListLength == 2
                    ? 'prop-center2'
                    : ''
                "
                class="prop-center"
                :options="swiperOptions2"
                :auto-update="true"
              >
                <swiper-slide
                  class="prop-item-box"
                  v-for="item in box.list"
                  :key="item.id"
                >
                  <template>
                    <div class="logo-box">
                      <img class="big-logo" :src="box.icon" alt="" />
                      <div class="small-logo">
                        <img :src="item.game.titlepic" alt="" />
                      </div>
                    </div>
                    <div class="name">{{ item.title }}</div>
                    <div class="price">{{ raffleInfo.prompt }}</div>
                  </template>
                </swiper-slide>
              </swiper>
            </div>
          </div>
          <div
            class="prop-btn"
            v-if="!userInfo.is_svip"
            @click="toPage('Svip')"
          >
            <img
              src="~@/assets/images/recharge/svip-exchange-btn1.png"
              alt=""
            />
            <span>开通SVIP立即兑换</span>
          </div>
        </div>
        <!-- 金币兑换商品 -->
        <div
          class="gold-exchange-container"
          v-if="exchangeGoods && exchangeGoods.list.length"
        >
          <div class="exchange-title">
            <div class="exchange-title-left">
              <div class="title">{{ exchangeGoods.title }}</div>
            </div>
            <div class="exchange-title-right">{{ exchangeGoods.subtitle }}</div>
          </div>
          <div class="exchange-list">
            <div
              class="exchange-item"
              v-for="item in exchangeGoods.list"
              :key="item.id"
              @click="toExchangeDetail(item)"
            >
              <div class="img">
                <img :src="item.pic" alt="" />
              </div>
              <div class="item-title">{{ item.title }}</div>
              <div class="need-gold"
                >{{ userInfo.is_svip ? item.svip_price : item.price }}金币</div
              >
            </div>
          </div>
        </div>
        <!-- 金币折扣 -->
        <div class="gold-discount-container" v-if="goldGameInfo.list.length">
          <div class="discount-title">
            <div class="discount-title-left">
              <div class="title">{{ goldGameInfo.title }}</div>
            </div>
            <div
              class="discount-title-right"
              @click="toPage('GoldCoinDiscountGame', { id: 151 })"
            >
              更多
            </div>
          </div>
          <div class="discount-tips">
            <div>{{ goldGameInfo.subtitle }}</div>
            <a @click="experienceShow = true" v-if="goldGameInfo.pop_img"
              >点击体验</a
            >
          </div>
          <div class="game-list">
            <div
              class="game-item"
              v-for="(game, index) in goldGameInfo.list"
              :key="game.id + '' + index"
            >
              <game-item-4
                :gameInfo="game"
                :iconSize="72"
                :showHot="true"
              ></game-item-4>
              <div class="game-btn">
                <yy-download-btn
                  :gameInfo="game"
                  v-if="!openGameShow(game)"
                ></yy-download-btn>
                <div class="download-btn" @click="openGame(game)" v-else>
                  打开
                </div>
                <div class="deduction-tips" v-if="game.gold_discount > 0">
                  <div class="deduction-tips-box">
                    <div
                      :class="{ zhekou100_icon: game.gold_discount == 100 }"
                    ></div>
                    <span v-if="game.gold_discount != 100"
                      >{{ game.gold_discount }}%</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 金币赢好礼 -->
        <div
          class="gold-gifts-container"
          v-if="Object.keys(raffleInfo.prize_info).length"
          @click="goToGoldGamble()"
        >
          <div class="discount-title">
            <div class="discount-title-left">
              <div class="title">{{ raffleInfo.title }}</div>
            </div>
            <div class="discount-title-right"> 更多奖品 </div>
          </div>
          <div class="gold-gifts-list">
            <div class="gold-gifts-item">
              <div class="item-left-box">
                <div class="item-img">
                  <img
                    src="~@/assets/images/recharge/svip-exchange-icon.png"
                    alt=""
                  />
                </div>
                <div class="item-info">
                  <div>{{ raffleInfo.prize_info.title }}</div>
                  <div>共{{ raffleInfo.prize_info.total }}份</div>
                  <div>{{ raffleInfo.prize_info.gold }}金币抽奖</div>
                </div>
              </div>
              <div class="item-right-btn"> 去抽奖 </div>
            </div>
          </div>
        </div>
        <!-- 金币活动 -->
        <div class="gold-activity-container">
          <div class="activity-title">
            <div class="activity-title-left">
              <div class="title">金币活动</div>
            </div>
            <div class="activity-title-right">
              <div
                class="container"
                v-if="activityInfo.max_reward.length && swiperShow"
              >
                <swiper
                  v-if="isKeep"
                  :options="swiperOption"
                  class="history-list"
                >
                  <swiper-slide
                    v-for="(item, index) in activityInfo.max_reward"
                    :key="index"
                    class="swiper-no-swiping"
                  >
                    <div class="history-item">
                      <user-avatar
                        class="avatar"
                        :self="false"
                        :src="item.avatar"
                      ></user-avatar>
                      <div class="nickname">{{ item.nickname }}</div>
                      <div class="history-desc">
                        抽中 <span>{{ item.num }}</span>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
            </div>
          </div>
          <div class="activity-list">
            <div class="activity-item btn" @click="goToGoldGamble()">
              <img
                src="@/assets/images/recharge/activity-gold-gameble.png"
                alt=""
              />
            </div>
            <div class="activity-item btn" @click="goToTurnTable()">
              <img
                src="@/assets/images/recharge/activity-turn-table.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <div
          class="signIn-container-btn1"
          v-if="isShowMoreBenefits"
          @click="viewMoreBenefits()"
        >
          <span>查看更多福利</span>
        </div>
      </template>
    </div>
    <!-- 引导添加到桌面弹窗 -->
    <van-dialog
      v-model="yindaoPopup"
      :showConfirmButton="false"
      :showCancelButton="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="yindao-popup"
    >
      <div class="close" @click="yindaoPopup = false"></div>
      <div class="text">{{ $t('为了更方便的连续签到领宝箱') }}</div>
      <div class="text">{{ $t('请添加到主屏幕哦') }}</div>
      <div class="down-arrow"></div>
    </van-dialog>

    <van-dialog
      v-model="notSvipSignShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="sign-before-dialog"
      @closed="closeNotSvipSignShow()"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('开通SVIP') }}</div>
        <div class="msg">
          <div class="msg-number" v-for="(item, index) in noSvipText" :key="index">{{
            item
          }}</div>
        </div>
        <div class="btns">
          <div class="btn" @click="handleClockIn">
            {{ $t('普通签到') }} <span>{{ common_gold_text }}</span></div
          >
          <div class="btn" @click="toSvip">
            {{ $t('SVIP签到') }}
            <span>{{ svip_gold_text }}</span>
            <div class="svip-tips" v-if="svipTip">{{ svipTip }}</div></div
          >
        </div>
        <div
          class="checkbox"
          @click="isNotShowSvipSign = !isNotShowSvipSign"
          :class="{ checked: isNotShowSvipSign }"
          ><span></span>不再提醒</div
        >
        <div class="dialog-close-btn" @click="notSvipSignShow = false"></div>
      </div>
    </van-dialog>

    <!-- 连签奖励未完成 -->
    <van-dialog
      v-model="getAwardIncompletePopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-before-dialog"
    >
      <div class="dialog-content">
        <div class="title" :class="{ fzs: getAwardPopupInfo.gold > 999 }">{{
          getAwardPopupInfo.title
        }}</div>
        <div class="detail">
          <div
            class="double"
            v-if="userInfo.is_svip && getAwardPopupInfo.type != 'svip'"
            >已翻倍</div
          >
          <img :src="getAwardPopupInfo.icon" alt="" />
          <div class="gold-num">{{ getAwardPopupInfo.icon_text }}</div>
        </div>
        <div class="msg">
          <div class="msg-number">{{ getAwardPopupInfo.prompt }}</div>
          <div
            class="msg-number"
            v-if="
              !userInfo.is_svip &&
              getAwardPopupInfo.svip_text &&
              getAwardPopupInfo.type != 'svip'
            "
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="getAwardIncompletePopupShow = false"
            >我知道了</div
          >
          <div class="btn" @click="toSvip" v-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div
          class="dialog-close-btn"
          @click="getAwardIncompletePopupShow = false"
        ></div>
      </div>
    </van-dialog>

    <!-- 连签奖励弹窗 -->
    <van-dialog
      v-model="getAwardPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-dialog"
      @closed="fetchGetGoldBoxAfter"
    >
      <div
        class="logo-icon"
        :style="{ backgroundImage: `url(${getAwardPopupInfo.icon})` }"
        :class="{ svip: getAwardPopupInfo.type == 'svip' }"
      ></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="msg">
          <div class="msg-number">{{ getAwardPopupInfo.day_text }}</div>
          <div class="msg-number">{{ getAwardPopupInfo.prize_text }}</div>
          <div
            class="tips"
            v-if="!userInfo.is_svip && getAwardPopupInfo.svip_text"
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="getAwardPopupShow = false">开心收下</div>
          <div
            class="btn"
            @click="toSvip"
            v-if="getAwardPopupInfo.type == 'svip'"
            >查看SVIP权益</div
          >
          <div class="btn" @click="toSvip" v-else-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div class="dialog-close-btn" @click="getAwardPopupShow = false"></div>
      </div>
    </van-dialog>

    <!-- 签到弹窗 -->
    <van-dialog
      v-model="signSuccessShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="sign-dialog"
      @closed="openYindaoPopup()"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="reward-box">
          <div class="reward-item" v-if="reward_info.exp">
            <div class="exp-icon"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.exp }}</span>
              <span>经验</span>
            </div>
          </div>
          <div class="reward-item" v-if="reward_info.gold">
            <div class="gold-icon" :class="{ svip: userInfo.is_svip }"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.gold }}</span>
              <span>金币</span>
            </div>
          </div>
          <div class="upgrade" v-if="!userInfo.is_svip">
            <div class="text">升级</div>
            <div class="up-icon"></div>
          </div>
          <div class="reward-item svip-more-gold" v-if="!userInfo.is_svip">
            <div class="gold-icon"></div>
            <div class="reward-msg">
              <span class="msg-number">
                {{ reward_info.gold + reward_info.svip_gold }}</span
              >
              <span>金币</span>
            </div>
          </div>
        </div>
        <div class="svip-tips" v-if="noSvipText && !userInfo.is_svip">
          <div class="tips-title">升级SVIP获得</div>
          <div
            class="small-tips"
            v-for="(item, index) in noSvipText"
            :key="index"
            >{{ item }}</div
          >
        </div>
        <div class="btn-list">
          <div class="btn" @click="signSuccessShow = false">开心收下</div>
          <div class="btn" v-if="!userInfo.is_svip" @click="toSvip"
            >升级领取
            <div class="svip-tip" v-if="svipTip">{{ svipTip }}</div></div
          >
        </div>
        <div class="tips" v-if="userInfo.is_svip && reward_info.extra_tip">{{
          reward_info.extra_tip
        }}</div>
        <div class="dialog-close-btn" @click="signSuccessShow = false"></div>
      </div>
    </van-dialog>

    <!-- 体验弹窗 -->
    <van-dialog
      v-model="experienceShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="experience-dialog"
      :close-on-click-overlay="true"
    >
      <div class="dialog-content">
        <img :src="goldGameInfo.pop_img" class="sign-success-bg" />
        <div class="dialog-btn" @click="experienceShow = false"></div>
        <div class="dialog-close-btn" @click="experienceShow = false"> </div>
      </div>
    </van-dialog>
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      :tip="popupTip"
      :confirmText="popupConfirmText"
      @confirm="popupConfirm"
    ></task-popup>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import {
  platform,
  BOX_showActivity,
  BOX_showActivityByAction,
  BOX_openInBrowser,
  BOX_openInNewWindow,
  BOX_openApp,
  authInfo,
  BOX_goToGame,
} from '@/utils/box.uni.js';
import { navigateToGameDetail } from '@/utils/function';
let defaultDate = new Date().getTime();
import { needGuide } from '@/utils/userAgent';
import {
  ApiUserActiveSign,
  ApiUserGetGoldBox,
  ApiUserRepairDeductGold,
  ApiUserRepairSign,
  ApiCoinCenterIndex,
} from '@/api/views/users.js';
import {
  ApiMissionGetDaily,
  ApiMissionGetAchievement,
  ApiMissionGetAchievementReward,
  ApiMissionGetNewcomer,
  ApiMissionGetMissionReward,
  ApiMissionCheckToComplete,
} from '@/api/views/mission.js';
import { ApiGame01DiscountList } from '@/api/views/game.js';
import noticeIcon from '@/assets/images/deal/notice-icon.png';
import TaskPopup from '@/components/task-popup';
import useCollectToast from '@/components/yy-collect-toast/index.js';
export default {
  name: 'GoldCoinCenter',
  components: {
    TaskPopup,
  },
  data() {
    return {
      loadSuccess: false,
      taskPopup: false, // 任务弹窗是否显示
      popupContent: '', // 任务弹窗内容
      popupTip: '', // 任务弹窗提示
      popupConfirmText: '', // 任务弹窗确认按钮文案
      noticeIcon,
      hasNotice: false,
      noSvipText: [],
      svipTip: '',
      common_gold_text: '+10金币',
      svip_gold_text: '+20金币',
      // bg_img: '',
      missionInfo: {
        list: [],
        title: '任务中心',
      },
      svipWelfareInfo: {
        list: [],
        title: 'SVIP专属福利拿不停',
        subtitle_img: '',
      },
      bgStyle: 'transparent-white',
      navbarOpacity: 0,
      taskList2: null,
      isSvip: false,
      ptbUrl: '',
      vipUrl: '',
      isExpand: false,
      gameList: [],
      swiperOptions0: {
        slidesPerView: 1,
        loop: true,
        autoplay: true,
        allowTouchMove: false,
        autoplay: {
          delay: 3000,
        },
        on: {
          click: function (e) {
            setTimeout(() => {
              // console.log(this.realIndex);
            }, 0);
          },
        },
      },
      swiperOptions1: {
        slidesPerView: 1,
        loop: true,
        autoplay: true,
        allowTouchMove: false,
        autoplay: {
          delay: 4000,
        },
        on: {
          click: function (e) {
            setTimeout(() => {
              // console.log(this.realIndex);
            }, 0);
          },
        },
      },
      swiperOptions2: {
        slidesPerView: 1,
        loop: true,
        autoplay: true,
        allowTouchMove: false,
        autoplay: {
          delay: 5000,
        },
        on: {
          click: function (e) {
            setTimeout(() => {
              // console.log(this.realIndex);
            }, 0);
          },
        },
      },
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 1,
        // speed: 3000,
        autoplay: {
          delay: 3000,
        },
        loop: true,
        freeMode: true,
      },
      // 金币活动
      activityInfo: {
        list: [],
        max_reward: [],
        title: '金币活动',
      },
      goldGameInfo: {
        list: [],
        pop_img: '',
        subtitle: '游戏充值享受金币超值抵扣，最高抵扣比例高达100%',
        title: '金币超值抵扣，最高全免！',
      }, //金币折扣
      raffleInfo: {
        title: '金币赢好礼',
        prompt: 'SVIP免费领取',
        prize_info: {},
      }, //金币赢好礼
      svip_propList: [], // 签到奖励列表
      exchangeGoods: { list: [] }, //限时兑换信息
      swiperShow: true,
      signSuccessShow: false, // 签到成功弹窗
      experienceShow: false, // 点击体验弹窗
      yindaoPopup: false, //引导到添加主屏幕的弹窗
      points: [], // 签到成功信息
      reward_info: {}, // 签到成功信息
      svip_extra_gold: 0,
      goldBoxList: [],
      icon_path:
        'https://static-hw.3733.com/wa/lottie/qiandao/qiandao/data.json',
      gold_title: '每日签到',
      clockInDate: [], // 已签到的日期
      maxDays: 0, // 本月最长连续签到
      continousDay: 0, // 目前连续签到天数
      gold_icon: '',
      subtitle_img: '',
      next_reward_diff_day: 0,
      next_reward_num: 0,
      next_reward_type: 'gold',
      currentDay: new Date(defaultDate).getDate(),
      isKeep: false,
      currentDate: new Date(), // 当前日期
      notSvipSignShow: false, // 是否显示非svip签到弹窗
      isNotShowSvipSign: false,
      getAwardIncompletePopupShow: false,
      getAwardPopupShow: false,
      getAwardPopupInfo: {},
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    if (platform == 'android') {
      document.title = '金币中心';
    }
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleReturnTop() {
      window.scrollTo(0, 0);
    },
    completeTheTask(item) {
      switch (item.type) {
        case 'daily':
          this.handleGo1(item, item.key);
          break;
        case 'achievement':
          this.handleGo2(item, item.key);
          break;
        case 'new_user':
          this.handleGo3(item, item.key);
          break;
        default:
          break;
      }
    },
    taskToCollect(item) {
      switch (item.type) {
        case 'daily':
          this.handleGet1(item, item.key);
          break;
        case 'achievement':
          this.handleGet2(item, item.key);
          break;
        case 'new_user':
          this.handleGet3(item, item.key);
          break;
        default:
          break;
      }
    },
    // 每日任务
    handleGo1(item, key) {
      switch (key) {
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'pay':
          this.popupContent =
            item.task_prompt || '每日充值一次平台币即可完成任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'pay100':
        case 'pay500':
          this.popupContent =
            item.task_prompt ||
            `每日充值平台币满${item.need_value}元即可完成任务哦~`;
          this.popupTip = '注：每日累计充值时间为0点至24点';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'play_time':
          this.popupContent = '成功登录并畅玩任意游戏30分钟即可完成当前任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '我知道了';
          this.taskPopup = true;
          break;
        default:
          break;
      }
    },
    // 成就任务
    handleGo2(item, key) {
      switch (key) {
        case 'pay':
          this.popupContent = `累计充值平台币满${item.need_value}元即可完成任务`;
          this.popupConfirmText = this.$t('充值平台币');
          this.taskPopup = true;
          break;
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        default:
          break;
      }
    },
    // 新手任务
    handleGo3(item, key) {
      switch (key) {
        case 'band_email': // 绑定邮箱
          BOX_showActivity(
            { name: 'ChangeEmail' },
            { page: 'com.a3733.cwbgamebox.ui.mine.BindEmailActivity' },
          );
          break;
        case 'add_assistant':
          BOX_openInNewWindow(
            { name: 'AddAssistant' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/add_assistant` },
          );
          break;
        case 'bind_wx': // 绑定微信
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'follow_gzh': // 关注公众号
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'down_game': // 下载游戏
          BOX_showActivity({ name: 'Category' }, { page: 'qbyx' });
          break;
        case 'first_pay': // 首充
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满6元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'mem_info': // 实名认证
        case 'mobile': // 绑定手机
          let set_temp =
            platform == 'android'
              ? 'com.a3733.gamebox.ui.etc.AccountSafeActivity'
              : 'YYAccountAndSecurityViewController';
          BOX_showActivity({ name: 'UserInfo' }, { page: set_temp });
          break;
        case 'pay100': // 充值100元
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满100元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'bookmark': //IOS商店版添加IOS书签版
          if (item.is_diff_users) {
            this.$toast('该设备已完成过任务');
          } else {
            ApiMissionCheckToComplete().then(res => {
              BOX_openInBrowser(
                {
                  h5_url: `https://${
                    this.$h5Page.env
                  }game.3733.com${window.location.search.substring(0)}`,
                },
                {},
              );
            });
          }
          break;
        case 'down_zasq': //下载追爱神器
          if (platform == 'android') {
            BOX_goToGame(
              {
                params: {
                  id: item.jump_game_id,
                },
              },
              { id: item.jump_game_id },
            );
            return false;
          }
          navigateToGameDetail({ id: item.jump_game_id });
          break;
      }
    },
    // 每日任务
    async handleGet1(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getCoinCenterIndex();
      this.SET_USER_INFO();
    },
    // 成就任务
    async handleGet2(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetAchievementReward({
        mission: key,
        mission_level: item.mission_level,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getCoinCenterIndex();
      this.SET_USER_INFO();
    },
    // 新手任务
    async handleGet3(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getCoinCenterIndex();
      this.SET_USER_INFO();
    },
    formatRewards(item) {
      let rewards = [];
      if (item?.gold_num) {
        rewards.push('金币+' + item.gold_num);
      }
      if (item?.reward) {
        rewards.push('金币+' + item.reward);
      }
      if (item.coupon) {
        rewards.push(item.coupon);
      }
      if (item.exp_num) {
        rewards.push('经验+' + item.exp_num);
      }
      return rewards.join('、');
    },
    popupConfirm() {
      this.taskPopup = false;
      if (this.popupConfirmText == this.$t('我知道了')) {
        return false;
      }
      BOX_openInNewWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    async getDiscountGame() {
      const res = await ApiGame01DiscountList({ id: 151, listRows: 5 });
      this.gameList = res.data.game_list;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 290;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    isDayFilled(item) {
      return this.continousDay >= item.box_num;
    },
    async onResume() {
      this.SET_USER_INFO(true);
      await this.getCoinCenterIndex();
      await this.getCoinCenterIndexPage2();
      await this.getAchievementData();
      await this.getDiscountGame();
    },
    async getAchievementData() {
      const res = await ApiMissionGetAchievement();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList2 = list;
    },
    goToGoldGamble() {
      BOX_openInNewWindow(
        { name: 'GoldGamble' },
        { url: `${window.location.origin}/#/gold_gamble` },
      );
    },
    goToTurnTable() {
      BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
    },
    toClockIn() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'Welfare',
          type: 1,
        });
      } catch (e) {
        BOX_showActivity(
          { name: 'Welfare', params: { type: 1 } },
          { page: 'qd' },
        );
      }
    },
    openYindaoPopup() {
      if (needGuide && platform != 'android') {
        this.yindaoPopup = true;
      }
    },
    // 立即签到
    async rightOffSignIn() {
      let temDay =
        this.currentDay < 10
          ? `0${this.currentDay}`
          : this.currentDay.toString();
      if (!this.clockInDate.includes(temDay)) {
        let NO_SVIP_SIGN_DIALOG_HIDE = localStorage.getItem(
          'NO_SVIP_SIGN_DIALOG_HIDE',
        );
        // 非svip且弹窗还能显示的情况
        if (!NO_SVIP_SIGN_DIALOG_HIDE && !this.userInfo.is_svip) {
          this.notSvipSignShow = true;
          return false;
        }
        // 还没签到的情况
        await this.handleClockIn();
        return false;
      }
      this.$toast(this.$t('今日已签到'));
    },
    async handleClockIn() {
      this.notSvipSignShow = false;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiUserActiveSign();
      this.$toast.clear();
      if (res.data.reward_info) {
        // 弹出签到成功弹窗
        this.reward_info = res.data.reward_info;
        this.signSuccessShow = true;
      } else {
        this.$toast(res.msg);
      }
      await this.getCoinCenterIndex();
      this.SET_USER_INFO();
    },
    // 查看全部任务
    viewAllTasks() {
      this.toPage('GoldCoin');
    },
    // 查看更多福利
    viewMoreBenefits() {
      BOX_showActivity({ name: 'Welfare' }, { page: 'flzx' });
    },
    // 签到成功跳转svip
    toSvip() {
      this.notSvipSignShow = false;
      this.signSuccessShow = false;
      this.getAwardIncompletePopupShow = false;
      this.getAwardPopupShow = false;
      this.$nextTick(() => {
        this.toPage('Svip');
        // BOX_showActivity({ name: 'Svip' }, { page: 'qd' });
      });
    },
    async getCoinCenterIndex() {
      try {
        const res = await ApiCoinCenterIndex({ page: 1 });
        let {
          gold_box_list,
          clock_in_date,
          max_days,
          continuous_day,
          svip_extra_gold,
          next_reward_diff_day,
          next_reward_num,
          next_reward_type,
          subtitle_img,
          title,
          icon_path,
          btn_corner_text,
          no_svip_text,
          common_gold_text,
          svip_gold_text,
        } = res.data.gold;
        // this.bg_img = res.data.base.bg_img;
        this.missionInfo = res.data.mission;
        this.svipWelfareInfo = res.data.svip;

        let newBox = {
          box_num: 1,
          gold: 0,
          state: 0,
          need_share: 0,
          level: 0,
        };
        this.goldBoxList = [newBox, ...gold_box_list];
        this.noSvipText = no_svip_text || [];
        this.svipTip = btn_corner_text;
        if (common_gold_text) {
          this.common_gold_text = common_gold_text;
        }
        if (svip_gold_text) {
          this.svip_gold_text = svip_gold_text;
        }
        this.clockInDate = clock_in_date;
        this.maxDays = max_days;
        this.gold_title = title;
        this.continousDay = continuous_day;
        this.svip_extra_gold = svip_extra_gold;
        this.next_reward_diff_day = next_reward_diff_day;
        this.next_reward_num = next_reward_num;
        this.next_reward_type = next_reward_type;
        this.subtitle_img = subtitle_img;
        this.icon_path = icon_path;
      } finally {
        // this.navbarOpacity = 0;
        // this.bgStyle = 'transparent-white';
        // this.loadSuccess = true;
      }
    },
    async getCoinCenterIndexPage2() {
      const res = await ApiCoinCenterIndex({ page: 2 });
      let { activity, gold_game, raffle, svip_prop, exchange_goods } = res.data;
      this.activityInfo = activity;
      this.goldGameInfo = gold_game;
      this.raffleInfo = raffle;
      this.svip_propList = svip_prop;
      this.exchangeGoods = exchange_goods;
    },
    // 是否是签到过的
    isSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.clockInDate.length > 0 && this.clockInDate.includes(str)) {
        return true;
      }
      return false;
    },
    async handleGet(item) {
      if (item.box_num == 1) return;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      // 文案由后端返回，所以前端没有判断是否已领取过或者还不能领取，所有宝箱均可点击
      await this.fetchGetGoldBox(item);
    },
    fetchGetGoldBoxAfter() {
      this.SET_USER_INFO(true);
    },
    async fetchGetGoldBox(item) {
      try {
        const res = await ApiUserGetGoldBox({
          box_num: item.box_num,
        });
        let {
          need_share,
          gold_box_list,
          clock_in_date,
          is_svip,
          get_box,
          show_text,
          share_info,
          list,
        } = res.data;
        if (res.code > 0) {
          this.$toast.clear();
          if (res.data.pop) {
            this.getAwardPopupInfo = res.data.pop;
            this.getAwardIncompletePopupShow = true;
            return false;
          }
          if (res.points) {
            this.getAwardPopupInfo = {
              ...res.data.prize_info,
              svip_text: res.data.svip_text || '',
            };
            this.getAwardPopupShow = true;
          }
          await this.getCoinCenterIndex();
        }

        // 以下两个if为刷新宝箱数据
        if (gold_box_list && gold_box_list.length) {
          let newBox = {
            box_num: 1,
            gold: 0,
            state: 0,
            need_share: 0,
            level: 0,
          };
          this.goldBoxList = [newBox, ...gold_box_list];
        }
        if (clock_in_date && clock_in_date.length) {
          this.clockInDate = clock_in_date;
        }

        if (need_share == true) {
          // 需要分享的
          if (is_svip == false) {
            // 非svip才需要助力
            if (get_box == false) {
              // 还需要助力
              this.goldBoxShareData = {
                gold: item.gold,
                showText: show_text,
              };
              if (share_info) {
                this.shareInfo = share_info;
              }
              this.goldBoxShareShow = true;
            } else {
              // 助力成功
              this.goldBoxShareSuccessData = list[0];
              this.goldBoxShareSuccessData.gold = item.gold;
              this.goldBoxShareSuccessShow = true;
            }
          } else {
            // svip直接领取的弹窗
            this.goldBoxSvipSuccessData = {
              gold: item.gold,
            };
            this.goldBoxSvipSuccessShow = true;
          }
        }
      } catch (e) {}
    },
    openGame(detail) {
      BOX_openApp(detail.package_name);
    },
    openGameShow(detail) {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          return BOX.checkInstall(detail.package_name);
        } catch (error) {
          return false;
        }
      } else {
        return false;
      }
    },
    toExchangeDetail(detail) {
      this.toPage('ExchangeDetail', { id: detail.id });
    },
    closeNotSvipSignShow() {
      if (this.isNotShowSvipSign) {
        this.isNotShowSvipSign = false;
        localStorage.setItem('NO_SVIP_SIGN_DIALOG_HIDE', true);
      }
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
  computed: {
    svipPropListLength() {
      return this.svip_propList.filter(item => {
        const list = item?.list || [];
        return list.length > 0;
      }).length;
    },
    isShowMoreBenefits() {
      if (platform == 'android') {
        if (authInfo.versionCode <= 4209) {
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    hasStateActive() {
      const formatDate = day => (day < 10 ? '0' + day : day.toString());

      // 当天日期
      const clockDate = formatDate(this.currentDay);

      // 前一天日期
      const frontClockDate = formatDate(this.currentDay - 1);

      // 今日已签到 或 昨天已签到
      return (
        this.clockInDate.includes(clockDate) ||
        (!this.clockInDate.includes(clockDate) &&
          this.clockInDate.includes(frontClockDate))
      );
    },
    lineWidth() {
      if (
        this.continousDay >=
        this.goldBoxList[this.goldBoxList.length - 1]?.box_num
      ) {
        return '100%';
      } else if (
        this.continousDay >
          this.goldBoxList[this.goldBoxList.length - 2]?.box_num &&
        this.continousDay <
          this.goldBoxList[this.goldBoxList.length - 1]?.box_num
      ) {
        return '90%';
      } else if (
        this.continousDay ===
        this.goldBoxList[this.goldBoxList.length - 2]?.box_num
      ) {
        return '80%';
      } else if (
        this.continousDay >
          this.goldBoxList[this.goldBoxList.length - 3]?.box_num &&
        this.continousDay <
          this.goldBoxList[this.goldBoxList.length - 2]?.box_num
      ) {
        return '70%';
      } else if (
        this.continousDay ===
        this.goldBoxList[this.goldBoxList.length - 3]?.box_num
      ) {
        return '60%';
      } else if (
        this.continousDay >
          this.goldBoxList[this.goldBoxList.length - 4]?.box_num &&
        this.continousDay <
          this.goldBoxList[this.goldBoxList.length - 3]?.box_num
      ) {
        return '50%';
      } else if (
        this.continousDay ===
        this.goldBoxList[this.goldBoxList.length - 4]?.box_num
      ) {
        return '40%';
      } else if (
        this.continousDay >
          this.goldBoxList[this.goldBoxList.length - 5]?.box_num &&
        this.continousDay <
          this.goldBoxList[this.goldBoxList.length - 4]?.box_num
      ) {
        return '30%';
      } else if (
        this.continousDay ===
        this.goldBoxList[this.goldBoxList.length - 5]?.box_num
      ) {
        return '20%';
      } else if (
        this.continousDay >
          this.goldBoxList[this.goldBoxList.length - 6]?.box_num &&
        this.continousDay <
          this.goldBoxList[this.goldBoxList.length - 5]?.box_num
      ) {
        return '10%';
      } else {
        return '0%';
      }
    },
    clockInBtnText() {
      let clockDate =
        this.currentDay < 10
          ? '0' + this.currentDay
          : this.currentDay.toString();
      return this.clockInDate.includes(clockDate)
        ? this.$t('已签到')
        : this.userInfo.is_svip
        ? this.$t('豪华签到')
        : this.$t('立即签到');
    },
    shouldShowFixSign() {
      const currentDay = this.currentDate.getDate(); // 获取当前几号

      // 生成当前日期之前的所有天数 包含当天日期
      const daysBeforeToday = Array.from({ length: currentDay }, (_, i) =>
        (i + 1).toString().padStart(2, '0'),
      );

      // 判断是否有未签到的日期
      const hasMissingSign = daysBeforeToday.some(
        day => !this.clockInDate.includes(day),
      );
      return hasMissingSign;
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async activated() {
    this.isKeep = true;
    this.currentDay = new Date(defaultDate).getDate();
    await this.getCoinCenterIndex();
    await this.getCoinCenterIndexPage2();
    await this.getAchievementData();
    await this.getDiscountGame();
  },
  deactivated() {
    this.isKeep = false;
  },
};
</script>

<style lang="less" scoped>
.gold-coin-center {
  background: #f5f5f6;
  .coin-tips {
    color: #fff;
    font-size: 14 * @rem;
    &.black {
      color: #000000;
    }
  }
  .notice-bar {
    width: 100%;
    height: 28 * @rem;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    margin-top: 50 * @rem;
    box-sizing: border-box;
    /deep/ .van-notice-bar__content {
      font-size: 13 * @rem;
    }
  }
  .main {
    background: #f5f5f6;
    flex: 1;
    .top-bg {
      width: 100%;
      height: 331 * @rem;
      .top-bg-img {
        position: relative;
      }
    }
    .gold-bar {
      box-sizing: border-box;
      position: relative;
      margin-top: -217 * @rem;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      max-width: 375 * @rem;
      padding: 0 20 * @rem;
      .gold-info {
        .title {
          height: 18 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #fff;
          line-height: 18 * @rem;
        }
        .gold-balance {
          display: flex;
          align-items: center;
          margin-top: 20 * @rem;
          .gold-num {
            white-space: nowrap;
            height: 39 * @rem;
            font-size: 28 * @rem;
            color: #fff;
            line-height: 39 * @rem;
            font-weight: bold;
          }
          .gold-detail {
            margin-left: 8 * @rem;
            font-size: 11 * @rem;
            color: #ffffff;
            padding-right: 10 * @rem;
            background: url(~@/assets/images/recharge/right-icon-white.png)
              right center no-repeat;
            background-size: 6 * @rem 10 * @rem;
          }
        }
      }
    }
    .signIn-container {
      position: relative;
      background: url(~@/assets/images/recharge/signIn-bg.png) no-repeat 0 0;
      background-size: 355 * @rem 235 * @rem;
      width: 355 * @rem;
      height: 235 * @rem;
      margin: 29 * @rem auto 0;
      box-sizing: border-box;
      padding: 16 * @rem;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .signIn-container-top {
        display: flex;
        align-items: center;
        .title {
          width: 73 * @rem;
          white-space: nowrap;
          height: 25 * @rem;
          font-size: 18 * @rem;
          color: #222222;
          line-height: 25 * @rem;
          font-weight: bold;
        }
        .icon {
          margin-left: 4 * @rem;
          background-repeat: no-repeat;
          background-position: 0 0;
          background-size: 98 * @rem 20 * @rem;
          width: 98 * @rem;
          height: 20 * @rem;
        }
      }
      .signIn-container-tips {
        margin-top: 8 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #999999;
        height: 14 * @rem;
        line-height: 14 * @rem;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 2;
        .days {
          color: #ed5c1f;
        }
        > div {
          display: flex;
          align-items: center;
        }
        > span {
          margin-left: 8 * @rem;
          font-size: 10 * @rem;
          color: #eb5210;
        }

        .svip {
          display: flex;
          align-items: center;
          color: #ed5c1f;

          &.gold::after {
            content: '';
            display: block;
            width: 10 * @rem;
            height: 10 * @rem;
            background: url(~@/assets/images/recharge/small-coin-icon.png)
              no-repeat;
            background-size: 10 * @rem 10 * @rem;
            margin-left: 1 * @rem;
            margin-right: 2 * @rem;
          }
        }
        > i {
          margin-left: 4 * @rem;
          background: url(~@/assets/images/recharge/signln-right-icon.png)
            no-repeat 0 0;
          background-size: 6 * @rem 10 * @rem;
          width: 6 * @rem;
          height: 10 * @rem;
        }
      }
      .signIn-container-speed {
        margin-top: 15 * @rem;
        height: 75 * @rem;
        line-height: 75 * @rem;
        position: relative;
        z-index: 1;
        .speed-list {
          position: absolute;
          width: 100%;
          top: 45 * @rem;
          left: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          transform: translate(-50%, -50%);
          .progress-container {
            width: 100%;
            max-width: 400 * @rem;
            .days {
              display: flex;
              justify-content: space-between;
              position: relative;
              padding: 0 10 * @rem;
              .day {
                width: 8 * @rem;
                height: 8 * @rem;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                z-index: 1;
                background-color: #d9d9d9;
                box-sizing: border-box;
                position: relative;
                .day-icon {
                  bottom: 32 * @rem;
                  display: flex;
                  align-items: center;
                  position: relative;
                  .day-icon-img {
                    background: url(~@/assets/images/recharge/gold-signln-day.png)
                      no-repeat center;
                    background-size: 54 * @rem 54 * @rem;
                    width: 36 * @rem;
                    height: 36 * @rem;
                    &.svip {
                      background-image: url(~@/assets/images/recharge/svip-signln-day.png);
                      background-size: 36 * @rem 36 * @rem;
                    }
                  }
                  .day-icon-img1 {
                    width: 48 * @rem;
                    height: 48 * @rem;
                  }
                  .day-icon-img2 {
                    background: url(~@/assets/images/recharge/gold-signln-day-ok.png)
                      no-repeat;
                    background-size: 36 * @rem 36 * @rem;
                    width: 36 * @rem;
                    height: 36 * @rem;
                    &.svip {
                      background-image: url(~@/assets/images/recharge/svip-signln-day-ok.png);
                    }
                  }
                  .day-icon-img3 {
                    background: url(~@/assets/images/recharge/svip-signln-day-active.png)
                      no-repeat;
                    background-size: 36 * @rem 36 * @rem;
                    width: 36 * @rem;
                    height: 36 * @rem;
                  }
                  .activeSigln {
                    background: url(~@/assets/images/recharge/gold-signln-day-active.png)
                      no-repeat -8 * @rem -8 * @rem;
                    background-size: 48 * @rem 48 * @rem;
                    width: 32 * @rem;
                    height: 32 * @rem;
                    &.svip {
                      background-image: url(~@/assets/images/recharge/svip-signln-day-active.png);
                    }
                  }
                  .day-icon-first {
                    width: 48 * @rem;
                    height: 48 * @rem;
                  }
                }
                .day-icon-btn {
                  bottom: 12 * @rem;
                  position: absolute;
                  width: 48 * @rem;
                  height: 48 * @rem;
                  z-index: 9;

                  .gold {
                    height: 11 * @rem;
                    position: absolute;
                    bottom: 6 * @rem;
                    right: 1 * @rem;
                    img {
                      width: auto;
                    }
                  }
                }
                .day-icon-xian {
                  position: absolute;
                  left: 27 * @rem;
                  bottom: 31 * @rem;
                  display: inline-block;
                  width: 10 * @rem;
                  height: 2 * @rem;
                  background: #ececec;
                }
                .day-text {
                  position: absolute;
                  font-weight: 500;
                  font-size: 10 * @rem;
                  color: #534c6f;
                  line-height: 14 * @rem;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  white-space: nowrap;
                  top: 14 * @rem;
                }
              }
              .filled {
                background-color: #ff8d35;
                color: white;
              }
              .progress-line {
                position: absolute;
                top: 3 * @rem;
                left: 18 * @rem;
                right: 18 * @rem;
                height: 2 * @rem;
                background-color: lightgray;
                z-index: 0;
                .line {
                  height: 100%;
                  background-color: #ff8d35;
                }
              }
            }
          }
        }
      }
      .signIn-container-btn {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280 * @rem;
        height: 40 * @rem;
        background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
        box-shadow: inset 0 0 10 * @rem 0 rgba(255, 255, 255, 0.7);
        border-radius: 22 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        margin: 18 * @rem auto 0;

        &.svip {
          background: url(~@/assets/images/clock-in/svip-clock-in-bg.png)
            no-repeat;
          background-size: contain;
        }

        &.had {
          color: #fff;
          background: linear-gradient(90deg, #888995 0%, #cecfd4 100%);
        }
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 78 * @rem;
          height: 25 * @rem;
          background: url(~@/assets/images/clock-in/tomorrow-sign-in2.png)
            no-repeat;
          background-size: 78 * @rem 25 * @rem;
          position: absolute;
          right: 0;
          top: -5 * @rem;
        }
      }
    }
    .first {
      margin-top: -162 * @rem;
    }
    .task-container {
      position: relative;
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 16 * @rem 0;
      box-sizing: border-box;
      .task-title {
        width: 73 * @rem;
        white-space: nowrap;
        height: 25 * @rem;
        font-size: 18 * @rem;
        color: #222222;
        line-height: 25 * @rem;
        font-weight: bold;
      }
      .task-list {
        .task-item {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          margin-top: 20 * @rem;
          height: 36 * @rem;
          .icon {
            width: 40 * @rem;
            height: 40 * @rem;
            background-color: #f5f5f5;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 24 * @rem;
              height: 24 * @rem;
            }
          }
          .task-info {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .title {
              font-size: 13 * @rem;
              color: #222222;
              height: 18 * @rem;
              line-height: 18 * @rem;
              font-weight: bold;
              text-align: left;
              word-break: break-all;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .subtitle {
              font-size: 10 * @rem;
              color: #7a7a7a;
              line-height: 18 * @rem;
              font-weight: 500;
              text-align: left;
              word-break: break-all;
              margin-top: 2 * @rem;
            }
            .line {
              display: flex;
              align-items: center;
              margin-top: 6 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 181 * @rem;
              flex-shrink: 0;
              .gold {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-gold.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }
              .exp {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-exp.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }
              .coupon {
                padding-left: 20 * @rem;
                background: url(~@/assets/images/recharge/task-coupon.png) left
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
                line-height: 18 * @rem;
                font-size: 12 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                margin-right: 12 * @rem;
              }
              .reward-info {
                color: #f05f29;
              }
            }
            .extra {
              font-size: 11 * @rem;
              color: #777777;
              line-height: 14 * @rem;
              margin-top: 6 * @rem;
            }
          }
          .task-btn {
            width: 72 * @rem;
            height: 28 * @rem;
            border-radius: 25 * @rem;
            border: 1 * @rem solid #cccccc;
            font-size: 12 * @rem;
            color: #333333;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            &.had {
              color: #cccccc;
              border: 1 * @rem solid rgba(204, 204, 204, 0.5);
            }
            &.get {
              color: #fff;
              background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
              box-shadow: inset 0 0 10 * @rem 0 rgba(255, 255, 255, 0.7);
              border: 0;
            }
          }
        }

        .exchange-down {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 18 * @rem 0 15 * @rem;
          span {
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-size: 12 * @rem;
            color: #777777;
          }
          i {
            display: block;
            margin-left: 4 * @rem;
            width: 12 * @rem;
            height: 12 * @rem;
          }
          &.up {
            i {
              margin-top: -1 * @rem;
              transform: rotate(-90deg);
            }
          }
        }
      }
    }
    .svip-exclusive-container {
      position: relative;
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem;
      box-sizing: border-box;
      .task-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .task-title-left {
          display: flex;
          align-items: center;
          .title {
            white-space: nowrap;
            height: 25 * @rem;
            font-size: 18 * @rem;
            color: #222222;
            line-height: 25 * @rem;
            font-weight: bold;
          }
          .sub-title {
            margin-left: 4 * @rem;
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 68 * @rem 20 * @rem;
            width: 68 * @rem;
            height: 20 * @rem;
          }
        }
        .task-title-right {
          white-space: nowrap;
          width: 48 * @rem;
          height: 20 * @rem;
          line-height: 20 * @rem;
          text-align: center;
          border-radius: 44 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #7a7a7a;
          border: 1 * @rem solid #cccccc;
        }
      }
      .task-content {
        margin-top: 17 * @rem;
        .svip-exchange-list {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .exchange-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            .item-img {
              width: 100 * @rem;
              height: 70 * @rem;
              background: #f5f5f5;
              border-radius: 8 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                // width: 64 * @rem;
                height: 62 * @rem;
              }
            }
            .item-title {
              margin-top: 7 * @rem;
              max-width: 100 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              height: 18 * @rem;
              font-weight: bold;
              font-size: 13 * @rem;
              color: #222222;
              line-height: 18 * @rem;
            }
          }
        }
        .svip-exchange-btn {
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 280 * @rem;
          height: 40 * @rem;
          margin: 0 auto;
          margin-top: 16 * @rem;
          img {
            position: absolute;
            background-size: 280 * @rem 40 * @rem;
            width: 100%;
            height: auto;
          }
          span {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 500;
            font-size: 15 * @rem;
            color: #ffffff;
          }
        }
      }
    }
    .prop-container {
      position: relative;
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 8 * @rem;
      box-sizing: border-box;
      background: linear-gradient(180deg, #fdecd6 0%, #ffffff 100%);
      .prop-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .prop-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .prop-title {
            white-space: nowrap;
            height: 21 * @rem;
            font-weight: bold;
            font-size: 15 * @rem;
            color: #333333;
            line-height: 21 * @rem;
          }
          .prop-center {
            margin-top: 2 * @rem;
            background: url(~@/assets/images/recharge/svip-prop-bg1.png)
              no-repeat 0 -1 * @rem;
            background-size: 107 * @rem 147 * @rem;
            width: 107 * @rem;
            height: 146 * @rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            .prop-item-box {
              margin-top: 16 * @rem;
              display: flex;
              flex-direction: column;
              align-items: center;
              box-sizing: border-box;
              padding: 0 12 * @rem;
              .logo-box {
                min-width: 85 * @rem;
                width: 100%;
                height: 85 * @rem;
                background: #f5f5f5;
                border-radius: 11 * @rem;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                .big-logo {
                  width: 58 * @rem;
                  height: 52 * @rem;
                }
                .small-logo {
                  position: absolute;
                  width: 18 * @rem;
                  height: 18 * @rem;
                  border-radius: 4 * @rem;
                  top: 0;
                  right: -4 * @rem;
                }
              }
              .name {
                margin-top: 2 * @rem;
                width: 107 * @rem;
                text-align: center;
                height: 18 * @rem;
                font-weight: 500;
                font-size: 13 * @rem;
                color: #666666;
                line-height: 18 * @rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .price {
                margin-top: 5 * @rem;
                height: 15 * @rem;
                font-weight: bold;
                font-size: 11 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
              }
            }
            &.prop-center2 {
              background: url(~@/assets/images/recharge/svip-prop-bg2.png)
                no-repeat 0 -1 * @rem;
              background-size: 165 * @rem 147 * @rem;
              width: 165 * @rem;
              height: 146 * @rem;
            }
          }
        }
        &.justify_center {
          justify-content: center;
        }
      }
      .prop-btn {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280 * @rem;
        height: 40 * @rem;
        margin: 0 auto;
        margin-top: 12 * @rem;
        img {
          position: absolute;
          background-size: 280 * @rem 40 * @rem;
          width: 100%;
          height: auto;
        }
        span {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-weight: 500;
          font-size: 15 * @rem;
          color: #ffffff;
        }
      }
    }
    .gold-exchange-container {
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 12 * @rem 0;
      box-sizing: border-box;
      .exchange-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .exchange-title-left {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;

          .title {
            height: 25 * @rem;
            font-size: 18 * @rem;
            color: #222222;
            line-height: 25 * @rem;
            font-weight: bold;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
        .exchange-title-right {
          flex-shrink: 0;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #7a7a7a;
          line-height: 14 * @rem;
          text-align: right;
        }
      }
      .exchange-list {
        display: flex;
        overflow-x: auto;
        padding: 17 * @rem 0 16 * @rem;
        &::-webkit-scrollbar {
          display: none;
        }

        .exchange-item {
          flex-shrink: 0;
          width: 100 * @rem;
          margin-right: 12 * @rem;

          &:last-of-type {
            margin-right: 0;
          }

          .img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100 * @rem;
            height: 70 * @rem;
            margin: 0 auto;
            background-color: #f5f5f5;
            border-radius: 8 * @rem;

            img {
              width: 100 * @rem;
              height: 70 * @rem;
              border-radius: 2 * @rem;
            }
          }

          .item-title {
            width: 100%;
            height: 18 * @rem;
            font-weight: bold;
            font-size: 13 * @rem;
            color: #666;
            line-height: 18 * @rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 2 * @rem;
          }

          .need-gold {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 15 * @rem;
            margin: 6 * @rem auto 0;
            font-weight: bold;
            font-size: 11 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            text-align: center;
          }
        }
      }
    }
    .gold-discount-container {
      position: relative;
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 12 * @rem;
      box-sizing: border-box;
      .discount-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .discount-title-left {
          display: flex;
          align-items: center;
          .title {
            white-space: nowrap;
            height: 25 * @rem;
            font-size: 18 * @rem;
            color: #222222;
            line-height: 25 * @rem;
            font-weight: bold;
          }
        }
        .discount-title-right {
          white-space: nowrap;
          width: 48 * @rem;
          height: 20 * @rem;
          line-height: 20 * @rem;
          text-align: center;
          border-radius: 44 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #7a7a7a;
          border: 1 * @rem solid #cccccc;
        }
      }
      .discount-tips {
        margin-top: 8 * @rem;
        display: flex;
        align-items: center;
        > div {
          height: 14 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #7a7a7a;
          line-height: 14 * @rem;
        }
        > a {
          margin-left: 8 * @rem;
          height: 15 * @rem;
          font-weight: 500;
          font-size: 11 * @rem;
          color: #21b98a;
          line-height: 15 * @rem;
          text-decoration: underline;
        }
      }
      .game-list {
        padding: 5 * @rem 0;
        overflow: hidden;
        .game-item {
          display: flex;
          align-items: center;
          .btn {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64 * @rem;
            height: 30 * @rem;
            background: @themeBg;
            border-radius: 19 * @rem;
            color: #fff;
            line-height: 30 * @rem;
            text-align: center;
            font-weight: 500;
            font-size: 12 * @rem;
          }
          .game-btn {
            position: relative;
            /deep/.download-btn {
              width: 64 * @rem;
              height: 30 * @rem;
              background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
              border-radius: 29 * @rem;
              font-weight: 500;
              font-size: 14 * @rem;
              color: #ffffff;
            }
            .download-btn {
              box-sizing: border-box;
              width: 64 * @rem;
              height: 30 * @rem;
              font-size: 12 * @rem;
              color: #fff;
              border-radius: 30 * @rem;
              background: @themeBg;
              display: flex;
              align-items: center;
              justify-content: center;
              background: linear-gradient(62deg, #46a6ff 0%, #3ac4ff 100%);
            }
            .deduction-tips {
              position: absolute;
              top: -14 * @rem;
              left: 0 * @rem;
              //   transform: rotate(-9.24deg);
              height: 20 * @rem;
              //   overflow: hidden;
              text {
                height: 12 * @rem;
                fill: #fff;
                font-size: 12 * @rem;
                stroke: #f7552b;
                stroke-width: 3 * @rem;
                font-weight: bold;
                paint-order: stroke fill;
                stroke-linejoin: round;
              }
              .deduction-tips-box {
                display: flex;
                align-items: center;
                div {
                  background: url('~@/assets/images/recharge/zhekou_icon.png')
                    no-repeat 0 0;
                  background-size: 65 * @rem 20 * @rem;
                  width: 65 * @rem;
                  height: 20 * @rem;
                  &.zhekou100_icon {
                    background: url('~@/assets/images/recharge/zhekou100_icon.png')
                      no-repeat 0 0;
                    background-size: 64 * @rem 20 * @rem;
                    width: 64 * @rem;
                    height: 20 * @rem;
                  }
                }
                span {
                  margin-top: 3 * @rem;
                  height: 20 * @rem;
                  position: absolute;
                  right: 5 * @rem;
                  font-family: Inter, Inter;
                  font-weight: bold;
                  font-size: 12 * @rem;
                  color: #ffffff;
                  &.zhekou11Size {
                    right: 4 * @rem;
                    height: 20 * @rem;
                    line-height: 15 * @rem;
                    font-size: 11 * @rem;
                  }
                }
              }
            }
          }
        }
      }
    }
    .gold-gifts-container {
      position: relative;
      margin: 12 * @rem auto 0;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 16 * @rem 20 * @rem 16 * @rem;
      box-sizing: border-box;
      .discount-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .discount-title-left {
          display: flex;
          align-items: center;
          .title {
            white-space: nowrap;
            height: 25 * @rem;
            font-size: 18 * @rem;
            color: #222222;
            line-height: 25 * @rem;
            font-weight: bold;
          }
        }
        .discount-title-right {
          white-space: nowrap;
          width: 60 * @rem;
          height: 20 * @rem;
          line-height: 20 * @rem;
          text-align: center;
          border-radius: 44 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #7a7a7a;
          border: 1 * @rem solid #cccccc;
        }
      }
      .gold-gifts-list {
        margin-top: 16 * @rem;
        display: flex;
        flex-direction: column;
        .gold-gifts-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .item-left-box {
            display: flex;
            align-items: center;
            .item-img {
              width: 80 * @rem;
              height: 80 * @rem;
              background: #f5f5f5;
              border-radius: 8 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 64 * @rem;
                height: 64 * @rem;
              }
            }
            .item-info {
              margin-left: 8 * @rem;

              > div:nth-child(1) {
                margin-top: 6 * @rem;
                height: 22 * @rem;
                font-weight: bold;
                font-size: 16 * @rem;
                color: #222222;
                line-height: 22 * @rem;
                width: 150 * @rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              > div:nth-child(2) {
                margin-top: 2 * @rem;
                height: 15 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #999999;
                line-height: 15 * @rem;
                width: 150 * @rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              > div:nth-child(3) {
                margin-top: 17 * @rem;
                height: 15 * @rem;
                font-weight: bold;
                font-size: 11 * @rem;
                color: #f05f29;
                line-height: 15 * @rem;
                width: 150 * @rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
          .item-right-btn {
            width: 72 * @rem;
            white-space: nowrap;
            height: 28 * @rem;
            background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
            box-shadow: inset 0 0 10 * @rem 0 rgba(255, 255, 255, 0.7);
            border-radius: 22 * @rem;
            font-size: 12 * @rem;
            color: #ffffff;
            text-align: center;
            line-height: 28 * @rem;
          }
        }
      }
    }
    .gold-activity-container {
      position: relative;
      margin: 12 * @rem auto 20 * @rem;
      width: 355 * @rem;
      background: #ffffff;
      box-shadow: 0 0 8 * @rem 0 rgba(136, 56, 39, 0.05);
      border-radius: 16 * @rem;
      padding: 16 * @rem 12 * @rem;
      box-sizing: border-box;
      .activity-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .activity-title-left {
          display: flex;
          align-items: center;
          .title {
            white-space: nowrap;
            height: 25 * @rem;
            font-size: 18 * @rem;
            color: #222222;
            line-height: 25 * @rem;
            font-weight: bold;
          }
        }
        .activity-title-right {
          .container {
            // .white-wrapper {
            //   width: 100%;
            //   height: 27 * @rem;
            //   position: absolute;
            //   z-index: 8;
            //   left: 0;
            //   top: 0;
            //   background: linear-gradient(
            //     180deg,
            //     #ffffff 28%,
            //     rgba(255, 255, 255, 0) 82%
            //   );
            // }
            .history-list {
              height: 25 * @rem;
              // padding: 0 20 * @rem;
              position: relative;

              /deep/ .swiper-wrapper {
                transition-timing-function: linear !important;
              }
              .history-item {
                display: flex;
                align-items: center;
                height: 25 * @rem;
                .avatar {
                  width: 18 * @rem;
                  height: 18 * @rem;
                }
                .nickname {
                  margin-left: 7 * @rem;
                  font-size: 12 * @rem;
                  color: #9b89c7;
                  flex: 1;
                  min-width: 0;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
                .history-desc {
                  width: 80 * @rem;
                  color: #9b89c7;
                  font-size: 12 * @rem;
                  text-align: center;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  span {
                    color: #ff471f;
                  }
                }
              }
            }
          }
        }
      }
      .activity-list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15 * @rem;
        .activity-item {
          width: 160 * @rem;
          height: 99 * @rem;
        }
      }
    }
  }
  .signIn-container-btn1 {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40 * @rem;
    margin: 0 38 * @rem 31 * @rem;
    background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
    box-shadow: inset 0 0 10 * @rem 0 rgba(255, 255, 255, 0.7);
    border-radius: 22 * @rem;
    span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
    }
  }
}
.yindao-popup {
  width: 250 * @rem;
  padding: 40 * @rem 35 * @rem 20 * @rem;
  background-color: #fff;
  border-radius: 10 * @rem;
  line-height: 28 * @rem;
  .close {
    position: absolute;
    top: 15 * @rem;
    right: 15 * @rem;
    width: 16 * @rem;
    height: 16 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .text {
    text-align: center;
    font-size: 15 * @rem;
  }
  .down-arrow {
    margin: 15 * @rem auto 0;
    width: 16 * @rem;
    height: 20 * @rem;
    background-image: url(~@/assets/images/clock-in/down-arrow.png);
    background-size: 100%;
    background-repeat: no-repeat;
    -webkit-animation: downward 0.8s ease-in-out infinite;
    animation: downward 0.8s ease-in-out infinite;
  }
}
@keyframes downward {
  0% {
  }
  50% {
    transform: translate(0, 10 * @rem);
  }
  to {
    transform: translate(0, 0);
  }
}
.sign-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 97 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-logo3.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -43 * @rem;
    z-index: 2;
    padding: 50 * @rem 23 * @rem 21 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg3.png')
      no-repeat 0 0;
    background-size: 300 * @rem 150 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 25 * @rem;
      font-weight: normal;
      font-size: 18 * @rem;
      color: #191b1f;
      line-height: 25 * @rem;
      text-align: center;
      font-weight: bold;
    }
    .reward-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 21 * @rem;

      .reward-item {
        margin-left: 20 * @rem;

        &:first-of-type {
          margin-left: 0;
        }

        .exp-icon {
          width: 48 * @rem;
          height: 36 * @rem;
          background: url(~@/assets/images/clock-in/new-icon-exp.png) no-repeat;
          background-size: 48 * @rem 36 * @rem;
        }

        .gold-icon {
          width: 48 * @rem;
          height: 36 * @rem;
          background: url(~@/assets/images/clock-in/new-icon-coin.png) no-repeat;
          background-size: 48 * @rem 36 * @rem;
        }

        .reward-msg {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          margin-top: 2 * @rem;

          span {
            height: 11 * @rem;
            font-weight: bold;
            font-size: 9 * @rem;
            color: #191b1f;
            line-height: 11 * @rem;
          }

          .msg-number {
            height: 14 * @rem;
            font-weight: bold;
            font-size: 14 * @rem;
            color: #191b1f;
            line-height: 14 * @rem;
          }
        }

        &.svip-more-gold {
          margin-left: 7 * @rem;
          .gold-icon {
            background-image: url(~@/assets/images/clock-in/new-icon-coin2.png);
          }

          .reward-msg {
            span {
              color: #ff8819;
            }
            .msg-number {
              color: #ff8819;
            }
          }
        }
      }

      .upgrade {
        margin-left: 13 * @rem;

        .text {
          height: 14 * @rem;
          font-weight: bold;
          font-size: 10 * @rem;
          color: #ff8819;
          line-height: 14 * @rem;
          text-align: center;
        }

        .up-icon {
          width: 20 * @rem;
          height: 21 * @rem;
          background: url(~@/assets/images/clock-in/level-up-icon.png) no-repeat;
          background-size: 20 * @rem 21 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
    .svip-tips {
      width: 224 * @rem;
      padding: 8 * @rem 15 * @rem;
      margin: 13 * @rem auto 0;
      box-sizing: border-box;
      border-radius: 8 * @rem;
      background: linear-gradient(180deg, #fff2e5 0%, #ffffff 48 * @rem);

      .tips-title {
        height: 17 * @rem;
        font-weight: bold;
        font-size: 12 * @rem;
        color: #ff8819;
        line-height: 17 * @rem;
        text-align: left;
        margin-bottom: 1 * @rem;
      }
      .small-tips {
        display: flex;
        align-items: center;
        height: 15 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #93999f;
        line-height: 15 * @rem;
        margin-top: 6 * @rem;

        &::before {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/welfare/tips-star-icon.png) no-repeat;
          background-size: 6 * @rem 6 * @rem;
          margin-right: 4 * @rem;
        }
      }
    }
    .btn-list {
      display: flex;
      align-content: center;
      justify-content: space-between;
      margin-top: 21 * @rem;
      .btn {
        flex: 1;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: #ffeadd;
        border-radius: 30 * @rem;
        font-weight: bold;
        font-size: 15 * @rem;
        color: #fe6600;
        text-align: center;
        margin-right: 14 * @rem;
        position: relative;

        &:last-of-type {
          background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
          color: #fff;
          margin-right: 0;
        }

        .svip-tip {
          width: unset;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 18 * @rem;
          padding: 0 8 * @rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          font-size: 9 * @rem;
          color: #c05c2a;
          line-height: 11 * @rem;
          text-align: center;
          border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
          box-sizing: border-box;
          background: linear-gradient(
            90deg,
            #ffeaab 0%,
            #ffe0dd 54%,
            #ffbdcf 100%
          );
          border: 1 * @rem solid #ffffff;
          position: absolute;
          top: -13 * @rem;
          right: -8 * @rem;
        }
      }
    }
    .tips {
      margin-top: 12 * @rem;
      white-space: nowrap;
      height: 15 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      font-size: 12 * @rem;
      color: #fe6600;
      line-height: 15 * @rem;
      text-align: center;
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.sign-before-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 105 * @rem;
    .image-bg('~@/assets/images/clock-in/clock-in-not-svip.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -58 * @rem;
    z-index: 2;
    padding: 50 * @rem 23 * @rem 20 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg2.png')
      no-repeat 0 0;
    background-size: 300 * @rem 150 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 25 * @rem;
      font-weight: bold;
      font-size: 18 * @rem;
      color: #333333;
      line-height: 25 * @rem;
      text-align: center;
    }
    .msg {
      margin-top: 20 * @rem;

      .msg-number {
        display: flex;
        align-items: center;
        height: 17 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #93999f;
        line-height: 17 * @rem;
        margin-top: 7 * @rem;
        padding-left: 39 * @rem;

        &::before {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/welfare/tips-star-icon.png) no-repeat;
          background-size: 6 * @rem 6 * @rem;
          margin-right: 4 * @rem;
        }
      }
    }
    .checkbox {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 16 * @rem auto 0;
      text-align: center;
      font-size: 12 * @rem;
      font-weight: 400;
      color: #999999;
      line-height: 12 * @rem;
      span {
        display: block;
        width: 12 * @rem;
        height: 12 * @rem;
        border-radius: 50%;
        margin-right: 4 * @rem;
        background: url('~@/assets/images/clock-in/check-icon.png') no-repeat;
        background-size: 12 * @rem 12 * @rem;
      }

      &.checked span {
        background-image: url('~@/assets/images/clock-in/checked-icon.png');
      }
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 29 * @rem;
    }
    .btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120 * @rem;
      height: 40 * @rem;
      line-height: 19 * @rem;
      background: #ffeadd;
      border-radius: 30 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #fe6600;
      text-align: center;
      position: relative;

      &:last-of-type {
        background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
        color: #fff;

        span {
          color: #fff;
        }
      }

      span {
        display: block;
        height: 11 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 9 * @rem;
        color: rgba(254, 102, 0, 0.8);
        line-height: 11 * @rem;
        text-align: center;
      }

      .svip-tips {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 18 * @rem;
        padding: 0 8 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 9 * @rem;
        color: #c05c2a;
        line-height: 11 * @rem;
        text-align: center;
        border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
        box-sizing: border-box;
        background: linear-gradient(
          90deg,
          #ffeaab 0%,
          #ffe0dd 54%,
          #ffbdcf 100%
        );
        border: 1 * @rem solid #ffffff;
        position: absolute;
        top: -13 * @rem;
        right: -7 * @rem;
      }
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.award-before-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    z-index: 2;
    padding: 32 * @rem 12 * @rem 20 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg2.png')
      no-repeat 0 0;
    background-size: 300 * @rem 264 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 34 * @rem;
      font-family: Dream Han Sans CN, Dream Han Sans CN;
      font-weight: normal;
      font-size: 22 * @rem;
      color: #222222;
      line-height: 34 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: bold;

      &.fzs {
        font-size: 22 * @rem;
      }
    }
    .detail {
      width: 80 * @rem;
      height: 80 * @rem;
      position: relative;
      margin: 8 * @rem auto;

      img {
        width: 100%;
        height: 100%;
      }
      .double {
        height: 16 * @rem;
        background: #ff674c;
        border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
        padding: 0 5 * @rem;
        font-weight: 500;
        font-size: 10 * @rem;
        color: #ffffff;
        line-height: 16 * @rem;
        text-align: center;
        position: absolute;
        top: 6 * @rem;
        right: -28 * @rem;
      }
      .gold-num {
        height: 20 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 16 * @rem;
        position: absolute;
        bottom: 4 * @rem;
        left: 53 * @rem;
        white-space: nowrap;
      }

      .svip-num {
        height: 20 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 16 * @rem;
        position: absolute;
        bottom: 4 * @rem;
        left: 53 * @rem;
        white-space: nowrap;
      }
    }
    .msg {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #999999;
      text-align: center;
      line-height: 17 * @rem;
      margin-top: 4 * @rem;
      overflow: hidden;
      .msg-number {
        margin-top: 4 * @rem;
      }
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20 * @rem 11 * @rem 0;
    }
    .btn {
      width: 100%;
      max-width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: #f2f2f2;
      border-radius: 30 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #7d7d7d;
      text-align: center;
      margin: 0 auto;
      margin-right: 14 * @rem;

      &:last-of-type {
        background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
        color: #fff;
        margin-right: auto;
      }
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.award-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 108 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-logo2.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
    &.svip {
      background-image: url('~@/assets/images/clock-in/sign-success-logo5.png');
    }
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    z-index: 2;
    padding: 54 * @rem 12 * @rem 20 * @rem;
    margin-top: -51 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg2.png')
      no-repeat 0 0;
    background-size: 300 * @rem 264 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 34 * @rem;
      font-family: Dream Han Sans CN, Dream Han Sans CN;
      font-weight: normal;
      font-size: 24 * @rem;
      color: #fe6600;
      line-height: 34 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: bold;
    }
    .msg {
      font-family: PingFang SC, PingFang SC;
      font-size: 15 * @rem;
      color: #333333;
      text-align: center;
      line-height: 19 * @rem;
      margin-top: 11 * @rem;
      overflow: hidden;
      .msg-number {
        margin-top: 3 * @rem;
        font-weight: bold;
      }
    }
    .tips {
      height: 15 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #ff954e;
      line-height: 15 * @rem;
      text-align: center;
      margin-top: 17 * @rem;
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20 * @rem 11 * @rem 0;
    }
    .btn {
      width: 100%;
      max-width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: #ffeadd;
      border-radius: 30 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #fe6600;
      text-align: center;
      margin: 0 auto;
      margin-right: 14 * @rem;

      &:last-of-type {
        background: linear-gradient(90deg, #ff4626 0%, #ff963c 100%);
        color: #fff;
        margin-right: auto;
      }
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.experience-dialog {
  background: transparent;
  overflow: visible;

  .dialog-content {
    box-sizing: border-box;
    position: relative;
    // background-color: #fff;
    // border-radius: 16 * @rem;
    // margin-top: -83 * @rem;
    z-index: 2;
    // padding: 84 * @rem 31 * @rem 25 * @rem;
    // background: url('~@/assets/images/clock-in/experience-show-bg.png')
    //   no-repeat -8 * @rem -1 * @rem;

    text-align: center;
    .sign-success-bg {
      background-size: 375 * @rem 400 * @rem;
      width: 359 * @rem;
      height: 399 * @rem;
    }
    .dialog-btn {
      position: absolute;
      left: 0;
      bottom: 47 * @rem;
      transform: translate(25%);
      width: 238 * @rem;
      height: 40 * @rem;
      border-radius: 40 * @rem;
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -40 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
/deep/.van-dialog {
  width: auto;
}
.loading-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
