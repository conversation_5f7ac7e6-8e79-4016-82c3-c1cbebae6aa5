<template>
  <div class="tool-tab" :class="{ centered: empty }">
    <content-empty
      v-if="empty"
      :tips="tips"
      :emptyImg="emptyImg"
    ></content-empty>
    <load-more
      v-else
      class="list-container"
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="tool-list">
        <div
          class="tool-item"
          :class="{ 'not-introduce': !item.video_url }"
          v-for="(item, index) in toolList"
          :key="index"
        >
          <div class="tool-info">
            <div class="tool-icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="info-right">
              <div class="title">{{ item.title }}</div>
              <div class="desc">{{ item.desc }}</div>
            </div>
            <div class="go-use" v-if="item.is_apply" @click="goToTool(item)"
              >去使用</div
            >
          </div>
          <div class="video-container" v-if="item.video_url && item.open">
            <video
              :src="item.video_url"
              :poster="item.video_thumb"
              :webkit-playsinline="true"
              :playsinline="true"
              :x5-playsinline="true"
              x-webkit-airplay="allow"
            >
              {{ $t('您的手机不支持该视频文件！！！') }}
            </video>
            <div v-if="item.video_url" @click="play($event)" class="play"></div>
          </div>
          <div
            v-if="item.video_url"
            class="open-btn"
            :class="{ open: item.open }"
            @click="item.open = !item.open"
          >
            {{ item.txt || '使用介绍' }}
            <span class="open-arrow"></span>
          </div>
        </div>
      </div>
    </load-more>
  </div>
</template>

<script>
import { ApiGameToolBox } from '@/api/views/game.js';
import { PageName, handleActionCode } from '@/utils/actionCode.js';
import emptyImg from '@/assets/images/games/comment-empty-img.png';

export default {
  name: 'toolTab',
  props: {
    gameId: {
      required: true,
    },
  },
  data() {
    return {
      toolList: [],
      finished: false,
      loading: false,
      empty: false,
      page: 1,
      listRows: 10,
      tips: '暂无工具箱',
      emptyImg,
    };
  },
  async created() {
    await this.getList();
  },
  methods: {
    goToTool(item) {
      // action_code 后端都不统一，哎
      // switch (item.action_code) {
      //   case 29:
      //     this.toPage('CloudHangup');
      //     break;
      //   default:
      //     if (PageName[item.action_code]) {
      //       this.toPage(PageName[item.action_code]);
      //       return;
      //     }
      //     break;
      // }

      // 新的actionCode方法 2024年8月30日14:14:25
      handleActionCode(item);
    },
    play($event) {
      this.pauseAll();
      let $play = $event.target;
      let $video = $play.parentNode.querySelector('video');
      $play.style.display = 'none';
      $video.play();
      $video.setAttribute('controls', 'true');
    },
    pauseAll() {
      let $videos = document.querySelectorAll('.video-container video');
      let $plays = document.querySelectorAll('.video-container .play');
      $videos.forEach(item => {
        item.pause();
        item.removeAttribute('controls');
      });
      $plays.forEach(item => {
        item.style.display = 'block';
      });
    },
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiGameToolBox({
        id: this.gameId,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      list = list.map(item => {
        return {
          ...item,
          open: false,
        };
      });
      if (action === 1 || this.page === 1) {
        this.toolList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.toolList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.tool-tab {
  height: 100%;
  &.centered {
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
  }
  .list-container {
    .tool-list {
      margin-top: 12 * @rem;
      .tool-item {
        box-sizing: border-box;
        width: 339 * @rem;
        &:not(:first-of-type) {
          margin: 18 * @rem auto 0;
        }
        &:first-of-type {
          margin: 0 auto;
        }
        &.not-introduce {
          padding: 14 * @rem 14 * @rem 12 * @rem 14 * @rem;
        }
        // box-shadow: 0 * @rem 0 * @rem 12 * @rem 0 * @rem rgba(19, 24, 64, 0.08);
        background: #fff;
        border-radius: 8 * @rem;
        padding: 14 * @rem 14 * @rem 0 14 * @rem;
        .tool-info {
          display: flex;
          align-items: center;
          .tool-icon {
            width: 56 * @rem;
            height: 56 * @rem;
            border-radius: 8 * @rem;
            overflow: hidden;
          }
          .info-right {
            flex: 1;
            min-width: 0;
            margin-left: 8 * @rem;
            .title {
              font-size: 17 * @rem;
              color: #191b1f;
              font-weight: bold;
              line-height: 20 * @rem;
            }
            .desc {
              font-size: 11 * @rem;
              color: #93999f;
              line-height: 14 * @rem;
              margin-top: 10 * @rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .go-use {
            width: 54 * @rem;
            text-align: center;
            height: 22 * @rem;
            line-height: 22 * @rem;
            border-radius: 21 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11 * @rem;
            background: #1cce94;
            color: #ffffff;
            margin-left: 10 * @rem;
          }
        }

        .video-container {
          position: relative;
          width: 311 * @rem;
          height: 175 * @rem;
          margin: 12 * @rem auto 0;
          border-radius: 8 * @rem;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          video {
            width: 311 * @rem;
            height: 175 * @rem;
            object-fit: cover;
          }
          .play {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 45 * @rem;
            height: 45 * @rem;
            background-image: url(~@/assets/images/tool-play.png);
            background-size: 100%;
          }
        }

        .open-btn {
          border-top: 1 * @rem solid #f0f1f5;
          height: 39 * @rem;
          line-height: 39 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 18 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #60666c;
          .open-arrow {
            display: block;
            width: 24 * @rem;
            height: 24 * @rem;
            background: url(~@/assets/images/right-icon.png) center center
              no-repeat;
            background-size: 5 * @rem 10 * @rem;
            transform: rotate(90deg);
            transition: transform 0.3s;
          }
          &.open {
            .open-arrow {
              transform: rotate(-90deg);
            }
          }
        }
      }
    }
  }
}
</style>
