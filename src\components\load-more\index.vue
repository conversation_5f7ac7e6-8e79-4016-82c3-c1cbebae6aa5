<template>
  <div class="load-more">
    <van-list
      v-model="loadingMore"
      :finished="finished"
      :finished-text="finishedText"
      :loading-text="$t('- 拼命加载中... -')"
      :error-text="$t('- 加载失败，请重试 -')"
      @load="load"
      :offset="100"
      :immediate-check="check"
    >
      <slot></slot>
    </van-list>
  </div>
</template>

<script>
export default {
  name: 'LoadMore',
  model: {
    prop: 'loading',
    event: 'change',
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    finished: {
      type: Boolean,
      default: false,
    },
    check: {
      type: Boolean,
      default: true,
    },
    finishedText: {
      type: String,
      default: '- 没有更多了 -',
    },
  },
  data() {
    return {
      loadingMore: this.loading,
    };
  },
  watch: {
    //这里检测data中的值，一旦发生变化就提交事件到父组件
    loadingMore(newVal) {
      this.$emit('change', newVal);
    },
    loading(newVal) {
      this.loadingMore = newVal;
    },
  },
  methods: {
    load() {
      this.$emit('loadMore');
    },
  },
};
</script>

<style lang="less" scoped></style>
