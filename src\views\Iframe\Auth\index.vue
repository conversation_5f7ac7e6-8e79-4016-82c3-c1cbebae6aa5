<template>
  <div class="iframe auth-iframe">
    <nav-bar-2 :border="true" :title="title" ref="topNavBar"></nav-bar-2>
    <iframe id="main" :src="url" frameborder="0"></iframe>
  </div>
</template>

<script>
import BASEPARAMS from '@/utils/baseParams';

export default {
  name: 'AuthIframe',
  data() {
    return {
      title: '',
      url: '',
    };
  },
  created() {
    this.title = this.$route.params.title;
    this.url = `${this.$route.params.url}&H5=1`;
  },
  mounted() {
    window.addEventListener('message', this.handleMessage, false);
    this.postMessage();
    // document.domain = this.$h5Page.domain;
    // window.name = `(${JSON.stringify(name)})`;
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage, false);
  },
  methods: {
    handleMessage(e) {
      switch (e.data.type) {
        case 'login':
          this.$dialog
            .alert({
              message: this.$t('您还未登录，请登录'),
            })
            .then(() => {
              this.$router.replace({ name: 'PhoneLogin' });
            });
          break;
        case 'goToGame':
          this.$router.push({
            name: 'GameDetail',
            params: { id: e.data.gameid },
          });
          break;
        case 'openInNewWindow':
          this.$router.replace({
            name: 'AuthIframe',
            params: { url: e.data.url },
          });
          break;
        case 'showPlatformIntroduced':
          this.$router.push({
            name: 'AuthIframe',
            params: { url: e.data.url },
          });
          break;
        case 'bindPhone':
          this.$router.push({ name: 'ChangePhone' });
          break;
        case 'openInBrowser':
          window.location.href = e.data.url;
          break;
        case 'close':
          this.$router.go(-1);
          break;
        case 'memAuth':
          this.$router.push({ name: 'IdCard' });
          break;
        case 'popToRecommend':
          break;
        case 'showActivity':
          break;
        case 'openApp':
          break;

        case 'setScreenOrientation':
          break;
        case 'showVideoAd':
          break;
        case 'setCanScreenShots':
          break;
        case 'setHideToolBar':
          break;

        case 'receiveFirstChargeSuccess':
          break;
        case 'wxOAuth2':
          break;
        case 'createShortcut':
          break;
        case 'share':
          break;
        default:
          break;
      }
    },
    postMessage() {
      let data = {};
      data.from = BASEPARAMS.from;
      data.token = this.userInfo.token ? this.userInfo.token : '';
      data.schemes = '';
      data.packageName = '';
      if (this.$route.params.gameId) data.gameid = this.$route.params.gameId;
      let iframe = document.getElementById('main');
      iframe.onload = function () {
        iframe.contentWindow.postMessage(`(${JSON.stringify(data)})`, '*');
      };
    },
  },
};

// let str = '({"isTest":true,"build":"100","channel":"empty","from":"101","model":"x86_64","uuid":"93E5DCC7-BA81-4F0C-BA04-60EA14EA7DCF","token":"7d300bb95b859c6c9a7835286590d4c0","versionCode":"100"})'

/* 
需要的参数名 
  token,
  from,
  schemes,
  packageName

登陆：
data = {"type":"login"};
赚金币：
var data = {"type":"makeGold"};

跳转游戏详情页
gameid:游戏id
var data = {"type":"goToGame","gameid":gameid};

跳转发布页
var data = {"type":"popToRecommend"};

打开新链接
url 跳转链接
 var data = {"type":"openInBrowser","url":url};

关闭：
refresh 是否刷新
var data = {"type":"close","refresh":refresh};

页面跳转 每日签到：qd 个人中心：grzx 赚金币：zjb 首页：index
 var data = {"type":"showActivity","page":page};

打开app
var data = {"type":"openApp","pack":pack};

APP内新窗口打开
 var data = {"type":"openInNewWindow","url":url};

横竖屏
type=0竖屏  1横屏
 var data = {"type":"setScreenOrientation","type":type};

视频广告
var data = {"type":"showVideoAd"};

是否允许截图
true可截图   false禁止截图
 var data = {"type":"setCanScreenShots","isShots":isShots};

跳转平台介绍视频
var data = {"type":"showPlatformIntroduced","url":url};

跳转用户实名认证
var data = {"type":"memAuth"};

隐藏首充福利返回键
 var data = {"type":"setHideToolBar"};

绑定手机号
 var data = {"type":"bindPhone"};


 var data = {"type":"receiveFirstChargeSuccess"};

提示微信授权
 var data = {"type":"wxOAuth2"};

创建桌面快捷方式
 var data = {"type":"createShortcut","url":h5Url};

分享
 let data = {
            title: '游戏盒年中福利',
            text: '游戏首充、海量金币、代金券、独家礼包免费领',
            url: url,
            icon: 'http://' + window.location.host + '//static.pic3733.com/api/static/images/img_user_default.png',
            type: 'anniversary'
        };
 var data = {"type":"share","data":data,"type":type};

*/
</script>

<style lang="less" scoped>
.iframe {
  iframe {
    width: 100%;
    height: calc(100vh - 50 * @rem - @safeAreaBottom);
    height: calc(100vh - 50 * @rem - @safeAreaBottomEnv);
  }
}
</style>
