<template>
  <div class="banner-panel">
    <div
      class="game-panel"
      :class="{ 'no-game': ![1, 31, 4].includes(Number(bannerInfo.type)) }"
      v-if="bannerList.length"
      @click="tapBanner(bannerInfo, bannerIndex)"
       v-sensors-exposure="bannerExposure(bannerInfo, bannerIndex)"
    >
      <div
        class="top-shadow"
        :style="{
          background: topShadow,
        }"
      ></div>
      <img
        class="game-banner"
        :src="bannerInfo.titleimg"
        alt=""
        onclick="return false"
      />
      <div class="game-card" v-if="bannerInfo.type == 1">
        <div class="game-info">
          <div class="game-name">
            {{ bannerInfo.game.main_title
            }}<span
              class="game-subtitle"
              v-if="bannerInfo.game && bannerInfo.game.subtitle"
              >{{ bannerInfo.game.subtitle }}</span
            >
          </div>
          <div class="info-center">
            <div class="types">
              <template v-for="(type, typeIndex) in bannerInfo.game.type">
                <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                  type
                }}</span>
              </template>
            </div>
            <div class="server-date" v-if="bannerInfo.game.service_date">
              {{ bannerInfo.game.service_date }}
            </div>
          </div>
          <div class="game-tags">
            <div
              class="tag"
              v-for="(tag, tagIndex) in bannerInfo.game.extra_tag"
              :key="tagIndex"
            >
              <template v-if="tagIndex < 3">
                <div class="tag-name">{{ tag.name }}</div>
              </template>
            </div>
          </div>
        </div>
        <div class="game-score">
          <div class="score-title">{{ $t('评分') }}</div>
          <div class="score-num">
            {{ bannerInfo.game.rating ? bannerInfo.game.rating.rating : '10' }}
          </div>
        </div>
      </div>
      <div class="game-card" v-if="bannerInfo.type == 31">
        <div class="heji-info">
          <div class="heji-title">{{ bannerInfo.heji.title }}</div>
          <div class="heji-desc">{{ bannerInfo.heji.desc }}</div>
          <div class="heji-count">
            {{ $t('共有') }}<span>{{ bannerInfo.heji.count }}</span
            >{{ $t('款游戏') }}
          </div>
        </div>
        <div class="heji-icon">
          <img
            src="@/assets/images/home/<USER>"
            alt=""
            onclick="return false"
          />
        </div>
      </div>
      <!-- 活动--对应后台‘盒子内url’ -->
      <div class="game-card activity-card" v-if="bannerInfo.type == 4">
        <div class="activity-info">
          <div class="activity-title">{{ bannerInfo.banner_title }}</div>
          <div class="activity-desc">{{ bannerInfo.banner_subtitle }}</div>
        </div>
        <div class="activity-icon">
          <img
            src="@/assets/images/home/<USER>"
            alt=""
            onclick="return false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { clickBanner } from '@/utils/function.js';
import { ApiStatisticsBanner } from '@/api/views/home.js';
export default {
  name: 'bannerPanel',
  props: {
    bannerList: {
      type: Array,
      default: () => [],
    },
    bgc: {
      type: String,
      default: '#ffffff',
    },
    bannerIndex: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    topShadow() {
      return `linear-gradient(360deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    bannerInfo() {
      return this.bannerList[this.bannerIndex];
    },
    hexToRgba() {
      return `rgba(${parseInt('0x' + this.bgc.slice(1, 3))},${parseInt(
        '0x' + this.bgc.slice(3, 5),
      )},${parseInt('0x' + this.bgc.slice(5, 7))},0)`;
    },
  },

  methods: {
    bannerExposure(banner, index) {
      return {
        'event-name': 'banner_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-banner_index': `${index}`,
        'property-banner_name': banner.title || banner.game?.title || banner.heji?.title,
      }
    },
    tapBanner(banner, index) {
      
      // 神策埋点
      this.$sensorsTrack('banner_click', {
        page_name: this.$sensorsPageGet(),
        banner_index: index,
        banner_name: banner.title || banner.game?.title || banner.heji?.title,
      });

      this.CLICK_EVENT(banner.click_id);
      try {
        ApiStatisticsBanner({ id: banner.id });
      } catch (e) {}
      clickBanner(banner);
    },
  },
};
</script>

<style lang="less" scoped>
.game-panel {
  position: relative;
  width: 100%;
  // height: 375 * @rem;
  padding-bottom: 50 * @rem;
  background-color: #fff;
  &.no-game {
    padding-bottom: 0;
    &::after {
      bottom: 0;
    }
  }
  .top-shadow {
    width: 100%;
    height: 150 * @rem;
    position: absolute;
    left: 0;
    top: -1 * @rem;
    z-index: 2;
  }
  &::after {
    content: '';
    width: 100%;
    height: 90 * @rem;
    position: absolute;
    left: 0;
    bottom: 49 * @rem;
    z-index: 2;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.81) 66%,
      #ffffff 100%
    );
  }
  .game-banner {
    object-fit: cover;
    position: relative;
    z-index: 1;
    // height: 375 * @rem;
  }
  .game-card {
    box-sizing: border-box;
    width: 339 * @rem;
    height: 109 * @rem;
    padding: 15 * @rem;
    margin: 0 auto;
    background: #ffffff;
    box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.1);
    border-radius: 14 * @rem;
    opacity: 0.84;
    position: absolute;
    left: 50%;
    bottom: 15 * @rem;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    &.activity-card {
      height: 80 * @rem;
      padding: 9 * @rem 10 * @rem 9 * @rem 15 * @rem;
      align-items: center;
      .activity-info {
        flex: 1;
        min-width: 0;
        .activity-title {
          font-size: 16 * @rem;
          color: #000000;
          line-height: 22 * @rem;
          font-weight: bold;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .activity-desc {
          font-size: 12 * @rem;
          color: #797979;
          line-height: 17 * @rem;
          margin-top: 5 * @rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .activity-icon {
        width: 64 * @rem;
        height: 62 * @rem;
        margin-left: 7 * @rem;
      }
    }
    .game-info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .game-name {
        box-sizing: border-box;
        font-size: 20 * @rem;
        color: #000000;
        font-weight: bold;
        line-height: 30 * @rem;
        white-space: nowrap;
        overflow: hidden;
        padding-right: 52 * @rem;
        display: flex;
        align-items: center;
        .game-subtitle {
          box-sizing: border-box;
          border: 1 * @rem solid fade(@themeColor, 80);
          border-radius: 3 * @rem;
          font-size: 13 * @rem;
          padding: 2 * @rem 3 * @rem;
          color: @themeColor;
          margin-left: 7 * @rem;
          vertical-align: middle;
          line-height: 1;
        }
      }
      .info-center {
        margin-top: 8 * @rem;
        display: flex;
        align-items: center;
        padding-right: 52 * @rem;
        .types {
          display: flex;
          align-items: center;
          height: 17 * @rem;
          font-size: 12 * @rem;
          color: #797979;
          .type {
            padding: 0 5 * @rem;
            position: relative;
            display: flex;
            align-items: center;
            &:not(:first-child) {
              &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1 * @rem;
                height: 10 * @rem;
                background-color: #929292;
              }
            }
          }
        }
        .server-date {
          font-size: 12 * @rem;
          color: @themeColor;
          margin-left: 8 * @rem;
        }
      }
      .game-tags {
        display: flex;
        overflow: hidden;
        flex-wrap: wrap;
        margin-top: 5 * @rem;
        padding-bottom: 1 * @rem;
        height: 17 * @rem;
        .tag {
          height: 17 * @rem;
          box-sizing: border-box;
          margin-bottom: 5 * @rem;
          margin-right: 5 * @rem;
          display: flex;
          align-items: center;
          flex-wrap: nowrap;
          color: #9a9a9a;
          background-color: #ffffff;
          border-radius: 5 * @rem;
          padding: 0 4 * @rem;
          border: 0.5 * @rem solid #9a9a9a;
          .tag-icon {
            width: 13 * @rem;
            height: 13 * @rem;
          }
          .tag-name {
            font-size: 11 * @rem;
            white-space: nowrap;
            margin-left: 2 * @rem;
          }
        }
      }
    }
    .game-score {
      position: absolute;
      right: 18 * @rem;
      top: 15 * @rem;
      width: 52 * @rem;
      height: 50 * @rem;
      background: #ffffff;
      box-shadow: 0 * @rem 1 * @rem 6 * @rem 0 * @rem rgba(0, 0, 0, 0.08);
      border-radius: 12 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .score-title {
        font-size: 10 * @rem;
        color: #797979;
        line-height: 14 * @rem;
        margin-top: 2 * @rem;
      }
      .score-num {
        font-size: 20 * @rem;
        line-height: 24 * @rem;
        color: @themeColor;
        font-weight: bold;
      }
    }

    .heji-info {
      flex: 1;
      min-width: 0;
      .heji-title {
        font-size: 20 * @rem;
        color: #000000;
        font-weight: bold;
        line-height: 30 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .heji-desc {
        font-size: 12 * @rem;
        color: #999999;
        line-height: 17 * @rem;
        margin-top: 10 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .heji-count {
        font-size: 12 * @rem;
        color: #999999;
        line-height: 17 * @rem;
        margin-top: 5 * @rem;
        span {
          color: @themeColor;
        }
      }
    }
    .heji-icon {
      width: 72 * @rem;
      height: 45 * @rem;
      margin-top: 17 * @rem;
    }
  }
}
</style>
