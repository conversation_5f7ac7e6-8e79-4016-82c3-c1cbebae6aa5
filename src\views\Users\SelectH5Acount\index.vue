<template>
  <div class="select-h5-acount">
    <nav-bar-2
      :border="true"
      :title="$t('选择小号')"
      ref="topNavBar"
    ></nav-bar-2>
    <section class="add-acount">
      <i class="add" @click="openAddPopup()"></i>
      <div class="text">{{ $t('添加小号') }}</div>
      <i class="help"></i>
    </section>
    <section class="acount-list">
      <div v-for="(item, index) of list" :key="index" class="item">
        <div class="text1" @click="openUpdatePopup(item, index)">
          【{{ $t('修改') }}】
        </div>
        <div class="text2">{{ item.name }}</div>
        <a :href="item.url" class="text3"
          >{{ $t('进入游戏') }}<i class="arrow"></i
        ></a>
      </div>
    </section>
    <van-dialog
      v-model="addDialogShow"
      :title="$t('添加小号')"
      show-cancel-button
      confirmButtonColor="#47A83A"
      :confirm="handleAddUserName()"
      class="popup"
    >
      <div class="content">
        <div class="text">{{ $t('请输入小号名称') }}</div>
        <input
          type="text"
          v-model="addUserName"
          :placeholder="$t('请输入小号名称')"
          class="username"
        />
      </div>
    </van-dialog>
    <van-dialog
      v-model="updateDialogShow"
      :title="$t('修改小号')"
      show-cancel-button
      confirmButtonColor="#47A83A"
      :confirm="handleUpdateUserName()"
      class="popup"
    >
      <div class="content">
        <div class="text">{{ $t('请输入小号名称') }}</div>
        <input type="text" v-model="updateUsername" class="username" />
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'SelectH5Acount',
  data() {
    return {
      list: [
        { name: '游戏名1', url: '111' },
        { name: '游戏名2', url: '111' },
        { name: '游戏名3', url: '111' },
      ],
      addDialogShow: false,
      updateDialogShow: false,
      addUserName: '',
      updateUsername: '',
    };
  },
  methods: {
    openAddPopup() {
      this.addDialogShow = !this.addDialogShow;
    },
    openUpdatePopup(item, index) {
      this.updateUsername = item.name;
      this.updateDialogShow = !this.updateDialogShow;
    },
    handleAddUserName() {},
    handleUpdateUserName() {},
  },
};
</script>

<style lang="less" scoped>
.select-h5-acount {
  .add-acount {
    display: flex;
    align-items: center;
    margin: 20 * @rem 15 * @rem 0;
    .add {
      display: block;
      width: 21 * @rem;
      height: 21 * @rem;
      background-image: url(~@/assets/images/users/add.png);
      background-size: 100%;
    }
    .text {
      margin: 0 8 * @rem;
      font-size: 15 * @rem;
    }
    .help {
      display: block;
      width: 16 * @rem;
      height: 16 * @rem;
      background-image: url(~@/assets/images/users/help.png);
      background-size: 100%;
    }
  }
  .acount-list {
    margin: 30 * @rem 15 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      width: 100%;
      height: 50 * @rem;
      margin-bottom: 22 * @rem;
      padding: 0 15 * @rem;
      background: #ffffff;
      box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.12);
      border-radius: 12px;
      font-size: 15 * @rem;
      .text1 {
        color: #3399f8;
      }
      .text2 {
        flex: 1;
        text-align: left;
      }
      .text3 {
        display: flex;
        align-items: center;
        color: #feb311;
        .arrow {
          display: block;
          width: 5 * @rem;
          height: 8 * @rem;
          margin-left: 3 * @rem;
          background-image: url(~@/assets/images/users/arrow.png);
          background-size: 100%;
        }
      }
    }
  }
  .popup {
    .content {
      padding: 0 20 * @rem;
      .text {
        margin: 16 * @rem 0;
        font-size: 15 * @rem;
        color: #666666;
      }
      input {
        box-sizing: border-box;
        width: 100%;
        height: 38 * @rem;
        padding: 0 15 * @rem;
        margin-bottom: 27 * @rem;
        line-height: 38 * @rem;
        background-color: #f8f8f8;
      }
    }
  }
}
</style>
