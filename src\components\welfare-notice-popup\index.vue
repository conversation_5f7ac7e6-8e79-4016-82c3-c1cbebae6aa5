<template>
  <div>
    <!-- 福利公告弹窗 -->
    <van-popup
      v-model="popupShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
      :lockScroll="false"
      class="welfare-notice-popup"
    >
      <div class="container">
        <div class="title">{{ title }}</div>
        <div class="notice-list">
          <div
            v-for="(item, index) in notice_list"
            :key="index"
            class="notice-item"
          >
            <span
              class="notice-type"
              :class="{ 'notice-type-1': bgType == 1 }"
              >{{ item.tag_name }}</span
            ><span @click="toNoticeDetail(item)" class="notice-title">
              <span class="text">{{ item.title }}</span>
              <i class="notice-icon"></i
            ></span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { jumpAnnouncement } from '@/utils/function';
export default {
  name: 'WelfareNoticePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '福利公告',
    },
    bgType: {
      type: Number,
      default: 0, //0默认颜色 1颜色1
    },
    notice_list: {
      type: Array,
      default: [],
    },
  },
  components: {},
  data() {
    return {
      // notice_list: [], //公告列表
    };
  },
  async mounted() {},

  methods: {
    toNoticeDetail(item) {
      this.$emit('update:show', false);
      this.$nextTick(() => {
        jumpAnnouncement(item);
      });
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.welfare-notice-popup {
  padding: 22 * @rem 12 * @rem 47 * @rem;
  max-height: 380 * @rem;
  background: #f7f8fa;
  overflow: hidden;
  box-sizing: border-box;
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      font-weight: 600;
      font-size: 16 * @rem;
      color: #191b1f;
    }
    .notice-list {
      margin: 20 * @rem 12 * @rem 0;
      width: 351 * @rem;
      max-height: 300 * @rem;
      overflow-y: auto;
      .notice-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 44 * @rem;
        background: #ffffff;
        border-radius: 8 * @rem;
        box-sizing: border-box;
        padding: 0 14 * @rem 0 12 * @rem;
        .notice-type {
          font-size: 10 * @rem;
          font-weight: 400;
          background: linear-gradient(90deg, #ecfbf4 0%, #ffffff 100%);
          border-radius: 4 * @rem;
          border: 1 * @rem solid #191b1f;
          color: #191b1f;
          padding: 3 * @rem 5 * @rem;
          box-sizing: border-box;
          &.notice-type-1 {
            background: linear-gradient(90deg, #faf2eb 0%, #ffffff 100%);
          }
        }
        .notice-title {
          display: flex;
          justify-content: space-between;
          flex: 1;
          margin-left: 7 * @rem;
          color: #303236;
          font-size: 13 * @rem;

          .text {
            width: 245 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .notice-icon {
            display: block;
            width: 14 * @rem;
            height: 14 * @rem;
            .image-bg('~@/assets/images/more-right-arrow.png');
          }
        }
        &:not(:first-child) {
          margin-top: 12 * @rem;
        }
      }
    }
  }
}
</style>
