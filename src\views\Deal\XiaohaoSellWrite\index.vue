<template>
  <div class="page xiaohao-sell-write-page">
    <nav-bar-2 :title="$t('账号交易')" :border="true"></nav-bar-2>
    <div class="main">
      <div class="form">
        <div class="item">
          <div class="title">{{ $t('选择游戏') }}</div>
          <div class="area" @click="selectGame">
            <div class="text" v-if="!xiaohaoSellInfo.app_id">
              {{ $t('请选择交易的游戏') }}
            </div>

            <template v-else>
              <div class="game-icon">
                <img :src="xiaohaoSellInfo.game_icon" />
              </div>
              <div class="game-name">
                {{ xiaohaoSellInfo.game_name }}
              </div>
            </template>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="item" v-if="xiaohaoSellInfo.is_h5 == true">
          <div class="title">{{ $t('选择角色所在端口') }}</div>
          <div class="area" @click="selectPort">
            <div class="text" v-if="!xiaohaoSellInfo.play_from">
              {{ $t('请选择角色所在端口') }}
            </div>
            <div class="game-port" v-else>
              {{ xiaohaoSellInfo.play_from.name }}
            </div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('选择小号') }}</div>
          <div class="area" @click="selectXiaohao">
            <div class="text" v-if="!xiaohaoSellInfo.nickname">
              {{ $t('请选择交易的小号') }}
            </div>
            <div class="game-xiaohao" v-else>
              <div class="top">
                <div class="platforms">
                  <div
                    class="plat"
                    v-for="(item, index) in xiaohaoSellInfo.platforms"
                    :key="index"
                  >
                    <img class="plat-icon" :src="item.icon" alt="" />
                  </div>
                </div>
                <div class="nickname">
                  {{ xiaohaoSellInfo.nickname }}
                </div>
              </div>
              <div class="bottom">
                {{ $t('总充值') }}：<span
                  >{{ Number(xiaohaoSellInfo.pay_sum).toFixed(1) }}元</span
                >
              </div>
            </div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('所在区服') }}</div>
          <div class="area">
            <input
              type="text"
              class="input-text"
              :placeholder="$t('请如实填写游戏内的区服信息')"
              v-model="game_area"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('主要角色名称') }}</div>
          <div class="area">
            <input
              type="text"
              class="input-text"
              :placeholder="$t('请如实填写游戏内的角色名称(选填)')"
              v-model="role_name"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('售价(元)') }}</div>
          <div class="area">
            <div
              class="text"
              v-if="!xiaohaoSellInfo.nickname"
              @click="$toast($t('请先选择小号'))"
            >
              {{ sellPriceRule.trade_price_text }}
            </div>
            <div v-else class="set-price">
              <input
                type="number"
                :maxlength="6"
                class="input-text"
                :placeholder="pricePlaceholder"
                v-model="price"
              />
              <div class="price-tip" v-if="price && ratioInfo.text1">
                {{ ratioInfo.text1 }},{{ $t('售出可得') }}{{ moneyCanGet }}元({{
                  ptbCanGet
                }}{{ $t('平台币') }})
              </div>
            </div>
          </div>
          <div class="computed-sum"></div>
        </div>
      </div>
      <div class="bottom-bar fixed-center">
        <div class="confirm-btn btn" @click="handleNext">
          {{ $t('下一步') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { ApiXiaohaoPaySum, ApiXiaohaoTradeIndex } from '@/api/views/xiaohao.js';
export default {
  name: 'XiaohaoSellWrite',
  data() {
    return {
      game_area: '',
      role_name: '',
      price: '',
      ratioInfo: {},
      isReWrite: false,
      sellPriceRule: {},
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
    moneyCanGet() {
      // 扣除手续费后的钱
      let fee = Math.ceil(this.price * this.ratioInfo.fee_rate);
      fee = fee < 5 ? 5 : fee;

      return this.price - fee < 0 ? 0 : this.price - fee;
    },
    ptbCanGet() {
      // 扣除手续费后的平台币
      let ptb = this.price * 10;
      let fee = Math.floor(ptb * this.ratioInfo.fee_rate);
      fee = fee < this.ratioInfo.min_fee_ptb ? this.ratioInfo.min_fee_ptb : fee;
      return ptb - fee < 0 ? 0 : ptb - fee;
    },
    pricePlaceholder() {
      let price = this.xiaohaoSellInfo.pay_sum ?? 0;
      price = Math.round(
        (Number(price) * this.sellPriceRule.trade_pay_percentage) / 100,
      );

      return this.$t('请设置价格，最低') + price + '元';
    },
  },
  // watch: {
  //   xiaohaoSellInfo: {
  //     handler(val, oldVal) {
  //       if (val.id != oldVal.id && val.id) {
  //         this.getPayRatio(val.id);
  //       }
  //     },
  //     deep: true,
  //   },
  // },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name != 'XiaohaoSellConfirm') {
        vm.isReWrite = false;
        vm.game_area = '';
        vm.role_name = '';
        vm.price = '';
      }
    });
  },
  beforeRouteLeave(to, from, next) {
    if (to.name == 'Deal') {
      // this.$dialog
      //   .confirm({
      //     title: "提示",
      //     message: "是否保存已编辑内容",
      //   })
      //   .then(() => {
      //     this.saveInfo();
      //     this.$dialog.close();
      //     next();
      //   })
      //   .catch(() => {
      //     this.setXiaohaoSellInfo();
      //     this.$dialog.close();
      //     next();
      //   });
      // return;
      this.setXiaohaoSellInfo();
    }
    next();
  },
  activated() {
    if (this.$route.params.info) {
      let info = this.$route.params.info;
      let xiaohaoSellInfo = {
        app_id: info.app_id,
        game_name: info.game.title,
        game_icon: info.game.titlepic,
        nickname: info.nickname,
        platforms: info.platforms,
        pay_sum: info.pay_sum,
        xh_id: info.xh_id,
        trade_id: info.id,
        title: info.title,
        desc: info.desc,
        game_area: info.game_area,
        secret: info.secret,
        role_name: info.role_name,
        price: info.price,
        images: info.images,
        video_url: info.video_url,
        mem_id: info.mem_id,
        play_from: info.play_from,
        is_h5: info.is_h5,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...xiaohaoSellInfo });
      this.isReWrite = true;
      this.game_area = info.game_area;
      this.role_name = info.role_name;
      this.price = info.price;
    }
    if (this.xiaohaoSellInfo.xh_id) {
      this.getPayRatio(this.xiaohaoSellInfo.xh_id);
    }
    this.getSellPriceRule();
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    async getPayRatio(xh_id) {
      const res = await ApiXiaohaoPaySum({
        xhId: xh_id,
        type: 1,
      });
      this.ratioInfo = res.data;
    },
    // 可设置的最小价格规则
    async getSellPriceRule() {
      const res = await ApiXiaohaoTradeIndex();
      this.sellPriceRule = res.data;
    },
    handleNext() {
      if (!this.xiaohaoSellInfo.game_name) {
        this.$toast(this.$t('请选择交易的游戏'));
        return false;
      }
      if (this.xiaohaoSellInfo.is_h5 && !this.xiaohaoSellInfo.play_from) {
        this.$toast(this.$t('请选择角色所在端口'));
        return false;
      }
      if (!this.xiaohaoSellInfo.nickname) {
        this.$toast(this.$t('请选择交易的小号'));
        return false;
      }
      if (!this.game_area) {
        this.$toast(this.$t('请输入区服'));
        return false;
      }
      if (!this.price) {
        this.$toast(this.$t('请设置价格'));
        return false;
      }
      if (
        Number(this.price) <
        (this.xiaohaoSellInfo.pay_sum *
          this.sellPriceRule.trade_pay_percentage) /
          100
      ) {
        this.$toast(this.sellPriceRule.trade_price_text);
        return false;
      }
      this.saveInfo();
      this.toPage('XiaohaoSellConfirm');
    },
    saveInfo() {
      let info = {
        game_area: this.game_area,
        role_name: this.role_name,
        price: this.price,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...info });
    },
    selectGame() {
      if (this.isReWrite) return false;
      this.toPage('SelectGame');
    },
    selectXiaohao() {
      if (this.isReWrite) return false;
      if (!this.xiaohaoSellInfo.app_id) {
        this.$toast(this.$t('请先选择游戏'));
        return false;
      }
      this.toPage('SelectXiaohao');
    },
    selectPort() {
      if (!this.xiaohaoSellInfo.app_id) {
        this.$toast(this.$t('请先选择游戏'));
        return false;
      }
      this.toPage('SelectPort');
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-sell-write-page {
  .main {
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
    .form {
      padding: 0 14 * @rem;
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20 * @rem 0;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #ebebeb;
        }
        .title {
          font-size: 14 * @rem;
          color: #000000;
          flex-shrink: 0;
        }
        .area {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          flex: 1;
          min-width: 0;
          padding-left: 10 * @rem;
          .text {
            font-size: 15 * @rem;
            color: #b4b4b4;
            text-align: right;
            &.fill {
              color: #333333;
            }
          }
          .right-icon {
            width: 6 * @rem;
            height: 8 * @rem;
            background: url(~@/assets/images/deal/right-icon.png) center center
              no-repeat;
            background-size: 6 * @rem 8 * @rem;
            margin-left: 4 * @rem;
          }
          .input-text {
            display: block;
            width: 100%;
            text-align: right;
            font-size: 14 * @rem;
            color: #333333;
          }
          .game-icon {
            width: 25 * @rem;
            height: 25 * @rem;
            border-radius: 6 * @rem;
            overflow: hidden;
          }
          .game-name {
            font-size: 15 * @rem;
            color: #333333;
            margin-left: 8 * @rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .game-port {
            font-size: 15 * @rem;
            color: #333333;
          }
          .game-xiaohao {
            position: relative;
            .top {
              display: flex;
              align-items: center;
              .platforms {
                display: flex;
                align-items: center;
                .plat {
                  width: 17 * @rem;
                  height: 17 * @rem;
                  margin-left: 5 * @rem;
                }
              }
              .nickname {
                font-size: 15 * @rem;
                color: #333333;
                margin-left: 10 * @rem;
              }
            }
            .bottom {
              font-size: 10 * @rem;
              color: #999;
              position: absolute;
              bottom: -16 * @rem;
              right: 0;
              white-space: nowrap;
              span {
                color: #ee1d44;
              }
            }
          }
          .set-price {
            position: relative;
            width: 100%;
            .price-tip {
              font-size: 10 * @rem;
              position: absolute;
              bottom: -30 * @rem;
              right: 0;
              color: #999;
              white-space: nowrap;
              height: 20 * @rem;
              padding: 0 5 * @rem;
              display: flex;
              align-items: center;
              background: #f6f6f6;
              border: 1px solid #eeeeee;
              border-radius: 5 * @rem;
              &::before {
                content: '';
                width: 16 * @rem;
                height: 8 * @rem;
                background: url(~@/assets/images/deal/price-tip-arrow.png)
                  center center no-repeat;
                background-size: 16 * @rem 8 * @rem;
                position: absolute;
                right: 12 * @rem;
                top: -7 * @rem;
              }
            }
          }
        }
      }
    }
  }
  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(70 * @rem + @safeAreaBottom);
    height: calc(70 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .confirm-btn {
      width: 347 * @rem;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
      background: @themeBg;
      border-radius: 8 * @rem;
      margin: 0 auto;
    }
  }
}
</style>
