<template>
  <div class="section-title-components">
    <div class="title">
      <div class="main-title">
        <span :style="{ color: colorIndex == 1 ? color : '' }">{{
          mainTitle1
        }}</span>
        <span :style="{ color: colorIndex == 2 ? color : '' }">{{
          mainTitle2
        }}</span>
      </div>
      <div class="small-title">{{ smallText }}</div>
    </div>
    <div class="more" @click="handleMore">
      {{ $t('查看更多') }}
      <div class="more-icon"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SectionTitle',
  data() {
    return {};
  },
  props: {
    mainTitle1: {
      type: String,
      required: true,
    },
    mainTitle2: {
      type: String,
      required: true,
    },
    smallText: {
      type: String,
      default: '',
    },
    colorIndex: {
      type: Number, // 1 或 2
      default: 1,
    },
    color: {
      type: String,
      default: '#141414',
    },
  },
  methods: {
    handleMore() {
      this.$emit('handleMore');
    },
  },
};
</script>

<style lang="less" scoped>
.section-title-components {
  padding: 5 * @rem 14 * @rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    flex: 1;
    display: flex;
    align-items: center;
    .main-title {
      font-size: 18 * @rem;
      color: #141414;
      font-weight: bold;
      span {
        font-weight: bold;
      }
    }
    .small-title {
      position: relative;
      font-size: 13 * @rem;
      color: #999999;
      padding: 0 5 * @rem;
      margin-left: 5 * @rem;
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1 * @rem;
        height: 16 * @rem;
        background-color: #bfbfbf;
      }
    }
  }
  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 65 * @rem;
    height: 23 * @rem;
    border: 1px solid #d2d2d2;
    border-radius: 12 * @rem;
    font-size: 12 * @rem;
    color: #666666;
    .more-icon {
      width: 4 * @rem;
      height: 7 * @rem;
      background: url(~@/assets/images/home/<USER>
        no-repeat;
      background-size: 4 * @rem 7 * @rem;
      margin-left: 2 * @rem;
    }
  }
}
</style>
