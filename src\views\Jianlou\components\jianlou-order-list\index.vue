<template>
  <div class="jianlou-order-list">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
    >
      <template v-for="(item, index) in list">
        <jianlou-order-item :key="index" :info="item"></jianlou-order-item>
      </template>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoMyOrderListJL } from '@/api/views/xiaohao.js';
import jianlouOrderItem from '../jianlou-order-item';
export default {
  name: 'JianlouOrderList',
  components: {
    jianlouOrderItem,
  },
  props: {
    status: {
      type: Number,
      required: true,
      default: -1,
    },
  },
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      list: [],
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res;
      res = await ApiXiaohaoMyOrderListJL({
        status: this.status,
        page: this.page,
        listRows: this.listRows,
      });

      if (action === 1 || this.page === 1) {
        this.list = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.list.push(...res.data.list);

      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.list.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-order-list {
  box-sizing: border-box;
  height: calc(100vh - 100 * @rem - @safeAreaTop);
  height: calc(100vh - 100 * @rem - @safeAreaTopEnv);
  flex: 1;
  display: flex;
  flex-direction: column;
  .yy-list {
    flex: 1;
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: scroll;
    }
  }
  /deep/ .can-sell {
    display: none;
  }
}
</style>
