<template>
  <div class="user-info-page page">
    <nav-bar-2 :border="true" :title="$t('个人资料')"></nav-bar-2>
    <div class="user-info-container">
      <div class="info-list">
        <div
          :data-clipboard-text="userInfo.username"
          @click="copy"
          class="info-item btn copy"
        >
          <div class="left">
            <div class="item-title">{{ $t('账号') }}</div>
          </div>
          <div class="right">{{ userInfo.username }}</div>
        </div>
        <div class="info-item btn" @click="changeUserPic">
          <input
            class="file"
            type="file"
            accept="image/*"
            ref="file"
            @change="uploadChange"
          />
          <div class="left">
            <div class="item-title">{{ $t('头像') }}</div>
          </div>
          <div class="right">
            <div class="avatar">
              <user-avatar class="pic"></user-avatar>
            </div>
          </div>
        </div>
        <div class="info-item btn" @click="toPage('ChangeNickname')">
          <div class="left">
            <div class="item-title">{{ $t('用户昵称') }}</div>
          </div>
          <div class="right">{{ userInfo.nickname }}</div>
        </div>
        <div class="info-item btn" @click="toPage('ChangePhone')">
          <div class="left">
            <div class="item-title">{{ $t('手机') }}</div>
          </div>
          <div class="right">{{ userInfo.mobile }}</div>
        </div>
        <!-- 邮箱绑定 -->
        <div class="info-item btn" @click="toPage('ChangeEmail')" v-if="isHw">
          <div class="left">
            <div class="item-title">邮箱绑定</div>
          </div>
          <div class="right">
            <div v-if="userInfo.email">{{ userInfo.email }}</div>
            <div v-else>设置绑定</div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="info-item btn" @click="goToChangePassword">
          <div class="left">
            <div class="item-title">{{ $t('修改密码') }}</div>
          </div>
          <div class="right">
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="info-item btn" @click="toPage('IdCard')">
          <div class="left">
            <div class="item-title">{{ $t('实名认证') }}</div>
          </div>
          <div class="right">
            <div>{{ ifAuthStatus }}</div>
            <div class="right-icon"></div>
          </div>
        </div>

        <!-- v-if="[1, 2, 3].includes(login_type)" bak 密保暂时下架 -->
        <div class="info-item btn" @click="goToSetSecurity" v-if="false">
          <div class="left">
            <div class="item-title">设置密保</div>
          </div>
          <div class="right">
            <div>{{ is_security ? '已设置' : '' }}</div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="info-item btn" @click="toPage('AccountCancel')">
          <div class="left"><div class="item-title">注销账号</div></div>
          <div class="right">
            <div class="right-icon"></div>
          </div>
        </div>
      </div>
      <div class="info-list">
        <div class="info-item btn" @click="toPage('ChangeQQ')">
          <div class="left"><div class="item-title">QQ</div></div>
          <div class="right">
            <div>{{ userInfoEx.qq }}</div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="info-item btn" @click="toAuthStatus()">
          <div class="left">
            <div class="item-title">{{ $t('性别') }}</div>
          </div>
          <div class="right">
            <div>{{ stringSex }}</div>
            <div class="right-icon"></div>
          </div>
        </div>
        <div class="info-item btn" @click="toAuthStatus()">
          <div class="left">
            <div class="item-title">{{ $t('生日') }}</div>
          </div>
          <div class="right">
            <div>{{ formatDate }}</div>
            <div class="right-icon"></div>
          </div>
        </div>
        <!-- 国际化语言切换 -->
        <!-- <div class="info-item btn" @click="changeLang()">
          <div class="left">
            <div class="item-title">{{ $t("切换语言") }}</div>
          </div>
          <div class="right">
            <div>{{ langText }}</div>
            <div class="right-icon"></div>
          </div>
        </div> -->
        <div class="info-item btn" @click="toPage('About')">
          <div class="left">
            <div class="item-title">{{ $t('关于') }}</div>
          </div>
        </div>

        <div class="info-item">
          <div class="left">
            <div class="item-title">主页动态</div>
          </div>
          <div class="right">
            <van-switch
              :value="!userInfoEx.open_index"
              @input="upShowInput"
              :size="16"
              active-color="#FE6600"
              inactive-color="#C6C6C6"
            />
          </div>
        </div>
      </div>
      <div class="info-list">
        <div class="info-item btn" @click="exitLogin">
          <div class="logout">{{ $t('退出登录') }}</div>
        </div>
      </div>
    </div>
    <van-action-sheet
      v-model="langShow"
      :actions="langList"
      :description="$t('请选择需要切换的语言')"
      close-on-click-action
      :lock-scroll="false"
      @select="handleChangeLang"
    />
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { ApiChangeAvatar, ApiUserChangeOpenIndex } from '@/api/views/users';
import { ApiUploadImage } from '@/api/views/system';
import { ApiUserInfoEx } from '@/api/views/users';
import { themeColorLess } from '@/common/styles/_variable.less';
import Clipboard from 'clipboard';
import md5 from 'js-md5';
import i18n from '@/i18n/index';
import { sensors } from '@/utils/sensors.js';

export default {
  name: 'UserInfo',
  data() {
    return {
      clipboard: '',
      themeColorLess,
      langShow: false,
      lang: localStorage.getItem('lang') || 'zh_s',
      langList: [
        {
          name: this.$t('简体中文'),
          key: 'zh_s',
        },
        {
          name: this.$t('繁体中文'),
          key: 'zh_t',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfoEx: 'user/userInfoEx',
    }),
    login_type() {
      return Number(this.initData.login_type.type);
    },
    is_security() {
      return this.userInfo.is_security;
    },
    token() {
      return this.userInfo.token;
    },

    langText() {
      let langObj = this.langList.find(item => item.key == this.lang);
      return langObj?.name || this.$t('简体中文');
    },
    mobile() {
      if (this.userInfo.mobile) {
        return this.userInfo.mobile;
      } else {
        return this.$t('未设置');
      }
    },
    stringSex() {
      if (this.userInfoEx.sex) {
        if (parseInt(this.userInfoEx.sex) === 1) {
          return this.$t('女');
        } else if (parseInt(this.userInfoEx.sex) === 2) {
          return this.$t('男');
        }
      } else {
        return this.$t('未设置');
      }
    },
    formatDate() {
      if (this.userInfoEx.birthday) {
        let date = new Date(Number(this.userInfoEx.birthday) * 1000);
        let y = date.getFullYear();
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? '0' + MM : MM;
        let d = date.getDate();
        d = d < 10 ? '0' + d : d;
        return y + '-' + MM + '-' + d;
      } else {
        return this.$t('未设置');
      }
    },
    ifAuthStatus() {
      let res;
      switch (parseInt(this.userInfo.auth_status)) {
        case 0:
          res = this.$t('未实名');
          break;
        case 1:
          res = this.$t('审核中');
          break;
        case 2:
          res = this.$t('已认证');
          break;
        case 3:
          res = this.$t('认证未通过');
          break;
        default:
          res = this.$t('未实名');
          break;
      }
      return res;
    },
  },
  async created() {
    await this.getUserInfoEx();
  },
  beforeDestory: function () {
    // 释放内存
    this.data.clipboard.destroy();
  },
  methods: {
    async getUserInfoEx() {
      const res = await ApiUserInfoEx();
      this.setUserInfoEx(res.data);
    },
    async upShowInput(e) {
      let status = e ? 0 : 1;
      const res = await ApiUserChangeOpenIndex({ status });
      await this.getUserInfoEx();
    },
    changeLang() {
      this.langShow = true;
    },
    handleChangeLang(item) {
      this.lang = item.name;
      localStorage.setItem('lang', item.key);
      i18n.locale = item.key;
    },
    async uploadChange(e) {
      let file = e.target.files[0];
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.image = file;
      data.time = time;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      let res = await ApiUploadImage(data);
      let res2 = await ApiChangeAvatar({ avatar: res.data.object });
      this.$toast(res2.msg);
      this.SET_USER_INFO();
    },
    // 修改头像
    changeUserPic() {
      this.$dialog
        .confirm({
          title: this.$t('头像设置'),
          message: this.$t('将从相簿里选择'),
          confirmButtonText: this.$t('确定'),
          confirmButtonColor: this.themeColorLess,
          lockScroll: false,
        })
        .then(() => {
          this.$refs['file'].click();
          this.$dialog.close();
        });
    },
    toAuthStatus() {
      // 总监说不需要判断是否已经是实名
      this.$dialog
        .confirm({
          message: this.$t('现在去实名认证'),
          confirmButtonText: this.$t('去认证'),
          lockScroll: false,
          confirmButtonColor: this.themeColorLess,
        })
        .then(() => {
          this.toPage('IdCard');
        });
    },
    toPage(name) {
      this.$router.push({ name: name });
    },
    copy() {
      let clipboard = new Clipboard('.copy');
      this.clipboard = clipboard;
      clipboard.on('success', e => {
        this.$toast(this.$t('复制成功'));
      });
      clipboard.on('error', e => {
        // 不支持复制
        this.$toast(this.$t('复制失败，请手动复制'));
      });
    },
    exitLogin() {
      this.$dialog
        .confirm({
          message: this.$t('是否退出登录?'),
          lockScroll: false,
          confirmButtonColor: this.themeColorLess,
        })
        .then(async () => {
          this.setUserInfo({});
          this.setUserInfoEx({});
          sensors.logout();
          // 清除礼包小号数据
          this.setXiaohaoMap();
          await this.SET_PAGE_NAV_LIST();
          await this.SET_REGRESSION_POPUP();
          await this.SET_FIRST_COUPON_ICON();
          localStorage.setItem('STORE', JSON.stringify(this.$store.state));
          this.$router.replace({ name: 'QualitySelect' });
          // 退出登录初始化用户客服信息
          ysf('logoff');
        });
    },
    goToSetSecurity() {
      if (this.is_security) {
        this.$toast('您已设置过密保');
        return;
      }
      this.toPage('SetSecurity');
    },

    goToChangePassword() {
      if (!this.isHw) {
        this.toPage('ChangePassword');
      } else {
        this.toPage('ChangePasswordHw', { step: 1 });
      }
    },
    ...mapMutations({
      setUserInfo: 'user/setUserInfo',
      setUserInfoEx: 'user/setUserInfoEx',
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    ...mapActions({
      SET_REGRESSION_POPUP: 'user/SET_REGRESSION_POPUP',
      SET_FIRST_COUPON_ICON: 'user/SET_FIRST_COUPON_ICON',
      SET_USER_INFO: 'user/SET_USER_INFO',
      SET_PAGE_NAV_LIST: 'system/SET_PAGE_NAV_LIST',
    }),
  },
};
</script>

<style lang="less" scoped>
.user-info-page {
  background-color: #f6f6f6;
}
.user-info-container {
  .info-list {
    margin-bottom: 10 * @rem;
    background-color: #fff;
    padding: 0 14 * @rem;
    .info-item {
      border-top: 1px solid #eeeeee;
      padding: 17 * @rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:nth-of-type(1) {
        border-top: 0;
      }
      .left {
        .item-title {
          font-size: 15 * @rem;
        }
      }
      .right {
        font-size: 15 * @rem;
        color: #666666;

        display: flex;
        align-items: center;
        .avatar {
          width: 40 * @rem;
          height: 40 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
        .right-icon {
          width: 6 * @rem;
          height: 10 * @rem;
          margin-left: 5 * @rem;
          background: url(~@/assets/images/right-icon.png) no-repeat;
          background-size: 6 * @rem 10 * @rem;
        }
      }
    }
  }
}
.logout {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16 * @rem;
  color: @themeColor;
}
input.file {
  display: none;
}
</style>
