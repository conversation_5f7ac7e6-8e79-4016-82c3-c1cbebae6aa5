<template>
  <div class="list-page">
    <nav-bar-2 title="往期精选建议" :azShow="true"></nav-bar-2>
    <yy-list
      class="list-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
      tips="暂无数据"
    >
      <div class="list">
        <div
          class="item"
          v-for="(item, index) in list"
          :key="index"
          @click="toDetail(item)"
        >
          <img class="pic" :src="item.cover_img" alt="" />
          <div class="item-info">
            <div class="title">{{ item.title }}</div>
            <div class="date"
              >{{ formatDate(item.start_time) }} 至
              {{ formatDate(item.end_time) }}</div
            >
          </div>
          <div class="state end">已结束</div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiPlayerVoiceGetOverList } from '@/api/views/playerVoice';
export default {
  name: 'PastList',
  data() {
    return {
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  async created() {
    this.loadingObj.loading = true;
    await this.getList(2);
    this.loadingObj.loading = false;
  },
  methods: {
    toDetail(item) {
      this.toPage('PlayerVoice', {
        id: item.id,
      });
    },
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
      }
      let res = await ApiPlayerVoiceGetOverList({
        page: this.page,
        listRows: this.listRows,
      });
      let list = res.data.list;
      if (list.length < this.listRows) {
        this.finished = true;
      }
      this.list.push(...list);
      if (this.list.length == 0) {
        this.empty = true;
      } else {
        this.empty = false;
      }
    },
    async onRefresh() {
      await this.getList(2);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
    formatDate(timeStamp) {
      let { year, month, day } = this.$handleTimestamp(timeStamp);
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style lang="less" scoped>
.list-page {
  .list {
    padding: 0 12px;
    .item {
      width: 351px;
      margin: 20px auto 0;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
      box-shadow: 0px 2px 8px 0px rgba(0, 41, 90, 0.08);

      .pic {
        width: 100%;
        height: 150px;
      }

      .item-info {
        padding: 14px 9px 16px;
        background-color: #fff;
        .title {
          width: 100%;
          height: 18px;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 18px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .date {
          width: 100%;
          height: 15px;
          font-weight: 400;
          font-size: 12px;
          color: #9a9a9a;
          line-height: 15px;
          margin-top: 6px;
        }
      }

      .state {
        height: 28px;
        line-height: 28px;
        padding: 0 17px;
        text-align: center;
        background-color: @themeColor;
        font-weight: 600;
        font-size: 13px;
        color: #ffffff;
        border-radius: 12px 0 12px 0;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;

        &.end {
          background-color: #393939;
        }
      }
    }
  }
}
</style>
