<template>
  <div class="page">
    <nav-bar-2 title="创建游戏合集" :placeholder="true">
      <template #right>
        <div class="submit-btn btn" @click="submit"> 提交 </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="upload-container">
        <div class="banner" v-if="banner">
          <img :src="banner" alt="" />
        </div>
        <van-uploader
          class="upload-btn"
          :preview-image="true"
          accept="image/*"
          :after-read="afterRead"
        >
          <div class="content">
            <div class="upload-icon"></div>
            <div class="text">点击上传图片</div>
          </div>
        </van-uploader>
      </div>
      <div class="label">
        <div class="label-title">
          <div class="title">选择游戏<span>*</span></div>
          <div class="tips" @click="toSelectUpGame">5款以上可发布&gt;</div>
        </div>
        <div class="selected-game-list">
          <div
            class="selected-game-item"
            v-for="(item, index) in selectedGameList"
            :key="index"
            @click="deleteGame(item)"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="line"></div>
      </div>
      <div class="label">
        <div class="label-title">
          <div class="title">填写标题<span>*</span></div>
        </div>
        <input
          v-model.trim="title"
          class="input-text"
          type="text"
          placeholder="请输入标题"
        />
      </div>
      <div class="label">
        <div class="title">填写副标题<span>*</span></div>
        <input
          v-model.trim="subtitle"
          class="input-text"
          type="text"
          placeholder="请输入副标题"
        />
      </div>
      <div class="label">
        <div class="title">填写简介：</div>
        <textarea
          class="input-text textarea-text"
          placeholder="请输入简介"
          v-model.trim="introduction"
        ></textarea>
      </div>
      <div class="bottom-text">
        <span
          @click="
            toPage('Iframe', {
              title: '游戏合集管理规范',
              url: H5Page.hejiguanli,
            })
          "
          >《游戏合集管理规范》</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import H5Page from '@/utils/h5Page';
import { mapGetters, mapMutations } from 'vuex';
export default {
  data() {
    return {
      H5Page,
      banner: '',
      title: '',
      subtitle: '',
      introduction: '',
    };
  },
  computed: {
    ...mapGetters({
      selectedGameList: 'up/selectedGameList',
    }),
  },
  methods: {
    ...mapMutations({
      setSelectedGameList: 'up/setSelectedGameList',
    }),
    toSelectUpGame() {
      this.toPage('SelectUpGame');
    },
    submit() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return;
      }
      if (this.selectedGameList?.length < 5) {
        this.$toast('您选择的游戏不5款');
        return;
      }
      if (!this.title) {
        this.$toast('标题未填写');
        return;
      }
      if (!this.subtitle) {
        this.$toast('副标题未填写');
        return;
      }
      this.$toast('提交成功，游戏合集将在1-2个工作日内审核完成');
      this.setSelectedGameList();
      this.title = '';
      this.subtitle = '';
      setTimeout(() => {
        this.back();
      }, 1000);
    },
    afterRead(e) {
      this.banner = e.content;
    },
    deleteGame(item) {
      this.setSelectedGameList(item.id);
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .submit-btn {
    width: 58 * @rem;
    height: 26 * @rem;
    background-color: @themeColor;
    border-radius: 13 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12 * @rem;
    color: #ffffff;
  }
  .main {
    padding-bottom: 40 * @rem;
    .upload-container {
      width: 335 * @rem;
      height: 140 * @rem;
      margin: 24 * @rem auto 0;
      border: 1px solid #ffefe8;
      border-radius: 10 * @rem;
      overflow: hidden;
      position: relative;
      .banner {
        width: 335 * @rem;
        height: 140 * @rem;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        &::after {
          content: '';
          width: 335 * @rem;
          height: 140 * @rem;
          background: rgba(0, 0, 0, 0.5);
          position: absolute;
          left: 0;
          top: 0;
          z-index: 2;
        }
      }
      .upload-btn {
        position: relative;
        z-index: 3;
      }
      .content {
        width: 335 * @rem;
        height: 140 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .upload-icon {
          width: 28 * @rem;
          height: 28 * @rem;
          background: url('~@/assets/images/up-collection/upload-icon.png')
            center center no-repeat;
          background-size: 28 * @rem 28 * @rem;
        }
        .text {
          width: 100 * @rem;
          height: 32 * @rem;
          background: #fffaf7;
          border-radius: 5 * @rem;
          border: 1px solid rgba(254, 102, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: @themeColor;
          margin-top: 18 * @rem;
        }
      }
    }
    .label {
      padding: 0 20 * @rem;
      margin-top: 24 * @rem;
      .label-title {
        display: flex;
        align-items: center;
      }
      .line {
        width: 100%;
        height: 1 * @rem;
        border-top: 1px solid #ebebeb;
        margin-top: 13 * @rem;
      }
      .title {
        font-size: 15 * @rem;
        line-height: 18 * @rem;
        color: #333333;
        font-weight: bold;
        flex: 1;
        min-width: 0;
        span {
          color: @themeColor;
        }
      }
      .input-text {
        box-sizing: border-box;
        padding: 10 * @rem;
        width: 335 * @rem;
        height: 42 * @rem;
        margin-top: 10 * @rem;
        border: 1px solid #ebebeb;
        border-radius: 8 * @rem;
        font-size: 15 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        &.textarea-text {
          height: 180 * @rem;
        }
      }
      .tips {
        font-size: 12 * @rem;
        color: #999999;
        line-height: 14 * @rem;
      }
    }
    .bottom-text {
      text-align: center;
      font-size: 12 * @rem;
      color: #1283ff;
      line-height: 14 * @rem;
      margin-top: 20 * @rem;
    }
  }
}
.selected-game-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10 * @rem;
  overflow: hidden;
  .selected-game-item {
    position: relative;
    height: 28 * @rem;
    padding: 0 11 * @rem;
    display: flex;
    align-items: center;
    margin-right: 10 * @rem;
    font-size: 12 * @rem;
    color: #555555;
    background-color: #f5f6f9;
    border-radius: 14 * @rem;
    margin-top: 12 * @rem;
    &::after {
      content: '';
      width: 14 * @rem;
      height: 14 * @rem;
      position: absolute;
      right: -4 * @rem;
      top: -4 * @rem;
      background: url('~@/assets/images/up-collection/game-clear.png') center
        center no-repeat;
      background-size: 14 * @rem 14 * @rem;
    }
  }
}
</style>
