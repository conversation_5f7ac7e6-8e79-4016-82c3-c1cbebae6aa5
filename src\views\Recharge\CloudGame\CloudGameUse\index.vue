<template>
  <div class="page cloud-game-use-page">
    <nav-bar-2
      :border="true"
      :title="$t('时长明细')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="duration-box">
        <div class="title">{{ $t('总时长') }}</div>
        <div class="duration-cont">{{ formatDuration(duration) }}</div>
      </div>
      <div class="duration-list">
        <template v-if="!empty">
          <yy-list
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :empty="empty"
          >
            <div
              class="duration-item"
              v-for="(item, index) in durationList"
              :key="index"
            >
              <div class="info">
                <div class="title">{{ item.title }}</div>
                <div class="time">{{ formatDate(item.create_time) }}</div>
              </div>
              <div class="duration-detail"
                >{{ item.duration }}{{ $t('分钟') }}</div
              >
            </div>
          </yy-list>
        </template>
        <div class="empty-box" v-else>
          <img
            src="@/assets/images/recharge/cloud-game/empty-icon.png"
            alt=""
          />
          <p>{{ $t('暂无订单数据') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ApiCloudUserDurationLog } from '@/api/views/recharge.js';
export default {
  name: 'CloudGameUse',
  data() {
    return {
      duration: 0,
      durationList: [],
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 15,
      empty: false,
    };
  },
  methods: {
    async getDurationLog() {
      let res = await ApiCloudUserDurationLog({
        page: this.page,
        listRows: this.listRows,
      });
      this.duration = res.data.duration;

      if (this.page === 1) this.durationList = [];
      this.durationList.push(...res.data.list);
      if (this.durationList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getDurationLog();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getDurationLog();
      this.page++;
      this.loadingObj.loading = false;
    },
    formatDuration(minute = 0) {
      if (minute) {
        let hour = Math.floor(minute / 60);
        let minutes = minute % 60;
        if (hour > 99999) {
          return `${hour}小时`;
        } else {
          return `${hour}小时${minutes}分`;
        }
      } else {
        return `0小时0分`;
      }
    },
    formatDate(date) {
      let temp = new Date(date * 1000);
      let y = temp.getFullYear();
      let m = temp.getMonth() + 1;
      let d = temp.getDate();
      let h = temp.getHours();
      let min = temp.getMinutes();
      let sec = temp.getSeconds();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      h = h < 10 ? '0' + h : h;
      min = min < 10 ? '0' + min : min;
      sec = sec < 10 ? '0' + sec : sec;
      return `${y}-${m}-${d} ${h}:${min}:${sec}`;
    },
  },
};
</script>

<style lang="less" scoped>
.main {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.duration-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 128 * @rem;
  background-color: #fff;
  border-bottom: 10 * @rem solid #f6f7f9;

  .title {
    width: 100%;
    height: 20 * @rem;
    font-weight: 400;
    font-size: 14 * @rem;
    color: #666666;
    line-height: 20 * @rem;
    text-align: center;
  }
  .duration-cont {
    width: 100%;
    height: 25 * @rem;
    font-weight: 600;
    font-size: 18 * @rem;
    color: #222222;
    line-height: 25 * @rem;
    text-align: center;
    margin-top: 6 * @rem;
  }
}
.duration-list {
  flex: 1;
  min-height: 0;

  .duration-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18 * @rem;
    margin-top: 20 * @rem;

    .info {
      flex: 1;
      min-width: 0;

      .title {
        height: 20 * @rem;
        font-weight: 600;
        font-size: 14 * @rem;
        color: #222222;
        line-height: 20 * @rem;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .time {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #9a9a9a;
        line-height: 15 * @rem;
        text-align: left;
        margin-top: 6 * @rem;
      }
    }

    .duration-detail {
      height: 20 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #222222;
      line-height: 20 * @rem;
      text-align: right;
    }
  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 128 * @rem;
      height: 128 * @rem;
    }

    p {
      width: 100%;
      height: 18 * @rem;
      font-size: 14 * @rem;
      color: #666666;
      line-height: 18 * @rem;
      text-align: center;
      margin-top: 16 * @rem;
    }
  }
}
</style>
