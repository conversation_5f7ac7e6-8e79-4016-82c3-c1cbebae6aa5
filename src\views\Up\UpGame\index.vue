<template>
  <div class="up-game">
    <nav-bar-2
      :title="'我要UP'"
      :placeholder="false"
      :border="true"
      class="nav"
    >
      <template #right>
        <div @click="toPage('MyUp')" class="text btn">我的资源</div>
      </template>
    </nav-bar-2>
    <div class="upload">
      <van-uploader
        v-model="file_list"
        :after-read="fileAfterRead"
        :preview-image="false"
        :max-count="1"
        accept=".apk"
        :max-size="5368709120"
        :multiple="true"
      >
        <div class="content">
          <div class="text">点击上传资源<span>（限制5120M）</span></div>
          <div class="arrow"></div>
        </div>
      </van-uploader>
      <div v-if="file_list.length > 0" class="content content2">
        <div class="text">文件扫描中，请耐心等待</div>
        <div class="text2">努力排队中</div>
      </div>
    </div>
    <div class="label">
      <div class="title">应用名称：</div>
      <input
        v-model.trim="game_name"
        class="app-name input-text"
        type="text"
        disabled
        placeholder="请输入应用名称(上传后自动填写)"
      />
    </div>
    <div class="label">
      <div class="title title2">
        <div class="text">UP主说：</div>
        <div class="right">{{ explain.length }}/1000字</div>
      </div>
      <textarea
        class="textarea1"
        placeholder="必填，请输入5--1000个文字"
        v-model.trim="explain"
      ></textarea>
    </div>
    <div class="label">
      <div class="title">分类选择：</div>
      <div @click="popup2 = true" class="app-category input-text">
        {{ app_category ? app_category : '最多可选择2种分类' }}
      </div>
    </div>
    <div class="label">
      <div class="title">请输入标签：</div>
      <input
        v-model.trim="app_tag"
        class="app-tag input-text"
        type="text"
        placeholder="请输入标签以 / 进行分隔，只有2个标签可生效"
      />
    </div>
    <div class="label">
      <div class="title">上传图片<span>（选填，最多5张）</span></div>
      <div class="photos">
        <van-uploader
          v-model="image_file_list"
          :after-read="afterRead"
          @delete="deletePic"
          :max-count="5"
          accept="image/*"
          :before-read="beforeRead"
          :multiple="true"
        >
          <div class="upload-btn">
            <img src="@/assets/images/feedback/upload-add.png" />
          </div>
        </van-uploader>
      </div>
    </div>
    <div class="bottom-text">
      查看<span
        @click="
          toPage('Iframe', {
            title: 'UP资源功能使用协议',
            url: H5Page.upAgreement,
          })
        "
        >《UP资源功能使用协议》</span
      >
    </div>
    <div class="button-container">
      <div @click="submit" class="btn button left-button">立即提交</div>
      <div @click="submit(2)" class="btn button right-button">后台上传</div>
    </div>
    <van-dialog
      v-model="popup1"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="tips-popup"
    >
      <div class="title">提示</div>
      <div class="text">扫描上传排队中</div>
      <div class="text">上传完毕后您将收到站内信提示消息</div>
      <div class="button-container">
        <div @click="toPage('Mine')" class="left">知道了</div>
        <div @click="toPage('MyUp')" class="right">切至后台上传</div>
      </div>
    </van-dialog>
    <!-- 分类弹窗 -->
    <van-dialog
      v-model="popup2"
      :showConfirmButton="false"
      :lockScroll="false"
      :closeOnClickOverlay="true"
      class="category-popup"
    >
      <div class="title">游戏分类</div>
      <div @click="popup2 = false" class="close"></div>
      <van-checkbox-group v-model="current_list" :max="2" class="list">
        <van-checkbox
          v-for="item in up_category_list"
          :key="item.id"
          :name="item"
          :class="{ current: 0 }"
          class="item"
        >
          {{ item.title }}
        </van-checkbox>
      </van-checkbox-group>
      <div class="button-container">
        <div @click="popup2 = false" class="left">取消</div>
        <div @click="changeAppCategory" class="right">确定</div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiUploadImage, ApiUploadApk } from '@/api/views/system';
import { ApiUserUpGame } from '@/api/views/users';
import { ApiGameGetUpCate } from '@/api/views/game.js';
import md5 from 'js-md5';
import H5Page from '@/utils/h5Page';

export default {
  data() {
    return {
      submiting: false, //是否提交中中
      file_list: [], //上传apk资源
      file_apk: [], //上传apk链接
      image_file_list: [], // 上传截图资源
      images: [], // 上传截图链接
      game_name: '', // 游戏名称
      explain: '', // 详细说明
      H5Page,
      app_category: '', //app分类id
      app_tag: '', //app的tag
      popup1: false, //提示弹窗
      popup2: false, //分类弹窗
      up_category_list: [],
      current_list: [], //选中分类列表
    };
  },
  async created() {
    const res = await this.getUpCategory();
  },
  methods: {
    // 处理上传图像
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.images.push(res.data.object);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    // 上传图像前置处理
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    // 图像上传完毕后
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
          this.handleUpload(item);
        }
      }
    },
    // 删除图片
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
    // 处理apk上传
    async handleApkUpload(file) {
      try {
        const res = await ApiUploadApk({
          up_file: file.file,
        });
        this.file_apk = res.data;
      } catch (err) {}
    },
    // apk上传前置
    fileBeforeRead() {},
    // apk上传后
    async fileAfterRead(file) {
      this.game_name = file.file.name;
      await this.handleApkUpload(file);
    },
    // 提交
    async submit(type = 1) {
      if (this.submiting) {
        this.popup1 = true;
      }
      if (!this.file_apk) {
        this.$toast('文件还未上传');
      }
      if (!this.game_name) {
        this.$toast('请填写应用名称');
        return false;
      }
      if (!this.explain) {
        this.$toast('请填写UP主说');
        return false;
      }
      if (!this.current_list.length) {
        this.$toast('请选择分类');
        return false;
      }
      if (!this.app_tag) {
        this.$toast('请输入标签');
        return false;
      }
      // 开始提交操作
      let params = {
        file_url: this.file_apk,
        file_name: this.game_name,
        content: this.explain,
        tag: this.app_tag,
      };
      // 分类
      params.cate =
        params.cate.length > 1
          ? `${this.current_list[0].id},${this.current_list[1].id}`
          : `${this.current_list[0].id}`;
      // 有上传图片就加图片参数
      if (this.images.length) {
        params.imgs = JSON.stringify(this.images);
      }
      try {
        this.submiting = true;
        const res = await ApiUserUpGame(params);
        if (type !== 1) {
          this.toPage('MyUp');
        }
        if (res.msg) {
          this.$toast(res.msg);
        }
      } catch (e) {}
    },
    // 拉取分类列表
    async getUpCategory() {
      const res = await ApiGameGetUpCate();
      this.up_category_list = res.data.list || [];
    },
    // 修改APP分类
    changeAppCategory() {
      try {
        this.app_category = `${this.current_list[0].title}、${this.current_list[1].title}`;
      } catch {}
      this.popup2 = false;
    },
  },
};
</script>
<style lang="less" scoped>
.up-game {
  display: flex;
  padding: 50 * @rem 0;
  flex-direction: column;
}
.nav {
  .text {
    font-size: 14 * @rem;
    color: #999999;
  }
}
.upload {
  display: flex;
  position: relative;
  height: 68 * @rem;
  margin: 35 * @rem 18 * @rem;
  background: linear-gradient(
    180deg,
    fade(@themeColor, 10) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 8 * @rem 8 * @rem 8 * @rem 8 * @rem;
  opacity: 1;
  border: 1 * @rem solid fade(@themeColor, 50);
  .content {
    box-sizing: border-box;
    width: 337 * @rem;
    height: 68 * @rem;
    display: flex;
    justify-content: right;
    align-items: center;
    padding: 0 22 * @rem;
    .text {
      flex: 1;
      font-size: 14 * @rem;
      font-family:
        PingFang SC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #333333;
      text-align: center;
      span {
        margin-left: 5 * @rem;
        font-size: 12 * @rem;
        color: #999999;
      }
    }
    .arrow {
      width: 24 * @rem;
      height: 24 * @rem;
      .image-bg('~@/assets/images/up/arrow.png');
    }
  }
  .input-file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}
.label {
  margin: 0 18 * @rem 24 * @rem;
  .input-text {
    box-sizing: border-box;
    padding: 10 * @rem;
    width: 339 * @rem;
    height: 36 * @rem;
    background: #f5f5f6;
    border-radius: 6 * @rem 6 * @rem 6 * @rem 6 * @rem;
  }
  .app-category {
    display: flex;
    align-items: center;
    color: #c1c1c1;
  }
  .textarea1 {
    box-sizing: border-box;
    padding: 10 * @rem;
    width: 339 * @rem;
    height: 160 * @rem;
    background: #f5f5f6;
    border-radius: 6 * @rem 6 * @rem 6 * @rem 6 * @rem;
    resize: none;
    border: none;
    font-size: 13 * @rem;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    line-height: 21 * @rem;
  }
  .title {
    margin-bottom: 10 * @rem;
    font-size: 14 * @rem;
    font-family:
      PingFang SC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #000000;
    &.title2 {
      display: flex;
      justify-content: space-between;
      .text {
        font-size: 14 * @rem;
        font-family:
          PingFang SC-Semibold,
          PingFang SC;
        font-weight: 600;
        color: #000000;
      }
      .right {
        font-size: 12 * @rem;
        color: #c1c1c1;
      }
    }
    span {
      margin-left: 2 * @rem;
      font-size: 12 * @rem;
      font-family:
        PingFang SC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #c1c1c1;
    }
  }
}
.bottom-text {
  margin: 16 * @rem 0 52 * @rem;
  padding-left: 18 * @rem;
  font-size: 14 * @rem;
  font-family:
    PingFang SC-Regular,
    PingFang SC;
  font-weight: 400;
  color: #333333;
  span {
    color: #5678ff;
  }
}
.button-container {
  display: flex;
  justify-content: space-between;
  margin: 0 18 * @rem;
  .button {
    width: 160 * @rem;
    height: 42 * @rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 33 * @rem 33 * @rem 33 * @rem 33 * @rem;
    font-size: 16 * @rem;
  }
  .left-button {
    background: @themeBg;
  }
  .right-button {
    background: #628dff;
  }
}
.photos {
  margin-top: 15 * @rem;
  /deep/ .van-uploader__preview {
    margin: 0 35 * @rem 15 * @rem 0;
  }
  /deep/ .van-uploader__preview:nth-of-type(3n) {
    margin-right: 0;
  }
  /deep/ .van-uploader__preview-image {
    width: 88 * @rem;
    height: 88 * @rem;
    border-radius: 7 * @rem;
  }
  /deep/ .van-uploader__preview-delete {
    width: 20 * @rem;
    height: 20 * @rem;
    top: -10 * @rem;
    right: -10 * @rem;
    transform: scale(0.8);
    border-radius: 10 * @rem;
    background-color: rgb(154, 154, 154);
  }
  /deep/ .van-uploader__preview-delete-icon {
    transform: scale(1);
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .upload-btn {
    width: 88 * @rem;
    height: 88 * @rem;
  }
}
.tips-popup {
  width: 300 * @rem;
  .title {
    margin: 20 * @rem auto 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-family:
      PingFang SC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .text {
    text-align: center;
    font-size: 14px;
    line-height: 21px;
    font-family:
      PingFang SC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #777777;
  }
  .button-container {
    margin: 15 * @rem auto;
    display: flex;
    justify-content: space-between;
    padding: 0 30 * @rem;
    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 110 * @rem;
      height: 35 * @rem;
      border-radius: 18 * @rem 18 * @rem 18 * @rem 18 * @rem;
    }
    .left {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .right {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #ffffff;
      background: @themeColor;
    }
  }
}
.category-popup {
  padding: 24 * @rem 20 * @rem;
  border-radius: 15 * @rem 15 * @rem;
  box-sizing: border-box;
  .title {
    font-size: 16 * @rem;
    font-weight: 600;
  }
  .close {
    position: absolute;
    top: 15 * @rem;
    right: 15 * @rem;
    width: 15 * @rem;
    height: 15 * @rem;
    .image-bg('~@/assets/images/close-black.png');
  }
  .list {
    overflow: hidden;
    .item {
      position: relative;
      float: left;
      box-sizing: border-box;
      width: 80 * @rem;
      height: 32 * @rem;
      margin-top: 15 * @rem;
      margin-right: 12 * @rem;
      border: 1 * @rem solid #c1c1c1;
      border-radius: 6 * @rem;
      font-size: 13 * @rem;
      color: #797979;
      display: flex;
      justify-content: center;
      align-items: center;
      &:nth-of-type(3n) {
        margin-right: 0;
      }
      /deep/ .van-checkbox__icon {
        display: none;
      }
      /deep/ .van-checkbox__label {
        margin-left: 0;
      }
      &[aria-checked='true'] {
        border-color: @themeColor;
        color: @themeColor;
        &::after {
          position: absolute;
          bottom: -1 * @rem;
          right: -1 * @rem;
          content: '';
          width: 12 * @rem;
          height: 12 * @rem;
          .image-bg('~@/assets/images/up/up_game_icon1.png');
        }
      }
    }
  }
  .button-container {
    margin: 15 * @rem auto 0;
    display: flex;
    justify-content: space-between;
    padding: 0 15 * @rem;
    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 110 * @rem;
      height: 35 * @rem;
      border-radius: 18 * @rem 18 * @rem 18 * @rem 18 * @rem;
    }
    .left {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #7d7d7d;
      background: #f2f2f2;
    }
    .right {
      font-size: 13 * @rem;
      font-weight: 400;
      color: #ffffff;
      background: @themeColor;
    }
  }
}
</style>
