<template>
  <div class="wrap" v-if="showWrap" :class="showContent ? 'fadein' : 'fadeout'">
    <div class="logo">
      <img
        :src="require('@/assets/images/clock-in/collect-toast-logo.png')"
        alt=""
      />
    </div>
    <div class="title">{{ title }}</div>
    <div class="info">{{ info }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      logoIcon: '',
      title: '',
      info: '',
      showWrap: true,
      showContent: true,
    };
  },
};
</script>
<style scoped lang="less">
.wrap {
  position: fixed;
  top: 50%;
  left: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  box-sizing: content-box;
  width: 146 * @rem;
  height: 160 * @rem;
  max-width: 70%;
  min-height: 160 * @rem;
  color: #fff;
  font-size: 14 * @rem;
  white-space: pre-wrap;
  text-align: center;
  word-break: break-all;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 12 * @rem;
  z-index: 999999;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  padding: 28 * @rem 19 * @rem 0 19 * @rem;
  box-sizing: border-box;
  .logo {
    background-size: 48 * @rem 48 * @rem;
    width: 48 * @rem;
    height: 48 * @rem;
  }
  .title {
    margin-top: 15 * @rem;
    white-space: nowrap;
    font-size: 16 * @rem;
    color: #ffffff;
    height: 22 * @rem;
    line-height: 22 * @rem;
  }
  .info {
    margin-top: 2 * @rem;
    width: 108 * @rem;
    height: 34 * @rem;
    font-weight: 400;
    font-size: 11 * @rem;
    color: #ffffff;
    line-height: 17 * @rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
.fadein {
  animation: animate_in 0.25s;
}
.fadeout {
  animation: animate_out 0.25s;
  opacity: 0;
}
@keyframes animate_in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes animate_out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
