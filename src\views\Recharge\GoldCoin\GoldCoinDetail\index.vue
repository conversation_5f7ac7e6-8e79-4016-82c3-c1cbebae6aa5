<template>
  <div class="page platform-coin-detail-page">
    <nav-bar-2
      :border="true"
      :title="$t('金币明细')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
      >
        <div class="record-list">
          <div
            class="record-item"
            v-for="(item, index) in coinDetailList"
            :key="index"
          >
            <div class="info">
              <div class="title">{{ item.rule }}</div>
              <div class="date">{{ item.create_time }}</div>
            </div>
            <div class="num" :class="{ pay: item.num < 0 }">
              {{ addPlus(item.num) }}{{ item.num }}{{ $t('个') }}
            </div>
          </div>
        </div>
      </yy-list>
    </div>
    <bottom-safe-area></bottom-safe-area>
  </div>
</template>
<script>
import { ApiGoldDetail } from '@/api/views/recharge.js';
export default {
  name: 'PlatformCoinDetail',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 30,
      coinDetailList: [],
      empty: false,
    };
  },
  methods: {
    async getList() {
      const res = await ApiGoldDetail({
        page: this.page,
        listRows: this.listRows,
      });
      if (this.page === 1) this.coinDetailList = [];
      this.coinDetailList.push(...res.data);
      if (this.coinDetailList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
      this.page++;
    },
    addPlus(num) {
      return num <= 0 ? '' : '+';
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-detail-page {
  .main {
    display: flex;
    flex-direction: column;
    flex: 1;

    .record-list {
      padding: 0 14 * @rem;

      .record-item {
        display: flex;
        justify-content: space-between;
        border-bottom: 0.5px solid #ebebeb;
        padding: 15 * @rem 0;

        .info {
          .title {
            font-size: 15 * @rem;
            color: #111111;
            font-weight: 600;
            line-height: 21 * @rem;
          }

          .date {
            font-size: 13 * @rem;
            color: #999999;
            margin-top: 6 * @rem;
            line-height: 18 * @rem;
          }
        }

        .num {
          font-size: 15 * @rem;
          line-height: 21 * @rem;
          color: #f60000;
          font-weight: 600;

          &.pay {
            color: #00902c;
          }
        }
      }
    }
  }
}
</style>
