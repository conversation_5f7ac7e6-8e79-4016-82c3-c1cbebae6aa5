<template>
  <div class="xiaohao-collection-page">
    <div class="collection-list" :class="{ pb: isEditing }">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="xiaohao-list">
          <van-swipe-cell
            v-for="(item, index) in xiaohaoList"
            :key="index"
            :disabled="isEditing"
          >
            <!-- <div
              class="xiaohao-item"
              :class="{ btn: isEditing }"
              @click.capture="handleSelect(item, $event)"
            >
              <div
                class="select-btn"
                :class="{ selected: selectedIn(item) }"
                v-if="isEditing"
              ></div>
              <div class="xiaohao-content">
                <div
                  class="buy-item"
                  @click="toPage('XiaohaoDetail', { id: item.id })"
                >
                  <div class="left">
                    <div class="info">
                      <div class="pic">
                        <img :src="item.images[0]" alt="" />
                      </div>
                      <div class="game-info">
                        <div class="title">{{ item.title }}</div>
                        <div class="game-name">{{ item.game.title }}</div>
                        <div class="game-port">
                          {{ $t('区服') }}：{{ item.game_area }}
                        </div>
                      </div>
                    </div>
                    <div class="desc">
                      {{ $t('该小号创建') }}
                      <span>{{ item.xh_days }}</span>
                      {{ $t('天，实际充值') }}
                      <span>{{ item.pay_sum }}</span>
                      元
                    </div>
                  </div>
                  <div class="right">
                    <div class="price">
                      ¥{{ Number(item.price).toFixed(2) }}
                    </div>
                    <div v-show="item.gold_num" class="gold">
                      {{ $t('立返') }}{{ item.gold_num }}{{ $t('金币') }}
                    </div>
                    <div class="platform">
                      <div
                        class="plat-icon"
                        v-for="(plat, platIndex) in item.platforms"
                        :key="platIndex"
                      >
                        <img :src="plat.icon" alt="" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div> -->
            <div
              class="deal-item-component"
              @click="toPage('XiaohaoDetail', { id: item.id })"
            >
              <div class="deal-info">
                <div class="deal-banner">
                  <img :src="item.images[0]" alt="" />
                </div>
                <div class="right-info">
                  <div class="center-info">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="game-info">
                      <div class="game-item">
                        <div class="game-name">{{ item.game.title }}</div>
                        <div class="platform">
                          <div
                            class="plat-icon"
                            v-for="(plat, platIndex) in item.platforms"
                            :key="platIndex"
                          >
                            <img :src="plat.icon" alt="" />
                          </div>
                        </div>
                      </div>
                      <div class="game-money"
                        >{{ $t('实充￥') }}{{ item.pay_sum }}</div
                      >
                    </div>

                    <div class="bottom-section">
                      <div class="date">
                        {{ $t('上架时间') }}：{{
                          formatDate(item.ss_create_time)
                        }}
                      </div>
                      <div
                        class="appoint-tag"
                        v-if="!!Number(item.specify_mem_id)"
                      >
                        <div class="appoint-icon"></div>
                        <div class="appoint-text">{{ $t('指定出售') }}</div>
                      </div>
                      <div class="gold gold-vip" v-else-if="item.gold_num_vip">
                        {{ $t('立返') }}{{ item.gold_num_vip }}{{ $t('金币') }}
                      </div>
                      <div class="gold" v-if="item.gold_num_normal">
                        {{ $t('立返') }}{{ item.gold_num_normal
                        }}{{ $t('金币') }}
                      </div>
                      <div class="price">
                        ¥<span>{{ Number(item.price).toFixed(0) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <template #right>
              <div class="delete-btn btn" @click="deleteItem(item.id)">
                {{ $t('删除') }}
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </yy-list>
    </div>
    <div class="bottom-bar fixed-center" v-if="isEditing">
      <div class="bottom-content">
        <div class="all-select btn" @click="handleAllSelect">
          <div class="select-icon" :class="{ selected: isAllSelected }"></div>
          <div class="all-select-text">{{ $t('全选') }}</div>
        </div>
        <div class="all-delete-btn btn" @click="handleDelete">
          {{ $t('删除') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiResourceCollection, ApiResourceCollect } from '@/api/views/game.js';
export default {
  name: 'xiaohaoCollection',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      xiaohaoList: [],
      empty: false,
      isEditing: false,
      selectedIdList: [], // 已选中的数据
    };
  },
  computed: {
    isAllSelected() {
      let flag = true;
      for (let item of this.xiaohaoList) {
        if (!this.selectedIn(item)) {
          flag = false;
          break;
        }
      }
      return flag;
    },
  },
  async activated() {
    this.$parent.isEditing = this.isEditing;
    this.finished = false;
    this.loadingObj.loading = false;
    this.loadingObj.reloading = false;
    await this.getMyxiaohao();
  },
  methods: {
    formatDate(val) {
      let { year, date } = this.$handleTimestamp(val);
      return `${year}-${date}`;
    },
    handleAllSelect() {
      if (this.isAllSelected) {
        this.selectedIdList = [];
      } else {
        this.selectedIdList = this.xiaohaoList.map(item => item.id);
      }
    },
    clickEdit(isEditing) {
      if (!this.xiaohaoList.length) {
        return false;
      }
      this.isEditing = isEditing;
      this.selectedIdList = [];
    },
    handleEdit(isEditing) {
      this.isEditing = isEditing;
      this.selectedIdList = [];
    },
    handleSelect(data, e) {
      if (!this.isEditing) {
        return false;
      } else {
        e.stopPropagation();
      }
      if (this.selectedIn(data)) {
        this.selectedIdList = this.selectedIdList.filter(item => {
          return item != data.id;
        });
      } else {
        this.selectedIdList.push(data.id);
      }
    },
    async handleDelete() {
      if (!this.selectedIdList.length) {
        this.$toast(this.$t('请选中要删除的游戏'));
        return false;
      }
      let selectedStr = this.selectedIdList.join(',');
      await this.deleteItem(selectedStr);
      this.$parent.isEditing = false;
      this.isEditing = false;
    },
    async deleteItem(id) {
      await ApiResourceCollect({
        classId: 5,
        sourceId: id,
        status: -1,
      });
      await this.getMyxiaohao();
    },
    async getMyxiaohao(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiResourceCollection({
        page: this.page,
        listRows: this.listRows,
        classId: 5,
        order: 3,
      });
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.xiaohaoList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getMyxiaohao();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getMyxiaohao(2);

      this.loadingObj.loading = false;
    },
    selectedIn(data) {
      let index = this.selectedIdList.findIndex(item => {
        return data.id == item;
      });
      return index == -1 ? false : true;
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-collection-page {
  position: fixed;
  width: 100%;
  max-width: 450px;
  height: calc(100vh - 50 * @rem - 44 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - 44 * @rem - @safeAreaTopEnv);
  top: calc(50 * @rem + 44 * @rem + @safeAreaTop);
  top: calc(50 * @rem + 44 * @rem + @safeAreaTopEnv);
  overflow-y: auto;
  .collection-list {
    box-sizing: border-box;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    &.pb {
      padding-bottom: calc(55 * @rem + @safeAreaBottom);
      padding-bottom: calc(55 * @rem + @safeAreaBottomEnv);
    }
    .xiaohao-list {
      // .xiaohao-item {
      //   padding: 0 13 * @rem;
      //   display: flex;
      //   .select-btn {
      //     display: flex;
      //     width: 34 * @rem;
      //     background: url(~@/assets/images/collection/select-icon.png) left
      //       center no-repeat;
      //     background-size: 20 * @rem 20 * @rem;
      //     &.selected {
      //       background-image: url(~@/assets/images/collection/selected-icon.png);
      //     }
      //   }
      //   .xiaohao-content {
      //     flex: 1;
      //     min-width: 0;
      //     .buy-item {
      //       display: flex;
      //       padding: 20 * @rem 0 15 * @rem;
      //       border-bottom: 1px solid #eeeeee;
      //       .left {
      //         flex: 1;
      //         min-width: 0;
      //         .info {
      //           display: flex;
      //           .pic {
      //             width: 70 * @rem;
      //             height: 70 * @rem;
      //             border-radius: 10 * @rem;
      //             background-color: #dcdcdc;
      //             overflow: hidden;
      //             img {
      //               object-fit: cover;
      //             }
      //           }
      //           .game-info {
      //             flex: 1;
      //             min-width: 0;
      //             margin-left: 14 * @rem;
      //             display: flex;
      //             flex-direction: column;
      //             justify-content: center;
      //             .title {
      //               font-size: 16 * @rem;
      //               color: #000000;
      //               // font-weight: bold;
      //               white-space: nowrap;
      //               overflow: hidden;
      //               text-overflow: ellipsis;
      //             }
      //             .game-name {
      //               font-size: 12 * @rem;
      //               color: #ff8c05;
      //               margin-top: 6 * @rem;
      //               white-space: nowrap;
      //               overflow: hidden;
      //               text-overflow: ellipsis;
      //             }
      //             .game-port {
      //               font-size: 12 * @rem;
      //               color: #666666;
      //               margin-top: 8 * @rem;
      //               white-space: nowrap;
      //               overflow: hidden;
      //               text-overflow: ellipsis;
      //             }
      //           }
      //         }
      //         .desc {
      //           font-size: 12 * @rem;
      //           color: #999999;
      //           margin-top: 10 * @rem;
      //           white-space: nowrap;
      //           overflow: hidden;
      //           text-overflow: ellipsis;
      //           span {
      //             color: #ff8c05;
      //           }
      //         }
      //       }
      //       .right {
      //         width: 90 * @rem;
      //         display: flex;
      //         flex-direction: column;
      //         align-items: flex-end;
      //         .price {
      //           font-size: 15 * @rem;
      //           color: #ee1d44;
      //           font-weight: bold;
      //           margin-top: 15 * @rem;
      //         }
      //         .gold {
      //           font-size: 10 * @rem;
      //           color: #ff395e;
      //           background-color: #ff395d10;
      //           border-radius: 3 * @rem;
      //           height: 15 * @rem;
      //           padding: 0 4 * @rem;
      //           display: flex;
      //           align-items: center;
      //           margin-top: 5 * @rem;
      //         }
      //         .platform {
      //           display: flex;
      //           align-items: center;
      //           margin-top: 20 * @rem;
      //           .plat-icon {
      //             width: 17 * @rem;
      //             height: 17 * @rem;
      //             margin-left: 5 * @rem;
      //           }
      //         }
      //       }
      //     }
      //   }
      // }
      .deal-item-component {
        padding: 14 * @rem 18 * @rem;
        position: relative;
        .deal-info {
          display: flex;
          align-items: center;
          .deal-banner {
            width: 100 * @rem;
            height: 70 * @rem;
            border-radius: 8 * @rem;
            overflow: hidden;
            background: #e3e5e8;
            &.game-icon {
              width: 70 * @rem;
              height: 70 * @rem;
            }
            img {
              object-fit: cover;
            }
          }
          .right-info {
            flex: 1;
            min-width: 0;
            height: 70 * @rem;
            .center-info {
              margin-left: 12 * @rem;
              flex: 1;
              min-width: 0;

              .title {
                font-size: 14 * @rem;
                color: #30343b;
                font-weight: 600;
                line-height: 18 * @rem;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              .game-info {
                margin-top: 14 * @rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .game-item {
                  display: flex;
                  align-items: center;
                  .game-name {
                    max-width: 70 * @rem;
                    font-weight: 400;
                    font-size: 12 * @rem;
                    color: #60666c;
                    line-height: 15 * @rem;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                  .game-area {
                    font-size: 12 * @rem;
                    color: #2bbe88;
                    font-weight: 400;
                    line-height: 15 * @rem;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                  .platform {
                    margin-left: 6 * @rem;
                    display: flex;
                    align-items: center;
                    .plat-icon {
                      width: 16 * @rem;
                      height: 16 * @rem;
                      border-radius: 6 * @rem;
                      overflow: hidden;
                      &:not(:first-of-type) {
                        margin-left: 6 * @rem;
                      }
                    }
                  }
                }
                .game-money {
                  font-weight: 400;
                  font-size: 12 * @rem;
                  color: #fe6600;
                  white-space: nowrap;
                }
              }

              .bottom-section {
                display: flex;
                align-items: center;
                height: 25 * @rem;
                line-height: 25 * @rem;
                .date {
                  padding-top: 5 * @rem;
                  font-size: 11 * @rem;
                  color: #93999f;
                  font-weight: 400;
                  flex: 1;
                  min-width: 0;
                  white-space: nowrap;
                }
                .gold {
                  font-size: 10 * @rem;
                  color: #a02d2d;
                  background: linear-gradient(90deg, #ffeded 0%, #fff4de 100%);
                  border-radius: 4 * @rem;
                  height: 22 * @rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0 5 * @rem;
                  margin-left: 8 * @rem;
                  &.gold-vip {
                    padding-left: 20 * @rem;
                    position: relative;
                    &::before {
                      content: '';
                      display: block;
                      width: 31 * @rem;
                      height: 24 * @rem;
                      .image-bg('~@/assets/images/deal-vip-icon.png');
                      position: absolute;
                      left: -14 * @rem;
                      top: 50%;
                      transform: translateY(-50%);
                    }
                  }
                }
                .appoint-tag {
                  display: flex;
                  align-items: center;
                  height: 22 * @rem;
                  background-color: #ffefd8;
                  border-radius: 9 * @rem;
                  padding: 0 5 * @rem;
                  .appoint-icon {
                    width: 12 * @rem;
                    height: 12 * @rem;
                    .image-bg('~@/assets/images/deal/ic_finger.png');
                    margin-right: 2 * @rem;
                  }
                  .appoint-text {
                    font-size: 10 * @rem;
                    color: #f95725;
                  }
                }
                .price {
                  height: 25 * @rem;
                  line-height: 25 * @rem;
                  width: 60 * @rem;
                  font-size: 12 * @rem;
                  color: #2bbe88;
                  font-weight: 600;
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                  span {
                    font-size: 20 * @rem;
                    color: #2bbe88;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
      .delete-btn {
        width: 50 * @rem;
        height: 100%;
        background-color: #f94a42;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;
      }
    }
  }
  .bottom-bar {
    border-top: 1px solid #eeeeee;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    z-index: 20000;
    .bottom-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 55 * @rem;
      padding: 0 14 * @rem;
      .all-select {
        display: flex;
        align-items: center;
        height: 100%;
        .select-icon {
          width: 20 * @rem;
          height: 20 * @rem;
          background: url(~@/assets/images/collection/select-icon.png) center
            center no-repeat;
          background-size: 20 * @rem 20 * @rem;
          &.selected {
            background-image: url(~@/assets/images/collection/selected-icon.png);
          }
        }
        .all-select-text {
          font-size: 15 * @rem;
          color: #666666;
          margin-left: 7 * @rem;
        }
      }
      .all-delete-btn {
        width: 65 * @rem;
        height: 30 * @rem;
        background-color: #f94a42;
        border-radius: 5 * @rem;
        font-size: 15 * @rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
