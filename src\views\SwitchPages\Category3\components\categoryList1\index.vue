<template>
  <div class="category-list">
    <div class="container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
        <content-empty v-if="empty"></content-empty>
        <van-list
          v-else
          v-model="loading"
          :finished="finished"
          :finished-text="$t('没有更多了')"
          @load="loadMore()"
        >
          <div
            v-for="(item, index) in game_list"
            :key="index"
            class="item-container"
            :class="`item-${index + 1}`"
          >
            <div class="item-content">
              <div class="game-index">{{ index + 1 }}</div>
              <game-item-2
                :gameInfo="item"
                class="game-item"
                :showRight="false"
              ></game-item-2>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import { ApiGameRecommend } from '@/api/views/game.js';
export default {
  props: {},
  data() {
    return {
      game_list: [],
      finished: false,
      loading: false,
      reloading: false,
      refreshing: false,
      page: 1,
      empty: false,
    };
  },
  methods: {
    async getGameList() {
      const res = await ApiGameRecommend({ listRows: 10, page: this.page });
      if (this.page === 1) {
        this.game_list = [];
        if (!res.data.list.length) {
          this.empty = true;
        }
      }
      this.game_list.push(...res.data.list);
      if (res.data.list.length < 10) {
        this.finished = true;
      } else {
        this.finished = false;
      }
    },
    onChange() {
      this.getGameList();
    },
    async onRefresh() {
      this.page = 1;
      await this.getGameList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getGameList();
      this.loading = false;
      this.page++;
    },
  },
};
</script>
<style lang="less" scoped>
.category-list {
  display: flex;
  height: calc(100vh - 105 * @rem - @safeAreaTop - @safeAreaBottom);
  height: calc(100vh - 105 * @rem - @safeAreaTopEnv - @safeAreaBottomEnv);
}
.container {
  display: flex;
  flex-flow: column;
  flex: 1;
  overflow: hidden;
  /deep/ .load-more {
    overflow-y: scroll;
  }
}
.list {
  flex: 1;
  overflow-y: scroll;
  // -webkit-overflow-scrolling: touch;
  ::-webkit-scrollbar {
    display: none;
  }
}
.item-container {
  margin: 10 * @rem 5 * @rem 10 * @rem 21 * @rem;
  &.item-1 {
    background: linear-gradient(270deg, #ff8973 0%, #ff965f 100%);
    border-radius: 16 * @rem;
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-1.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        .game-icon {
          box-sizing: border-box;
          border: 1 * @rem solid #fff;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
          }
          .tags {
            display: flex;
            height: 17 * @rem;
            overflow: hidden;
            flex-wrap: wrap;
            margin-top: 7 * @rem;
            .tag {
              box-sizing: border-box;
              border: 0.5 * @rem solid #fff;
              color: #fff;
              background-color: transparent;
              .tag-name {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
  &.item-2 {
    background: linear-gradient(270deg, #9aa3b9 0%, #8da9c1 100%);
    border-radius: 16 * @rem;
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-2.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          box-sizing: border-box;
          border: 1 * @rem solid #fff;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
          }
          .tags {
            display: flex;
            height: 17 * @rem;
            overflow: hidden;
            flex-wrap: wrap;
            margin-top: 7 * @rem;
            .tag {
              box-sizing: border-box;
              border: 0.5 * @rem solid #fff;
              color: #fff;
              background-color: transparent;
              .tag-name {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
  &.item-3 {
    background: linear-gradient(270deg, #d3ae8b 0%, #e6cfb5 100%);
    border-radius: 16 * @rem;
    .item-content {
      .game-index {
        .image-bg('~@/assets/images/category/index-3.png');
        color: transparent;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          box-sizing: border-box;
          border: 1 * @rem solid #fff;
          img {
            border-radius: 14 * @rem;
          }
        }
        .game-info {
          height: unset;
          .game-name {
            color: #fff;
          }
          .game-bottom {
            color: #fff;
            .server-date {
              color: #fff;
            }
            .types {
              .type {
                &:not(:first-child) {
                  &:before {
                    content: '';
                    background-color: #fff;
                  }
                }
              }
            }
          }
          .tags {
            display: flex;
            height: 17 * @rem;
            overflow: hidden;
            flex-wrap: wrap;
            margin-top: 7 * @rem;
            .tag {
              box-sizing: border-box;
              border: 0.5 * @rem solid #fff;
              color: #fff;
              background-color: transparent;
              .tag-name {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
  .item-content {
    display: flex;
    align-items: center;
    margin-left: -16 * @rem;
    padding-right: 5 * @rem;
    .game-index {
      width: 32 * @rem;
      height: 32 * @rem;
      font-size: 12 * @rem;
      color: #797979;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    /deep/ .game-item-components {
      padding: 8 * @rem 0;
      .game-icon {
        width: 64 * @rem;
        height: 64 * @rem;
        flex: 0 0 64 * @rem;
      }
      .game-info {
        height: unset;
        .game-name {
          font-size: 14 * @rem;
          font-weight: bold;
          line-height: 20 * @rem;
        }
        .game-bottom {
          font-size: 11 * @rem;
          margin-top: 5 * @rem;
          line-height: 16 * @rem;
        }
        .tags {
          margin-top: 5 * @rem;
        }
      }
    }
  }
}
</style>
