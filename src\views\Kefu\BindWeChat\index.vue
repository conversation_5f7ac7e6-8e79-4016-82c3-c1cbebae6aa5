<template>
  <div class="bind-wechat-page">
    <nav-bar-2
      :border="false"
      title="设置微信提醒"
      :bgStyle="bgStyle"
      :azShow="true"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bg"></div>
      <div class="explain-container">
        <div class="content">
          首次关注和绑定微信公众号最高可领200金币，在新手任务领取。<br />
          设置微信微信提醒后，你预约游戏的上线，开服信息，
          以及其他重要信息，都会在微信通知你，让你不再错 过任何有用信息~
        </div>
        <div class="tips">注：只有关注了公众号才能收到提醒消息哦~</div>
      </div>

      <component :is="componentName"></component>

      <div class="page-bottom">
        <div class="ewm">
          <img src="@/assets/images/kefu/qr-code-new.jpg" alt="" />
        </div>
        <div class="right-content">
          <div class="gzh-name">公众号: 3733游戏</div>
          <div class="btn gzh-btn" @click="copy">复制公众号并关注</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_openWx, platform } from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'BindWeChat',
  components: {
    Web: () => import(/* webpackChunkName: "component" */ './web.vue'),
    Az: () => import(/* webpackChunkName: "component" */ './az.vue'),
  },
  data() {
    return {
      bgStyle: 'transparent',
      navbarOpacity: 0,

      text: '3733游戏',
    };
  },
  computed: {
    componentName() {
      return ['android', 'android_box'].includes(platform) &&
        this.initData.share_info?.length
        ? 'Az'
        : 'Web';
    },
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  created() {
    if (platform == 'android') {
      document.title = '设置微信提醒';
    }
    window.addEventListener('scroll', this.handleScroll);
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async onResume() {},
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    // 去微信
    bindWX() {
      BOX_openWx();
    },
    // 关注微信
    attentionWX() {
      // 跳转到微信
      this.$copyText(this.text).then(() => {
        this.$toast('复制成功');
        setTimeout(() => {
          BOX_openWx();
        }, 1000);
      });
    },
    copy() {
      this.$copyText(this.text).then(() => {
        this.$toast('复制成功');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.bind-wechat-page {
  background-color: #fff;
  .main {
    padding-bottom: 30 * @rem;
    .top-bg {
      width: 100%;
      height: 180 * @rem;
      background: linear-gradient(180deg, #4ad950 0%, #ffffff 100%);
    }
    .explain-container {
      box-sizing: border-box;
      width: 335 * @rem;
      height: 123 * @rem;
      margin: -82 * @rem auto 0;
      background-color: #fff;
      box-shadow: 0px 0px 10px 0px rgba(0, 102, 255, 0.06);
      border-radius: 14 * @rem;
      padding: 13 * @rem 12 * @rem;
      .content {
        font-size: 13 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        text-align: left;
      }
      .tips {
        font-size: 12 * @rem;
        margin-top: 10 * @rem;
        line-height: 15 * @rem;
        color: #32b768;
      }
    }

    .step-container {
      box-sizing: border-box;
      padding: 28 * @rem 0 40 * @rem;
      margin: 0 25 * @rem;
      border-bottom: 1 * @rem dashed #32b768;
      .title {
        font-size: 14 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        font-weight: 600;
        span {
          font-weight: 600;
          color: #32b768;
        }
      }
      .step-list {
        .step-item {
          position: relative;
          display: flex;
          margin-top: 29 * @rem;
          &:not(:last-child) {
            &::before {
              content: '';
              position: absolute;
              left: 10 * @rem;
              top: 28 * @rem;
              width: 0;
              height: calc(100% - 8 * @rem);
              border-left: 1 * @rem dashed #32b768;
            }
          }
          .num {
            width: 20 * @rem;
            height: 20 * @rem;
            background-size: 20 * @rem 20 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.num1 {
              &.on {
                background-image: url(~@/assets/images/step-num-1-on.png);
              }
            }
            &.num2 {
              background-image: url(~@/assets/images/step-num-2.png);
              &.on {
                background-image: url(~@/assets/images/step-num-2-on.png);
              }
            }
            &.num3 {
              background-image: url(~@/assets/images/step-num-3.png);
              &.on {
                background-image: url(~@/assets/images/step-num-3-on.png);
              }
            }
            &.success {
              background-image: url(~@/assets/images/step-success.png) !important;
            }
          }
          .step-card {
            border-radius: 12 * @rem;
            background-color: #fff;
            flex: 1;
            min-width: 0;
            margin-left: 12 * @rem;
            border: 1 * @rem solid #e0f1e0;
            padding: 17 * @rem 14 * @rem;
            &.on {
              border: 1 * @rem solid #32b768;
              background-color: #f2fbf2;
            }
            .bind-wechat {
              display: flex;
              align-items: center;
              .bind-title {
                flex: 1;
                min-width: 0;
                font-size: 14 * @rem;
                color: #333333;
                font-weight: 600;
                line-height: 18 * @rem;
              }
              .bind-btn {
                width: 64 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                background: #32b768;
                display: flex;
                line-height: 24 * @rem;
                justify-content: center;
                font-size: 11 * @rem;
                color: #ffffff;
                font-weight: 600;
              }
            }
            .bind-question {
              font-size: 12 * @rem;
              color: #27bb3f;
              line-height: 15 * @rem;
              margin-top: 11 * @rem;
            }

            .bind-gzh {
              display: flex;
              align-items: center;
              .bind-title {
                flex: 1;
                min-width: 0;
                font-size: 14 * @rem;
                line-height: 18 * @rem;
                color: #333333;
                font-weight: 600;
              }
              .bind-btn {
                width: 64 * @rem;
                height: 24 * @rem;
                border-radius: 12 * @rem;
                background-color: #32b768;
                display: flex;
                line-height: 24 * @rem;
                justify-content: center;
                font-size: 11 * @rem;
                color: #ffffff;
                font-weight: 600;
              }
            }
            .bind-desc {
              line-height: 25 * @rem;
              font-size: 12 * @rem;
              color: #777777;
              margin-top: 7 * @rem;
            }
            .bind-info {
              display: flex;
              align-items: center;
              margin-left: 4 * @rem;
              margin-top: 6 * @rem;
              .icon {
                width: 18 * @rem;
                height: 18 * @rem;
              }
              .name {
                font-size: 12 * @rem;
                color: #32b768;
                margin-left: 4 * @rem;
                line-height: 15 * @rem;
              }
            }

            .notice-title {
              font-size: 14 * @rem;
              color: #333333;
              font-weight: 600;
              line-height: 18 * @rem;
            }
            .notice-desc {
              font-size: 12 * @rem;
              line-height: 15 * @rem;
              margin-top: 11 * @rem;
              color: #777777;
            }
          }
        }
      }
    }

    .page-bottom {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 325 * @rem;
      height: 136 * @rem;
      border-radius: 12 * @rem;
      background: #23b960;
      margin: 32 * @rem auto 0;
      padding: 13 * @rem 15 * @rem;
      .ewm {
        width: 110 * @rem;
        height: 110 * @rem;
        background: #ffffff;
      }
      .right-content {
        margin-left: 18 * @rem;
        .gzh-name {
          font-size: 18 * @rem;
          color: #ffffff;
          font-weight: 600;
          line-height: 23 * @rem;
          white-space: nowrap;
        }
        .gzh-btn {
          font-size: 13 * @rem;
          color: #5bc69c;
          width: 135 * @rem;
          height: 34 * @rem;
          background: #fefefe;
          border-radius: 17 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 14 * @rem;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
