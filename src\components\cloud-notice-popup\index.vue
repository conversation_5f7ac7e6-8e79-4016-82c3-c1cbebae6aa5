<template>
  <van-dialog
    v-model="show"
    :showConfirmButton="false"
    :lockScroll="false"
    class="popup-dialog"
    :class="{ 'ad-popup-dialog': content.type == 8 }"
  >
    <div class="popup-container" v-if="content.type != 9">
      <div class="content-box">
        <div class="title">{{ content.title }}</div>
        <div class="content" v-html="content.txt1"> </div>
      </div>
      <div class="btn-box">
        <div class="confirm btn" @click="handleConfirm()">确定</div>
      </div>
    </div>
    <div class="ad-popup-container" v-else>
      <div class="ad-popup-bg" @click="toDetail()">
        <img class="ad-popup-bg-img" :src="content.ad_bg" />
        <img class="ad-popup-img-reset" :src="content.ad_image" />
      </div>
      <div @click="handleCancel()" class="close"></div>
    </div>
  </van-dialog>
</template>

<script>
import { handleActionCode } from '@/utils/actionCode.js';
import { ApiV2024IndexPopcloseLog } from '@/api/views/system.js';

export default {
  data() {
    return {
      show: false,
      content: {},
    };
  },
  methods: {
    async handleConfirm() {
      this.show = false;
      try {
        await ApiV2024IndexPopcloseLog({
          type: this.content.type,
          pop_id: this.content.pop_id,
          pop_type: this.content.pop_type,
        });
      } catch (error) {
      } finally {
        handleActionCode(this.content.action);
      }
    },
    toDetail() {
      this.show = false;
      handleActionCode(this.content.action);
      // 神策埋点
      this.$sensorsTrack('popup_exposure', {
        page_name: this.$sensorsPageGet(),
        popup_id: `${this.content.pop_id}`,
        banner_name: this.content.title || '暂无',
        banner_type: `${this.content.pop_type}` || '暂无',
        button_name: '暂无',
      });
    },
    handleCancel() {
      this.show = false;
      if (typeof this.onCancel === 'function') {
        this.onCancel();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.popup-dialog {
  z-index: 3000 !important;
  background: none;
  overflow: unset;
  .popup-container {
    height: 216 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .content-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        padding: 28 * @rem 0 0 0;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #191b1f;
      }
      .content {
        padding: 28 * @rem 0 0 0;
        width: 238 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #60666c;
        text-align: center;
        line-height: 22 * @rem;
      }
    }

    .btn-box {
      margin-top: 21 * @rem;
      border-top: 1px solid #f0f1f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48 * @rem;
      width: 100%;
      .confirm {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
    }
  }
  .ad-popup-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .ad-popup-bg {
      width: 100%;
      height: 100%;
      position: relative;
      top: 0;
      left: 0;
      .ad-popup-img-reset {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 196 * @rem;
        height: 196 * @rem;
        z-index: 1;
      }
    }

    .close {
      margin-top: 38 * @rem;
      width: 30 * @rem;
      height: 30 * @rem;
      background-image: url(~@/assets/images/activity-popup-close.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}
.ad-popup-dialog {
  width: 300 * @rem;
  height: 216 * @rem;
  background: #ffffff;
  border-radius: 16 * @rem;
  z-index: 3001 !important;
}
</style>
