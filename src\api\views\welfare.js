import {
  request
} from '../index';

/**
 * 福利中心碎片
 */
export function ApiWelfareCenter(params = {}) {
  return request('/cwb/index/welfareCenter', params);
}


/**
 * 福利中心碎片
 */
export function ApiCoinCenterWelfareCenter(params = {}) {
  return request('/v2024/coin_center/welfareCenter', params);
}

/**
 * 福利中心金刚区
 */
export function ApiCoinCenterTabAction(params = {}) {
  return request('/v2024/coin_center/tabAction', params);
}

/**
 * 碎片
 */
export function ApiIndexGetSplndexData(params = {}) {
  return request('/cwb/index/getSpIndexData', params);
}


// 648福利游戏列表
export function ApiGetGame648List(params = {}) {
  return request('/v2024/game/getGame648List', params);
}

// 648福利已领取列表
export function ApiGetMy648GameList(params = {}) {
  return request('/v2024/game/getMy648GameList', params);
}

// 领取648福利
export function ApiGetGame648(params = {}) {
  return request('/v2024/game/getGame648', params);
}
/**
 * @function 平台币兑换道具列表
 * @param {string} title 
 * @param {int} page 
 * @param {int} listRows
 */
export function ApiCarRedeemItems(params = {}) {
  return request('/api/card/redeemItems', params);
}

/**
 * @function 验证小号是否能兑换道具
 * @param {string} card_id 
 * @param {string} xh_id 
 */
export function ApiCarCheckPtbCard(params = {}) {
  return request('/api/card/checkPtbCard', params);
}

/**
 * @function 平台币兑换道具订单信息
 * @param {string} card_id 
 * @param {string} xh_id
 */
export function ApiCarPtbCardOrderInfo(params = {}) {
  return request('/api/card/ptbCardOrderInfo', params);
}

/**
 * @function 平台币兑换道具下单
 * @param {string} card_id 
 * @param {string} xh_id
 */
export function ApiCarPtbCardToPay(params = {}) {
  return request('/api/card/ptbCardToPay', params);
}

/**
 * @function 获取兑换道具礼包码
 * @param {string} order_id 订单号
 */
export function ApiCarGetOrderCardPass(params = {}) {
  return request('/api/card/getOrderCardPass', params);
}

/**
 * @function 平台币兑换道具取消订单
 * @param {string} order_id 订单号
 */
export function ApiCarGetCancelOrder(params = {}) {
  return request('/api/card/cancelOrder', params);
}

// 签到规则
export function ApiUserGetSignInDesc(params = {}) {
  return request('/api/user/getSignInDesc', params);
}


/**
 * 交易中心
 */
export function ApiV2024TransactionCenter(params = {}) {
  return request('/v2024/Transaction/center', params);
}