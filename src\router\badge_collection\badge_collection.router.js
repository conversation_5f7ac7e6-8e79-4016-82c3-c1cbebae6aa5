// 徽章模块路由
export default [
    {
        path: '/badge_collection',
        name: 'BadgeCollection',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/BadgeCollection/index.vue'
            ),
        meta: {
            requiresAuth: true,
            keepAlive: true,
            pageTitle: '成就徽章',
        },
    },
    {
        path: '/award_record',
        name: 'AwardRecord',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/BadgeCollection/AwardRecord/index.vue'
            ),
        meta: {
            requiresAuth: true,
            keepAlive: true,
            pageTitle: '获取记录',
        },
    },
    {
        path: '/badge_detail/:is_valid/:id/:type_id/:record_id',
        name: 'BadgeDetail',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/BadgeCollection/BadgeDetail/index.vue'
            ),
        meta: {
            requiresAuth: true,
            keepAlive: true,
            pageTitle: '徽章详情',
        },
    },
    {
        path: '/comment_badge_detail/:id/:mem_id/:record_id',
        name: 'CommentBadgeDetail',
        component: () =>
            import(
        /* webpackChunkName: "badge_collection" */ '@/views/BadgeCollection/CommentBadgeDetail/index.vue'
            ),
        meta: {
            requiresAuth: true,
            keepAlive: true,
            pageTitle: '评价徽章详情',
        },
    },
];
