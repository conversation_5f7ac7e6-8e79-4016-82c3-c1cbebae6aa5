<template>
  <!-- 云挂机客服弹窗 -->
  <div>
    <van-dialog
      v-model="show"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-title" v-if="title">{{ title }}</div>
      <div class="popup-desc" v-if="desc" v-html="desc"></div>

      <div class="operation-bar">
        <div
          class="btn operation-btn cancel"
          @click="onCancel"
          v-if="showCancel"
        >
          {{ cancelText }}
        </div>
        <div
          class="btn operation-btn confirm"
          :class="{ confirm1: isConfirm1 }"
          @click="onConfirm"
          v-if="showConfirm"
        >
          {{ confirmText }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      show: true,
      title: '提示',
      desc: '抱歉,无法安装此游戏,\n建议重新安装或联系客服',
      content: '',
      cardpass: '',
      tips: '',
      cancelText: '取消',
      confirmText: '确定',
      showCancel: false,
      showConfirm: true,
      isConfirm1: false,
    };
  },
  methods: {
    onCancel() {
      this.show = false;
    },
    onConfirm() {
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 300 * @rem;
  padding: 20 * @rem 31 * @rem;
  .popup-title {
    font-size: 16 * @rem;
    color: #333333;
    text-align: center;
    font-weight: 600;
    line-height: 40 * @rem;
    overflow: hidden;
    white-space: nowrap;
  }
  .popup-desc {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    text-align: center;
    margin-top: 10 * @rem;
  }
  .operation-bar {
    display: flex;
    align-items: center;
    margin: 29 * @rem 0 0;
    gap: 20 * @rem;
    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      font-size: 14 * @rem;
      &.confirm {
        background: linear-gradient(39deg, #46a6ff 0%, #3ac4ff 100%);
        color: #fff;
      }
      &.confirm1 {
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
        color: #fff;
      }
      &.cancel {
        background: #f5f5f5;
        color: #7d7d7d;
      }
    }
  }
}
</style>
