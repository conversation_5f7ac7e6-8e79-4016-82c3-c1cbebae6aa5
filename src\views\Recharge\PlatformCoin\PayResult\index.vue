<template>
  <rubber-band :topColor="'#FFF0D5'" :bottomColor="'#F7F8FA'">
    <div class="pay-result-page page">
      <nav-bar-2 bgStyle="transparent" :placeholder="false" :azShow="true">
        <template #left>
          <div class="back" @click="back"></div>
        </template>
        <template #right>
          <div class="complete" v-if="!navbarOpacity" @click="back">完成</div>
        </template>
      </nav-bar-2>
      <div class="main">
        <div class="pay-result-content">
          <div class="top-content">
            <div class="pay-result-icon"></div>
            <div class="pay-result-text">支付成功</div>
          </div>
          <div class="pay-result-coin">+{{ platformCoinCount }} 平台币</div>
          <div class="to-coin-detail btn" @click="toPlatformCoinDetail"
            >查看明细</div
          >
        </div>
        <div class="table-box" v-if="prizeList.length">
          <div class="table-title"></div>
          <!-- 转盘 -->
          <div class="table-container">
            <div class="coin-count">我的金币：{{ userInfo.gold }}</div>
            <div class="table-content">
              <div class="table-list">
                <div
                  class="table-item"
                  v-for="(item, index) in prizeList"
                  :key="index"
                  :class="{
                    active: current == index + 1,
                  }"
                >
                  <img class="reward-icon" :src="item.icon" alt="" />
                  <div class="reward-text">{{ item.title }}</div>
                </div>
                <div
                  class="table-item turn-btn btn"
                  v-if="freeNum"
                  @click="handleRaffle(0)"
                >
                  <div class="turn-btn-text free" v-if="freeNum">
                    (剩余{{ freeNum }}次)
                  </div>
                </div>
                <div
                  class="table-item turn-btn btn"
                  v-else
                  @click="handleRaffle(1)"
                >
                  <div class="turn-btn-text">
                    （剩余{{ goldLotteryCount }}次，<br />
                    {{ lotteryConfig.gold }}金币/次）</div
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="rule-btn" @click="ruleDialogShow = true">规则</div>
        </div>
        <div class="page-content">
          <div class="game-content">
            <div class="game-box" v-if="gameList1.length">
              <div class="box-title fanli-game-title"></div>
              <div class="game-list">
                <div
                  class="game-item"
                  v-for="(game, index) in gameList1"
                  :key="index"
                >
                  <game-item :gameInfo="game" :isShowFanli="true"></game-item>
                  <div class="fanli-content">
                    <div class="fanli-icon"></div>
                    <div class="desc">充值返利活动正在进行</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="game-box" v-if="gameList2.length">
              <div class="box-title xihuan-game-title"></div>
              <div class="game-list">
                <div
                  class="game-item"
                  v-for="(game, index) in gameList2"
                  :key="index"
                >
                  <game-item :gameInfo="game"></game-item>
                </div>
              </div>
            </div>
          </div>
          <div class="page-bottom" v-if="!isSdk">已经到底 ~ ~</div>
          <div class="page-bottom" v-else @click="moreGame()">更多游戏</div>
        </div>
      </div>
      <!-- 抽奖结果弹窗 -->
      <van-dialog
        v-model="resultDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="false"
        class="result-dialog"
      >
        <div class="result-dialog-content">
          <div class="dialog-title">{{
            result.type != 7 ? '恭喜中奖' : '谢谢参与'
          }}</div>
          <div class="result-item">
            <img class="result-icon" :src="result.icon" alt="" />
            <div class="result-name">
              {{ result.title }}
            </div>
            <div class="operation-btns">
              <div
                class="to-detail-btn btn"
                v-if="btnText"
                @click="btnClick(result.type)"
                >{{ btnText }}</div
              >
              <div class="get-btn btn" @click="resultDialogShow = false">{{
                result.type != 7 ? '开心收下' : '我知道了'
              }}</div>
            </div>
            <div class="close-btn" @click="resultDialogShow = false"></div>
          </div>
        </div>
      </van-dialog>
      <!-- 规则弹窗 -->
      <van-popup
        v-model="ruleDialogShow"
        :lock-scroll="false"
        position="bottom"
        round
        :closeOnClickOverlay="true"
        class="rule-popup"
      >
        <div class="rule-popup-content">
          <div class="rule-title"></div>
          <div class="close-btn" @click="ruleDialogShow = false"></div>
          <div class="rule-content" v-html="ruleText"> </div>
        </div>
      </van-popup>
    </div>
  </rubber-band>
</template>

<script>
import {
  ApiPtbLotteryIndex,
  ApiGetLotteryPrize,
} from '@/api/views/recharge.js';
import {
  isSdk,
  isIosSdk,
  isAndroidSdk,
  BOX_showActivityByAction,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_getAllDownloadInfo,
  platform,
} from '@/utils/box.uni.js';
import GameItem from '../../components/game-item/index.vue';
import { mapMutations } from 'vuex';
export default {
  name: 'PayResult',
  components: {
    GameItem,
  },
  data() {
    let self = this;
    return {
      isSdk,
      isIosSdk,
      isAndroidSdk,
      navbarOpacity: 0,
      current: 0,
      platformCoinCount: 0,
      orderId: 0,
      prizeList: [], // 转盘内容
      freeNum: 0, // 剩余免费次数
      timer: null, // 定时器
      lotteryConfig: {},
      resultDialogShow: false, // 抽奖结果提示窗
      result: {},
      isStarting: false,
      turnIndex: 0, // 转盘旋转角度
      animationIndex: 0, // 动画滚动索引
      goldLotteryCount: 3,
      ruleDialogShow: false, // 规则弹窗
      ruleText: '',
      gameList1: [],
      gameList2: [],
      JSBridge: {
        receiveGameDownloadProgress(data) {
          console.log(data);
          if (data) {
            self.setUserDownload(data);
          }
        },
      },
    };
  },
  async created() {
    this.orderId = this.$route.params.id;
    await this.getLotteryIndex();
  },
  async mounted() {
    window.addEventListener('scroll', this.handleScroll);
    if (platform == 'android' && !isAndroidSdk) {
      let downloadList = await BOX_getAllDownloadInfo();
      if (downloadList) {
        downloadList = JSON.parse(downloadList);
        this.setUserDownload(downloadList);
      }
      window.onResume = this.onResume;
      window.JSBridge = this.JSBridge;
    }
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    window.removeEventListener('scroll', this.handleScroll);
    window.JSBridge = null;
    next();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  computed: {
    btnText() {
      switch (Number(this.result.type)) {
        case 1:
        case 5:
          return '查看明细';
          break;
        case 2:
        case 3:
        case 4:
          return '我的代金券';
          break;
        default:
          return '';
          break;
      }
    },
  },
  methods: {
    async onResume() {
      await this.getLotteryIndex();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async getLotteryIndex() {
      const res = await ApiPtbLotteryIndex({
        orderId: this.orderId,
      });
      this.lotteryConfig = res.data.config;
      this.platformCoinCount = res.data.ptbCount;
      this.prizeList = res.data.prizeList;
      this.freeNum = res.data.limit.lottery_limit;
      this.goldLotteryCount = res.data.limit.lottery_gold_limit;
      this.gameList1 = res.data.rebate;
      this.gameList2 = res.data.likeList;
      this.ruleText = res.data.ruleText;
    },
    async handleRaffle(type) {
      if (this.isStarting) {
        this.$toast('抽奖中');
        return false;
      }
      if (!this.freeNum && !this.goldLotteryCount) {
        this.$toast('无抽奖次数');
        return false;
      }
      if (
        !this.freeNum &&
        this.goldLotteryCount &&
        this.lotteryConfig.gold > this.userInfo.gold
      ) {
        this.$toast('金币不足，无法抽奖');
        return false;
      }
      this.isStarting = true;
      this.resultDialogShow = false;
      this.current = 0;
      this.turnIndex = 0;

      try {
        const res = await ApiGetLotteryPrize({
          orderId: this.orderId,
          type,
        });
        this.result = res.data.prize;
        if (type) {
          this.goldLotteryCount = res.data.count;
        } else {
          this.freeNum = res.data.count;
        }
        await this.turning(this.result.index);
        this.current = 0;
        this.turnIndex = 0;
        this.resultDialogShow = true;
        await this.SET_USER_INFO();
      } finally {
        this.isStarting = false;
      }
    },
    turning(currentIndex) {
      return new Promise((resolve, reject) => {
        this.turnIndex = this.prizeList.findIndex(item => {
          return item.index == currentIndex;
        });
        this.turnIndex = this.turnIndex + 1 + 40;
        this.timer = setInterval(() => {
          if (this.turnIndex > 10) {
            this.currentChange();
            this.turnIndex--;
          } else {
            clearInterval(this.timer);
            this.timer = setInterval(() => {
              if (this.turnIndex > 0) {
                this.currentChange();
                this.turnIndex--;
              } else {
                clearInterval(this.timer);
                this.timer = null;
                resolve();
              }
            }, 300);
          }
        }, 100);
      });
    },
    currentChange() {
      if (this.current > 7) {
        this.current = 1;
      } else {
        this.current++;
      }
    },
    toPlatformCoinDetail() {
      if (isAndroidSdk) {
        this.toPage('PlatformCoinDetail');
      } else if (isIosSdk) {
        this.$toast('因iOS系统受限，请手动打开3733游戏盒查看');
      } else {
        BOX_openInNewWindow(
          { name: 'PlatformCoinDetail' },
          { url: `${window.location.origin}/#/platform_coin_detail` },
        );
      }
    },
    toMyCoupon() {
      if (isAndroidSdk) {
        BOX_showActivityByAction({
          action_code: 5011,
          isNeedLogin: true,
        });
      } else if (isIosSdk) {
        this.$toast('因iOS系统受限，请手动打开3733游戏盒查看');
      } else {
        BOX_showActivity({ name: 'MyCoupon' }, { page: 'wddjq' });
      }
    },
    toGoldCoinDetail() {
      if (isAndroidSdk) {
        this.toPage('GoldCoinDetail');
      } else if (isIosSdk) {
        this.$toast('因iOS系统受限，请手动打开3733游戏盒查看');
      } else {
        BOX_openInNewWindow(
          { name: 'GoldCoinDetail' },
          { url: `${window.location.origin}/#/gold_coin_detail` },
        );
      }
    },
    btnClick(type) {
      this.resultDialogShow = false;
      this.$nextTick(() => {
        switch (Number(type)) {
          case 1:
            this.toGoldCoinDetail();
            break;
          case 2:
          case 3:
          case 4:
            this.toMyCoupon();
            break;
          case 5:
            this.toPlatformCoinDetail();
            break;
          default:
            break;
        }
      });
    },
    moreGame() {
      if (this.isAndroidSdk) {
        BOX_showActivityByAction({
          action_code: 12,
          tab_id: 1001,
        });
      } else if (this.isIosSdk) {
        this.$toast('因iOS系统受限，请手动打开3733游戏盒查找对应游戏');
      }
    },
    ...mapMutations({
      setUserDownload: 'user/setUserDownload',
    }),
  },
};
</script>

<style lang="less" scoped>
.pay-result-page {
  background-color: #fff;
  .back {
    width: 18 * @rem;
    height: 18 * @rem;
    background: url(~@/assets/images/nav-bar-back-black.png) center center
      no-repeat;
    background-size: 10 * @rem 18 * @rem;
  }

  .complete {
    font-weight: 600;
    font-size: 13 * @rem;
    color: #333333;
    line-height: 16 * @rem;
    text-align: right;
  }

  .main {
    padding-top: 44 * @rem;
    padding-top: calc(44 * @rem + @safeAreaTop);
    padding-top: calc(44 * @rem + @safeAreaTopEnv);
    background: url(~@/assets/images/recharge/pay-result/page-top-bg.png)
      no-repeat top center;
    background-size: 375 * @rem auto;
    flex: 1;
    display: flex;
    flex-direction: column;

    .pay-result-content {
      .top-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 25 * @rem 16 * @rem 0;
        .pay-result-icon {
          width: 28 * @rem;
          height: 28 * @rem;
          background: url(~@/assets/images/recharge/pay-result/success-icon.png)
            no-repeat;
          background-size: 28 * @rem 28 * @rem;
          margin-right: 10 * @rem;
        }
        .pay-result-text {
          height: 30 * @rem;
          font-weight: 600;
          color: #ff8400;
          font-size: 24 * @rem;
          line-height: 30 * @rem;
        }
      }
      .pay-result-coin {
        height: 20 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #333333;
        text-align: center;
        margin-top: 11 * @rem;
      }
      .to-coin-detail {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 158 * @rem;
        height: 40 * @rem;
        background: rgba(242, 242, 242, 0.5);
        border-radius: 29 * @rem;
        margin: 26 * @rem auto 0;
        font-weight: 500;
        font-size: 14 * @rem;
        color: #403f3d;
        text-align: center;
        margin-bottom: 34 * @rem;
      }
    }
    .table-box {
      padding-top: 22 * @rem;
      padding-bottom: 70 * @rem;
      position: relative;
      background: url(~@/assets/images/recharge/pay-result/turn-table-bg.png)
        no-repeat top center;
      background-size: 100% auto;
      .table-title {
        width: 100%;
        height: 33 * @rem;
        font-weight: bold;
        font-size: 20 * @rem;
        color: #ff8400;
        line-height: 33 * @rem;
        text-align: center;
        background: url(~@/assets/images/recharge/pay-result/table-title.png)
          no-repeat center center;
        background-size: auto 33 * @rem;
        margin: 0 auto;
      }
      .table-container {
        margin-top: 32 * @rem;
        .coin-count {
          height: 15 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #c3772c;
          line-height: 15 * @rem;
          text-align: center;
        }

        .table-content {
          box-sizing: border-box;
          width: 272 * @rem;
          height: 272 * @rem;
          margin: 42 * @rem 0 0 48 * @rem;
          .table-list {
            position: relative;
            .table-item {
              width: 84 * @rem;
              height: 84 * @rem;
              background: #fef8e4;
              border-radius: 12 * @rem;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              position: absolute;
              box-sizing: border-box;
              &.active {
                background: #fff5b2;
              }
              .reward-icon {
                height: 42 * @rem;
                width: 42 * @rem;
              }
              .reward-text {
                font-size: 12 * @rem;
                line-height: 12 * @rem;
                color: #000000;
                margin-top: 2 * @rem;
                font-weight: 600;
              }
              &:nth-of-type(1) {
                left: 188 * @rem;
                top: 0;
              }
              &:nth-of-type(2) {
                left: 188 * @rem;
                top: 94 * @rem;
              }
              &:nth-of-type(3) {
                left: 188 * @rem;
                top: 188 * @rem;
              }
              &:nth-of-type(4) {
                left: 94 * @rem;
                top: 188 * @rem;
              }
              &:nth-of-type(5) {
                left: 0;
                top: 188 * @rem;
              }
              &:nth-of-type(6) {
                left: 0;
                top: 94 * @rem;
              }
              &:nth-of-type(7) {
                left: 0;
                top: 0;
              }
              &:nth-of-type(8) {
                left: 94 * @rem;
                top: 0;
              }
              &.turn-btn {
                left: 94 * @rem;
                top: 94 * @rem;
                border: none;
                width: 84 * @rem;
                height: 84 * @rem;
                background: url(~@/assets/images/recharge/pay-result/raffle-btn.png)
                  no-repeat;
                background-size: 84 * @rem 84 * @rem;
                .turn-btn-text {
                  font-weight: 400;
                  font-size: 7 * @rem;
                  color: #c3772c;
                  line-height: 10 * @rem;
                  text-align: center;
                  position: absolute;
                  top: 48 * @rem;
                  &.free {
                    font-size: 9 * @rem;
                    line-height: 20 * @rem;
                  }
                }
              }
            }
          }
        }
      }

      .rule-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80 * @rem;
        height: 30 * @rem;
        background: #fee2b6;
        border-radius: 12 * @rem;
        font-size: 12 * @rem;
        color: #c3772c;
        text-align: center;
        margin: 25 * @rem auto 0;
      }
    }
    .page-content {
      background: linear-gradient(to Bottom, #fff5e3 0%, #f7f8fa 100%);
    }
    .game-content {
      .game-box {
        width: 350 * @rem;
        margin: 0 auto 35 * @rem;
        background: url(~@/assets/images/recharge/pay-result/game-box-top-bg.png)
          no-repeat top center;
        background-size: 100% 64 * @rem;
        overflow: hidden;

        &:last-of-type {
          margin-bottom: 0;
        }

        .box-title {
          margin: 21 * @rem 20 * @rem 0;
          width: auto;
          height: 31 * @rem;
          background: url(~@/assets/images/recharge/pay-result/game-box-title1.png)
            no-repeat left center;
          background-size: auto 31 * @rem;

          &.xihuan-game-title {
            background-image: url(~@/assets/images/recharge/pay-result/game-box-title2.png);
          }
        }

        .game-list {
          background: #fffcf7;
          padding: 28 * @rem 5 * @rem 0;
          overflow: hidden;

          .game-item {
            margin-bottom: 12 * @rem;
            border-radius: 12 * @rem;
            background-color: #fff;

            /deep/ .game-item-components {
              padding: 16 * @rem 7 * @rem;
              box-sizing: border-box;
            }

            .fanli-content {
              display: flex;
              align-items: center;
              padding: 14 * @rem 7 * @rem;
              margin-top: -16 * @rem;
              .fanli-icon {
                width: 18 * @rem;
                height: 18 * @rem;
                margin-right: 4 * @rem;
                background: url(~@/assets/images/recharge/pay-result/fanli-icon.png)
                  no-repeat;
                background-size: 18 * @rem 18 * @rem;
              }
              .desc {
                flex: 1;
                min-width: 0;
                height: 17 * @rem;
                line-height: 17 * @rem;
                font-size: 12 * @rem;
                color: #93999f;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
    .page-bottom {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 12 * @rem;
      color: #999999;
      line-height: 15 * @rem;
      text-align: center;
      padding: 20 * @rem 0 31 * @rem;
    }
  }

  .result-dialog {
    overflow: unset;

    .result-dialog-content {
      position: relative;
      width: 300 * @rem;
      height: 256 * @rem;
      background: #fff
        url(~@/assets/images/recharge/pay-result/reward-popup-bg.png) no-repeat
        top center;
      background-size: 300 * @rem auto;
      border-radius: 16 * @rem;

      .dialog-title {
        height: 31 * @rem;
        font-weight: 600;
        font-size: 22 * @rem;
        color: #191b1f;
        line-height: 31 * @rem;
        text-align: center;
        padding-top: 32 * @rem;
      }

      .result-icon {
        display: block;
        width: auto;
        height: 65 * @rem;
        margin: 10 * @rem auto 0;
      }

      .result-name {
        font-weight: 500;
        font-size: 15 * @rem;
        color: #333333;
        line-height: 15 * @rem;
        text-align: center;
        margin-top: 17 * @rem;
      }

      .operation-btns {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 250 * @rem;
        margin: 24 * @rem auto 0;

        .to-detail-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 36 * @rem;
          background: #ffeadd;
          font-size: 14 * @rem;
          color: #fe6600;
          text-align: center;
          border-radius: 22 * @rem;
          margin-right: 10 * @rem;
        }
        .get-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          max-width: 238 * @rem;
          height: 36 * @rem;
          background: linear-gradient(90deg, #ff5c27 0%, #ffa045 100%);
          border-radius: 20 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          color: #ffffff;
          text-align: center;
        }
      }
      .close-btn {
        width: 28 * @rem;
        height: 28 * @rem;
        background: url(~@/assets/images/recharge/pay-result/popup-bottom-close-btn.png)
          no-repeat;
        background-size: 28 * @rem 28 * @rem;
        position: absolute;
        bottom: -42 * @rem;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .rule-popup {
    .rule-popup-content {
      background-color: #fdf8ed;
      .rule-title {
        width: 100%;
        height: 72 * @rem;
        background: url(~@/assets/images/recharge/pay-result/rule-popup-title.png)
          no-repeat center center;
        background-size: auto 32 * @rem;
      }
      .close-btn {
        width: 12 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/recharge/pay-result/popup-close-btn.png)
          no-repeat center center;
        background-size: 12 * @rem 12 * @rem;
        padding: 4 * @rem;
        position: absolute;
        top: 12 * @rem;
        right: 14 * @rem;
      }
      .rule-content {
        padding: 0 14 * @rem 50 * @rem;
        font-size: 12 * @rem;
        color: #000000;
        line-height: 24 * @rem;
        max-height: 60vh;
        overflow-y: auto;

        /deep/ table {
          width: 100%;
          margin: 20 * @rem auto;
          table-layout: fixed;
          border-collapse: separate;

          th {
            width: 50%;
            height: 40 * @rem;
            line-height: 40 * @rem;
            font-weight: 600;
            font-size: 16 * @rem;
            color: #bb5b3b;
            text-align: center;
            background-color: #ffe197;
            border-right: 0.5px solid #cabdad;
            border-bottom: 0.5px solid #cabdad;
            border-top: 0.5px solid #cabdad;
            &:first-of-type {
              border-radius: 8 * @rem 0 0 0;
              border-left: 0.5px solid #cabdad;
            }
            &:last-of-type {
              border-radius: 0 8 * @rem 0 0;
            }
          }
          tbody tr {
            background-color: #fff;

            &:nth-of-type(2n) {
              background-color: #fffaef;
            }
          }
          td {
            padding: 10 * @rem;
            font-size: 14 * @rem;
            color: #191b1f;
            line-height: 18 * @rem;
            text-align: center;
            border-right: 0.5px solid #cabdad;
            border-bottom: 0.5px solid #cabdad;
            &:first-of-type {
              border-left: 0.5px solid #cabdad;
            }
          }
        }
      }
    }
  }

  @keyframes rotate-animation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
