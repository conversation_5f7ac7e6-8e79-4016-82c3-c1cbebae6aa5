<template>
  <!-- 二维码支付弹窗 -->
  <van-popup
    v-model="ewmPopupShow"
    :close-on-click-overlay="false"
    :lock-scroll="false"
    class="ewm-pay"
  >
    <div id="qrcode" class="qrcode" ref="qrcode"></div>
    <div class="text">{{ $t('请扫描二维码支付') }}</div>
    <div @click="handleRefresh" class="button">{{ $t('我已扫完') }}</div>
  </van-popup>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import QRCode from 'qrcodejs2';
export default {
  name: 'ewmPopup',
  computed: {
    ...mapGetters({
      showEwmPopup: 'recharge/showEwmPopup',
      ewmSrc: 'recharge/ewmSrc',
    }),
    // 显示二维码弹窗
    ewmPopupShow: {
      get() {
        return this.showEwmPopup;
      },
      set(value) {
        this.setShowEwmPopup(value);
      },
    },
  },
  watch: {
    ewmSrc() {
      this.$nextTick(() => {
        this.$refs.qrcode.innerHTML = '';
        this.createQrCode();
      });
    },
  },
  methods: {
    ...mapMutations({
      setShowEwmPopup: 'recharge/setShowEwmPopup',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    createQrCode() {
      new QRCode(this.$refs.qrcode, {
        text: this.ewmSrc, // 需要转换为二维码的内容
        width: 180,
        height: 180,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    handleRefresh() {
      this.ewmPopupShow = false;
      this.$emit('success'); // 支付完成的操作
      this.SET_USER_INFO();
    },
  },
};
</script>

<style lang="less" scoped>
.ewm-pay {
  width: 230px;
  padding: 17px 0;
  border-radius: 20px;
  .qrcode {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 180px;
    height: 180px;
    margin: 0 auto;
    canvas,
    img {
      display: block;
      width: 180px;
      height: 180px;
      margin: 0 auto;
    }
  }
  .text {
    margin-top: 20px;
    font-size: 17px;
    text-align: center;
    color: #333;
  }
  .button {
    width: 180px;
    height: 44px;
    margin: 20px auto 0;
    background: @themeBg;
    font-size: 16px;
    color: #fff;
    line-height: 44px;
    text-align: center;
    border-radius: 24px;
  }
}
</style>
