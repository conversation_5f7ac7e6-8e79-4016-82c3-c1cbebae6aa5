<template>
  <div class="marquee-text" ref="marqueeContainer">
    <div class="getWidth" ref="marqueeText">{{ text }}</div>
    <div
      v-if="scroll"
      class="scroll-text"
      :style="{ animationDuration: time + 's' }"
    >
      <span>{{ text }}</span>
      <span>{{ text }}</span>
    </div>
    <template v-else>
      {{ text }}
    </template>
  </div>
</template>

<script>
export default {
  name: 'MarqueeText',
  props: {
    text: {
      type: String,
      required: true,
    },
    time: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      scroll: false,
    };
  },
  created() {
    this.$nextTick(() => {
      let width = this.$refs.marqueeText.scrollWidth;
      let maxWidth = this.$refs.marqueeContainer.clientWidth;
      this.scroll = width > maxWidth;
    });
  },
};
</script>

<style lang="less" scoped>
.marquee-text {
  display: flex;
  width: 100%;
  overflow: hidden;
  position: relative;

  .scroll-text {
    flex: 1;
    display: flex;
    animation: marquee 4s linear infinite;
    font-weight: unset;
    font-size: unset;

    span {
      display: block;
      flex-shrink: 0;
      margin-right: 30 * @rem;
      white-space: nowrap;
      font-weight: unset;
      font-size: unset;
    }
    @keyframes marquee {
      0% {
        transform: translateX(0);
      }
      20% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(-50%);
      }
    }
  }

  .getWidth {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: -1;
    white-space: nowrap;
  }
}
</style>
