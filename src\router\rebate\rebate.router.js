export default [
  {
    path: '/rebate',
    name: 'Rebate',
    component: () => import(/* webpackChunkName: "rebate" */ '@/views/Rebate'),
    meta: {
      requiresAuth: true,
      pageTitle: '返利申请'
    },
  },
  {
    path: '/rebate/:game_id/first',
    name: 'RebateFirst',
    component: () =>
      import(/* webpackChunkName: "rebate" */ '@/views/Rebate/First'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/rebate/:game_id/second',
    name: 'RebateSecond',
    component: () =>
      import(/* webpackChunkName: "rebate" */ '@/views/Rebate/Second'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/rebate/gamename',
    name: 'RebateGamename',
    component: () =>
      import(/* webpackChunkName: "rebate" */ '@/views/Rebate/Gamename'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/rebate/select_xh/:game_id',
    name: 'RebateSelectXH',
    component: () =>
      import(/* webpackChunkName: "rebate" */ '@/views/Rebate/SelectXH'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/rebate/detail',
    name: 'RebateDetail',
    component: () =>
      import(/* webpackChunkName: "rebate" */ '@/views/Rebate/Detail'),
    meta: {
      requiresAuth: true,
    },
  },
];
