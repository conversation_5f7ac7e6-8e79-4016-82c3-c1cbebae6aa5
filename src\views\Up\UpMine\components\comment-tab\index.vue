<template>
  <div class="comment-tab">
    <template v-if="empty || is_hide">
      <div v-if="is_hide && self" class="empty">
        <div class="text">当前动态已设置为隐藏</div>
        <div class="text">可前往 【 我的 - 设置 - 主页动态 】开启展示</div>
      </div>
      <div v-if="is_hide && !self" class="empty">
        <div class="icon"></div>
        <div class="text">用户设置了动态隐藏您暂时无法查看哦~</div>
      </div>
      <div v-if="!is_hide" class="empty">
        {{ self ? '您暂无任何动态' : '该用户暂无任何动态' }}
      </div>
    </template>
    <template v-else>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="list">
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="$t('没有更多了')"
          :offset="50"
          @load="loadMore()"
          class="list"
        >
          <div v-for="(item, index) in list" :key="index" class="item">
            <div class="comment-content">{{ item.content }}</div>
            <div
              @click="toPage({ gameInfo: item, id: item.game_id })"
              class="game-info"
            >
              <img :src="item.titlepic" class="game-img" />
              <div class="title">{{ item.game_title }}</div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </template>
  </div>
</template>
<script>
import { ApiUserGetUpComment } from '@/api/views/users';

export default {
  props: {
    mem_id: {
      type: Number,
      default: 0,
    },
    self: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      is_hide: 0, //是否隐藏动态
      page: 1, //up动态的页码
      list: [], //动态列表
      refreshing: false, //是否刷新中
      loading: false, //是否加载更多中
      finished: false, //防抖
      empty: false, //是否为空
    };
  },
  methods: {
    async getList() {
      try {
        const res = await ApiUserGetUpComment({
          memId: this.mem_id,
          page: this.page,
        });
        this.is_hide = res.data.is_hide;
        if (this.page === 1) this.list = [];
        let list = res.data.comment_list || [];
        this.list.push(...list);
        if (!this.list.length) {
          this.empty = true;
        }
        if (list.length < 10) {
          this.finished = true;
        } else {
          this.finished = false;
        }
      } finally {
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.refreshing = false;
    },
    async loadMore() {
      await this.getList();
      this.loading = false;
      this.page++;
    },
    toPage(params) {
      if (params.gameInfo.classid == 40) {
        this.$router.push({ name: 'UpDetail', params: params });
      } else {
        this.$router.push({ name: 'GameDetail', params: params });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.comment-tab {
  display: flex;
  flex-direction: column;
  flex: 1 0;
  /deep/ .van-loading {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .empty {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    line-height: 21 * @rem;
    color: #999999;
    .icon {
      width: 100 * @rem;
      height: 100 * @rem;
      .image-bg('~@/assets/images/up/empty.png');
      margin-top: -60 * @rem;
      margin-bottom: 23 * @rem;
    }
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    line-height: 21 * @rem;
    color: #999999;
  }
  .list {
    flex: 1 0;
    margin-top: 28 * @rem;
    .item {
      margin: 0 60 * @rem 0 26 * @rem;
      padding-bottom: 32 * @rem;
      border-left: 1 * @rem solid #e9edf2;
      padding-left: 22 * @rem;
      .comment-content {
        font-size: 14 * @rem;
        color: #999999;
        line-height: 21 * @rem;
      }
      .game-info {
        position: relative;
        display: flex;
        align-items: center;
        margin-top: 8 * @rem;
        &::before {
          content: '';
          position: absolute;
          top: -10 * @rem;
          left: -27 * @rem;
          width: 10 * @rem;
          height: 10 * @rem;
          background: #e9edf2;
          border-radius: 50%;
        }
        .game-img {
          width: 36 * @rem;
          height: 36 * @rem;
        }
        .title {
          flex: 1;
          font-size: 14 * @rem;
          font-family:
            PingFang SC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #333333;
          margin-left: 8 * @rem;
        }
      }
    }
  }
}
</style>
