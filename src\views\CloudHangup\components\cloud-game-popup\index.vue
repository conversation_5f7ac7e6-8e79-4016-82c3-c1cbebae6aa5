<template>
  <van-popup
    v-model="popupShow"
    :lock-scroll="false"
    position="bottom"
    :style="{ height: '60%' }"
    :safe-area-inset-bottom="true"
    class="vant-more-game-popup"
  >
    <div class="game-list-title">
      <div class="left-back" v-if="isGameRecordShow" @click="leftBackMyOnHook">
        <img src="@/assets/images/cloudHangup/left-back.png" alt="" />
      </div>
      <div class="title-text" v-if="isGameRecordShow">
        {{ $t('我的挂机记录') }}
      </div>
      <div class="title-text" v-else>{{ $t('选择游戏') }}</div>
      <div class="title-close-btn" @click.stop="popupShow = false">
        <img src="@/assets/images/cloudHangup/xx-logo.png" alt="" />
      </div>
    </div>
    <div
      class="onHook-Record"
      v-if="!isGameRecordShow"
      @click="onClickOnHookRecord"
    >
      <div class="onHook-Record-left">
        <div>
          <img
            src="@/assets/images/cloudHangup/onHook-Record-logo.png"
            alt=""
          />
        </div>
        <span>{{ $t('我的挂机记录') }}</span>
      </div>
      <div class="onHook-Record-right">
        <img src="@/assets/images/cloudHangup/onHook-Record-right.png" alt="" />
      </div>
    </div>
    <div v-if="isGameRecordShow" class="game-list-tips">
      <span>为了更好的游戏体验,建议您定期移除长期不登录的游戏</span>
    </div>
    <div class="game-list-search">
      <div class="search-bar">
        <div class="search-icon"></div>
        <div class="search-input">
          <form @submit.prevent="submitSearch(keyword)">
            <input
              v-model.trim="keyword"
              type="text"
              :placeholder="$t('请输入游戏名')"
              :maxlength="15"
            />
          </form>
        </div>
        <div
          class="input-clear"
          v-if="inputClearShow"
          @click="clearInput"
        ></div>
        <div class="search-btn">{{ $t('搜索') }}</div>
      </div>
    </div>
    <div class="game-list-container">
      <content-empty
        v-if="empty"
        :emptyImg="isGameRecordShow ? onOnHookEmpty : emptyImg"
        :tips="
          isGameRecordShow
            ? $t('您暂时没有挂机记录哦~')
            : $t('咦，什么都没找到哦~')
        "
      ></content-empty>
      <yy-list
        v-else
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh()"
        @loadMore="loadMore()"
      >
        <div class="game-list">
          <!-- <game-item-info
            v-for="item in gameListInfo"
            :key="item.id"
            :itemInfo="item"
            @send-data="receiveDataFromGrandchild"
            @isShowClose="isClose"
          ></game-item-info> -->
          <cloud-game-item
            :isGameRecordShow="isGameRecordShow"
            v-for="item in gameListInfo"
            :key="item.id"
            :gameInfo="item"
            @send-removedData="removedInsGame"
            @send-data="receiveDataFromGrandchild"
            @isShowClose="isClose"
          ></cloud-game-item>
        </div>
      </yy-list>
    </div>
  </van-popup>
</template>

<script>
import CloudGameItem from '../cloud-game-item/index.vue';
import { ApiCloudChooseGame } from '@/api/views/upCloud.js';
function debounce(fn, delay) {
  let timer = null;
  return function (value) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.call(this, value);
    }, delay);
  };
}
export default {
  name: 'cloud-game-popup',
  components: {
    CloudGameItem,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    equip_id: {
      type: Number,
      default: 0,
    },
    updateTrigger: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isResult: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      timer: null,
      gameListInfo: [],
      keyword: '',
      page: 1,
      list_rows: 10,
      isGameRecordShow: false, //是否显示游戏记录
      emptyImg: require('@/assets/images/empty.png'),
      onOnHookEmpty: require('@/assets/images/cloudHangup/no-onhook-empty.png'),
      isFetching: false,
    };
  },
  created() {
    this.getCardGameList(this.keyword, 1);
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    inputClearShow() {
      return this.keyword.length ? true : false;
    },
  },
  methods: {
    clearInput() {
      this.keyword = '';
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
    },
    async searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      // this.gameListInfo = []
      // await this.getGameList()
    },
    async getCardGameList(keyword, action = 1) {
      if (this.isFetching) return;
      this.isFetching = true;
      keyword = keyword.trim();
      // if (!keyword) {
      //   this.$toast(this.$t("请输入搜索词"))
      //   return
      // }
      // this.isResult = true;
      // this.empty = false;
      // this.finished = false;
      this.loadingObj.loading = true;

      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      try {
        const res = await ApiCloudChooseGame({
          equip_id: this.equip_id,
          keyword,
          is_self: this.isGameRecordShow ? 1 : '',
          page: this.page,
          listRows: this.list_rows,
        });
        if (action === 1 || this.page === 1) {
          this.gameListInfo = [];
        }
        this.gameListInfo.push(...res.data.list);
        if (!this.gameListInfo.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
        if (res.data.list.length < this.list_rows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (error) {
        if (error.code == 500) {
          this.empty = true;
        }
      } finally {
        this.isFetching = false;
        this.loadingObj.loading = false;
      }
    },
    async onRefresh() {
      await this.getCardGameList(this.keyword, 1);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameListInfo.length) {
        await this.getCardGameList(this.keyword, 1);
      } else {
        await this.getCardGameList(this.keyword, 2);
      }
      this.loadingObj.loading = false;
    },
    leftBackMyOnHook() {
      this.empty = false;
      this.finished = false;
      this.gameListInfo = [];
      this.isGameRecordShow = false;
      this.getCardGameList(this.keyword, 1);
    },
    onClickOnHookRecord() {
      if (this.loadingObj.loading) return;
      this.empty = false;
      this.finished = false;
      this.gameListInfo = [];
      this.isGameRecordShow = true;
      this.getCardGameList(this.keyword, 1);
    },
    receiveDataFromGrandchild(data) {
      this.$emit('send-data', data);
    },
    removedInsGame(data) {
      this.$emit('send-removedData', data);
    },
    isClose(val) {
      this.popupShow = val;
    },
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.getCardGameList(this.keyword, 1);
      }, 500),
    },
    show: {
      immediate: true,
      handler(newValue) {
        if (newValue && !this.isFetching) {
          this.getCardGameList(this.keyword, 1)
            .then(() => {
              this.isFetching = false;
            })
            .catch(() => {
              this.isFetching = false;
            });
        }
      },
    },
    updateTrigger() {
      this.getCardGameList(this.keyword, 1);
    },
  },
};
</script>
<style lang="less" scoped>
.vant-more-game-popup {
  border-radius: 24 * @rem 24 * @rem 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  .game-list-title {
    // background: url(~@/assets/images/recharge/gold-coin-game-list.png) no-repeat
    //   0 0;
    // background-size: 375 * @rem 61 * @rem;
    width: 375 * @rem;
    height: 61 * @rem;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 18 * @rem;
    box-sizing: border-box;
    .left-back {
      width: 16 * @rem;
      height: 18 * @rem;
    }
    .title-text {
      flex: 1;
      text-align: center;
      width: 100 * @rem;
      height: 16 * @rem;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 16 * @rem;
      font-weight: bold;
    }
    .title-close-btn {
      position: absolute;
      top: 20 * @rem;
      right: 20 * @rem;
      width: 16 * @rem;
      height: 16 * @rem;
    }
  }
  .onHook-Record {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 18 * @rem;
    height: 40 * @rem;
    background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
    margin-bottom: 16 * @rem;
    box-sizing: border-box;
    .onHook-Record-left {
      display: flex;
      align-items: center;
      div {
        width: 22 * @rem;
        height: 22 * @rem;
      }
      span {
        width: 150 * @rem;
        margin-left: 8 * @rem;
        height: 14 * @rem;
        font-size: 14 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .onHook-Record-right {
      width: 81 * @rem;
      height: 40 * @rem;
    }
  }
  .game-list-tips {
    margin: 0 0 10 * @rem 0;
    font-weight: 600;
    font-size: 12 * @rem;
    color: #999999;
  }
  .game-list-search {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 18 * @rem;
    box-sizing: border-box;
    .search-bar {
      box-sizing: border-box;
      padding: 0 16 * @rem 0 16 * @rem;
      width: 100%;
      height: 33 * @rem;
      border-radius: 17 * @rem;
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      .search-icon {
        width: 17 * @rem;
        height: 17 * @rem;
        background: url(~@/assets/images/search-icon.png) center center
          no-repeat;
        background-size: 17 * @rem 17 * @rem;
      }
      .search-input {
        flex: 1;
        height: 33 * @rem;
        margin-left: 7 * @rem;
        form {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
      .input-clear {
        width: 18 * @rem;
        height: 18 * @rem;
        .image-bg('~@/assets/images/input-clear.png');
        margin: 0 12 * @rem;
      }
      .search-btn {
        height: 14 * @rem;
        font-size: 14 * @rem;
        color: #3abe69;
        line-height: 14 * @rem;
        text-align: center;
        margin-left: 5 * @rem;
        position: relative;
        font-weight: bold;
        &::before {
          content: '';
          position: absolute;
          left: -12 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 0;
          height: 18 * @rem;
          border: 1 * @rem solid rgba(125, 125, 125, 0.21);
          // background-color: #929292;
        }
      }
    }
  }
  .game-list-container {
    flex: 1;
    min-height: 0;
    margin-top: 10 * @rem;
    overflow: auto;
    width: 100%;
    .game-list {
      display: flex;
      flex-direction: column;
      padding: 10 * @rem 20 * @rem;
      box-sizing: border-box;
    }
  }
}
/* 浏览器滚动条隐藏 */
* {
  scrollbar-width: none;
}
*::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
}
* {
  -ms-overflow-style: none;
}
* {
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}
* {
  overflow: -webkit-scrollbar;
}
</style>
