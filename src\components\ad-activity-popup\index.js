import Vue from 'vue';
import adActivityPopupComponent from './index.vue';
import router from '@/router';
import store from '@/store';

const AdActivityPopupConstructor = Vue.extend(adActivityPopupComponent);
// 用于存储当前的 AdActivityPopup 实例
let currentAdActivityPopup = null;
// 用于存储待显示的弹窗队列
let popupQueue = [];

// 清除当前弹窗和队列
function clearCurrentPopups() {
    // 清空队列
    popupQueue = [];

    // 关闭当前显示的弹窗
    if (currentAdActivityPopup) {
        currentAdActivityPopup.show = false;
        currentAdActivityPopup = null;
    }
}

// 显示下一个弹窗的函数
function showNextPopup() {
    // 如果队列中还有弹窗，则显示下一个
    if (popupQueue.length > 0) {
        const nextPopupContent = popupQueue.shift();
        // 创建新的弹窗实例
        createPopup(nextPopupContent);
    } else {
        currentAdActivityPopup = null;
    }
}

// 创建弹窗的函数
function createPopup(content) {
    const adActivityPopup = new AdActivityPopupConstructor({
        router,
        store,
    });

    adActivityPopup.$mount(document.createElement('div'));
    document.body.appendChild(adActivityPopup.$el);

    adActivityPopup.$el.addEventListener(
        'animationend',
        () => {
            if (adActivityPopup.show == false) {
                adActivityPopup.$destroy();
                adActivityPopup.$el.parentNode.removeChild(adActivityPopup.$el);
            }
        },
        false,
    );

    // 设置当前的 AdActivityPopup 实例
    currentAdActivityPopup = adActivityPopup;

    adActivityPopup.show = true;
    adActivityPopup.content = content;

    // 设置关闭回调
    adActivityPopup.onCancel = () => {
        // 关闭当前弹窗 显示下一个弹窗
        adActivityPopup.show = false;
        currentAdActivityPopup = null;
        showNextPopup();
    };

    return adActivityPopup;
}

function useAdActivityPopup(options) {
    // 如果没有 content，则直接返回
    if (!options.content) {
        return false;
    }

    // 清除当前所有弹窗和队列
    clearCurrentPopups();

    // 处理传入的 content
    let contentArray = Array.isArray(options.content)
        ? options.content
        : [options.content];

    // 如果数组为空，直接返回
    if (contentArray.length === 0) {
        return false;
    }

    // 如果当前有弹窗显示，则将所有内容加入队列
    if (currentAdActivityPopup) {
        popupQueue = popupQueue.concat(contentArray);
        return false;
    }

    // 取出第一个弹窗内容，其余放入队列
    const firstContent = contentArray[0];
    popupQueue = contentArray.slice(1);

    // 显示第一个弹窗
    return new Promise((resolve, reject) => {
        createPopup(firstContent);
    });
}

export default useAdActivityPopup;
