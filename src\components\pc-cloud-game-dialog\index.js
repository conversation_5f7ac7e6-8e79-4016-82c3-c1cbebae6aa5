import Vue from 'vue';
import pcCloudGameDialog from './index.vue';
import router from '@/router';
import store from '@/store';

const PcCloudGameDialog = Vue.extend(pcCloudGameDialog);

function usePcCloudGameDialog(options) {
  return new Promise((resolve, reject) => {
    const dialog = new PcCloudGameDialog({
      router,
      store,
    });

    dialog.$mount(document.createElement('div'));
    document.body.appendChild(dialog.$el);

    dialog.$el.addEventListener(
      'animationend',
      () => {
        if (dialog.show == false) {
          dialog.$destroy();
          dialog.$el.parentNode.removeChild(dialog.$el);
        }
      },
      false,
    );

    dialog.cancelText = options.cancelText || '取消';
    dialog.confirmText = options.confirmText || '确定';
    dialog.showCancel = options.showCancel || false;
    dialog.showConfirm = options.showConfirm || true;
    if (options.onCancel) {
      dialog.onCancel = () => {
        options.onCancel();
        dialog.show = false;
      };
    }
    if (options.onConfirm) {
      dialog.onConfirm = () => {
        options.onConfirm();
        dialog.show = false;
      };
    }
  });
}

export default usePcCloudGameDialog;
