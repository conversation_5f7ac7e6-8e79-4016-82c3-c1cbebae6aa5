<template>
  <div class="role-transfer-page">
    <nav-bar-2
      bgStyle="transparent"
      title="交易中心"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg-circle"> </div>
    <div class="main" v-if="Object.keys(transactionCenterData).length">
      <div class="banner">
        <img
          v-if="transactionCenterData.top_img"
          :src="transactionCenterData.top_img"
          alt=""
        />
      </div>
      <div class="transfer-tips">{{ transactionCenterData.top_txt }}</div>
      <div class="transfer-container">
        <div
          class="transfer-item"
          v-for="(item, index) in transactionCenterData.tab_action"
          :key="index"
          @click="handleClick(item)"
        >
          <div class="left-info">
            <div class="title">{{ item.text1 }}</div>
            <div class="tips">{{ item.text2 }}</div>
          </div>
          <div class="right-icon">
            <img :src="item.icon_url" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiV2024TransactionCenter } from '@/api/views/welfare.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  data() {
    return {
      transactionCenterData: {},
    };
  },
  activated() {
    this.getTransactionCenter();
  },
  methods: {
    handleClick(item) {
      handleActionCode(item);
    },
    async getTransactionCenter() {
      const res = await ApiV2024TransactionCenter();
      this.transactionCenterData = res.data;
    },
  },
};
</script>

<style lang="less" scoped>
.role-transfer-page {
  position: relative;
  background: #beffe2;
  min-height: 100vh;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  .bg-circle {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    width: 100%;
    height: 347 * @rem;
    background: url('~@/assets/images/role-transfer/role-transfer-bg.png')
      no-repeat;
    background-size: 100% 347 * @rem;
  }
  .main {
    flex: 1;
    background: linear-gradient(180deg, #87f6c9 0%, #beffe3 55%, #beffe2 100%);
    padding-top: calc(50 * @rem + @safeAreaTop);
    padding-top: calc(50 * @rem + @safeAreaTopEnv);
    padding-bottom: 59 * @rem;
    .banner {
      margin: 0 auto;
      width: 310 * @rem;
      height: 305 * @rem;
    }
    .transfer-tips {
      text-align: center;
      margin: 0 auto;
      width: 310 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #60666c;
      margin-top: 7 * @rem;
    }
    .transfer-container {
      margin-top: 29 * @rem;
      padding: 0 12 * @rem;
      .transfer-item {
        width: 351 * @rem;
        height: 92 * @rem;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 20 * @rem;
        border: 2 * @rem solid rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16 * @rem 0 20 * @rem;
        box-sizing: border-box;
        &:not(:last-of-type) {
          margin-bottom: 12 * @rem;
        }
        .left-info {
          .title {
            font-weight: 600;
            font-size: 18 * @rem;
            color: #111f3c;
          }
          .tips {
            margin-top: 6 * @rem;
            font-size: 14 * @rem;
            color: rgba(17, 31, 60, 0.75);
          }
        }
        .right-icon {
          width: 72 * @rem;
          height: 60 * @rem;
        }
      }
    }
  }
}
</style>
