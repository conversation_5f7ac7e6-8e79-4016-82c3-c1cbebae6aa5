<template>
  <div class="game-collection-page">
    <div class="collection-list" :class="{ pb: isEditing }">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
      >
        <div class="game-list">
          <van-swipe-cell
            v-for="(item, index) in gameList"
            :key="index"
            :disabled="isEditing"
          >
            <div
              class="game-item"
              :class="{ btn: isEditing }"
              @click.capture="handleSelect(item, $event)"
            >
              <div
                class="select-btn"
                :class="{ selected: selectedIn(item) }"
                v-if="isEditing"
              ></div>
              <game-item
                class="game-item-component"
                :gameInfo="item"
                :showRight="!isEditing"
              ></game-item>
            </div>

            <template #right>
              <div class="delete-btn btn" @click="deleteItem(item.id)">
                {{ $t('删除') }}
              </div>
            </template>
          </van-swipe-cell>
        </div>
      </yy-list>
    </div>
    <div class="bottom-bar fixed-center" v-if="isEditing">
      <div class="bottom-content">
        <div class="all-select btn" @click="handleAllSelect">
          <div class="select-icon" :class="{ selected: isAllSelected }"></div>
          <div class="all-select-text">{{ $t('全选') }}</div>
        </div>
        <div class="all-delete-btn btn" @click="handleDelete">
          {{ $t('删除') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiResourceCollection, ApiResourceCollect } from '@/api/views/game.js';
import gameItem from '@/components/game-item';
export default {
  name: 'GameCollection',
  components: {
    gameItem,
  },
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      gameList: [],
      empty: false,
      isEditing: false,
      selectedIdList: [], // 已选中的数据
    };
  },
  computed: {
    isAllSelected() {
      let flag = true;
      for (let item of this.gameList) {
        if (!this.selectedIn(item)) {
          flag = false;
          break;
        }
      }
      return flag;
    },
  },
  async activated() {
    this.$parent.isEditing = this.isEditing;
    this.finished = false;
    this.loadingObj.loading = false;
    this.loadingObj.reloading = false;
    await this.getMyGames();
  },
  methods: {
    handleAllSelect() {
      if (this.isAllSelected) {
        this.selectedIdList = [];
      } else {
        this.selectedIdList = this.gameList.map(item => item.id);
      }
    },
    clickEdit(isEditing) {
      if (!this.gameList.length) {
        return false;
      }
      this.isEditing = isEditing;
      this.selectedIdList = [];
    },
    handleEdit(isEditing) {
      this.isEditing = isEditing;
      this.selectedIdList = [];
    },
    handleSelect(data, e) {
      if (!this.isEditing) {
        return false;
      } else {
        e.stopPropagation();
      }
      if (this.selectedIn(data)) {
        this.selectedIdList = this.selectedIdList.filter(item => {
          return item != data.id;
        });
      } else {
        this.selectedIdList.push(data.id);
      }
    },
    async handleDelete() {
      if (!this.selectedIdList.length) {
        this.$toast(this.$t('请选中要删除的游戏'));
        return false;
      }
      let selectedStr = this.selectedIdList.join(',');
      await this.deleteItem(selectedStr);
      this.$parent.isEditing = false;
      this.isEditing = false;
    },
    async deleteItem(id) {
      await ApiResourceCollect({
        classId: 3,
        sourceId: id,
        status: -1,
      });
      await this.getMyGames();
    },
    async getMyGames(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiResourceCollection({
        page: this.page,
        listRows: this.listRows,
        classId: 3,
        order: 3,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gameList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getMyGames();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getMyGames(2);

      this.loadingObj.loading = false;
    },
    selectedIn(data) {
      let index = this.selectedIdList.findIndex(item => {
        return data.id == item;
      });
      return index == -1 ? false : true;
    },
  },
};
</script>

<style lang="less" scoped>
.game-collection-page {
  position: fixed;
  width: 100%;
  max-width: 450px;
  height: calc(100vh - 50 * @rem - 44 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - 44 * @rem - @safeAreaTopEnv);
  top: calc(50 * @rem + 44 * @rem + @safeAreaTop);
  top: calc(50 * @rem + 44 * @rem + @safeAreaTopEnv);
  overflow-y: auto;
  .collection-list {
    box-sizing: border-box;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    &.pb {
      padding-bottom: calc(55 * @rem + @safeAreaBottom);
      padding-bottom: calc(55 * @rem + @safeAreaBottomEnv);
    }
    .game-list {
      .game-item {
        padding: 0 13 * @rem;
        display: flex;
        .select-btn {
          display: flex;
          width: 34 * @rem;
          background: url(~@/assets/images/collection/select-icon.png) left
            center no-repeat;
          background-size: 20 * @rem 20 * @rem;
          &.selected {
            background-image: url(~@/assets/images/collection/selected-icon.png);
          }
        }
        .game-item-component {
          flex: 1;
          min-width: 0;
        }
      }
      .delete-btn {
        width: 50 * @rem;
        height: 100%;
        background-color: #f94a42;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;
      }
    }
  }
  .bottom-bar {
    border-top: 1px solid #eeeeee;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    z-index: 2000;
    .bottom-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 55 * @rem;
      padding: 0 14 * @rem;
      .all-select {
        display: flex;
        align-items: center;
        height: 100%;
        .select-icon {
          width: 20 * @rem;
          height: 20 * @rem;
          background: url(~@/assets/images/collection/select-icon.png) center
            center no-repeat;
          background-size: 20 * @rem 20 * @rem;
          &.selected {
            background-image: url(~@/assets/images/collection/selected-icon.png);
          }
        }
        .all-select-text {
          font-size: 15 * @rem;
          color: #666666;
          margin-left: 7 * @rem;
        }
      }
      .all-delete-btn {
        width: 65 * @rem;
        height: 30 * @rem;
        background-color: #f94a42;
        border-radius: 5 * @rem;
        font-size: 15 * @rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
