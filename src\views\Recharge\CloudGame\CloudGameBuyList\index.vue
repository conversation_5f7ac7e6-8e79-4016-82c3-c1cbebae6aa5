<template>
  <div class="page cloud-game-list-page">
    <nav-bar-2
      :border="true"
      :title="$t('开通记录')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <template v-if="!empty">
        <yy-list
          class="yy-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="empty"
        >
          <div class="order-list">
            <div
              class="order-item"
              v-for="(item, index) in buyList"
              :key="index"
            >
              <div class="order-info">
                <div class="order-id"
                  >{{ $t('订单号') }}：{{ item.order_id }}</div
                >
                <div
                  class="order-status"
                  :class="{ active: item.status == 1 }"
                  >{{ item.status_str }}</div
                >
              </div>
              <div class="order-content">
                <div class="order-icon">
                  <img
                    src="@/assets/images/recharge/cloud-game/vip-icon.png"
                    alt=""
                    v-if="item.type == 1"
                  />
                  <img
                    src="@/assets/images/recharge/cloud-game/duration-icon.png"
                    alt=""
                    v-else
                  />
                </div>
                <div class="info">
                  <div class="title">
                    <span>{{ item.type_str.title }}</span>
                    <div class="price">{{ unit }}{{ item.amount }}</div>
                  </div>
                  <div class="desc">
                    <span>{{ item.title }}</span>
                    <div class="count">*1</div>
                  </div>
                </div>
              </div>
              <div class="date">
                <span>{{ item.create_time }}</span>
                <div class="btns">
                  <div class="detail-btn btn" @click="toDetail(item)">{{
                    $t('订单详情')
                  }}</div>
                  <div
                    class="pay-btn btn"
                    v-if="item.status == 1"
                    @click="getPayMethod(item)"
                    >{{ $t('立即支付') }}</div
                  >
                </div>
              </div>
            </div>
          </div>
        </yy-list>
      </template>
      <div class="empty-box" v-else>
        <img src="@/assets/images/recharge/cloud-game/empty-icon.png" alt="" />
        <p>{{ $t('暂无订单数据') }}</p>
      </div>
    </div>
    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(this.selectedMeal.amount)"
      :unit="unit"
    ></pay-type-popup>
  </div>
</template>
<script>
import {
  ApiCloudPayOrderList,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
export default {
  name: 'CloudGameBuyList',
  data() {
    return {
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      buyList: [],
      empty: false,
      orderType: 502,
      selectedPayType: 'wx',
      payList: [],
      selectedMeal: {},
      payPopupShow: false,
    };
  },
  computed: {
    unit() {
      return this.userInfo.is_hw ? '$' : '￥';
    },
  },
  methods: {
    async getList() {
      const res = await ApiCloudPayOrderList({
        page: this.page,
        listRows: this.listRows,
      });
      if (this.page === 1) this.buyList = [];
      this.buyList.push(...res.data.list);
      if (this.buyList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
      this.page++;
    },
    toDetail(item) {
      this.toPage('CloudGameBuyDetail', { id: item.order_id });
    },
    async getPayMethod(item) {
      this.$toast.loading();
      this.selectedMeal = item;
      this.orderType = item.order_type;
      try {
        let res = await ApiGetPaymentMethod({
          orderType: this.orderType,
        });
        this.payList = res.data;
        this.payPopupShow = true;
      } finally {
        this.$toast.clear();
      }
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    async handlePay() {
      this.payPopupShow = false;
      await ApiGetPayUrl({
        orderId: this.selectedMeal.order_id,
        orderType: this.orderType,
        payWay: this.selectedPayType,
        packageName: '',
      }).finally(() => {
        ApiGetOrderStatus({
          order_id: this.selectedMeal.order_id,
          order_type: this.orderType,
        });
        setTimeout(() => {
          this.onRefresh();
        }, 2000);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.cloud-game-list-page {
  .main {
    background-color: #f6f7f9;
    flex: 1;
    .yy-list {
      height: 100%;
    }
    .order-list {
      padding: 14 * @rem 15 * @rem;

      .order-item {
        margin-bottom: 14 * @rem;
        background-color: #fff;
        border-radius: 10 * @rem;
        padding: 15 * @rem;

        &:last-of-type {
          margin-bottom: 0;
        }

        .order-info {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .order-id {
            flex: 1;
            min-width: 0;
            margin-right: 10 * @rem;
            color: #868686;
            font-size: 12 * @rem;
            line-height: 15 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .order-status {
            font-size: 13 * @rem;
            line-height: 16 * @rem;
            color: #9a9a9a;

            &.active {
              color: #ff6649;
              font-weight: bold;
            }
          }
        }

        .order-content {
          margin: 12 * @rem 0 16 * @rem;
          display: flex;

          .order-icon {
            width: 72 * @rem;
            height: 72 * @rem;
            border-radius: 8 * @rem;
            margin-right: 10 * @rem;
          }

          .info {
            flex: 1;
            min-width: 0;
            padding-top: 8 * @rem;

            .title {
              display: flex;
              justify-content: space-between;

              span {
                flex: 1;
                min-width: 0;
                max-height: 36 * @rem;
                line-height: 18 * @rem;
                color: #222222;
                font-size: 14 * @rem;
                font-weight: bold;
                overflow: hidden;
              }

              .price {
                flex-shrink: 0;
                font-size: 18 * @rem;
                line-height: 23 * @rem;
                color: #222;
                font-weight: bold;
                margin-left: 10 * @rem;
              }
            }
            .desc {
              display: flex;
              justify-content: space-between;
              margin-top: 12 * @rem;
              font-size: 12 * @rem;
              color: #868686;
              line-height: 15 * @rem;
            }
          }
        }

        .date {
          display: flex;
          align-items: center;
          justify-content: space-between;

          span {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #9a9a9a;
            line-height: 15 * @rem;
          }

          .btns {
            display: flex;
            align-items: center;

            .detail-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 72 * @rem;
              height: 27 * @rem;
              box-sizing: border-box;
              text-align: center;
              font-size: 12 * @rem;
              color: #222;
              line-height: 25 * @rem;
              border: 1 * @rem solid #9a9a9a;
              border-radius: 24 * @rem;
              margin-left: 10 * @rem;
            }

            .pay-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 72 * @rem;
              height: 27 * @rem;
              box-sizing: border-box;
              text-align: center;
              font-size: 12 * @rem;
              color: #fff;
              background: @themeBg;
              line-height: 1;
              border-radius: 24 * @rem;
              margin-left: 10 * @rem;
            }
          }
        }
      }
    }
  }
  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    img {
      width: 128 * @rem;
      height: 128 * @rem;
    }

    p {
      width: 100%;
      height: 18 * @rem;
      font-size: 14 * @rem;
      color: #666666;
      line-height: 18 * @rem;
      text-align: center;
      margin-top: 16 * @rem;
    }
  }
}
</style>
