<template>
  <div class="kefu-qa">
    <nav-bar-2 :title="pageTitle" :border="true" :azShow="true"></nav-bar-2>
    <van-collapse v-model="activeNames" accordion>
      <van-collapse-item v-for="item in list" :key="item.id" :name="item.id">
        <template #title>
          <div class="question-title">
            <div class="qustion-icon">
              <img
                src="~@/assets/images/cloudHangup/cloud_FAQ_logo.png"
                alt=""
              />
            </div>
            {{ item.qustion }}
          </div>
        </template>
        <div v-html="item.answer"></div>
      </van-collapse-item>
    </van-collapse>
    <div class="bottom-bar">
      <div @click="connectQiYu()" class="bottom-button" v-if="!noNeedWykefu">
        {{ $t('联系客服') }}
      </div>
    </div>
  </div>
</template>
<script>
import { ApiCloudFaq } from '@/api/views/upCloud.js';
import {
  platform,
  BOX_getPackageName,
  BOX_openInNewNavWindow,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'KefuQA',
  data() {
    return {
      pageTitle: '云挂机FAQ',
      platform,
      list: [],
      activeNames: ['1'],
    };
  },
  computed: {
    ...mapGetters({
      kefuQQNumber: 'system/kefuQQNumber',
      kefuQQLink: 'system/kefuQQLink',
    }),
    noNeedWykefu() {
      return !!this.kefuQQNumber && !this.kefuQQLink;
    },
  },
  async created() {
    const res = await ApiCloudFaq();
    this.list = res.data;
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
  },
  methods: {
    connectQiYu() {
      this.openKefu();
    },
    formatAnswer(str) {
      return str.replace(/3733游戏盒子/g, this.appName);
    },
  },
};
</script>
<style lang="less" scoped>
.kefu-qa {
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: calc(80 * @rem + @safeAreaBottom);
  padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
  /deep/ .van-collapse-item__title {
    font-size: 15 * @rem;
    color: #000000;
    padding: 16 * @rem 18 * @rem;
  }
  /deep/ .van-collapse-item__title .van-cell__right-icon::before {
    line-height: 24px;
  }
  /deep/ .van-collapse-item__content {
    font-size: 14 * @rem;
    color: #757575;
  }
  .question-title {
    display: flex;
    align-items: center;

    .qustion-icon {
      width: 30 * @rem;
      height: 28 * @rem;
      margin-right: 8 * @rem;
    }
  }
}
.bottom-bar {
  width: 100%;
  height: 74 * @rem;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .bottom-button {
    width: 311 * @rem;
    height: 50 * @rem;
    background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
    border-radius: 29 * @rem;
    border-radius: 24 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
