<template>
  <div class="page">
    <nav-bar-2 title="选择游戏" :placeholder="true"></nav-bar-2>
    <div class="main">
      <div class="search-bar">
        <div class="search-box">
          <form @submit.prevent="submitSearch">
            <input
              type="text"
              class="input-text"
              v-model.trim="keyword"
              placeholder="请输入游戏关键词"
            />
          </form>
          <div class="clear-game" @click="clearKeyword" v-if="keyword"></div>
        </div>
        <div class="search-btn" @click="submitSearch">搜索</div>
      </div>
      <div class="search-index" v-if="!isResult">
        搜索好玩的游戏，加入我的游戏单
      </div>
      <template v-else>
        <content-empty
          v-if="empty"
          :tips="$t('咦，什么也都找到哦~')"
        ></content-empty>
        <yy-list
          v-else
          class="yy-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
        >
          <div class="game-list">
            <up-game-item v-for="item in gameList" :key="item.id" :info="item">
              <div
                class="add-game"
                @click.stop="addGame(item)"
                v-if="!item.selected"
              >
                添加
              </div>
              <div class="del-game" @click.stop="deleteGame(item)" v-else>
                删除
              </div>
            </up-game-item>
          </div>
        </yy-list>
      </template>
    </div>
  </div>
</template>

<script>
import { ApiGameIndexV1 } from '@/api/views/game.js';
import upGameItem from '@/components/up-game-item';
import { mapGetters, mapMutations } from 'vuex';
export default {
  components: {
    upGameItem,
  },
  data() {
    return {
      gameList: [],
      keyword: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 15,
      timer: null,
      isResult: false,
    };
  },
  computed: {
    ...mapGetters({
      selectedGameList: 'up/selectedGameList',
    }),
    empty() {
      return !this.loadingObj.loading && this.finished && !this.gameList.length;
    },
  },
  watch: {
    keyword(val) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      if (val == '') {
        this.isResult = false;
        return;
      }

      this.timer = setTimeout(async () => {
        this.finished = false;
        this.gameList = [];
        this.loadingObj.loading = true;
        this.isResult = true;
        await this.getGameList();
        this.loadingObj.loading = false;
      }, 300);
    },
  },
  async activated() {
    if (this.keyword) {
      this.loadingObj.reloading = true;
      this.gameList = [];
      await this.onRefresh();
    }
  },
  methods: {
    ...mapMutations({
      setSelectedGameList: 'up/setSelectedGameList',
    }),
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameIndexV1({
        classId: 40,
        keyword: this.keyword,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      let result = res.data.list.map(item => {
        let selected = false;
        if (this.selectedGameList.some(game => game.id == item.id)) {
          selected = true;
        }
        return {
          ...item,
          selected,
        };
      });
      this.gameList.push(...result);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },

    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },
    clearKeyword() {
      this.keyword = '';
    },
    async submitSearch() {
      if (!this.keyword) {
        this.$toast('请输入关键词');
        return false;
      }
      await this.getGameList();
    },
    addGame(item) {
      this.setSelectedGameList(item);
      item.selected = true;
    },

    deleteGame(item) {
      this.setSelectedGameList(item.id);
      item.selected = false;
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .main {
    padding-top: 50 * @rem;
  }
  .search-bar {
    box-sizing: border-box;
    position: fixed;
    .fixed-center;
    width: 100%;
    height: 50 * @rem;
    z-index: 200;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 18 * @rem;
    padding-right: 8 * @rem;
    .search-box {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      height: 34 * @rem;
      background-color: #f8f8f8;
      border-radius: 17 * @rem;
      padding: 0 10 * @rem;
      .clear-game {
        width: 14 * @rem;
        height: 14 * @rem;
        background: url('~@/assets/images/up-collection/game-clear.png') center
          center no-repeat;
        background-size: 14 * @rem 14 * @rem;
      }
      form {
        border: 0;
        outline: 0;
        display: block;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .input-text {
        padding: 0 10 * @rem;
        font-size: 15 * @rem;
        color: #333333;
        flex: 1;
        min-width: 0;
        background-color: transparent;
      }
    }
    .search-btn {
      width: 50 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 15 * @rem;
      color: #1283ff;
    }
  }
  .search-index {
    display: flex;
    justify-content: center;
    padding: 200 * @rem 0;
    font-size: 14 * @rem;
    color: #aaaaaa;
  }
  .game-list {
    padding: 0 10 * @rem;
    .add-game {
      box-sizing: border-box;
      width: 68 * @rem;
      height: 28 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: @themeColor;
      border-radius: 14 * @rem;
      border: 1px solid @themeColor;
      &::before {
        content: '';
        width: 7 * @rem;
        height: 7 * @rem;
        margin-right: 5 * @rem;
        background: url('~@/assets/images/up-collection/add-icon.png') center
          center no-repeat;
        background-size: 14 * @rem 14 * @rem;
      }
    }
    .del-game {
      width: 68 * @rem;
      height: 28 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: @themeColor;
      border-radius: 14 * @rem;
      background-color: #f5f5f6;
    }
  }
}
</style>
