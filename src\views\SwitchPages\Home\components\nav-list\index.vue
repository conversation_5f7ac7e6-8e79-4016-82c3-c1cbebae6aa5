<template>
  <!-- 金刚区 -->
  <div class="nav-list-component">
    <swiper
      id="navListSwiper"
      ref="navListSwiper"
      :options="swiperOptions"
      :auto-update="true"
      style="width: 100%; margin: 0 auto"
      v-if="navList.length > 0"
    >
      <swiper-slide
        class="nav-item btn"
        v-for="(item, index) in navList"
        :key="index"
      >
        <template>
          <yy-lottie
            class="lottie-icon"
            :width="46"
            :height="46"
            :options="{
              autoplay: true,
              loop: true,
              path: item.icon_lottie,
            }"
            v-if="item.icon_lottie"
          ></yy-lottie>
          <div class="nav-icon" :class="item.icon_url" v-else>
            <div
              class="img"
              :style="{ backgroundImage: `url(${item.icon_url})` }"
            ></div>
          </div>
          <div class="nav-name">{{ item.text1 }}</div>
        </template>
      </swiper-slide>
    </swiper>
    <div class="swiper-scrollbar"></div>
  </div>
</template>

<script>
import { PageName, handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'NavList',
  props: {
    navList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    let that = this;
    return {
      swiperOptions: {
        slidesPerView: 5,
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.swiper-scrollbar',
        },
        on: {
          init() {
            // 金刚区首次加载动画演示
            let navAnimation = localStorage.getItem('NAV_ANIMATION');
            if (!navAnimation) {
              this.detachEvents();
              that.$nextTick(() => {
                let navItemWidth = parseInt(
                  document.querySelector('body').clientWidth / 5,
                );
                let navNum = that.navList.length;
                let duration = 1000;
                if (navNum > 5) {
                  this.translateTo(
                    -navItemWidth * (navNum - 5),
                    duration,
                    true,
                    false,
                  );
                  setTimeout(() => {
                    this.slideTo(0, duration, true);
                  }, 1300);
                  setTimeout(() => {
                    this.attachEvents();

                    localStorage.setItem('NAV_ANIMATION', 1);
                  }, 2300);
                }
              });
            }
          },
          click: function () {
            setTimeout(() => {
              let nav = that.navList[this.clickedIndex];
              that.CLICK_EVENT(nav.click_id);
              that.handleActionCode(nav);
            }, 0);
          },
        },
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      // 解决金刚区点击穿透的问题
      document
        .querySelector('#navListSwiper')
        .addEventListener('touchstart', function (e) {
          e.preventDefault();
        }),
        {
          passive: false,
        };
    });
  },
  methods: {
    handleActionCode,
  },
};
</script>

<style lang="less" scoped>
.nav-list-component {
  padding: 0 12 * @rem 12 * @rem;
  width: 100%;
  .swiper-scrollbar {
    margin: 13 * @rem auto 0;
    width: 24 * @rem;
    height: 6 * @rem;
  }
  /deep/ .swiper-scrollbar-drag {
    background: @themeColor;
  }
  .nav-item {
    width: 78 * @rem;
    flex-shrink: 0;
    &:nth-of-type(1) {
      margin-left: 0;
    }
    .lottie-icon {
      width: 46 * @rem;
      height: 46 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .nav-icon {
      width: 38 * @rem;
      height: 38 * @rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      // background-position: center center;
      // background-repeat: no-repeat;
      // background-size: 50 * @rem 50 * @rem;
      margin: 0 auto;
      // &.changwan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.lingquan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.chongzhi-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.kaifu-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      img {
        width: 100%;
        height: 100%;
      }
      .img {
        width: 100%;
        height: 100%;
        background-size: 100%;
      }
    }
    .nav-name {
      text-align: center;
      font-size: 11 * @rem;
      color: #60666c;
      font-weight: 400;
      margin-top: 6 * @rem;
    }
  }
}
</style>
