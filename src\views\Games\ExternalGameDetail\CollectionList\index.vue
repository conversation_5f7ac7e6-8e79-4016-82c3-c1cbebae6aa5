<template>
  <div class="gift-package-container">
    <p class="collection-tips">和你一起玩{{ title }}的小伙伴都在玩</p>
    <yy-list
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh()"
      @loadMore="loadMore()"
      :check="false"
      :empty="empty"
      :tips="tips"
    >
      <div class="collection-list">
        <div
          class="collection-item"
          v-for="(item, index) in resultList"
          :key="index"
        >
          <collection-item :couponItem="item"></collection-item>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiCardIndex, ApiCardGet } from '@/api/views/gift.js';
import { ApiGameGetGameCollect } from '@/api/views/game.js';
import CollectionItem from '../Components/collection-itme';
export default {
  name: 'collectionList',
  components: { CollectionItem },
  props: {
    cate: {
      type: String,
      default: '94',
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      use_total: 0,
      total: 0,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无合集',
    };
  },
  async mounted() {
    await this.getMyGames();
  },
  methods: {
    async getMyGames(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiGameGetGameCollect({
        theme: this.cate,
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getMyGames();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getMyGames(2);
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.gift-package-container {
  padding: 0 18 * @rem;
  .collection-tips {
    margin-top: 8 * @rem;
    background: #f3ece9;
    font-size: 15 * @rem;
    color: #ff773d;
    text-align: center;
    border-radius: 2 * @rem;
    overflow: hidden;
  }
  .game-list-box {
    .collection-list {
      .collection-item {
        margin-bottom: 20 * @rem;
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
