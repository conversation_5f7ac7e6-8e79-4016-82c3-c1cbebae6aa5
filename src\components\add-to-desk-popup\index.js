import Vue from 'vue';
import addToDeskPopup from '@/components/add-to-desk-popup/index.vue';
import router from '@/router';
import store from '@/store';

const AddToDeskPopup = Vue.extend(addToDeskPopup);

function useAddToDeskPopup(isShow) {
  return new Promise((resolve, reject) => {
    const popupInstance = new AddToDeskPopup({
      router,
      store,
    });

    popupInstance.$mount(document.createElement('div'));
    document.body.appendChild(popupInstance.$el);

    popupInstance.$el.addEventListener(
      'animationend',
      () => {
        if (popupInstance.show == false) {
          popupInstance.$destroy();
          popupInstance.$el.parentNode.removeChild(popupInstance.$el);
        }
      },
      false,
    );

    if (isShow) {
      popupInstance.show = true;
      resolve(true);
    } else {
      popupInstance.show = false;
    }

    popupInstance.close = function () {
      resolve(true);
      popupInstance.show = false;
    };
  });
}

export default useAddToDeskPopup;
