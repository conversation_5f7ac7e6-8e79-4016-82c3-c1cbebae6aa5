<template>
  <div class="task-popup">
    <van-popup
      v-model="taskListPopupShow"
      position="bottom"
      :lock-scroll="false"
      round
      @click-overlay="closePopup"
      class="task-container-popup"
    >
      <div class="task-container">
        <div class="task-bg">
          <img src="~@/assets/images/games/task-list-bg.png" alt="" />
        </div>
        <div class="top-operation">
          <div class="top-box">
            <div class="top-title">
              <div class="title_icon"> </div>
              <div class="rule" @click="activityRulesShow = true">
                <div>活动规则</div>
                <span class="arrow">
                  <img
                    src="~@/assets/images/cloud-game/icon-rule-arrow.png"
                    alt=""
                  />
                </span>
              </div>
            </div>
            <div class="top-close" @click="closePopup">
              <img
                src="~@/assets/images/cloud-game/time-arrow-left.png"
                alt=""
              />
            </div>
          </div>
          <div class="tab-list">
            <div class="tabs">
              <div
                class="tab btn"
                v-for="(tab, index) in tabList"
                :key="index"
                @click="clickTab(index)"
              >
                <span :class="{ active: current === index }"
                  >{{ tab.title }}
                </span>
              </div>
              <div
                class="tab-line"
                :style="{ left: `${current * 93.75 * remNumberLess}rem` }"
              ></div>
            </div>
          </div>
        </div>

        <div class="task-box" :class="{ df_cc: !loadSuccess }">
          <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
            <div
              class="loading-content"
              v-if="!loadSuccess && !defaultPageShow"
            >
              <van-loading vertical>加载中...</van-loading>
            </div>
            <div class="loading-content" v-if="!loadSuccess && defaultPageShow">
              <default-error-page @callback="parentCallback" />
            </div>
            <template v-if="loadSuccess && TaskInfoList.length">
              <TaskInfoList
                @refreshDataType="refreshDataType"
                :TaskInfoList="TaskInfoList"
                v-if="this.current === 0"
              />
              <GameInfoList
                @refreshDataType="refreshDataType"
                :GameInfoList="TaskInfoList"
                v-else
              />
            </template>
          </van-pull-refresh>
          <div class="emptyData" v-if="!TaskInfoList.length && empty">
            <div class="box">
              <img
                src="~@/assets/images/cloud-game/task-empty-img.png"
                alt=""
              />
              <div class="text">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 活动规则弹窗 -->
    <van-popup
      v-model="activityRulesShow"
      :close-on-click-overlay="true"
      position="bottom"
      round
      :lock-scroll="false"
    >
      <div class="activityRules-popup">
        <div class="activityRules-top">
          <div class="title">活动规则</div>
          <div class="close" @click="activityRulesShow = false"></div>
        </div>
        <div class="rule-info" v-html="pc_task_rule"></div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import GameInfoList from './components/GameInfoList/index.vue';
import TaskInfoList from './components/TaskInfoList/index.vue';
import { ApiCloudCloudTaskGetIndex } from '@/api/views/game.js';
import emptyImg from '@/assets/images/cloud-game/task-empty-img.png';
import { mapGetters, mapMutations } from 'vuex';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'taskListPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    updateTrigger: {
      type: Number,
      default: 0,
    },
  },
  components: {
    TaskInfoList,
    GameInfoList,
  },
  data() {
    return {
      current: 0, //tabs选中
      remNumberLess, //rem大小转换
      tabList: [
        {
          title: '做任务赚',
          status: 0,
        },
        {
          title: '玩游戏赚',
          status: 1,
        },
      ],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      isLoading: false,
      page: 1,
      listRows: 10,
      type: 1, //1 任务赚，2 游戏赚
      empty: false,
      TaskInfoList: [],
      activityRulesShow: false, // 活动规则弹窗
      pc_task_rule: '', // 活动规则
      emptyImg,
      loadSuccess: false, //加载完毕
      defaultPageShow: false, //网络错误占位符
    };
  },
  computed: {
    ...mapGetters({
      hereItIosBoxTaskShow: 'game/hereItIosBoxTaskShow',
    }),
    taskListPopupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },

  watch: {
    updateTrigger() {
      this.getTaskInfoList();
    },
  },
  mounted() {
    if (this.userInfo.token) {
      this.getTaskInfoList();
    }
  },

  methods: {
    async parentCallback() {
      this.loadSuccess = false;
      this.defaultPageShow = false;
      await this.getTaskInfoList();
    },
    // 刷新数据
    async refreshDataType() {
      await this.getTaskInfoList();
    },
    clickTab(index) {
      if (this.current === index || !this.loadSuccess) return;
      this.current = index;
      this.TaskInfoList = [];
      this.loadSuccess = false;
      this.defaultPageShow = false;
      if (this.current === 0) {
        this.type = 1;
      } else {
        this.type = 2;
      }
      this.getTaskInfoList();
    },
    closePopup() {
      if (this.hereItIosBoxTaskShow && platform == 'iosBox') {
        // 通知ios马甲包关闭任务弹窗
        window.webkit.messageHandlers.getPcCloudGameInfoResult.postMessage({
          type: 'closeTaskPopup',
          isClose: true,
        });
      }
      this.taskListPopupShow = false;
      this.$emit('updateVipInfo');
      this.$nextTick(() => {
        this.setHereItIosBoxTaskShow(false);
      });
    },
    async getTaskInfoList() {
      try {
        const res = await ApiCloudCloudTaskGetIndex({
          // page: this.page,
          // listRows: this.listRows,
          type: this.type,
        });

        let { task_list, pc_task_rule } = res.data;
        if (!task_list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
        this.loadSuccess = true;
        this.pc_task_rule = pc_task_rule;
        this.TaskInfoList = task_list || [];
      } catch (error) {
        if (error.code == -2) {
          this.taskListPopupShow = false;
          this.setHereItIosBoxTaskShow(false);
        }
        if (!navigator.onLine) {
          this.loadSuccess = false;
          this.defaultPageShow = true;
        }
      } finally {
        this.$nextTick(() => {
          this.isLoading = false;
        });
      }
    },
    async onRefresh() {
      try {
        await this.getTaskInfoList();
      } finally {
        this.isLoading = false;
      }
    },
    async loadMore() {
      await this.getTaskInfoList();
    },
    ...mapMutations({
      setHereItIosBoxTaskShow: 'game/setHereItIosBoxTaskShow',
    }),
  },
};
</script>
<style lang="less" scoped>
.task-popup {
  .task-container-popup {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    .task-container {
      display: flex;
      flex-direction: column;
      max-height: 466 * @rem;
      height: 466 * @rem;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      .task-bg {
        height: 127 * @rem;
        overflow: hidden;
      }
      .top-operation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        box-sizing: border-box;

        .top-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 100%;
          padding: 20 * @rem 14 * @rem 0 18 * @rem;
          .top-title {
            display: flex;
            align-items: center;
            max-width: 200 * @rem;
            flex-shrink: 0;
            > div:first-child {
              font-size: 15 * @rem;
              font-weight: 600;
            }
            .title_icon {
              background: url('~@/assets/images/games/task-title-icon.png')
                no-repeat 0 0;
              background-size: 96 * @rem 29 * @rem;
              width: 94 * @rem;
              height: 29 * @rem;
              flex-shrink: 0;
            }
            .rule {
              height: 20 * @rem;
              line-height: 20 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #cd974a;
              margin-left: 10 * @rem;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              justify-content: center;
              .arrow {
                margin-left: 1 * @rem;
                width: 7 * @rem;
                height: 7 * @rem;
              }
            }
          }
          .top-close {
            width: 20 * @rem;
            height: 20 * @rem;
          }
        }
        .tab-list {
          margin-top: 20 * @rem;
          .tabs {
            display: flex;
            position: relative;
            align-items: center;
            width: 100%;
            .tab {
              width: 93.75 * @rem;
              height: 26 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              span {
                display: block;
                font-size: 15 * @rem;
                font-family:
                  PingFang SC-Semibold,
                  PingFang SC;
                font-weight: 400;
                color: #999999;
                position: relative;
              }
              .notice {
                position: absolute;
                left: 50%;
                top: 2 * @rem;
                transform: translateX(12 * @rem);
                height: 14 * @rem;
                line-height: 14 * @rem;
                background: #ff504f;
                color: #ffffff;
                font-size: 10 * @rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 4 * @rem;
                border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
              }
            }
            .line {
              flex: 1;
              height: 0 * @rem;
              border: 1 * @rem solid #e8e8e8;
            }
            .tab-line {
              position: absolute;
              width: 30 * @rem;
              height: 3 * @rem;
              border-radius: 9 * @rem;
              background-color: #222222;
              left: 0;
              bottom: -3 * @rem;
              transform: translateX(19.875 * @rem);
              transition: 0.3s;
            }
            .active {
              font-size: 16 * @rem!important;
              font-weight: 600 !important;
              color: #111111 !important;
            }
          }
        }
      }

      .task-box {
        height: 100%;
        overflow-y: auto;
        padding: 0 18 * @rem;
        margin-top: 12.5 * @rem;
        .emptyData {
          height: 100%;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          .box {
            display: flex;
            flex-direction: column;
            align-items: center;
            img {
              width: 128 * @rem;
              height: 128 * @rem;
            }
            .text {
              margin-top: 16 * @rem;
              height: 18 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #666666;
              line-height: 18 * @rem;
            }
          }
        }
        &.df_cc {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        .loading-content {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
      }
    }
  }
}
.activityRules-popup {
  max-height: 400 * @rem;
  display: flex;
  flex-direction: column;

  .activityRules-top {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 19 * @rem 0 0;
    margin-bottom: 24 * @rem;
    position: relative;
    .title {
      font-size: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      flex-shrink: 0;
      font-weight: 600;
      color: #111111;
    }
    .close {
      position: absolute;
      right: 17 * @rem;
      background: url('~@/assets/images/games/permission-close.png') no-repeat 0
        0;
      background-size: 14 * @rem 14 * @rem;
      width: 13 * @rem;
      height: 13 * @rem;
    }
  }
  .rule-info {
    padding: 0 18 * @rem 18 * @rem 18 * @rem;
    overflow-y: auto;
  }
}
</style>
