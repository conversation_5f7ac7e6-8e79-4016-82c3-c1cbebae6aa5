<template>
  <div class="follow-up-page page">
    <nav-bar-2 title="关注UP" :border="true"></nav-bar-2>
    <div class="tab-bar">
      <div
        class="tab-item btn"
        v-for="(tab, tabIndex) in tabList"
        :key="tabIndex"
        :class="{ on: current === tabIndex }"
        @click="tapNav(tabIndex)"
      >
        {{ tab.name }}
      </div>
      <div
        class="line"
        :style="{ left: `${current * 156 * remNumberLess}rem` }"
      ></div>
    </div>
    <div
      class="follow-up-container"
      v-for="(data, dataIndex) in dataList"
      :key="dataIndex"
      v-show="current === dataIndex"
    >
      <yy-list
        class="follow-up-content"
        v-model="data.loadingObj"
        :finished="data.finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
        :empty="data.empty"
      >
        <div class="follow-list">
          <div
            class="follow-item"
            v-for="(item, index) in data.data"
            :key="index"
          >
            <template v-if="tabList[current].type == 'up'">
              <div
                class="up-item"
                @click="toPage('UpMine', { mem_id: item.user_id })"
              >
                <div class="up-info">
                  <div class="up-avatar">
                    <user-avatar :self="false" :src="item.avatar"></user-avatar>
                  </div>
                  <div class="info-center">
                    <div class="up-nickname">{{ item.nickname }}</div>
                    <div class="num">
                      关注：{{ item.focus_count }}&emsp;粉丝：{{
                        item.fans_count
                      }}
                    </div>
                  </div>
                </div>
                <div
                  class="follow-btn btn"
                  :class="{ can: item.is_focus == 0 }"
                  @click.stop="handleFollow(item)"
                >
                  {{ item.is_focus == 0 ? '关注' : '已关注' }}
                </div>
              </div>
            </template>
            <template v-else>
              <div
                class="resource-item"
                @click="toPage('UpDetail', { id: item.id, gameInfo: item })"
              >
                <div class="resource-info">
                  <div class="resource-icon">
                    <img :src="item.titlepic" alt="" />
                  </div>
                  <div class="info-center">
                    <div class="name">{{ item.title }}</div>
                    <div class="line">
                      <div
                        class="tags"
                        v-if="item.extra_tag && item.extra_tag.length"
                      >
                        <div
                          class="tag"
                          v-for="(tag, tagIndex) in item.extra_tag"
                          :key="tagIndex"
                        >
                          {{ tag.name }}
                        </div>
                      </div>
                      <div class="size" v-if="item.size_a">
                        {{ item.size_a }}
                      </div>
                    </div>
                    <div v-if="item.up_info" class="bottom">
                      <div class="text">{{ item.up_info.nickname }}</div>
                      <UserAvatar
                        :src="item.up_info.avatar"
                        :self="false"
                        class="icon"
                      />
                    </div>
                  </div>
                </div>
                <div class="download-btn btn">下载</div>
              </div>
            </template>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { mapGetters, mapMutations } from 'vuex';
import {
  ApiUserGetFocusUp,
  ApiUserGetFocusUpGame,
  ApiUserFollowUser,
} from '@/api/views/users.js';
export default {
  name: 'MyGame',
  data() {
    return {
      remNumberLess,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      gameList: [],
      empty: false,
      tabList: [
        { name: 'UP主', type: 'up', handleFunction: this.getUpList },
        {
          name: 'UP资源',
          type: 'resource',
          handleFunction: this.getResourceList,
        },
      ],
      current: 0,
      dataList: [],
      noClick: false, // 防抖
    };
  },
  async created() {
    this.tabList.forEach(tab => {
      this.dataList.push({
        data: [],
        finished: false,
        loadingObj: {
          loading: false,
          reloading: false,
        },
        empty: false,
        params: {
          page: 1,
          listRows: 10,
        },
      });
    });

    await this.getDataList();
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
    tapNav(index) {
      if (this.current != index) {
        this.current = index;
        // let active = this.dataList[this.current];
        // if (!active.data.length) {
        //   this.getDataList();
        // }
        this.getDataList();
      }
    },
    getUpList(params) {
      return new Promise((resolve, reject) => {
        ApiUserGetFocusUp({
          page: params.page,
          listRows: params.listRows,
        }).then(res => {
          res.data.list = res.data.list.map(item => {
            return {
              ...item,
              is_focus: 1,
            };
          });
          resolve(res);
        });
      });
    },
    getResourceList(params) {
      return new Promise((resolve, reject) => {
        ApiUserGetFocusUpGame({
          page: params.page,
          listRows: params.listRows,
        }).then(res => {
          resolve(res);
        });
      });
    },
    async getDataList(action = 1) {
      let active = this.dataList[this.current];
      if (action === 1) {
        active.params.page = 1;
      } else {
        if (active.finished) {
          return;
        }
        active.params.page++;
      }
      active.loadingObj.loading = true;
      const res = await this.tabList[this.current].handleFunction(
        active.params,
      );
      active.loadingObj.loading = false;
      if (action === 1 || active.params.page === 1) {
        active.data = [];
        if (!res.data.list.length) {
          active.empty = true;
        } else {
          active.empty = false;
        }
      }
      active.data.push(...res.data.list);
      if (res.data.list.length < active.params.listRows) {
        active.finished = true;
      } else {
        if (active.finished === true) {
          active.finished = false;
        }
      }
    },
    async onRefresh() {
      let active = this.dataList[this.current];
      active.finished = false;
      await this.getDataList();
      active.loadingObj.reloading = false;
    },
    async loadMore() {
      let active = this.dataList[this.current];
      await this.getDataList(2);
      active.loadingObj.loading = false;
    },
    // 处理关注
    async handleFollow(up) {
      if (this.noClick == true) return false;
      this.noClick = true;
      try {
        const res = await ApiUserFollowUser({
          memId: up.user_id,
          type: up.is_focus === 0 ? 1 : 0,
        });
        if (res.code == 1) {
          this.$toast(res.msg);
          up.is_focus = Math.abs(up.is_focus - 1);
        }
      } finally {
        this.noClick = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.follow-up-page {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  .tab-bar {
    .fixed-center;
    position: fixed;
    width: 100%;
    height: 50 * @rem;
    z-index: 200;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    .tab-item {
      width: 156 * @rem;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      font-weight: 400;
      color: #797979;
      &.on {
        font-weight: 600;
        color: #000000;
      }
    }
    .line {
      width: 12 * @rem;
      height: 4 * @rem;
      border-radius: 2 * @rem;
      background-color: @themeColor;
      position: absolute;
      bottom: 2 * @rem;
      transform: translateX(102 * @rem);
      transition: 0.3s;
    }
  }
  .follow-up-container {
    padding-top: 50 * @rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 1;
    .follow-list {
      padding: 5 * @rem 18 * @rem 0;
      .follow-item {
        &:not(:last-of-type) {
          .up-item {
            .up-info {
              border-bottom: 0.5 * @rem solid #ebebeb;
            }
          }
        }
        &:not(:last-of-type) {
          .resource-item {
            border-bottom: 0.5 * @rem solid #ebebeb;
          }
        }
        .up-item {
          position: relative;
          display: flex;
          align-items: center;
          .up-info {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            padding: 15 * @rem 0;
            .up-avatar {
              width: 60 * @rem;
              height: 60 * @rem;
            }
            .info-center {
              flex: 1;
              min-width: 0;
              margin-left: 12 * @rem;
              .up-nickname {
                font-size: 16 * @rem;
                color: #000000;
                font-weight: 500;
                line-height: 22 * @rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .num {
                font-size: 12 * @rem;
                color: #929292;
                line-height: 17 * @rem;
                margin-top: 11 * @rem;
              }
            }
          }

          .follow-btn {
            box-sizing: border-box;
            width: 64 * @rem;
            height: 30 * @rem;
            font-size: 12 * @rem;
            color: #ffffff;
            border-radius: 15 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e0e0e0;
            &.can {
              background-color: @themeColor;
            }
          }
        }

        .resource-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 15 * @rem 0;
          .resource-info {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            .resource-icon {
              width: 70 * @rem;
              height: 70 * @rem;
            }
            .info-center {
              flex: 1;
              min-width: 0;
              margin-left: 12 * @rem;
              .name {
                font-size: 16 * @rem;
                color: #000000;
                font-weight: 500;
                line-height: 22 * @rem;
              }
              .line {
                margin-top: 4 * @rem;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                height: 17 * @rem;
                overflow: hidden;
                .tags {
                  display: flex;
                  .tag {
                    flex-shrink: 0;
                    height: 17 * @rem;
                    padding: 0 4 * @rem;
                    display: flex;
                    align-items: center;
                    border-radius: 6 * @rem;
                    background-color: #f5f5f6;
                    font-size: 10 * @rem;
                    color: #9a9a9a;
                    margin-right: 5 * @rem;
                  }
                }
                .size {
                  font-size: 10 * @rem;
                  color: #9a9a9a;
                }
              }
              .bottom {
                margin-top: 5 * @rem;
                display: flex;
                align-items: center;
                .text {
                  font-size: 11 * @rem;
                  color: #444444;
                }
                .icon {
                  width: 14 * @rem;
                  height: 14 * @rem;
                  margin-left: 8 * @rem;
                }
              }
            }
          }
          .download-btn {
            width: 64 * @rem;
            height: 30 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15 * @rem;
            background-color: @themeColor;
            color: #fff;
            font-size: 13 * @rem;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
