<template>
  <div class="task-new-page">
    <nav-bar-2
      bgStyle="transparent-white"
      :placeholder="false"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bar"></div>
      <div class="active-ruler-btn" @click="activeRulerShow = true"></div>
      <div class="main-content">
        <div class="content-box">
          <div class="page-title">
            <div class="text">限时免费 先领后用</div>
            <div class="getRecord" @click="handleGo({}, 'MyCoupon')">
              我的领取记录
            </div>
          </div>
          <div class="top-info">
            <div class="get-number">今日已有{{ topInfo.num }}人领</div>
            <div class="get-swiper">
              <swiper :options="getSwiperOption">
                <swiper-slide
                  class="swiper-slide"
                  v-for="(item, index) in topInfo.list"
                  :key="index"
                >
                  <div
                    class="info"
                    :style="{ color: topInfo.configs.get_time_color }"
                  >
                    <span :style="{ color: topInfo.configs.get_game_color }">{{
                      item.nickname
                    }}</span>
                    领取了
                    <span :style="{ color: topInfo.configs.get_game_color }">{{
                      item.game_title
                    }}</span>
                    648充值卡
                  </div>
                </swiper-slide>
              </swiper>
            </div>
          </div>
          <div class="card-swiper" :class="{ one: coupon648List.length == 1 }">
            <template v-if="coupon648List.length && coupon648List[0].length">
              <swiper :options="cardSwiperOption1">
                <swiper-slide
                  class="swiper-slide"
                  v-for="(couponGroup, index) in coupon648List"
                  :key="index"
                >
                  <div class="list">
                    <div
                      class="item"
                      v-for="(coupon, couponIndex) in couponGroup"
                      :key="couponIndex"
                      @click="handleGo(coupon, 'game_detail')"
                    >
                      <img
                        class="item-pic"
                        :src="coupon.game.titlepic"
                        alt=""
                      />
                      <div class="info">
                        <div class="name">
                          <span>{{ coupon.game_title }}</span>
                          <div class="discount">{{ coupon.subtitle }}</div>
                        </div>
                        <div class="desc">
                          <div class="star" v-if="coupon.game">
                            <i></i>
                            <span>{{ coupon.game.rating.rating }}</span>
                          </div>
                          <div class="type">{{ coupon.game.type[0] }}</div>
                        </div>
                        <div class="tags">
                          <span class="welfare_count"
                            >{{ coupon.game.welfare_count }}个福利</span
                          >
                          <span
                            v-for="(tag, tagIndex) in coupon.game.extra_tag"
                            :key="tagIndex"
                            >{{ tag.name }}</span
                          >
                        </div>
                      </div>
                      <div class="item-right">
                        <div class="get-btn" @click.stop="takeCoupon(coupon)">
                          领取
                        </div>
                      </div>
                    </div>
                  </div>
                </swiper-slide>
              </swiper>
              <div class="swiper-pagination pagination1"></div>
            </template>
            <content-empty v-else></content-empty>
          </div>
        </div>
      </div>
      <div class="content-box">
        <div class="page-title">
          <div class="text">价值58元任务礼</div>
        </div>
        <pull-refresh @refresh="onRefresh" v-model="isLoading">
          <div class="task-list">
            <template v-for="(item, key) in taskList">
              <div class="task-item" :key="key">
                <div class="icon">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="task-info">
                  <div class="title">{{ item.title }}</div>
                  <div class="line">
                    <div class="gold" v-if="item.gold">+{{ item.gold }}</div>
                    <div class="coupon" v-if="item.coupon">
                      {{ item.coupon }}
                    </div>
                    <div class="exp" v-if="item.exp">+{{ item.exp }}</div>
                  </div>
                  <div class="extra" v-if="item.extra_exp">
                    会员加成：{{ item.extra_exp }}
                  </div>
                </div>
                <div
                  class="task-btn btn"
                  v-if="item.is_finish == 0"
                  @click.stop="handleGo(item, key)"
                >
                  {{ $t('去完成') }}
                </div>
                <div
                  class="task-btn btn get"
                  v-if="item.is_finish == 1"
                  @click.stop="handleGet(item, key)"
                >
                  {{ $t('领取') }}
                </div>

                <div class="task-btn btn had" v-else-if="item.is_finish == 2">
                  {{ $t('已领取') }}
                </div>
              </div>
            </template>
          </div>
        </pull-refresh>
      </div>
      <div class="content-box" ref="lotteryTask">
        <div class="page-title">
          <div class="text">{{ lottery.title || '得抽奖券赢好礼' }}</div>
        </div>
        <div
          class="lotteryTaskSwiper"
          :class="{ one: lotteryTaskList.length == 1 }"
          v-if="lotteryTaskList.length && lotteryTaskList[0].length"
        >
          <swiper :options="taskSwiperOption">
            <swiper-slide
              class="swiper-slide"
              v-for="(group, index) in lotteryTaskList"
              :key="index"
            >
              <div class="lottery-task-list">
                <div class="item" v-for="(item, index1) in group" :key="index1">
                  <div class="icon">
                    <img :src="item.icon" alt="" />
                  </div>
                  <div class="info">
                    <div class="name">{{ item.title }}</div>
                    <div class="desc"><i></i> 抽奖券 x1</div>
                  </div>
                  <div class="right">
                    <div
                      class="btn"
                      v-if="item.status == 0"
                      @click="handleGo(item, item.type)"
                    >
                      去完成
                    </div>
                    <div
                      class="btn get"
                      v-else-if="item.status == 1"
                      @click="getNewUserReward(item)"
                    >
                      领取
                    </div>
                    <div class="btn geted" v-else-if="item.status == 2">
                      已领取
                    </div>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <div class="swiper-pagination taskPagination"></div>
        </div>
        <content-empty v-else></content-empty>
      </div>
      <div class="lottery-table-box">
        <div class="table-content">
          <div class="table-list">
            <div
              class="table-item"
              v-for="(item, index) in reward.list"
              :key="index"
            >
              <img class="reward-icon" :src="item.icon" alt="" />
              <div class="reward-text">{{ item.title }}</div>
            </div>
            <div class="table-item turn-btn-1 btn" @click="handleRaffle">
              <div class="bg" :class="`turn-result-${turnIndex}`"></div>
              <div class="turn-btn-1-content"></div>
            </div>
          </div>
        </div>
        <div class="my-info">
          <div class="count">剩余抽奖券：{{ lotteryCount }}</div>
          <div class="record" @click="showLotteryLogDialog">我的抽奖记录</div>
        </div>
      </div>
    </div>
    <!-- 小号选择弹窗 -->
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="Number(selectCoupon.game_id)"
      :autoXh="1"
      @onSelectSuccess="getCoupon"
    ></yy-xh-select>
    <!-- 代金券领取成功弹窗 -->
    <coupon-get-popup :info="selectCoupon" :show.sync="getCouponShow">
    </coupon-get-popup>

    <van-dialog
      v-model="activeRulerShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="ruler-dialog"
    >
      <div>
        <div class="dialog-title">活动规则</div>
        <div class="content">
          <template v-for="(item, index) in ruler">
            <div :key="index">
              <div class="title" v-if="item.title">{{ item.title }}</div>
              <p v-if="item.content">
                {{ item.content }}
              </p>
            </div>
          </template>
        </div>
        <div class="close-btn" @click="activeRulerShow = false"></div>
      </div>
    </van-dialog>
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      :tip="popupTip"
      :confirmText="popupConfirmText"
      @confirm="popupConfirm"
    ></task-popup>
    <!-- 抽奖结果弹窗 -->
    <van-dialog
      v-model="resultDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="result-dialog"
    >
      <div>
        <div class="dialog-title">恭喜您抽中</div>
        <div class="result-item">
          <img class="result-icon" :src="lotteryResult.icon" alt="" />
          <div class="result-name">{{ lotteryResult.title }}</div>
        </div>
        <div class="tips" v-if="lotteryResult.type == 1">
          在“我的-代金券”查看
        </div>
        <div
          class="operate-btn btn"
          v-if="lotteryResult.type == 0"
          @click="resultDialogShow = false"
        >
          我知道了
        </div>
        <div
          class="operate-btn btn"
          v-if="lotteryResult.type == 1"
          @click="handleGo(lotteryResult, 'MyCoupon')"
        >
          去查看
        </div>
        <div
          class="operate-btn btn"
          v-if="lotteryResult.type == 2"
          @click="resultDialogShow = false"
        >
          联系客服
        </div>
      </div>
    </van-dialog>
    <!-- 抽奖记录弹窗 -->
    <van-dialog
      v-model="lotteryLogDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      class="log-dialog"
    >
      <div>
        <div class="dialog-title">我的抽奖记录</div>
        <div class="lottery-log-list">
          <div
            class="lottery-log-item"
            v-for="(log, index) in lotteryLogList"
            :key="index"
          >
            <div class="time">{{ timeFormat(log.create_time) }}</div>
            <div class="title">{{ log.title }}</div>
          </div>
        </div>
        <div class="close-btn" @click="lotteryLogDialogShow = false"></div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import TaskPopup from '../comonents/task-popup';
import {
  ApiMissionGetNewcomer,
  ApiMissionGetMissionReward,
  ApiMissionGetNewUserMission,
  ApiMissionGetNewUserMissionCoupon,
  ApiMissionGetNewUserReward,
  ApiMissionGetNewUserLog,
} from '@/api/views/mission.js';
import {
  ApiCardGetMemGet648CardLog,
  ApiCardGetNewUserRule,
} from '@/api/views/gift.js';
import {
  ApiGetSpecialOrderCouponList,
  ApiCouponTake,
} from '@/api/views/coupon.js';
import {
  BOX_goToGame,
  BOX_openInNewNavWindow,
  BOX_openInNewWindow,
  BOX_showActivity,
  BOX_showActivityByAction,
  platform,
  BOX_takeCoupon,
} from '@/utils/box.uni.js';
import yyXhSelect from '@/components/yy-xh-select/index.vue';
import couponGetPopup from '@/components/coupon-get-popup/index.vue';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'TaskNew',
  components: {
    TaskPopup,
    yyXhSelect,
    couponGetPopup,
  },
  data() {
    return {
      platform,
      taskPopup: false, // 任务弹窗是否显示
      popupContent: '', // 任务弹窗内容
      popupTip: '', // 任务弹窗提示
      popupConfirmText: '', // 任务弹窗确认按钮文案
      activeRulerShow: false,
      isSvip: false,
      topInfo: {
        num: 0,
        list: {},
        ruleText: '',
        configs: {},
      },
      taskList: {},
      ptbUrl: '',
      vipUrl: '',
      isLoading: false,
      getSwiperOption: {
        slidesPerView: 1,
        direction: 'vertical',
        autoplay: true,
        allowTouchMove: false,
      },
      coupon648List: [],
      cardSwiperOption1: {
        slidesPerView: 'auto',
        observer: true,
        observerSlideChildren: true,
        observerParents: true,
        pagination: {
          el: '.pagination1',
        },
      },
      xhDialogShow: false,
      selectCoupon: {},
      getCouponShow: false, //领取成功代金券弹窗
      lottery: {},
      reward: {},
      taskSwiperOption: {
        slidesPerView: 'auto',
        observer: true,
        observerSlideChildren: true,
        observerParents: true,
        pagination: {
          el: '.taskPagination',
        },
      },
      lotteryTaskList: [],
      turnIndex: 0, //转盘旋转角度
      lotteryCount: 0, //抽奖次数
      isTurning: false, //转盘是否转动中
      lotteryResult: {}, //转盘结果
      resultDialogShow: false, //转盘结果弹窗
      lotteryLogDialogShow: false, //抽奖记录弹窗
      lotteryLogList: [], //抽奖记录列表
      ruler: [], //规则
    };
  },
  watch: {
    lottery(newVal, oldVal) {
      this.lotteryTaskList = [];
      if (newVal.list && newVal.list.length > 0) {
        try {
          let list = [...newVal.list];
          if (list.length > 4) {
            let arr = [];
            list.forEach((item, index) => {
              arr.push(item);
              if (index == list.length - 1) {
                this.lotteryTaskList.push(arr);
                return false;
              }
              if ((index + 1) % 4 == 0) {
                this.lotteryTaskList.push(arr);
                arr = [];
              }
            });
          } else {
            this.lotteryTaskList.push(list);
          }
        } catch {}
      }
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = '新手见面礼';
    }

    // 神策埋点
    this.$sensorsTrack('new_user_pack_page_view');

    await this.getNewData();
    await this.getMemberList();
    await this.get648CouponList();
    await this.getNewUserMission();
    await this.getRuler();
  },
  mounted() {
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  methods: {
    async onResume() {
      await this.getNewUserMission();
      await this.get648CouponList();
    },
    async onRefresh() {
      await this.getNewData();
      this.isLoading = false;
    },
    async getRuler() {
      let res = await ApiCardGetNewUserRule();
      this.ruler = res.data;
    },
    toSvip() {
      BOX_openInNewWindow({ name: 'Svip' }, { url: this.vipUrl });
    },
    popupConfirm() {
      if (this.popupConfirmText == this.$t('我知道了')) {
        this.taskPopup = false;
        return false;
      }
      BOX_openInNewNavWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    async handleGet(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      this.$toast(res.msg);
      await this.getNewData();
    },
    handleGo(item, key) {
      switch (key) {
        case 'game_detail':
          console.log(item);
          BOX_goToGame(
            {
              params: {
                id: item.game_id,
              },
            },
            { id: item.game_id },
          );
          break;
        case 'add_assistant':
          BOX_openInNewWindow(
            { name: 'AddAssistant' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/add_assistant` },
          );
          break;
        case 'bind_wx': // 绑定公众号
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'follow_gzh': // 关注公众号
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'down_game': // 下载游戏
          BOX_showActivity({ name: 'Category' }, { page: 'qbyx' });
          break;
        case 'first_pay': // 首充
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满6元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'band_email': //绑定邮箱
          BOX_showActivity(
            { name: 'ChangeEmail' },
            { page: 'com.a3733.cwbgamebox.ui.mine.BindEmailActivity' },
          );
          break;
        case 'mem_info': // 实名认证
        case 'mobile': // 绑定手机
          let set_temp =
            platform == 'android'
              ? 'com.a3733.gamebox.ui.etc.AccountSafeActivity'
              : 'YYAccountAndSecurityViewController';
          BOX_showActivity({ name: 'UserInfo' }, { page: set_temp });
          break;
        case 'pay100': // 充值100元
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满100元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'MyCoupon':
          BOX_showActivity({ name: 'MyCoupon' }, { page: 'wddjq' });
          break;
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        case 'svip':
          BOX_openInNewWindow(
            { name: 'Svip' },
            { url: `${window.location.origin}/#/svip` },
          );
          break;
        case 'savings_card':
          BOX_openInNewWindow(
            { name: 'SavingsCard' },
            { url: `${window.location.origin}/#/savings_card` },
          );
          break;
        case 'gold':
          BOX_openInNewWindow(
            { name: 'GoldCoinExchange' },
            { url: `${window.location.origin}/#/gold_coin_exchange` },
          );
          break;
      }
    },
    async getMemberList() {
      const res = await ApiCardGetMemGet648CardLog();
      if (res.data.num > 9999) {
        this.topInfo.num = res.data.num.tofixed(1) + 'W';
      } else {
        this.topInfo.num = res.data.num;
      }
      this.topInfo.list = res.data.list;
      this.topInfo.ruleText = res.data.rule_text;
      this.topInfo.configs = res.data.configs;
    },
    async get648CouponList() {
      this.coupon648List = [];
      this.giftLoading = true;
      try {
        const res = await ApiGetSpecialOrderCouponList({
          is_list: 1,
          is_648: 1,
          type: 0,
          listRows: 12,
        });
        let list = [...res.data.coupon_list];
        if (list.length > 3) {
          let arr = [];
          list.forEach((item, index) => {
            arr.push(item);
            if (index == list.length - 1) {
              this.coupon648List.push(arr);
              return false;
            }
            if ((index + 1) % 3 == 0) {
              this.coupon648List.push(arr);
              arr = [];
            }
          });
        } else {
          this.coupon648List.push(list);
        }
      } finally {
        this.giftLoading = false;
      }
    },
    async takeCoupon(item) {
      if (platform == 'android') {
        // 新人礼代金券特殊传第三个参数
        BOX_takeCoupon(item, 1, 'new_user_pack_get');
        return;
      }
      this.selectCoupon = item;
      this.xhDialogShow = true;
    },
    async getCoupon(xhInfo) {
      this.xhDialogShow = false;
      const res = await ApiCouponTake({
        couponId: this.selectCoupon.id,
        xhId: xhInfo.xhId,
      });
      this.$toast.clear();
      this.getCouponShow = true;

      // 神策埋点
      this.$sensorsTrack('get_voucher', {
        voucher_id: `${this.selectCoupon.id}`,
        voucher_name: `${this.selectCoupon.title}`,
        voucher_amount: `${this.selectCoupon.money}`,
      });

      // 神策埋点
      this.$sensorsTrack('new_user_pack_get', {
        reward_type: `${this.selectCoupon.type}`,
        reward_num: `${this.selectCoupon.money}`,
      });

      await this.get648CouponList();
    },
    async getNewData() {
      const res = await ApiMissionGetNewcomer();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList = list;
    },
    async getNewUserMission() {
      let res = await ApiMissionGetNewUserMission();
      this.lottery = res.data.task;
      this.reward = res.data.reward;
      this.lotteryCount = res.data.coupon_count;
    },
    async getNewUserReward(item) {
      await ApiMissionGetNewUserMissionCoupon({ id: item.id });
      this.getNewUserMission();
    },
    async handleRaffle() {
      if (!this.lotteryCount) {
        this.popupContent =
          '您当前尚未有抽奖机会，请前往完成的抽奖券任务获得机会';
        this.popupTip = '';
        this.popupConfirmText = this.$t('我知道了');
        this.taskPopup = true;
        window.scrollTo(0, this.$refs.lotteryTask.offsetTop - 100);
        return false;
      }
      if (this.isTurning) {
        return false;
      }
      this.isTurning = true;
      this.resultDialogShow = false;
      this.turnIndex = 0;
      try {
        const res = await ApiMissionGetNewUserReward();
        this.lotteryResult = res.data.info;
        this.lotteryCount = res.data.count;
        await this.onTurning(this.lotteryResult.id);
        this.resultDialogShow = true;
      } finally {
        this.isTurning = false;
      }
    },
    async onTurning(id) {
      return new Promise(resolve => {
        this.turnIndex =
          this.reward.list.findIndex(item => {
            return item.id == id;
          }) + 1;
        setTimeout(() => {
          resolve();
        }, 5000);
      });
    },
    async showLotteryLogDialog() {
      const res = await ApiMissionGetNewUserLog();
      this.lotteryLogList = res.data.list;
      this.lotteryLogDialogShow = true;
    },
    timeFormat(timestamp) {
      let date = handleTimestamp(timestamp);
      return date.detail_time;
    },
  },
};
</script>

<style lang="less" scoped>
.task-new-page {
  font-size: 14 * @rem;
  min-height: 100vh;

  .main {
    padding-bottom: 40 * @rem;
    background-color: #ffc94d;
    position: relative;

    .active-ruler-btn {
      width: 24 * @rem;
      height: 63 * @rem;
      background: url(~@/assets/images/welfare/active-ruler-btn.png) no-repeat;
      background-size: 24 * @rem 63 * @rem;
      position: absolute;
      top: 77 * @rem;
      right: 0;
    }

    .main-content {
      width: 100%;
      overflow: hidden;
      margin: -43 * @rem auto 0;
    }

    .content-box {
      margin: 0 10 * @rem;
      padding: 10 * @rem;
      background-color: #fff;
      border-radius: 12 * @rem;
      margin-bottom: 24 * @rem;

      .page-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10 * @rem;

        .text {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;
          height: 22 * @rem;
          line-height: 22 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #2a1c14;

          &::before {
            content: '';
            display: block;
            width: 6 * @rem;
            height: 16 * @rem;
            background-color: #ff5c4e;
            border-radius: 16 * @rem;
            margin-right: 8 * @rem;
          }
        }

        .getRecord {
          display: block;
          flex-shrink: 0;
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #5a4d44;
          line-height: 17 * @rem;
          margin-right: 14 * @rem;
        }
      }
    }
  }
  .top-bar {
    width: 100%;
    height: 228 * @rem;
    .image-bg('~@/assets/images/welfare/task-new-bg-new.png');
  }
  .card-swiper {
    position: relative;
    margin-top: 16 * @rem;
    padding-bottom: 18 * @rem;

    &.one {
      padding-bottom: 0;
      .swiper-slide {
        width: 100%;
      }

      .swiper-pagination {
        display: none;
      }
    }
    .swiper-slide {
      width: 306 * @rem;
      margin-right: 10 * @rem;
    }
    .item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 84 * @rem;
      background: #fafafa;
      border-radius: 8 * @rem;
      padding: 10 * @rem;
      margin-bottom: 10 * @rem;
      box-sizing: border-box;

      &:last-of-type {
        margin-bottom: 0;
      }

      .item-pic {
        display: block;
        flex-shrink: 0;
        width: 58 * @rem;
        height: 58 * @rem;
        margin-right: 8 * @rem;
      }

      .info {
        flex: 1;
        min-width: 0;

        .name {
          display: flex;
          align-items: center;
          width: 200 * @rem;

          span {
            flex-shrink: 0;
            height: 20 * @rem;
            font-size: 14 * @rem;
            font-weight: 500;
            color: #333333;
            line-height: 20 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .discount {
            height: 17 * @rem;
            line-height: 17 * @rem;
            background: #f5f5f6;
            border-radius: 4 * @rem;
            padding: 0 4 * @rem;
            color: #ff8a00;
            text-align: center;
            font-size: 10 * @rem;
            margin-left: 8 * @rem;
            background-color: #ffefdd;
            overflow: hidden;
          }
        }

        .desc {
          display: flex;
          align-items: center;
          height: 15 * @rem;
          margin-top: 6 * @rem;
          font-size: 10 * @rem;
          font-weight: 400;
          color: #999;
          line-height: 15 * @rem;
          overflow: hidden;

          .star {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            height: 15 * @rem;
            font-size: 12 * @rem;
            font-weight: 600;
            color: @themeColor;
            line-height: 15 * @rem;
            margin-right: 8 * @rem;

            i {
              display: block;
              width: 10 * @rem;
              height: 10 * @rem;
              background: url(~@/assets/images/welfare/welfare648/star.png)
                no-repeat;
              background-size: 10 * @rem 10 * @rem;
              margin-right: 4 * @rem;
              flex-shrink: 0;
              margin-top: -2 * @rem;
            }
          }
          .type {
            flex-shrink: 0;
          }
          .play-count {
            margin-left: 6 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .tags {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          margin-top: 6 * @rem;
          height: 19 * @rem;
          overflow: hidden;

          span {
            flex-shrink: 0;
            height: 17 * @rem;
            line-height: 17 * @rem;
            border-radius: 4 * @rem;
            border: 1 * @rem solid #d3d3d3;
            padding: 0 4 * @rem;
            font-size: 10 * @rem;
            font-weight: 400;
            color: #888;
            margin-right: 4 * @rem;
            margin-bottom: 2 * @rem;
          }

          .welfare_count {
            border-color: #ff8a00;
            color: #ff8a00;
          }
        }
      }

      .item-right {
        flex-shrink: 0;
        margin-left: 8 * @rem;

        .get-btn {
          display: block;
          width: 72 * @rem;
          height: 32 * @rem;
          line-height: 32 * @rem;
          text-align: center;
          background: #ff5c4e;
          color: #fff;
          border-radius: 21 * @rem;
          margin: 0 auto;
          position: relative;
          z-index: 1;
        }

        .residue {
          width: 100%;
          height: 13 * @rem;
          font-size: 10 * @rem;
          font-weight: 400;
          color: #777777;
          line-height: 13 * @rem;
          text-align: right;
          margin-top: 17 * @rem;

          span {
            color: #ff5c4e;
          }
        }
      }
    }
  }
  ::v-deep {
    .swiper-pagination {
      bottom: 2 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 325 * @rem;
      height: 6 * @rem;

      .swiper-pagination-bullet {
        display: block;
        width: 6 * @rem;
        height: 6 * @rem;
        margin-right: 6 * @rem;
        border-radius: 10 * @rem;

        &:last-of-type {
          margin-right: 0;
        }

        &.swiper-pagination-bullet-active {
          width: 12 * @rem;
          background-color: @themeColor;
        }
      }
    }
  }
  .top-info {
    display: flex;
    align-items: center;
    margin-top: 15 * @rem;

    .get-number {
      flex-shrink: 0;
      width: 105 * @rem;
      height: 30 * @rem;
      line-height: 30 * @rem;
      font-size: 11 * @rem;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      border-top-left-radius: 4 * @rem;
      border-bottom-left-radius: 4 * @rem;
      background: linear-gradient(#ff976a, #e74b36);
    }

    .get-swiper {
      flex: 1;
      min-width: 0;
      padding: 0 13 * @rem;
      height: 30 * @rem;
      line-height: 30 * @rem;
      border-top-right-radius: 4 * @rem;
      border-bottom-right-radius: 4 * @rem;
      background-color: #fff5f5;
      .swiper-container {
        height: 100%;
      }
      .info {
        display: block;
        line-height: 30 * @rem;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #cb3344;

        span {
          color: #c106f0;
        }
      }
    }
  }
  .svip-info {
    width: 100%;
    height: 76 * @rem;
  }
  .task-list {
    .task-item {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 337 * @rem;
      height: 80 * @rem;
      background: #fafafa;
      border-radius: 8 * @rem;
      margin: 12 * @rem auto 0;
      padding: 0 12 * @rem;
      .icon {
        width: 50 * @rem;
        height: 50 * @rem;
        background-color: #ffffff;
        border-radius: 8 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 32 * @rem;
          height: 32 * @rem;
        }
      }
      .task-info {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .title {
          font-size: 14 * @rem;
          color: #333333;
          line-height: 18 * @rem;
          font-weight: 500;
          text-align: left;
          word-break: break-all;
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 6 * @rem;
          .gold {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-gold.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .exp {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-exp.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .coupon {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-coupon.png) left
              center no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
        }
        .extra {
          font-size: 11 * @rem;
          color: #777777;
          line-height: 14 * @rem;
          margin-top: 6 * @rem;
        }
      }
      .task-btn {
        width: 72 * @rem;
        height: 32 * @rem;
        border-radius: 16 * @rem;
        background: #ffc34e;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        &.had {
          background: #e4e4e4;
        }
        &.get {
          background: #ff5c4e;
        }
      }
    }
  }
  .lotteryTaskSwiper {
    padding-bottom: 18 * @rem;
    position: relative;

    .lottery-task-list {
      margin-top: 20 * @rem;

      .item {
        display: flex;
        align-items: center;
        width: 335 * @rem;
        height: 80 * @rem;
        background-color: #fafafa;
        border-radius: 8 * @rem;
        padding: 0 10 * @rem;
        margin-bottom: 12 * @rem;
        box-sizing: border-box;

        .icon {
          width: 50 * @rem;
          height: 50 * @rem;
          margin-right: 10 * @rem;
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            width: 100%;
            height: 18 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #333333;
            line-height: 18 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .desc {
            display: flex;
            align-items: center;
            height: 18 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            text-align: left;
            margin-top: 6 * @rem;

            i {
              display: block;
              width: 18 * @rem;
              height: 18 * @rem;
              background: url(~@/assets/images/welfare/choujiang-icon.png)
                no-repeat center center;
              background-size: 18 * @rem 18 * @rem;
              margin-right: 4 * @rem;
            }
          }
        }

        .right {
          .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 72 * @rem;
            height: 32 * @rem;
            line-height: 32 * @rem;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #ffffff;
            text-align: center;
            background-color: #ffc34e;
            border-radius: 21 * @rem;
            margin-left: 10 * @rem;

            &.get {
              background-color: #ff5c4e;
            }

            &.geted {
              background-color: #e4e4e4;
            }
          }
        }
      }
    }
    &.one {
      padding-bottom: 0;
      .taskPagination {
        display: none;
      }
    }
    .taskPagination {
      bottom: 3 * @rem;
    }
  }
  .lottery-table-box {
    width: 100%;
    height: 453 * @rem;
    margin: 0 auto;
    background: url(~@/assets/images/welfare/task-new-lottery-bg.png) no-repeat
      center center;
    background-size: 100% 453 * @rem;
    overflow: hidden;

    .table-content {
      box-sizing: border-box;
      width: 309 * @rem;
      height: 309 * @rem;
      margin: 80 * @rem auto 0;
      box-sizing: border-box;
      .table-list {
        background-color: #fff;
        position: relative;
        .table-item {
          width: 103 * @rem;
          height: 103 * @rem;
          background: url(~@/assets/images/turn-table/turn-item-new.png) center
            center no-repeat;
          background-size: 103 * @rem 103 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: absolute;
          .reward-icon {
            height: 60 * @rem;
            width: auto;
            margin-top: -5 * @rem;
          }
          .reward-text {
            font-size: 12 * @rem;
            line-height: 14 * @rem;
            color: #d84c10;
            font-weight: 500;
          }
          &:nth-of-type(1) {
            left: 206 * @rem;
            top: 0;
          }
          &:nth-of-type(2) {
            left: 206 * @rem;
            top: 103 * @rem;
          }
          &:nth-of-type(3) {
            left: 206 * @rem;
            top: 206 * @rem;
          }
          &:nth-of-type(4) {
            left: 103 * @rem;
            top: 206 * @rem;
          }
          &:nth-of-type(5) {
            left: 0;
            top: 206 * @rem;
          }
          &:nth-of-type(6) {
            left: 0;
            top: 103 * @rem;
          }
          &:nth-of-type(7) {
            left: 0;
            top: 0;
          }
          &:nth-of-type(8) {
            left: 103 * @rem;
            top: 0;
          }
          &.turn-btn-1 {
            left: 103 * @rem;
            top: 103 * @rem;
            width: 103 * @rem;
            height: 103 * @rem;
            background: none;
            .bg {
              width: 103 * @rem;
              height: 103 * @rem;
              background-image: url(~@/assets/images/welfare/task-new-lottery-btn-bg.png);
              background-size: 103 * @rem 103 * @rem;
              position: absolute;
              left: 0;
              top: 0;
              z-index: 1;
              transform: rotate(0deg);
              &.turn-result-0 {
                transform: rotate(0deg);
              }
              &.turn-result-1 {
                transform: rotate(3645deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-2 {
                transform: rotate(3690deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-3 {
                transform: rotate(3735deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-4 {
                transform: rotate(3780deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-5 {
                transform: rotate(3825deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-6 {
                transform: rotate(3870deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-7 {
                transform: rotate(3915deg);
                transition: all 5s ease-in-out;
              }
              &.turn-result-8 {
                transform: rotate(3960deg);
                transition: all 5s ease-in-out;
              }
            }
            .turn-btn-1-content {
              width: 103 * @rem;
              height: 103 * @rem;
              background-image: url(~@/assets/images/welfare/task-new-lottery-btn.png);
              background-size: 103 * @rem 103 * @rem;
              position: absolute;
              left: 0;
              top: 0;
              z-index: 2;
            }
          }
        }
      }
    }

    .my-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 299 * @rem;
      height: 20 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #ffffff;
      line-height: 16 * @rem;
      margin: 10 * @rem auto 0;
    }
  }
  .result-dialog {
    .dialog-title {
      display: block;
      padding: 30 * @rem 0 20 * @rem;
      text-align: center;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
    }

    .result-item {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      padding: 0 10 * @rem;
      margin: 0 auto;

      .result-icon {
        display: block;
        width: auto;
        height: 95 * @rem;
        margin: 0 auto;
      }
      .result-name {
        height: 18 * @rem;
        font-weight: 500;
        font-size: 13 * @rem;
        color: #000;
        line-height: 15 * @rem;
        text-align: center;
        margin-top: 10 * @rem;
      }
    }
    .tips {
      height: 15 * @rem;
      font-weight: 400;
      font-size: 11 * @rem;
      color: #777777;
      line-height: 13 * @rem;
      text-align: center;
      margin-top: 5 * @rem;
      padding: 0 10 * @rem;
    }

    .operate-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 238 * @rem;
      height: 38 * @rem;
      line-height: 38 * @rem;
      background: #ff5c4e;
      border-radius: 21 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #ffffff;
      text-align: center;
      margin: 10 * @rem auto 20 * @rem;
    }
  }
  .log-dialog {
    overflow: unset;
    .dialog-title {
      display: block;
      padding: 30 * @rem 0 20 * @rem;
      text-align: center;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
    }

    .lottery-log-list {
      padding: 0 24 * @rem;
      margin-bottom: 20 * @rem;
      max-height: 300 * @rem;
      overflow-y: auto;

      .lottery-log-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15 * @rem;

        .time {
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #777777;
          line-height: 14 * @rem;
        }

        .title {
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #777777;
          line-height: 14 * @rem;
        }
      }
    }

    .close-btn {
      display: block;
      width: 22 * @rem;
      height: 22 * @rem;
      background: url(~@/assets/images/welfare/popup-close-btn.png) no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      bottom: -38 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .ruler-dialog {
    overflow: unset;
    .dialog-title {
      display: block;
      padding: 30 * @rem 0 20 * @rem;
      text-align: center;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
    }

    .content {
      padding: 0 16 * @rem 15 * @rem;
      max-height: 400 * @rem;
      overflow-y: auto;

      p {
        font-weight: 400;
        font-size: 12 * @rem;
        color: #777777;
        line-height: 17 * @rem;
        text-align: justify;
        margin-bottom: 5 * @rem;
      }
      .title {
        display: inline-flex;
        align-items: center;
        background-color: #fff6dd;
        height: 18 * @rem;
        font-weight: 600;
        font-size: 13 * @rem;
        color: #333333;
        line-height: 15 * @rem;
        margin-top: 25 * @rem;
        margin-bottom: 8 * @rem;

        &::before {
          content: '';
          display: block;
          width: 5 * @rem;
          height: 5 * @rem;
          background-color: #000;
          border-radius: 50%;
          margin-left: 3 * @rem;
          margin-right: 10 * @rem;
        }
      }
    }
  }
}
</style>
