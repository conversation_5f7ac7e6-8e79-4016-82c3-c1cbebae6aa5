export default [
  {
    path: '/up_detail/:id',
    name: 'UpDetail',
    component: () => import(/* webpackChunkName: "up" */ '@/views/Up/UpDetail'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/up_mine/:mem_id',
    name: 'UpMine',
    component: () => import(/* webpackChunkName: "up" */ '@/views/Up/UpMine'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/up_game',
    name: 'UpGame',
    component: () => import(/* webpackChunkName: "up" */ '@/views/Up/UpGame'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/fllow_up',
    name: 'FllowUp',
    component: () => import(/* webpackChunkName: "up" */ '@/views/Up/FllowUp'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_up',
    name: 'MyUp',
    component: () => import(/* webpackChunkName: "up" */ '@/views/Up/MyUp'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/popularize_list',
    name: 'PopularizeList',
    component: () =>
      import(/* webpackChunkName: "up" */ '@/views/Up/PopularizeList'),
  },
  {
    path: '/popularize_detail/:id',
    name: 'PopularizeDetail',
    component: () =>
      import(/* webpackChunkName: "up" */ '@/views/Up/PopularizeDetail'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/up_collection_detail/:id',
    name: 'UpCollectionDetail',
    component: () =>
      import(
        /* webpackChunkName: "up" */ '@/views/Up/UpCollection/UpCollectionDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/add_up_collection',
    name: 'AddUpCollection',
    component: () =>
      import(
        /* webpackChunkName: "up" */ '@/views/Up/UpCollection/AddUpCollection'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/select_up_game',
    name: 'SelectUpGame',
    component: () =>
      import(
        /* webpackChunkName: "up" */ '@/views/Up/UpCollection/SelectUpGame'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/up_game_list/:id',
    name: 'UpGameList',
    component: () =>
      import(/* webpackChunkName: "up" */ '@/views/Up/UpGameList'),
    // meta: {
    //   requiresAuth: true,
    // },
  },
];
