<template>
  <div class="complaint">
    <nav-bar-2 :border="true" :title="$t('游戏反馈')" :azShow="true">
      <template #right>
        <div class="my-feedback" @click="toPage('MyFeedback')">
          {{ $t('我的反馈') }}
        </div>
      </template>
    </nav-bar-2>
    <main>
      <div class="top-tab">
        <div class="container">
          <div class="left on">{{ $t('游戏反馈') }}</div>
          <div class="right" @click="toPage('FeedbackComplaint', {}, 1)">
            {{ $t('举报投诉') }}
          </div>
        </div>
      </div>
      <form v-if="cateList.length > 0">
        <div class="container">
          <input
            v-model="gameName"
            :disabled="disable"
            class="game-name"
            type="text"
            :placeholder="$t('请填写游戏名（必填）')"
          />
          <ul v-show="searchListShow" class="gameList">
            <li
              v-for="(item, index) in searchList"
              :key="index"
              @click="selectGameName(item)"
            >
              {{ item }}
            </li>
          </ul>
        </div>
        <van-radio-group v-model="reportType" class="checkbox-list">
          <div class="gray-text">{{ $t('选择举报类型(必填)') }}:</div>
          <van-radio
            v-for="(item, index) in cateList"
            :key="index"
            :name="item.id"
            :checked-color="themeColorLess"
            class="checkbox-item"
            >{{ item.title }}
          </van-radio>
        </van-radio-group>
        <div class="feedback-text">
          <textarea
            v-model="textarea"
            :placeholder="
              $t('请描述您遇到的问题或向我们提出修改建议，我们会尽快进行处理！')
            "
          ></textarea>
          <div class="image-list">
            <van-uploader
              v-model="fileList"
              :after-read="afterRead"
              @delete="deletePic"
              :max-count="3"
            />
          </div>
        </div>
        <input
          v-model="contact"
          class="form-input"
          type="text"
          :placeholder="$t('请输入手机号或QQ或邮箱')"
        />
        <input
          v-model="enterAuthCode"
          class="form-input"
          type="text"
          :placeholder="$t('请输入验证码')"
        />
        <img
          @click="changeAuthCode"
          class="captcha"
          :src="`https://static.pic3733.com/api/static/h5/feedback/images/${authCode}.png`"
        />
      </form>
    </main>
    <div class="bottom">
      <div @click="submit" class="button btn">{{ $t('确认提交') }}</div>
    </div>
  </div>
</template>
<script>
import { ApiGameFeedback, ApiGameFeedbackReport } from '@/api/views/feedback';
import { ApiUploadImage } from '@/api/views/system';
import { themeColorLess } from '@/common/styles/_variable.less';
import { checkEmail, checkIphone, checkQQ } from '@/utils/formValidation';
import md5 from 'js-md5';
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'FeedbackGame',
  data() {
    return {
      themeColorLess, //主题色
      gameList: [], //返回游戏列表
      cateList: [], //返回举报类型列表
      gameName: '', //当前选中的游戏名称
      reportType: 0, //被选中的举报类型
      searchList: [], //当前输入相关游戏名
      searchListShow: false, //控制游戏列表显示,
      textarea: '', //文本内容
      fileList: [], //图片列表
      imageList: [], //上传的图片列表
      contact: '', //联系方式
      authCodeList: [
        'bw2v',
        'khjw',
        'kntv',
        'mx6m',
        'pr4k',
        'rxjg',
        'urcn',
        'yvqp',
      ], //验证码图片
      authCode: '', //当前验证码
      enterAuthCode: '', //当前输入的验证码
      gameId: 0, //当前游戏id
      disable: false, //游戏栏是否可编辑
    };
  },
  watch: {
    gameName() {
      if (this.$route.params.id) {
        return;
      }
      this.searchList = [];
      this.searchListShow = this.gameName.length > 0 ? true : false;
      for (let i of this.gameList) {
        if (i.game_name.indexOf(this.gameName) > -1)
          this.searchList.push(i.game_name);
      }
    },
  },
  async created() {
    if (['android', 'ios'].includes(platform)) {
      document.title = this.$t('游戏反馈');
      // 因为反馈和投诉两个页面是replace的，在安卓里返回按钮都要关掉窗口
      window.sessionStorage.firstUrl = window.location.href;
    }
    this.changeAuthCode();
    if (this.$route.params.id) {
      this.gameId = this.$route.params.id;
      this.gameName = this.$route.params.gameName;
      this.disable = true;
    } else if (this.$route.query.id) {
      this.gameId = this.$route.query.id;
      this.gameName = this.$route.query.gameName;
      this.disable = true;
    }
    const res = await ApiGameFeedback();
    this.gameList = res.data.gameList;
    this.cateList = res.data.rptCate;
  },
  methods: {
    toPage(name, params = {}, replace = 0) {
      replace
        ? this.$router.replace({ name: name, params: params })
        : this.$router.push({ name: name, params: params });
    },
    selectGameName(item) {
      // 搜索框点击确认
      this.gameName = item;
      this.searchList.splice(0, this.searchList.length);
      this.$nextTick(() => {
        this.searchListShow = false;
      });
    },
    afterRead(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'screenshots'; //写死
      data.image = uploadFile;
      data.time = time;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      ApiUploadImage(data).then(
        res => {
          this.imageList.push(res.data.url);
          file.status = 'done';
          file.message = this.$t('上传成功');
        },
        err => {
          file.status = 'failed';
          file.message = this.$t('上传失败');
        },
      );
    },
    changeAuthCode() {
      let num = 0;
      num = Math.ceil(Math.random() * this.authCodeList.length) - 1;
      num = num < 0 ? 0 : num;
      num = num > this.authCodeList.length ? 0 : num;
      if (this.authCode === this.authCodeList[num]) this.changeAuthCode();
      this.authCode = this.authCodeList[num];
    },
    async submit() {
      if (!this.gameName) {
        this.$toast(this.$t('游戏名未填写'));
        return false;
      }
      for (let i of this.gameList) {
        if (i.game_name === this.gameName) this.gameId = i.game_id;
      }
      if (!this.gameId) {
        this.$toast(this.$t('游戏不存在'));
        return false;
      }
      if (!this.reportType) {
        this.$toast(this.$t('请选择举报类型'));
        return false;
      }
      if (!this.textarea) {
        this.$toast(this.$t('内容未填写'));
        return false;
      }
      if (!this.contact) {
        this.$toast(this.$t('联系方式不能为空'));
        return false;
      }
      if (
        !checkEmail(this.contact) &&
        !checkIphone(this.contact) &&
        !checkQQ(this.contact)
      ) {
        this.$toast(this.$t('非法的联系方式'));
        return false;
      }
      if (!this.enterAuthCode) {
        this.$toast(this.$t('验证码不能为空'));
        return false;
      }
      if (
        this.enterAuthCode.toLocaleLowerCase() !==
        this.authCode.toLocaleLowerCase()
      ) {
        this.$toast(this.$t('验证码错误'));
        this.changeAuthCode();
        return false;
      }
      let data = {
        gameId: this.gameId,
        gameTitle: this.gameName,
        reportCate: this.reportType,
        content: this.textarea,
        contact: this.contact,
        isHide: 0,
      };
      if (this.imageList.length) {
        let imageStr = this.imageList.join(',');
        data.photos = imageStr;
      }
      const res = await ApiGameFeedbackReport(data);
      this.$toast(res.msg);
      this.init();
    },
    init() {
      this.gameName = '';
      this.reportType = 0;
      this.textarea = '';
      this.contact = '';
      this.enterAuthCode = '';
      this.fileList = [];
      this.searchList = [];
      this.imageList = [];
    },
    deletePic(file, detail) {
      this.imageList.splice(detail.index, 1);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-uploader__preview-image {
  width: 106 * @rem;
  height: 106 * @rem;
}
/deep/ .van-uploader__upload {
  width: 106 * @rem;
  height: 106 * @rem;
}

main {
  overflow: hidden;
}
.my-feedback {
  color: #000;
  font-size: 14 * @rem;
}
.top-tab {
  background-color: #ffffff;
  border-bottom: 1 * @rem solid #e5e5e5;
}

.top-tab .container {
  width: 5.34rem;
  height: 1.068rem;
  margin: 0 auto;
  line-height: 1.068rem;
  font-size: 0.4272rem;
  overflow: hidden;
}

.top-tab .container .left {
  float: left;
}

.top-tab .container .right {
  float: right;
}

.top-tab .container .on {
  color: @themeColor;
}

form {
  position: relative;
  margin-bottom: 80 * @rem;
}

form .container {
  border-bottom: 0.0267rem solid #e5e5e5;
  padding: 0 0.4005rem;
  background: #fff;
  position: relative;
}

form .container .game-name {
  width: 100%;
  height: 1.2015rem;
  line-height: 1.2015rem;
  font-size: 0.4272rem;
  letter-spacing: 1 * @rem;
  color: #888888;
}

form .container .gray-text {
  height: 1.2015rem;
  line-height: 1.2015rem;
  font-size: 0.4005rem;
  letter-spacing: 1 * @rem;
  color: #000000;
}

form .container .gameList {
  width: 100%;
  max-height: calc(100vh - 6.9153rem);
  background-color: #fff;
  position: absolute;
  top: 1.2015rem;
  left: 0;
  padding: 0 0.4005rem 0.4005rem;
  z-index: 99;
  box-sizing: border-box;
  overflow-y: scroll;
}

form .container .gameList li {
  height: 1.2015rem;
  line-height: 1.2015rem;
  font-size: 0.4005rem;
  letter-spacing: 1 * @rem;
  color: #000000;
  border-bottom: 1 * @rem solid #eee;
  padding-left: 0.64rem;
  background: url(~@/assets/images/search-icon.png) no-repeat center left;
  background-size: 0.3733rem 0.3733rem;
}

form .checkbox-list {
  padding: 0.3204rem 0.4005rem;
  background-color: #ffffff;
  overflow: hidden;
}

form .gray-text {
  font-size: 16 * @rem;
}

form .checkbox-list .checkbox-item {
  position: relative;
  float: left;
  width: 50%;
  padding-top: 0.3204rem;
  font-size: 16 * @rem;
}

form .captcha {
  position: absolute;
  right: 0.4005rem;
  bottom: 0.20025rem;
  width: 2.0025rem;
  height: 0.801rem;
}
.image-list {
  font-size: 24 * @rem;
}
/deep/ .van-uploader__preview-delete {
  width: 24 * @rem;
  height: 24 * @rem;
}
/deep/ .van-uploader__preview-delete-icon {
  top: -5 * @rem;
  right: -4 * @rem;
}
/deep/ .van-uploader__preview-delete {
  width: 24 * @rem;
  height: 24 * @rem;
  border-radius: 0 0 0 30 * @rem;
}

.feedback-text {
  box-sizing: border-box;
  width: 100%;
  padding: 0.4005rem 0.4005rem 0 0.4005rem;
  background-color: #ffffff;
}

.feedback-text textarea {
  width: 100%;
  height: 2.136rem;
  padding-bottom: 0.4005rem;
  line-height: 0.534rem;
  font-size: 0.4005rem;
  letter-spacing: 1.25 * @rem;
  color: #888888;
  outline: none;
  resize: none;
  border: none;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 1.2015rem;
  margin-top: 0.267rem;
  padding: 0.3738rem;
  line-height: 0.534rem;
  font-size: 0.4005rem;
  letter-spacing: 1 * @rem;
  color: #888888;
  outline: none;
}

.bottom {
  position: fixed;
  bottom: 0;
  .fixed-center;
  width: 100%;
  height: 1.602rem;
  background-color: #ffffff;
  box-shadow: 0 -1 * @rem 2 * @rem rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.bottom .button {
  width: 6.408rem;
  height: 1.068rem;
  margin: 0.267rem auto;
  text-align: center;
  line-height: 1.068rem;
  border-radius: 5 * @rem;
  background-color: @themeColor;
  font-size: 0.4005rem;
  color: #ffffff;
}

@keyframes animation1 {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
</style>
