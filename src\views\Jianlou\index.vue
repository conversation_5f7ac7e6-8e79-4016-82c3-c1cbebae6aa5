<template>
  <div class="page jianlou-page">
    <nav-bar-2 :border="true" :title="$t('超值捡漏')">
      <template #right>
        <div class="question" @click="clickQuestion"></div>
        <div class="my-deal" @click="toPage('<PERSON><PERSON><PERSON>lou')">
          {{ $t('我的订单') }}
        </div>
      </template>
    </nav-bar-2>

    <main class="fixed-center">
      <van-tabs
        v-model="active"
        swipeable
        animated
        title-active-color="#000000"
        title-inactive-color="#797979"
        :color="themeColorLess"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :title="item.title"
        >
          <buy-list v-if="index === 0" @getAbout="getAbout"></buy-list>
          <sell-list v-else @getAbout="getAbout"></sell-list>
        </van-tab>
      </van-tabs>
    </main>

    <!-- 关于超值捡漏 -->
    <van-dialog
      v-show="about"
      v-model="introductionShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="intro-container">
        <div class="title">{{ $t('关于超值捡漏') }}</div>
        <div class="intro-content" v-html="about"></div>
        <div class="button-container">
          <div class="button-btn btn" @click="introductionShow = false">
            {{ $t('我知道了') }}
          </div>
          <div
            v-if="noLongerShow"
            class="button-btn cancel btn"
            @click="noLonger()"
          >
            {{ $t('不再提示') }}
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import BuyList from './BuyList';
import SellList from './SellList';
export default {
  name: 'Jianlou',
  components: {
    BuyList,
    SellList,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      introductionShow: false, // 介绍弹窗是否显示
      tabList: [
        {
          name: 'BuyList',
          title: this.$t('捡漏列表'),
        },
        {
          name: 'SellList',
          title: this.$t('捡漏成交动态'),
        },
      ],
      about: '',
      noLongerShow: false, // 不再提示的按钮是否显示
    };
  },
  created() {
    if (!localStorage.getItem('jianlouPopup')) {
      this.noLongerShow = true;
      this.introductionShow = true;
    }
  },
  activated() {
    if (this.$route.params.active || this.$route.params.active === 0) {
      this.active = this.$route.params.active;
    }
  },
  methods: {
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: { url: h5Page.jiaoyixuzhi, title: this.$t('交易须知') },
      });
    },
    getAbout(about) {
      this.about = about;
    },
    noLonger() {
      localStorage.setItem('jianlouPopup', 1);
      this.introductionShow = false;
    },
    clickQuestion() {
      this.noLongerShow = false;
      this.introductionShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-page {
  background-color: #f5f5f6;
  /deep/ .van-tab__pane {
    height: 100%;
  }
  .question {
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/users/help.png) center center no-repeat;
    background-size: 16 * @rem 16 * @rem;
  }
  .my-deal {
    font-size: 14 * @rem;
    color: #111111;
    margin-left: 5 * @rem;
  }
}

main {
  // box-sizing: border-box;
  height: calc(100vh - 50 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
  position: fixed;
  top: calc(50 * @rem + @safeAreaTop);
  top: calc(50 * @rem + @safeAreaTopEnv);
  left: 0;
  width: 100%;
  /deep/ .van-tab {
    font-size: 15 * @rem;
    color: #797979;
  }
  /deep/ .van-tab--active .van-tab__text {
    font-weight: 600;
  }
  /deep/ .van-tabs__line {
    height: 4 * @rem;
    width: 12 * @rem;
  }
}
main /deep/ .van-tabs.van-tabs--line {
  height: 100%;
}
main /deep/ .van-tabs--line .van-tabs__wrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
}
main /deep/ .van-tabs--line .van-tabs__content {
  box-sizing: border-box;
  height: 100%;
  padding-top: 44px;
  overflow: hidden;
}

/deep/ .van-dialog {
  width: 315 * @rem;
}
.intro-container {
  box-sizing: border-box;
  width: 315 * @rem;
  padding: 20 * @rem 16 * @rem;
  .title {
    font-size: 18 * @rem;
    color: #000000;
    text-align: center;
    font-weight: 600;
  }
  .intro-content {
    font-size: 13 * @rem;
    color: #000000;
    line-height: 22 * @rem;
    margin-top: 10 * @rem;
    .intro-p {
      span {
        color: @themeColor;
      }
    }
  }
  .button-container {
    display: flex;
    justify-content: center;
  }
  .button-btn {
    flex: 1;
    box-sizing: border-box;
    max-width: 204 * @rem;
    height: 40 * @rem;
    background: @themeBg;
    border-radius: 5 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    color: #ffffff;
    margin-top: 12 * @rem;
    font-weight: 600;
    border-radius: 20px;
    &.cancel {
      margin-left: 12 * @rem;
      background: #fff;
      color: #999999;
      border: 1px solid #dcdcdc;
    }
  }
}
</style>
