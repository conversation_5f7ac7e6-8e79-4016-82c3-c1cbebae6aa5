<template>
  <div class="pay-help-page">
    <nav-bar-2
      :title="pageTitle"
      :placeholder="false"
      bgStyle="transparent"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :azShow="true"
    >
      <template #right>
        <div class="explain-btn btn" @click="toPayHelpExplain">
          {{ $t('财富说明') }}
        </div>
      </template>
    </nav-bar-2>
    <section class="first-section">
      <div class="background">
        <div class="level">
          <div class="content">{{ userPay.pay_level_name }}</div>
          <!-- <div class="content">{{ userPay.pay_level }}富</div> -->
        </div>
      </div>
      <div class="pay-swiper" v-if="userPay.pay_level > -1">
        <swiper :options="swiperOption" class="pay-list" ref="mySwiper">
          <swiper-slide></swiper-slide>
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in payList"
            :key="index"
            class="pay-item"
          >
            <div class="level-line">
              <div
                class="line"
                :class="{
                  'line-had': item.level_id < userPay.pay_level,
                  'line-current': item.level_id == userPay.pay_level,
                  'line-first': index == 0,
                  'line-last': index == payList.length - 1,
                }"
              ></div>
              <div
                class="prize"
                :class="{ can: item.status == 0 && item.status != 2 }"
              ></div>
            </div>
            <!-- <div class="level-name">{{ item.level_id }}富</div> -->
            <div class="level-name">{{ item.level_name }}</div>
          </swiper-slide>
          <swiper-slide></swiper-slide>
        </swiper>
      </div>
    </section>
    <section class="second-section">
      <div
        class="container-wrapper"
        :class="{ 'svip-wrapper': userInfo.is_svip }"
      >
        <div class="title">
          <div class="title-text">{{ userPay.pay_level_name }}</div>
          <!-- <div class="title-text">LV：{{ userPay.pay_level }}富</div> -->
          <div class="title-btn btn" @click="toSvip" v-if="!userInfo.is_svip">
            购买
          </div>
          <div class="svip-name" @click="toSvip" v-else>SVIP会员</div>
        </div>
        <div class="subtitle">当前等级</div>
        <div class="container">
          <div class="max-bar">
            <div
              v-if="userPay.pay_sum"
              class="now-bar"
              :style="{ width: `${percentage}%` }"
            ></div>
          </div>
          <div class="content">
            <div class="left" @click="toPayHelpDetail">
              {{ $t('财富值') }}:<span id="now_sum">{{ userPay.pay_sum }}</span>
            </div>
            <div class="right">
              {{ $t('还差') }}<span id="lack_sum">{{ userPay.need_pay }}</span
              >{{ $t('财富值升级') }}
            </div>
          </div>
          <div class="tips" v-if="userInfo.is_svip">
            财富值加成：<span>{{ userPay.make_up }}</span>
          </div>
          <div class="tips" v-else>充值SVIP可以加速财富值的增长哦~</div>
        </div>
      </div>
      <div class="card-list" v-if="kefu_list.length">
        <div
          v-for="(cardItem, index) in kefu_list"
          :key="index"
          class="card-swiper-container"
        >
          <swiper
            id="cardSwiper"
            ref="cardSwiper"
            :options="cardOption[index]"
            :auto-update="true"
            style="width: 100%; margin: 0 auto"
            v-if="kefu_list.length > 0"
            class="card-swiper"
          >
            <swiper-slide
              class="card-swiper-item"
              v-for="(item, index) in [cardItem]"
              :key="index"
            >
              <div class="card-item" :key="index" v-if="item && item.level_id">
                <div class="tag" :class="{ svip: userInfo.is_svip }">
                  {{ item.is_svip ? 'SVIP福利' : '财富福利' }}
                </div>
                <div class="title">{{ item.title }}</div>
                <div class="money" v-if="item.type != 2">
                  ¥<span>{{ item.money }}</span>
                </div>
                <div class="desc" :class="{ kefu: item.type == 2 }">
                  {{ item.remark }}
                </div>
                <template v-if="item.coupon_status == 2">
                  <div class="get-btn disabled btn">
                    {{ item.level_name }}可领
                  </div>
                </template>

                <template v-else-if="item.type == 2">
                  <!-- 联系客服 -->
                  <div class="get-btn btn" @click="openKefu()">
                    前往在线客服
                  </div>
                </template>
                <template v-else-if="item.coupon_status == 0">
                  <!-- 可领取 -->
                  <div class="get-btn btn" @click="takeLevelWelfare(item)">
                    点击领取
                  </div>
                </template>
                <template v-else-if="item.coupon_status == 1">
                  <!-- 已领取 -->
                  <div class="get-btn disabled btn">已领取</div>
                </template>
              </div>
            </swiper-slide>
          </swiper>
          <div :class="`swiper-scrollbar-${index + 1}`"></div>
        </div>
      </div>
      <div
        class="card-list"
        v-if="
          (!kefu_list.length && coupon_list.length) || svip_coupon_list.length
        "
      >
        <div
          v-for="(cardItem, index) in cardList"
          :key="index"
          class="card-swiper-container"
        >
          <swiper
            id="cardSwiper"
            ref="cardSwiper"
            :options="cardOption[index]"
            :auto-update="true"
            style="width: 100%; margin: 0 auto"
            v-if="cardList[index].length > 0"
            class="card-swiper"
          >
            <swiper-slide
              class="card-swiper-item"
              v-for="(item, index) in cardItem"
              :key="index"
            >
              <div
                class="card-item"
                :key="index"
                v-if="item && item.level_name"
              >
                <div class="tag" :class="{ svip: userInfo.is_svip }">
                  {{ item.is_svip ? 'SVIP福利' : '财富福利' }}
                </div>
                <div class="title">{{ item.title }}</div>
                <div class="money" v-if="item.type != 2">
                  ¥<span>{{ item.money }}</span>
                </div>
                <div class="desc" :class="{ kefu: item.type == 2 }">
                  {{ item.remark }}
                </div>
                <template v-if="item.coupon_status == 2">
                  <div class="get-btn disabled btn">
                    {{ item.level_name }}可领
                  </div>
                </template>

                <template v-else-if="item.type == 2">
                  <!-- 联系客服 -->
                  <div class="get-btn btn" @click="openKefu()">
                    前往在线客服
                  </div>
                </template>
                <template v-else-if="item.coupon_status == 0">
                  <!-- 可领取 -->
                  <div class="get-btn btn" @click="takeLevelWelfare(item)">
                    点击领取
                  </div>
                </template>
                <template v-else-if="item.coupon_status == 1">
                  <!-- 已领取 -->
                  <div class="get-btn disabled btn">已领取</div>
                </template>
              </div>
            </swiper-slide>
          </swiper>
          <div
            :class="`swiper-scrollbar-${index + 1}`"
            v-if="cardList[index].length > 0"
          ></div>
        </div>
      </div>
      <div
        class="bottom-tips"
        v-if="coupon_list.length || svip_coupon_list.length || kefu_list.length"
      >
        <span v-if="can_get_count > 0">{{ can_get_count }}张待领取，</span
        >领取后可在我的-代金券中查看
      </div>

      <!-- 查询可使用3733代金券的游戏 -->
      <div class="btn game-popup-btn" @click="clickNoCouponGame">
        查询3733代金券可使用的游戏 &gt;
      </div>
    </section>
    <section class="third-section">
      <div class="title">{{ $t('如何获取财富值') }}</div>
      <ul class="list">
        <li class="item">
          {{ $t('购买游戏盒SVIP') }}
          <div @click="toSvip" class="right-btn btn">{{ $t('充值') }}</div>
        </li>
        <li class="item">
          {{ $t('充值平台币') }}
          <div @click="toPlatformCoin" class="right-btn btn">
            {{ $t('充值') }}
          </div>
        </li>
      </ul>
      <div class="tips">
        注：RMB对财富值的比例为1:10，即平台币充值1元可获得10财富值。<br />
        自2025年7月1日起，获取财富值途径变为：<br />
        1.购买SVIP <br />
        2.充平台币 <br />
        SVIP享受代金券与金币抵扣比例提升福利时，需“会员剩余天数”>30天,否则即使为SVIP也无法享受以上福利
      </div>
    </section>
    <van-dialog
      v-model="svipNoEnoughPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :close-on-click-overlay="true"
      class="svip-no-enough-popup"
    >
      <div class="popup-content">您的会员日期不足30天，续费后可领取奖励</div>
      <div class="popup-operation">
        <div @click="closeRecharge" class="more btn">取消</div>
        <div @click="rechargeSvip" class="recharge btn">充值SVIP</div>
        <!-- <div @click="rechargeSvip30" class="recharge btn">充值30天SVIP</div> -->
      </div>
    </van-dialog>
    <svip-recharge-popup
      @success="rechargeSuccess"
      :show.sync="svipRechargePopupShow"
      :fastPay="fastPay"
      :title="`开通${fastPay ? fastPay + '天' : ''}SVIP会员`"
    ></svip-recharge-popup>

    <!-- 不可用代金券的游戏弹窗 -->
    <coupon-game-dialog :show.sync="noCouponPopupShow"></coupon-game-dialog>
  </div>
</template>
<script>
import {
  ApiUserPayRankV1,
  ApiUserTakeLevel,
  ApiUserLevelWelfare,
} from '@/api/views/users';
import {
  BOX_openInNewWindow,
  BOX_openInNewNavWindow,
  platform,
} from '@/utils/box.uni.js';
import svipRechargePopup from '@/components/svip-recharge-popup';

export default {
  components: {
    svipRechargePopup,
  },
  data() {
    return {
      pageTitle: this.$t('财富等级'),
      userPay: {},
      payList: [],
      navbarOpacity: 0,
      coupon_list: [],
      kefu_list: [],
      svip_coupon_list: [],
      can_get_count: 0, // 待领取的代金券数量
      svipNoEnoughPopupShow: false, // svip不足30天时的弹窗
      svipRechargePopupShow: false, // svip充值弹窗显示
      fastPay: 30, // 快速充值（直接确定是充值30天） 0为普通充值弹窗
      svipRechargePopupTitle: '开通SVIP会员',
      cardOption: [
        {
          freeModeMomentumRatio: 0.3, // 运动惯量
          scrollbar: {
            el: '.swiper-scrollbar-1',
          },
        },
        {
          freeModeMomentumRatio: 0.3, // 运动惯量
          scrollbar: {
            el: '.swiper-scrollbar-2',
          },
        },
      ],
      noCouponPopupShow: false,
    };
  },
  computed: {
    percentage() {
      let percent = Math.floor(
        (parseInt(this.userPay.pay_sum) / parseInt(this.userPay.pay_next)) *
          100,
      );
      return percent > 100 ? 100 : percent;
    },
    cardList() {
      return [this.coupon_list, this.svip_coupon_list];
    },
    swiperOption() {
      let _this = this;
      return {
        observer: true,
        observeParents: true,
        slidesPerView: 3,
        speed: 200,
        initialSlide: _this.userPay.pay_level,
        slideToClickedSlide: false,
        on: {
          slideChange: async function () {
            await _this.getLevelWelfare(this.activeIndex);
          },
          click: function () {
            this.slideTo(this.clickedIndex - 1, 300);
          },
        },
      };
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = this.pageTitle;
    }
    window.addEventListener('scroll', this.handleScroll);
    await this.init();

    // 神策埋点
    this.$sensorsTrack('wealthPrivilege_pageView');
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async clickNoCouponGame() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      this.noCouponPopupShow = true;
      this.$toast.clear();
    },
    toPayHelpDetail() {
      BOX_openInNewNavWindow(
        { name: 'PayHelpDetail' },
        { url: this.$h5Page.caifudengjiDetail },
      );
    },
    toPayHelpExplain() {
      BOX_openInNewNavWindow(
        { name: 'PayHelpExplain' },
        { url: this.$h5Page.caifudengjiExp },
      );
    },
    toSvip() {
      BOX_openInNewWindow(
        {
          name: 'Svip',
          params: {
            sensors_source: this.pageTitle,
          },
        },
        { url: this.$h5Page.svip_url },
      );
    },
    toPlatformCoin() {
      BOX_openInNewWindow(
        {
          name: 'PlatformCoin',
          params: {
            sensors_source: this.pageTitle,
          },
        },
        { url: this.$h5Page.ptb_url },
      );
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async init() {
      const res = await ApiUserPayRankV1();
      this.payList = res.data.pay_rank;
      this.userPay = res.data.user_pay;
    },
    async getLevelWelfare(levelId) {
      const res = await ApiUserLevelWelfare({ levelId });
      const { coupon_list, kefu_list, svip_coupon_list } = res.data.list;
      this.coupon_list = coupon_list ?? [];
      this.kefu_list = kefu_list ?? [];
      this.svip_coupon_list = svip_coupon_list ?? [];
      this.can_get_count = res.data.count_num ?? 0;
    },
    async takeLevelWelfare(item) {
      // 当用户是svip，并且svip天数不足30天， 并且领取的该代金券是svip专属代金券时，弹出充值弹窗在本页充值
      if (
        this.userInfo.is_svip &&
        this.userPay.surplus_day < 30 &&
        item.is_svip == 1
      ) {
        this.svipNoEnoughPopupShow = true;
        return false;
      }
      // 非svip用户领取svip的代金券时直接通过后端接口返回的code跳转svip页面了
      const res = await ApiUserTakeLevel({
        levelId: item.level_id,
        couponId: item.coupon_id,
        isSvip: item.is_svip,
      });
      await this.init();
      this.getLevelWelfare(item.level_id);
    },
    // 充值svip弹窗
    rechargeSvip() {
      this.svipNoEnoughPopupShow = false;
      this.fastPay = 0;
      this.$nextTick(() => {
        // 关闭svip弹窗，跳转到svip 2023年11月1日09:31:29
        // this.svipRechargePopupShow = true;
        this.toSvip();
      });
    },
    // 关闭svip弹窗，跳转到svip 2023年11月1日09:31:29
    closeRecharge() {
      this.svipNoEnoughPopupShow = false;
    },
    // 充值30天svip弹窗
    rechargeSvip30() {
      this.svipNoEnoughPopupShow = false;
      this.fastPay = 30;
      this.$nextTick(() => {
        this.svipRechargePopupShow = true;
      });
    },
    // 充值成功
    async rechargeSuccess() {
      await this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.pay-help-page {
  .explain-btn {
    font-size: 14 * @rem;
    color: #000000;
  }
  .first-section {
    position: relative;
    height: 340 * @rem;
    width: 100%;
    &::after {
      content: '';
      width: 47 * @rem;
      height: 12 * @rem;
      background: url(~@/assets/images/users/tuqi.png) no-repeat;
      background-size: 47 * @rem 12 * @rem;
      position: absolute;
      bottom: -1 * @rem;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
    .background {
      width: 100%;
      height: 340 * @rem;
      background: linear-gradient(360deg, #c1b3ff 0%, #94aeff 100%);
      overflow: hidden;
      .level {
        position: relative;
        width: 210 * @rem;
        height: 205 * @rem;
        margin: 62 * @rem auto 0;
        background-size: 210 * @rem 205 * @rem;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/users/pay_help_level_new.png);
        position: relative;
        .content {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 128 * @rem;
          width: 100 * @rem;
          height: 20 * @rem;
          text-align: center;
          line-height: 20 * @rem;
          font-size: 14 * @rem;
          font-weight: bold;
          color: @themeColor;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .pay-swiper {
      width: 100%;
      height: 80 * @rem;
      position: absolute;
      left: 0;
      bottom: 20 * @rem;
      .pay-list {
        width: 100%;
        height: 80 * @rem;
        .pay-item {
          width: 33.33%;
          height: 80 * @rem;
          .level-line {
            width: 100%;
            height: 50 * @rem;
            position: relative;
            .line {
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 0;
              width: 100%;
              height: 6 * @rem;
              background-color: #a898ff;
              &.line-had {
                background-color: #ffffff;
              }
              &.line-current {
                &::before {
                  content: '';
                  display: block;
                  width: 50%;
                  height: 100%;
                  position: absolute;
                  left: 0;
                  top: 0;
                  background-color: #ffffff;
                  z-index: 2;
                }
              }
              &.line-first {
                width: 50%;
                left: 50%;
                &::before {
                  display: none;
                }
              }
              &.line-last {
                width: 50%;
              }
              &.line-last.line-current {
                &::before {
                  width: 100%;
                }
              }
            }
            .prize {
              width: 16 * @rem;
              height: 16 * @rem;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              border-radius: 50%;
              background-color: #fff;
              box-shadow: 0 0 0 6 * @rem rgba(255, 255, 255, 0.2);
              &.can {
                &::after {
                  content: '';
                  width: 8 * @rem;
                  height: 8 * @rem;
                  background-color: @themeColor;
                  border-radius: 50%;
                  position: absolute;
                  right: -8 * @rem;
                  top: -8 * @rem;
                }
              }
            }
          }
          .level-name {
            text-align: center;
            font-size: 15 * @rem;
            color: #3e4688;
            line-height: 21 * @rem;
          }
        }
      }
    }
  }
  .second-section {
    padding: 12 * @rem 18 * @rem 18 * @rem;
    position: relative;
    .zhuanyekefu {
      margin-top: 20 * @rem;
    }
    .card-list {
      display: flex;
      justify-content: space-between;
      margin-top: 12 * @rem;
      .card-swiper-container {
        width: 160 * @rem;
        .card-swiper {
          box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
          border-radius: 10 * @rem;
          border: 1 * @rem solid #f5f5f5;
        }
        .swiper-scrollbar-1,
        .swiper-scrollbar-2 {
          margin: 13 * @rem auto 0;
          width: 32 * @rem;
          height: 4 * @rem;
          background: #f5f5f5;
        }
        /deep/ .swiper-scrollbar-drag {
          background: @themeColor;
        }
      }
      .card-item {
        box-sizing: border-box;
        position: relative;
        width: 160 * @rem;
        height: 185 * @rem;
        overflow: hidden;
        border-radius: 10 * @rem;
        border: 1 * @rem solid #f5f5f5;
        .tag {
          position: absolute;
          left: 0;
          top: 0;
          width: 73 * @rem;
          height: 28 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(229deg, #d8d7ff 0%, #c1b4ff 100%);
          font-size: 13 * @rem;
          color: #3e4688;
          font-weight: bold;
          border-radius: 10 * @rem 0 10 * @rem 0;
          &.svip {
            color: #aa5e4d;
            background: linear-gradient(229deg, #efe7fe 0%, #ffd2d3 100%);
          }
        }
        .title {
          font-size: 10 * @rem;
          text-indent: 14 * @rem;
          color: #666666;
          margin-top: 39 * @rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .money {
          font-size: 20 * @rem;
          line-height: 45 * @rem;
          margin-top: 12 * @rem;
          text-align: center;
          color: @themeColor;
          span {
            font-size: 24 * @rem;
            font-weight: bold;
          }
        }
        .desc {
          font-size: 12 * @rem;
          color: #444444;
          text-align: center;
          line-height: 17 * @rem;
          margin-top: 1 * @rem;
          padding: 0 10 * @rem;
          height: 17 * @rem;
          overflow: hidden;
          &.kefu {
            height: 51 * @rem;
            margin-top: 20 * @rem;
            display: -webkit-box;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
          }
        }
        .get-btn {
          width: 104 * @rem;
          height: 24 * @rem;
          border-radius: 12 * @rem;
          background-color: @themeColor;
          font-size: 12 * @rem;
          color: #ffffff;
          display: block;
          text-align: center;
          line-height: 24 * @rem;
          margin: 16 * @rem auto 0;
          &.disabled {
            background-color: #dcdde1;
          }
        }
      }
    }
    .container-wrapper {
      box-sizing: border-box;
      width: 339 * @rem;
      height: 162 * @rem;
      background: linear-gradient(180deg, #e9e8ff 0%, #d2d0ff 100%);
      border-radius: 12 * @rem;
      box-shadow: 0 * @rem 2 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.05);
      padding: 15 * @rem 28 * @rem 14 * @rem;
      &.svip-wrapper {
        background: url(~@/assets/images/users/caifu-card.png);
        background-size: 339 * @rem 162 * @rem;
      }
      .title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-text {
          color: #3c437e;
          font-weight: bold;
          font-size: 18 * @rem;
          line-height: 25 * @rem;
        }
        .title-btn {
          width: 50 * @rem;
          height: 25 * @rem;
          background-color: @themeColor;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 12 * @rem;
          border-radius: 13 * @rem;
        }
        .svip-name {
          font-size: 17 * @rem;
          color: #5a4aad;
          font-weight: 500;
        }
      }
      .subtitle {
        font-size: 11 * @rem;
        color: #292e59;
        line-height: 15 * @rem;
        margin-top: 5 * @rem;
      }
      .container {
        width: 283 * @rem;
        margin: 20 * @rem auto 0;
        .max-bar {
          width: 100%;
          height: 8 * @rem;
          background-color: #ffffff;
          border-radius: 4 * @rem;
          .now-bar {
            width: 0%;
            height: 8 * @rem;
            background-color: @themeColor;
            border-radius: 4 * @rem;
          }
        }
        .content {
          margin-top: 5 * @rem;
          color: #666666;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 11 * @rem;
          line-height: 15 * @rem;
          color: #9a9a9a;
          span {
            color: @themeColor;
          }
        }
        .tips {
          color: #333333;
          font-size: 13 * @rem;
          line-height: 18 * @rem;
          margin-top: 21 * @rem;
          span {
            color: @themeColor;
          }
        }
      }
    }
    .bottom-tips {
      text-align: center;
      font-size: 11 * @rem;
      color: bottom-tips;
      line-height: 15 * @rem;
      margin-top: 20 * @rem;
      color: #666666;
    }
    .game-popup-btn {
      width: 267 * @rem;
      height: 42 * @rem;
      border: 1 * @rem solid @themeColor;
      border-radius: 32 * @rem;
      margin: 18 * @rem auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      color: @themeColor;
    }
  }
  .third-section {
    padding: 17 * @rem 18 * @rem 30 * @rem;
    background: #ffffff;
    border-top: 8 * @rem solid #f5f5f6;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .list {
      margin-top: 7 * @rem;
      .item {
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 47 * @rem;
        padding-left: 9 * @rem;
        font-size: 14 * @rem;
        color: #000000;
        border-bottom: 0.5 * @rem solid #e8e8e8;
        &::before {
          content: '';
          width: 4 * @rem;
          height: 4 * @rem;
          border-radius: 50%;
          background-color: @themeColor;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        span {
          color: @themeColor;
        }
        .right-btn {
          height: 25 * @rem;
          padding: 0 14 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: @themeColor;
          font-size: 12 * @rem;
          color: #ffffff;
          border-radius: 13 * @rem;
        }
      }
    }
    .tips {
      box-sizing: border-box;
      position: relative;
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      color: #9a9a9a;
      margin-top: 14 * @rem;
    }
  }
}
.svip-no-enough-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  background: #fff;
  border-radius: 20 * @rem;
  padding: 36 * @rem 0 18 * @rem;
  .popup-content {
    font-size: 15 * @rem;
    color: #333333;
    line-height: 21 * @rem;
    text-align: center;
    padding: 0 36 * @rem;
  }
  .popup-operation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35 * @rem;
    margin-top: 29 * @rem;
    .more,
    .recharge {
      margin: 0 10 * @rem;
      width: 110 * @rem;
      height: 35 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #7d7d7d;
      background-color: #f2f2f2;
      font-size: 13 * @rem;
      border-radius: 18 * @rem;
    }
    .recharge {
      color: #fff;
      background-color: @themeColor;
    }
  }
}
</style>
