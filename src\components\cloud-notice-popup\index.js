import Vue from 'vue';
import cloudNoticePopupComponent from './index.vue';
import router from '@/router';
import store from '@/store';

const cloudNoticePopupConstructor = Vue.extend(cloudNoticePopupComponent);

function useCloudNoticePopup(options) {
    return new Promise((resolve, reject) => {
        const cloudNoticePopup = new cloudNoticePopupConstructor({
            router,
            store,
        });

        cloudNoticePopup.$mount(document.createElement('div'));
        document.body.appendChild(cloudNoticePopup.$el);

        cloudNoticePopup.$el.addEventListener(
            'animationend',
            () => {
                if (cloudNoticePopup.show == false) {
                    cloudNoticePopup.$destroy();
                    cloudNoticePopup.$el.parentNode.removeChild(cloudNoticePopup.$el);
                }
            },
            false,
        );

        cloudNoticePopup.show = true;
        cloudNoticePopup.content = options.content;
    });
}

export default useCloudNoticePopup;
