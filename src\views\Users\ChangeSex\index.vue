<template>
  <div class="change-sex-page page">
    <nav-bar-2 :border="true" :title="$t('性别选择')">
      <template #right>
        <div class="save" @click="save">{{ $t('完成') }}</div>
      </template>
    </nav-bar-2>
    <div class="sex-container">
      <div class="sex-list">
        <div class="sex-item" @click="selectSex(2)">
          <div class="name">{{ $t('男') }}</div>
          <div class="gou" :class="{ active: current === 2 }"></div>
        </div>
        <div class="sex-item" @click="selectSex(1)">
          <div class="name">{{ $t('女') }}</div>
          <div class="gou" :class="{ active: current === 1 }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiChangeInfoEx } from '@/api/views/users';
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'ChangeSex',
  data() {
    return {
      sexList: [
        {
          name: this.$t('男'),
          id: 2,
        },
        {
          name: this.$t('女'),
          id: 1,
        },
      ],
      current: 1,
    };
  },
  computed: {
    ...mapGetters({
      userInfoEx: 'user/userInfoEx',
    }),
  },
  mounted() {
    if (parseInt(this.userInfoEx.sex) === 2) {
      this.current = 2;
    }
  },
  methods: {
    ...mapMutations({
      setUserInfoEx: 'user/setUserInfoEx',
    }),
    selectSex(index) {
      this.current = index;
    },
    save() {
      ApiChangeInfoEx({ sex: this.current }).then(res => {
        this.setUserInfoEx(res.data);
        this.$toast(res.msg);
        this.$router.go(-1);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.change-sex-page {
  background-color: #f6f6f6;
  .save {
    font-size: 15 * @rem;
    color: #000;
  }
  .sex-container {
    width: 100%;
    .sex-list {
      padding: 0 14 * @rem;
      background-color: #fff;
      .sex-item {
        border-top: 1px solid #eeeeee;
        height: 55 * @rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &:nth-of-type(1) {
          border-top: 0;
        }
        .name {
          font-size: 15 * @rem;
          color: #333333;
        }
        .gou {
          width: 20 * @rem;
          height: 15 * @rem;
          &.active {
            background: url(~@/assets/images/users/sex-gou.png) no-repeat;
            background-size: 20 * @rem 15 * @rem;
          }
        }
      }
    }
  }
}
</style>
