export default [
  {
    path: '/first_coupon',
    name: 'FirstCoupon',
    component: () =>
      import(/* webpackChunkName: "active" */ '@/views/Active/FirstCoupon'),
  },
  {
    path: '/active/game_snxyj',
    name: 'GameSnxyj',
    component: () =>
      import(/* webpackChunkName: "active" */ '@/views/Active/GameSnxyj'),
  },
  {
    path: '/exclusive_activity/:id',
    name: 'ExclusiveActivity',
    component: () =>
      import(
        /* webpackChunkName: "active" */ '@/views/Active/ExclusiveActivity'
      ),
    meta: {
      keepAlive: true,
      pageTitle: '专服活动',
    },
  },
  {
    path: '/game_down_activity/:id',
    name: 'GameDownActivity',
    component: () =>
      import(
        /* webpackChunkName: "active" */ '@/views/Active/GameDownActivity'
      ),
    meta: {
      keepAlive: false,
    },
  },
  {
    path: '/game_down_rebate_apply/:type',
    name: 'GameDownRebateApply',
    component: () =>
      import(
        /* webpackChunkName: "active" */ '@/views/Active/GameDownActivity/GameDownRebateApply'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: false,
    },
  },
  {
    path: '/game_down_rebate_record',
    name: 'GameDownRebateRecord',
    component: () =>
      import(
        /* webpackChunkName: "active" */ '@/views/Active/GameDownActivity/GameDownRebateRecord'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/game_down_rebate_detail',
    name: 'GameDownRebateDetail',
    component: () =>
      import(
        /* webpackChunkName: "active" */ '@/views/Active/GameDownActivity/GameDownRebateDetail'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: false,
    },
  },
];
