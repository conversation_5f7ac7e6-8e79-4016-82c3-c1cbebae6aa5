<template>
  <!-- 复制礼包弹窗 -->
  <van-popup
    class="success-popup"
    v-model="popupShow"
    :close-on-click-overlay="false"
    :lock-scroll="false"
    position="bottom"
    round
  >
    <div class="popup-close" @click="popupShow = false"></div>
    <div class="title-text">复制成功</div>
    <div class="title-tips">请前往游戏内使用</div>
    <div class="card-title">礼包名称：{{ info.title }}</div>
    <div class="desc">{{ introduction }}</div>
    <div class="btn-group">
      <div class="close-btn btn" @click="popupShow = false"> 我知道了 </div>
      <div class="copy-btn btn" @click="copy648(info)"> 启动游戏 </div>
    </div>
  </van-popup>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
    introduction() {
      if (this.info.cardtext) {
        return `使用方法：${this.info.cardtext}`;
      } else {
        return `使用方法：请在游戏中使用`;
      }
    },
  },
  methods: {
    copy648(info) {
      this.popupShow = false;
      this.$emit('close-popup');
      setTimeout(() => {
        BOX_goToGame(
          {
            params: {
              id: info.game_id,
              gameInfo: info.game,
            },
          },
          { id: info.game_id },
        );
      }, 0);
    },
  },
};
</script>

<style lang="less" scoped>
.success-popup {
  box-sizing: border-box;
  background-color: #fff;
  padding: 24 * @rem 18 * @rem 111 * @rem;

  .popup-close {
    width: 20 * @rem;
    height: 20 * @rem;
    background: url(~@/assets/images/close-dialog.png) center no-repeat;
    background-size: 13 * @rem 13 * @rem;
    position: absolute;
    top: 14 * @rem;
    right: 14 * @rem;
  }
  .title-text {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #111111;
    line-height: 22 * @rem;
  }

  .title-tips {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12 * @rem;
    color: #999999;
    line-height: 15 * @rem;
    margin-top: 8 * @rem;
  }
  .card-title {
    height: 18 * @rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14 * @rem;
    color: #666666;
    line-height: 18 * @rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 24 * @rem;
  }

  .desc {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14 * @rem;
    color: #666666;
    line-height: 18 * @rem;
    margin-top: 10 * @rem;
  }
  .btn-group {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 64 * @rem;
    background: #ffffff;
    box-shadow: 0 * @rem 1 * @rem 6 * @rem 0 * @rem rgba(25, 27, 31, 0.1);
    padding: 13 * @rem 18 * @rem;
    box-sizing: border-box;

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 126 * @rem;
      height: 38 * @rem;
      line-height: 38 * @rem;
      background: #f2f2f2;
      border-radius: 20 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 15 * @rem;
      color: #666666;
      line-height: 15 * @rem;
      text-align: center;
    }

    .copy-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 189 * @rem;
      height: 38 * @rem;
      background: @themeBg;
      border-radius: 20 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 15 * @rem;
      text-align: center;
    }
  }
}
</style>
