<template>
  <div class="page svip-discount-page" v-if="info.svip">
    <nav-bar-2
      :placeholder="false"
      bgStyle="transparent"
      :azShow="true"
      :title="info.head_title"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="main">
      <div
        class="top-bg"
        :class="{ hw: isHw }"
        :style="{ backgroundImage: `url(${info.head_img})` }"
      >
        <div
          class="user-bar"
          :style="{ backgroundImage: `url(${info.head_img2})` }"
        >
          <div class="user-info">
            <user-avatar
              class="avatar"
              :self="true"
              :src="mem.avatar"
            ></user-avatar>
            <div class="nickname">{{ mem.nickname }}</div>
          </div>
          <div class="bottom-desc" v-html="info.head_text"></div>
        </div>
      </div>
      <div class="svip-container">
        <div class="svip-title">{{ info.svip.title }}</div>
        <div class="card-content">
          <div class="tag">{{ info.svip.discounts_text }}</div>
          <div class="left">
            <div class="card-title">{{ info.svip.name }}</div>
            <div class="card-desc">
              {{ $t('开通立返') }}<span>{{ info.svip.describe }}</span
              >{{ $t('金币') }}
            </div>
          </div>
          <div class="unit-money">{{ text_list.pay_symbol }}</div>
          <div class="now-money">{{ info.svip.amount }}</div>
          <div class="old-money">
            {{ text_list.pay_symbol }}{{ info.svip.show_amount }}
          </div>
        </div>
        <div class="explain-list">
          <div
            class="explain-item"
            v-for="(item, index) in info.describe"
            :key="index"
          >
            <div class="explain-tag" v-if="index == 0">
              <div class="left">{{ $t('惊喜券') }}</div>
              <div class="right">{{ $t('已生效') }}</div>
            </div>
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-container">
      <div class="btn confirm-btn" @click="clickRechargeBtn">
        {{ $t('确定并支付') }}{{ text_list.pay_symbol }}{{ info.svip.amount }}
      </div>
      <div class="confirm-tip">
        {{ $t('已减') }}{{ text_list.pay_symbol
        }}{{ info.svip.discounts_amount }} {{ $t('查看')
        }}<span @click="discountDetailShow = true">{{ $t('优惠明细') }}</span>
      </div>
    </div>

    <!-- 优惠明细弹窗 -->
    <van-popup
      v-model="discountDetailShow"
      position="bottom"
      :lock-scroll="false"
      round
      class="discount-detail-popup"
      :close-on-popstate="true"
    >
      <div class="close" @click="discountDetailShow = false"></div>
      <div class="discount-title">{{ $t('优惠明细') }}</div>
      <div class="discount-section">
        <div class="section-title">{{ $t('商品') }}</div>
        <div class="section-bottom">
          <span>{{ info.svip.title }}</span>
          <span>{{ text_list.pay_symbol }}{{ info.svip.show_amount }}</span>
        </div>
      </div>
      <div class="discount-section">
        <div class="section-title">{{ $t('优惠') }}</div>
        <div class="section-bottom">
          <span>{{ $t('商品活动优惠') }}</span>
          <span class="red"
            >-{{ text_list.pay_symbol }}{{ info.svip.discounts_amount }}</span
          >
        </div>
      </div>
      <div class="discount-section">
        <div class="section-title">
          <span>{{ $t('合计支付') }}</span>
          <span>{{ text_list.pay_symbol }}{{ info.svip.amount }}</span>
        </div>
      </div>
    </van-popup>
    <pay-type-popup
      :show.sync="selectPayWayShow"
      :list="payWayList"
      @choosePayType="choosePayType"
      :money="info.svip.amount"
      :unit="text_list.pay_symbol"
    ></pay-type-popup>
    <!-- 支付弹窗抽屉 -->
    <!-- <van-popup
      v-model="payPopupShow"
      position="bottom"
      :lock-scroll="false"
      round
      class="pay-container-popup"
      :close-on-popstate="true"
    >
      <div class="pay-container">
        <div class="pay-way-title">{{ $t('选择支付方式') }}</div>
        <div class="pay-list">
          <div
            class="pay-item"
            v-for="(item, index) in payArr"
            :key="index"
            @click="selectedPayType = item.key"
          >
            <div
              class="icon"
              :style="{ backgroundImage: `url(${item.icon})` }"
            ></div>
            <div class="pay-center">
              <div class="line">
                <div class="pay-name">{{ item.name }}</div>
                <div
                  class="recommend-icon"
                  v-if="item.tag_img"
                  :style="{ backgroundImage: `url(${item.tag_img})` }"
                ></div>
              </div>
              <div v-if="item.subtitle" class="subtitle">
                {{ item.subtitle }}
              </div>
            </div>
            <div
              class="choose"
              :class="{ on: selectedPayType == item.key }"
            ></div>
          </div>
        </div>
        <div class="pay-btn btn" @click="handlePay">
          {{ $t('确认支付') }}{{ text_list.pay_symbol }}{{ info.svip.amount }}
        </div>
      </div>
    </van-popup> -->
  </div>
</template>

<script>
import {
  ApiSvipHideDiscounts,
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGetPaymentMethod
} from '@/api/views/recharge.js';
export default {
  data() {
    return {
      navbarOpacity: 0,
      info: {},
      mem: {},
      payArr: [],
      text_list: {}, // 一些翻译的文案
      payWayList:[],
      discountDetailShow: false, // 优惠明细弹窗
      selectPayWayShow:false,
      payPopupShow: false,
      selectedPayType: 'wx', // 支付方式
    };
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.getIndexData();
    await this.getPayMethod();
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payWayList = res.data;
      this.selectedPayType = this.payWayList[0];
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async getIndexData() {
      const res = await ApiSvipHideDiscounts();
      this.info = res.data.info;
      this.mem = res.data.mem;
      this.payArr = res.data.payArr;
      this.text_list = res.data.text_list;
      this.selectedPayType = this.payArr[0].key;
      console.log(this.info);
    },
    clickRechargeBtn() {
      this.selectPayWayShow = true;
    },
    handlePay() {
      this.selectPayWayShow = false;
      const orderParams = {
        day: this.info.svip.day,
        amount: this.info.svip.amount,
        rebate_gold: this.info.svip.describe,
        payWay: this.selectedPayType,
        is_cycle: 0,
        is_hide_discounts: 1,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        await ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.selectedPayType,
          packageName: '',
        });
        await this.getIndexData();
        this.back();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.svip-discount-page {
  padding-bottom: 150 * @rem;
  .main {
    .top-bg {
      padding-top: 0.5 * @rem;
      position: relative;
      height: 310 * @rem;
      background-image: url('~@/assets/images/recharge/svip-discount-bg-new.png');
      background-repeat: no-repeat;
      background-size: 100% 310 * @rem;
      &.hw {
        background-image: url('~@/assets/images/recharge/svip-discount-bg-new-hw.png');
      }
      .user-bar {
        box-sizing: border-box;
        margin: 145 * @rem auto 0;
        width: 335 * @rem;
        height: 157 * @rem;
        background-image: url(~@/assets/images/recharge/svip-discount-user-bar-new.png);
        background-size: 335 * @rem 157 * @rem;
        padding: 1 * @rem 14 * @rem 0;
        .user-info {
          display: flex;
          align-items: center;
          margin-top: 21 * @rem;
          .avatar {
            width: 34 * @rem;
            height: 34 * @rem;
            border: 1 * @rem solid rgba(255, 255, 255, 0.2);
          }
          .nickname {
            font-size: 14 * @rem;
            color: #8f5016;
            margin-left: 8 * @rem;
            flex: 1;
            min-width: 0;
            font-weight: 600;
          }
        }
        .bottom-desc {
          font-size: 12 * @rem;
          color: #8f5016;
          font-weight: 600;
          margin-top: 14 * @rem;
          line-height: 15 * @rem;
          /deep/ span {
            color: #ff581e;
            font-weight: 600 !important;
          }
        }
      }
    }
    .svip-container {
      margin-top: -61 * @rem;
      background-color: #fff;
      position: relative;
      padding: 0 20 * @rem;
      border-radius: 16 * @rem 16 * @rem 0 0;
      .svip-title {
        font-size: 18 * @rem;
        color: #6d5ec9;
        line-height: 23 * @rem;
        padding-top: 19 * @rem;
        font-weight: 600;
      }
      .card-content {
        box-sizing: border-box;
        margin: 14 * @rem auto 0;
        position: relative;
        width: 335 * @rem;
        height: 80 * @rem;
        background: linear-gradient(161deg, #faf4ff 0%, #f5eeff 100%);
        border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
        opacity: 1;
        border: 2 * @rem solid #dddaff;
        display: flex;
        align-items: center;
        padding: 0 20 * @rem;

        .tag {
          box-sizing: border-box;
          position: absolute;
          width: 90 * @rem;
          height: 22 * @rem;
          right: -11 * @rem;
          top: -8 * @rem;
          background: url(~@/assets/images/recharge/svip-discount-card-tag.png)
            no-repeat;
          background-size: 90 * @rem 22 * @rem;
          display: flex;
          align-items: center;
          padding-left: 8 * @rem;
          font-size: 13 * @rem;
          color: #ffffff;
          font-weight: 600;
        }

        .left {
          flex: 1;
          min-width: 0;
          .card-title {
            font-size: 16 * @rem;
            font-weight: 600;
            color: #6d5ec9;
            line-height: 20 * @rem;
          }
          .card-desc {
            font-size: 11 * @rem;
            color: #643cc9;
            line-height: 14 * @rem;
            margin-top: 6 * @rem;
            span {
              color: #ff591f;
            }
          }
        }
        .unit-money {
          font-size: 14 * @rem;
          color: #ff591f;
          font-weight: 600;
          line-height: 18 * @rem;
          margin-top: 10 * @rem;
          margin-right: 3 * @rem;
        }
        .now-money {
          font-size: 28 * @rem;
          color: #ff471f;
          font-weight: 600;
        }
        .old-money {
          font-size: 14 * @rem;
          color: rgba(181, 170, 255, 0.6);
          line-height: 18 * @rem;
          text-decoration: line-through;
          margin-left: 4 * @rem;
          margin-top: 10 * @rem;
        }
      }
      .explain-list {
        margin-top: 20 * @rem;
        .explain-item {
          display: flex;
          align-items: center;
          margin-bottom: 12 * @rem;
          position: relative;
          padding-left: 12 * @rem;
          &::before {
            content: '';
            width: 4 * @rem;
            height: 4 * @rem;
            border-radius: 2 * @rem;
            background: #805e46;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
          .explain-tag {
            width: 118 * @rem;
            height: 23 * @rem;
            background: url(~@/assets/images/recharge/svip-discount-explain-bg.png)
              no-repeat;
            background-size: 118 * @rem 23 * @rem;
            display: flex;
            align-items: center;
            margin-right: 6 * @rem;
            .left {
              flex: 1;
              text-align: center;
              font-size: 13 * @rem;
              color: #ffffff;
              font-weight: 600;
            }
            .right {
              flex: 1;
              text-align: center;
              font-size: 13 * @rem;
              color: #6a55a7;
              font-weight: 600;
            }
          }
          span {
            font-size: 14 * @rem;
            color: #6a55a7;
            line-height: 18 * @rem;
          }
        }
      }
    }
  }
  .bottom-container {
    position: fixed;
    bottom: 0;
    .fixed-center;
    padding-bottom: 42 * @rem;
    .confirm-btn {
      width: 335 * @rem;
      height: 50 * @rem;
      background: #895ff6;
      box-shadow: 0 * @rem 4 * @rem 8 * @rem 0 * @rem #e1dcff;
      border-radius: 32 * @rem 32 * @rem 32 * @rem 32 * @rem;
      margin: 10 * @rem auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18 * @rem;
      color: #ffffff;
      font-weight: 600;
    }
    .confirm-tip {
      font-size: 13 * @rem;
      color: #6a55a7;
      line-height: 16 * @rem;
      margin-top: 17 * @rem;
      text-align: center;
      span {
        color: #ff591f;
        text-decoration: underline;
      }
    }
  }
}

.discount-detail-popup {
  box-sizing: border-box;
  padding: 30 * @rem 20 * @rem 20 * @rem;
  .close {
    position: absolute;
    right: 0;
    top: 0;
    width: 48 * @rem;
    height: 48 * @rem;
    background: url(~@/assets/images/recharge/svip-ad-close.png) center center
      no-repeat;
    background-size: 16 * @rem 16 * @rem;
  }
  .discount-title {
    font-size: 18 * @rem;
    color: #333333;
    line-height: 23 * @rem;
    font-weight: 600;
    text-align: center;
  }
  .discount-section {
    padding: 16 * @rem 0;
    &:not(:last-of-type) {
      border-bottom: 1 * @rem solid rgba(237, 237, 237, 0.71);
    }
    .section-title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      display: flex;
      justify-content: space-between;
      line-height: 20 * @rem;
      span {
        font-weight: 600;
      }
    }
    .section-bottom {
      font-size: 14 * @rem;
      color: #666666;
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 18 * @rem;
      margin-top: 12 * @rem;
      span {
        &.red {
          color: #f22727;
        }
      }
    }
  }
}

.pay-container-popup {
  .pay-container {
    padding: 10 * @rem 14 * @rem 0;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    .pay-way-title {
      font-size: 16 * @rem;
      color: #333;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .pay-list {
      .pay-item {
        display: flex;
        align-items: center;
        padding: 16 * @rem 0;
        border-bottom: 1px solid #eeeeee;
        .icon {
          width: 24 * @rem;
          height: 24 * @rem;
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 24 * @rem 24 * @rem;
          // &.wx {
          //   background-image: url(~@/assets/images/recharge/wx-icon.png);
          //   background-size: 24 * @rem 21 * @rem;
          // }
          // &.zfb_dmf {
          //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
          //   background-size: 24 * @rem 24 * @rem;
          // }
        }
        .pay-center {
          margin-left: 8 * @rem;
          .line {
            display: flex;
            align-items: center;
            .pay-name {
              font-size: 15 * @rem;
              color: #333;
            }
            .recommend-icon {
              width: 39 * @rem;
              height: 17 * @rem;
              background-size: 39 * @rem 17 * @rem;
              background-position: left center;
              background-repeat: no-repeat;
              margin-left: 5 * @rem;
            }
          }
          .subtitle {
            color: #999;
            font-size: 12 * @rem;
            line-height: 20 * @rem;
          }
        }
        .choose {
          margin-left: auto;
          width: 18 * @rem;
          height: 18 * @rem;
          background: url(~@/assets/images/recharge/n_radio.png) center center
            no-repeat;
          background-size: 18 * @rem 18 * @rem;
          &.on {
            background-image: url(~@/assets/images/recharge/c_radio.png);
          }
        }
      }
    }
    .pay-btn {
      width: 290 * @rem;
      height: 45 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9d482;
      font-size: 18 * @rem;
      font-weight: bold;
      color: #7d561c;
      margin: 30 * @rem auto;
      border-radius: 23 * @rem;
      box-shadow: 0px 2px 13px 0px rgba(99, 68, 24, 0.24);
    }
  }
}
</style>
