<template>
  <div class="category-list2">
    <yy-list
      class="rank-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
      tips="暂无数据"
    >
      <div class="game-list">
        <div class="game-item" v-for="(item, index) in game_list" :key="index">
          <resourceList-item :gameInfo="item"></resourceList-item>
        </div>
      </div>
    </yy-list>
  </div>
</template>
<script>
import { ApiGameGetUpOwnerGame } from '@/api/views/game.js';
import ResourceListItem from '../resourceList-item/index.vue';
export default {
  name: 'ResourceListTab',
  components: {
    ResourceListItem,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      game_list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
      gameId: '',
    };
  },
  async created() {
    this.loadingObj.loading = true;
    this.gameId = this.$route.params.id;
    await this.getGameList();
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGameGetUpOwnerGame({
        page: this.page,
        listRows: this.listRows,
        game_id: this.gameId,
      });

      let { list } = res.data;
      if (action === 1 || this.page == 1) {
        this.game_list = [];
        if (!list.length) {
          this.empty = true;
          this.$emit('update:showEmptyMsg', false);
        } else {
          this.empty = false;
          this.$emit('update:showEmptyMsg', true);
        }
      }
      this.game_list.push(...list);
      this.loadingObj.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGameList(2);
    },
  },
};
</script>
<style lang="less" scoped>
.category-list2 {
  height: 100%;
  .container {
    display: flex;
    flex-flow: column;
    flex: 1;
    height: 100%;
    overflow: hidden;
    /deep/ .load-more {
      overflow-y: scroll;
    }
  }
  .list {
    flex: 1;
    overflow-y: scroll;
    // -webkit-overflow-scrolling: touch;
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .item-container {
    margin: 10 * @rem 5 * @rem 10 * @rem 21 * @rem;
    border-radius: 16 * @rem;
    &.item-1 {
      background: linear-gradient(270deg, #ff7156 0%, #ffb053 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-1.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          .game-icon {
            box-sizing: border-box;
            border: 1 * @rem solid #fff;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                font-size: 10 * @rem;
                color: #ff7b57;
                background: #f5f5f6;
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
              .score::before {
                .image-bg('~@/assets/images/category/star-2.png');
              }
            }
            .tags {
              display: flex;
              height: 17 * @rem;
              overflow: hidden;
              flex-wrap: wrap;
              .tag {
                box-sizing: border-box;
                border: 0.5 * @rem solid #fff;
                color: #fff;
                background-color: transparent;
                padding: 2 * @rem 4 * @rem;
                .tag-name {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
    &.item-2 {
      background: linear-gradient(270deg, #738ac0 0%, #74a4cd 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-2.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          padding: 8 * @rem 0;
          .game-icon {
            box-sizing: border-box;
            border: 1 * @rem solid #fff;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                font-size: 10 * @rem;
                color: #ff7b57;
                background: #f5f5f6;
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
            }
            .tags {
              display: flex;
              height: 17 * @rem;
              overflow: hidden;
              flex-wrap: wrap;
              .tag {
                box-sizing: border-box;
                border: 0.5 * @rem solid #fff;
                color: #fff;
                background-color: transparent;
                .tag-name {
                  color: #fff;
                }
              }
            }
            .score::before {
              .image-bg('~@/assets/images/category/star-2.png');
            }
          }
        }
      }
    }
    &.item-3 {
      background: linear-gradient(270deg, #d99d64 0%, #f0ca9e 100%);
      .item-content {
        .game-index {
          .image-bg('~@/assets/images/category/index-3.png');
          color: transparent;
        }
        /deep/ .game-item-components {
          padding: 8 * @rem 0;
          .game-icon {
            box-sizing: border-box;
            border: 1 * @rem solid #fff;
            img {
              border-radius: 14 * @rem;
            }
          }
          .game-info {
            height: unset;
            .game-name {
              color: #fff;
              .game-subtitle {
                font-size: 10 * @rem;
                color: #ff7b57;
                background: #f5f5f6;
              }
            }
            .game-bottom {
              color: #fff;
              .server-date {
                color: #fff;
              }
              .types {
                .type {
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      background-color: #fff;
                    }
                  }
                }
              }
              .score::before {
                .image-bg('~@/assets/images/category/star-2.png');
              }
            }
            .tags {
              display: flex;
              height: 17 * @rem;
              overflow: hidden;
              flex-wrap: wrap;
              .tag {
                box-sizing: border-box;
                border: 0.5 * @rem solid #fff;
                color: #fff;
                background-color: transparent;
                .tag-name {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
    .item-content {
      display: flex;
      align-items: center;
      margin-left: -16 * @rem;
      .game-index {
        width: 22 * @rem;
        height: 22 * @rem;
        margin: 5 * @rem;
        font-size: 14 * @rem;
        color: #797979;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      /deep/ .game-item-components {
        padding: 8 * @rem 0;
        .game-icon {
          width: 64 * @rem;
          height: 64 * @rem;
          flex: 0 0 64 * @rem;
        }
        .game-info {
          height: unset;
          .game-name {
            font-size: 14 * @rem;
            font-weight: bold;
            line-height: 20 * @rem;
            .game-subtitle {
              font-size: 10 * @rem;
              color: #ff7b57;
              background: #f5f5f6;
              border: none;
              padding: 2 * @rem 4 * @rem;
            }
          }
          .game-bottom {
            margin-top: 0;
            font-size: 11 * @rem;
            line-height: 16 * @rem;
          }
          .tags {
            margin: 4 * @rem 0;
          }
          .score {
            font-size: 12 * @rem;
            font-weight: 600;
            &::before {
              content: '';
              display: block;
              width: 8 * @rem;
              height: 8 * @rem;
              margin-right: 4 * @rem;
              .image-bg('~@/assets/images/category/star-3.png');
            }
          }
          .types {
            .type {
              font-size: 11 * @rem;
            }
          }
        }
      }
    }
  }
  .game-list {
    .game-item {
      &:not(:last-of-type) {
        border-bottom: 0.5 * @rem solid #ebebeb;
      }
      position: relative;
      padding: 5 * @rem 0;
    }
  }
}
</style>
