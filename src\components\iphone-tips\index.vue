<template>
  <div class="black-wapper" v-if="isShow && showSuspension == 1">
    <div class="wechat-tips" v-if="isWechat">
      <img class="big-img" src="@/assets/images/tips/wechat-tips.png" />
    </div>
    <template v-else>
      <div class="tips" v-if="isIos">
        <van-popup
          v-if="isSafari"
          v-model="safariShow"
          :lock-scroll="false"
          position="bottom"
          round
          @closed="openButton()"
          class="safari-popup tips-popup"
          :close-on-click-overlay="!initData.web_show_down"
        >
          <div class="container">
            <div class="app-container">
              <img :src="defaultAvatar" class="app-icon">
              <div class="text-container">
                <div class="big-text">快速添加桌面应用</div>
                <div class="small-text">便捷启动•全屏体验</div>
              </div>
            </div>
            <div @click="safariShow = false" class="close"></div>
            <img
              class="big-img"
              v-show="safari_tips == 1"
              src="@/assets/images/tips/safari-tips.png"
            />
            <img
              class="big-img"
              v-show="safari_tips == 2"
              src="@/assets/images/tips/yindao2.gif"
            />
            <div class="explain">
              受苹果开发者账号签名限制，提供桌面书签版服务
            </div>
          </div>
        </van-popup>
        <van-popup
          v-else
          v-model="noSafariShow"
          :lock-scroll="false"
          position="bottom"
          round
          @closed="openButton()"
          class="no-safari-popup tips-popup"
        >
          <img v-if="BASEPARAMS.channel != 'cps5740'" class="big-img" src="~@/assets/images/tips/yindao.gif" />
          <div @click="noSafariShow = false" class="close"></div>
          <div class="text text1">
            请使用<img :src="defaultAvatar" class="icon"><span>Safari</span>浏览器打开链接
          </div>
          <div class="text text2">
            安装<img :src="defaultAvatar" class="icon"><span>{{ appName }}</span>到桌面即可体验游戏
          </div>
          <div @click="copy()" class="button btn">复制链接</div>
        </van-popup>
        <div
          v-if="buttonShow"
          @click="openPopup()"
          class="fix-button yindao-button"
        ></div>
      </div>
      <div class="tips" v-if="isAndroid && !isAndroidBox">
        <div @click="downloadBox()" class="fix-button"></div>
      </div>
    </template>
  </div>
</template>

<script>
import {
  isWechat,
  isSafari,
  needGuide,
  isIos,
  isAndroid,
  isAndroidBox,
} from '@/utils/userAgent';
import BASEPARAMS from '@/utils/baseParams';
import { platform } from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      isShow: false, //是否展示引导
      href: '', //当前地址
      safariShow: false, //是否展示safari软引导层
      noSafariShow: false, //是否展示非safari软引导层
      buttonShow: false, //是否打开按钮
      isWechat,
      isSafari,
      isIos,
      BASEPARAMS,
      isAndroid,
      isAndroidBox,
      safari_tips: 1, //safari引导时当前显示的图片
    };
  },
  computed: {
    ...mapGetters({
      showSuspension: 'system/showSuspension',
      initData: 'system/initData',
      defaultAvatar: 'system/defaultAvatar',
      appName: 'system/appName',
    }),
  },
  watch: {
    safariShow(value) {
      if (value) {
        setTimeout(() => {
          if(BASEPARAMS.channel != 'cps5740') {
            this.safari_tips = 2;
          }
        }, 2000);
      }
    },
  },
  created() {
    switch (platform) {
      case 'ios':
      case 'iosBox':
      case 'android':
      case 'androidBox':
        this.isShow = false;
        break;
      default:
        // 判断是否打开引导
        this.isShow = needGuide;
        break;
    }

    // 判断是打开safari引导还是非safari引导
    if (this.isSafari) {
      this.safariShow = true;
      this.noSafariShow = false;
    } else {
      this.safariShow = false;
      this.noSafariShow = true;
    }

    // 如果是微信引导防手势和超出隐藏
    if (this.isShow && this.isWechat) {
      document.getElementsByTagName('body')[0].style.overflow = 'hidden';
      document.addEventListener('gesturestart', function (event) {
        event.preventDefault();
      });
    }
  },
  methods: {
    openButton() {
      this.buttonShow = true;
      this.safari_tips = 1;
    },
    openPopup() {
      // 打开引导
      if (this.isSafari) {
        this.safariShow = true;
      } else {
        this.noSafariShow = true;
      }
      this.buttonShow = false;
    },
    copy() {
      event.stopPropagation();
      this.href = window.location.href;
      this.$copyText(this.href).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    downloadBox() {
      const url = !!BASEPARAMS.channel
        ? `https://xz.xz3733.com/apk/gamebox/latest/3733gamebox_${BASEPARAMS.channel}.apk`
        : `https://xz.xz3733.com/apk/gamebox/latest/3733gamebox_cps3457.apk`;
      window.location.href = url;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .tips .van-overlay {
  z-index: 10000 !important;
}

/deep/ .tips-popup {
  z-index: 99999 !important;
}
.wechat-tips {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10001;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 0.388933rem;
}

.wechat-tips .big-img {
  position: absolute;
  top: 0;
  right: 0;
  width: 80%;
  height: auto;
}
.fix-button {
  position: fixed;
  bottom: calc(70 * @rem + @safeAreaBottom);
  bottom: calc(70 * @rem + @safeAreaBottomEnv);
  transform: translate(290 * @rem, 0);
  z-index: 9999999;
  width: 70 * @rem;
  height: 67 * @rem;
  background-image: url(~@/assets/images/tips/guide-ball.gif);
  background-repeat: no-repeat;
  background-size: 100%;
}

.safari-popup {
  .container {
    position: relative;
    .app-container {
      padding: 24 * @rem;
      display: flex;
      .app-icon {
        display: inline-block;
        margin: 0;
        width: 40 * @rem;
        height: 40 * @rem;
      }
      .text-container {
        height: 40 * @rem;
        margin-left: 10 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .big-text {
          font-size: 16 * @rem;
          font-weight: 600;
          color: #333333;
          line-height: 20 * @rem;
        }
        .small-text {
          color: #999999;
          line-height: 15 * @rem;
        }
      }
    }
    .close {
      position: absolute;
      top: 15 * @rem;
      right: 20 * @rem;
      width: 14 * @rem;
      height: 14 * @rem;
      .image-bg('~@/assets/images/close-dialog.png');
    }
    .big-img {
      display: block;
      width: 327 * @rem;
      height: 184 * @rem;
      margin: 0 auto;
      border-radius: 10 * @rem;
    }
    .explain {
      margin: 7 * @rem 0 41 * @rem;
      text-align: center;
      font-size: 12 * @rem;
      color: #999999;
      line-height: 15 * @rem;
    }
  }
}

.no-safari-popup {
  width: 100%;
  .big-img {
    position: relative;
    top: -0.0267rem;
    left: 0;
  }
  .close {
    position: absolute;
    top: 0.267rem;
    right: 0.2403rem;
    display: block;
    width: 0.6408rem;
    height: 0.6408rem;
    background: url(~@/assets/images/close-filled-reverse.png);
    background-size: 100%;
  }
  .text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.3738rem;
    font-family:
      PingFang SC,
      PingFang SC;
    color: #666666;
    .icon {
      margin: 0 0.1068rem;
    }
    span {
      margin-right: 0.2136rem;
    }
    &.text1 {
      margin-top: 0.3738rem;
      .icon {
        display: block;
        width: 0.3738rem;
        height: 0.3738rem;
        background: url(~@/assets/images/safari-icon-32x32.png);
        background-size: 100%;
      }
      span {
        color: #468ee7;
      }
    }
    &.text2 {
      margin-top: 0.1869rem;
      .icon {
        display: block;
        width: 0.54735rem;
        height: 0.54735rem;
      }
      span {
        color: #32b768;
      }
    }
  }
  .button {
    width: 6.6216rem;
    height: 1.0146rem;
    margin: 0.534rem auto;
    background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
    border-radius: 0.1335rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.3471rem;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
