<template>
  <van-popup
    v-model="popupShow"
    :close-on-click-overlay="true"
    :lock-scroll="false"
    position="bottom"
    round
    get-container="body"
    class="recycle-xh-popup"
  >
    <div class="title">选择小号</div>
    <div class="close-btn" @click="popupShow = false"></div>
    <div class="popup-content">
      <yy-list
        class="yy-list"
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
      >
        <div class="xiaohao-list">
          <div
            class="xiaohao-item"
            v-for="item in xiaohaoList"
            :key="item.id"
            @click="selectXh(item)"
          >
            <div class="info">
              <div class="xh-title">
                <span>{{ item.nickname }}</span>
                <div class="status" v-if="item.recycleStatus">可回收</div>
                <div class="status not" v-else>不可回收</div>
              </div>
              <div class="xh-info">
                <div class="xh-id">小号ID：{{ item.id }}</div>
                <div class="xh-cost">累计充值：{{ item.paySum }}元</div>
              </div>
            </div>
            <div
              class="select"
              :class="{
                selected: selectedXh.id == item.id,
                disabled: !item.recycleStatus,
              }"
            ></div>
          </div>
        </div>
      </yy-list>
    </div>
    <div
      class="recycle-btn"
      :class="{ disabled: !selectedXh.id }"
      @click="clickRecycle"
      >确定</div
    >
  </van-popup>
</template>
<script>
import { ApiXiaohaoManage } from '@/api/views/xiaohao.js';
export default {
  name: 'RecycleXhPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      required: true,
    },
  },
  data() {
    return {
      xiaohaoList: [],
      selectedXh: {},
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  created() {
   
  },
  computed: {
    popupShow: {
      set: function (val) {
        this.$emit('update:show', val);
      },
      get: function () {
        return this.show;
      },
    },
  },
  methods: {
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoManage({
        appId: this.id,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
      }
      this.xiaohaoList.push(...res.data.list);

      if (this.xiaohaoList.length) {
        this.empty = false;
        this.selectedXh =
          this.xiaohaoList.find(item => {
            return item.recycleStatus;
          }) || {};
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getXiaohaoList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.xiaohaoList.length) {
        await this.getXiaohaoList();
      } else {
        await this.getXiaohaoList(2);
      }

      this.loadingObj.loading = false;
    },
    selectXh(item) {
      if (item.recycleStatus) {
        this.selectedXh = item;
      }
    },
    clickRecycle() {
      if (!Number(this.selectedXh.recycleStatus)) {
        return false;
      }
      this.$nextTick(() => {
        let info = {
          id: this.selectedXh.id,
          game_name: this.selectedXh.title,
          game_icon: this.selectedXh.titlepic,
          nickname: this.selectedXh.nickname,
        };
        this.toPage('Recycle', {
          info,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.recycle-xh-popup {
  display: flex;
  flex-direction: column;
  background-color: #fbfbfe;
  padding: 0 18 * @rem 20 * @rem;
  box-sizing: border-box;
  height: 60vh;

  .title {
    padding: 26 * @rem 0 24 * @rem;
    text-align: center;
    height: 22 * @rem;
    font-weight: 600;
    font-size: 16 * @rem;
    color: #000000;
    line-height: 22 * @rem;
  }

  .close-btn {
    display: block;
    width: 16 * @rem;
    height: 16 * @rem;
    background: url(~@/assets/images/my-game/close.png) no-repeat center;
    background-size: 16 * @rem 16 * @rem;
    padding: 10 * @rem;
    position: absolute;
    top: 15 * @rem;
    right: 8 * @rem;
  }

  .popup-content {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    .xiaohao-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 81 * @rem;
      border-radius: 6 * @rem;
      background-color: #fff;
      border: 0.5 * @rem solid #f0f0f0;
      margin-bottom: 8 * @rem;
      padding: 0 12 * @rem;
      box-sizing: border-box;

      .info {
        flex: 1;
        min-width: 0;

        .xh-title {
          display: flex;
          align-items: center;

          span {
            display: block;
            height: 20 * @rem;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #111111;
            line-height: 20 * @rem;
            text-align: left;
            text-transform: none;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .status {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            margin-left: 8 * @rem;
            color: #32b768;
            height: 20 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            line-height: 20 * @rem;
            text-align: left;

            &::before {
              content: '';
              display: block;
              flex-shrink: 0;
              width: 4 * @rem;
              height: 4 * @rem;
              border-radius: 50%;
              background-color: #32b768;
              margin-right: 4 * @rem;
            }

            &.not {
              color: #f44040;

              &::before {
                background-color: #f44040;
              }
            }
          }
        }

        .xh-info {
          display: flex;
          align-items: center;
          margin-top: 16 * @rem;
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #9a9a9a;
          line-height: 17 * @rem;

          .xh-id {
            flex-shrink: 0;
            margin-right: 18 * @rem;
          }

          .xh-cost {
            overflow: hidden;
          }
        }
      }
      .select {
        width: 20 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/my-game/select.png) no-repeat center;
        background-size: 20 * @rem 20 * @rem;
        margin-left: 10 * @rem;

        &.disabled {
          background-image: url(~@/assets/images/my-game/select-disabled.png);
        }

        &.selected {
          background-image: url(~@/assets/images/my-game/selected.png);
        }
      }
    }
  }

  .recycle-btn {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 339 * @rem;
    height: 44 * @rem;
    border-radius: 29 * @rem;
    line-height: 44 * @rem;
    background: @themeBg;
    font-weight: 500;
    font-size: 15 * @rem;
    color: #ffffff;
    text-align: center;
    margin: 20 * @rem auto 0;

    &.disabled {
      background: #dcdcdc;
    }
  }
}
</style>
