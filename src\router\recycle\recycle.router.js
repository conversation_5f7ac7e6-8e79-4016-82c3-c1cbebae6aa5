export default [
  {
    path: '/recycle',
    name: 'Recycle',
    component: () =>
      import(/* webpackChunkName: "recycle" */ '@/views/Recycle'),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      pageTitle: '小号回收'
    },
  },
  {
    path: '/recycle_record',
    name: 'RecycleRecord',
    component: () =>
      import(/* webpackChunkName: "recycle" */ '@/views/Recycle/RecycleRecord'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '回收记录'
    },
  },
  {
    path: '/recycle_select_xiaohao',
    name: 'RecycleSelectXiaohao',
    component: () =>
      import(
        /* webpackChunkName: "recycle" */ '@/views/Recycle/RecycleSelectXiaohao'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
];
