<template>
  <div class="news-gonglue-page">
    <div class="news-container">
      <content-empty v-if="empty" :tips="$t('暂无相关攻略')"></content-empty>
      <load-more
        v-else
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
        class="news-list"
        :check="false"
      >
        <template>
          <div
            class="news-item"
            v-for="(item, index) in newsList"
            :key="index"
            @click="toNewsDetail(item)"
          >
            <div class="left">
              <div class="news-title">{{ item.title }}</div>
              <div class="news-info">
                <div class="news-view">{{ item.onclick }}</div>
                <div class="news-date">{{ formatDate(item.newstime) }}</div>
              </div>
            </div>
            <div class="right">
              <img :src="item.titlepic" alt="" />
            </div>
          </div>
        </template>
      </load-more>
    </div>
  </div>
</template>

<script>
import { ApiNewsGameNewsList } from '@/api/views/news.js';
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      page: 1,
      listRows: 20,
      newsList: [],
      loading: false,
      finished: false,
      empty: false,
      vipOpen: true,
    };
  },
  async created() {
    this.id = this.$route.params.game_id;
    await this.getNewsList();
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.vipTable) {
        this.vipHeight = this.$refs.vipTable.clientHeight;
        if (this.vipHeight > 250) {
          this.vipOpen = false;
        }
      }
    });
  },
  computed: {
    ...mapGetters({
      gameInfo: 'game/gameInfo',
    }),
  },
  methods: {
    async getNewsList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiNewsGameNewsList({
        gameId: this.id,
        type: 2,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.newsList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.newsList.push(...list);
      this.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getNewsList(2);
    },
    toNewsDetail(item) {
      this.$router.push({
        name: 'Iframe',
        params: { url: item.titleurl, title: item.title },
      });
    },
    formatDate(val) {
      let { year, date } = this.$handleTimestamp(val);
      return `${year}-${date}`;
    },
  },
};
</script>

<style lang="less" scoped>
.news-gonglue-page {
  .section {
    padding: 10 * @rem 18 * @rem 20 * @rem;
    .section-title {
      margin-bottom: 12 * @rem;
    }
  }
  .section-title {
    display: flex;
    justify-content: space-between;
    height: 25 * @rem;
    align-items: center;
    .title-icon {
      width: 22 * @rem;
      height: 22 * @rem;
      .image-bg('~@/assets/images/games/sun-icon.png');
      margin-right: 5 * @rem;
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 600;
      flex: 1;
      min-width: 0;
    }
    .title-right {
      display: flex;
      align-items: center;
      span {
        font-size: 15 * @rem;
        color: @themeColor;
      }
      .title-right-icon {
        width: 10 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/games/right-arrow.png) center center
          no-repeat;
        background-size: 10 * @rem 10 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
  .vip-table {
    .vip-desc {
      font-size: 14 * @rem;
      color: #797979;
      line-height: 20 * @rem;
    }
    .table-container {
      height: 250px;
      overflow: hidden;
      position: relative;
      &.open {
        height: auto;
      }
      .vip-open {
        box-sizing: border-box;
        padding-top: 20 * @rem;
        width: 100%;
        position: absolute;
        bottom: -1 * @rem;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40 * @rem;
        font-size: 12 * @rem;
        color: @themeColor;
        background: linear-gradient(
          to top,
          rgba(255, 255, 255, 1) 20 * @rem,
          rgba(255, 255, 255, 0)
        );
        .more-text-icon {
          width: 11 * @rem;
          height: 7 * @rem;
          background: url(~@/assets/images/games/bottom-arrow-green.png) center
            center no-repeat;
          background-size: 11 * @rem 7 * @rem;
          margin-left: 4 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
    }
    .table {
      box-sizing: border-box;
      width: 100%;
      margin-top: 10 * @rem;
      border-radius: 12 * @rem;
      border: 0.5 * @rem solid @themeColor;
      overflow: hidden;
      .row {
        display: flex;
        &:nth-of-type(n + 2) {
          border-top: 0.5 * @rem solid @themeColor;
        }
      }
      .th,
      .td {
        width: 50%;
        text-align: center;
        font-size: 12 * @rem;
        color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 11 * @rem 5 * @rem;
        &:nth-of-type(n + 2) {
          border-left: 0.5 * @rem solid @themeColor;
        }
      }
      .th {
        background-color: #ffebe1;
        font-size: 12 * @rem;
        color: #000000;
        font-weight: 600;
      }
    }
  }
  .news-container {
    // border-top: 8 * @rem solid #f5f5f6;
    padding-bottom: 40 * @rem;
    .news-list {
      padding-top: 5 * @rem;
      .news-item {
        display: flex;
        padding: 18 * @rem;
        .left {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .news-title {
            font-size: 16 * @rem;
            color: #000000;
            font-weight: 600;
            line-height: 22 * @rem;
            height: 44 * @rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .news-info {
            display: flex;
            justify-content: space-between;
            font-size: 13 * @rem;
            color: #9a9a9a;
            .news-view {
              padding-left: 18 * @rem;
              background: url(~@/assets/images/games/view-icon.png) left center
                no-repeat;
              background-size: 14 * @rem 10 * @rem;
            }
          }
        }
        .right {
          width: 142 * @rem;
          height: 71 * @rem;
          border-radius: 6 * @rem;
          overflow: hidden;
          margin-left: 18 * @rem;
        }
      }
    }
  }
}
</style>
