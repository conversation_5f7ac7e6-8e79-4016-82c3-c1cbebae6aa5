import Vue from 'vue';
import yyConfirmAgreement from '@/components/yy-confirm-agreement/index.vue';
import router from '@/router';
import store from '@/store';

import { Toast } from 'vant';

const Agreement = Vue.extend(yyConfirmAgreement);

function useConfirmAgreement(isAgree) {
  return new Promise((resolve, reject) => {
    const agreement = new Agreement({
      router,
      store,
    });

    agreement.$mount(document.createElement('div'));
    document.body.appendChild(agreement.$el);

    agreement.$el.addEventListener(
      'animationend',
      () => {
        if (agreement.show == false) {
          agreement.$destroy();
          agreement.$el.parentNode.removeChild(agreement.$el);
        }
      },
      false,
    );

    if (isAgree) {
      agreement.show = false;
      resolve(true);
    } else {
      agreement.show = true;
    }

    agreement.confirm = function () {
      resolve(true);
      agreement.show = false;
    };

    agreement.cancel = function () {
      reject(false);
      Toast('登录需同意服务协议及隐私保护');
      agreement.show = false;
    };
  });
}

export default useConfirmAgreement;
