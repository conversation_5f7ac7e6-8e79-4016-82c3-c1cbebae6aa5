<template>
  <van-dialog
    v-model="show"
    :showConfirmButton="false"
    :lockScroll="false"
    class="ad-and-coupon-popup"
    :overlay-style="{ 'z-index': '3000' }"
    :closeOnPopstate="false"
  >
    <div class="ad-popup-container">
      <div class="ad-popup-bg" @click="toDetail()">
        <img
          class="ad-popup-bg-img"
          v-if="content.ad_bg"
          :src="content.ad_bg"
        />
        <img
          class="ad-popup-img"
          :class="{ 'ad-popup-img-reset': content.ad_bg }"
          :src="content.ad_image"
        />
      </div>
      <div
        class="popup-content"
        @click="closeCheck = !closeCheck"
        v-if="content.no_tip_text"
      >
        <div class="check-box">
          <div class="check-box-inner1" v-if="!closeCheck"> </div>
          <div class="check-box-inner2" v-else> </div>
          {{ content.no_tip_text }}
        </div>
      </div>
      <div @click="handleCancel()" class="close"></div>
    </div>
  </van-dialog>
</template>

<script>
import { ApiV2024IndexPopcloseLog } from '@/api/views/system.js';
import { BOX_showActivityByAction } from '@/utils/box.uni.js';
import BASEPARAMS from '@/utils/baseParams';
export default {
  data() {
    return {
      show: false,
      content: {
        popup_id: '',
        ad_image: '',
        type: '',
        pop_type: '',
        no_tip_text: '',
        no_tip_day: '',
        action: {},
      },
      //       {
      //     "pop_id": 1,
      //     "ad_image": "https://pic5.xz3733.com/banner/202503/01045196f36bf0a0dea3dd8ff60f2ad7.png",
      //     "type": 5,
      //     "pop_type": 1,
      //     "no_tip_text": "",
      //     "no_tip_day": 0,
      //     "action": {
      //         "text1": "",
      //         "action_code": 1041,
      //         "bg_img_url": "https://pic5.xz3733.com/banner/202503/01045196f36bf0a0dea3dd8ff60f2ad7.png",
      //         "extra_id": 0,
      //         "cate_id": 0,
      //         "radius": 8,
      //         "next_page_title": "全部分类"
      //     }
      // }
      closeCheck: false,
      noTipDay: 3,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.handlePopupInfo();
      // 神策埋点
      this.$sensorsTrack('popup_exposure', {
        page_name: this.$sensorsPageGet(),
        popup_id: `${this.content.pop_id}`,
        banner_name: this.content.title || '暂无',
        banner_type: this.content.type ? `${this.content.type}` : '广告', // 没有类型的传‘广告’
      });
    });
  },
  methods: {
    handlePopupInfo(isClose = false) {
      // isClose弹窗关闭的时候刷新数据，不增加次数；
      const popupConfig = {
        close_check: this.closeCheck,
        pop_id: this.content.pop_id,
        pop_type: this.content.pop_type,
        update_time: this.content.update_time,
        close_time: Math.floor(Date.now() / 1000),
        remind_days: this.content.remind_days,
      };
      let popup_config_name = `POPUP_CONFIG_${this.content.box_position}`;
      let now_popup_config_name = `NOW_POPUP_CONFIG_${this.content.box_position}`;
      if (this.userInfo.token) {
        popupConfig.user_id = this.userInfo.user_id;
      } else {
        popupConfig.uuid = BASEPARAMS.uuid;
      }
      // 每次启动不记录弹窗
      // if (this.content.pop_type == 2) return false;

      // 存在本地 先判断是否已经存在了
      let localPopupConfig = localStorage.getItem(popup_config_name);
      let localPopupConfigList = localPopupConfig
        ? JSON.parse(localPopupConfig)
        : [];

      const matchIndex = localPopupConfigList.findIndex(
        item =>
          item.pop_id === popupConfig.pop_id &&
          (this.userInfo.token
            ? item.user_id === popupConfig.user_id
            : item.uuid === popupConfig.uuid),
      );
      if (matchIndex !== -1) {
        // 如果已存在且 close_time 或 update_time 不一样 替换为新的
        if (
          localPopupConfigList[matchIndex].close_time !==
            popupConfig.close_time ||
          localPopupConfigList[matchIndex].update_time !==
            popupConfig.update_time
        ) {
          popupConfig.times = isClose
            ? localPopupConfigList[matchIndex].times
            : localPopupConfigList[matchIndex].times + 1;
          localPopupConfigList[matchIndex] = popupConfig;
        }
      } else {
        // 没找到匹配项，直接添加
        popupConfig.times = 1;
        localPopupConfigList.push(popupConfig);
      }

      // 保存回本地
      localStorage.setItem(
        popup_config_name,
        JSON.stringify(localPopupConfigList),
      );
      localStorage.setItem(now_popup_config_name, JSON.stringify(popupConfig));
    },
    toDetail() {
      this.show = false;

      this.$sensorsModuleSet('活动弹窗')
      
      if (this.content.click_id) {
        this.CLICK_EVENT(this.content.click_id);
      }
      this.handlePopupInfo(true);

      // 神策埋点
      this.$sensorsTrack('popup_click', {
        page_name: this.$sensorsPageGet(),
        popup_id: `${this.content.pop_id}`,
        banner_name: this.content.title || '暂无',
        banner_type: this.content.type ? `${this.content.type}` : '广告', // 没有类型的传‘广告’
        button_name: '暂无',
      });

      BOX_showActivityByAction(this.content.action);
    },
    handleCancel() {
      this.show = false;
      this.handlePopupInfo(true);
      // this.popupCloseLog(this.content);
      if (typeof this.onCancel === 'function') {
        this.onCancel();
      }
    },
    async popupCloseLog(info) {
      const res = await ApiV2024IndexPopcloseLog({
        type: info.type,
        pop_id: info.pop_id,
        pop_type: info.pop_type,
        no_tip_day: !this.closeCheck ? 0 : info.no_tip_day,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ad-and-coupon-popup {
  z-index: 3000 !important;
  background: none;
  overflow: unset;
  .ad-popup-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .ad-popup-bg {
      width: 100%;
      height: 100%;
      position: relative;
      top: 0;
      left: 0;
      .ad-popup-bg-img {
        position: relative;
        width: 100%;
        height: 100%;
      }
      .ad-popup-img-reset {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 196 * @rem;
        height: 196 * @rem;
        z-index: 1;
      }
    }
    .popup-content {
      margin-top: 14 * @rem;
      height: 20 * @rem;
      line-height: 20 * @rem;
      display: flex;
      align-items: center;
      .check-box {
        line-height: 20 * @rem;
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 14 * @rem;
        color: #ffffff;
        .check-box-inner1,
        .check-box-inner2 {
          width: 15 * @rem;
          height: 15 * @rem;
          margin-right: 6 * @rem;
        }
        .check-box-inner1 {
          background: url('~@/assets/images/activity-popup-none-check.png')
            no-repeat center center;
          background-size: 15 * @rem 15 * @rem;
        }
        .check-box-inner2 {
          background: url('~@/assets/images/activity-popup-check.png') no-repeat
            center center;
          background-size: 15 * @rem 15 * @rem;
        }
      }
    }
    .close {
      margin-top: 38 * @rem;
      width: 30 * @rem;
      height: 30 * @rem;
      background-image: url(~@/assets/images/activity-popup-close.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }
}
</style>
