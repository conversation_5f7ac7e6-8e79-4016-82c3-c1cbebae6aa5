<template>
  <!-- 最近在玩 -->
  <div class="nav-list-component">
    <swiper
      :options="swiperOptions"
      :auto-update="true"
      style="width: 100%; margin: 0 auto"
      v-if="navList.length > 0"
    >
      <swiper-slide
        class="nav-item btn"
        v-for="(item, index) in navList"
        :key="index"
      >
        <template>
          <div class="nav-icon">
            <div
              class="img"
              :style="{ backgroundImage: `url(${item.titlepic})` }"
            ></div>
          </div>
          <div class="nav-name">{{ item.title }}</div>
        </template>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script>
export default {
  name: 'RecentlyPlayingList',
  props: {
    navList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    let that = this;
    return {
      swiperOptions: {
        // slidesPerView: 5,
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        on: {
          click: function () {
            setTimeout(() => {
              let nav = that.navList[this.clickedIndex];
              that.toPage('EmulatorGameDetail', { id: nav.id });
            }, 0);
          },
        },
      },
    };
  },
};
</script>

<style lang="less" scoped>
.nav-list-component {
  padding: 16 * @rem 0 18 * @rem 0;
  width: 100%;
  display: flex;
  align-items: center;
  .swiper-scrollbar {
    margin: 13 * @rem auto 0;
    width: 24 * @rem;
    height: 6 * @rem;
  }
  /deep/ .swiper-scrollbar-drag {
    background: @themeColor;
  }
  .nav-item {
    width: 66 * @rem;
    flex-shrink: 0;
    margin-left: 10 * @rem;
    .nav-icon {
      width: 58 * @rem;
      height: 58 * @rem;
      border-radius: 16 * @rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
      }
      .img {
        width: 100%;
        height: 100%;
        background-size: 100%;
      }
    }
    .nav-name {
      text-align: center;
      margin-top: 8 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #222222;
      height: 15 * @rem;
      line-height: 15 * @rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &:last-child {
      margin-right: 10 * @rem;
    }
  }
}
</style>
