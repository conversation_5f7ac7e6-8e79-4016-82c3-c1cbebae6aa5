<template>
  <div class="sell-list-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
    >
      <jianlou-sell-list :list="sellList"></jianlou-sell-list>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoJianlouList } from '@/api/views/xiaohao.js';
import jianlouSellList from '../components/jianlou-sell-list';
export default {
  name: 'SellList',
  components: {
    jianlouSellList,
  },
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      sellList: [],
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  methods: {
    async getSellList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await A<PERSON><PERSON><PERSON>ohao<PERSON><PERSON>louList({
        isDone: 1,
        page: this.page,
        listRows: this.listRows,
      });
      if (res.data.about) {
        this.$emit('getAbout', res.data.about);
      }
      if (action === 1 || this.page === 1) {
        this.sellList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.sellList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getSellList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.sellList.length) {
        await this.getSellList();
      } else {
        await this.getSellList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.sell-list-page {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  .yy-list {
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: auto;
    }
  }
  /deep/ .pull-refresh {
    min-height: 0;
    flex: 1;
  }
}
</style>
