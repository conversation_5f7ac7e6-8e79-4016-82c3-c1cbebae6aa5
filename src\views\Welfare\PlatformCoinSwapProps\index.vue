<template>
  <div class="props-page page">
    <nav-bar-2 ref="navBar" title="兑换道具" :border="false" :azShow="true">
    </nav-bar-2>
    <van-loading class="loading-box" v-if="pageLoading">{{
      $t('加载中...')
    }}</van-loading>
    <template v-else>
      <!-- 搜索栏 -->
      <div class="game-list-search">
        <div class="search-bar">
          <div class="search-input">
            <form @submit.prevent="submitSearch(keyword)">
              <input
                v-model.trim="keyword"
                type="text"
                placeholder="请输入游戏名"
              />
            </form>
          </div>
          <div
            class="input-clear"
            v-if="inputClearShow"
            @click="clearInput"
          ></div>
          <div class="search-icon" @click="searchBtn"></div>
        </div>
      </div>
      <!-- 道具列表 -->
      <div class="props-container">
        <div class="props-content">
          <yy-list
            class="props-list"
            v-model="loadingObj"
            :finished="finished"
            :empty="empty"
            @refresh="onRefresh()"
            @loadMore="loadMore()"
          >
            <div
              class="props-item"
              v-for="(item, index) in propsListInfo"
              :key="index"
            >
              <div class="props-info">
                <div class="game-info">
                  <div class="left-box" @click="goToDetail(item.game)">
                    <div class="title-pic">
                      <img :src="item.game.titlepic" alt="" />
                    </div>
                    <div class="content-box">
                      <div class="game-name">
                        <div class="title">
                          <div class="title-content">
                            {{ item.game.title }}
                          </div>
                        </div>
                        <div class="subtitle">{{ item.game.subtitle }}</div>
                      </div>
                      <div class="discount-tag" v-if="discountTag(item.game)">
                        <img
                          class="discount-icon discount-01"
                          src="@/assets/images/games/discount-01.png"
                          v-if="discountTag(item.game) == 0.1"
                        />
                        <img
                          class="discount-icon"
                          src="@/assets/images/games/discount-normal.png"
                          v-else-if="discountTag(item.game)"
                        />
                        <div class="discount-text"
                          ><span>{{ discountTag(item.game) }}</span
                          >折直充</div
                        >
                      </div>
                    </div>
                  </div>
                  <div class="right-btn">
                    <span
                      v-if="!openGameShow(item.game)"
                      @click="goToDetail(item.game)"
                      >下载</span
                    >
                    <span @click="openGame(item.game)" v-else>打开</span>
                  </div>
                </div>
                <div
                  class="exchange-info"
                  v-for="card in isExpanded[index]
                    ? item.card
                    : item.card.slice(0, 3)"
                  :key="card.id"
                >
                  <div class="exchange-left-box">
                    <div class="icon">
                      <div class="icon-box">
                        <img :src="card.titlepic" alt="" />
                      </div>
                    </div>
                    <div class="info">
                      <div class="name">
                        {{ card.title }}
                      </div>
                      <div class="num">
                        <div class="text1">消耗</div>
                        <div class="text2">{{ card.need_ptb }}</div>
                        <div class="text3">平台币</div>
                      </div>
                    </div>
                  </div>
                  <div class="exchange-right-box">
                    <div
                      class="exchange-right-btn"
                      @click="exchangeImmediately(item.game, card)"
                    >
                      <div>立即兑换</div>
                    </div>
                    <div class="prompt" v-if="card.btn_text">
                      <div
                        class="prompt-content"
                        :style="{
                          marginRight:
                            card.btn_text && card.btn_text.length > 6
                              ? '4*@rem'
                              : '0',
                        }"
                      >
                        <div
                          :class="{
                            'text-scroll':
                              card.btn_text && card.btn_text.length > 6,
                            'text-scroll-fast':
                              card.btn_text && card.btn_text.length > 15,
                          }"
                          :style="{
                            animationDuration: `${
                              card.btn_text.length > 15
                                ? card.btn_text.length * 400
                                : card.btn_text.length * 800
                            }ms`,
                            animationDelay: 0,
                          }"
                          >{{ card.btn_text }}
                          <template v-if="card.btn_text.length > 6"
                            >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{
                              card.btn_text
                            }}
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</template
                          >
                        </div></div
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="more-props" v-if="item.card.length > 3">
                <div class="more-btn" @click="toggleExpand(index)">
                  <div class="text">{{
                    isExpanded[index] ? '收起' : '更多道具'
                  }}</div>
                  <div
                    class="arrow"
                    :class="{ rotated: isExpanded[index] }"
                  ></div>
                </div>
              </div>
            </div>
          </yy-list>
        </div> </div
    ></template>

    <div
      class="return-top"
      :class="{ az: platform == 'android' }"
      v-show="showReturnTop"
      @click.stop="handleReturnTop"
    ></div>
    <!-- 下载游戏弹窗 -->
    <van-dialog
      v-model="downGameShow"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="create-dialog"
    >
      <div class="dialog-content">
        <!-- <div class="title">领取成功</div> -->
        <div class="message" v-html="unmetTitle"></div>
        <div class="dialog-bottom-bar">
          <div class="confirm btn" @click.stop="openOrDowGame()">下载游戏</div>
          <div class="btn-close" @click="downGameShow = false">
            <span></span>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 平台币不足提示弹窗 -->
    <van-dialog
      v-model="platformCoinShow"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="create-dialog"
    >
      <div class="dialog-content h-148">
        <div class="message mess-t0" v-html="platformCoinTitle"></div>
        <div class="dialog-bottom-bar">
          <div class="confirm btn" @click="toPlatformCoin">充值平台币</div>
          <div class="btn-close" @click="platformCoinShow = false">
            <span></span>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="false"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-text">道具已领取，您的兑换码是</div>
      </div>
      <div class="cardpass">{{ dialogInfo.cardpass }}</div>
      <div class="desc" v-html="introduction"></div>
      <div class="copy-btn btn" @click="copy(dialogInfo)">
        {{ $t('复制') }}
      </div>
      <div class="close-btn" @click="copyDialogShow = false"></div>
    </van-dialog>

    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>

    <!-- 平台币兑换道具 -->
    <platform-coin-exchange-prop
      :show.sync="platformCoinExchangePropShow"
      :cardOrderInfo="cardOrderInfo"
      :list="cardOrderInfo.pay_way_list"
      @ptbInsufficient="ptbInsufficient"
      @choosePayType="choosePayType"
      @closePopup="closePopup"
    >
    </platform-coin-exchange-prop>
  </div>
</template>

<script>
import {
  ApiCarRedeemItems,
  ApiCarCheckPtbCard,
  ApiCarPtbCardOrderInfo,
  ApiCarPtbCardToPay,
  ApiCarGetOrderCardPass,
  ApiCarGetCancelOrder,
} from '@/api/views/welfare.js';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import { navigateToGameDetail } from '@/utils/function';
import {
  BOX_goToGame,
  platform,
  BOX_checkInstall,
  BOX_openInNewWindow,
  BOX_openApp,
  BOX_openInNewNavWindow,
  BOX_login,
} from '@/utils/box.uni.js';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
import PlatformCoinExchangeProp from '@/components/platform-coin-exchange-prop';
import { ApiGetPayUrl, ApiGetOrderStatus } from '@/api/views/recharge.js';
import { mapGetters } from 'vuex';
function debounce(fn, delay) {
  let timer = null;
  return function (value) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.call(this, value);
    }, delay);
  };
}
export default {
  name: 'PlatformCoinSwapProps',
  props: {},
  components: { xhCreateTipDialog, PlatformCoinExchangeProp },
  data() {
    return {
      platform,
      isResult: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      timer: null,
      propsListInfo: [],
      keyword: '',
      page: 1,
      listRows: 20,
      isExpanded: [],
      cardOrderInfo: {}, //card订单信息
      currentGameInfo: {}, //当前游戏
      currentCardInfo: {}, //当前道具
      downGameShow: false, //下载游戏弹窗
      platformCoinShow: false, //平台币不足提示弹窗
      unmetTitle: '抱歉，您当前尚未创建角色，请先下载游戏并创建角色',
      platformCoinTitle: '您的平台币不足，请前往充值',
      copyDialogShow: false, //复制礼包弹窗
      dialogInfo: {
        cardpass: '',
      },
      introduction: '在"我的-我的礼包"查看',
      xhDialogShow: false, //小号选择弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      xiaohaoListShowItem: {},
      currentXiaohao: {}, //当前选择小号
      platformCoinExchangePropShow: false, //兑换道具弹窗
      payWay: {}, // 支付方式的对象
      order_id: '', //当前订单编号
      pageLoading: true,
      isExchanging: false, // 添加标志变量来防止重复点击
      showReturnTop: false,
    };
  },
  async created() {
    this.pageLoading = true;
    try {
      await this.getDataList();
    } finally {
      this.pageLoading = false;
    }
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  deactivated() {
    this.removeScrollEvent();
  },
  watch: {
    keyword: {
      handler: debounce(function () {
        this.getDataList();
      }, 500),
    },
  },
  methods: {
    addScrollEvent() {
      const scrollTarget = document.querySelector('.props-container');

      if (scrollTarget) {
        scrollTarget.addEventListener('scroll', this.handleScroll);
      }
    },
    removeScrollEvent() {
      const scrollTarget = document.querySelector('.props-container');
      if (scrollTarget) {
        scrollTarget.removeEventListener('scroll', this.handleScroll);
      }
    },
    handleScroll() {
      let windowScrollTop =
        document.querySelector('.props-container').scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      document.querySelector('.props-container').scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    openGame(detail) {
      BOX_openApp(detail.package_name);
    },
    openGameShow(detail) {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          return BOX_checkInstall(detail.package_name);
        } catch (error) {
          return false;
        }
      } else {
        return false;
      }
    },
    ptbInsufficient() {
      this.platformCoinShow = true;
    },
    choosePayType(selectedPayType) {
      this.payWay = selectedPayType;
      this.$toast.loading();
      this.handlePay();
    },
    closePopup() {
      this.platformCoinExchangePropShow = false;
    },
    async GetOrderCardPass() {
      const res = await ApiCarGetOrderCardPass({
        order_id: this.order_id,
      });
      if (res.data.cardpass) {
        this.dialogInfo.cardpass = res.data.cardpass;
        this.platformCoinExchangePropShow = false;
        this.copyDialogShow = true;
        return;
      }
    },
    handlePay() {
      this.$toast.loading('加载中');
      ApiCarPtbCardToPay({
        card_id: this.currentCardInfo.id,
        xh_id: this.currentXiaohao.id,
        payWay: this.payWay.symbol,
      }).then(orderRes => {
        this.order_id = orderRes.data.orderId;
        if (this.payWay.symbol == 'ptb') {
          if (orderRes.data.cardpass) {
            this.dialogInfo.cardpass = orderRes.data.cardpass;
            this.copyDialogShow = true;
            return;
          }
          return;
        }
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 503,
          payWay: this.payWay.symbol,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 503,
          })
            .then(res2 => {
              this.platformCoinExchangePropShow = false;
            })
            .catch(() => {
              this.releaseGiftOrder();
            })
            .finally(() => {
              this.GetOrderCardPass();
              this.getDataList();
            });
        });
      });
    },
    async getCarPtbCardOrderInfo() {
      try {
        const res = await ApiCarPtbCardOrderInfo({
          card_id: this.currentCardInfo.id,
          xh_id: this.currentXiaohao.id,
        });
        this.platformCoinExchangePropShow = true;
        this.cardOrderInfo = res.data;
      } finally {
        this.$toast.clear();
      }
    },
    // 兑换
    async exchangeImmediately(game, card) {
      if (this.isExchanging) return;
      try {
        this.isExchanging = true;

        if (!this.userInfo.token) {
          if (platform == 'android') {
            BOX_login();
          } else {
            this.$router.push({ name: 'PhoneLogin' });
          }
          return;
        }
        this.$toast({
          type: 'loading',
          duration: 0,
          message: '兑换中...',
        });
        this.order_id = '';
        this.currentGameInfo = game;
        this.currentCardInfo = card;

        // 获取游戏小号
        await this.getXiaohaoList();

        if (!this.xiaohaoList.length) {
          this.$toast.clear();
          this.downGameShow = true;
        } else if (this.xiaohaoList.length == 1) {
          this.xiaohaoListShowItem = this.xiaohaoList[0];
          this.currentXiaohao = this.xiaohaoList[0];
          this.checkXhPtbExchange();
        } else {
          this.$toast.clear();
          this.xiaohaoListShowItem = this.xiaohaoList[0];
          this.currentXiaohao = this.xiaohaoList[0];
          this.xhDialogShow = true;
        }
      } finally {
        setTimeout(() => {
          this.isExchanging = false;
        }, 500);
      }
    },
    // 检查小号是否能兑换
    async checkXhPtbExchange() {
      if (!this.currentXiaohao.id) return;
      await ApiCarCheckPtbCard({
        card_id: this.currentCardInfo.id,
        xh_id: this.currentXiaohao.id,
      });
      this.getCarPtbCardOrderInfo();
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    // 选择完小号执行兑换操作
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.checkXhPtbExchange();
    },
    // 获取游戏小号列表 用来判断是否下载注册过
    async getXiaohaoList() {
      this.xiaohaoList = [];
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.currentGameInfo.id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
    },
    copy(info) {
      this.$copyText(info.cardpass).then(
        res => {
          this.$toast(this.$t('复制成功'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    toPlatformCoin() {
      this.platformCoinShow = false;
      this.$nextTick(() => {
        BOX_openInNewWindow(
          { name: 'PlatformCoin' },
          { url: this.$h5Page.ptb_url },
        );
      });
    },
    openOrDowGame() {
      this.downGameShow = false;
      this.$nextTick(() => {
        BOX_goToGame(
          {
            params: {
              id: this.currentGameInfo.id,
            },
          },
          { id: this.currentGameInfo.id },
        );
        if (platform === 'android') {
          window.BOX.startDownload(JSON.stringify(this.currentGameInfo));
        }
      });
    },
    clearInput() {
      this.keyword = '';
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
    },
    // 点击搜索
    searchBtn() {
      if (!this.keyword) {
        return;
      }
      if (this.keyword) {
        this.page = 1;
        this.getDataList();
      }
    },
    goToDetail(game) {
      if (platform == 'android') {
        BOX_goToGame(
          {
            params: {
              id: game.id,
            },
          },
          { id: game.id },
        );
        window.BOX.startDownload(JSON.stringify(this.currentGameInfo));
        return false;
      }
      navigateToGameDetail(game);
    },
    discountTag(item) {
      if (item.classid != 107) {
        return '';
      }
      if (item.f_pay_rebate && item.f_pay_rebate != 100) {
        return `${parseFloat(item.f_pay_rebate) / 10}`;
      } else if (item.pay_rebate && item.pay_rebate != 100) {
        return `${parseFloat(item.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
    async getDataList(action = 1) {
      try {
        if (action === 1) {
          this.finished = false;
          this.page = 1;
        } else {
          if (this.finished) {
            return;
          }
          this.page++;
        }
        const res = await ApiCarRedeemItems({
          page: this.page,
          listRows: this.listRows,
          title: this.keyword,
        });
        let { list } = res.data;
        if (action === 1 || this.page == 1) {
          this.propsListInfo = [];
          if (!list.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.propsListInfo.push(...list);
        this.loadingObj.loading = false;
        if (list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished) {
            this.finished = false;
          }
        }
      } catch (error) {
      } finally {
        this.$nextTick(() => {
          setTimeout(() => {
            this.addScrollEvent();
          }, 100);
        });
      }
    },
    async onRefresh() {
      await this.getDataList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.propsListInfo.length) {
        await this.getDataList();
      } else {
        await this.getDataList(2);
      }
      this.loadingObj.loading = false;
    },
    toggleExpand(index) {
      this.$set(this.isExpanded, index, !this.isExpanded[index]);
    },
    // 释放礼包订单
    async releaseGiftOrder() {
      if (!this.order_id) return false;
      try {
        await ApiCarGetCancelOrder({
          order_id: this.order_id,
        });
      } catch (error) {
      } finally {
        this.order_id = '';
      }
    },
  },
  computed: {
    inputClearShow() {
      return this.keyword.length ? true : false;
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
};
</script>

<style lang="less" scoped>
/deep/.van-dialog {
  overflow: visible;
}
.props-page {
  overflow: hidden;
  background: #f7f8fa;
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .game-list-search {
    position: fixed;
    z-index: 100;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    background: #f7f8fa;
    left: 0;
    width: 100%;
    padding: 11 * @rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .search-bar {
      box-sizing: border-box;
      padding: 0 12 * @rem 0 18 * @rem;
      width: 351 * @rem;
      height: 36 * @rem;
      border-radius: 35 * @rem;
      display: flex;
      align-items: center;
      background: #ffffff;
      .search-input {
        flex: 1;
        height: 33 * @rem;
        margin-left: 7 * @rem;
        form {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          border: 0;
          outline: 0;
          display: block;
          width: 100%;
          height: 100%;
          background-color: transparent;
          font-size: 14 * @rem;
          color: #333;
        }
      }
      .input-clear {
        width: 14 * @rem;
        height: 14 * @rem;
        .image-bg('~@/assets/images/input-clear.png');
        margin: 0 12 * @rem;
      }
      .search-icon {
        width: 14 * @rem;
        height: 14 * @rem;
        background: url(~@/assets/images/welfare/props-search-icon.png) center
          center no-repeat;
        background-size: 14 * @rem 14 * @rem;
      }
    }
  }
  .props-container {
    display: flex;
    flex-direction: column;
    height: calc(100% - 61 * @rem - @safeAreaBottomEnv);
    height: calc(100% - 61 * @rem - @safeAreaBottom);
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 61 * @rem;
    .props-content {
      flex: 1;
      .props-list {
        .props-item {
          width: 351 * @rem;
          margin: 0 auto 0;
          background: #ffffff;
          border-radius: 17 * @rem;
          box-sizing: border-box;
          padding: 14 * @rem 12 * @rem 16 * @rem;
          &:not(:first-child) {
            margin-top: 12 * @rem;
          }
          .props-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            .game-info {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 327 * @rem;
              height: 76 * @rem;
              box-sizing: border-box;
              padding: 0 12 * @rem 0 10 * @rem;
              border-radius: 17 * @rem;
              z-index: 2;
              &::before {
                content: '';
                background: linear-gradient(
                  180deg,
                  rgba(203, 227, 255, 1),
                  rgba(242, 247, 255, 1)
                );
                width: 327 * @rem;
                height: 76 * @rem;

                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border-radius: 17 * @rem;
                z-index: -2;
              }
              &::after {
                content: '';
                background: linear-gradient(
                  180deg,
                  #ecf8ff 0%,
                  rgba(255, 255, 255, 1) 100%
                );
                width: 325 * @rem;
                height: 74 * @rem;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border-radius: 17 * @rem;
                z-index: -1;
              }
              .left-box {
                display: flex;
                align-items: center;
                .title-pic {
                  width: 48 * @rem;
                  height: 48 * @rem;
                  border-radius: 10 * @rem;
                }
                .content-box {
                  margin-left: 6 * @rem;
                  .game-name {
                    width: 180 * @rem;
                    display: flex;
                    align-items: center;
                    .title {
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      font-weight: 600;
                      font-size: 15 * @rem;
                      color: #191b1f;
                      .title-content {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      }
                    }
                    .subtitle {
                      flex-shrink: 0;
                      white-space: nowrap;
                      margin-left: 6 * @rem;
                      padding: 0 4 * @rem;
                      box-sizing: border-box;
                      font-weight: 400;
                      font-size: 11 * @rem;
                      color: #93999f;
                      height: 18 * @rem;
                      border-radius: 4 * @rem;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      border: 1 * @rem solid #e3e5e8;
                    }
                  }
                  .discount-tag {
                    display: flex;
                    align-items: center;
                    width: fit-content;
                    margin-right: 8 * @rem;
                    flex-shrink: 0;
                    margin-top: 5 * @rem;
                    .discount-icon {
                      width: 30 * @rem;
                      height: 18 * @rem;
                      position: relative;
                      z-index: 1;
                      &.discount-01 {
                        width: 49 * @rem;
                      }
                    }
                    .discount-text {
                      display: flex;
                      align-items: center;
                      height: 18 * @rem;
                      padding-right: 4 * @rem;
                      flex: 1;
                      min-width: 0;
                      font-size: 11 * @rem;
                      color: #ff6649;
                      white-space: nowrap;
                      background-color: #fff5ed;
                      border-radius: 0 2 * @rem 2 * @rem 0;
                      margin-left: -5 * @rem;
                      padding-left: 5 * @rem;
                    }
                  }
                }
              }
              .right-btn {
                background: #ecfbf4;
                font-weight: 500;
                font-size: 13 * @rem;
                color: #2bbe88;
                span {
                  border: 1 * @rem solid #1cce94;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 58 * @rem;
                  height: 26 * @rem;
                  border-radius: 29 * @rem;
                }
                .open-btn {
                  background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
                  border: none;
                  border-radius: 29 * @rem;
                  color: #ffffff;
                }
              }
            }
            .exchange-info {
              position: relative;
              width: 327 * @rem;
              height: 73 * @rem;
              background: #f7f8fa;
              border-radius: 9 * @rem;
              display: flex;
              align-items: center;
              justify-content: space-between;
              box-sizing: border-box;
              padding: 0 12 * @rem 0 10 * @rem;
              margin-top: 6 * @rem;
              .exchange-left-box {
                flex: 1;
                display: flex;
                align-items: center;
                overflow: hidden;
                .icon {
                  flex-shrink: 0;
                  width: 45 * @rem;
                  height: 45 * @rem;
                  background: #ffffff;
                  border-radius: 11 * @rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  overflow: hidden;
                  .icon-box {
                    width: 35 * @rem;
                    height: 35 * @rem;
                  }
                }
                .info {
                  flex: 1;
                  width: 180 * @rem;
                  margin-left: 6 * @rem;
                  display: flex;
                  flex-direction: column;
                  .name {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-weight: 600;
                    font-size: 14 * @rem;
                    color: #242840;
                  }

                  .num {
                    margin-top: 8 * @rem;
                    display: flex;
                    align-items: center;
                    .text1,
                    .text2,
                    .text3 {
                      color: #777777;
                      font-size: 12 * @rem;
                    }
                    .text2 {
                      color: #ff6b69;
                    }
                  }
                }
              }
              .exchange-right-box {
                width: 70 * @rem;
                height: 100%;
                .exchange-right-btn {
                  position: absolute;
                  right: 12 * @rem;
                  bottom: 17 * @rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0 7 * @rem;
                  box-sizing: border-box;
                  height: 22 * @rem;
                  font-size: 11 * @rem;
                  font-weight: 500;
                  color: #fff;
                  background: #1cce94;
                  border-radius: 29 * @rem;
                  &.grey_active {
                    background: #e3e5e8;
                    color: #93999f;
                  }
                }
                .prompt {
                  position: absolute;
                  right: 0;
                  top: 0;
                  font-weight: 400;
                  font-size: 10 * @rem;
                  color: #60666c;
                  min-width: 58 * @rem;
                  max-width: 70 * @rem;
                  padding: 0 10 * @rem 0 12 * @rem;
                  height: 22 * @rem;
                  overflow: hidden;
                  line-height: 22 * @rem;
                  box-sizing: border-box;
                  white-space: nowrap;
                  background: #f0f1f5;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 0 8 * @rem 0 14 * @rem;
                  .prompt-content {
                    overflow: hidden;
                    display: flex;
                  }
                }
              }
            }
          }
          .more-props {
            margin-top: 14 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            .more-btn {
              display: flex;
              align-items: center;
              .text {
                font-weight: 400;
                font-size: 12 * @rem;
                color: #777777;
              }
              .arrow {
                margin-left: 4 * @rem;
                background: url('~@/assets/images/games/prop-arrow-icon.png')
                  no-repeat center center;
                width: 11 * @rem;
                height: 7 * @rem;
                background-size: 11 * @rem 7 * @rem;
                transition: transform 0.3s ease;
                &.rotated {
                  transform: rotate(180deg);
                }
              }
            }
          }
        }
      }
    }
  }
}
.create-dialog {
  width: 300 * @rem;
  background: transparent;
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 300 * @rem;
    height: 180 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    z-index: 2;
    padding: 30 * @rem 31 * @rem 20 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &.h-148 {
      height: 148 * @rem;
    }
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 20 * @rem;
    }
    .message {
      margin-top: 25 * @rem;
      height: 40 * @rem;
      font-weight: 400;
      font-size: 15 * @rem;
      color: #60666c;
      line-height: 18 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      word-break: break-all;
      &.mess-t0 {
        margin-top: 0 * @rem;
      }
    }
    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 25 * @rem;
      position: relative;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 100%;
        height: 38 * @rem;
        line-height: 38 * @rem;
        color: #ffffff;
        font-size: 15 * @rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeColor;
        border-radius: 18 * @rem;
      }
      .btn-close {
        position: absolute;
        bottom: -72 * @rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          width: 28 * @rem;
          height: 28 * @rem;
          background: url('~@/assets/images/games/btn-close.png') no-repeat
            center center;
          background-size: 28 * @rem 28 * @rem;
        }
      }
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 300 * @rem;
  border-radius: 29 * @rem;
  background-color: #fff;
  padding: 30 * @rem 31 * @rem 20 * @rem;
  overflow: unset;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;

    .title-text {
      line-height: 20 * @rem;
      font-size: 16 * @rem;
      color: #333333;
      font-weight: 600;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 100%;
    height: 70 * @rem;
    background-color: #f5f5f5;
    border-radius: 8 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 10 * @rem;
    margin-top: 27 * @rem;
    font-size: 16 * @rem;
    line-height: 22 * @rem;
    color: #333333;
    font-weight: 600;
    word-break: break-all;
  }
  .desc {
    font-size: 11 * @rem;
    line-height: 15 * @rem;
    color: #777777;
    font-weight: 400;
    margin-top: 10 * @rem;
    text-align: center;
    word-break: break-all;
  }
  .copy-btn {
    width: 238 * @rem;
    height: 38 * @rem;
    margin: 21 * @rem auto 0;
    background: @themeColor;
    border-radius: 38 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14 * @rem;
    font-weight: 400;
    color: #ffffff;
  }
  .close-btn {
    width: 32 * @rem;
    height: 32 * @rem;
    position: absolute;
    bottom: -58 * @rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    .image-bg('~@/assets/images/games/get-gift-popup-close-btn.png');
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.text-scroll {
  white-space: nowrap;
  animation: scroll-left 3s linear forwards infinite;
}
.return-top {
  position: fixed;
  right: 25 * @rem;
  bottom: 50 * @rem;
  width: 38 * @rem;
  height: 38 * @rem;
  background: url(~@/assets/images/exclusive-activity/return-top.png) center
    center no-repeat;
  background-size: 38 * @rem 38 * @rem;
  z-index: 100;
  &.az {
    bottom: 50 * @rem;
  }
}
.text-scroll-fast {
  white-space: nowrap;
  animation: scroll-left-fast 3s linear forwards infinite;
}
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(-50%);
  }
}
@keyframes scroll-left-fast {
  0% {
    transform: translateX(0);
  }
  70% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(-50%);
  }
}
</style>
