<template>
  <van-dialog
    v-model="popupShow"
    :close-on-click-overlay="false"
    message-align="left"
    :lock-scroll="false"
    class="gzh-popup"
    :show-confirm-button="false"
  >
    <div class="gzh-container">
      <div class="gzh-title">如何关注3733游戏微信公众号</div>
      <div class="swiper-container">
        <van-swipe
          v-if="popupShow"
          class="my-swipe"
          :autoplay="3000"
          indicator-color="#32B768"
          :loop="false"
        >
          <van-swipe-item v-for="(item, index) in picList" :key="index">
            <div class="pic-item">
              <img :src="item.src" alt="" />
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="bottom-btn" @click="goToWx">前往微信关注</div>
    </div>
    <div class="close" @click="popupShow = false"></div>
  </van-dialog>
</template>

<script>
import { BOX_openWx } from '@/utils/box.uni.js';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      picList: [
        {
          src: require('@/assets/images/kefu/gzh-1.png'),
        },
        {
          src: require('@/assets/images/kefu/gzh-2.png'),
        },
        {
          src: require('@/assets/images/kefu/gzh-3.png'),
        },
        {
          src: require('@/assets/images/kefu/gzh-4.png'),
        },
        {
          src: require('@/assets/images/kefu/gzh-5.png'),
        },
      ],
    };
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
  methods: {
    goToWx() {
      this.$toast(
        '已复制公众号名称，\n去微信搜索公众号，\n将于2秒后唤起微信页面',
      );
      setTimeout(() => {
        this.popupShow = false;
        BOX_openWx();
      }, 2000);
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.gzh-popup {
  width: 320 * @rem;
  background: transparent;
  .gzh-container {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16 * @rem;
    width: 320 * @rem;
    padding: 28 * @rem 20 * @rem;
    .gzh-title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
      padding-bottom: 6 * @rem;
    }
    .swiper-container {
      width: 280 * @rem;
      height: 330 * @rem;
      margin-top: 20 * @rem;
      .pic-item {
        width: 280 * @rem;
        height: 330 * @rem;
      }
    }
    .bottom-btn {
      width: 205 * @rem;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
      font-size: 15 * @rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20 * @rem auto 0;
    }
  }
  .close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/close.png) center center no-repeat;
    background-size: 28 * @rem 28 * @rem;
    margin: 28 * @rem auto 0;
  }
}
</style>
