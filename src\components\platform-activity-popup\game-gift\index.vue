<template>
  <div class="game-gift-page">
    <div class="xiaohao-fixed-placeholder" ref="xiaohaoFixed">
      <div
        class="xiaohao-fixed"
        v-if="currentXiaohao.nickname"
        @click="changeXiaohao"
      >
        <div class="xiaohao-name">
          {{ $t('小号') }}：{{ currentXiaohao.nickname }}
        </div>
        <div class="change-xiaohao">
          {{ $t('切换') }}<span class="change-xiaohao-icon"></span>
        </div>
      </div>
      <div
        class="xiaohao-fixed"
        :class="{ 'not-xiaohao-bg': class_id == 115 }"
        v-else
        >{{ fixedText }}</div
      >
    </div>
    <div class="tabs-list">
      <div class="tabs-item">
        <div
          class="item"
          v-for="(item, index) in tabsList"
          :key="index"
          :class="{ active: currentTab === index }"
          @click="clickTab(index)"
        >
          <span>{{ item.title }}</span>
        </div>
      </div>
    </div>
    <div class="gift-container">
      <div class="gift-content">
        <content-empty
          tips="暂无可领取的礼包"
          :emptyImg="emptyImg"
          v-if="
            (isLoadingOver && empty) ||
            (isLoadingOver && resultGiftList.every(gifts => gifts.length === 0))
          "
        ></content-empty>
        <load-more
          class="gift-bar"
          v-else
          v-model="loading"
          :finished="finished"
          @loadMore="loadMore()"
          :check="false"
        >
          <div class="gift-list-container">
            <div class="tabs">
              <template v-for="(gifts, index) in resultGiftList">
                <div :key="index" v-if="gifts.length" class="tab">
                  <div class="type-small-title">{{ gifts[0].class_name }}</div>
                  <div class="gift-list" v-if="gifts.length">
                    <div class="gift-item" v-for="item in gifts" :key="item.id">
                      <div class="gift-info-wrap">
                        <div class="info-content">
                          <div class="gift-img">
                            <img :src="item.card_icon" alt="" />
                          </div>
                          <div
                            class="gift-info btn"
                            @click="
                              $router.push({
                                name: 'GiftDetail',
                                params: { gift_id: item.id, game_id: gameId },
                              })
                            "
                          >
                            <div class="gift-name">
                              {{ item.title }}
                            </div>
                            <div class="gift-desc">
                              {{ item.cardbody }}
                            </div>
                          </div>
                        </div>
                        <div class="get">
                          <span
                            v-if="item.remain !== 0 && !item.cardpass"
                            @click.stop="getGift(item)"
                          >
                            {{ $t('领取') }}</span
                          >
                          <span class="already" v-else-if="item.cardpass"
                            >已领取</span
                          >
                          <span
                            class="already"
                            v-else-if="item.remain == 0 && !item.cardpass"
                            >暂无库存</span
                          >
                        </div>
                      </div>
                      <div class="dashed-line" v-if="item.cardpass"></div>
                      <div class="gift-info-code" v-if="item.cardpass">
                        <div class="gift-code">
                          {{ $t('礼包码') }}: <span>{{ item.cardpass }}</span>
                        </div>
                        <div class="get-copy copy" @click.stop="copy(item)">
                          <div class="copy-icon"> </div>
                          <span>{{ $t('复制') }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </load-more>
      </div>
    </div>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="false"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-text">礼包已领取，您的兑换码</div>
      </div>
      <div class="cardpass">{{ dialogInfo.cardpass }}</div>
      <div class="desc" v-html="introduction"></div>
      <div class="copy-btn btn" @click="copy(dialogInfo)">
        {{ $t('复制礼包码') }}
      </div>
      <div class="close-btn" @click="copyDialogShow = false"></div>
    </van-dialog>
    <!-- 复制成功弹窗 -->
    <cardpass-copy-success-popup
      :info="dialogInfo"
      :show.sync="copySuccessPopupShow"
      @close-popup="closePopup"
    ></cardpass-copy-success-popup>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(gameId)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import { themeColorLess, remNumberLess } from '@/common/styles/_variable.less';
import { ApiCardIndex, ApiCardGet } from '@/api/views/gift.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
export default {
  game: 'GameGift',
  components: {
    xhCreateTipDialog,
  },
  props: {
    gameId: {
      type: Number,
      default: 0,
    },
    class_id: {
      type: Number,
      default: 0,
    },
    popupCounterNum: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      themeColorLess,
      remNumberLess,
      // gameId: this.$route.params.game_id,
      stickyOffsetTop: '0*@rem', // 顶部导航栏高度
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      xiaohaoListShowItem: {},
      currentXiaohao: {}, //当前选择小号
      giftList: [],
      page: 1,
      listRows: 100, // 一次性把全部礼包返回，不然拿不到所有分类
      finished: false,
      loading: false,
      empty: false,
      dialogInfo: {},
      fixedText: '',
      tabIndex: 0, // 顶部导航索引
      // class_id: this.$route.params.class_id,
      isLoadingOver: false, //列表加载完毕
      copySuccessPopupShow: false,
      currentTab: 0, //0全部 1未领取 2已领取
      tabsList: [
        {
          title: '全部',
        },
        {
          title: '未领取',
        },
        {
          title: '已领取',
        },
      ],
      emptyImg,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    resultGiftList() {
      // 根据当前选中的下标过滤礼包列表
      let gifts = [
        this.svipGift,
        this.changwanGift,
        this.chargeGift,
        this.commonGift,
        this.internalGift,
      ];

      // 根据当前下标过滤
      if (this.currentTab === 1) {
        // 未领取：过滤出 cardpass 没有值的礼包
        return gifts.map(giftCategory =>
          giftCategory.filter(gift => !gift.cardpass),
        );
      } else if (this.currentTab === 2) {
        // 已领取：过滤出 cardpass 有值的礼包

        return gifts.map(giftCategory =>
          giftCategory.filter(gift => gift.cardpass),
        );
      }

      // 全部：不过滤
      return gifts;
    },
    commonGift() {
      return this.giftList.filter(item => {
        return ![22, 23, 24, 26].includes(Number(item.classid));
      });
    },
    chargeGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 22;
      });
    },
    svipGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 23;
      });
    },
    changwanGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 24;
      });
    },
    internalGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 26;
      });
    },
    introduction() {
      return `${this.$t('使用说明')}：${this.dialogInfo.cardtext}`;
    },
  },
  async created() {
    if (Number(this.class_id) != 115) {
      await this.getCurrentXiaohaoId();
    }
    await this.getList();
  },
  mounted() {
    // 获取顶部导航栏的高度
    // this.$nextTick(() => {
    //   this.stickyOffsetTop =
    //     this.$refs.topNavBar.clientHeight +
    //     this.$refs.xiaohaoFixed.clientHeight +
    //     '*@rem';
    // });
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    clickTab(index) {
      if (!this.isLoadingOver) return false;
      this.currentTab = index;
    },
    async getCurrentXiaohaoId() {
      try {
        const res = await ApiXiaohaoMyListByGameId({
          gameId: this.gameId,
        });
        const { list, text } = res.data;
        if (list && list.length) {
          this.xiaohaoList = list;
          // 判断是否有已选择小号
          let flag = list.some(item => {
            return item.id == this.xiaohaoMap[this.gameId]?.id;
          });
          if (flag) {
            this.currentXiaohao = this.xiaohaoMap[this.gameId];
          } else {
            this.currentXiaohao = list[0];
            this.setXiaohaoMap([this.gameId, list[0]]);
          }
        } else {
          this.xiaohaoList = [];
          this.currentXiaohao = {};
          this.setXiaohaoMap([this.gameId, {}]);
          if (text) this.fixedText = text;
        }
      } catch (error) {}
    },
    changeXiaohao() {
      if (!this.isLoadingOver) {
        this.$toast('稍后片刻，加载中...');
        return false;
      }
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    getGift(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (item.remain == 0) {
          this.$toast(this.$t('礼包已被抢光啦~'));
          return false;
        }
        if (!this.currentXiaohao.id && Number(this.class_id) != 115) {
          this.createDialogShow = true;
          return false;
        }
        this.$toast.loading({
          message: this.$t('加载中...'),
        });
        ApiCardGet({
          cardId: item.id,
          xhId: this.currentXiaohao.id,
        }).then(
          res => {
            this.$toast.clear();
            this.updateCardpass(item, res.data.cardpass);
            this.showCopyBar(res.data);

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${item.game_id}`,
              adv_id: '暂无',
              game_name: item.titlegame,
              game_type: `${item.classid}`,
              game_size: '暂无',
              reward_type: item.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });
          },
          e => {
            this.$toast.clear();
            this.$dialog
              .confirm({
                title: '提示',
                message: e.msg,
                confirmButtonText: '打开游戏',
              })
              .then(() => {
                this.toPage('GameDetail', { id: item.game_id });
              });
          },
        );
      }
    },
    showCopyBar(info) {
      this.dialogInfo = info;
      this.copyDialogShow = true;
    },
    copy(info) {
      this.$copyText(info.cardpass).then(
        res => {
          this.dialogInfo = info;
          // this.$toast(this.$t('复制成功'));
          this.copySuccessPopupShow = true;
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    async getList(action = 1) {
      try {
        if (action === 1) {
          this.page = 1;
        } else {
          if (this.finished) {
            return;
          }
          this.page++;
        }
        this.isLoadingOver = false;
        this.loading = true;
        const res = await ApiCardIndex({
          page: this.page,
          listRows: this.listRows,
          gameId: this.gameId,
          xhId: this.currentXiaohao.id || '',
        });
        if (action === 1 || this.page === 1) {
          this.giftList = [];
          if (!res.data.list.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.giftList.push(...res.data.list);
        this.loading = false;
        if (res.data.list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (error) {
      } finally {
        this.isLoadingOver = true;
      }
    },
    async loadMore() {
      if (!this.giftList.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }
      this.loading = false;
    },
    updateRemain(giftItem) {
      this.$set(this.giftList[this.findGiftIndex(giftItem)], 'remain', 0);
    },
    updateCardpass(giftItem, cardpass) {
      this.$set(
        this.giftList[this.findGiftIndex(giftItem)],
        'cardpass',
        cardpass,
      );
    },
    findGiftIndex(giftItem) {
      return this.giftList.findIndex(item => {
        return item.id == giftItem.id;
      });
    },
    getChargeGift(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        this.$toast.loading({
          message: this.$t('加载中...'),
          forbidClick: false,
        });
        ApiXiaohaoMyListByCard({
          cardId: item.id,
        }).then(
          res => {
            this.$toast.clear();
            this.xiaohaoList = res.data.list;
            this.currentXiaohao = this.xiaohaoList[0];
            this.xhDialogShow = true;
            this.currentGiftId = item.id;
          },
          () => {},
        );
      }
    },
    closePopup() {
      this.$emit('close-popup');
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.setXiaohaoMap([this.gameId, this.currentXiaohao]);
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getList();
      this.$toast.clear();
    },
  },
  watch: {
    popupCounterNum(newVal) {
      if (Number(this.class_id) != 115) {
        this.getCurrentXiaohaoId();
      }
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
.game-gift-page {
  background-color: #f5f5f6;
  .pull-refresh {
    background-color: #f5f5f6;
  }
  .xiaohao-fixed-placeholder {
    position: relative;
    width: 100%;
    height: 36 * @rem;
    flex-shrink: 0;
  }
  .xiaohao-fixed {
    position: fixed;
    z-index: 100;
    .fixed-center;
    box-sizing: border-box;
    padding: 0 12 * @rem;
    width: 100%;
    height: 36 * @rem;
    line-height: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #eafbf3;
    color: #191b1f;
    font-size: 12 * @rem;
    .change-xiaohao {
      display: flex;
      align-items: center;

      .change-xiaohao-icon {
        margin-left: 6 * @rem;
        width: 14 * @rem;
        height: 14 * @rem;
        .image-bg('~@/assets/images/games/change-xiaohao-icon-new.png');
      }
    }
  }
  .tabs-list {
    padding: 0 12 * @rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tabs-item {
      width: 192 * @rem;
      height: 42 * @rem;
      display: flex;
      align-items: center;
      .item {
        box-sizing: border-box;
        width: 192 * @rem;
        height: 26 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        border: 1 * @rem solid #ffffff;
        color: #93999f;
        display: flex;
        align-items: center;
        justify-content: center;
        &.active {
          border-radius: 6 * @rem;
          background: #ecfbf4;
          border: 1 * @rem solid #ecfbf4;
          font-weight: 500;
          color: #1cce94;
        }
      }
    }
    .instruction {
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
  .gift-container {
    background-color: #f5f5f6;
    height: 350 * @rem;

    .gift-navs {
      overflow: auto;
      display: flex;
      justify-content: center;
      padding: 20 * @rem 0;
      .gift-nav {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 90 * @rem;
        height: 30 * @rem;
        border-radius: 6 * @rem;
        background-color: #dcdcdc;
        font-size: 15 * @rem;
        color: #ffffff;
        margin: 0 10 * @rem;
        &.active {
          background: @themeColor;
        }
      }
    }

    .gift-content {
      height: 350 * @rem;
      overflow-y: auto;
      /deep/.empty {
        height: 100%;
      }
      &::-webkit-scrollbar {
        display: none;
      }
      .type-title {
        padding: 20 * @rem 0 10 * @rem;
        display: flex;
        .title-text {
          padding: 0 10 * @rem;
          height: 30 * @rem;
          font-size: 14 * @rem;
          color: #fff;
          background: @themeColor;
          border-radius: 0 * @rem 15 * @rem 15 * @rem 0 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .type-small-title {
        padding: 16 * @rem 12 * @rem 0;
        font-weight: 600;
        font-size: 15 * @rem;
        color: #191b1f;
      }
      .gift-bar {
        display: flex;
        flex-direction: column;
        .gift-list-container {
          .tabs {
            /deep/ .van-sticky {
              width: 100%;
              position: fixed !important;
              z-index: 1000;
              top: calc(82 * @rem + @safeAreaTop) !important;
              top: calc(82 * @rem + @safeAreaTopEnv) !important;
            }
            /deep/ .van-sticky--fixed {
              top: calc(82 * @rem + @safeAreaTop) !important;
              top: calc(82 * @rem + @safeAreaTopEnv) !important;
            }
            /deep/ .van-tabs__wrap {
              height: 48 * @rem;
            }
            /deep/ .van-tabs__nav {
              background-color: #f5f5f6;
              padding: 0 18 * @rem;
            }
            /deep/ .van-tab {
              height: 48 * @rem;
              flex-shrink: 0;
              flex-grow: 0;
              padding: 0;
              &:not(:first-of-type) {
                margin-left: 10 * @rem;
              }
            }
            /deep/ .van-tab--active {
              .tab-item {
                background-color: @themeColor;
                color: #ffffff;
              }
            }
            .tab-item {
              height: 28 * @rem;
              background-color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 15 * @rem;
              border-radius: 14 * @rem;
              font-size: 13 * @rem;
              color: #000000;
              white-space: nowrap;
            }
            /deep/ .van-tabs__line {
              display: none;
            }
            .tab {
              &:not(:first-of-type) {
                .type-small-title {
                  padding-top: 20 * @rem;
                }
              }
            }
          }
        }
      }
      .gift-list {
        .gift-item {
          box-sizing: border-box;
          padding: 15 * @rem 12 * @rem 10 * @rem 15 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          background-color: #fff;
          width: 351 * @rem;
          background: #ffffff;
          border-radius: 12 * @rem;
          margin: 12 * @rem auto 0;
          .gift-info-wrap {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .info-content {
              display: flex;
              align-items: center;
              .gift-img {
                width: 40 * @rem;
                height: 40 * @rem;
              }
              .gift-info {
                margin-left: 10 * @rem;
                flex: 1;
                min-width: 0;
                height: 40 * @rem;
                width: 186 * @rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                .gift-name {
                  font-size: 14 * @rem;
                  color: #303236;
                  font-weight: bold;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .gift-desc {
                  margin-top: 4 * @rem;
                  font-size: 11 * @rem;
                  color: #93999f;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .gift-left {
                  display: flex;
                  align-items: center;
                  margin-top: 15 * @rem;
                  .totals {
                    width: 170 * @rem;
                    height: 6 * @rem;
                    border-radius: 3 * @rem;
                    overflow: hidden;
                    background: #ebebeb;
                    .left {
                      width: 0%;
                      height: 100%;
                      background: #ff7554
                        linear-gradient(270deg, #fdba42 0%, #fe5b5b 100%);
                      border-radius: 3 * @rem;
                    }
                  }
                  i {
                    display: block;
                    width: 18 * @rem;
                    height: 18 * @rem;
                    .image-bg('~@/assets/images/games/gift-title-icon.png');
                    margin-left: 12 * @rem;
                  }
                  .left-text {
                    font-size: 12 * @rem;
                    color: #666666;
                    margin-left: 8 * @rem;
                    white-space: nowrap;
                    span {
                      color: #ff4f4f;
                    }
                  }
                }
              }
            }
          }
          .dashed-line {
            margin: 16 * @rem 0 12 * @rem 0;
            width: 100%;
            height: 1 * @rem;
            background-color: #e9ecf1;
            mask-image: linear-gradient(90deg, #e9ecf1 50%, transparent 0%);
            mask-size: 8 * @rem 100%;
          }
          .gift-info-code {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .gift-code {
              font-weight: 400;
              font-size: 11 * @rem;
              color: #93999f;
              word-break: break-all;
            }
            .get-copy {
              display: flex;
              align-items: center;
              .copy-icon {
                width: 18 * @rem;
                height: 18 * @rem;
                .image-bg('~@/assets/images/games/gift-copy-icon.png');
              }
              span {
                margin-left: 4 * @rem;
                font-weight: 400;
                font-size: 12 * @rem;
                color: #1cce94;
              }
            }
          }
        }
      }
    }
    .get {
      margin-left: 20 * @rem;
      span {
        background-color: #1cce94;
        border-radius: 15 * @rem;
        overflow: hidden;
        box-sizing: border-box;
        width: 68 * @rem;
        height: 28 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #fff;
        font-weight: 600;
        &.already {
          color: #ffffff;
          background-color: #cfd3d8;
        }
      }
    }
  }
  /deep/ .van-button {
    border-radius: 16 * @rem;
  }
  .copy-dialog {
    box-sizing: border-box;
    width: 300 * @rem;
    border-radius: 12 * @rem;
    background-color: #fff;
    padding: 30 * @rem;
    overflow: unset;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;

      .title-text {
        line-height: 20 * @rem;
        font-size: 16 * @rem;
        color: #333333;
        font-weight: 600;
      }
    }
    .cardpass {
      box-sizing: border-box;
      width: 100%;
      height: 70 * @rem;
      background-color: #f5f5f5;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 0 10 * @rem;
      margin-top: 27 * @rem;
      font-size: 16 * @rem;
      line-height: 22 * @rem;
      color: #333333;
      font-weight: 600;
      word-break: break-all;
    }
    .desc {
      font-size: 11 * @rem;
      line-height: 15 * @rem;
      color: #777777;
      font-weight: 400;
      margin-top: 10 * @rem;
      text-align: center;
      word-break: break-all;
    }
    .copy-btn {
      width: 238 * @rem;
      height: 44 * @rem;
      margin: 21 * @rem auto 0;
      background: @themeBg;
      border-radius: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #ffffff;
    }
    .close-btn {
      width: 32 * @rem;
      height: 32 * @rem;
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translateX(-50%);
      z-index: 2;
      .image-bg('~@/assets/images/games/get-gift-popup-close-btn.png');
    }
  }
  .xh-dialog {
    width: 244 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 244 * @rem;
      height: 37 * @rem;
      .image-bg('~@/assets/images/games/dialog-logo.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      width: 244 * @rem;
      background-color: #fff;
      border-radius: 20 * @rem;
      margin-top: -4 * @rem;
      z-index: 2;
      padding: 16 * @rem 10 * @rem 19 * @rem;
      .title {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        text-align: center;
        line-height: 25 * @rem;
      }
      .center {
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 15 * @rem 0 0;
        padding: 0 18 * @rem;
        .left,
        .right {
          position: relative;
          line-height: 40 * @rem;
        }
        .left {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: 400;
        }
        .right {
          width: 133 * @rem;
          text-align: right;
          border-bottom: 0.5 * @rem solid #a6a6a6;
          .text {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            color: #000000;
            font-size: 13 * @rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            span {
              display: block;
              flex-shrink: 0;
            }
          }
          .more-text-icon {
            width: 10 * @rem;
            height: 6 * @rem;
            background: url(~@/assets/images/games/bottom-arrow.png) center
              center no-repeat;
            background-size: 10 * @rem 6 * @rem;
            margin-left: 6 * @rem;
            transition: 0.3s;
            &.on {
              transform: rotateZ(180deg);
            }
          }
        }
        .xiaohao-list {
          display: none;
          position: absolute;
          top: 40 * @rem;
          left: 0;
          z-index: 2000;
          width: 100%;
          max-height: 200 * @rem;
          overflow: auto;
          border-radius: 0 0 4 * @rem 4 * @rem;
          background: #fff;

          border: 1 * @rem solid #f2f2f2;
          &.on {
            display: block;
          }
          .xiaohao-item {
            box-sizing: border-box;
            text-align: center;
            line-height: 40 * @rem;
            text-align: right;
            padding: 0 15 * @rem;
            font-size: 13 * @rem;
            color: #000000;
            font-weight: 400;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &:not(:last-of-type) {
              border-bottom: 0.5 * @rem solid #f2f2f2;
            }
          }
        }
      }

      .dialog-bottom-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 18 * @rem;
        padding: 0 5 * @rem;
        .cancel {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #7d7d7d;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f2f2f2;
          border-radius: 18 * @rem;
        }
        .confirm {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #ffffff;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: @themeBg;
          border-radius: 18 * @rem;
        }
      }
    }
  }
}
</style>
