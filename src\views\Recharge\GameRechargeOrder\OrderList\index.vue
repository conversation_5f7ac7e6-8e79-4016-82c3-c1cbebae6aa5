<template>
  <div class="order-list-page">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <div class="order-list">
        <div class="order-item" v-for="(item, index) in orderList" :key="index">
          <div class="top-content">
            <div class="date">{{ item.create_time | formatTime }}</div>
            <div class="item-title">{{ $t('支付金额') }}</div>
            <div class="money">¥{{ item.pay_amount }}元</div>
          </div>
          <div class="bottom-content">
            <div class="bottom-item">
              <div class="left">{{ $t('支付方式') }}:</div>
              <div class="right">{{ item.pw }}</div>
            </div>
            <div class="bottom-item">
              <div class="left">{{ $t('订单号') }}:</div>
              <div class="right">{{ item.order_id }}</div>
            </div>
            <div class="bottom-item">
              <div class="left">{{ $t('支付情况') }}:</div>
              <div class="right">{{ item.status | formatStatus }}</div>
            </div>
            <div class="bottom-item">
              <div class="left">{{ $t('充值到') }}:</div>
              <div class="right">{{ item.game_name }}</div>
            </div>
          </div>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiGetPayRecordList } from '@/api/views/recharge.js';
import { handleTimestamp } from '@/utils/datetime.js';
let _this;
export default {
  name: 'OrderList',
  props: {
    status: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      orderList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
    formatStatus(val) {
      let result;
      switch (Number(val)) {
        case 1:
          result = _this.$t('未支付');
          break;
        case 2:
          result = _this.$t('支付成功');
          break;
        case 3:
          result = _this.$t('支付失败');
          break;
        default:
          result = '';
          break;
      }
      return result;
    },
  },
  beforeCreate() {
    _this = this;
  },
  methods: {
    async getOrderList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      try {
        let res = await ApiGetPayRecordList({
          page: this.page,
          listRows: this.listRows,
          status: this.status,
        });
        if (action === 1 || this.page === 1) {
          this.orderList = [];
        }
        this.orderList.push(...res.data);
        if (res.data.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (e) {
        if (e.code == 0) {
          this.finished = true;
        }
      } finally {
      }
    },
    async onRefresh() {
      await this.getOrderList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.orderList.length) {
        await this.getOrderList();
      } else {
        await this.getOrderList(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.order-list-page {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .order-list {
    .order-item {
      box-sizing: border-box;
      padding: 0 16 * @rem;
      width: 347 * @rem;
      margin: 20 * @rem auto 0;
      box-shadow: 0 * @rem 0 * @rem 9 * @rem 0 * @rem rgba(0, 0, 0, 0.2);
      border-radius: 10 * @rem;
      background-color: #fff;
      .top-content {
        border-bottom: 1px solid #eeeeee;
        padding: 15 * @rem 0;
        .date {
          font-size: 14 * @rem;
          color: #666666;
        }
        .item-title {
          font-size: 13 * @rem;
          color: #999999;
          text-align: center;
          margin-top: 20 * @rem;
        }
        .money {
          font-size: 20 * @rem;
          color: #000000;
          font-weight: bold;
          text-align: center;
          margin-top: 5 * @rem;
        }
      }
      .bottom-content {
        padding: 10 * @rem 0;
        .bottom-item {
          display: flex;
          justify-content: space-between;
          line-height: 24 * @rem;
          .left {
            font-size: 14 * @rem;
            color: #666666;
          }
          .right {
            font-size: 14 * @rem;
            color: #333333;
          }
        }
      }
    }
  }
}
</style>
