<template>
  <div class="developer-games-container">
    <nav-bar-2
      :title="company_name"
      :placeholder="false"
      :azShow="true"
      :border="true"
    >
    </nav-bar-2>

    <yy-list
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :tips="tips"
      :check="false"
    >
      <div class="game-list">
        <div
          class="game-item"
          v-for="(game, index) in resultList"
          :key="game.id + '' + index"
        >
          <game-item-4
            :gameInfo="game"
            :iconSize="72"
            :showHot="true"
          ></game-item-4>
          <div class="game-btn">
            <yy-download-btn
              :gameInfo="game"
              v-if="!openGameShow(game)"
            ></yy-download-btn>
            <div class="download-btn" @click="openGame(game)" v-else>
              打开
            </div>
            <div class="deduction-tips" v-if="game.gold_discount > 0">
              <div class="deduction-tips-box">
                <div
                  :class="{ zhekou100_icon: game.gold_discount == 100 }"
                ></div>
                <span v-if="game.gold_discount != 100"
                  >{{ game.gold_discount }}%</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </yy-list></div
  >
</template>

<script>
import { ApiCoinCenterGetGoldBoxList } from '@/api/views/users.js';
import { platform, BOX_openApp } from '@/utils/box.uni.js';
export default {
  name: 'GoldCoinDiscountGame',
  data() {
    return {
      company_name: '金币折扣游戏',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无游戏',
    };
  },
  async created() {
    await this.getGameGetCPOtherGame();
  },
  methods: {
    openGame(detail) {
      BOX_openApp(detail.package_name);
    },
    openGameShow(detail) {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          return BOX.checkInstall(detail.package_name);
        } catch (error) {
          return false;
        }
      } else {
        return false;
      }
    },
    async getGameGetCPOtherGame(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiCoinCenterGetGoldBoxList({
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getGameGetCPOtherGame();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.resultList.length) {
        await this.getGameGetCPOtherGame();
      } else {
        await this.getGameGetCPOtherGame(2);
      }
      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.developer-games-container {
  background-color: #fff;
  min-height: 100vh;
  padding: 0 18 * @rem;
  margin-top: calc(50 * @rem + @safeAreaTop);
  margin-top: calc(50 * @rem + @safeAreaTopEnv);
  .game-list-box {
    .game-list {
      padding: 5 * @rem 0;
      overflow: hidden;
      .game-item {
        display: flex;
        align-items: center;
        .btn {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64 * @rem;
          height: 30 * @rem;
          background: @themeBg;
          border-radius: 19 * @rem;
          color: #fff;
          line-height: 30 * @rem;
          text-align: center;
          font-weight: 500;
          font-size: 12 * @rem;
        }
        .game-btn {
          position: relative;
          /deep/.download-btn {
            width: 64 * @rem;
            height: 30 * @rem;
            background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
            border-radius: 29 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #ffffff;
          }
          .download-btn {
            box-sizing: border-box;
            width: 64 * @rem;
            height: 30 * @rem;
            font-size: 12 * @rem;
            color: #fff;
            border-radius: 30 * @rem;
            background: @themeBg;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(62deg, #46a6ff 0%, #3ac4ff 100%);
          }
          .deduction-tips {
            position: absolute;
            top: -14 * @rem;
            left: 0 * @rem;
            //   transform: rotate(-9.24deg);
            height: 20 * @rem;
            //   overflow: hidden;
            text {
              height: 12 * @rem;
              fill: #fff;
              font-size: 12 * @rem;
              stroke: #f7552b;
              stroke-width: 3 * @rem;
              font-weight: bold;
              paint-order: stroke fill;
              stroke-linejoin: round;
            }
            .deduction-tips-box {
              display: flex;
              align-items: center;
              div {
                background: url('~@/assets/images/recharge/zhekou_icon.png')
                  no-repeat 0 0;
                background-size: 65 * @rem 20 * @rem;
                width: 65 * @rem;
                height: 20 * @rem;
                &.zhekou100_icon {
                  background: url('~@/assets/images/recharge/zhekou100_icon.png')
                    no-repeat 0 0;
                  background-size: 64 * @rem 20 * @rem;
                  width: 64 * @rem;
                  height: 20 * @rem;
                }
              }
              span {
                margin-top: 3 * @rem;
                height: 20 * @rem;
                position: absolute;
                right: 5 * @rem;
                font-family: Inter, Inter;
                font-weight: 900;
                font-size: 12 * @rem;
                color: #ffffff;
                &.zhekou11Size {
                  right: 4 * @rem;
                  height: 20 * @rem;
                  line-height: 15 * @rem;
                  font-size: 11 * @rem;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
