<template>
  <div
    class="remind-item"
    @click="
      toPage('GameDetail', {
        id: remindInfo.game.id,
        gameInfo: remindInfo.game,
      })
    "
  >
    <div class="game-icon">
      <img :src="remindInfo.game.titlepic" alt="" />
    </div>
    <div class="game-info">
      <div class="game-name">{{ remindInfo.game.title }}</div>
      <div class="date">
        {{ remindInfo.newstime | formatTime }} ({{
          $getDateDiff(remindInfo.newstime)
        }})
      </div>
      <!-- <div class="tags">
        <div
          class="tag"
          v-for="(tag, tagIndex) in remindInfo.game.extra_tag"
          :key="tagIndex"
        >
          <template v-if="tagIndex < 3">
            <div class="tag-name">{{ tag.name }}</div>
          </template>
        </div>
      </div> -->
      <div class="state">{{ $t('区服') }}：{{ remindInfo.state }}</div>
    </div>
    <div
      class="remind-btn"
      @click.stop="remind(remindInfo.id, Math.abs(remindInfo.status - 1))"
    >
      {{ remindInfo.status == 1 ? $t('取消') : $t('提醒我') }}
    </div>
  </div>
</template>

<script>
import { handleTimestamp } from '@/utils/datetime.js';
import { ApiServerRemind } from '@/api/views/game.js';
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
  },
  filters: {
    formatTime(val) {
      let { date, time } = handleTimestamp(val);
      return `${date} ${time}`;
    },
  },
  created() {
    this.remindInfo = this.info;
  },
  methods: {
    async remind(serverId, status) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiServerRemind({
        serverId,
        status,
      });
      if (res.code > 0) {
        this.remindInfo.status = Math.abs(this.remindInfo.status - 1);
        this.$toast(res.msg);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.remind-item {
  box-sizing: border-box;
  width: 351 * @rem;
  border: 12 * @rem;
  background-color: #fff;
  margin: 12 * @rem auto 0;
  padding: 12 * @rem;
  display: flex;
  align-items: center;
  .game-icon {
    width: 70 * @rem;
    height: 70 * @rem;
    border-radius: 12 * @rem;
    overflow: hidden;
  }
  .game-info {
    flex: 1;
    min-width: 0;
    margin-left: 13 * @rem;
    .game-name {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 22 * @rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .date {
      font-size: 12 * @rem;
      color: #929292;
      line-height: 17 * @rem;
      margin-top: 5 * @rem;
    }
    .state {
      font-size: 12 * @rem;
      color: #929292;
      line-height: 17 * @rem;
      margin-top: 5 * @rem;
    }
    .tags {
      display: flex;
      height: 17 * @rem;
      overflow: hidden;
      flex-wrap: wrap;
      margin-top: 5 * @rem;
      .tag {
        height: 17 * @rem;
        margin-right: 5 * @rem;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        color: #9a9a9a;
        background-color: #f5f5f6;
        border-radius: 5 * @rem;
        padding: 0 4 * @rem;
        .tag-icon {
          width: 13 * @rem;
          height: 13 * @rem;
        }
        .tag-name {
          font-size: 11 * @rem;
          white-space: nowrap;
          margin-left: 2 * @rem;
        }
      }
    }
  }
  .remind-btn {
    width: 54 * @rem;
    height: 28 * @rem;
    color: #fff;
    font-size: 13 * @rem;
    border-radius: 6 * @rem;
    background-color: @themeColor;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10 * @rem;
  }
}
</style>
