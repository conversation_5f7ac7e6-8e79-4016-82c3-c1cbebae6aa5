<template>
  <div class="forget-password">
    <nav-bar-2
      :border="true"
      :title="$t('重置密码')"
      ref="topNavBar"
    ></nav-bar-2>
    <form class="form">
      <div class="field" v-if="show_phone">
        <input
          class="pdr115"
          type="number"
          v-model="phone"
          :placeholder="$t('请输入手机号码')"
        />
        <div class="tel-right">
          <div
            class="clear"
            :class="{ transparent: phone == '' }"
            @click="phone = ''"
          ></div>
          <div class="country-code" @click="toPage('AreaCode')">
            <div class="country-code-text">+{{ areaCode }}</div>
            <div class="arrow-down"></div>
          </div>
        </div>
      </div>
      <div class="field" v-if="show_phone">
        <input
          type="number"
          v-model="authCode"
          :placeholder="$t('请输入验证码')"
        />
        <div class="text" v-if="!ifCount" @click="captchaClick()">
          {{ $t('获取验证码') }}
        </div>
        <div class="text" v-else>
          {{ `${$t('重新获取')}${countdown}s` }}
        </div>
      </div>

      <div class="field-new" v-if="show_username">
        <div class="field-title">请输入您的账号</div>
        <div class="field-content gray-content">
          <input
            type="text"
            v-model="username"
            placeholder="请输入用户名/手机号"
          />
        </div>
      </div>
      <template v-if="show_security">
        <div class="field-new">
          <div class="field-title">
            请{{
              token && !is_security && login_type == 1 ? '设置' : '选择'
            }}密保问题
          </div>
          <security-question-bar
            class="field-content question-box"
            :selectedQuestion.sync="selectedQuestion"
          ></security-question-bar>
        </div>

        <div class="field-new">
          <div class="field-title">
            请{{
              token && !is_security && login_type == 1 ? '设置' : '输入'
            }}密保答案
          </div>
          <div class="field-content">
            <input
              type="text"
              v-model="answer"
              placeholder="最长可输入10个字符"
            />
          </div>
        </div>
      </template>

      <div class="field-new">
        <div class="field-title">请输入您的新密码</div>
        <div class="field-content gray-content">
          <input
            :type="open ? 'text' : 'password'"
            v-model="password"
            placeholder="请输入8~16位数字或字母"
          />
          <div
            v-if="password.length > 0"
            class="eyes"
            :class="{ open: open }"
            @click="isShow()"
          ></div>
        </div>
      </div>
    </form>
    <div class="button" @click="commit()">{{ $t('提交') }}</div>
  </div>
</template>

<script>
// [0].includes(login_type) // login_type == 0 表示大陆用户
import {
  ApiResetPassword,
  ApiAuthCode,
  ApiRestPasswordBySecurity,
} from '@/api/views/users';
import { mapGetters, mapMutations } from 'vuex';
import securityQuestionBar from '@/components/security-question-bar';
export default {
  name: 'ChangePassword',
  components: {
    securityQuestionBar,
  },
  data() {
    return {
      phone: '',
      authCode: '',
      password: '',
      countdown: 60,
      ifCount: false,
      open: false, //是否显示密码
      captcha: null,

      username: '',
      selectedQuestion: {
        title: '请选择问题',
      },
      answer: '',

      show_phone: false, // 显示手机号和验证码

      show_username: true, // 显示账号

      show_security: false, // 显示密保
    };
  },
  computed: {
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
    login_type() {
      return Number(this.initData.login_type.type);
    },
    is_security() {
      return this.userInfo.is_security;
    },
    token() {
      return this.userInfo.token;
    },
  },
  watch: {
    authCode() {
      if (this.authCode > 1000000)
        this.authCode = Math.floor(this.authCode / 10);
    },
  },
  created() {
    // show_phone show_auth_code show_password show_security
    switch (this.login_type) {
      case 0:
        this.show_phone = true;
        this.show_username = false;
        this.show_security = false;
        break;
      case 1:
        this.show_phone = this.token && this.is_security ? false : true;
        this.show_username = this.token && this.is_security ? true : false;
        this.show_security = this.token ? true : false;
        break;
      case 2:
      case 3:
        this.show_phone = false;
        this.show_username = true;
        this.show_security = true;
        break;
    }

    if (!this.areaCode) {
      this.setAreaCode(86);
    }
    if (this.userInfo.username) {
      this.username = this.userInfo.username;
    }
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    isShow() {
      this.open = !this.open;
    },
    toPage(page, params) {
      this.$router.push({ name: page, params: params });
    },
    async commit() {
      if (this.show_phone) {
        if (this.phone === '') {
          this.$toast(this.$t('请输入手机号码'));
          return false;
        }
        if (this.authCode === '') {
          this.$toast(this.$t('请输入获得的验证码!'));
          return false;
        }
      }

      if (this.show_username) {
        if (this.username === '') {
          this.$toast('请输入用户名/手机号');
          return false;
        }
      }

      if (this.show_security) {
        if (this.answer === '') {
          this.$toast(this.$t('请输入密保答案'));
          return false;
        }
        if (!this.selectedQuestion.id) {
          this.$toast(this.$t('请选择问题'));
          return false;
        }
      }

      if (this.password === '') {
        this.$toast(this.$t('请输入新密码'));
        return false;
      }
      this.$toast.loading({
        message: this.$t('重置密码中...'),
        forbidClick: true,
        duration: 0,
      });

      // 提交逻辑
      let params = {
        password: this.password,
      };
      if (this.show_phone) {
        params.phone = this.phone;
        params.code = this.authCode;
        if (this.areaCode) {
          params.countryCode = this.areaCode;
        }
      }
      if (this.show_username) {
        params.username = this.username;
      }
      if (this.show_security) {
        params.question_1 = this.selectedQuestion.id;
        params.answer_1 = this.answer;
      }
      if (this.show_phone) {
        await ApiResetPassword(params);
      } else {
        await ApiRestPasswordBySecurity(params);
      }
      this.$toast.clear();
      this.$toast(this.$t('重置成功'));
      if (this.isHw) {
        // 海外的重置密码后要跳转LoginHw 2023年12月12日19:25:04
        this.$router.replace({ name: 'LoginHw' });
      } else {
        this.$router.replace({ name: 'Login' });
      }
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.phone === '') {
        this.$toast(this.$t('请输入手机号码'));
        return false;
      }
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      let params = {
        phone: this.phone,
        type: 3,
      };
      if (this.areaCode) {
        params.countryCode = this.areaCode;
      }
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      ApiAuthCode(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
  },
};
</script>

<style lang="less" scoped>
.forget-password {
  &.pdr115 {
    padding-right: 115 * @rem;
  }
  &.pdr40 {
    padding-right: 40 * @rem;
  }
  .title {
    margin: 42 * @rem 0 60 * @rem;
    text-align: center;
    font-size: 22 * @rem;
    font-weight: 600;
  }

  .field-new {
    padding: 0 13 * @rem;
    margin-top: 30 * @rem;
    .field-title {
      font-size: 16 * @rem;
      color: #565656;
      line-height: 22 * @rem;
    }
    .field-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 42 * @rem;
      border-bottom: 0.5 * @rem solid #e0e0e0;
      margin-top: 4 * @rem;
      margin-bottom: 32 * @rem;
      position: relative;
      &.question-box {
        margin-top: 8 * @rem;
        border: 0;
      }
      &.gray-content {
        margin-top: 8 * @rem;
        border: 0;
        background: #f4f4f4;
        border-radius: 6 * @rem;
        padding: 0 11 * @rem;
        input {
          background: transparent;
        }
      }

      input {
        min-width: 0;
        flex: 1;
        height: 100%;
        line-height: 42 * @rem;
        font-size: 14 * @rem;
      }
      .eyes {
        width: 18 * @rem;
        height: 44 * @rem;
        background-image: url(~@/assets/images/users/no-look.png);
        background-size: 18 * @rem 7 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        margin-left: 20 * @rem;
        &.open {
          background-image: url(~@/assets/images/users/look.png);
          background-size: 18 * @rem 12 * @rem;
        }
      }
    }
  }
  .form {
    padding: 0 17 * @rem;
    margin-top: 30 * @rem;

    .field {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      height: 44 * @rem;
      border-radius: 22 * @rem;
      overflow: hidden;
      position: relative;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      .tel-right {
        width: 113 * @rem;
        height: 42 * @rem;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        .clear {
          width: 16 * @rem;
          height: 42 * @rem;
          padding: 0 10 * @rem;
          background-image: url(~@/assets/images/users/keyword-clear.png);
          background-size: 14 * @rem 14 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.transparent {
            opacity: 0;
          }
        }
        .country-code {
          display: flex;
          height: 42 * @rem;
          align-items: center;
          padding-left: 9 * @rem;
          position: relative;
          &::before {
            content: '';
            width: 1 * @rem;
            height: 11 * @rem;
            background-color: #dadada;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
          .country-code-text {
            font-size: 16 * @rem;
            color: #000000;
          }
          .arrow-down {
            width: 10 * @rem;
            height: 6 * @rem;
            .image-bg('~@/assets/images/users/arrow-down.png');
            margin-left: 5 * @rem;
            margin-top: 2 * @rem;
          }
        }
      }
      input {
        box-sizing: border-box;
        flex: 1;
        min-width: 0;
        height: 100%;
        padding: 0 5 * @rem;
        line-height: 44 * @rem;
        font-size: 14 * @rem;
        letter-spacing: 1 * @rem;
        background-color: #f4f4f4;
        padding: 0 20 * @rem;
        border-radius: 22 * @rem;
      }
      .text {
        box-sizing: border-box;
        border: 1 * @rem solid @themeColor;
        font-size: 14 * @rem;
        height: 42 * @rem;
        width: 116 * @rem;
        border-radius: 22 * @rem;
        color: @themeColor;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 10 * @rem;
        margin-right: 1 * @rem;
        &.text2 {
          color: #a4a4a4;
        }
      }
      .eyes {
        width: 18 * @rem;
        height: 44 * @rem;
        background-image: url(~@/assets/images/users/no-look.png);
        background-size: 18 * @rem 7 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        position: absolute;
        right: 20 * @rem;
        top: 50%;
        transform: translateY(-50%);
        &.open {
          background-image: url(~@/assets/images/users/look.png);
          background-size: 18 * @rem 12 * @rem;
        }
      }
    }
  }
  .button {
    height: 50 * @rem;
    margin: 50 * @rem 30 * @rem 0;
    text-align: center;
    line-height: 50 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    letter-spacing: 2px;
  }
}
</style>
