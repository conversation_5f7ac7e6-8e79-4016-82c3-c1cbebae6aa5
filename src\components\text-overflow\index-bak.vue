<template>
  <div class="mm-ellipsis-container">
    <div class="shadow">
      <textarea :rows="rows" readonly></textarea>
      <div class="shadow-box" ref="box">
        {{ showContent }}
        <slot name="ellipsis">
          {{ ellipsisText }}
          <span class="ellipsis-btn">{{ btnText }}</span>
        </slot>
        <span ref="tail"></span>
      </div>
    </div>
    <div class="real-box">
      {{ showContent }}
      <slot name="ellipsis" v-if="isEllipsisActive">
        {{ ellipsisText }}
        <span class="ellipsis-btn" @click="clickBtn">{{ btnText }}</span>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: '',
    },
    btnText: {
      type: String,
      default: '展开',
    },
    ellipsisText: {
      type: String,
      default: '...',
    },
    rows: {
      type: Number,
      default: 3,
    },
    btnShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textLength: 0,
      boxWidth: 0,
      boxHeight: 0,
      observer: null,
    };
  },
  computed: {
    showContent() {
      return this.content.substr(0, this.textLength);
    },
    isEllipsisActive() {
      return this.textLength < this.content.length || this.btnShow;
    },
    watchData() {
      return [
        this.content,
        this.btnText,
        this.ellipsisText,
        this.rows,
        this.btnShow,
      ];
    },
  },
  watch: {
    watchData: {
      immediate: true,
      handler() {
        this.refresh();
      },
    },
  },
  mounted() {
    this.observer = new ResizeObserver(() => {
      const el = this.$refs.box;
      if (
        el.offsetWidth === this.boxWidth &&
        el.offsetHeight === this.boxHeight
      )
        return;
      this.boxWidth = el.offsetWidth;
      this.boxHeight = el.offsetHeight;
      this.refresh();
    });

    this.observer.observe(this.$refs.box);
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.unobserve(this.$refs.box);
    }
  },
  methods: {
    refresh() {
      let start = 0;
      let end = this.content.length;
      const checkLoop = () => {
        const boxRect = this.$refs.box.getBoundingClientRect();
        const tailRect = this.$refs.tail.getBoundingClientRect();
        const overflow = tailRect.bottom > boxRect.bottom;

        if (overflow) {
          end = this.textLength;
        } else {
          start = this.textLength;
        }

        this.textLength = Math.floor((start + end) / 2);

        if (start + 1 < end) {
          this.$nextTick(checkLoop);
        }
      };

      this.textLength = this.content.length;
      this.$nextTick(checkLoop);
    },
    clickBtn(event) {
      this.$emit('click-btn', event);
    },
  },
};
</script>

<style lang="less" scoped>
.mm-ellipsis-container {
  text-align: left;
  position: relative;
  line-height: 1.5;

  .shadow {
    width: 100%;
    display: flex;
    pointer-events: none;
    opacity: 0;
    user-select: none;
    position: absolute;
    outline: green solid 1px;

    textarea {
      border: none;
      flex: auto;
      padding: 0;
      resize: none;
      overflow: hidden;
      font-size: inherit;
      line-height: inherit;
      outline: none;
    }

    .shadow-box {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
  }
  .ellipsis-btn {
    cursor: pointer;
    color: #32b768;
  }
}
</style>
