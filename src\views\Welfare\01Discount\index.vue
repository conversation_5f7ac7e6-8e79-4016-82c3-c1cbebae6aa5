<template>
  <div class="free-play-page">
    <nav-bar-2
      bgStyle="white"
      title="充值0.1折"
      :border="false"
      :placeholder="false"
    ></nav-bar-2>
    <div class="main">
      <content-empty v-if="empty"></content-empty>
      <load-more
        v-else
        class="game-list"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="game-item" v-for="(item, index) in gameList" :key="index">
          <game-item-2
            :type="6"
            :showRight="true"
            :gameInfo="item"
          ></game-item-2>
        </div>
      </load-more>
    </div>
  </div>
</template>
<script>
import { ApiGame01DiscountList } from '@/api/views/game.js';
export default {
  name: 'FreePlay',
  data() {
    return {
      gameList: [],
      finished: false,
      loading: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  async created() {
    await this.getList();
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiGame01DiscountList({
        id: 151,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        if (!res.data.game_list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gameList.push(...res.data.game_list);
      if (res.data.game_list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.free-play-page {
  background-color: #ffffff;
  font-size: 14 * @rem;
  min-height: 100vh;
  .main {
    margin: 40 * @rem 0;
  }
  .top-bar {
    width: 100%;
    height: 190 * @rem;
  }
  .free-introduction {
    font-size: 12 * @rem;
    line-height: 20 * @rem;
    padding: 16 * @rem 30 * @rem 0;
  }
  .game-list {
    padding: 10 * @rem 20 * @rem;
    .game-item {
      padding: 5 * @rem 0;
      border-bottom: 0.5px solid #ebebeb;
      &:last-of-type {
        border-bottom: 0;
      }
    }
  }
}
</style>
