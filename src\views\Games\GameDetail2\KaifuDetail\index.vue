<template>
  <div class="kaifu-detail-page">
    <nav-bar-2 :title="$t('开服表')" :border="true"></nav-bar-2>
    <div class="list-container">
      <kaifu-list :gameId="gameId"></kaifu-list>
    </div>
  </div>
</template>
<script>
import KaifuList from '../components/kaifu-list';
export default {
  name: 'KaifuDetail',
  components: {
    KaifuList,
  },
  data() {
    return {
      gameId: 0,
    };
  },
  created() {
    this.gameId = this.$route.params.id;
  },
};
</script>
<style lang="less" scoped>
.kaifu-detail-page {
  .list-container {
    padding: 14 * @rem 0;
  }
}
</style>
