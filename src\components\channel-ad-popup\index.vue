<template>
  <van-dialog
    v-model="popup"
    :showConfirmButton="false"
    :lockScroll="false"
    class="channel-ad-popup"
    :overlay-style="{ 'z-index': '3001' }"
  >
    <img :src="data.bg_img_url" @click="toDetail()" />
    <div @click="popup = false" class="close"></div>
  </van-dialog>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { PageName } from '@/utils/actionCode.js';
import {
  BOX_showActivity,
  BOX_showActivityByAction,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';

export default {
  computed: {
    popup: {
      get() {
        return this.showChannelAdPopup;
      },
      set(value) {
        this.setShowChannelAdPopup(value);
      },
    },
    ...mapGetters({
      showChannelAdPopup: 'user/showChannelAdPopup',
      data: 'user/channelAdData',
    }),
  },
  methods: {
    toDetail() {
      this.popup = false;
      // 0是盒子内页面 1游戏 4URL
      switch (Number(this.data.show_type)) {
        case 0:
          // 跳转分类页 -1到-5对应游戏分类id，还要自己去找，真蠢
          switch (Number(this.data.show_extra)) {
            case -1: // BT版
              this.toPage('Category', { classId: 1 });
              break;
            case -2: // H5在线玩
              this.toPage('Category', { classId: 10003 });
              break;
            case -3: // 折扣版
              this.toPage('Category', { classId: 107 });
              break;
            case -4: // 常规服
              this.toPage('Category', { classId: 110 });
              break;
            case -5: // UP资源
              this.toPage('Category', { classId: 40 });
              break;
            case 30: // 捡漏
              BOX_showActivity({ name: 'Jianlou' }, { page: 'jl' });
              break;
            case 21: // 金币商城
              BOX_showActivity({ name: 'GoldMall' }, { page: 'jbsc' });
              break;
            case 9: // 签到
              try {
                BOX_showActivityByAction({
                  action_code: 30,
                  web_url: 'Welfare',
                  type: 1,
                });
              } catch (e) {
                BOX_showActivity(
                  { name: 'Welfare', params: { type: 1 } },
                  { page: 'qd' },
                );
              }
              // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
              break;
            case 8: // 返利申请
              BOX_showActivity({ name: 'Rebate' }, { page: 'fl' });
              break;
            case 22: // 小号回收
              BOX_showActivity({ name: 'Recycle' }, { page: 'xhhs' });
              break;
            case 23: // 转游中心
              BOX_showActivity({ name: 'Zhuanyou' }, { page: 'zyzx' });
              break;
            case 13: // 金币转盘
              BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
              break;
            case 15: // 邀请赚佣金
              BOX_openInNewWindow(
                { name: 'Invite' },
                { url: `${window.location.origin}/#/invite` },
              );
              break;
            case 17: // 排行榜
              BOX_showActivity({ name: 'Rank' }, { page: 'phb' });
              break;
            case 18: // 新游首发
              BOX_showActivity({ name: 'NewGame' }, { page: 'xysf' });
              break;
            case 31:
              this.toPage('GameCollect', { id: this.data.heji_id });
          }
          break;
        case 1:
          this.toPage('GameDetail', { id: this.data.show_extra });
          break;
        case 4:
          this.toPage('Activity', { url: this.data.show_extra });
          break;
      }
      // 1033 后台配置为活动地址 强行拦截 ------2022年9月30日17:49:31
      if (this.data.action_code == 1033) {
        this.toPage('Activity', { url: this.data.web_url });
        return false;
      }
      switch (PageName[this.data.action_code]) {
        case 'GameDetail':
          this.toPage('GameDetail', { id: this.data.extra_id });
          break;
        case 'Activity':
          this.toPage('Activity', { url: this.data.web_url });
          break;
      }
    },
    ...mapMutations({
      setShowChannelAdPopup: 'user/setShowChannelAdPopup',
    }),
  },
};
</script>

<style lang="less" scoped>
.channel-ad-popup {
  z-index: 3001 !important;
  background: none;
  overflow: unset;
}
.close {
  position: absolute;
  right: 0;
  top: -26 * @rem;
  width: 26 * @rem;
  height: 26 * @rem;
  background-image: url(~@/assets/images/close.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
</style>
