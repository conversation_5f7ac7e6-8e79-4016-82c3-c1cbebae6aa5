
/**
 * 2025年3月13日15:53:44
 * 与碎片中的view_type一一对应的样式组件
 */
export const fragmentComponentMap = {
  // 普通游戏列表
  18: 'fragment-game-list',

  // 普通游戏列表 x
  21: 'fragment-game-list-x',

  // 游戏卡片列表 x
  22: 'fragment-game-card-list-x',

  // 金刚区-v1
  24: 'fragment-quick-actions-v1',

  // 说是25、26没差别，咱也不知道
  25: 'fragment-game-new-x',
  26: 'fragment-game-new-x',

  28: 'fragment-banner-swiper',
  // 游戏列表 x y
  40: 'fragment-game-list-x-y',

  // 首推游戏
  81: 'fragment-game-first',

  82: 'fragment-tab',
  83: 'fragment-portal',

  // 视频同款
  84: 'fragment-game-video-same',

  // 轮播
  85: 'fragment-banner-swiper',

  // 精选轮播
  86: 'fragment-banner-list',

  // 游戏首推推荐banner 单个
  87: 'fragment-banner-recommend',

  // 金刚区-v2
  89: 'fragment-quick-actions-v2',
}
