<template>
  <div class="bounty-item" :class="{ gray: taskInfo.record_status == 3 }">
    <div class="game-bar" @click="goToGame">
      <div class="game-icon">
        <img :src="taskInfo.detail.titlepic" alt="" />
      </div>
      <div class="game-info">
        <div class="game-title">
          {{ taskInfo.detail.title }}
        </div>
        <div class="bottom">
          <div class="discount-tag" v-if="discountTag">
            <img
              class="discount-icon discount-01"
              src="@/assets/images/games/discount-01.png"
              v-if="discountTag == 0.1"
            />
            <img
              class="discount-icon"
              src="@/assets/images/games/discount-normal.png"
              v-else-if="discountTag"
            />
            <div class="discount-text"
              ><span>{{ discountTag }}</span
              >折直充</div
            >
          </div>
          <template v-if="taskInfo.detail.rand_cate">
            <div class="tag">
              {{ taskInfo.detail.rand_cate.title }}
            </div>
          </template>
        </div>
      </div>
      <div
        class="btn bounty-btn"
        :class="{ disabled: isDisabled }"
        v-if="taskInfo.record_status == 0 && taskInfo.residue > 0"
        @click.stop="takeTask"
      >
        领取任务
      </div>
      <div
        class="btn bounty-btn submitted"
        :class="{ disabled: isDisabled }"
        v-if="taskInfo.record_status == 1"
        @click.stop="submitTask"
      >
        提交任务
      </div>
      <div
        class="btn bounty-btn gray"
        @click.stop=""
        v-if="taskInfo.record_status == 2"
      >
        已领取
      </div>
      <div
        class="btn bounty-btn gray"
        v-if="taskInfo.record_status == 3"
        @click.stop="toToast(taskInfo.record_status)"
      >
        已过期
      </div>
      <div
        class="btn bounty-btn none"
        v-if="taskInfo.record_status == 0 && taskInfo.residue == 0"
        @click.stop="toToast(taskInfo.residue)"
      >
        领光了
      </div>
    </div>
    <div class="bounty-card">
      <div class="last-number">
        剩余<span>{{ taskInfo.residue }}%</span>
      </div>
      <div class="star item">
        <div class="title-text">难度：</div>
        <div class="star-list">
          <div
            class="star-item"
            :class="{ on: index < taskInfo.difficulty }"
            v-for="(star, index) in 5"
            :key="index"
          ></div>
        </div>
      </div>
      <div class="reward item">
        <div class="title-text">金币奖励：</div>
        <span>{{ taskInfo.gold }}金币</span>
      </div>
      <div class="bounty-task item">
        <div class="title-text">任务要求：</div>
        <span>{{ taskInfo.describe }}</span>
      </div>
      <div class="time-text item">
        <div class="title-text">时限要求：</div>
        <span>自领取后{{ taskInfo.limited_time / 3600 }}小时内完成</span>
      </div>
    </div>
    <div class="bounty-info">
      <div class="down-user">
        <swiper :options="getSwiperOption">
          <swiper-slide
            class="swiper-slide"
            v-for="(item, index) in taskInfo.down_user"
            :key="index"
          >
            <div class="game-player">
              <div class="avatar">
                <img :src="item.avatar" alt="" />
              </div>
              <div class="nickname">{{ item.nickname }}</div>
              <div class="text">正在下载游戏</div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="bounty-time" v-if="taskInfo.record_status == 1">
        <div class="time-text">活动剩余时间</div>
        <div class="time-clock">
          <div class="num-box">{{ format_residue_time.hour }}</div>
          :
          <div class="num-box">{{ format_residue_time.minute }}</div>
          :
          <div class="num-box">{{ format_residue_time.second }}</div>
        </div>
      </div>
    </div>
    <!-- 赏金任务提示弹窗 -->
    <bounty-mission-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(taskInfo.game_id)"
      :detail="taskInfo.detail"
      :isDisabled="isDisabled"
    ></bounty-mission-tip-dialog>
    <!-- 未满足领取资格弹窗 -->
    <van-dialog
      v-model="unmetShow"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="create-dialog"
    >
      <div class="dialog-content">
        <div class="title" v-if="isReceive">领取成功</div>
        <div
          class="message"
          :class="{ mt30: !isReceive }"
          v-html="unmetTitle"
        ></div>
        <div class="dialog-bottom-bar">
          <!-- <div class="cancel btn" @click.stop="unmetShow = false">{{
            isDow ? '稍后打开' : '稍后下载'
          }}</div> -->
          <div
            class="confirm1 btn"
            :class="{ disabled: isDisabled }"
            @click.stop="openOrDowGame()"
            >{{ isDow ? '打开游戏' : '下载游戏' }}</div
          >
          <div class="btn-close" @click="unmetShow = false">
            <span></span>
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 我知道了 -->
    <van-dialog
      v-model="completeTheTaskShow"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      class="create-dialog"
    >
      <div class="dialog-content">
        <!-- <div class="title">{{ $t('提示') }}</div> -->
        <div class="message mt30" v-html="completeTheTaskTitle"></div>
        <div class="dialog-bottom-bar">
          <div
            class="confirm1 btn"
            :class="{ disabled: isDisabled }"
            @click.stop="completeTheTaskShow = false"
            >我知道了</div
          >
          <div class="btn-close" @click="completeTheTaskShow = false">
            <span></span>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
/**
 * 任务状态：0未领取 1未完成 2已领取 3已过期
 */
import {
  ApiBountyTaskGetTask,
  ApiBountyTaskGetMyTaskReward,
} from '@/api/views/bounty.js';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import bountyMissionTipDialog from '@/components/bounty-mission-tip-dialog';
import {
  BOX_goToGame,
  platform,
  BOX_checkInstall,
  BOX_login,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
export default {
  name: 'bountyTaskItem',
  components: {
    bountyMissionTipDialog,
  },
  props: {
    info: {
      type: Object,
      default: {},
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      taskInfo: {},
      timer: null,
      xiaohaoList: [], //小号列表
      createDialogShow: false,
      unmetShow: false,
      unmetTitle: '',
      isDow: false,
      completeTheTaskShow: false,
      completeTheTaskTitle: '',
      isDisabledShow: false,
      isReceive: true,
      getSwiperOption: {
        slidesPerView: 1,
        direction: 'vertical',
        autoplay: true,
        loop: true,
        allowTouchMove: false,
      },
    };
  },
  computed: {
    discountTag() {
      // if (this.taskInfo.detail.classid != 107) {
      //   return '';
      // }
      if (
        this.taskInfo.detail.f_pay_rebate &&
        this.taskInfo.detail.f_pay_rebate != 100
      ) {
        return `${parseFloat(this.taskInfo.detail.f_pay_rebate) / 10}`;
      } else if (
        this.taskInfo.detail.pay_rebate &&
        this.taskInfo.detail.pay_rebate != 100
      ) {
        return `${parseFloat(this.taskInfo.detail.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
    format_residue_time() {
      const timestamp = this.taskInfo.residue_time;

      let second = timestamp % 60;
      let minute = Math.floor(timestamp / 60) % 60;
      let hour = Math.floor(Math.floor(timestamp / 60) / 60);

      second = second < 10 ? '0' + second : second;
      minute = minute < 10 ? '0' + minute : minute;
      hour = hour < 10 ? '0' + hour : hour;

      return { hour, minute, second };
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  watch: {
    'taskInfo.record_status': {
      handler(newVal, oldVal) {
        if (this.taskInfo.record_status === 1) {
          this.refreshTimer();
        } else if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.taskInfo = this.info;
    this.isDisabledShow = this.isDisabled;
  },
  activated() {
    this.completeTheTaskShow = false;
    this.createDialogShow = false;
    this.unmetShow = false;
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    refreshTimer() {
      clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        this.taskInfo.residue_time--;
      }, 1000);
    },
    // 领取任务
    async takeTask() {
      // 先判断是否登录，未登录则跳转登录页
      if (!this.userInfo.token) {
        BOX_login();
        // this.$router.push({ name: 'PhoneLogin' });
        return;
      }

      try {
        // 1. 领取任务并打开游戏
        await this.handleTaskClaim();

        // 2. 判断是否下载游戏和有无小号
        const isGameInstalled =
          platform === 'android' &&
          !BOX_checkInstall(this.taskInfo.detail.package_name);
        if (isGameInstalled) {
          this.showUnmetDialog(
            '您当前尚未创建角色，请先下\n载游戏并创建角色',
            false,
          );
          return false;
        }
        await this.handleAccountCheck();
      } catch (error) {
        console.error(error);
      }
    },
    // 领取任务
    async handleTaskClaim() {
      this.CLICK_EVENT(this.taskInfo.click_id);
      this.$toast.loading('加载中');
      const res = await ApiBountyTaskGetTask({ id: this.taskInfo.id });
      this.taskInfo.record_status = res.data.status;
      this.taskInfo.record_id = res.data.id;
      this.taskInfo.residue_time = this.taskInfo.limited_time;
      this.$toast.clear();
    },
    // 是否创建小号
    async handleAccountCheck() {
      this.xiaohaoList = [];
      await this.getXiaohaoList();

      if (!this.xiaohaoList.length) {
        const message =
          platform === 'android'
            ? '您当前尚未创建角色，请先进\n入游戏创建角色'
            : '';

        if (platform === 'android') {
          this.showUnmetDialog(message, true);
          return;
        } else {
          this.createDialogShow = true;
          return;
        }
      } else {
        await this.handleTaskReward();
      }
    },
    // 领取奖励
    async handleTaskReward() {
      this.CLICK_EVENT(this.taskInfo.click_id);
      this.$toast.loading('加载中');
      const res = await ApiBountyTaskGetMyTaskReward({
        id: this.taskInfo.record_id,
      });
      this.$toast.clear();

      if (res.code === -38) {
        this.isReceive = false;
        this.unmetShow = true;
        const isGameInstalled =
          platform === 'android' &&
          !BOX_checkInstall(this.taskInfo.detail.package_name);
        if (isGameInstalled) {
          const msg = res.msg.replace(/\n/g, '<br>');
          this.showUnmetDialog(msg, false);
        } else {
          const msg = res.msg.replace(/\n/g, '<br>');
          this.showUnmetDialog(msg, true);
          return;
        }
      }

      this.taskInfo.record_status = res.data.status;
      if (this.taskInfo.record_status === 2) {
        this.completeTheTaskShow = true;
        this.completeTheTaskTitle = res.msg;
      }
    },
    showUnmetDialog(message, isDow) {
      this.unmetShow = true;
      let msg = message.replace(/\n/g, '<br>');
      this.unmetTitle = msg;
      this.isDow = isDow;
    },
    // 提交任务
    async submitTask() {
      await this.handleTaskReward();
    },
    openOrDowGame() {
      this.unmetShow = false;
      if (!this.isDow) {
        this.goToGame();
        if (platform === 'android') {
          window.BOX.startDownload(JSON.stringify(this.taskInfo.detail));
        }
      } else {
        if (platform === 'android') {
          window.BOX.openApp(this.taskInfo.detail.package_name);
          return;
        }
        this.goToGame();
      }
    },
    // 获取游戏小号列表 用来判断是否下载注册过
    async getXiaohaoList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.taskInfo.game_id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
    },
    toToast(status) {
      switch (status) {
        case 0:
          this.$toast('该赏金任务已被领光了，请领取其他任务');
          break;
        case 3:
          this.$toast('该赏金任务已过期');
          break;
        default:
          break;
      }
    },
    goToGame() {
      BOX_goToGame(
        {
          params: {
            id: this.taskInfo.game_id,
          },
        },
        { id: this.taskInfo.game_id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.van-dialog {
  overflow: visible;
}
.bounty-item {
  box-sizing: border-box;
  width: 331 * @rem;
  margin: 26 * @rem auto 0;
  padding: 0 12 * @rem 16 * @rem;
  background-color: #fff;
  border: 1 * @rem solid #f1f2fa;
  border-radius: 8 * @rem;

  &.gray {
    filter: grayscale(100%);
    opacity: 0.95;
  }

  .game-bar {
    display: flex;
    align-items: center;
    height: 71 * @rem;
    position: relative;

    .game-icon {
      width: 68 * @rem;
      height: 68 * @rem;
      position: absolute;
      top: -10 * @rem;
      left: 0;
      border-radius: 16 * @rem;
    }

    .game-info {
      flex: 1;
      min-width: 0;
      padding-left: 76 * @rem;
      .game-title {
        width: 100%;
        font-size: 14 * @rem;
        color: #303236;
        font-weight: bold;
        line-height: 20 * @rem;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .bottom {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        height: 18 * @rem;
        margin-top: 8 * @rem;
        overflow: hidden;
        .discount-tag {
          display: flex;
          align-items: center;
          width: fit-content;
          margin-right: 8 * @rem;
          flex-shrink: 0;

          .discount-icon {
            width: 30 * @rem;
            height: 18 * @rem;
            position: relative;
            z-index: 1;
            &.discount-01 {
              width: 49 * @rem;
            }
          }
          .discount-text {
            display: flex;
            align-items: center;
            height: 18 * @rem;
            padding-right: 4 * @rem;
            flex: 1;
            min-width: 0;
            font-size: 11 * @rem;
            color: #ff6649;
            white-space: nowrap;
            background-color: #fff5ed;
            border-radius: 0 2 * @rem 2 * @rem 0;
            margin-left: -5 * @rem;
            padding-left: 5 * @rem;
          }
        }
        .tag {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 18 * @rem;
          border-radius: 4 * @rem;
          padding: 0 6 * @rem;
          border: 1 * @rem solid #e3e5e8;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #93999f;
          line-height: 13 * @rem;
          text-align: center;
          box-sizing: border-box;
        }
      }
    }

    .bounty-btn {
      width: 78 * @rem;
      height: 32 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6 * @rem;
      font-size: 14 * @rem;
      color: #ffffff;
      background: #69a4fc;
      border-radius: 24 * @rem;

      &.submitted {
        background: linear-gradient(90deg, #90e4fe 0%, #558dfc 100%);
      }
      &.gray {
        background: #d8d7e0;
      }
      &.disabled {
        background: @themeBg;
      }
      &.none {
        background: #e7f4fe;
        color: #64a0f7;
      }
    }
  }

  .bounty-card {
    width: 100%;
    background-color: #f4f6fa;
    border-radius: 8 * @rem;
    padding: 14 * @rem 12 * @rem;
    position: relative;
    box-sizing: border-box;

    .last-number {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 24 * @rem;
      border-radius: 0 8 * @rem 0 8 * @rem;
      background-color: #e2effd;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #97a5ba;
      line-height: 15 * @rem;
      text-align: center;
      position: absolute;
      top: 0;
      right: 0;
      padding: 0 10 * @rem;

      span {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 12 * @rem;
        color: #585e74;
        line-height: 15 * @rem;
      }
    }

    .item {
      display: flex;
      margin-bottom: 8 * @rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      .title-text {
        height: 16 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #60666c;
        line-height: 16 * @rem;
      }

      span {
        flex: 1;
        min-width: 0;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #303236;
        line-height: 16 * @rem;
      }

      &.reward {
        span {
          font-size: 14 * @rem;
          line-height: 16 * @rem;
          font-weight: bold;
          color: #ff850a;
        }
      }
    }
    .star {
      display: flex;
      align-items: center;

      .star-list {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        .star-item {
          margin: 0 1 * @rem;
          width: 11 * @rem;
          height: 11 * @rem;
          background-image: url(~@/assets/images/welfare/star.png);
          background-size: 11 * @rem 11 * @rem;
          &.on {
            background-image: url(~@/assets/images/welfare/star-on.png);
          }
        }
      }
    }
  }

  .bounty-info {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    margin-top: 16 * @rem;

    .bounty-time {
      display: flex;
      align-items: center;

      .time-text {
        flex-shrink: 0;
        height: 15 * @rem;
        line-height: 15 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #60666c;
      }
      .time-clock {
        display: flex;
        align-items: center;
        font-size: 16 * @rem;
        color: #ff850a;
        line-height: 7 * @rem;
        .num-box {
          width: 18 * @rem;
          height: 18 * @rem;
          background-color: #ffede0;
          border-radius: 3 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11 * @rem;
          font-weight: bold;
          color: #ff850a;
          margin: 0 4 * @rem;
        }
      }
    }
    .down-user {
      height: 16 * @rem;
      overflow: hidden;
      .swiper-container {
        height: 100%;
      }
    }
    .game-player {
      display: flex;
      align-items: center;

      .avatar {
        width: 16 * @rem;
        height: 16 * @rem;
        border-radius: 50%;
        margin-right: 3 * @rem;
        overflow: hidden;
      }

      .nickname {
        height: 14 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #93999f;
        line-height: 12 * @rem;
      }
      .text {
        height: 14 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #93999f;
        line-height: 12 * @rem;
        margin-left: 3 * @rem;
      }
    }
  }

  .create-dialog {
    width: 300 * @rem;
    background: transparent;
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      width: 300 * @rem;
      height: 200 * @rem;
      background-color: #fff;
      border-radius: 20 * @rem;
      // margin-top: -4 * @rem;
      z-index: 2;
      padding: 30 * @rem 31 * @rem 20 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        text-align: center;
        line-height: 20 * @rem;
      }
      .message {
        margin-top: 25 * @rem;
        height: 40 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        word-break: break-all;
        &.mt30 {
          margin-top: 30 * @rem;
        }
      }
      .dialog-bottom-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 25 * @rem;
        // padding: 0 5 * @rem;
        position: relative;
        .cancel {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #7d7d7d;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f2f2f2;
          border-radius: 18 * @rem;
        }
        .confirm {
          width: 102 * @rem;
          height: 38 * @rem;
          line-height: 38 * @rem;
          color: #ffffff;
          font-size: 15 * @rem;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(90deg, #8ad6ef 0%, #a1b2f5 100%);
          border-radius: 18 * @rem;
          &.disabled {
            background: @themeBg;
          }
        }
        .confirm1 {
          width: 100%;
          height: 38 * @rem;
          line-height: 38 * @rem;
          color: #ffffff;
          font-size: 15 * @rem;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(90deg, #8ad6ef 0%, #a1b2f5 100%);
          border-radius: 18 * @rem;
          &.disabled {
            background: @themeBg;
          }
        }
        .btn-close {
          position: absolute;
          bottom: -52 * @rem;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            background: url('~@/assets/images/welfare/btn-close.png') no-repeat
              0 0;
            background-size: 22 * @rem 23 * @rem;
            width: 22 * @rem;
            height: 23 * @rem;
          }
        }
      }
    }
  }
}
</style>
