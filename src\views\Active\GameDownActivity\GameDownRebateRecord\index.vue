<template>
  <div class="page my-rebate-page">
    <nav-bar-2
      :title="$t('我的返利')"
      :border="true"
      :azShow="true"
    ></nav-bar-2>
    <div class="tab-bar">
      <div
        class="tab-item btn"
        v-for="(tab, tabIndex) in tabList"
        :key="tabIndex"
        :class="{ on: current === tabIndex }"
        @click="tapNav(tabIndex)"
      >
        {{ tab.name }}
      </div>
      <div
        class="line"
        :style="{ left: `${current * 125 * remNumberLess}rem` }"
      ></div>
    </div>
    <div
      class="my-rebate-container"
      v-for="(data, dataIndex) in dataList"
      :key="dataIndex"
      v-show="current === dataIndex"
    >
      <yy-list
        class="my-rebate-content"
        v-model="data.loadingObj"
        :finished="data.finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :check="false"
        :empty="data.empty"
      >
        <div class="rebate-list">
          <div
            class="rebate-item"
            v-for="(item, index) in data.data"
            :key="index"
          >
            <rebate-item :info="item"></rebate-item>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>

<script>
import { ApiDownActivityMineRebate } from '@/api/views/downActivity.js';
import { remNumberLess } from '@/common/styles/_variable.less';
import rebateItem from './components/rebate-item/index.vue';
export default {
  name: 'GameDownRebateRecord',
  components: {
    rebateItem,
  },
  data() {
    return {
      remNumberLess,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      empty: false,
      tabList: [
        { name: this.$t('待处理'), status: 0 },
        { name: this.$t('已发放'), status: 1 },
        { name: this.$t('驳回'), status: 2 },
      ],
      current: 0,
      dataList: [],
    };
  },
  async created() {
    this.tabList.forEach(tab => {
      this.dataList.push({
        data: [],
        finished: false,
        loadingObj: {
          loading: false,
          reloading: false,
        },
        empty: false,
        params: {
          page: 1,
          listRows: 10,
          status: tab.status,
        },
      });
    });
  },
  async activated() {
    this.current = 0;
    await this.getMyRebates();
  },
  methods: {
    tapNav(index) {
      if (this.current != index) {
        this.current = index;
        this.getMyRebates();
      }
    },
    async getMyRebates(action = 1) {
      let active = this.dataList[this.current];
      if (action === 1) {
        active.params.page = 1;
      } else {
        if (active.finished) {
          return;
        }
        active.params.page++;
      }
      active.loadingObj.loading = true;
      const res = await ApiDownActivityMineRebate(active.params);
      active.loadingObj.loading = false;
      if (action === 1 || active.params.page === 1) {
        active.data = [];
        if (!res.data.list.length) {
          active.empty = true;
        } else {
          active.empty = false;
        }
      }
      active.data.push(...res.data.list);
      if (res.data.list.length < active.params.listRows) {
        active.finished = true;
      } else {
        if (active.finished === true) {
          active.finished = false;
        }
      }
    },
    async onRefresh() {
      let active = this.dataList[this.current];
      active.finished = false;
      await this.getMyRebates();
      active.loadingObj.reloading = false;
    },
    async loadMore() {
      let active = this.dataList[this.current];
      await this.getMyRebates(2);
      active.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.my-rebate-page {
  flex-shrink: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  .tab-bar {
    position: fixed;
    width: 100%;
    height: 38 * @rem;
    z-index: 200;
    left: 0;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    .tab-item {
      width: 125 * @rem;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      font-weight: 400;
      color: #797979;
      &.on {
        color: #000000;
      }
    }
    .line {
      width: 14 * @rem;
      height: 4 * @rem;
      border-radius: 2 * @rem;
      background-color: #fe6600;
      position: absolute;
      bottom: 0 * @rem;
      transform: translateX(56 * @rem);
      transition: 0.3s;
    }
  }
  .my-rebate-container {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 1;
    background-color: #f5f5f6;
    padding-top: 38 * @rem;
    .rebate-list {
      padding: 0 * @rem 12 * @rem 0;
    }
  }
}
</style>
