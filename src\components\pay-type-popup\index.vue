<template>
  <div class="pay-type-popup">
    <van-popup
      v-model="payPopupShow"
      position="bottom"
      :lock-scroll="false"
      round
      @click-overlay="closePopup"
      class="pay-container-popup"
    >
      <div class="pay-container">
        <div class="pay-way-title"
          ><span>{{ unit }}</span> {{ money.toFixed(2) }}</div
        >
        <div class="pay-way-close btn" @click="closePopup"></div>
        <div class="pay-list" ref="payList" :class="{ showAll: showAll }">
          <div
            class="pay-item"
            v-for="(item, index) in list"
            :key="index"
            @click="selectedPayType = item"
          >
            <div class="pay-center">
              <div class="line">
                <img class="item-icon" :src="item.icon" alt="" />
                <div class="pay-name">{{ item.name }}</div>
                <div class="recommend-icon" v-if="item.recommend">{{
                  $t('推荐')
                }}</div>
              </div>
              <div
                v-if="item.activity && item.activity_info?.act_tag"
                class="subtitle"
              >
                <span
                  v-for="(tag, tagIndex) in item.activity_info.act_tag"
                  :key="tagIndex"
                  >{{ tag }}</span
                >
              </div>
            </div>
            <div
              class="choose"
              :class="{ on: selectedPayType.symbol == item.symbol }"
            ></div>
          </div>
        </div>
        <div
          class="more-pay-list"
          @click="showAll = true"
          v-if="list.length > 2 && !showAll"
        >
          {{ $t('更多支付方式') }}
        </div>
        <div class="pay-btn btn" @click="handlePay">
          {{ $t('立即支付') }}
        </div>
      </div>
      <!-- 预加载图片 -->
      <img
        style="display: none"
        src="~@/assets/images/recharge/surprise-coupon-bg2.png"
        alt=""
      />
    </van-popup>
    <van-popup
      v-model="welfarePopupShow"
      :close-on-click-overlay="false"
      position="bottom"
      :lock-scroll="true"
      round
      class="welfare-container-popup"
    >
      <div class="welfare-container">
        <div class="welfare-title">{{ welfareInfo.pop_title }}</div>
        <div class="welfare-close btn" @click="welfarePopupShow = false"></div>
        <div class="welfare-content">
          <div class="left"
            ><span>{{ unit }}</span
            >{{ welfareInfo.max_discount_amount }}</div
          >
          <div class="right">{{ welfareInfo.coupon_title }}</div>
        </div>
        <div class="btn-list">
          <div class="cancel-btn btn" @click="welfarePopupShow = false">
            {{ $t('放弃优惠并退出') }}</div
          >
          <div class="welfare-btn btn" @click="continuPay">
            {{ $t('继续支付') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'payTypePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: [],
    },
    money: {
      type: Number,
      default: 0,
    },
    unit: {
      type: String,
      default: '¥',
    },
  },
  data() {
    return {
      selectedPayType: {},
      showAll: false,
      welfarePopupShow: false,
      welfareInfo: {},
      welfareOnce: true,
    };
  },
  computed: {
    payPopupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },

  watch: {
    list(val) {
      this.selectedPayType =
        val.find(item => {
          return item.recommend;
        }) || val[0];
    },
  },
  methods: {
    closePopup() {
      this.payPopupShow = false;
      if (
        this.selectedPayType.activity &&
        this.selectedPayType.activity_info.is_retention &&
        this.welfareOnce
      ) {
        this.welfarePopupShow = true;
        this.welfareInfo = this.selectedPayType.activity_info;
        // this.welfareOnce = false;
      }
    },
    handlePay() {
      this.$emit('choosePayType', this.selectedPayType);
      this.payPopupShow = false;
    },
    continuPay() {
      this.payPopupShow = true;
      this.welfarePopupShow = false;
    },
    hidePayTypeList() {
      setTimeout(() => {
        this.$refs.payList.scrollTop = 0;
      }, 0);
      this.showAll = false;
    },
  },
};
</script>
<style lang="less" scoped>
.pay-container {
  display: flex;
  flex-direction: column;
  max-height: 427 * @rem;
  padding: 33 * @rem 10 * @rem 22 * @rem;
  box-sizing: border-box;
  position: relative;
  background: #f3f5f9;

  .pay-way-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50 * @rem;
    font-weight: 500;
    font-size: 36 * @rem;
    color: #222222;
    line-height: 50 * @rem;

    span {
      height: 22 * @rem;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #222222;
      line-height: 22 * @rem;
      margin-right: 2 * @rem;
      margin-top: 10 * @rem;
    }
  }

  .pay-way-close {
    width: 20 * @rem;
    height: 20 * @rem;
    background: url(~@/assets/images/close-gray.png) no-repeat;
    background-size: 20 * @rem 20 * @rem;
    position: absolute;
    top: 28 * @rem;
    right: 14 * @rem;
  }

  .pay-list {
    flex: 1;
    min-height: 0;
    margin-top: 46 * @rem;
    overflow: hidden;
    scroll-behavior: smooth;

    &.showAll {
      overflow: auto;
    }

    .pay-item {
      // width: 339 * @rem;
      height: 70 * @rem;
      background: #ffffff;
      border-radius: 8 * @rem;
      border: 1 * @rem solid #efefef;
      display: flex;
      align-items: center;
      padding: 0 12 * @rem;
      box-sizing: border-box;
      margin: 0 auto 8 * @rem;

      .pay-center {
        flex: 1;

        .line {
          display: flex;
          align-items: center;
          width: 100%;

          .item-icon {
            flex-shrink: 0;
            width: 20 * @rem;
            height: 20 * @rem;
            margin-right: 10 * @rem;
          }

          .pay-name {
            height: 21 * @rem;
            font-weight: 600;
            font-size: 15 * @rem;
            color: #222222;
            line-height: 21 * @rem;
            overflow: hidden;
          }

          .recommend-icon {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 34 * @rem;
            height: 18 * @rem;
            line-height: 1;
            margin-left: 3 * @rem;
            background: #fc3a3b;
            border-radius: 4 * @rem;
            font-weight: 600;
            font-size: 11 * @rem;
            color: #ffffff;
            text-align: center;
          }
        }

        .subtitle {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          height: 18 * @rem;
          overflow: hidden;
          margin-left: 30 * @rem;
          margin-top: 4 * @rem;

          span {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 16 * @rem;
            line-height: 16 * @rem;
            font-weight: 400;
            font-size: 10 * @rem;
            color: #fc3a3b;
            text-align: center;
            border-radius: 4 * @rem;
            border: 1 * @rem solid rgba(252, 58, 59, 0.4);
            padding: 0 4 * @rem;
            margin-right: 6 * @rem;
          }
        }
      }

      .choose {
        flex-shrink: 0;
        width: 18 * @rem;
        height: 18 * @rem;
        border-radius: 50%;
        border: 1 * @rem solid #e5e6eb;

        &.on {
          background: url(~@/assets/images/recharge/pay-selected-circle.png)
            no-repeat;
          background-size: 18 * @rem 18 * @rem;
          border: none;
        }
      }
    }

    .shouqi {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 13 * @rem;
      color: #666666;
      line-height: 18 * @rem;
      margin: 20 * @rem;

      &::after {
        content: '';
        display: block;
        width: 12 * @rem;
        height: 7 * @rem;
        background: url(~@/assets/images/recharge/gray-bottom-arrow.png)
          no-repeat;
        background-size: 12 * @rem 7 * @rem;
        margin-left: 4 * @rem;
        transform: rotate(-180deg);
      }
    }
  }

  .more-pay-list {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 13 * @rem;
    color: #666666;
    line-height: 18 * @rem;
    margin: 20 * @rem;

    &::after {
      content: '';
      display: block;
      width: 12 * @rem;
      height: 7 * @rem;
      background: url(~@/assets/images/recharge/gray-bottom-arrow.png) no-repeat;
      background-size: 12 * @rem 7 * @rem;
      margin-left: 4 * @rem;
      transition: all 0.2s linear;
    }
  }

  .pay-btn {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 355 * @rem;
    height: 44 * @rem;
    background: #3cd279;
    border-radius: 6 * @rem;
    font-weight: 500;
    font-size: 16 * @rem;
    color: #ffffff;
    line-height: 22 * @rem;
    text-align: center;
    margin-top: 20 * @rem;
  }
}

.welfare-container {
  padding: 25 * @rem 0 30 * @rem;
  background-color: #f3f5f9;

  .welfare-close {
    width: 20 * @rem;
    height: 20 * @rem;
    background: url(~@/assets/images/close-gray.png) no-repeat;
    background-size: 20 * @rem 20 * @rem;
    position: absolute;
    top: 20 * @rem;
    right: 14 * @rem;
  }

  .welfare-title {
    height: 28 * @rem;
    font-weight: bold;
    font-size: 20 * @rem;
    color: #121212;
    line-height: 28 * @rem;
    text-align: center;
  }

  .welfare-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 348 * @rem;
    height: 104 * @rem;
    background: url(~@/assets/images/recharge/surprise-coupon-bg2.png) no-repeat;
    background-size: 348 * @rem 104 * @rem;
    margin: 30 * @rem auto 0;

    .left {
      display: flex;
      justify-content: center;
      width: 125 * @rem;
      height: 31 * @rem;
      font-weight: bold;
      font-size: 26 * @rem;
      color: #f30f03;
      line-height: 30 * @rem;

      span {
        width: 10 * @rem;
        height: 22 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #f30f03;
        line-height: 19 * @rem;
        margin-top: 2 * @rem;
        margin-right: 3 * @rem;
      }
    }

    .right {
      width: 170 * @rem;
      font-weight: bold;
      font-size: 18 * @rem;
      color: #ffffff;
      line-height: 25 * @rem;
      text-align: center;
    }
  }

  .btn-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 347 * @rem;
    margin: 34 * @rem auto 0;

    .cancel-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 155 * @rem;
      height: 44 * @rem;
      border-radius: 6 * @rem;
      border: 1 * @rem solid #979797;
      font-weight: bold;
      font-size: 16 * @rem;
      color: #979797;
      line-height: 22 * @rem;
      text-align: center;
    }

    .welfare-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 180 * @rem;
      height: 44 * @rem;
      background: #3cd279;
      border-radius: 6 * @rem;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 22 * @rem;
      text-align: center;
    }
  }
}
</style>
