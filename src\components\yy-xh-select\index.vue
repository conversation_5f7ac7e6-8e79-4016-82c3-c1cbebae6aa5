<template>
  <div>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(id)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
/**
 * 组件使用方式：
 * import yyXhSelect from "@/components/yy-xh-select/index.vue";
 * components: {
 *  yyXhSelect,
 * }
 *
 * <yy-xh-select
 *   :show.sync="xhDialogShow"
 *   :id="游戏id"
 *   :autoXh="1" // 需要自动创建小号的时候才要传这个参数
 *   @onSelectSuccess="onSelectSuccess"
 *  ></yy-xh-select>
 * onSelectSuccess({xhId: xxx, autoXh: 1}) 回调函数的参数为一个对象 xhId为选择的小号id，autoXh为是否要自动创建一个小号
 */
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
export default {
  name: 'yy-xh-select',
  components: {
    xhCreateTipDialog,
  },
  props: {
    defaultXh: {
      type: Number | String,
      default: 0,
    },
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      // 游戏id
      type: Number,
      required: true,
    },
    // 可选，需要自动创建小号的时候才要传这个参数
    autoXh: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      xiaohaoList: [], // 小号列表
      xiaohaoListShow: false, // 显示小号列表
      xhDialogShow: false, // 显示小号选择弹窗
      createDialogShow: false, // 没有小号列表时显示弹窗
      xiaohaoListShowItem: {}, // 选中的小号
    };
  },
  computed: {
    dialogInfo() {
      const { show, id } = this;
      return {
        show,
        id,
      };
    },
    currentDialogInfo() {
      const { xhDialogShow, createDialogShow } = this;
      return {
        xhDialogShow,
        createDialogShow,
      };
    },
  },
  watch: {
    dialogInfo: {
      async handler(val, oldVal) {
        if (val.id != oldVal.id || val.show == true) {
          await this.getXiaohaoList();
        }
        if (val.show == true) {
          this.checkXiaohao();
        }
      },
      deep: true,
    },
    currentDialogInfo: {
      handler(val, oldVal) {
        if (val.xhDialogShow == false && val.createDialogShow == false) {
          this.$emit('update:show', false);
        }
      },
      deep: true,
    },
  },
  // emit 暴露出去一个对象 例如：{xhId: xxx, autoXh: 1},
  // autoXh 为1表示为用户自动创建小号
  methods: {
    checkXiaohao() {
      // 判断是否存在小号
      if (!this.xiaohaoList.length) {
        this.createDialogShow = true;
        return;
      }
      // 只有一个小号，直接帮助用户选择
      if (this.xiaohaoList.length == 1) {
        this.xiaohaoListShowItem = this.xiaohaoList[0];
        this.$emit('onSelectSuccess', {
          xhId: this.xiaohaoListShowItem.id,
          nickname: this.xiaohaoListShowItem.nickname,
          autoXh: 1,
        });
        return;
      }
      // 有多个小号的情况，弹出小号选择窗口
      const defaultXh = this.xiaohaoList.find(
        item => item.id == this.defaultXh,
      );
      if (defaultXh) {
        this.xiaohaoListShowItem = defaultXh;
      } else {
        this.xiaohaoListShowItem = this.xiaohaoList[0];
      }
      this.xhDialogShow = true;
    },
    // 点击小号列表中的一项
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    // 获取小号列表
    async getXiaohaoList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.id,
        autoXh: this.autoXh,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
        this.xiaohaoListShowItem = this.xiaohaoList[0];
      } else {
        this.xiaohaoList = [];
        this.xiaohaoListShowItem = {};
      }
    },
    // 关闭选择小号弹窗
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    // 选完小号点击确定
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.$emit('onSelectSuccess', {
        xhId: this.xiaohaoListShowItem.id,
        nickname: this.xiaohaoListShowItem.nickname,
      });
      this.$toast.clear();
    },
  },
};
</script>

<style lang="less" scoped>
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
