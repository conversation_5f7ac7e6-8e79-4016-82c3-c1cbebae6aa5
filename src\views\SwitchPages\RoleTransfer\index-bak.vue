<template>
  <div class="role-transfer-page">
    <nav-bar-2
      bgStyle="transparent"
      title="交易中心"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg-circle"> </div>
    <div class="main">
      <div class="banner">
        <img
          src="@/assets/images/role-transfer/role-transfer-banner.png"
          alt=""
        />
      </div>
      <div class="transfer-tips">{{ $t('未成年用户无法使用角色转移') }}</div>
      <div class="transfer-container">
        <div class="transfer-item" @click="toPage('Recycle')">
          <div class="left-info">
            <div class="title">回收小号</div>
            <div class="tips">官方回收，大量福利</div>
          </div>
          <div class="right-icon">
            <img src="@/assets/images/role-transfer/role-recycle.png" alt="" />
          </div>
        </div>
        <div class="transfer-item" @click="toPage('Deal')">
          <div class="left-info">
            <div class="title">账号交易</div>
            <div class="tips">角色交易，无需换号</div>
          </div>
          <div class="right-icon">
            <img src="@/assets/images/role-transfer/role-deal.png" alt="" />
          </div>
        </div>
        <div class="transfer-item" @click="toPage('Zhuanyou')">
          <div class="left-info">
            <div class="title">转游福利</div>
            <div class="tips">游戏关服不用怕，转游点换代金券</div>
          </div>
          <div class="right-icon">
            <img src="@/assets/images/role-transfer/role-zhuanyou.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiV2024TransactionCenter } from '@/api/views/welfare.js';
export default {
  data() {
    return {
      transactionCenterData: {},
    };
  },
  created() {
    this.getTransactionCenter();
  },
  methods: {
    async getTransactionCenter() {
      const res = await ApiV2024TransactionCenter();
      this.transactionCenterData = res.data;
    },
  },
};
</script>

<style lang="less" scoped>
.role-transfer-page {
  position: relative;
  background: #beffe2;
  min-height: 100vh;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
  .bg-circle {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    width: 100%;
    height: 347 * @rem;
    background: url('~@/assets/images/role-transfer/role-transfer-bg.png')
      no-repeat;
    background-size: 100% 347 * @rem;
  }
  .main {
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(117, 243, 192, 0.81) 0%,
      rgba(190, 255, 227, 0.88) 55%,
      rgba(190, 255, 214, 0) 100%
    );
    padding-top: calc(50 * @rem + @safeAreaTop);
    padding-top: calc(50 * @rem + @safeAreaTopEnv);
    padding-bottom: 59 * @rem;
    .banner {
      margin: 0 auto;
      width: 310 * @rem;
      height: 305 * @rem;
    }
    .transfer-tips {
      text-align: center;
      margin: 0 auto;
      width: 310 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #60666c;
      margin-top: 7 * @rem;
    }
    .transfer-container {
      margin-top: 29 * @rem;
      padding: 0 12 * @rem;
      .transfer-item {
        width: 351 * @rem;
        height: 92 * @rem;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 20 * @rem;
        border: 2 * @rem solid rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16 * @rem 0 20 * @rem;
        box-sizing: border-box;
        &:not(:last-of-type) {
          margin-bottom: 12 * @rem;
        }
        .left-info {
          .title {
            font-weight: 600;
            font-size: 18 * @rem;
            color: #111f3c;
          }
          .tips {
            margin-top: 6 * @rem;
            font-size: 14 * @rem;
            color: rgba(17, 31, 60, 0.75);
          }
        }
        .right-icon {
          width: 72 * @rem;
          height: 60 * @rem;
        }
      }
    }
  }
}
</style>
