<template>
  <div class="cloud-recharge">
    <main v-if="orderInfo.coupon" v-show="!couponListShow" class="pay-page">
      <nav-bar-2 :border="true" :title="orderInfo.itemName"></nav-bar-2>
      <div class="project-name">
        <img
          class="header-img"
          src="~@/assets/images/game-recharge/money.png"
        />
        <h2>{{ $t('游戏道具') }}</h2>
      </div>
      <div v-if="orderInfo.username !== userInfo.username" class="error">
        <div class="text">
          {{
            $t(
              '订单账号与本地账号不一致，将无法使用代金券、平台币、金币进行充值',
            )
          }}
        </div>
        <div @click="changeAccount()" class="button">
          {{ $t('切换账号') }}
        </div>
      </div>
      <ul class="listMsg">
        <li v-if="orderInfo.username !== userInfo.username">
          <div class="left">{{ $t('订单账号') }}</div>
          <div class="right">{{ orderInfo.username }}</div>
        </li>
        <li v-if="orderInfo.username !== userInfo.username">
          <div class="left">{{ $t('本地账号') }}</div>
          <div class="right">{{ userInfo.username }}</div>
        </li>
        <li v-if="orderInfo.username === userInfo.username" class="username">
          <div class="left">{{ $t('充值账号') }}</div>
          <div class="right">{{ userInfo.username }}</div>
        </li>
        <li class="original_price">
          <div class="left">{{ $t('原价') }}</div>
          <div class="right">
            <span class="money">￥{{ money }}</span
            >元
          </div>
        </li>
        <li class="coupon">
          <div class="left">{{ $t('代金券') }}</div>
          <div class="right">
            <div v-if="orderInfo.username !== userInfo.username" class="empty">
              {{ $t('无法使用') }}
            </div>
            <template v-else>
              <div
                v-if="
                  orderInfo.coupon.coupon_record_id > 0 ||
                  orderInfo.coupon.coupon_count > 0
                "
                @click="toCouponList()"
                class="select btn"
              >
                <div class="arrow"></div>
                <div
                  v-if="orderInfo.coupon.coupon_count > 0"
                  class="coupon-number"
                >
                  {{ orderInfo.coupon.coupon_count }}{{ $t('个可用') }}
                </div>
                <div
                  v-if="orderInfo.coupon.coupon_record_id > 0"
                  class="now money"
                >
                  -￥{{ orderInfo.coupon.max_money }}
                </div>
              </div>
              <div v-else class="empty">
                {{ $t('暂无可用') }}
                <span class="btn" @click="toPage('ChangwanCard')">{{
                  $t('开通畅玩卡立省￥6')
                }}</span>
              </div>
            </template>
          </div>
        </li>
        <li class="color">
          <div class="left">
            {{ $t('共计') }}：<span class="money">￥{{ orderInfo.amount }}</span
            >元
          </div>
        </li>
      </ul>
      <ul class="pay-type">
        <div class="title">{{ $t('请选择支付方式') }}</div>
        <li
          v-for="(item, index) in payArr"
          :key="index"
          :class="{
            'error-item':
              (item.key === 'ptb' || item.key === 'gold') &&
              orderInfo.username !== userInfo.username,
          }"
          @click="selectPayWay(item.key)"
          class="arro btn"
        >
          <div class="left">
            <img :src="item.icon" />
          </div>
          <div
            :class="{ center2: item.key === 'ptb' || item.key === 'gold' }"
            class="center"
          >
            <span class="text">{{ item.name }}</span>
            <span class="yue" v-if="item.key === 'ptb'"
              >{{ $t('账户余额') }}：<span class="money">{{
                userInfo.ptb
              }}</span
              >{{ $t('个') }}</span
            >
            <span class="yue" v-if="item.key === 'gold'"
              >{{ $t('账户余额') }}：<span class="money">{{
                userInfo.gold
              }}</span
              >{{ $t('个') }}</span
            >
          </div>
          <div class="right">
            <i :class="{ checked: item.key === payType }"></i>
          </div>
        </li>
      </ul>
      <div class="bottom">
        <div @click="pay()" class="bottom-button btn">
          {{ $t('立即支付') }}
        </div>
      </div>
    </main>
    <main v-if="couponListShow" class="coupon-page">
      <nav-bar-2
        :border="true"
        :title="$t('选择优惠券')"
        :backShow="false"
      ></nav-bar-2>
      <div class="coupon-list">
        <div class="title">
          {{ $t('可使用优惠券')
          }}<span>{{ couponList.length }}{{ $t('张') }}</span>
        </div>
        <div
          v-for="(item, index) in couponList"
          :key="index"
          @click="selectCoupon(item, index)"
          :class="{ current: index === current }"
          class="coupon"
        >
          <div class="top">
            <div
              :class="{
                size2: item.money >= 100 && item.money < 10000,
                size3: item.money >= 10000,
              }"
              class="left"
            >
              {{ item.money }}<span>元</span>
            </div>
            <div class="center">
              <div class="big-text">{{ item.title }}</div>
              <div class="small-text">
                {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
              </div>
              <div class="small-text small-text2">
                {{ item.create_time }}-{{ item.expire_time }}
              </div>
            </div>
            <div class="right"></div>
          </div>
          <div class="bottom">{{ item.remark }}</div>
        </div>
      </div>
      <div class="coupon-list coupon-no-list">
        <div class="title">
          {{ $t('不可使用优惠券')
          }}<span>{{ couponNoList.length }}{{ $t('张') }}</span>
        </div>
        <div v-for="(item, index) in couponNoList" :key="index" class="coupon">
          <div class="top">
            <div
              :class="{
                size2: item.money >= 100 && item.money < 10000,
                size3: item.money >= 10000,
              }"
              class="left"
            >
              {{ item.money }}<span>元</span>
            </div>
            <div class="center">
              <div class="big-text">{{ item.title }}</div>
              <div class="small-text">
                {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
              </div>
              <div class="small-text small-text2">
                {{ item.create_time }}-{{ item.expire_time }}
              </div>
            </div>
          </div>
          <div class="bottom">{{ item.remark }}</div>
        </div>
      </div>
      <div class="bottom-container">
        <div @click="confirmCoupon()" class="bottom-button btn">
          <div class="left">
            {{ $t('代金券优惠') }}￥{{ currentCouponMoney }}
          </div>
          <div class="right">{{ $t('确认') }}</div>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { ApiGetPayUrl } from '@/api/views/recharge';
import { ApiGetGamePayInfo } from '@/api/views/game';
import { ApiGetGameCouponList, ApiChooseCoupon } from '@/api/views/coupon';

export default {
  name: 'GameRecharge',
  data() {
    return {
      orderInfo: {}, //订单信息
      payArr: [], //可支付方式列表
      coupon: 0, //当前选择的优惠券
      payType: '', //当前选择的支付方式
      orderId: '', //当前订单ID
      orderType: '', //当前订单类型
      couponListShow: false, //是否显示优惠券列表
      couponList: [], //优惠券列表
      couponNoList: [], //优惠券不可用列表
      currentCoupon: {}, //当前选中代金券,
      current: false, //当前选中代金券index
    };
  },
  computed: {
    money() {
      return parseInt(this.orderInfo.original_price) > 0
        ? parseInt(this.orderInfo.original_price)
        : parseInt(this.orderInfo.amount);
    },
    currentCouponMoney() {
      return this.currentCoupon.money ? this.currentCoupon.money : 0.0;
    },
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化页面
    async init() {
      this.orderId = this.$route.query.orderId;
      let loading = this.$toast.loading({
        message: this.$t('加载中...'),
        forbidClick: true,
      });
      try {
        const res = await ApiGetGamePayInfo({ orderId: this.orderId });
        this.orderInfo = res.data.orderInfo;
        this.payArr = res.data.payArr;
        this.orderType = res.data.orderType;
        const res2 = await ApiGetGameCouponList({ orderId: this.orderId });
        this.couponList = res2.data.list;
        this.couponNoList = res2.data.no_list;
      } catch {
        this.back();
      } finally {
        loading.clear();
      }
    },
    // 支付
    async pay() {
      if (!this.payType) {
        this.$toast(this.$t('请选择支付方式'));
        return false;
      }
      switch (this.payType) {
        case 'ptb':
          if (this.orderInfo.username !== this.userInfo.username) {
            this.$toast(this.$t('订单账号和本地账号不同，无法使用该支付'));
            return false;
          }
          if (this.orderInfo.amount > parseInt(this.orderInfo.ptbBal) / 10) {
            this.$toast(this.$t('平台币不足'));
            return false;
          }
          break;
        case 'gold':
          if (this.orderInfo.username !== this.userInfo.username) {
            this.$toast(this.$t('订单账号和本地账号不同，无法使用该支付'));
            return false;
          }
          if (this.orderInfo.amount > parseInt(this.orderInfo.goldBal) / 100) {
            this.$toast(this.$t('金币不足'));
            return false;
          }
          break;
        case 'wx':
          break;
        case 'zfb_dmf':
          break;
      }
      try {
        await ApiGetPayUrl({
          orderId: this.orderId,
          orderType: this.orderType,
          payWay: this.payType,
        });
      } finally {
        this.$router.replace({ name: 'GameRechargeOrder' });
      }
    },
    // 选择优惠券
    selectCoupon(item, index) {
      if (this.current === index) {
        this.current = -1;
        this.currentCoupon = {};
      } else {
        this.current = index;
        this.currentCoupon = item;
      }
    },
    // 确认优惠券
    async confirmCoupon() {
      await ApiChooseCoupon({
        orderId: this.orderId,
        RecordId: this.currentCoupon.id,
      });
      this.init();
      this.couponListShow = false;
    },
    // 改变账号
    changeAccount() {
      this.toPage('PhoneLogin');
    },
    // 打开优惠券列表
    toCouponList() {
      if (this.orderInfo.username !== this.userInfo.username) {
        this.$toast(this.$t('订单账号和本地账号不同，无法使用优惠券'));
        return false;
      }
      this.couponListShow = true;
    },
    // 选择支付方式
    selectPayWay(payway) {
      if (
        (payway === 'ptb' || payway === 'gold') &&
        this.orderInfo.username !== this.userInfo.username
      ) {
        this.$toast(this.$t('订单账号和本地账号不同，无法使用该支付'));
      } else {
        this.payType = payway;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.pay-page {
  padding-bottom: 80 * @rem;
  .project-name {
    margin: 20 * @rem auto 35 * @rem;
    .header-img {
      display: block;
      width: 58 * @rem;
      height: 60 * @rem;
      margin: 0 auto 12 * @rem;
    }
    h2 {
      text-align: center;
      font-size: 18 * @rem;
      font-weight: bolder;
      color: #000;
    }
  }
  .money {
    color: #ff2227;
  }
  .listMsg {
    padding: 0 14 * @rem;
    font-size: 15 * @rem;
    li {
      display: flex;
      height: 35 * @rem;
      justify-content: space-between;
      align-items: center;
      &.username {
        .right {
          color: #888888;
        }
      }
      &.coupon {
        .right {
          flex: 1;
          text-align: right;
          .select {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            .coupon-number {
              padding: 4 * @rem 6 * @rem;
              background: #ff6905;
              font-size: 12 * @rem;
              color: #fff;
              border-radius: 5 * @rem;
            }
            .now {
              height: 21 * @rem;
            }
          }
          .arrow {
            width: 7 * @rem;
            height: 12 * @rem;
            margin-left: 5 * @rem;
            background-image: url(~@/assets/images/right-icon.png);
            background-size: 100%;
            background-repeat: no-repeat;
          }
          .empty {
            color: #ababab;
            span {
              margin-left: 5 * @rem;
              padding: 4 * @rem 5 * @rem;
              font-size: 12 * @rem;
              color: #f35600;
              background-color: #ffcf67;
              border-radius: 4 * @rem;
            }
          }
        }
      }
    }
  }
  .pay-type {
    margin-top: 10 * @rem;
    border-top: 0.5 * @rem solid #eeeeee;
    padding: 0 14 * @rem;
    .title {
      height: 40 * @rem;
      margin-top: 5 * @rem;
      line-height: 40 * @rem;
      font-size: 15 * @rem;
    }
    li {
      display: flex;
      justify-content: space-between;
      margin-bottom: 22 * @rem;
      &.error-item {
        color: #999;
        .money {
          color: #999;
        }
        .right i {
          background-color: #eee;
        }
      }
      .left {
        display: flex;
        img {
          width: 35 * @rem;
          height: 35 * @rem;
        }
      }
      .center {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 8 * @rem;
        flex: 1;
        &.center2 {
          justify-content: space-between;
        }
        .text {
          font-size: 15 * @rem;
        }
        .yue {
          color: #888888;
        }
      }
      .right {
        display: flex;
        align-items: center;
        i {
          display: block;
          width: 25 * @rem;
          height: 25 * @rem;
          border-radius: 50%;
          overflow: hidden;
          box-sizing: border-box;
          border: 1 * @rem solid #bfbfbf;
          &.checked {
            background-image: url(~@/assets/images/game-recharge/checked.png);
            background-size: 100%;
            border: none;
          }
        }
      }
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70 * @rem;
    background: #fff;
    .bottom-button {
      margin: 15 * @rem 14 * @rem;
      background: #ff6905;
      color: #fff;
      line-height: 40 * @rem;
      height: 40 * @rem;
      text-align: center;
      font-size: 16 * @rem;
      border-radius: 5 * @rem;
      overflow: hidden;
    }
  }
  .error {
    padding: 0 14 * @rem;
    .text {
      text-align: center;
      font-size: 14 * @rem;
      color: #f60;
      line-height: 24 * @rem;
    }
    .button {
      width: 120 * @rem;
      height: 35 * @rem;
      line-height: 35 * @rem;
      text-align: center;
      margin: 15 * @rem auto;
      background: @themeColor;
      border-radius: 20 * @rem;
      font-size: 14 * @rem;
      color: #fff;
    }
  }
}
.coupon-page {
  min-height: 100vh;
  background-color: #f6f6f6;
  .title {
    margin: 10 * @rem 0;
    padding: 0 14 * @rem;
    font-size: 15 * @rem;
    color: #333;
    span {
      margin-left: 10 * @rem;
      font-size: 12 * @rem;
      color: #666;
    }
  }
  .coupon {
    position: relative;
    box-sizing: border-box;
    width: 360 * @rem;
    height: 135 * @rem;
    margin: 0 auto;
    padding: 0 20 * @rem;
    background-image: url(~@/assets/images/game-recharge/coupon.png);
    background-size: 100%;
    background-repeat: no-repeat;
    overflow: hidden;
    &.current {
      .top {
        .right {
          background-image: url(~@/assets/images/game-recharge/checked.png);
          border: 1px solid #fff;
          background-size: 100%;
        }
      }
    }
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 88 * @rem;
      margin-top: 6 * @rem;
      // background: blue;
      color: #333;
      .left {
        flex: 0 0 80 * @rem;
        line-height: 1;
        text-align: center;
        font-size: 39 * @rem;
        color: #ff6600;
        &.size2 {
          font-size: 26 * @rem;
        }
        &.size3 {
          font-size: 20 * @rem;
        }
        span {
          position: relative;
          top: -2 * @rem;
          left: 3 * @rem;
          font-size: 13 * @rem;
          color: #ff8d40;
        }
      }
      .center {
        flex: 1;
        text-align: left;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        box-sizing: border-box;
        padding: 12 * @rem 0;
        margin-left: 15 * @rem;
        overflow: hidden;
        .big-text {
          font-size: 14 * @rem;
          white-space: nowrap;
        }
        .small-text {
          font-size: 12 * @rem;
          color: #666;
        }
        .small-text2 {
          color: #999;
        }
      }
      .right {
        width: 20 * @rem;
        height: 20 * @rem;
        background-color: #fff;
        border-radius: 14 * @rem;
        border: 1px solid #d2d2d2;
        text-align: center;
        flex: 0 0 20 * @rem;
      }
    }
    .bottom {
      height: 33 * @rem;
      line-height: 33 * @rem;
      color: #666666;
    }
  }
  .coupon-no-list {
    margin-top: 20 * @rem;
    .coupon {
      background-image: url(~@/assets/images/game-recharge/no-coupon.png);
      .top {
        .left {
          color: #fff;
          span {
            color: #f2f2f2;
          }
        }
        .center {
          .big-text {
            color: #fff;
          }
          .small-text {
            color: #fff;
          }
          .small-text2 {
            color: #f3f3f3;
          }
        }
        .right {
          width: 20 * @rem;
          height: 20 * @rem;
          background-color: #fff;
          border-radius: 14 * @rem;
          border: 1px solid #d2d2d2;
          background-color: #f4f4f4;
          text-align: center;
          &.already {
            color: #999;
          }
        }
      }
    }
  }
  .bottom-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70 * @rem;
    background: #fff;
    box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.06);
    .bottom-button {
      display: flex;
      justify-content: space-between;
      margin: 15 * @rem 14 * @rem;
      background: #151515;
      color: #fff;
      line-height: 40 * @rem;
      height: 40 * @rem;
      text-align: center;
      font-size: 16 * @rem;
      border-radius: 25.5 * @rem;
      overflow: hidden;
      .left {
        flex: 0 0 70%;
        text-align: left;
        text-indent: 2em;
      }
      .right {
        flex: 0 0 30%;
        background: linear-gradient(to right, #ffbf37, #ffaf15);
      }
    }
  }
}
</style>
