<template>
  <div class="my-feedback">
    <nav-bar-2
      :border="true"
      :title="$t('我的反馈')"
      :azShow="true"
    ></nav-bar-2>
    <content-empty v-if="!feedList.length"></content-empty>
    <section v-else v-for="(item, index) in feedList" :key="index">
      <div class="text">{{ $t('游戏名称') }}：{{ item.game_name }}</div>
      <div class="text">{{ item.report_cate }}</div>
      <div class="content">{{ item.saytext }}</div>
      <div class="time">{{ item.saytime }}</div>
      <div class="image-list" v-if="item.photos">
        <img
          v-for="(item2, index2) in imageList(item)"
          :key="index2"
          :src="item2"
          @click="imagePreview(item2)"
        />
      </div>
      <div class="footer" :class="{ color: !item.editor_remark }">
        <span>{{ $t('官方回复') }}：</span
        >{{ item.editor_remark ? item.editor_remark : $t('您的反馈待处理') }}
      </div>
    </section>
  </div>
</template>
<script>
import { ApiMyFeedback } from '@/api/views/users';
import { ImagePreview } from 'vant';
import { platform } from '@/utils/box.uni.js';

export default {
  name: 'MyFeedback',
  data() {
    return {
      feedList: [], //反馈列表
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('我的反馈');
    }
    const res = await ApiMyFeedback();
    this.feedList = res.data.feedbackList;
  },
  methods: {
    imageList(item) {
      return item.photos.split(',');
    },
    imagePreview(item2) {
      ImagePreview([item2]);
    },
  },
};
</script>

<style lang="less" scoped>
.my-feedback {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 30 * @rem;
}
section {
  width: 347 * @rem;
  height: auto;
  box-sizing: border-box;
  margin: 18 * @rem auto 0;
  padding: 14 * @rem 14 * @rem 0;
  line-height: 23 * @rem;
  background: #fff;
  border-radius: 10 * @rem;
  font-size: 15 * @rem;
  color: #666666;
  .text {
    margin-bottom: 3 * @rem;
  }
  .content {
    margin-top: 15 * @rem;
  }
  .time {
    margin-top: 10 * @rem;
    font-size: 12 * @rem;
    color: #aaaaaa;
  }
  .image-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    img {
      flex: 0 0 30%;
      width: 100 * @rem;
      height: 110 * @rem;
      margin-top: 15 * @rem;
      border-radius: 5 * @rem;
      overflow: hidden;
    }
  }
  .footer {
    height: 50 * @rem;
    line-height: 50 * @rem;
    margin-top: 15 * @rem;
    border-top: 1px solid #e5e5e5;
    color: #666666;
    &.color {
      color: #158af3;
    }
    span {
      color: #666666;
      font-weight: 600;
    }
  }
}
</style>
