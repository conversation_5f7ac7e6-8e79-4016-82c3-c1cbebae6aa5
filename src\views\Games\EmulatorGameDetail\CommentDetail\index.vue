<template>
  <div class="list-page">
    <nav-bar-2 ref="navBar" title="评论详情" :azShow="true"></nav-bar-2>
    <div class="main-comment" v-if="info.comment_id">
      <CommentItem2
        :comment="info"
        :showChildren="false"
        :showReplyCount="false"
        :showOverflow="false"
        :showOperation="false"
        :showTypesettingImg1="false"
        :showTypesettingImg2="true"
        :showScoreTime="true"
        :noJump="true"
      ></CommentItem2>
    </div>
    <div class="h8-bg" ref="h8Bg"></div>
    <div class="list-container">
      <div class="title">
        <span class="text">共{{ reply_count }}条评论</span>
        <div class="order">
          <span :class="{ active: order == 2 }" @click="changeOrder(2)"
            >正序</span
          >
          <span :class="{ active: order == 1 }" @click="changeOrder(1)"
            >倒序</span
          >
        </div>
      </div>
      <div class="user-comment" v-if="userInfo.token && reply_count">
        <user-avatar class="avatar"></user-avatar>
        <div class="user-click" @click="handleReply(info)">
          <span>写下你的想法...</span>
        </div>
      </div>
      <div class="empty-content" v-if="empty">
        <div class="empty-img">
          <img :src="emptyImg" alt="" />
        </div>
        <div class="tips">期待你的首次评论</div>
        <div class="btn" @click="handleReply(info)"> 去评论 </div>
      </div>
      <yy-list
        v-else
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
        :check="false"
        tips="暂无数据"
      >
        <div class="comment-list">
          <div class="comment-item1" v-for="(item, index) in list" :key="index">
            <CommentItem2
              class="item"
              :comment="item"
              :showReplyCount="false"
              :showPhone="false"
              :noJump="true"
              :showLeftReply="true"
              :showRightReply="false"
              :level="2"
            ></CommentItem2>
          </div>
        </div>
      </yy-list>
    </div>
    <!-- 底部fixed -->
    <div class="bottom-container" v-if="info.comment_id">
      <div class="bottom-fixed">
        <div class="comment-content">
          <div class="text" @click="handleReply(info)">写下你的想法...</div>
          <div class="operation">
            <div
              class="like"
              :class="{ active: info.is_support == 1 }"
              @click="handleLike(info)"
            >
              <span class="like-icon"></span>
              <span v-if="info.support_count">{{ info.support_count }}</span>
              <span v-else>点赞</span>
            </div>
            <div class="reply" @click="goToSticky()">
              <span class="reply-icon"></span>
              <span v-if="info.reply_count">{{ info.reply_count }}</span>
              <span v-else>回复</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="isReplying" :lock-scroll="false" position="bottom">
      <div class="reply-editor" v-if="isReplying && replyInfo.level == 1">
        <div class="class1-bg">
          <div class="input-container1">
            <input
              id="inputText"
              type="text"
              v-model.trim="replyInput"
              placeholder="写下你的想法..."
            />
          </div>
          <div
            class="send-btn1 class1"
            :class="{ on: replyInput.length > 0 }"
            @click="sendReply"
          >
            发送
          </div>
        </div>
      </div>
      <div class="reply-editor" v-if="isReplying && replyInfo.level != 1">
        <div class="input-container">
          <input
            id="inputText"
            type="text"
            v-model.trim="replyInput"
            :placeholder="`回复@${replyNickname}`"
          />
        </div>
        <div
          class="send-btn"
          :class="{ on: replyInput.length > 0 }"
          @click="sendReply"
        >
          回复
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiCommentReplies,
  ApiCommentSubmit,
  ApiResourceSupport,
} from '@/api/views/comment.js';
import CommentItem2 from '@/components/comment-item-2';
import { platform } from '@/utils/box.uni.js';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
export default {
  name: 'CommentDetailGameDetail',
  data() {
    return {
      id: 0,
      sourceId: 0,
      info: {},
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      order: 2, //排序 1倒序 2正序
      finished: false,
      empty: false,
      page: 1,
      listRows: 10,
      isReplying: false,
      reply_count: 0,
      replyNickname: '',
      replyInput: '',
      replyInfo: {},
      emptyImg,
      h8Top: 0,
      scrollTop: 0,
    };
  },
  components: {
    CommentItem2,
  },
  async created() {
    this.id = this.$route.params.id || 0;
    this.sourceId = this.$route.params.sourceId || 0;
    this.loadingObj.loading = true;
    await this.getList(2);
    this.loadingObj.loading = false;
    const h8BgElement = this.$refs.h8Bg;
    const navBarElement = this.$refs.navBar.$el;
    if (h8BgElement && navBarElement) {
      const rect = h8BgElement.getBoundingClientRect();
      const navBar = navBarElement.getBoundingClientRect();
      this.h8Top = rect.top - navBar.height;
    }
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    goToSticky() {
      window.scrollTo({
        top: this.h8Top,
      });
    },
    handleScroll() {
      this.scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
    },
    async handleLike(item) {
      if (!item.is_support) {
        item.is_support = -1;
      }
      const res = await ApiResourceSupport({
        classId: 103,
        sourceId: item.comment_id,
        status: item.is_support == 1 ? -1 : 1,
      });
      if (item.is_support == -1) {
        if (!item.support_count) {
          item.support_count = res.data.count;
        } else {
          item.support_count++;
        }
        item.is_support = 1;
      } else {
        if (!item.support_count) {
          item.support_count = res.data.count;
        } else {
          item.support_count--;
        }
        item.is_support = -1;
      }
    },
    handleReply(item, level = 1) {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.replyNickname = item.user.nickname;
      this.replyInfo = item;
      this.isReplying = true;
      this.replyInput = '';
      this.replyInfo.level = level;
    },
    async sendReply() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (this.replyInput.length < 1) {
        this.$toast('输入的内容不能为空');
        this.isReplying = false;
        return false;
      }
      if (this.replyInput.length > 100) {
        this.$toast('输入的内容不能超过100字符');
        this.isReplying = false;
        return false;
      }
      this.$toast.loading({
        message: this.$t('发送中...'),
      });

      let params = {
        sourceId: this.info.source_id,
        classId: 103,
        model: 'iPhone',
        content: this.formatReplyInput,
      };
      if (this.replyInfo.level == 1) {
        params.replyOuterId = this.replyInfo.comment_id;
      } else {
        params.replyCommentId = this.replyInfo.reply_outer_id;
        params.replyOuterId = this.replyInfo.comment_id;
      }

      if (platform == 'android') {
        params.model = this.modelName ? this.modelName : '安卓';
      }
      try {
        const res = await ApiCommentSubmit({ ...params });
        this.$toast(res.msg);
      } finally {
        this.isReplying = false;
      }
    },
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
      }
      try {
        const res = await ApiCommentReplies({
          page: this.page,
          listRows: this.listRows,
          classId: 103,
          sourceId: this.sourceId,
          commentId: this.id,
          order: this.order,
        });

        this.info = res.data;
        this.reply_count = res.data.reply_count;
        let list = this.info.replies ?? [];
        if (list.length < this.listRows) {
          this.finished = true;
        }
        this.list.push(...list);
        if (this.list.length == 0) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      } catch {
        this.$toast.clear();
        this.empty = true;
      }
    },
    async onRefresh() {
      await this.getList(2);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
    async changeOrder(order) {
      if (order == this.order) {
        return false;
      }
      this.order = order;
      this.finished = false;
      this.loadingObj.loading = true;
      await this.getList(2);
      this.loadingObj.loading = false;
    },
    toBeCode(codePoint) {
      let TEN_BITS = parseInt('1111111111', 2);
      if (codePoint <= 0xffff) {
        return this.u(codePoint);
      }
      codePoint -= 0x10000;
      // Shift right to get to most significant 10 bits
      let leadSurrogate = 0xd800 + (codePoint >> 10);
      // Mask to get least significant 10 bits
      let tailSurrogate = 0xdc00 + (codePoint & TEN_BITS);
      return this.u(leadSurrogate) + this.u(tailSurrogate);
    },
    u(codeUnit) {
      return '\\u' + codeUnit.toString(16).toUpperCase();
    },
  },
  computed: {
    formatReplyInput() {
      return this.replyInput.replace(
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
        emoji => {
          let utf16 = this.toBeCode(emoji.codePointAt(0));
          let str = utf16.split('\\u').join('').toLowerCase();
          return `[emoji:${str}]`;
        },
      );
    },
  },

  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
};
</script>

<style lang="less" scoped>
.list-page {
  height: 100vh;
  .main-comment {
    padding: 20 * @rem 18 * @rem;
  }
  .h8-bg {
    height: 8 * @rem;
    background: #f8f8f8;
  }
  .list-container {
    padding: 16 * @rem 18 * @rem 0;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        display: flex;
        align-items: center;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #222222;
        line-height: 20 * @rem;

        // &::before {
        //   content: '';
        //   display: block;
        //   width: 5 * @rem;
        //   height: 12 * @rem;
        //   border-radius: 16 * @rem;
        //   background-color: @themeColor;
        //   margin-right: 6 * @rem;
        // }
      }
    }
    .user-comment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20 * @rem 0 12 * @rem 0;
      .avatar {
        width: 36 * @rem;
        height: 36 * @rem;
        border-radius: 50%;
        overflow: hidden;
      }

      .user-click {
        flex: 1;
        height: 34 * @rem;
        line-height: 34 * @rem;
        background: #f9f9f9;
        border-radius: 30 * @rem;
        display: flex;
        margin-left: 12 * @rem;
        span {
          margin-left: 14 * @rem;
          font-weight: 400;
          font-size: 13 * @rem;
          color: #9a9a9a;
        }
      }
    }
    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 371 * @rem;
      background: #ffffff;
      .empty-img {
        width: 128 * @rem;
        height: 128 * @rem;
        margin-top: 30 * @rem;
      }
      .tips {
        margin-top: 16 * @rem;
        height: 18 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #666666;
        line-height: 18 * @rem;
        text-align: center;
      }
      .btn {
        margin-top: 16 * @rem;
        width: 96 * @rem;
        white-space: nowrap;
        height: 34 * @rem;
        line-height: 34 * @rem;
        border-radius: 4 * @rem;
        border: 1px solid #21b98a;
        font-weight: 600;
        font-size: 14 * @rem;
        color: #21b98a;
        text-align: center;
      }
    }
    .order {
      display: flex;
      align-items: center;

      span {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #666666;
        line-height: 15 * @rem;
        text-align: left;
        margin-right: 8 * @rem;
        padding-right: 8 * @rem;
        border-right: 1 * @rem solid #d9d9d9;

        &:last-of-type {
          margin-right: 0;
          padding-right: 0;
          border-right: none;
        }

        &.active {
          font-weight: 600;
          font-size: 12 * @rem;
          color: #111111;
        }
      }
    }
  }
  .bottom-container {
    flex-shrink: 0;
    width: 100%;
    height: calc(64 * @rem + @safeAreaBottom);
    height: calc(64 * @rem + @safeAreaBottomEnv);
    .bottom-fixed {
      box-sizing: border-box;
      background-color: #fff;
      position: fixed;
      bottom: 0;
      left: 0;
      .fixed-center;
      width: 100%;
      z-index: 2000;
      box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 18 * @rem;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .comment-content {
        width: 100%;
        height: 64 * @rem;
        background: #ffffff;
        box-shadow: 0 -2 * @rem 4 * @rem 0 rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .text {
          flex: 1;
          height: 36 * @rem;
          line-height: 36 * @rem;
          background: #f9f9f9;
          border-radius: 30 * @rem;
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #9a9a9a;
          padding: 0 0 0 14 * @rem;
          white-space: nowrap;
          overflow: hidden;
        }
        .operation {
          display: flex;
          align-items: center;
          padding-left: 16 * @rem;
          .like {
            display: flex;
            align-items: center;

            span {
              height: 18 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #666666;
              line-height: 18 * @rem;
            }

            .like-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/player-voice/like-icon1.png)
                no-repeat;
              background-size: 16 * @rem 16 * @rem;
              margin-right: 4 * @rem;
            }

            &.active {
              .like-icon {
                background-image: url(~@/assets/images/player-voice/like-icon-active1.png);
              }
              // span {
              //   color: #21b98a;
              // }
            }
          }
          .reply {
            display: flex;
            align-items: center;
            margin-left: 12 * @rem;

            span {
              height: 18 * @rem;
              font-weight: 400;
              font-size: 14 * @rem;
              color: #555555;
              line-height: 18 * @rem;
            }

            .reply-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/player-voice/reply-icon1.png)
                no-repeat;
              background-size: 16 * @rem 16 * @rem;
              margin-right: 4 * @rem;
            }
          }
        }
      }
    }
  }
  .comment-list {
    .comment-item1 {
      background-color: #fff;
      border-radius: 12 * @rem;

      margin-top: 16 * @rem;
      &:not(:last-child) {
        padding-bottom: 16 * @rem;
        border-bottom: 1px solid #f9f9f9;
      }

      // margin-bottom: 32 * @rem;
      .item {
        /deep/.user-avatar {
          width: 36 * @rem;
          height: 36 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
        /deep/.info {
          .name {
            font-weight: 400;
            font-size: 14 * @rem;
            color: #9a9a9a;
          }
        }
        /deep/.comment-content {
          padding-left: 42 * @rem;
        }
        // &:last-of-type {
        //   margin-bottom: 0;
        // }
      }

      .feedback {
        border: 1 * @rem solid #f4efe9;
        border-radius: 6 * @rem;
        background: #fcfaf9;
        padding: 10 * @rem;
        margin-left: 34 * @rem;
        margin-top: 15 * @rem;

        .kefu-info {
          display: flex;
          align-items: center;

          img {
            width: 28 * @rem;
            height: 28 * @rem;
            margin-right: 6 * @rem;
            border-radius: 50%;
          }

          .name {
            flex: 1;
            min-width: 0;
            height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            color: #111111;
            line-height: 15 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .feedback-content {
          width: 100%;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #93734d;
          line-height: 15 * @rem;
          text-align: left;
          margin-top: 16 * @rem;
        }
      }
    }
  }
  .reply-editor {
    width: 100%;
    height: 64 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12 * @rem 0 16 * @rem;
    box-sizing: border-box;
    .class1-bg {
      width: 100%;
      height: 36 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      width: 339 * @rem;
      background: #f9f9f9;

      border-radius: 30 * @rem;
      .input-container1 {
        flex: 1;
        height: 36 * @rem;
        line-height: 36 * @rem;
        color: #999;

        input {
          width: 100%;
          height: 100%;
          background-color: #f8f8f8;
          // border: 1 * @rem solid #f2f2f2;
          font-size: 14 * @rem;
          border-radius: 30 * @rem;
          padding: 0 12 * @rem;
          box-sizing: border-box;
        }
      }

      .send-btn1 {
        width: 120 * @rem;
        height: 38 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 38 * @rem;
        text-align: center;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        background: @themeBg;
        border-radius: 30 * @rem;
        margin-right: 4 * @rem;
        &.class1 {
          width: 53 * @rem;
          height: 28 * @rem;
          line-height: 28 * @rem;
          font-size: 12 * @rem;
        }
      }
    }
    .input-container {
      width: 212 * @rem;
      height: 38 * @rem;
      line-height: 38 * @rem;
      color: #999;

      input {
        width: 100%;
        height: 100%;
        background-color: #f8f8f8;
        border: 1 * @rem solid #f2f2f2;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
        padding: 0 12 * @rem;
        box-sizing: border-box;
      }
    }

    .send-btn {
      width: 120 * @rem;
      height: 38 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 38 * @rem;
      text-align: center;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      background: @themeBg;
      border-radius: 29 * @rem;
    }
  }
}
</style>
