<template>
  <div class="game-gift-page page">
    <nav-bar-2 :title="$t('礼包')" ref="topNavBar"></nav-bar-2>

    <div class="xiaohao-fixed-placeholder" ref="xiaohaoFixed">
      <div
        class="xiaohao-fixed"
        v-if="currentXiaohao.nickname"
        @click="changeXiaohao"
      >
        <div class="xiaohao-name">
          {{ $t('小号') }}：{{ currentXiaohao.nickname }}
        </div>
        <div class="change-xiaohao">
          {{ $t('切换') }}<span class="change-xiaohao-icon"></span>
        </div>
      </div>
      <div
        class="xiaohao-fixed"
        :class="{ 'not-xiaohao-bg': class_id == 115 }"
        v-else
        >{{ fixedText }}</div
      >
    </div>
    <div class="gift-container">
      <div class="gift-content">
        <content-empty v-if="empty"></content-empty>
        <load-more
          class="gift-bar"
          v-else
          v-model="loading"
          :finished="finished"
          @loadMore="loadMore()"
          :check="false"
        >
          <div class="gift-list-container">
            <van-tabs
              v-model="tabIndex"
              scrollspy
              sticky
              :offset-top="stickyOffsetTop"
              class="tabs"
              :swipe-threshold="1"
              :animated="false"
            >
              <template v-for="(gifts, index) in resultGiftList">
                <van-tab :key="index" v-if="gifts.length" class="tab">
                  <template #title>
                    <div class="tab-item">
                      {{ gifts[0].class_name }}
                    </div>
                  </template>
                  <div class="type-small-title">{{ gifts[0].class_name }}</div>
                  <div class="gift-list" v-if="gifts.length">
                    <div class="gift-item" v-for="item in gifts" :key="item.id">
                      <div
                        class="gift-info btn"
                        @click="
                          $router.push({
                            name: 'GiftDetail',
                            params: { gift_id: item.id, game_id: gameId },
                          })
                        "
                      >
                        <div class="gift-name">
                          {{ item.title }}
                        </div>
                        <div class="gift-desc">
                          {{ item.cardbody }}
                        </div>
                        <div class="gift-code" v-if="item.cardpass">
                          {{ $t('礼包码') }}: <span>{{ item.cardpass }}</span>
                        </div>
                        <div class="gift-left" v-else>
                          <div class="totals">
                            <div
                              class="left"
                              :style="{
                                width: `${item.remain}%`,
                              }"
                            ></div>
                          </div>
                          <i></i>
                          <div class="left-text">
                            {{ $t('剩余') }}<span>{{ item.remain }}</span
                            >%
                          </div>
                        </div>
                      </div>
                      <div
                        class="get btn"
                        :class="{ already: item.remain == 0 }"
                        @click.stop="getGift(item)"
                        v-if="!item.cardpass"
                      >
                        {{ item.remain != 0 ? $t('领取') : $t('已抢光') }}
                      </div>
                      <div class="get copy" @click.stop="copy(item)" v-else>
                        {{ $t('复制') }}
                      </div>
                    </div>
                  </div>
                </van-tab>
              </template>
            </van-tabs>
          </div>
        </load-more>
      </div>
    </div>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="false"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-text">礼包已领取，您的兑换码</div>
      </div>
      <div class="cardpass">{{ dialogInfo.cardpass }}</div>
      <div class="desc" v-html="introduction"></div>
      <div class="copy-btn btn" @click="copy(dialogInfo)">
        {{ $t('复制礼包码') }}
      </div>
      <div class="close-btn" @click="copyDialogShow = false"></div>
    </van-dialog>
    <!-- 复制成功弹窗 -->
    <cardpass-copy-success-popup
      :info="dialogInfo"
      :show.sync="copySuccessPopupShow"
    ></cardpass-copy-success-popup>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(gameId)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import { themeColorLess, remNumberLess } from '@/common/styles/_variable.less';
import { ApiCardIndex, ApiCardGet } from '@/api/views/gift.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';

import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
export default {
  game: 'GameGift',
  components: {
    xhCreateTipDialog,
  },
  data() {
    return {
      themeColorLess,
      remNumberLess,
      gameId: this.$route.params.game_id,
      stickyOffsetTop: '0*@rem', // 顶部导航栏高度
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      xiaohaoListShowItem: {},
      currentXiaohao: {}, //当前选择小号
      giftList: [],
      page: 1,
      listRows: 100, // 一次性把全部礼包返回，不然拿不到所有分类
      finished: false,
      loading: false,
      empty: false,
      dialogInfo: {},
      fixedText: '',
      tabIndex: 0, // 顶部导航索引
      class_id: this.$route.params.class_id,
      copySuccessPopupShow: false,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    resultGiftList() {
      return [
        this.svipGift,
        this.changwanGift,
        this.chargeGift,
        this.commonGift,
        this.internalGift,
      ];
    },
    commonGift() {
      return this.giftList.filter(item => {
        return ![22, 23, 24, 26].includes(Number(item.classid));
      });
    },
    chargeGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 22;
      });
    },
    svipGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 23;
      });
    },
    changwanGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 24;
      });
    },
    internalGift() {
      return this.giftList.filter(item => {
        return Number(item.classid) == 26;
      });
    },
    introduction() {
      return `${this.$t('使用说明')}：${this.dialogInfo.cardtext}`;
    },
  },
  async created() {
    if (Number(this.class_id) != 115) {
      await this.getCurrentXiaohaoId();
    }
    await this.getList();
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.$nextTick(() => {
      this.stickyOffsetTop =
        this.$refs.topNavBar.clientHeight +
        this.$refs.xiaohaoFixed.clientHeight +
        '*@rem';
    });
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    async getCurrentXiaohaoId() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.gameId,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
        // 判断是否有已选择小号
        let flag = list.some(item => {
          return item.id == this.xiaohaoMap[this.gameId]?.id;
        });
        if (flag) {
          this.currentXiaohao = this.xiaohaoMap[this.gameId];
        } else {
          this.currentXiaohao = list[0];
          this.setXiaohaoMap([this.gameId, list[0]]);
        }
      } else {
        this.xiaohaoList = [];
        this.currentXiaohao = {};
        this.setXiaohaoMap([this.gameId, {}]);
        if (text) this.fixedText = text;
      }
    },
    changeXiaohao() {
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    getGift(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (item.remain == 0) {
          this.$toast(this.$t('礼包已被抢光啦~'));
          return false;
        }
        if (!this.currentXiaohao.id && Number(this.class_id) != 115) {
          this.createDialogShow = true;
          return false;
        }
        this.$toast.loading({
          message: this.$t('加载中...'),
        });
        ApiCardGet({
          cardId: item.id,
          xhId: this.currentXiaohao.id,
        }).then(
          res => {
            this.$toast.clear();
            this.updateCardpass(item, res.data.cardpass);
            this.showCopyBar(res.data);

            // 神策埋点
            this.$sensorsTrack('game_rewards_claim', {
              game_id: `${item.game_id}`,
              adv_id: '暂无',
              game_name: item.titlegame,
              game_type: `${item.classid}`,
              game_size: '暂无',
              reward_type: item.title, // 传礼包名称
              data_source: this.$sensorsChainGet(),
            });
          },
          e => {
            this.$toast.clear();
            this.$dialog
              .confirm({
                title: '提示',
                message: e.msg,
                confirmButtonText: '打开游戏',
              })
              .then(() => {
                this.toPage('GameDetail', { id: item.game_id });
              });
          },
        );
      }
    },
    showCopyBar(info) {
      this.dialogInfo = info;
      this.copyDialogShow = true;
    },
    copy(info) {
      this.$copyText(info.cardpass).then(
        res => {
          this.dialogInfo = info;
          // this.$toast(this.$t('复制成功'));
          this.copySuccessPopupShow = true;
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiCardIndex({
        page: this.page,
        listRows: this.listRows,
        gameId: this.gameId,
        xhId: this.currentXiaohao.id || '',
      });
      if (action === 1 || this.page === 1) {
        this.giftList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.giftList.push(...res.data.list);
      this.loading = false;
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      if (!this.giftList.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }
      this.loading = false;
    },
    updateRemain(giftItem) {
      this.$set(this.giftList[this.findGiftIndex(giftItem)], 'remain', 0);
    },
    updateCardpass(giftItem, cardpass) {
      this.$set(
        this.giftList[this.findGiftIndex(giftItem)],
        'cardpass',
        cardpass,
      );
    },
    findGiftIndex(giftItem) {
      return this.giftList.findIndex(item => {
        return item.id == giftItem.id;
      });
    },
    getChargeGift(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        this.$toast.loading({
          message: this.$t('加载中...'),
          forbidClick: false,
        });
        ApiXiaohaoMyListByCard({
          cardId: item.id,
        }).then(
          res => {
            this.$toast.clear();
            this.xiaohaoList = res.data.list;
            this.currentXiaohao = this.xiaohaoList[0];
            this.xhDialogShow = true;
            this.currentGiftId = item.id;
          },
          () => {},
        );
      }
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.setXiaohaoMap([this.gameId, this.currentXiaohao]);
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getList();
      this.$toast.clear();
    },
  },
};
</script>

<style lang="less" scoped>
.game-gift-page {
  background-color: #f5f5f6;
  .pull-refresh {
    background-color: #f5f5f6;
  }
  .xiaohao-fixed-placeholder {
    background-color: #f5f5f6;
    position: relative;
    width: 100%;
    height: 30 * @rem;
    flex-shrink: 0;
  }
  .xiaohao-fixed {
    position: fixed;
    z-index: 100;
    .fixed-center;
    box-sizing: border-box;
    padding: 0 12 * @rem;
    width: 100%;
    height: 32 * @rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fee7db;
    font-size: 13 * @rem;
    color: @themeColor;
    &.not-xiaohao-bg {
      background-color: #f5f5f6;
    }
    .change-xiaohao {
      display: flex;
      align-items: center;
      .change-xiaohao-icon {
        margin-left: 4 * @rem;
        width: 12 * @rem;
        height: 12 * @rem;
        .image-bg('~@/assets/images/games/change-xiaohao-icon.png');
      }
    }
  }
  .dialog-title {
    box-sizing: border-box;
    height: 35 * @rem;
    display: flex;
    align-items: center;
    background-color: #f6f6f6;
    margin: 0 24 * @rem 14 * @rem;
    padding: 0 16 * @rem;
    .key {
      font-size: 14 * @rem;
      color: #666666;
    }
    .value {
      font-size: 14 * @rem;
      color: #ffbf13;
      margin-left: 12 * @rem;
    }
  }
  .gift-container {
    background-color: #f5f5f6;
    .gift-navs {
      display: flex;
      justify-content: center;
      padding: 20 * @rem 0;
      .gift-nav {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 90 * @rem;
        height: 30 * @rem;
        border-radius: 6 * @rem;
        background-color: #dcdcdc;
        font-size: 15 * @rem;
        color: #ffffff;
        margin: 0 10 * @rem;
        &.active {
          background: @themeColor;
        }
      }
    }

    .gift-content {
      .type-title {
        padding: 20 * @rem 0 10 * @rem;
        display: flex;
        .title-text {
          padding: 0 10 * @rem;
          height: 30 * @rem;
          font-size: 14 * @rem;
          color: #fff;
          background: @themeColor;
          border-radius: 0 * @rem 15 * @rem 15 * @rem 0 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .type-small-title {
        padding: 10 * @rem 18 * @rem 0;
        font-size: 14 * @rem;
        color: @themeColor;
      }
      .gift-bar {
        display: flex;
        flex-direction: column;
        .gift-list-container {
          .tabs {
            padding-top: 48 * @rem;
            /deep/ .van-sticky {
              width: 100%;
              position: fixed !important;
              z-index: 1000;
              top: calc(82 * @rem + @safeAreaTop) !important;
              top: calc(82 * @rem + @safeAreaTopEnv) !important;
            }
            /deep/ .van-sticky--fixed {
              top: calc(82 * @rem + @safeAreaTop) !important;
              top: calc(82 * @rem + @safeAreaTopEnv) !important;
            }
            /deep/ .van-tabs__wrap {
              height: 48 * @rem;
            }
            /deep/ .van-tabs__nav {
              background-color: #f5f5f6;
              padding: 0 18 * @rem;
            }
            /deep/ .van-tab {
              height: 48 * @rem;
              flex-shrink: 0;
              flex-grow: 0;
              padding: 0;
              &:not(:first-of-type) {
                margin-left: 10 * @rem;
              }
            }
            /deep/ .van-tab--active {
              .tab-item {
                background-color: @themeColor;
                color: #ffffff;
              }
            }
            .tab-item {
              height: 28 * @rem;
              background-color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0 15 * @rem;
              border-radius: 14 * @rem;
              font-size: 13 * @rem;
              color: #000000;
              white-space: nowrap;
            }
            /deep/ .van-tabs__line {
              display: none;
            }
          }
        }
      }
      .gift-list {
        .gift-item {
          box-sizing: border-box;
          padding: 15 * @rem 12 * @rem 10 * @rem 15 * @rem;
          display: flex;
          align-items: center;
          background-color: #fff;
          width: 339 * @rem;
          background: #ffffff;
          border-radius: 12 * @rem;
          margin: 10 * @rem auto 0;
          &:not(:first-of-type) {
            margin-top: 10 * @rem;
          }
          .gift-info {
            flex: 1;
            min-width: 0;
            .gift-name {
              font-size: 16 * @rem;
              color: #000000;
              font-weight: bold;
              display: flex;
              align-items: center;
              line-height: 22 * @rem;
              max-height: 44 * @rem;
              overflow: hidden;
            }
            .gift-code {
              font-size: 13 * @rem;
              color: #666666;
              padding-top: 10 * @rem;
              border-top: 0.5 * @rem solid #ebebeb;
              word-break: break-all;
              span {
                color: #ffbf13;
              }
            }
            .gift-desc {
              font-size: 13 * @rem;
              color: #757575;
              line-height: 18 * @rem;
              margin-top: 8 * @rem;
              height: 36 * @rem;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              margin-bottom: 12 * @rem;
            }
            .gift-left {
              display: flex;
              align-items: center;
              margin-top: 15 * @rem;
              .totals {
                width: 170 * @rem;
                height: 6 * @rem;
                border-radius: 3 * @rem;
                overflow: hidden;
                background: #ebebeb;
                .left {
                  width: 0%;
                  height: 100%;
                  background: #ff7554
                    linear-gradient(270deg, #fdba42 0%, #fe5b5b 100%);
                  border-radius: 3 * @rem;
                }
              }
              i {
                display: block;
                width: 18 * @rem;
                height: 18 * @rem;
                .image-bg('~@/assets/images/games/gift-title-icon.png');
                margin-left: 12 * @rem;
              }
              .left-text {
                font-size: 12 * @rem;
                color: #666666;
                margin-left: 8 * @rem;
                white-space: nowrap;
                span {
                  color: #ff4f4f;
                }
              }
            }
          }
          .get {
            width: 54 * @rem;
            height: 28 * @rem;
            border: 1 * @rem solid @themeColor;
            background-color: @themeColor;
            border-radius: 15 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            color: #fff;
            font-weight: 400;
            margin-left: 20 * @rem;
            &.already {
              background-color: #e4e4e4;
              color: #9a9a9a;
              border-color: #e4e4e4;
            }
            &.copy {
              background-color: #fff;
              color: @themeColor;
              border-color: @themeColor;
            }
          }
        }
      }
    }
  }
  /deep/ .van-button {
    border-radius: 16 * @rem;
  }
  .copy-dialog {
    box-sizing: border-box;
    width: 300 * @rem;
    border-radius: 12 * @rem;
    background-color: #fff;
    padding: 30 * @rem;
    overflow: unset;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;

      .title-text {
        line-height: 20 * @rem;
        font-size: 16 * @rem;
        color: #333333;
        font-weight: 600;
      }
    }
    .cardpass {
      box-sizing: border-box;
      width: 100%;
      height: 70 * @rem;
      background-color: #f5f5f5;
      border-radius: 8 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 0 10 * @rem;
      margin-top: 27 * @rem;
      font-size: 16 * @rem;
      line-height: 22 * @rem;
      color: #333333;
      font-weight: 600;
      word-break: break-all;
    }
    .desc {
      font-size: 11 * @rem;
      line-height: 15 * @rem;
      color: #777777;
      font-weight: 400;
      margin-top: 10 * @rem;
      text-align: center;
      word-break: break-all;
    }
    .copy-btn {
      width: 238 * @rem;
      height: 44 * @rem;
      margin: 21 * @rem auto 0;
      background: @themeBg;
      border-radius: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #ffffff;
    }
    .close-btn {
      width: 32 * @rem;
      height: 32 * @rem;
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translateX(-50%);
      z-index: 2;
      .image-bg('~@/assets/images/games/get-gift-popup-close-btn.png');
    }
  }
  .xh-dialog {
    width: 244 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 244 * @rem;
      height: 37 * @rem;
      .image-bg('~@/assets/images/games/dialog-logo.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      width: 244 * @rem;
      background-color: #fff;
      border-radius: 20 * @rem;
      margin-top: -4 * @rem;
      z-index: 2;
      padding: 16 * @rem 10 * @rem 19 * @rem;
      .title {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        text-align: center;
        line-height: 25 * @rem;
      }
      .center {
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 15 * @rem 0 0;
        padding: 0 18 * @rem;
        .left,
        .right {
          position: relative;
          line-height: 40 * @rem;
        }
        .left {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: 400;
        }
        .right {
          width: 133 * @rem;
          text-align: right;
          border-bottom: 0.5 * @rem solid #a6a6a6;
          .text {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            color: #000000;
            font-size: 13 * @rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            span {
              display: block;
              flex-shrink: 0;
            }
          }
          .more-text-icon {
            width: 10 * @rem;
            height: 6 * @rem;
            background: url(~@/assets/images/games/bottom-arrow.png) center
              center no-repeat;
            background-size: 10 * @rem 6 * @rem;
            margin-left: 6 * @rem;
            transition: 0.3s;
            &.on {
              transform: rotateZ(180deg);
            }
          }
        }
        .xiaohao-list {
          display: none;
          position: absolute;
          top: 40 * @rem;
          left: 0;
          z-index: 2000;
          width: 100%;
          max-height: 200 * @rem;
          overflow: auto;
          border-radius: 0 0 4 * @rem 4 * @rem;
          background: #fff;

          border: 1 * @rem solid #f2f2f2;
          &.on {
            display: block;
          }
          .xiaohao-item {
            box-sizing: border-box;
            text-align: center;
            line-height: 40 * @rem;
            text-align: right;
            padding: 0 15 * @rem;
            font-size: 13 * @rem;
            color: #000000;
            font-weight: 400;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &:not(:last-of-type) {
              border-bottom: 0.5 * @rem solid #f2f2f2;
            }
          }
        }
      }

      .dialog-bottom-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 18 * @rem;
        padding: 0 5 * @rem;
        .cancel {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #7d7d7d;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f2f2f2;
          border-radius: 18 * @rem;
        }
        .confirm {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #ffffff;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: @themeBg;
          border-radius: 18 * @rem;
        }
      }
    }
  }
}
</style>
