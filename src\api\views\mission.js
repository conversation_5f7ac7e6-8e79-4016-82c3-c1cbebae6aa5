import { request } from '../index';

/**
 * 获取成就任务数据
 *
 */
export function ApiMissionGetAchievement(params = {}) {
  return request('/api/mission/getAchievement', params);
}
//return
//  "data": {
//    "list": {
//      "pay": {//累积充值
//        "mission_info": {
//          "mission_rule_id": 23,
//          "mission_level": 6,
//          "need_value": 600,//达标充值数
//          "reward": 1500,//奖励金币
//          "is_finish": 0,//任务状态 0=未完成 1=已完成 未领取 2= 已完成 已领取
//          "all_finish": false//任务全部完成 ture=全部完成  false=没有全部完成
//        },
//        "total": 1191//累积充值数
//      },
//      "sign": {//累积签到
//        "mission_info": {
//          "mission_rule_id": 31,
//          "mission_level": 5,
//          "need_value": 90,
//          "reward": 100,
//          "is_finish": 0,
//          "all_finish": false
//        },
//        "total": 39
//      }
//    },
//    "isSvip": false，//是否为svip  true=是
//    "ptbUrl": "http://bbh5.a3733.com/h5/platcoin/index",//平台币链接
//    "vipUrl": "https://bbapi.a3733.com/h5/vip/index"//svip链接
//  }

/**
 * 领取成就任务
 * @param {mission} pay
 * @param {mission_level} 获取成就任务列表中的mission_level
 */
export function ApiMissionGetAchievementReward(params = {}) {
  return request('/api/mission/getAchievementReward', params);
}

/**
 * 获取每日任务数据
 */
export function ApiMissionGetDaily(params = {}) {
  return request('/api/mission/getDaily', params);
}

/**
 * 获取新手任务数据
 */
export function ApiMissionGetNewcomer(params = {}) {
  return request('/api/mission/getNewcomer', params);
}

/**
 * 领取新手任务奖励/每日任务奖励
 * @param {rule_id} 获取任务列表中的rule_id
 */
export function ApiMissionGetMissionReward(params = {}) {
  return request('/api/mission/getMissionReward', params);
}

/**
 * 新手任务抽奖券任务列表
 * @param {rule_id} 获取任务列表中的rule_id
 */
export function ApiMissionGetNewUserMission(params = {}) {
  return request('/api/mission/getNewUserMission', params);
}

/**
 * 领取新手任务抽奖券
 * @param {id} 获取任务列表中的id
 */
export function ApiMissionGetNewUserMissionCoupon(params = {}) {
  return request('/api/mission/getNewUserMissionCoupon', params);
}

/**
 * 新手任务抽奖
 *
 */
export function ApiMissionGetNewUserReward(params = {}) {
  return request('/api/mission/getNewUserReward', params);
}

/**
 * 新手任务抽奖日志
 *
 */
export function ApiMissionGetNewUserLog(params = {}) {
  return request('/api/mission/getNewUserLog', params);
}

// 确认人物是否做完
export function ApiMissionCheckToComplete(params = {}) {
  return request('/api/mission/checkToComplete', params);
}
