<template>
  <van-dialog
    v-model="popupShow"
    :close-on-click-overlay="false"
    message-align="left"
    :lock-scroll="false"
    class="gzh-popup"
    :show-confirm-button="false"
  >
    <div class="gzh-container">
      <div class="gzh-title">{{ popupTitle }}</div>
      <div class="swiper-container">
        <van-swipe
          v-if="popupShow"
          class="my-swipe"
          :autoplay="3000"
          indicator-color="#32B768"
          :loop="false"
        >
          <van-swipe-item v-for="(item, index) in picList" :key="index">
            <div class="pic-item">
              <img :src="item.src" alt="" />
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="bottom-btn" @click="goToWx" v-if="!isCheck">前往微信开启</div>
      <div class="bottom-operation" v-else>
        <div class="btn cancel" @click="confrim">是,我已经开启</div>
        <div class="btn confirm" @click="cancel">否,前往开启</div>
      </div>
    </div>
    <div class="close" @click="clickClose"></div>
  </van-dialog>
</template>

<script>
import { BOX_openWx } from '@/utils/box.uni.js';
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isCheck: false,
      picList: [
        {
          src: require('@/assets/images/kefu/notice-1.png'),
        },
        {
          src: require('@/assets/images/kefu/notice-2.png'),
        },
        {
          src: require('@/assets/images/kefu/notice-3.png'),
        },
      ],
    };
  },
  computed: {
    popupTitle() {
      if (this.isCheck) {
        return '确认是否已开启微信消息通知';
      } else {
        return '如何开启微信消息通知';
      }
    },
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
  methods: {
    goToWx() {
      BOX_openWx();
      setTimeout(() => {
        this.isCheck = true;
      }, 1000);
    },
    confrim() {
      this.isCheck = false;
      this.popupShow = false;
      localStorage.setItem('is_notice', 1);
      this.$toast('开启通知成功');
      this.$emit('success');
    },
    cancel() {
      this.goToWx();
    },
    clickClose() {
      this.popupShow = false;
      this.isCheck = false;
    },
  },
};
</script>

<style lang="less" scoped>
.gzh-popup {
  width: 320 * @rem;
  background: transparent;
  .gzh-container {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16 * @rem;
    width: 320 * @rem;
    padding: 28 * @rem 20 * @rem;
    .gzh-title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
      padding-bottom: 6 * @rem;
    }
    .swiper-container {
      width: 280 * @rem;
      height: 330 * @rem;
      margin-top: 20 * @rem;
      .pic-item {
        width: 280 * @rem;
        height: 330 * @rem;
      }
    }
    .bottom-btn {
      width: 205 * @rem;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
      font-size: 15 * @rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20 * @rem auto 0;
    }
    .bottom-operation {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20 * @rem auto 0;
      .cancel {
        box-sizing: border-box;
        width: 135 * @rem;
        height: 36 * @rem;
        border-radius: 18 * @rem;
        border: 1 * @rem solid #33b869;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        color: #33b869;
      }
      .confirm {
        box-sizing: border-box;
        width: 135 * @rem;
        height: 36 * @rem;
        border-radius: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        color: #ffffff;
        background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
      }
    }
  }
  .close {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/close.png) center center no-repeat;
    background-size: 28 * @rem 28 * @rem;
    margin: 28 * @rem auto 0;
  }
}
</style>
