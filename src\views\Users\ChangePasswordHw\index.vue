<template>
  <div class="page email-page">
    <nav-bar-2 :placeholder="false" bgStyle="transparent">
      <!-- <template v-slot:left><div class="back" @click="back2"></div></template> -->
    </nav-bar-2>
    <template v-if="step == 0">
      <div class="top-banner">
        <div class="page-title">找回密码</div>
      </div>
      <form class="form">
        <!-- 账号 -->
        <div class="field">
          <div class="field-icon user-icon"></div>
          <input
            type="text"
            v-model="account"
            placeholder="请输入用户名/手机号/邮箱"
          />
        </div>
      </form>
      <div class="button" :class="{ on: account }" @click="next()">下一步</div>
    </template>
    <template v-if="step == 1">
      <div class="top-banner">
        <div class="page-title">重置密码</div>
      </div>
      <form class="form">
        <!-- 邮箱 -->
        <div class="field">
          <div class="field-icon email-icon"></div>
          <input type="text" v-model="email" placeholder="请输入邮箱" />
          <div v-if="email != ''" class="clear" @click="email = ''"></div>
        </div>
        <!-- 验证码 -->
        <div class="field">
          <div class="field-icon authcode-icon"></div>
          <input type="text" v-model="authCode" placeholder="输入验证码" />
          <div class="right">
            <div
              class="code-btn"
              :class="{ disabled: !email }"
              v-if="!ifCount"
              @click="getAuthCode()"
            >
              {{ $t('获取验证码') }}
            </div>
            <div class="code-btn disabled" v-else>
              {{ `${$t('重新获取')}${countdown}s` }}
            </div>
          </div>
        </div>
        <!-- 密码 -->
        <div class="field">
          <div class="field-icon lock-icon"></div>
          <input
            :type="eyeOpen ? 'text' : 'password'"
            v-model="password"
            placeholder="请输入新密码"
          />
          <div class="right">
            <div
              v-if="password.length > 0"
              class="eyes"
              :class="{ open: eyeOpen }"
              @click="clickEyeBtn()"
            ></div>
          </div>
        </div>
        <!-- 重复密码 -->
        <div class="field">
          <div class="field-icon lock-icon"></div>
          <input
            :type="eyeOpen2 ? 'text' : 'password'"
            v-model="password2"
            placeholder="请再次输入新密码"
          />
          <div class="right">
            <div
              v-if="password2.length > 0"
              class="eyes"
              :class="{ open: eyeOpen2 }"
              @click="clickEyeBtn2()"
            ></div>
          </div>
        </div>
      </form>
      <div class="button" :class="{ on: canSubmit }" @click="commit()">
        提交
      </div></template
    >
    <template v-if="step == 2">
      <div class="top-banner">
        <div class="page-title">重置密码</div>
      </div>
      <div class="kefu-text first">
        为了保障用户的账号安全，您当前需要找回密码的账号安全性比较低，需要通过人工客服处理。
      </div>
      <div class="kefu-text">请联系在线客服，进行处理~</div>
      <div class="button on" @click="openKefu()">在线客服</div>
    </template>
  </div>
</template>

<script>
import {
  ApiMailSend,
  ApiResetPassword,
  ApiUserCheckAccount,
} from '@/api/views/users';

import { mapGetters, mapMutations, mapActions } from 'vuex';

import { isWebApp } from '@/utils/userAgent.js';

import { BOX_login } from '@/utils/box.uni.js';

export default {
  name: 'Login',
  data() {
    return {
      isWebApp,
      email: '',
      authCode: '',
      password: '',
      password2: '',
      countdown: 60,
      ifCount: false,
      eyeOpen: false, //是否显示密码
      eyeOpen2: false, //是否显示密码
      step: 0, //0检测账号 1邮箱重置 2联系人工客服
      account: '', //输入的账号
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfoEx: 'user/userInfoEx',
    }),
    canSubmit() {
      return this.email && this.authCode && this.password && this.password2;
    },
  },
  beforeRouteLeave(to, from, next) {
    if (!this.userInfo.email) {
      this.setUserInfo({});
      this.setUserInfoEx({});

      localStorage.setItem('STORE', JSON.stringify(this.$store.state));
      next();
      return false;
    }
    next();
  },
  methods: {
    ...mapMutations({
      setUserInfo: 'user/setUserInfo',
      setUserInfoEx: 'user/setUserInfoEx',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    // 获取邮箱验证码
    getAuthCode() {
      if (this.email === '') {
        this.$toast('请输入邮箱');
        return false;
      }
      // 发送axios请求
      let params = {
        email: this.email,
        type: 4,
      };
      ApiMailSend(params).then(
        res => {
          this.$toast(res.msg);
          // 出现倒计时，颜色变暗
          this.ifCount = !this.ifCount;
          let fun = setInterval(() => {
            this.countdown--;
            if (this.countdown === -1) {
              clearInterval(fun);
              this.countdown = 60;
              this.ifCount = !this.ifCount;
            }
          }, 1000);
        },
        err => {},
      );
    },
    goToLoginHw() {
      this.toPage('LoginHw');
    },
    clickEyeBtn() {
      this.eyeOpen = !this.eyeOpen;
    },
    clickEyeBtn2() {
      this.eyeOpen2 = !this.eyeOpen2;
    },
    async next() {
      if (!this.account) {
        this.$toast('请输入用户名/手机号/邮箱!');
        return false;
      }
      const res = await ApiUserCheckAccount({ account: this.account });
      if (res.data.resetPwd == 1) {
        this.step = 1;
        this.email = res.data.email;
      } else {
        this.step = 2;
      }
    },
    commit() {
      if (this.email === '') {
        this.$toast('请输入邮箱!');
        return false;
      }
      if (this.password === '') {
        this.$toast('请输入新密码!');
        return false;
      }
      if (this.password !== this.password2) {
        this.$toast('两次密码输入不一致');
        return false;
      }
      const toast1 = this.$toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      let params = {
        email: this.email,
        password: this.password,
        code: this.authCode,
      };
      ApiResetPassword(params).then(async res => {
        if (res.code > 0) {
          if (this.isHw) {
            // 海外的重置密码后要跳转LoginHw 2023年12月12日19:25:04
            this.$router.replace({ name: 'LoginHw' });
          } else {
            this.$router.replace({ name: 'Login' });
          }
        }
      });
    },
    // back2() {
    //   if(this.step !=0) {
    //     this.step = 0;
    //   } else {
    //     this.back();
    //   }
    // }
  },
};
</script>

<style lang="less" scoped>
.email-page {
  background-color: #f6f7f8;
  .back {
    width: 30 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/nav-bar-back-black.png) center center
      no-repeat;
    background-size: 10 * @rem 18 * @rem;
  }
  .top-banner {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 160 * @rem;
    overflow: hidden;
    .page-title {
      font-size: 28 * @rem;
      color: #28292c;
      font-weight: 600;
      line-height: 40 * @rem;
      margin-top: 120 * @rem;
      text-align: center;
    }
  }
  .form {
    padding: 0 16 * @rem;
    margin-top: 66 * @rem;
    .field {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100%;
      height: 50 * @rem;
      position: relative;
      background: #ffffff;
      padding: 0 16 * @rem;
      border-radius: 25 * @rem;
      &:not(:first-of-type) {
        margin-top: 20 * @rem;
      }
      .field-icon {
        width: 24 * @rem;
        height: 24 * @rem;
        background-size: 24 * @rem 24 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
        &.email-icon {
          background-image: url('~@/assets/images/users/login-email-icon.png');
        }
        &.user-icon {
          background-image: url('~@/assets/images/users/login-user-icon.png');
        }
        &.lock-icon {
          background-image: url('~@/assets/images/users/login-lock-icon.png');
        }
        &.email-icon {
          background-image: url('~@/assets/images/users/login-email-icon.png');
        }
        &.authcode-icon {
          background-image: url('~@/assets/images/users/login-verify-icon.png');
        }
      }
      input {
        flex: 1;
        min-width: 0;
        height: 100%;
        line-height: 50 * @rem;
        font-size: 14 * @rem;
        letter-spacing: 1 * @rem;
        padding: 0 16 * @rem;
      }
      .right {
        height: 100%;
        display: flex;
        align-items: center;
        .text {
          font-size: 14 * @rem;
          text-align: right;
          color: @themeColor;
        }
        .eyes {
          width: 18 * @rem;
          height: 50 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.open {
            height: 12 * @rem;
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
        .code-btn {
          width: 100 * @rem;
          height: 32 * @rem;
          border-radius: 16 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12 * @rem;
          background: @themeColor;
          margin-right: -7 * @rem;
          &.disabled {
            opacity: 0.5;
          }
        }
      }
      .clear {
        width: 16 * @rem;
        height: 50 * @rem;
        background-image: url(~@/assets/images/users/keyword-clear.png);
        background-size: 14 * @rem 14 * @rem;
        background-repeat: no-repeat;
        background-position: center center;
      }
      .down-arrow {
        width: 15 * @rem;
        height: 50 * @rem;
        background: url(~@/assets/images/users/arrow-down.png) center center
          no-repeat;
        background-size: 15 * @rem 6 * @rem;
        margin-left: 10 * @rem;
        &.show {
          transform: rotateX(180deg);
        }
      }
    }
  }
  .kefu-text {
    font-size: 15 * @rem;
    color: #333;
    text-indent: 2em;
    line-height: 1.4;
    padding: 0 20 * @rem;
    &.first {
      margin-top: 30 * @rem;
    }
  }
  .button {
    height: 44 * @rem;
    margin: 52 * @rem 16 * @rem 0;
    text-align: center;
    line-height: 44 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: bold;
    opacity: 0.5;
    transition: opacity 0.2s;
    &.on {
      opacity: 1;
    }
  }
  .explain {
    box-sizing: border-box;
    padding: 0 16 * @rem;
    display: flex;
    align-items: center;
    color: #323233;
    font-size: 12 * @rem;
    margin-top: 10 * @rem;
    height: 20 * @rem;
    line-height: 20 * @rem;
    input[type='checkbox'] {
      width: 14 * @rem;
      height: 14 * @rem;
      margin-right: 6 * @rem;
      box-sizing: border-box;
      background-color: #fff;
      appearance: none;
      border: 1 * @rem solid #c9c9c9;
      border-radius: 2 * @rem;
      outline: none;
      border-radius: 50%;
      margin-top: -1 * @rem;
    }
    input[type='checkbox']:checked {
      background: url('~@/assets/images/gou-yes.png') no-repeat center center;
      background-size: 14 * @rem auto;
      border: none;
    }
    .link {
      color: @themeColor;
    }
  }
}
</style>
