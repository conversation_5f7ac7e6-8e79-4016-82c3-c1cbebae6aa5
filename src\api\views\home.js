import {
  request
} from '../index';

export function ApiIndex(params = {}) {
  return request('/web/index/index', params);
}

/**
 * 畅玩版 - 畅玩版栏目控制
 */
export function ApiIndexGetShowList(params = {}) {
  return request('/cwb/index/getShowList', params);
}

/**
 * 获取顶部标签栏
 */
export function ApiIndexGetTopTab(params = {}) {
  return request('/v2024/index/getTopTab', params);
}

/**
 * 首页 - 额外列表
 */
export function ApiIndexExtraIndex(params = {}) {
  return request('/web/index/extraIndex', params);
}

/**
 * 首页 - 碎片管理列表
 */
export function ApiIndexSpIndex(params = {}) {
  return request('/web/index/spIndex', params);
}

/**
 * 首页 - 碎片管理列表v2
 * @param type 类型 1 = 人气推荐 2 = 精品推荐 3=本周热门 4=近期新游 5= 热门活动
 */
export function ApiIndexSpIndexV2(params = {}) {
  return request('/web/index/spIndexV2', params);
}

/**
 * 首页 - 碎片管理列表v3
 * @param type 类型 1 = 人气推荐 2 = 精品推荐 3=本周热门 4=近期新游 5= 热门活动 6=角色交易 7=玩家推荐  8=下载落地页 9=baner碎片  60=0.1折新游预告 61=近期新游
 */
export function ApiIndexSpIndexV3(params = {}) {
  return request('/web/index/spIndexV3', params);
}

/**
 * 首页 - 碎片列表
 * @param type 类型 1 = 人气推荐 2 = 精品推荐 3=本周热门 4=近期新游 5= 热门活动 6=角色交易 7=玩家推荐  8=下载落地页 9=baner碎片  60=0.1折新游预告 61=近期新游
 */
export function ApiIndexGetTopic(params = {}) {
  return request('/v2024/index/getTopic', params);
}

/**
 * banner点击统计
 * @param id banner列表中的id
 */
export function ApiStatisticsBanner(params = {}) {
  return request('/api/Statistics/banner', params);
}

/**
 * 游戏落地页
 * @param id
 */
export function ApiIndexGetDownActivityInfo(params = {}) {
  return request('/api/index/getDownActivityInfo', params);
}

/**
 * 游戏落地页领取
 * @param id id为奖品id
 * @param xh_id 小号id
 */
export function ApiIndexReceiveRrize(params = {}) {
  return request('/api/index/receiveRrize', params);
}

/**
 * 首页弹窗广告+推送代金券
 */
export function ApiIndexAdAndCoupon(params = {}) {
  return request('/api/index/adAndCoupon', params);
}


/**
 * 首页新游列表
 */
export function ApiCwbIndexGameList(params = {}) {
  return request('/cwb/index/gameList', params);
}

/**
 * 首页新游列表
 */
export function ApiCwbIndexGameListNew(params = {}) {
  return request('/cwb/index/gameListNew', params);
}


/**
 * 首页新游弹窗列表
 * @params type 1=近期新游 2=即将上线 3=最新开服
 */
export function ApiCwbIndexGameScreenList(params = {}) {
  return request('/cwb/index/gameScreenList', params);
}