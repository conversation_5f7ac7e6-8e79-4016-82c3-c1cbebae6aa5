<template>
  <div class="upgrade-item" v-if="show">
    <div class="icon">
      <img :src="info.titlepic" alt="" />
    </div>
    <div class="upgrade-info">
      <div class="title">{{ info.update_title }}</div>
      <div class="line">
        <div class="version"
          >{{ info.version }}.{{ info.version_code }}版本</div
        >
        <div class="size">{{ info.size_a }}M</div>
      </div>
      <div class="desc">{{ info.update_msg }}</div>
    </div>
    <div class="upgrade-btn" @click="handleUpgrade">升级</div>
    <div class="close" @click="show = false"></div>
  </div>
</template>

<script>
import { platform, BOX_openInBrowser } from '@/utils/box.uni.js';
import { ApiUpdateMjbCheck } from '@/api/views/system.js';
import { downloadGame } from '@/utils/function.js';
export default {
  data() {
    return {
      show: false,
      info: {},
    };
  },
  async created() {
    if (platform === 'androidBox') {
      await this.getInfo();
    }
  },
  methods: {
    async getInfo() {
      const res = await ApiUpdateMjbCheck();
      this.info = res.data.data;
      if (this.info.down_a) {
        setTimeout(() => {
          this.show = true;
        }, 500);
      }
    },
    handleUpgrade() {
      BOX_openInBrowser(
        { h5_url: this.info.down_a },
        {
          url: this.info.down_a,
        },
      );
      //下载不了，因为安卓判断了已经下载过的不能再下载
      // downloadGame(this.info, false);
    },
  },
};
</script>

<style lang="less" scoped>
.upgrade-item {
  box-sizing: border-box;
  width: 339 * @rem;
  height: 80 * @rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16 * @rem auto 10 * @rem;
  box-shadow: 0 * @rem 0 * @rem 8 * @rem 0 * @rem rgba(0, 13, 33, 0.08);
  border-radius: 6 * @rem;
  background: #fff;
  position: relative;
  display: flex;
  align-items: center;
  padding: 8 * @rem 12 * @rem;
  .close {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 26 * @rem;
    height: 26 * @rem;
    background: url(~@/assets/images/upgrade-close.png) no-repeat;
    background-size: 26 * @rem 26 * @rem;
  }
  .upgrade-btn {
    position: absolute;
    right: 12 * @rem;
    top: 18 * @rem;
    width: 68 * @rem;
    height: 28 * @rem;
    border-radius: 14 * @rem;
    background: #ff9029;
    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 14 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
  .icon {
    width: 64 * @rem;
    height: 64 * @rem;
    border-radius: 8 * @rem;
    overflow: hidden;
  }
  .upgrade-info {
    flex: 1;
    min-width: 0;
    margin-left: 10 * @rem;
    .title {
      font-size: 15 * @rem;
      font-weight: 600;
      color: #333333;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-right: 68 * @rem;
    }
    .line {
      display: flex;
      align-items: center;
      height: 17 * @rem;
      margin-top: 7 * @rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-right: 68 * @rem;
      .version {
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 3 * @rem;
        font-size: 11 * @rem;
        color: #22a03c;
        background: rgba(52, 191, 80, 0.12);
        text-align: center;
        padding: 0 3 * @rem;
      }
      .size {
        font-size: 11 * @rem;
        color: #666666;
        margin-left: 11 * @rem;
      }
    }
    .desc {
      margin-top: 6 * @rem;
      font-size: 11 * @rem;
      color: #999999;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-right: 20 * @rem;
    }
  }
}
</style>
