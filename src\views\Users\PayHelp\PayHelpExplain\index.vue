<template>
  <div class="explain-page">
    <nav-bar-2
      :title="$t('财富说明')"
      :border="true"
      :azShow="false"
    ></nav-bar-2>
    <section class="section">
      <div class="title">{{ $t('财富等级说明') }}</div>
      <div class="tips">衰退值=现有财富值*5%</div>
      <div class="table">
        <div class="row">
          <div class="th">{{ $t('财富等级') }}</div>
          <div class="th">{{ $t('财富名称') }}</div>
          <div class="th">{{ $t('财富值') }}</div>
          <div class="th">额外金币抵扣比例</div>
        </div>
        <div class="row" v-for="(item, index) in payList" :key="index">
          <div class="td">{{ item.level_id }}</div>
          <div class="td">{{ item.level_name }}</div>
          <div class="td">{{ item.require_pay }}</div>
          <div class="td">{{ item.gold_discount }}</div>
        </div>
      </div>
    </section>
    <section class="section-container">
      <div class="section-title">财富值说明</div>
      <div class="section-content">
        财富值对应您在游戏平台付费行为得到的数值。当您的财富值积累到某一个等级时，即可享受该等级对应的特权。
      </div>
    </section>
    <section class="section-container">
      <div class="section-title">财富值升级与衰退</div>
      <div class="section-content">
        付费行为会自动增加财富值，满足条件自动升级，无须领取。3733游戏平台为了维持特权服务的整体水平和协调性，针对连续30天内无任何付费行为的账号，系统将会按照现有财富值的5%作为衰退值扣减，当衰退值小于1时，则直接扣除1。SVIP账号拥有不衰退财富值的特权
        <span
          >（系统每天不定期检测所有账号历史XX天付费行为，针对不满足条件者进行扣减，付费行为要满足“单笔订单实际支付金额”≥10元，代金券、金币、平台币抵扣的不计算在内）</span
        >
      </div>
    </section>
    <section class="section-container">
      <div class="section-title">金币抵扣比例说明</div>
      <div class="section-content">
        达到表中对应的财富等级，且为SVIP的用户，会相应提升金币抵扣比例，财富值越高，额外获取的金币抵扣比例越大。由于游戏厂商的要求，部分游戏内充值暂不支持使用金币抵扣，请先确定需要用金币充值的游戏在不在可使用名单内。<span
          class="read-game"
          @click="gameDialogShow = true"
          >查看名单&gt;</span
        >
      </div>
    </section>
    <gold-game-dialog :show.sync="gameDialogShow"></gold-game-dialog>
  </div>
</template>

<script>
import { ApiUserPayRankV1 } from '@/api/views/users';
import { platform } from '@/utils/box.uni.js';
export default {
  data() {
    return {
      payList: [],
      gameDialogShow: false, // 是否显示非金币抵扣游戏
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = '财富说明';
    }
    const res = await ApiUserPayRankV1();
    this.payList = res.data.pay_rank;
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.explain-page {
  padding-bottom: 30 * @rem;
  .section {
    padding: 19 * @rem 18 * @rem 7 * @rem;
    background: #ffffff;
    .title {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: bold;
      line-height: 25 * @rem;
    }
    .tips {
      font-size: 14 * @rem;
      line-height: 16 * @rem;
      margin-top: 6 * @rem;
      color: #b47c4a;
    }
    .table {
      box-sizing: border-box;
      width: 100%;
      margin-top: 20 * @rem;
      border-radius: 12 * @rem;
      border: 0.5 * @rem solid #cba597;
      overflow: hidden;
      .row {
        display: flex;
        &:nth-of-type(n + 2) {
          border-top: 0.5 * @rem solid #cba597;
        }
        &:nth-of-type(2n + 1) {
          background-color: #f5f5f6;
        }
      }
      .th,
      .td {
        width: 50%;
        text-align: center;
        font-size: 12 * @rem;
        color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 11 * @rem 5 * @rem;
        &:nth-of-type(n + 2) {
          border-left: 0.5 * @rem solid #cba597;
        }
      }
      .th {
        background-color: #f5e2ce;
        font-size: 14 * @rem;
        color: #7a532a;
        font-weight: normal;
      }
    }
  }
  .section-container {
    padding: 0 18 * @rem;
    .section-title {
      font-size: 16 * @rem;
      line-height: 22 * @rem;
      color: #333333;
      font-weight: bold;
      margin-top: 14 * @rem;
    }
    .section-content {
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      color: #666666;
      margin-top: 6 * @rem;
      span {
        color: @themeColor;
        &.read-game {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
