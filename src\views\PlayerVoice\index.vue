<template>
  <div class="list-page">
    <nav-bar-2 title="玩家之声" :azShow="true"></nav-bar-2>
    <pull-refresh v-model="pageReloading" @refresh="onRefresh">
      <div class="top-banner">
        <van-swipe
          :autoplay="5000"
          indicator-color="white"
          v-if="bannerList.length"
        >
          <van-swipe-item
            class="swiper-slide"
            v-for="(banner, index) in bannerList"
            :key="index"
          >
            <img class="banner" :src="banner" />
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="bolang"></div>
      <div class="container special">
        <div class="title">
          <span>本期精选建议</span>
        </div>
        <div class="tab-nav">
          <div class="tab-list">
            <div
              class="tab"
              :class="{ active: tab == item.id }"
              v-for="(item, index) in tabList"
              :key="index"
              @click="tabChange(index)"
              >{{ item.title }}</div
            >
          </div>
          <div class="order" @click="isSelectPopupShow = true">
            <span>排序方式</span>
            <span class="icon"></span>
          </div>
        </div>
        <yy-list
          class="list-container"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :isPullRefresh="true"
          :empty="empty"
          :check="false"
          tips="暂无数据"
        >
          <div class="comment-list">
            <CommentItem
              class="item"
              :comment="item"
              v-for="(item, index) in list"
              :key="index"
            ></CommentItem>
          </div>
        </yy-list>
      </div>
    </pull-refresh>
    <div class="container" v-if="pastActivities.title">
      <div class="title">
        <span>{{ pastActivities.title }}</span>
      </div>
      <div class="active-list">
        <div
          class="item"
          v-for="(item, index) in pastActivities.list"
          :key="index"
          @click="activeClick(item)"
        >
          <img class="img" :src="item.icon" alt="" />
          <div class="info">
            <div class="name">{{ item.title }}</div>
            <div class="desc">{{ item.subtitle }}</div>
          </div>
          <div class="right-icon"></div>
        </div>
      </div>
    </div>
    <div class="bottom-fixed">
      <div class="btns">
        <div
          class="share-btn btn"
          :class="{ disabled: overdue || !shareUrl }"
          @click="share"
          ><span>分享</span>
        </div>
        <div class="add-btn btn" :class="{ disabled: overdue }" @click="toAdd"
          ><span>参与评论</span>
        </div>
      </div>
    </div>
    <van-popup
      class="selectPopup"
      v-model="isSelectPopupShow"
      :lock-scroll="false"
      round
      position="bottom"
      @closed="popupClose"
    >
      <div class="select-content">
        <div
          class="select-list"
          v-for="(select, index) in selectList"
          :key="index"
        >
          <span>{{ select.title }}</span>
          <div class="list">
            <div
              class="item"
              :class="{ active: item.value == selected[index] }"
              v-for="(item, itemIndex) in select.list"
              :key="itemIndex"
              @click="changeSelect(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="btns">
          <div class="cancel-btn btn" @click="isSelectPopupShow = false"
            >取消</div
          >
          <div class="submit-btn btn" @click="handleSelectSubmit">确定</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiPlayerVoiceIndex,
  ApiCommonShareInfo,
} from '@/api/views/playerVoice';
import { ApiCommentComments } from '@/api/views/comment';
import CommentItem from './components/comment-item';
import { platform } from '@/utils/box.uni.js';
import PullRefresh from '@/components/pull-refresh';
export default {
  name: 'PastList',
  components: {
    PullRefresh,
  },
  data() {
    return {
      id: 0,
      bannerList: [],
      list: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      pageReloading: false,
      userId: 0,
      tabList: [
        { id: 0, title: '全部' },
        { id: 1, title: '热门' },
        { id: 2, title: '图文' },
        { id: 3, title: '只看自己' },
      ],
      equipment: '',
      order: 0,
      tab: 0,
      type: 0,
      finished: true,
      empty: false,
      page: 1,
      listRows: 10,
      pastActivities: {},
      overdue: true, //活动是否过期
      isSelectPopupShow: false,
      selectList: [
        {
          title: '排序方式',
          list: [
            {
              name: '默认排序',
              value: 0,
            },
            {
              name: '最新评论',
              value: 2,
            },
            {
              name: '最新回复',
              value: 12,
            },
          ],
        },
        {
          title: '设备',
          list: [
            {
              name: '全部',
              value: 0,
            },
            {
              name: '相同机型',
              value: 'iPhone',
            },
          ],
        },
      ],
      selected: [0, 0],
      shareUrl: '',
    };
  },
  components: {
    CommentItem,
  },
  async created() {
    this.$route.meta.keepAlive = true;
    this.id = parseInt(this.$route.params.id) || 0;
    await this.getBanner();
    await this.getShareUrl();
  },
  methods: {
    async getBanner() {
      const res = await ApiPlayerVoiceIndex({
        id: this.id,
      });
      this.finished = false;
      this.bannerList = [...res.data.item.banner_img];
      this.pastActivities = res.data.past_activities;
      this.id = res.data.item.id;
      if (
        res.data.item.end_time * 1000 > new Date().getTime() &&
        res.data.item.start_time * 1000 < new Date().getTime()
      ) {
        this.overdue = false;
      }
      this.loadingObj.loading = true;
      await this.getList(2);
      this.loadingObj.loading = false;
    },
    async tabChange(index) {
      if (this.loadingObj.loading || this.tab == index) {
        return false;
      }
      this.tab = index;
      if (this.tab == 3 && !this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      this.loadingObj.loading = true;
      await this.getList(2);
      this.loadingObj.loading = false;
    },
    async getList(action = 1) {
      if (action == 1) {
        this.page++;
      } else {
        this.page = 1;
        this.list = [];
        this.finished = false;
      }
      let params = {
        page: this.page,
        listRows: this.listRows,
        classId: 106,
        sourceId: this.id,
        order: this.order,
        equipment: this.equipment,
      };
      if (platform == 'android' && params.equipment == 'iPhone') {
        params.equipment = this.modelName ? this.modelName : '安卓';
      }
      if (this.tab == 3) {
        params.user_id = this.userInfo.user_id;
      } else {
        params.type = this.tab;
      }
      const res = await ApiCommentComments(params);
      let list = [];
      if (res.data.tops) {
        list.push(...res.data.tops);
      }
      if (res.data.hots) {
        list.push(...res.data.hots);
      }
      list.push(...res.data.comments);
      if (list.length < this.listRows) {
        this.finished = true;
      }
      this.list.push(...list);
      if (this.list.length == 0) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      this.loadingObj.loading = false;
    },
    async onRefresh() {
      await this.getList(2);
      this.pageReloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
    },
    activeClick(item) {
      if (item.type == 1) {
        this.toPage('PlayerVoiceDetail', { id: this.id, sourceId: this.id });
      } else if (item.type == 2) {
        this.toPage('PastList');
      }
    },
    async getShareUrl() {
      let res = await ApiCommonShareInfo({
        type: 6,
        id: this.id,
      });
      this.shareUrl = res.data.url;
    },
    async share() {
      if (this.overdue || !this.shareUrl) {
        return false;
      }
      if (platform == 'android') {
        window.BOX.mobShare(6, this.id);
        return false;
      }

      this.$copyText(this.shareUrl).then(
        res => {
          this.$toast('链接已复制到剪贴板，快去分享给好友吧');
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请稍后重试'),
            lockScroll: false,
          });
        },
      );
    },
    toAdd() {
      if (this.overdue) {
        return false;
      }
      this.toPage('AddComment', { id: this.id });
    },
    changeSelect(item, index) {
      this.$set(this.selected, index, item.value);
    },
    popupClose() {
      this.selected = [this.order, this.equipment];
    },
    async handleSelectSubmit() {
      this.order = this.selected[0];
      this.equipment = this.selected[1];
      this.isSelectPopupShow = false;
      this.loadingObj.loading = true;
      await this.getList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.list-page {
  padding-bottom: 60 * @rem;
  .top-banner {
    width: 100%;
    position: relative;

    /deep/ .van-swipe__indicators {
      padding: 5 * @rem 10 * @rem;
      border-radius: 20 * @rem;
      background-color: rgba(0, 0, 0, 0.15);
      position: absolute;
      bottom: 12 * @rem;
      left: 50%;
      transform: translateX(-50%);

      .van-swipe__indicator {
        width: 6 * @rem;
        height: 4 * @rem;
        background: #ffffff;
        border-radius: 4 * @rem;
        opacity: 0.3;
        &.van-swipe__indicator--active {
          width: 10 * @rem;
          opacity: 1;
        }
      }
    }

    .banner {
      width: 100%;
      height: 490 * @rem;
    }
  }

  .bolang {
    width: 375 * @rem;
    height: 82 * @rem;
    background: url(~@/assets/images/player-voice/bolang.png) no-repeat;
    background-size: 375 * @rem 82 * @rem;
    position: relative;
    top: -20 * @rem;
    bottom: -50 * @rem;
  }
  .container {
    width: 351 * @rem;
    margin: 0 auto 20 * @rem;
    padding-bottom: 15 * @rem;
    border-radius: 12 * @rem;
    border: 1 * @rem solid #eff1f2;

    &.special {
      margin-top: -50 * @rem;
      position: relative;
    }

    .title {
      width: 353 * @rem;
      height: 72 * @rem;
      display: flex;
      align-items: center;
      background: #fff url(~@/assets/images/player-voice/title-bg.png) no-repeat;
      background-size: 353 * @rem 72 * @rem;
      padding-left: 5 * @rem;
      margin: -1 * @rem;
      box-sizing: border-box;

      span {
        display: block;
        height: 18 * @rem;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #333333;
        line-height: 18 * @rem;
        padding: 0 13 * @rem;
        position: relative;

        &::before {
          content: '';
          display: block;
          width: 10 * @rem;
          height: 8 * @rem;
          background: url(~@/assets/images/player-voice/title-before-icon.png)
            no-repeat;
          background-size: 10 * @rem 8 * @rem;
          position: absolute;
          top: 0;
          left: 0;
        }
        &::after {
          content: '';
          display: block;
          width: 10 * @rem;
          height: 8 * @rem;
          background: url(~@/assets/images/player-voice/title-after-icon.png)
            no-repeat;
          background-size: 10 * @rem 8 * @rem;
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }

    .tab-nav {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 18 * @rem;
      box-sizing: border-box;
      margin-top: -8 * @rem;

      .tab-list {
        display: flex;
        align-items: center;

        .tab {
          height: 30 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #333333;
          line-height: 18 * @rem;
          margin-right: 22 * @rem;
          position: relative;

          &:last-of-type {
            margin-right: 0;
          }

          &.active {
            font-weight: bold;

            &::after {
              content: '';
              display: block;
              width: 12 * @rem;
              height: 6 * @rem;
              border-radius: 16 * @rem;
              background-color: @themeColor;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }

      .order {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        height: 18 * @rem;

        span {
          flex-shrink: 0;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #999999;
          line-height: 18 * @rem;
        }

        .icon {
          width: 11 * @rem;
          height: 7 * @rem;
          margin-left: 2 * @rem;
          background: url(~@/assets/images/player-voice/arrow.png) no-repeat;
          background-size: 11 * @rem 7 * @rem;
        }
      }
    }

    .comment-list {
      padding: 25 * @rem 19 * @rem 0;

      .item {
        margin-bottom: 40 * @rem;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .active-list {
      padding: 0 30 * @rem 0 20 * @rem;

      .item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 15 * @rem 0;
        border-bottom: 1 * @rem solid #eee;

        &:first-of-type {
          padding-top: 0;
        }

        &:last-of-type {
          border-bottom: none;
        }

        .img {
          flex-shrink: 0;
          width: 40 * @rem;
          height: 40 * @rem;
          margin-right: 11 * @rem;
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            width: 100%;
            height: 18 * @rem;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #333333;
            line-height: 18 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;

            &::before {
              content: '';
              display: block;
              width: 78 * @rem;
              height: 11 * @rem;
              background: rgba(33, 185, 138, 0.1);
              position: absolute;
              bottom: -2 * @rem;
              left: 0;
            }
          }

          .desc {
            width: 100%;
            height: 18 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #666666;
            line-height: 18 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 5 * @rem;
          }
        }

        .right-icon {
          flex-shrink: 0;
          width: 18 * @rem;
          height: 18 * @rem;
          background: url(~@/assets/images/player-voice/right-icon.png)
            no-repeat;
          background-size: 18 * @rem 18 * @rem;
          margin-left: 10 * @rem;
        }
      }
    }
  }
  .bottom-fixed {
    width: 375 * @rem;
    position: fixed;
    bottom: 0;
    background-color: #fff;
    padding: 7 * @rem 0;
    box-shadow: 0 * @rem -5 * @rem 8 * @rem 0 * @rem rgba(0, 133, 255, 0.05);

    .btns {
      width: 326 * @rem;
      height: 42 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 21 * @rem;
      overflow: hidden;
      margin: 0 auto;
    }

    .share-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 103 * @rem;
      height: 42 * @rem;
      background: @themeBg;
      border-radius: 6 * @rem;
      transform: skew(-10deg);
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 15 * @rem;
      text-align: center;
      span {
        transform: skew(10deg);
      }
      &.disabled {
        background: #ccc;

        span {
          color: #fff;
        }
      }
    }
    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 218 * @rem;
      height: 42 * @rem;
      background: @themeBg;
      border-radius: 6 * @rem;
      transform: skew(-10deg);
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 15 * @rem;
      text-align: center;
      span {
        transform: skew(10deg);
      }

      &.disabled {
        background: #ccc;

        span {
          color: #fff;
        }
      }
    }
  }

  .select-content {
    width: 100%;
    padding: 12 * @rem;
    box-sizing: border-box;

    .select-list {
      display: flex;
      margin-top: 15 * @rem;

      span {
        flex-shrink: 0;
        width: 70 * @rem;
        line-height: 35 * @rem;
        font-size: 16 * @rem;
        color: #333;
        margin-right: 10 * @rem;
      }

      .list {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 80 * @rem;
          height: 35 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 35 * @rem;
          border-radius: 20 * @rem;
          background-color: #f2f2f2;
          border: 1 * @rem solid #f2f2f2;
          color: #333;
          font-size: 14 * @rem;
          margin-right: 10 * @rem;
          margin-bottom: 10 * @rem;

          &:nth-of-type(3n) {
            margin-right: 0;
          }

          &.active {
            background-color: #e7f8f3;
            color: @themeColor;
            border-color: @themeColor;
          }
        }
      }
    }

    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20 * @rem;

      .cancel-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120 * @rem;
        height: 42 * @rem;
        background-color: #efefef;
        color: #333;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
      }
      .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 200 * @rem;
        height: 42 * @rem;
        background: @themeBg;
        color: #fff;
        font-size: 14 * @rem;
        border-radius: 20 * @rem;
      }
    }
  }
}
</style>
