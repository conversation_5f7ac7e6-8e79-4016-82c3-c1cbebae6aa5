<template>
  <div class="game-quan-page">
    <div class="xiaohao-fixed-placeholder" ref="xiaohaoFixed">
      <div
        class="xiaohao-fixed"
        v-if="currentXiaohao.nickname"
        @click="changeXiaohao"
      >
        <div class="xiaohao-name">
          {{ $t('小号') }}：{{ currentXiaohao.nickname }}
        </div>
        <div class="change-xiaohao">
          {{ $t('切换') }}<span class="change-xiaohao-icon"></span>
        </div>
      </div>
      <div class="xiaohao-fixed" v-else>{{ fixedText }}</div>
    </div>
    <div class="tabs-list">
      <div class="tabs-item">
        <div
          class="item"
          v-for="(item, index) in tabsList"
          :key="index"
          :class="{ active: currentTab === index }"
          @click="clickTab(index)"
        >
          <span>{{ item.title }}</span>
        </div>
      </div>
      <div class="instruction" @click="toIntroduction">
        {{ $t('使用说明') }}</div
      >
    </div>
    <div
      class="quan-container"
      :class="{
        centered:
          !isLoadingOver ||
          (isLoadingOver && (empty || !filteredCouponList.length)),
      }"
    >
      <loading-indicator v-if="!isLoadingOver"></loading-indicator>
      <content-empty
        tips="暂无可领取的代金券"
        :emptyImg="emptyImg"
        v-else-if="isLoadingOver && (empty || !filteredCouponList.length)"
      ></content-empty>
      <div class="quan-list" v-else>
        <template v-for="(item, index) in filteredCouponList">
          <div
            class="quan-item"
            :key="index"
            :class="{ 'quan-svip': item.type == 1 }"
          >
            <div
              v-if="item.type && item.type != 0 && subscript(item)"
              class="subscript"
              :class="{ svip: item.type == 1 }"
            >
              {{ subscript(item) }}
            </div>
            <div class="quan-info">
              <div class="top">
                <div class="left">
                  <div class="quan-num">
                    <em class="unit">¥</em><span>{{ item.money }}</span>
                  </div>
                  <div class="total-num">
                    {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
                  </div>
                </div>
                <div class="center">
                  <div class="title">{{ item.title }}</div>
                  <div class="date" :class="{ 'svip-date': item.type == 1 }">
                    {{ item.expire_time_text || item.period_title }}
                  </div>
                </div>
                <div class="right">
                  <div
                    class="get btn"
                    :class="{
                      had: item.remain_percent == 0,
                    }"
                    @click="getCoupon(item)"
                  >
                    {{
                      item.take_status != 0
                        ? $t('去使用')
                        : item.remain_percent != 0
                          ? $t('领取')
                          : $t('已抢光')
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- <div class="bottom-fixed" v-if="couponList.length">
      <div class="get-all btn" @click="getCouponAll">{{ $t('全部领取') }}</div>
    </div> -->
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      @close-popup="closePopup"
      :id="Number(gameId)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import {
  ApiCouponCoupon,
  ApiCouponTake,
  ApiCouponTakeByGameId,
} from '@/api/views/coupon.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';
import { mapActions } from 'vuex';
import h5Page from '@/utils/h5Page';
import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
import LoadingIndicator from '@/components/loading-Indicator';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
export default {
  name: 'GameCoupon',
  components: {
    xhCreateTipDialog,
    LoadingIndicator,
  },
  props: {
    gameId: {
      type: Number,
      default: 0,
    },
    popupCounterNum: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      xiaohaoListShowItem: {},
      currentXiaohao: {}, //当前选择小号
      fixedText: '',
      couponList: [],
      empty: false,
      // gameId: this.$route.params.game_id,
      isLoadingOver: false, //列表加载完毕
      currentTab: 0, //0全部 1未领取 2已领取
      tabsList: [
        {
          title: '全部',
        },
        {
          title: '未领取',
        },
        {
          title: '已领取',
        },
      ],
      emptyImg,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    filteredCouponList() {
      if (this.currentTab === 1) {
        return this.couponList.filter(coupon => !coupon.take_status);
      } else if (this.currentTab === 2) {
        return this.couponList.filter(coupon => coupon.take_status);
      }
      return this.couponList;
    },
  },
  async created() {
    await this.getCurrentXiaohaoId();
    await this.getList();
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    clickTab(index) {
      if (!this.isLoadingOver) return false;
      this.currentTab = index;
    },
    closePopup(bool) {
      this.$emit('close-popup', bool);
    },
    // 获取当前的小号id
    async getCurrentXiaohaoId() {
      try {
        const res = await ApiXiaohaoMyListByGameId({
          gameId: this.gameId,
        });
        const { list, text } = res.data;
        if (list && list.length) {
          this.xiaohaoList = list;
          // 判断是否有已选择小号
          let flag = list.some(item => {
            return item.id == this.xiaohaoMap[this.gameId]?.id;
          });
          if (flag) {
            this.currentXiaohao = this.xiaohaoMap[this.gameId];
          } else {
            this.currentXiaohao = list[0];
            // this.setXiaohaoMap([this.gameId, list[0]]);
          }
        } else {
          this.xiaohaoList = [];
          this.currentXiaohao = {};
          this.setXiaohaoMap([this.gameId, {}]);
          if (text) this.fixedText = text;
        }
      } catch (error) {}
    },
    changeXiaohao() {
      if (!this.isLoadingOver) return false;
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    getCoupon(item) {
      if (item.take_status != 0) {
        this.$emit('close-popup');
        this.$nextTick(() => {
          this.toPage('MyCoupon');
        });
        // this.$toast(this.$t('亲，当前账号已经领取过该券了~'));
        return false;
      }
      if (!this.currentXiaohao.id) {
        this.createDialogShow = true;
        return false;
      }
      this.$toast.loading({
        message: this.$t('领取中...'),
      });
      ApiCouponTake({
        couponId: item.id,
        xhId: this.currentXiaohao.id,
      }).then(
        async res => {
          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${item.id}`,
            voucher_name: `${item.title}`,
            voucher_amount: `${item.money}`,
          });

          this.$toast(res.msg);
          this.$set(this.couponList[this.findIndex(item)], 'take_status', true);
          this.SET_USER_INFO();
          await this.getList();
        },
        err => {
          // if (err.code == 0) {
          //   this.$set(
          //     this.couponList[this.findIndex(item)],
          //     "remain_percent",
          //     0
          //   );
          // }
        },
      );
    },
    // getCouponAll() {
    //   this.$toast.loading({
    //     message: this.$t('领取中...'),
    //   });
    //   ApiCouponTakeByGameId({
    //     gameId: this.gameId,
    //     xhId: this.currentXiaohao.id,
    //   }).then(async res => {
    //     this.$toast(res.msg);
    //     this.couponList.forEach((item, index) => {
    //       item.take_status = true;
    //     });
    //     this.SET_USER_INFO();
    //     await this.getList();
    //   });
    // },
    findIndex(coupon) {
      return this.couponList.findIndex(item => {
        return item.id == coupon.id;
      });
    },
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: h5Page.daijinquanshuoming,
          title: this.$t('代金券使用说明'),
        },
      });
    },
    subscript(item) {
      switch (parseInt(item.type)) {
        case 1:
          return this.$t('SVIP代金券');
        case 2:
          return this.$t('无门槛');
        // case 5:
        //   return this.$t("零门槛");
        default:
          return false;
      }
    },
    async getList() {
      try {
        this.isLoadingOver = false;
        const res = await ApiCouponCoupon({
          gameId: this.gameId,
          xhId: this.currentXiaohao.id || '',
        });
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.couponList = res.data.list;
        }
      } catch (error) {
      } finally {
        this.isLoadingOver = true;
      }
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.setXiaohaoMap([this.gameId, this.currentXiaohao]);
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getList();
      this.$toast.clear();
    },
  },
  watch: {
    popupCounterNum(newVal) {
      this.getCurrentXiaohaoId();
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-page {
  background-color: #f3f5f9;
  .xiaohao-fixed-placeholder {
    position: relative;
    width: 100%;
    height: 36 * @rem;
    flex-shrink: 0;
  }
  .xiaohao-fixed {
    position: fixed;
    z-index: 100;
    .fixed-center;
    box-sizing: border-box;
    padding: 0 12 * @rem;
    width: 100%;
    height: 36 * @rem;
    line-height: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #eafbf3;
    color: #191b1f;
    font-size: 12 * @rem;
    .change-xiaohao {
      display: flex;
      align-items: center;

      .change-xiaohao-icon {
        margin-left: 6 * @rem;
        width: 14 * @rem;
        height: 14 * @rem;
        .image-bg('~@/assets/images/games/change-xiaohao-icon-new.png');
      }
    }
  }
  .tabs-list {
    padding: 0 12 * @rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tabs-item {
      width: 192 * @rem;
      height: 42 * @rem;
      display: flex;
      align-items: center;
      .item {
        box-sizing: border-box;
        width: 192 * @rem;
        height: 26 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        border: 1 * @rem solid #ffffff;
        color: #93999f;
        display: flex;
        align-items: center;
        justify-content: center;
        &.active {
          border-radius: 6 * @rem;
          background: #ecfbf4;
          border: 1 * @rem solid #ecfbf4;
          font-weight: 500;
          color: #1cce94;
        }
      }
    }
    .instruction {
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
    }
  }
  .quan-container {
    height: 350 * @rem;
    overflow-y: auto;
    &.centered {
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
    }
    &::-webkit-scrollbar {
      display: none;
    }
    background-color: #f3f5f9;
    .changwan-card {
      width: 339 * @rem;
      height: 42 * @rem;
      margin: 15 * @rem auto 12 * @rem;
      background: url(~@/assets/images/games/changwan-bg.png) center center
        no-repeat;
      background-size: 339 * @rem 42 * @rem;
    }
    .quan-list {
      padding-bottom: 34 * @rem;
      .quan-item {
        position: relative;
        margin: 12 * @rem auto 0 * @rem;
        background-color: #fff;
        border-radius: 8 * @rem;
        padding: 0 14 * @rem;
        box-sizing: border-box;
        width: 351 * @rem;
        height: 88 * @rem;
        background: url(~@/assets/images/games/welfare-coupon-bg.png) center
          center no-repeat;
        background-size: 351 * @rem 88 * @rem;
        .subscript {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 46 * @rem;
          height: 15 * @rem;
          font-weight: bold;
          font-size: 9 * @rem;
          color: #ffffff;
          line-height: 1;
          border-top-left-radius: 8 * @rem;
          border-bottom-right-radius: 12 * @rem;
          background: linear-gradient(90deg, #1cce94 0%, #55efbd 99%);
          position: absolute;
          top: 0;
          left: 0;

          &.svip {
            padding: 0 7 * @rem 0 6 * @rem;
            color: #754400;
            background: linear-gradient(90deg, #fed184 0%, #ffcd77 100%);
          }
        }
        .quan-info {
          box-sizing: border-box;
          height: 100%;
          position: relative;
          display: flex;
          .top {
            display: flex;
            align-items: center;
            padding: 12 * @rem 0;
            .left {
              width: 60 * @rem;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              .quan-num {
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                .unit {
                  font-size: 11 * @rem;
                  line-height: 1;
                  color: #fe4a26;
                }
                span {
                  display: block;
                  font-size: 24 * @rem;
                  font-weight: bold;
                  margin-left: 1 * @rem;
                  line-height: 34 * @rem;
                  overflow: hidden;
                  color: #fe4a26;
                }
              }
              .total-num {
                white-space: nowrap;
                font-size: 11 * @rem;
                line-height: 12 * @rem;
                color: #fe4a26;
                font-weight: 500;
              }
            }
            .center {
              max-width: 170 * @rem;
              position: absolute;
              left: 86 * @rem;
              flex: 1;
              min-width: 0;
              .title {
                font-size: 14 * @rem;
                color: #000000;
                font-weight: bold;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-height: 18 * @rem;
                max-height: 36 * @rem;
                overflow: hidden;
              }
              .date {
                max-width: 170 * @rem;
                overflow: hidden;
                margin-top: 8 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #93999f;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                &.svip-date {
                  color: #c29170;
                }
              }
            }
            .right {
              position: absolute;
              right: 0;
            }
          }

          .get {
            width: 60 * @rem;
            height: 28 * @rem;
            border-radius: 48 * @rem;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 11 * @rem;
            font-weight: 600;
            background: #1cce94;
            margin-left: 10 * @rem;
            &.had {
              background: #bababa;
            }
          }
        }
        &.quan-svip {
          background-image: url(~@/assets/images/games/welfare-coupon-bg-svip.png);
          .quan-info {
            .center {
              .title {
                color: #75440d;
              }
              .desc {
                .date {
                  color: #a16910;
                }
              }
            }
            .get {
              background: linear-gradient(90deg, #ffa06c 0%, #ff5050 100%);
            }
          }
        }
      }
    }
  }
  .bottom-fixed {
    width: 100%;
    height: 68 * @rem;
    padding: 12 * @rem 10 * @rem;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    box-shadow: 0 * @rem -4 * @rem 8 * @rem 0 * @rem rgba(0, 37, 168, 0.05);

    .get-all {
      width: 100%;
      height: 44 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      font-weight: 500;
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 1;
      text-align: center;
      border-radius: 43 * @rem;
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
