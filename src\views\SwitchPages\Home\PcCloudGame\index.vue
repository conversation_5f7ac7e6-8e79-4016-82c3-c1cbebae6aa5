<template>
  <div
    class="cloud-game-page"
    :class="{ 'no-login-page': !vipInfoList.login_status || !userInfo.token }"
  >
    <van-loading v-if="!loadSuccess && !defaultPageShow" />
    <div class="default-error" v-else-if="!loadSuccess && defaultPageShow">
      <default-error-page @callback="parentCallback" />
    </div>
    <yy-list
      v-else
      class="cloud-game-container"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
      :check="false"
    >
      <!-- 云玩会员 -->
      <div
        class="cloud_vip_content"
        :class="{
          no_login_padding: !vipInfoList.login_status || !userInfo.token,
        }"
      >
        <div class="vip_info">
          <div
            class="no_login_box"
            v-if="!vipInfoList.login_status || !userInfo.token"
          >
            <div class="cloud_novip_bg">
              <img
                src="~@/assets/images/cloud-game/cloud_novip_bg.png"
                alt=""
              />
            </div>
            <div class="cloud_novip_item">
              <div class="left_msg1">
                <div class="title">云玩会员</div>
                <div class="desc">排队优先·&nbsp; 高画质 ·&nbsp; 低延迟</div>
              </div>
              <div class="login_btn" @click.stop="toPage('PhoneLogin')"
                >登录</div
              >
            </div>
          </div>
          <div class="login_box" v-else>
            <div class="vip_top" @click.prevent="toPage('CloudGameUse')">
              <div class="cloud_vip_bg">
                <img
                  src="~@/assets/images/cloud-game/cloud_vip_bg.png"
                  alt=""
                />
              </div>
              <div class="left_msg2">
                <div class="title">云玩会员</div>
                <div class="time_desc">
                  <div class="vip_time">
                    <div
                      class="time"
                      :class="{
                        fontSize: vipInfoList?.user?.duration / 60 > 999,
                      }"
                      >{{ taskTime }}</div
                    >
                    <div
                      class="task"
                      v-if="vipInfoList?.isShowTask"
                      @click.stop="openTaskListPopup"
                    >
                      <img
                        src="~@/assets/images/cloud-game/task_time_icon.png"
                        alt=""
                      />
                      <div class="task-text"
                        ><div> 做任务，得时长 </div>
                        <img
                          src="~@/assets/images/cloud-game/task_time_right.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                  <div class="text">剩余时长</div>
                </div>
              </div>
            </div>
            <div class="vip_bottom">
              <div class="vip_bottom_bg">
                <img
                  src="~@/assets/images/cloud-game/vip_bottom_bg.png"
                  alt=""
                />
              </div>
              <div class="msg">
                <div class="box open_vip" v-if="!vipInfoList.isVip">
                  <div class="title">开通会员送免费时长</div>
                  <div class="desc">排队优先·&nbsp; 高画质 ·&nbsp; 低延迟</div>
                </div>
                <div
                  class="box renew_vip"
                  v-else-if="
                    vipInfoList.isVip && vipInfoList.expirestatus !== 0
                  "
                >
                  <div class="title">尊享8项+会员专属特权</div>
                  <div class="desc"
                    >到期时间：{{ formatTime(vipInfoList.expiretime) }}</div
                  >
                </div>
                <div
                  class="box expire_vip"
                  v-else-if="vipInfoList.isVip && !vipInfoList.expirestatus"
                >
                  <div class="title">会员即将到期</div>
                  <div class="desc"
                    >到期时间：{{ formatTime(vipInfoList.expiretime) }}</div
                  >
                </div>
              </div>
              <div class="btn_box">
                <div class="btn_item" @click.stop="toPage('CloudGameBuy')">
                  <div v-if="!vipInfoList.isVip">开通会员</div>
                  <div v-else>立即续费</div>
                </div>
              </div>
            </div>
            <div class="cloud_vip_logo">
              <img
                src="~@/assets/images/cloud-game/cloud_vip_logo.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 为你推荐 -->
      <div
        class="container_box recommended_content"
        v-if="recommendedList.length"
      >
        <div class="header">
          <div class="header_title">为你推荐</div>
        </div>
        <div class="container">
          <div
            class="item"
            v-for="item in recommendedList"
            :key="item.id"
            @click="toPage('GameDetail', { id: item.id })"
          >
            <div class="item_img">
              <img v-show="item.verticalpic" :src="item.verticalpic" alt="" />
            </div>
            <div class="item_title"> {{ item.title }} </div>
          </div>
        </div>
      </div>
      <!-- 编辑推荐 -->
      <div class="container_box edit_content" v-if="editList.length">
        <div class="header">
          <div class="header_title">编辑推荐</div>
        </div>
        <div
          class="edit_container"
          :style="{ background: hex2rgba(editList[0].boldcolor, 0.05) }"
          @click.prevent="toPage('GameDetail', { id: editList[0].id })"
        >
          <div class="edit_card">
            <div class="card_img">
              <img
                v-if="editList[0].verticalpic"
                :src="editList[0].verticalpic"
                alt=""
              />
            </div>
            <div class="card_info">
              <div class="card_top">
                <div class="container-box">
                  <span
                    class="title"
                    :style="{ color: hex2rgba(editList[0].boldcolor) }"
                    >{{ getDisplayText(0) }}</span
                  >
                  <div
                    class="label"
                    v-if="editList[0].state_tag"
                    :style="{
                      background: hex2rgba(editList[0].boldcolor, 0.1),
                      color: hex2rgba(editList[0].boldcolor),
                    }"
                    >{{ editList[0].state_tag }}</div
                  >
                </div>
                <div class="tag-list">
                  <div class="tags">
                    <div
                      class="tag"
                      v-for="tag in editList[0].new_cate_list"
                      :key="tag.id"
                      :style="{
                        color: hex2rgba(editList[0].boldcolor, 0.6),
                        border: editList[0].boldcolor
                          ? `${0.5 * remNumberLess}rem solid ${
                              editList[0].boldcolor
                            }`
                          : '',
                      }"
                    >
                      {{ tag.title }}
                    </div>
                  </div>
                </div>
                <div
                  class="score"
                  :style="{ color: hex2rgba(editList[0].boldcolor) }"
                  v-if="Number(editList[0].score)"
                >
                  <span>{{ editList[0].score }}</span>
                  <span>分</span>
                </div>
              </div>

              <div class="btn">
                <div
                  v-if="editList[0].detailid == 1"
                  :style="{ background: hex2rgba(editList[0].boldcolor) }"
                  :class="{ loading: pcCloudGameInitLoading[editList[0].id] }"
                  @click.stop="cloudPlayInit(editList[0], editList[0].id)"
                  >云玩</div
                >
                <div
                  v-else
                  :style="{ background: hex2rgba(editList[0].boldcolor) }"
                  :class="{ loading: pcCloudGameInitLoading[editList[0].id] }"
                  @click.stop="downJymyBtn(editList[0])"
                  >下载</div
                >
              </div>
            </div>
          </div>
          <div
            v-if="editList[0].smalltexts"
            class="dashed-line"
            :style="{ background: hex2rgba(editList[0].boldcolor) }"
          ></div>
          <div class="edit_describe" v-if="editList[0].smalltexts">
            <div
              class="info"
              :style="{ color: hex2rgba(editList[0].boldcolor, 0.6) }"
            >
              <div v-html="editList[0].smalltexts"></div>
            </div>
            <div class="info_before">
              <svg
                width="15"
                height="13"
                viewBox="0 0 15 13"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.5507 6.97895H14.85V13H8.40143V7.8C8.30146 3.78596 10.501 1.18596 15 0V2.32632C12.6005 3.51228 11.4508 5.06316 11.5507 6.97895ZM3.15258 6.97895H6.45186V13H0.00326016V7.8C-0.0967181 3.78596 2.1028 1.18596 6.60183 0V2.32632C4.20235 3.51228 3.0526 5.06316 3.15258 6.97895Z"
                  :fill="
                    editList[0].boldcolor
                      ? hex2rgba(editList[0].boldcolor, 0.6)
                      : 'rgba(89,106,144,0.6)'
                  "
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <!-- 热门游戏 -->
      <div class="container_box hot_game_content" v-if="hotGameList.length">
        <div class="header">
          <div class="header_title">热门游戏</div>
          <div class="header_arrow" @click="toPage('HotGameList')">
            <span>
              <img src="~@/assets/images/pc_right_arrow.png" alt="" />
            </span>
          </div>
        </div>
        <div class="container">
          <div
            class="item"
            v-for="item in hotGameList"
            :key="item.game_id"
            @click="toPage('GameDetail', { id: item.game_id })"
          >
            <div class="item_img">
              <img v-show="item.verticalpic" :src="item.verticalpic" alt="" />
            </div>
            <div class="item_title"> {{ item.title }} </div>
          </div>
        </div>
      </div>
      <!-- 高时长云玩游戏 -->
      <div
        class="container_box longtime_game_content"
        v-if="longTimeGameList.length"
      >
        <swiper :options="getSwiperOption">
          <swiper-slide
            class="swiper-slide"
            v-for="(item, index) in longTimeGameList"
            :key="index"
          >
            <div class="header">
              <div class="header_title">{{ item.header_title }}</div>
            </div>
            <div
              class="edit_container lt_bg_color"
              @click.prevent="toPage('GameDetail', { id: item.game_id })"
              :style="{ background: hex2rgba(item.boldcolor, 0.05) }"
            >
              <div class="edit_card">
                <div class="card_img">
                  <img
                    v-show="item.verticalpic"
                    :src="item.verticalpic"
                    alt=""
                  />
                </div>
                <div class="card_info">
                  <div class="card_top">
                    <div
                      class="card_desc"
                      :style="{ color: hex2rgba(item.boldcolor) }"
                      >{{ item.pc_state }}</div
                    >
                    <div :class="`container-box${index}`">
                      <span
                        class="title1"
                        :style="{ color: hex2rgba(item.boldcolor) }"
                        >{{ getDisplayText(index + 1) }}</span
                      >
                      <div
                        class="icon-2"
                        v-if="item.state_tag"
                        :style="{
                          background: hex2rgba(item.boldcolor, 0.1),
                          color: hex2rgba(item.boldcolor),
                        }"
                      >
                        {{ item.state_tag }}
                      </div>
                    </div>
                    <div
                      class="score score_top"
                      :style="{ color: hex2rgba(item.boldcolor) }"
                      v-if="Number(item.score)"
                    >
                      <span>{{ item.score }}</span>
                      <span>分</span>
                    </div>
                    <div v-else class="tag-list">
                      <div class="tags">
                        <div
                          class="tag"
                          v-for="tag in item.new_cate_list"
                          :key="tag.id"
                          :style="{
                            color: hex2rgba(item.boldcolor, 0.6),
                            border: item.boldcolor
                              ? `${0.5 * remNumberLess}rem solid ${
                                  item.boldcolor
                                }`
                              : '',
                          }"
                        >
                          {{ tag.title }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="btn">
                    <!-- 1是云游戏，2是鲸云 -->
                    <div
                      v-if="item.detailid == 1"
                      class="lt_btn_color"
                      :style="{ background: hex2rgba(item.boldcolor) }"
                      :class="{ loading: pcCloudGameInitLoading[item.game_id] }"
                      @click.stop="cloudPlayInit(item, item.game_id)"
                      >云玩</div
                    >
                    <div
                      v-else
                      class="lt_btn_color"
                      :style="{ background: hex2rgba(item.boldcolor) }"
                      :class="{ loading: pcCloudGameInitLoading[item.game_id] }"
                      @click.stop="downJymyBtn(item)"
                      >下载</div
                    >
                  </div>
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <!-- 新游速递 -->
      <div class="container_box new_game_content" v-if="newGameList.length">
        <div class="header">
          <div class="header_title">新游速递</div>
          <div class="header_arrow" @click="toPage('NewGameList')">
            <span>
              <img src="~@/assets/images/pc_right_arrow.png" alt="" />
            </span>
          </div>
        </div>
        <div class="container">
          <div
            class="item"
            v-for="item in newGameList"
            :key="item.game_id"
            @click="toPage('GameDetail', { id: item.game_id })"
          >
            <div class="item_img">
              <img v-show="item.verticalpic" :src="item.verticalpic" alt="" />
            </div>
            <div class="item_title"> {{ item.title }} </div>
          </div>
        </div>
      </div>
      <!-- 游戏平铺 -->
      <div class="game_list_content" v-if="gameList.length">
        <div class="cloud-play-list">
          <template v-for="item in gameList">
            <div class="cloud-play-item" :key="item.id">
              <cloud-play-item :info="item" :isShowBtn="true"></cloud-play-item>
            </div>
          </template>
        </div>
      </div>
    </yy-list>

    <!-- pc云游戏任务弹窗 -->
    <taskListPopup
      @updateVipInfo="updateVipInfo"
      :show.sync="_taskListPopupShow"
      :update-trigger="updateTrigger"
    ></taskListPopup>

    <DurationOverPopup :show.sync="_durationOverShow"> </DurationOverPopup>
  </div>
</template>
<script>
import taskListPopup from '@/components/task-list-popup';
import CloudPlayItem from './components/cloud-play-item';
import { ApiCloudGameIndex } from '@/api/views/game';
import { mapActions, mapMutations, mapGetters } from 'vuex';
import { hex2rgb, hex2rgba } from '@/utils/color';
import { remNumberLess } from '@/common/styles/_variable.less';
import DurationOverPopup from '@/components/duration-over-popup';
import { downJymyBtnCallback } from '@/utils/function';
export default {
  name: 'PcCloudGame',
  components: {
    taskListPopup,
    CloudPlayItem,
    DurationOverPopup,
  },
  data() {
    return {
      vip_status: 0, //0：开通会员 1:时长足，续费 2:时长不足，续费
      need_Renew_Vip: false, //开通会员
      loading: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      paging: 1,
      tj_page: 1,
      listRows: 10,
      empty: false,
      getSwiperOption: {
        slidesPerView: 'auto',
        observer: true,
        observerSlideChildren: true,
        observerParents: true,
      },
      updateTrigger: 0, // 更新的计数器
      vipInfoList: [], // 云玩会员信息
      recommendedList: [], //为你推荐
      editList: [], //编辑推荐
      hotGameList: [], //热门游戏
      longTimeGameList: [], //高时长云玩游戏
      newGameList: [], //新游速递
      gameList: [], //游戏平铺
      game_list: [], //当前页游戏平铺
      loadSuccess: false, //加载完毕
      defaultPageShow: false, //网络错误占位符
      hex2rgb,
      hex2rgba,
      remNumberLess,
      lengthIndex: 17, // 保留的长度
      spanTexts: ['', '', '', ''], // 原本的的文字
      displayTexts: ['', '', '', ''], //计算后的文字
    };
  },
  async mounted() {
    await this.getCloudGameList();
  },
  methods: {
    updateTitleText() {
      // 将游戏标题赋值给spanTexts数组
      let GameList = [
        { title: this.editList[0]?.title || '' },
        ...this.longTimeGameList,
      ];
      this.spanTexts = GameList.map(item => item.title);
      const targets = [
        this.$el.querySelector('.container-box'),
        this.$el.querySelector('.container-box0'),
        this.$el.querySelector('.container-box1'),
        this.$el.querySelector('.container-box2'),
      ];

      const spans = targets.map(target =>
        target ? target.querySelector('span') : null,
      );

      // 使用ResizeObserver和MutationObserver来监听多个目标
      const resizeObserver = new ResizeObserver(() => {
        this.onChange(true);
      });
      targets.forEach(target => {
        if (target) {
          resizeObserver.observe(target);
        }
      });

      const mutationObserver = new MutationObserver(() => {
        this.onChange(false);
      });
      spans.forEach(span => {
        if (span) {
          mutationObserver.observe(span, { childList: true, attributes: true });
        }
      });
    },
    onChange(reset) {
      const targets = [
        this.$el.querySelector('.container-box'),
        this.$el.querySelector('.container-box0'),
        this.$el.querySelector('.container-box1'),
        this.$el.querySelector('.container-box2'),
      ];

      targets.forEach((target, index) => {
        if (target) {
          if (target.clientHeight > `${40 * remNumberLess}rem`) {
            this.$set(
              this.displayTexts,
              index,
              this.spanTexts[index].slice(0, this.lengthIndex) + '...',
            );
          } else {
            if (reset) {
              this.$set(
                this.displayTexts,
                index,
                this.spanTexts[index].length > this.lengthIndex
                  ? this.spanTexts[index].slice(0, this.lengthIndex) + '...'
                  : this.spanTexts[index],
              );
            }
          }
          target.style.position = 'relative';
        }
      });
    },
    formatTime(timestamp) {
      let { year, month, day, time } = this.$handleTimestamp(timestamp);
      return `${year}.${month}.${day} ${time}`;
    },
    // 下载鲸云漫游
    downJymyBtn(item) {
      downJymyBtnCallback(item);
    },
    // 主动更新会员信息
    async updateVipInfo() {
      try {
        const res = await ApiCloudGameIndex({
          page: 1,
          paging: 1,
          listRows: 10,
        });
        res.data.forEach(item => {
          if (item.view_type == 1) {
            this.vipInfoList = item.pcgame_list;
          }
        });
      } catch (error) {
      } finally {
        await this.updateTitleText();
      }
    },
    async parentCallback() {
      this.loadSuccess = false;
      this.defaultPageShow = false;
      await this.getCloudGameList();
    },
    openTaskListPopup() {
      this.updateTrigger++;
      this.setTaskListPopupShow(true);
    },
    async getCloudGameList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
        this.paging = 1;
      } else if (this.finished) {
        return;
      } else {
        if (this.paging < 3) {
          this.paging++;
        }
        if (this.paging == 3) {
          this.page++;
        }
      }
      this.loadingObj.loading = true;
      try {
        const res = await ApiCloudGameIndex({
          page: this.page,
          paging: this.paging,
          tj_page: this.tj_page,
          listRows: this.listRows,
        });

        // 初始化各个列表
        const listMap = {
          1: 'vipInfoList',
          2: 'editList',
          3: 'hotGameList',
          4: 'longTimeGameList',
          5: 'newGameList',
          6: 'gameList',
          7: 'recommendedList',
        };

        if (action === 1 || this.paging == 1) {
          this.recommendedList = [];
          this.editList = [];
          this.hotGameList = [];
          this.longTimeGameList = [];
          this.newGameList = [];
          this.gameList = [];
          this.vipInfoList = [];
        }
        // 处理返回的数据
        res.data.forEach(item => {
          const listKey = listMap[item.view_type];
          if (listKey) {
            if (listKey === 'gameList') {
              this[listKey].push(...(item.game_list || []));
              this.game_list = item.game_list;
            } else {
              this[listKey] = item.game_list || item.pcgame_list || [];
            }
          }
        });
        // 检查是否有数据
        this.empty = !(
          this.recommendedList.length ||
          this.editList.length ||
          this.hotGameList.length ||
          this.longTimeGameList.length ||
          this.newGameList.length ||
          this.gameList.length ||
          Object.values(this.vipInfoList).length
        );
        this.loadSuccess = true;
        this.defaultPageShow = true;
        // 判断是否已加载完所有数据
        if (this.game_list.length < this.listRows && this.paging >= 2) {
          this.finished = true;
        } else {
          this.finished = false;
        }
      } catch (error) {
        if (error.toString().includes('code 500')) {
          this.loadSuccess = false;
          this.defaultPageShow = true;
        }
        if (!navigator.onLine) {
          this.loadSuccess = false;
          this.defaultPageShow = true;
        }
      } finally {
        this.$nextTick(async () => {
          await this.updateTitleText();
        });
        this.loadingObj.loading = false;
      }
    },
    async onRefresh() {
      this.tj_page++;
      await this.getCloudGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getCloudGameList(2);
    },
    // 云玩
    async cloudPlayInit(item, id) {
      if (
        this.pcCloudGameInitLoading[id] ||
        Object.values(this.pcCloudGameInitLoading).some(value => value === true)
      ) {
        return;
      }
      await this.OPEN_PC_CLOUD_GAME({ item, id });
    },
    ...mapActions({
      OPEN_PC_CLOUD_GAME: 'game/OPEN_PC_CLOUD_GAME',
    }),
    ...mapMutations({
      setPcCloudGameInitLoadingEmpty: 'game/setPcCloudGameInitLoadingEmpty',
      setDurationOverShow: 'game/setDurationOverShow',
      setTaskListPopupShow: 'game/setTaskListPopupShow',
      setHereItIosBoxTaskShow: 'game/setHereItIosBoxTaskShow',
    }),
  },
  computed: {
    ...mapGetters({
      pcCloudGameInitLoading: 'game/pcCloudGameInitLoading',
      durationOverShow: 'game/durationOverShow',
      taskListPopupShow: 'game/taskListPopupShow',
      hereItIosBoxTaskShow: 'game/hereItIosBoxTaskShow',
    }),
    _taskListPopupShow: {
      //是否显示任务弹窗
      get() {
        return this.taskListPopupShow;
      },
      set(value) {
        this.setTaskListPopupShow(value);
      },
    },
    _durationOverShow: {
      //是否显示时长不足弹窗
      get() {
        return this.durationOverShow;
      },
      set(value) {
        this.setDurationOverShow(value);
      },
    },
    taskTime() {
      const totalMinutes = this.vipInfoList?.user?.duration || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // 如果小时数大于 99999，则只显示小时
      if (hours > 99999) {
        return `${hours}小时`;
      }
      return `${hours}小时${minutes}分`;
    },
    getDisplayText() {
      return index => {
        return this.displayTexts[index] || '';
      };
    },
  },
  watch: {
    hereItIosBoxTaskShow(val) {
      if (val) {
        this.setTaskListPopupShow(true);
      }
    },
  },
  async activated() {
    this.defaultPageShow = false;
    this.setDurationOverShow(false);
    this.setPcCloudGameInitLoadingEmpty({});
    await this.updateVipInfo();
  },
  beforeRouteEnter(to, from, next) {
    if (
      ['/phone_login', '/login', '/login_hw', '/register_hw'].includes(
        from.fullPath,
      )
    ) {
      next(vm => {
        if (!vm.userInfo.token) {
          next();
        } else {
          vm.$router.push({ name: 'SwitchPages' });
        }
      });
    } else {
      next();
    }
  },
};
</script>

<style lang="less" scoped>
@edit_bg: rgba(89, 106, 144, 0.05);
@edit_color: #596a90;
@long_time_bg: rgba(89, 106, 144, 0.05);
@long_time_color: #596a90;

.cloud-game-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 18 * @rem;
  box-sizing: border-box;
  &:before {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(101 * @rem + @safeAreaTop);
    height: calc(101 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .default-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  /deep/ .van-loading {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cloud_vip_content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    .vip_info {
      box-sizing: border-box;
      .no_login_box {
        .cloud_novip_bg {
          width: 339 * @rem;
          height: 71 * @rem;
        }
        .cloud_novip_item {
          position: absolute;
          width: 100%;
          top: 14 * @rem;
          padding: 0 12 * @rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          .left_msg1 {
            .title {
              font-weight: 600;
              font-size: 16 * @rem;
              color: #e9d39c;
            }
            .desc {
              margin-top: 6 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #9b8b63;
              white-space: nowrap;
            }
          }
          .login_btn {
            width: 84 * @rem;
            height: 32 * @rem;
            line-height: 32 * @rem;
            text-align: center;
            background: linear-gradient(137deg, #ffc180 0%, #ffe9a7 99%);
            border-radius: 16 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #712203;
          }
        }
      }
      .login_box {
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        margin-top: 8 * @rem;

        .vip_top {
          height: 101 * @rem;
          .cloud_vip_bg {
            width: 339 * @rem;
            height: 101 * @rem;
          }
          .left_msg2 {
            position: absolute;
            top: 14 * @rem;
            left: 12 * @rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .title {
              height: 22 * @rem;
              font-weight: 600;
              font-size: 16 * @rem;
              color: #e9d39c;
              line-height: 22 * @rem;
            }
            .time_desc {
              position: relative;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              margin-top: 14 * @rem;
              .vip_time {
                display: flex;
                align-items: center;
                .time {
                  height: 25 * @rem;
                  font-weight: 600;
                  font-size: 20 * @rem;
                  color: #f9e3ac;
                  line-height: 25 * @rem;
                }
                .fontSize {
                  font-size: 17 * @rem;
                }
                .task {
                  position: relative;
                  img {
                    background-size: 112 * @rem 24 * @rem;
                    width: 112 * @rem;
                    height: 24 * @rem;
                  }
                  .task-text {
                    position: absolute;
                    top: 5 * @rem;
                    left: 12 * @rem;
                    display: flex;
                    align-items: center;
                    div {
                      height: 15 * @rem;
                      font-weight: 500;
                      font-size: 12 * @rem;
                      color: #701818;
                      line-height: 15 * @rem;
                    }
                    img {
                      margin-left: 1 * @rem;
                      width: 8 * @rem;
                      height: 8 * @rem;
                    }
                  }
                }
              }

              .text {
                margin-top: 3 * @rem;
                height: 15 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #ac9a6c;
                line-height: 15 * @rem;
              }
            }
          }
        }

        .vip_bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          height: 52 * @rem;
          // background: linear-gradient(
          //   92deg,
          //   #fde9bf 0%,
          //   #fef7db 37%,
          //   #ffd69c 91%,
          //   #ffe6c2 98%
          // );
          // border: 1 * @rem solid;
          // border-image: linear-gradient(
          //     108deg,
          //     rgba(251, 221, 161, 1),
          //     rgba(251, 243, 193, 1),
          //     rgba(255, 201, 119, 1)
          //   )
          //   1 1;
          // padding: 9 * @rem 12 * @rem;
          position: relative;
          .vip_bottom_bg {
            position: absolute;
          }
          .msg {
            position: absolute;
            top: 9 * @rem;
            left: 12 * @rem;
            .box {
              .title {
                height: 20 * @rem;
                font-weight: 600;
                font-size: 14 * @rem;
                color: #772b21;
                line-height: 20 * @rem;
              }
              .desc {
                height: 14px;
                font-weight: 400;
                font-size: 10 * @rem;
                color: #772b21;
                line-height: 14 * @rem;
              }
            }
          }
          .btn_box {
            position: absolute;
            top: 9 * @rem;
            right: 12 * @rem;
            border-radius: 16 * @rem;
            overflow: hidden;
            .btn_item {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 32 * @rem;
              line-height: 32 * @rem;
              box-sizing: border-box;
              background: linear-gradient(137deg, #98432b 0%, #772b21 100%);

              div {
                padding: 0 14 * @rem;
                font-weight: 500;
                font-size: 14 * @rem;
                white-space: nowrap;
              }
            }
          }
        }
        .cloud_vip_logo {
          position: absolute;
          right: 0;
          top: -8 * @rem;
          background-size: 91 * @rem 93 * @rem;
          width: 91 * @rem;
          height: 93 * @rem;
        }
      }
    }
    &.no_login_padding {
      width: 339 * @rem;
      height: 71 * @rem;
      border-radius: 12 * @rem;
      overflow: hidden;
    }
  }
  .container_box {
    margin-top: 20 * @rem;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 23 * @rem;
      margin-bottom: 10 * @rem;
      .header_title {
        font-size: 18 * @rem;
        font-weight: 600;
        color: #111111;
      }
      .header_arrow {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 100%;
        width: 20 * @rem;
        span {
          margin-left: 5 * @rem;
          img {
            width: 8 * @rem;
            height: 12 * @rem;
          }
        }
      }
    }
    .container {
      display: flex;
      align-items: center;
      .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 8 * @rem;
        .item_img {
          width: 105 * @rem;
          height: 143 * @rem;
          border-radius: 8 * @rem;
          background-color: #eeeeee;
          overflow: hidden;
        }
        .item_title {
          width: 105 * @rem;
          height: 18 * @rem;
          font-weight: 400;
          padding: 5 * @rem 0;
          font-size: 14 * @rem;
          color: #666666;
          line-height: 18 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: center;
        }
        &:not(:first-child) {
          margin-left: 12 * @rem;
        }
      }
    }
  }
  .recommended_content {
  }
  .edit_content,
  .longtime_game_content {
    .swiper-slide {
      margin-right: 10 * @rem;
    }
    .edit_container {
      width: 100%;
      background: @edit_bg;
      border-radius: 9 * @rem;
      padding: 16 * @rem 15 * @rem;
      box-sizing: border-box;
      overflow: hidden;
      .edit_card {
        display: flex;
        .card_img {
          width: 105 * @rem;
          height: 143 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
          background-color: rgba(89, 106, 144, 0.2);
        }
        .card_info {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: space-around;
          margin-left: 12 * @rem;
          .card_top {
            .card_desc {
              height: 17 * @rem;
              line-height: 17 * @rem;
              font-weight: 500;
              font-size: 12px;
              color: @long_time_color;
            }
            .container-box {
              position: relative;
              width: 187 * @rem;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              // -webkit-box-orient: vertical;
              // overflow: hidden;
              text-align: left;
              max-height: 40 * @rem;
              line-height: 20 * @rem;
              box-sizing: border-box;
              .title {
                font-weight: 600;
                font-size: 16 * @rem;
                height: 40 * @rem;
                line-height: 20 * @rem;
                color: @edit_color;
                overflow: hidden;
                &.lt_title_color {
                  color: @long_time_color;
                }
              }
              .label {
                position: relative;
                display: inline-block;
                flex-shrink: 0;
                background-size: cover;
                transform: translateY(-1 * @rem);
                background: blue;
                width: 52 * @rem;
                white-space: nowrap;
                height: 18 * @rem;
                line-height: 18 * @rem;
                text-align: center;
                margin-left: 4 * @rem;
                background: rgba(89, 106, 144, 0.1);
                border-radius: 3 * @rem;
                font-weight: 500;
                font-size: 11 * @rem;
                color: @edit_color;
                &.lt_label_color {
                  background: rgba(19, 104, 150, 0.1);
                  color: @long_time_color;
                }
              }
              &.header_top {
                margin-top: 6 * @rem;
              }
            }
            .tag-list {
              display: flex;
              align-items: center;

              .tags {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                overflow: hidden;
                margin-top: 7 * @rem;
                margin-bottom: 10 * @rem;
                height: 18 * @rem;
                line-height: 18 * @rem;

                .tag {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  white-space: nowrap;
                  border: 1px solid rgba(89, 106, 144, 0.6);
                  border-radius: 4 * @rem;
                  padding: 2 * @rem 4 * @rem;
                  height: 18 * @rem;
                  line-height: 18 * @rem;
                  box-sizing: border-box;
                  font-weight: 400;
                  font-size: 11 * @rem;
                  color: rgba(89, 106, 144, 0.6);
                  &:not(:first-child) {
                    margin-left: 8 * @rem;
                  }
                }
              }
            }
            .container-box0,
            .container-box1,
            .container-box2 {
              position: relative;
              margin-top: 6 * @rem;
              width: 187 * @rem;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              // -webkit-box-orient: vertical;
              // overflow: hidden;
              text-align: left;
              max-height: 40 * @rem;
              line-height: 20 * @rem;
              box-sizing: border-box;
              .title1 {
                font-weight: 600;
                font-size: 16 * @rem;
                height: 40 * @rem;
                line-height: 20 * @rem;
                color: @long_time_color;
              }
              .icon-2 {
                position: relative;
                display: inline-block;
                flex-shrink: 0;
                background-size: cover;
                transform: translateY(-1 * @rem);
                width: 52 * @rem;
                white-space: nowrap;
                height: 18 * @rem;
                line-height: 18 * @rem;
                text-align: center;
                margin-left: 4 * @rem;
                background: rgba(89, 106, 144, 0.1);
                border-radius: 3 * @rem;
                font-weight: 500;
                font-size: 11 * @rem;
                color: @long_time_color;
              }
            }
            .score {
              color: @edit_color;
              font-weight: 500;
              font-size: 12 * @rem;
              > span:first-of-type {
                font-size: 20 * @rem;
                font-weight: bold;
              }
              &.score_top {
                margin-top: 10 * @rem;
                color: @long_time_color;
              }
            }
          }

          .btn {
            flex-shrink: 0;
            min-width: 0;
            width: 54 * @rem;
            height: 24 * @rem;
            line-height: 24 * @rem;
            > div {
              text-align: center;
              background: @edit_color;
              color: #ffffff;
              font-weight: 500;
              font-size: 12 * @rem;
              white-space: nowrap;
              position: relative;
              border-radius: 19 * @rem;
              overflow: hidden;
              &.lt_btn_color {
                background: @long_time_color;
              }
              &.loading {
                position: relative;
                font-size: 0;
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  -webkit-transform: translate(-50%, -50%);
                  display: block;
                  width: 16 * @rem;
                  height: 16 * @rem;
                  background-size: 16 * @rem 16 * @rem;
                  background-image: url(~@/assets/images/downloadLoading.png);
                  animation: rotate 1s infinite linear;
                  -webkit-animation: rotate 1s infinite linear;
                }
              }
            }
          }
        }
        // &.borderBottom {
        //   border-bottom: 1px dashed rgba(89,106,144,0.6);
        // }
      }
      .dashed-line {
        margin: 20 * @rem 0 14 * @rem 0;
        width: 100%;
        height: 1 * @rem;
        background-color: @edit_color;
        mask-image: linear-gradient(90deg, @edit_color 50%, transparent 0%);
        mask-size: 12 * @rem 100%;
        opacity: 0.3;
      }
      .edit_describe {
        position: relative;
        .info {
          text-indent: 2.5ch;
          max-height: 40 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: rgba(89, 106, 144, 0.6);
          line-height: 20 * @rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
        }
        .info_before {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }
      &.lt_bg_color {
        background: @long_time_bg;
      }
    }
  }
  .hot_game_content {
  }
  .longtime_game_content {
  }
  .new_game_content {
  }
  .game_list_content {
    margin-top: 20 * @rem;
    .cloud-play-list {
      .cloud-play-item {
        &:not(:first-child) {
          margin-top: 10 * @rem;
        }
      }
    }
  }
}
.no-login-page {
  &:before {
    height: calc(114 * @rem + @safeAreaTop);
    height: calc(114 * @rem + @safeAreaTopEnv);
  }
}
body {
  /deep/.van-overflow-hidden {
    overflow: visible !important;
  }
}
</style>
