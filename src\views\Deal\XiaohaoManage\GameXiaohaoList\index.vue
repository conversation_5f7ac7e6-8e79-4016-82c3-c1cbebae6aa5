<template>
  <div class="game-xiaohao-list page">
    <nav-bar-2 :border="true" :title="$t('小号')"></nav-bar-2>
    <content-empty
      v-if="empty"
      :tips="$t('咦，什么都没找到哦~')"
    ></content-empty>
    <yy-list
      v-else
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
    >
      <div class="xiaohao-list">
        <div class="xiaohao-item" v-for="item in xiaohaoList" :key="item.id">
          <xiaohao-item :info="item"></xiaohao-item>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiXiaohaoManage } from '@/api/views/xiaohao.js';
import xiaohaoItem from '../components/xiaohao-item';
export default {
  name: 'GameXiaohaoList',
  components: {
    xiaohaoItem,
  },
  data() {
    return {
      appId: 0,
      xiaohaoList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      xiaohaoList: [],
    };
  },
  created() {
    this.appId = this.$route.params.id;
  },
  methods: {
    async getXiaohaoList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiXiaohaoManage({
        appId: this.appId,
        page: this.page,
        listRows: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.xiaohaoList = [];
      }
      this.xiaohaoList.push(...res.data.list);
      if (this.xiaohaoList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getXiaohaoList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.xiaohaoList.length) {
        await this.getXiaohaoList();
      } else {
        await this.getXiaohaoList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.game-xiaohao-list {
  .xiaohao-list {
    padding: 10 * @rem 0;
    .xiaohao-item {
      margin-top: 20 * @rem;
      &:nth-of-type(1) {
        margin-top: 0;
      }
    }
  }
}
</style>
