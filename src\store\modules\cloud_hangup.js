export default {
  state: {
    receiveData: [], //云挂机游戏
    checkCloudDeviceItem: [], //当前设备
  },
  getters: {
    receiveData(state) {
      return state.receiveData;
    },
    checkCloudDeviceItem(state) {
      return state.checkCloudDeviceItem;
    },
  },
  mutations: {
    setReceiveData(state, receiveData) {
      state.receiveData = receiveData ?? [];
    },
    setCheckCloudDeviceItem(state, checkCloudDeviceItem) {
      state.checkCloudDeviceItem = checkCloudDeviceItem ?? [];
    },
  },
  actions: {},
};
