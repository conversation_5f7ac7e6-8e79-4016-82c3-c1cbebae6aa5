<template>
  <div>
    <!-- 绑定手机 -->
    <van-dialog
      v-model="show"
      :close-on-click-overlay="false"
      :lock-scroll="false"
      class="dialog popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-title">{{ title }}</div>

      <div class="popup-content">
        <div class="popup-close" @click="onCancel">
          <img src="~@/assets/images/close-dialog.png" alt="" />
        </div>
        <div class="change-content">
          <div class="popup-prompt">
            为了保障您的账号安全，请先绑定手机号。
          </div>
          <div class="field">
            <input
              type="number"
              v-model="phone"
              :disabled="disabled"
              :placeholder="$t('请输入手机号码')"
              :class="{ pdr115: !userInfo.mobile }"
            />
            <div class="tel-right" v-if="!userInfo.mobile">
              <div
                class="clear"
                :class="{ transparent: phone == '' }"
                @click="phone = ''"
              ></div>
              <div class="country-code" @click="toPage('AreaCode')">
                <div class="country-code-text">+{{ areaCode }}</div>
                <div class="arrow-down"></div>
              </div>
            </div>
          </div>
          <div class="field">
            <input
              type="number"
              v-model="authCode"
              :placeholder="$t('请输入验证码')"
            />
            <div class="text" v-if="!ifCount" @click="captchaClick()">
              {{ $t('获取验证码') }}
            </div>
            <div class="text" v-else>
              {{ `${$t('重新获取')}${countdown}s` }}
            </div>
          </div>
        </div>
      </div>

      <div class="operation-bar">
        <div class="btn operation-btn cancel" @click="onCancel">
          {{ cancelText }}
        </div>
        <div class="btn operation-btn confirm" @click="onConfirm">
          {{ confirmText }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApibindPhone, ApiAuthCode } from '@/api/views/users';
import { mapActions } from 'vuex';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'BindMobileDialog',
  model: {
    prop: 'dialogShow',
    event: 'change',
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: this.dialogShow,
      title: '绑定手机',
      cancelText: '取消',
      confirmText: '确定',
      showCancel: false,
      showConfirm: true,

      phone: '', //手机号码
      authCode: '', //验证码
      countdown: 60, //倒计时
      ifCount: false, //倒计时开关
      disabled: false, //手机栏是否可以编辑
      imgUrl: '', //生成的图片地址
      captcha: null, //生成验证
    };
  },
  created() {
    this.setAreaCode(this.userInfo.country_code);
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  watch: {
    show(val) {
      this.$emit('change', val);
    },
    dialogShow(val) {
      this.show = val;
    },
    authCode() {
      if (this.authCode > 1000000)
        this.authCode = Math.floor(this.authCode / 10);
    },
  },
  computed: {
    ...mapGetters({
      areaCode: 'user/areaCode',
      initData: 'system/initData',
    }),
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    ...mapMutations({
      setAreaCode: 'user/setAreaCode',
    }),
    onCancel() {
      this.show = false;
      setTimeout(() => {
        this.phone = '';
        this.authCode = '';
      }, 200);
    },
    async onConfirm() {
      if (!this.authCode) {
        this.$toast(this.$t('请输入验证码'));
        return false;
      }
      if (!!this.userInfo.mobile) {
        this.show = true;
      } else {
        try {
          let res = await ApibindPhone({
            phone: this.phone,
            code: this.authCode,
            countryCode: this.areaCode,
          });
          if (res) {
            await this.SET_USER_INFO();
            this.$toast(res.msg);
            this.show = false;
          }
        } catch (error) {
        } finally {
          this.disabled = !this.disabled;
        }
      }
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (this.initData.captcha_is_open) {
        this.captcha.show();
      } else {
        this.getAuthCode();
      }
    },
    async getAuthCode(captcha) {
      // 获取验证码
      let params = {
        phone: this.phone,
        type: 5,
      };
      if (captcha) {
        params.randStr = captcha.randstr;
        params.ticket = captcha.ticket;
      }
      if (this.areaCode) {
        params.countryCode = this.areaCode;
      }
      let res = await ApiAuthCode(params);
      this.$toast(res.msg);
      // 出现倒计时，颜色变暗
      this.ifCount = !this.ifCount;
      let fun = setInterval(() => {
        this.countdown--;
        if (this.countdown === -1) {
          clearInterval(fun);
          this.countdown = 60;
          this.ifCount = !this.ifCount;
        }
      }, 1000);
    },
  },
  activated() {
    this.show = false;
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 355 * @rem;
  padding: 20 * @rem 0;

  .popup-title {
    font-size: 16 * @rem;
    color: #333333;
    text-align: center;
    font-weight: 600;
    line-height: 40 * @rem;
    overflow: hidden;
    white-space: nowrap;
    border-bottom: 1px solid #f3f3f8;
  }

  .popup-content {
    box-sizing: border-box;
    font-size: 14 * @rem;
    color: #777777;
    line-height: 20 * @rem;
    // text-align: center;
    padding: 0 31 * @rem;
    position: relative;
    .popup-close {
      position: absolute;
      top: -64 * @rem;
      right: 14 * @rem;
      img {
        width: 15 * @rem;
        height: 15 * @rem;
      }
    }
    .change-content {
      .popup-prompt {
        margin: 18 * @rem 0 19 * @rem 0;
      }
      //   margin-top: 19 * @rem;
      //   padding: 0 17 * @rem;
      .field {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 44 * @rem;
        border-radius: 6 * @rem;
        overflow: hidden;
        position: relative;
        &:not(:first-of-type) {
          margin-top: 15 * @rem;
        }
        .tel-right {
          width: 113 * @rem;
          height: 42 * @rem;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          .clear {
            width: 16 * @rem;
            height: 42 * @rem;
            padding: 0 10 * @rem;
            background-image: url(~@/assets/images/users/keyword-clear.png);
            background-size: 14 * @rem 14 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.transparent {
              opacity: 0;
            }
          }
          .country-code {
            display: flex;
            height: 42 * @rem;
            align-items: center;
            padding-left: 9 * @rem;
            position: relative;
            &::before {
              content: '';
              width: 1 * @rem;
              height: 11 * @rem;
              background-color: #dadada;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .country-code-text {
              font-size: 16 * @rem;
              color: #000000;
            }
            .arrow-down {
              width: 10 * @rem;
              height: 6 * @rem;
              .image-bg('~@/assets/images/users/arrow-down.png');
              margin-left: 5 * @rem;
              margin-top: 2 * @rem;
            }
          }
        }
        input {
          box-sizing: border-box;
          flex: 1;
          min-width: 0;
          height: 100%;
          padding: 0 5 * @rem;
          line-height: 44 * @rem;
          font-size: 14 * @rem;
          letter-spacing: 1 * @rem;
          background-color: #f4f4f4;
          padding: 0 20 * @rem;
          border-radius: 6 * @rem;
          &.pdr115 {
            padding-right: 115 * @rem;
          }
          &.pdr40 {
            padding-right: 40 * @rem;
          }
          &[disabled] {
            color: #000;
            opacity: 1;
          }
        }
        .text {
          box-sizing: border-box;
          border: 1 * @rem solid @themeColor;
          font-size: 14 * @rem;
          height: 42 * @rem;
          width: 116 * @rem;
          border-radius: 6 * @rem;
          color: @themeColor;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 10 * @rem;
          margin-right: 1 * @rem;
          &.text2 {
            color: #a4a4a4;
          }
        }
        .eyes {
          width: 18 * @rem;
          height: 44 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          position: absolute;
          right: 20 * @rem;
          top: 50%;
          transform: translateY(-50%);
          &.open {
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
    }
  }
  .operation-bar {
    display: flex;
    align-items: center;
    margin: 21 * @rem 0 0;
    padding: 0 31 * @rem;
    gap: 20 * @rem;
    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 36 * @rem;
      border-radius: 22 * @rem;
      font-size: 14 * @rem;
      &.confirm {
        background: @themeBg;
        color: #fff;
      }
      &.cancel {
        background: #f5f5f5;
        color: #7d7d7d;
      }
    }
  }
}
</style>
