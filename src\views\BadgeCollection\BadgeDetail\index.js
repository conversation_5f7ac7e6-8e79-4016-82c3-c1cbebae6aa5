// 粒子对象池
class ParticlePool {
    constructor(maxSize) {
        this.maxSize = maxSize;
        this.pool = new Array(maxSize); // 预分配数组空间
        this.currentSize = 0; // 跟踪池中实际元素的数量
    }

    // 从池中获取一个粒子
    acquire() {
        if (this.currentSize === 0) {
            return null; // 池中没有可用粒子
        }
        this.currentSize--;
        return this.pool[this.currentSize]; // 从末尾获取
    }

    // 将粒子释放回池中
    release(particle) {
        if (this.currentSize < this.maxSize) {
            this.pool[this.currentSize] = particle;
            this.currentSize++;
        } else {
            // 池已满，回收粒子资源（如果粒子有可释放的资源，如离屏 Canvas 位图）
            particle.recycle();
        }
    }

    // 调整粒子池大小
    resize(newSize) {
        // 缩小池时，释放多余的粒子
        while (this.currentSize > newSize) {
            const particle = this.pool[this.currentSize - 1];
            particle.recycle(); // 确保资源被清理
            this.currentSize--;
        }
        this.maxSize = newSize;
        // 如果池变大，更新 maxSize，并在需要时重新分配数组
        if (this.pool.length < newSize) {
            this.pool = new Array(newSize);
        }
    }

    // 清空粒子池
    clear() {
        for (let i = 0; i < this.currentSize; i++) {
            this.pool[i].recycle(); // 确保资源被清理
        }
        this.pool = [];
        this.currentSize = 0;
    }
}

class GentleParticleView {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        if (!this.canvas) {
            console.error(`Canvas 元素 id 为 "${canvasId}" 未找到。`);
            return;
        }
        this.context = this.canvas.getContext('2d');
        this.particles = [];
        this.particleImage = null; // 存储粒子图像对象
        this.animationFrameId = null; // 用于 requestAnimationFrame 的 ID
        this.random = new Random(); // 使用自定义的随机数类

        this.spawnWidth = 0; // 将在 onSizeChanged 中设置
        this.maxParticles = 20;
        this.lastSpawnTime = 0;
        this.spawnInterval = 300 + Math.floor(this.random.nextNumber() * 600);

        // 对象池优化
        this.particlePool = new ParticlePool(this.maxParticles);
        // 位图缓存（JavaScript 中的 Image 缓存）
        this.imageCache = new Map(); // 使用 Map 以支持更灵活的键类型

        this.init();
    }

    init() {
        // 浏览器会自动处理 Canvas 的硬件加速，或通过 CSS 属性如 `will-change`
        // 对于明确的像素完美缩放或抗锯齿，可以使用：
        // this.context.imageSmoothingEnabled = true;

        // 设置初始 Canvas 大小
        this.canvas.width = this.canvas.offsetWidth || 300;
        this.canvas.height = this.canvas.offsetHeight || 150;
        this.onSizeChanged(this.canvas.width, this.canvas.height);

        // 绑定 `this` 到 updateAndDraw 方法，以便在 requestAnimationFrame 中使用
        this.updateAndDraw = this.updateAndDraw.bind(this);
    }

    // 当 Canvas 尺寸改变时调用
    onSizeChanged(w, h) {
        this.spawnWidth = w / 4;
        // 当尺寸改变时，可能需要重新定位或清除并重新初始化现有粒子。
        // 目前只更新 spawnWidth。
    }

    // 绘制所有粒子
    drawParticles() {
        // 清除整个 Canvas
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
        for (const particle of this.particles) {
            particle.draw(this.context);
        }
    }

    // 更新粒子状态
    updateParticles() {
        const currentTime = performance.now(); // 使用高精度时间

        if (this.particles.length < this.maxParticles &&
            currentTime - this.lastSpawnTime > this.spawnInterval &&
            this.particleImage !== null) {

            let particle = this.particlePool.acquire();
            if (particle === null) {
                // 将 `this.particleImage` 传递给构造函数
                particle = new GentleParticle(this.canvas.width, this.canvas.height, this.particleImage, this.spawnWidth);
            } else {
                // 将 `this.particleImage` 传递给 reset
                particle.reset(this.canvas.width, this.canvas.height, this.particleImage, this.spawnWidth);
            }
            this.particles.push(particle);
            this.lastSpawnTime = currentTime;
            this.spawnInterval = 300 + Math.floor(this.random.nextNumber() * 500);
        }

        // 使用传统的 for 循环，以便安全地在迭代中移除元素
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.update();
            if (!particle.isAlive()) {
                this.particles.splice(i, 1); // 从数组中移除
                this.particlePool.release(particle);
            }
        }
    }

    // 更新并绘制（动画循环）
    updateAndDraw() {
        this.updateParticles();
        this.drawParticles();
        this.animationFrameId = requestAnimationFrame(this.updateAndDraw);
    }

    // 开始动画
    startAnimation() {
        if (!this.animationFrameId) { // 防止重复启动动画
            this.animationFrameId = requestAnimationFrame(this.updateAndDraw);
        }
    }

    // 停止动画
    stopAnimation() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    // 设置粒子图像
    setParticleImage(imageSource) {
        if (typeof imageSource === 'string') {
            // 如果是字符串，则假定为 URL 并加载
            const cached = this.imageCache.get(imageSource);
            if (cached) {
                this.particleImage = cached;
                for (const particle of this.particles) {
                    particle.image = this.particleImage;
                }
                return;
            }

            const img = new Image();
            img.onload = () => {
                this.particleImage = img;
                this.imageCache.set(imageSource, img);
                for (const particle of this.particles) {
                    particle.image = this.particleImage;
                }
                if (!this.animationFrameId) {
                    this.drawParticles();
                }
            };
            img.onerror = (e) => {
                console.error(`加载粒子图像失败: ${imageSource}`, e);
                this.particleImage = null;
            };
            img.src = imageSource;
        } else if (imageSource instanceof HTMLImageElement || imageSource instanceof HTMLCanvasElement) {
            // 如果是 Image 或 Canvas 元素，直接使用
            this.particleImage = imageSource;
            // 可选：如果希望重用此特定元素，可以将其缓存起来
            for (const particle of this.particles) {
                particle.image = this.particleImage;
            }
            if (!this.animationFrameId) {
                this.drawParticles();
            }
        } else {
            console.error("提供的 imageSource 无效。必须是字符串 (URL) 或 Image/Canvas 元素。");
            this.particleImage = null;
        }
    }

    // 设置最大粒子数量
    setMaxParticles(max) {
        this.maxParticles = Math.max(1, Math.min(max, 50));
        this.particlePool.resize(this.maxParticles);
    }

    // 销毁实例时调用，释放资源
    destroy() {
        this.stopAnimation();

        // 清除图像缓存 (可选，浏览器会处理图像内存)
        this.imageCache.clear();

        // 回收活动列表和池中的所有粒子
        for (const particle of this.particles) {
            particle.recycle();
        }
        this.particles = [];
        this.particlePool.clear();

        // 分离 Canvas 或 window 上附加的事件监听器（如果存在）
        this.canvas = null;
        this.context = null;
    }
}

/**
 * GentleParticle.js
 */
class GentleParticle {
    constructor(canvasWidth, canvasHeight, originalImage, spawnWidth) {
        this.random = new Random(); // 假设 Random 类或 Math.random() 被使用
        this.originalWidth = originalImage.width;
        this.originalHeight = originalImage.height;
        this.image = originalImage; // 存储原始图像引用
        this.reset(canvasWidth, canvasHeight, originalImage, spawnWidth);
    }

    // 重置粒子状态
    reset(canvasWidth, canvasHeight, originalImage, spawnWidth) {
        const spawnCenter = canvasWidth / 2;
        const spawnArea = spawnWidth * 0.6;
        this.x = spawnCenter - spawnArea / 2 + this.random.nextNumber() * spawnArea;
        this.y = canvasHeight + this.random.nextNumber() * 20;
        this.speed = 0.15 + this.random.nextNumber() * 0.3; // 控制粒子速度
        this.angle = (this.random.nextNumber() - 0.5) * 20;// 角度的随机范围
        this.rotation = this.random.nextNumber() * 360;
        this.scale = 0.5 + this.random.nextNumber() * 0.5;
        this.alpha = Math.floor(255 * this.scale); // 将 alpha 转换为整数
        this.targetY = canvasHeight * (this.random.nextNumber() * 0.3);
        this.windEffect = (this.random.nextNumber() - 0.5) * 0.1;// 风力影响的随机范围

        // 预计算三角函数值
        this.sinAngle = Math.sin(this.angle * Math.PI / 180); // 将角度转换为弧度
        this.cosAngle = Math.cos(this.angle * Math.PI / 180);

        // 在 JavaScript 中，通常不会为每个粒子创建新的缩放位图。
        // 我们会使用原始图像并在绘制时进行缩放。
        // 为了简单起见，我们只使用原始 `this.image` 并在 `draw` 中进行缩放。
    }

    // 更新粒子位置和透明度
    update() {
        this.x += this.sinAngle * 1.2 + this.windEffect;
        this.y -= this.speed;

        if (this.y < this.targetY + 200) {
            const progress = (this.y - this.targetY) / 300;
            this.alpha = Math.floor(255 * progress);
            if (this.alpha < 10) this.alpha = 0;
        }

        this.rotation += this.angle * 0.03;
    }

    // 绘制粒子
    draw(context) { // context 是 CanvasRenderingContext2D
        context.save();
        context.globalAlpha = this.alpha / 80; // 设置全局透明度

        // 计算缩放后的尺寸
        const scaledWidth = this.originalWidth * this.scale;
        const scaledHeight = this.originalHeight * this.scale;

        // 移动到粒子中心，旋转，然后移回
        context.translate(this.x + scaledWidth / 2, this.y + scaledHeight / 2);
        context.rotate(this.rotation * Math.PI / 180); // 将角度转换为弧度
        context.drawImage(this.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);

        context.restore(); // 恢复之前 Canvas 状态（包括透明度）
    }

    // 判断粒子是否仍然“存活”
    isAlive() {
        return this.y > this.targetY && this.alpha > 5;
    }

    // 回收粒子资源
    recycle() {
        // 在 JavaScript 中，通常不需要显式回收位图/图像。
        // 垃圾回收器会处理内存。但为了与 Java `bitmap = null` 一致，
        // 并且如果粒子被放回池中并可能与不同图像一起使用，可以清除图像引用。
        this.image = null;
    }
}

// 一个简单的随机数生成器，在需要时模仿 Java 的 Random。
// 在实际场景中，可以直接使用 Math.random()。
class Random {
    nextNumber() {
        return Math.random(); // 返回一个 [0, 1) 范围内的浮点数
    }
    // nextInt(n) 相当于 Math.floor(Math.random() * n)
}

/**
 * 生成一个带发光效果的白色五角星图案的Canvas元素。
 * @param {number} size - 星星的大小（Canvas的宽度和高度）。
 * @param {string} color - 星星的颜色。
 * @param {string} glowColor - 发光颜色。
 * @param {number} glowBlur - 发光模糊半径。
 * @returns {HTMLCanvasElement} 绘制有五角星图案的Canvas元素。
 */
export function createStarCanvas(size = 20, color = '#fff', glowColor = '#fff', glowBlur = 8) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    ctx.shadowColor = glowColor;
    ctx.shadowBlur = glowBlur;
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 5, 0, Math.PI * 2);
    ctx.fill();

    return canvas;
}
// export function createStarCanvas(size = 30, color = 'rgba(255,255,255,1)', glowColor = '#ADD8E6', glowBlur = 8) {
//     const canvas = document.createElement('canvas');
//     canvas.width = size;
//     canvas.height = size;
//     const ctx = canvas.getContext('2d');

//     const halfSize = size / 2;
//     // 外部半径，考虑发光模糊，避免星星被裁剪
//     const outerRadius = size / 2 - glowBlur;
//     // 内部半径，通常是外部半径的约 0.382 倍，或根据需要调整
//     const innerRadius = outerRadius * 0.4;
//     const numPoints = 5; // 五角星的角数

//     // 绘制发光效果 (先绘制模糊效果)
//     ctx.shadowColor = glowColor;
//     ctx.shadowBlur = glowBlur;
//     ctx.fillStyle = color;

//     ctx.beginPath();
//     for (let i = 0; i < numPoints * 2; i++) {
//         const radius = i % 2 === 0 ? outerRadius : innerRadius;
//         // 计算每个点的角度，从顶部开始
//         const angle = Math.PI / numPoints * i - Math.PI / 2; // 调整开始角度使一个角向上

//         const x = halfSize + Math.cos(angle) * radius;
//         const y = halfSize + Math.sin(angle) * radius;

//         if (i === 0) {
//             ctx.moveTo(x, y);
//         } else {
//             ctx.lineTo(x, y);
//         }
//     }
//     ctx.closePath();
//     ctx.fill();

//     // 清除阴影，以便后续绘制不会受到影响
//     ctx.shadowColor = 'transparent';
//     ctx.shadowBlur = 0;

//     // 再次绘制实心五角星，确保清晰度
//     ctx.fillStyle = color;
//     ctx.beginPath();
//     for (let i = 0; i < numPoints * 2; i++) {
//         const radius = i % 2 === 0 ? outerRadius * 0.9 : innerRadius * 0.9; // 稍微缩小，形成核心
//         const angle = Math.PI / numPoints * i - Math.PI / 2;

//         const x = halfSize + Math.cos(angle) * radius;
//         const y = halfSize + Math.sin(angle) * radius;

//         if (i === 0) {
//             ctx.moveTo(x, y);
//         } else {
//             ctx.lineTo(x, y);
//         }
//     }
//     ctx.closePath();
//     ctx.fill();

//     return canvas;
// }

// 导出所有类
export { GentleParticleView, GentleParticle, Random, ParticlePool };