<template>
  <div class="page">
    <nav-bar-2 :border="true" title="设置密保"> </nav-bar-2>
    <div class="main">
      <form class="form">
        <div class="field-new">
          <div class="field-title"> 请设置密保问题 </div>
          <security-question-bar
            class="field-content question-box"
            :selectedQuestion.sync="selectedQuestion"
          ></security-question-bar>
        </div>

        <div class="field-new">
          <div class="field-title"> 请设置密保答案 </div>
          <div class="field-content">
            <input
              type="text"
              v-model="answer"
              placeholder="最长可输入10个字符"
            />
          </div>
        </div>
      </form>
      <div class="button" @click="commit()">{{ $t('提交') }}</div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { ApiUserSetSecurity } from '@/api/views/users';

import securityQuestionBar from '@/components/security-question-bar';
export default {
  components: {
    securityQuestionBar,
  },
  data() {
    return {
      selectedQuestion: {
        title: '请选择问题',
      },
      answer: '',
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async commit() {
      this.$toast.loading('加载中');
      const params = {
        question_1: this.selectedQuestion.id,
        answer_1: this.answer,
      };
      const res = await ApiUserSetSecurity(params);
      await this.SET_USER_INFO();
      this.$toast.clear();
      this.back();
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .main {
    .field-new {
      padding: 0 13 * @rem;
      margin-top: 30 * @rem;
      .field-title {
        font-size: 16 * @rem;
        color: #565656;
        line-height: 22 * @rem;
      }
      .field-content {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        width: 100%;
        height: 42 * @rem;
        border-bottom: 0.5 * @rem solid #e0e0e0;
        margin-top: 4 * @rem;
        margin-bottom: 32 * @rem;
        position: relative;
        &.question-box {
          margin-top: 8 * @rem;
          border: 0;
        }
        &.gray-content {
          margin-top: 8 * @rem;
          border: 0;
          background: #f4f4f4;
          border-radius: 6 * @rem;
          padding: 0 11 * @rem;
          input {
            background: transparent;
          }
        }

        input {
          min-width: 0;
          flex: 1;
          height: 100%;
          line-height: 42 * @rem;
          font-size: 14 * @rem;
        }
        .eyes {
          width: 18 * @rem;
          height: 44 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          margin-left: 20 * @rem;
          &.open {
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
    }
    .form {
      padding: 0 17 * @rem;
      margin-top: 30 * @rem;

      .field {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 44 * @rem;
        border-radius: 22 * @rem;
        overflow: hidden;
        position: relative;
        &:not(:first-of-type) {
          margin-top: 20 * @rem;
        }
        .tel-right {
          width: 113 * @rem;
          height: 42 * @rem;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          .clear {
            width: 16 * @rem;
            height: 42 * @rem;
            padding: 0 10 * @rem;
            background-image: url(~@/assets/images/users/keyword-clear.png);
            background-size: 14 * @rem 14 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.transparent {
              opacity: 0;
            }
          }
          .country-code {
            display: flex;
            height: 42 * @rem;
            align-items: center;
            padding-left: 9 * @rem;
            position: relative;
            &::before {
              content: '';
              width: 1 * @rem;
              height: 11 * @rem;
              background-color: #dadada;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .country-code-text {
              font-size: 16 * @rem;
              color: #000000;
            }
            .arrow-down {
              width: 10 * @rem;
              height: 6 * @rem;
              .image-bg('~@/assets/images/users/arrow-down.png');
              margin-left: 5 * @rem;
              margin-top: 2 * @rem;
            }
          }
        }
        input {
          box-sizing: border-box;
          flex: 1;
          min-width: 0;
          height: 100%;
          padding: 0 5 * @rem;
          line-height: 44 * @rem;
          font-size: 14 * @rem;
          letter-spacing: 1 * @rem;
          background-color: #f4f4f4;
          padding: 0 20 * @rem;
          border-radius: 22 * @rem;
        }
        .text {
          box-sizing: border-box;
          border: 1 * @rem solid @themeColor;
          font-size: 14 * @rem;
          height: 42 * @rem;
          width: 116 * @rem;
          border-radius: 22 * @rem;
          color: @themeColor;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 10 * @rem;
          margin-right: 1 * @rem;
          &.text2 {
            color: #a4a4a4;
          }
        }
        .eyes {
          width: 18 * @rem;
          height: 44 * @rem;
          background-image: url(~@/assets/images/users/no-look.png);
          background-size: 18 * @rem 7 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          position: absolute;
          right: 20 * @rem;
          top: 50%;
          transform: translateY(-50%);
          &.open {
            background-image: url(~@/assets/images/users/look.png);
            background-size: 18 * @rem 12 * @rem;
          }
        }
      }
    }
    .button {
      height: 50 * @rem;
      margin: 50 * @rem 30 * @rem 0;
      text-align: center;
      line-height: 50 * @rem;
      background: @themeBg;
      border-radius: 25 * @rem;
      font-size: 16 * @rem;
      color: #ffffff;
      letter-spacing: 2px;
    }
  }
}
</style>
