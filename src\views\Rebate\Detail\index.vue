<template>
  <div class="rebate-detail">
    <nav-bar-2 :border="true" :title="$t('申请详情')"></nav-bar-2>
    <main>
      <div class="game-info">
        <img :src="info.titlepic" class="left" />
        <div class="center">
          <div class="name">{{ info.title }}</div>
          <div class="small-text">{{ info.game_area }}</div>
        </div>
        <div :style="{ color: info.status_color }" class="right">
          {{ info.status_str }}
        </div>
      </div>
      <div v-if="info.editor_remark" class="tips">{{ info.editor_remark }}</div>
      <div class="content">
        <div class="detail">
          <div class="item">
            <div class="left">{{ $t('充值账号') }}：</div>
            <div class="right">{{ userInfo.username }}</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('角色名') }}：</div>
            <div class="right">{{ info.game_role_name }}</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('角色ID') }}：</div>
            <div class="right">{{ info.game_role_id }}</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('充值金额') }}：</div>
            <div class="right red">{{ info.amount }}元</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('充值时间') }}：</div>
            <div class="right">{{ info.pay_date }}</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('返利道具') }}：</div>
            <div class="right">
              {{ info.rebate_content ? info.rebate_content : '' }}
            </div>
          </div>
          <div class="item">
            <div class="left">{{ $t('联系QQ') }}：</div>
            <div class="right">{{ info.contact }}</div>
          </div>
          <div class="item">
            <div class="left">{{ $t('备注') }}：</div>
            <div class="right">
              {{ info.user_remark ? info.user_remark : $t('无') }}
            </div>
          </div>
          <div class="item">
            <div class="left">{{ $t('申请时间') }}：</div>
            <div class="right">{{ time }}</div>
          </div>
        </div>
        <div class="explain">
          {{
            $t(
              '提交返利申请后我们会第一时间为您审核发放，若您提交后再次充值需提交返利申请，可联系QQ',
            )
          }}：{{ initData.configs.kefu.qq }}
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  name: 'RebateDetail',
  data() {
    return {
      info: {},
    };
  },
  computed: {
    time() {
      const res = this.$handleTimestamp(this.info.create_time);
      return `${res.year}-${res.date} ${res.time}:${res.second}`;
    },
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  created() {
    this.info = this.$route.query;
  },
};
</script>
<style lang="less" scoped>
main {
  .game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 17 * @rem 14 * @rem;
    .left {
      width: 60 * @rem;
      height: 60 * @rem;
      background: #eeeeee;
      border-radius: 10 * @rem;
      overflow: hidden;
    }
    .center {
      flex: 1;
      margin-left: 15 * @rem;
      .name {
        font-size: 16 * @rem;
      }
      .small-text {
        margin-top: 5 * @rem;
        font-size: 12 * @rem;
        color: #999999;
      }
    }
    .right {
      font-size: 16 * @rem;
      color: #999999;
    }
  }
  .tips {
    height: 40 * @rem;
    text-align: center;
    line-height: 40 * @rem;
    font-size: 14 * @rem;
    color: #f63838;
    background: #f6f6f6;
  }
  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 14 * @rem 18 * @rem;
    .detail {
      .item {
        display: flex;
        margin-top: 18 * @rem;
        font-size: 15 * @rem;
        .left {
          flex: 0 0 82 * @rem;
          text-align: right;
          color: #666666;
        }
        .right {
          flex: 1;
          &.red {
            color: #f70000;
          }
        }
      }
    }
    .explain {
      position: fixed;
      bottom: 20 * @rem;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      padding: 0 14 * @rem;
      line-height: 21 * @rem;
      font-size: 12 * @rem;
      color: #999999;
    }
  }
}
</style>
