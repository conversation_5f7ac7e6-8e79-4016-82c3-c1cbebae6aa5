<template>
  <div class="game-activity-page" :style="{ background: bgc }" v-if="info.id">
    <rubber-band :topColor="bgc" :bottomColor="bgc">
      <div class="placeholder" :style="{ background: bgc }"></div>
      <div class="game-panel" @click="goToGame">
        <div
          class="top-shadow"
          :style="{
            background: topShadow,
          }"
        ></div>
        <img class="game-banner" :src="info.bg_img" alt="" />
        <div
          class="bottom-shadow"
          :style="{
            background: bottomShadow,
          }"
        ></div>
      </div>
      <div class="download-section" v-if="info.bg_img">
        <div class="download-btn btn" @click="goToGame">
          <img src="@/assets/images/game-activity/download-btn.png" alt="" />
        </div>
      </div>
      <div class="section">
        <div class="section-title">
          {{ $t('每日登录游戏即可领取充值豪礼') }}
        </div>
        <div class="section-list">
          <div class="info-item" v-for="(item, index) in total" :key="index">
            <div class="info-icon">
              <img class="info-icon-img" :src="item.img" alt="" />
              <img class="frame" :src="formatFrame(item.card_type)" alt="" />
            </div>
            <div class="info-title">{{ item.title }}</div>
            <div class="get-btn had-get" v-if="item.is_receive">
              {{ $t('已领取') }}
            </div>
            <template v-else>
              <div class="get-btn" v-if="item.gold" @click="clickGet(item)">
                {{ $t('消耗') }}{{ item.gold }}{{ $t('金币') }}
              </div>
              <div class="get-btn" v-else @click="clickGet(item)">
                {{ $t('领取') }}
              </div>
            </template>
            <div class="desc" v-if="item.control">{{ item.control }}</div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="section-title">{{ $t('金币限时兑换商城') }}</div>
        <div class="section-list">
          <div class="info-item" v-for="(item, index) in gold" :key="index">
            <div class="info-icon">
              <img class="info-icon-img" :src="item.img" alt="" />
              <img class="frame" :src="formatFrame(item.card_type)" alt="" />
            </div>
            <div class="info-title">{{ item.title }}</div>
            <div class="get-btn had-get" v-if="item.is_receive">
              {{ $t('已领取') }}
            </div>
            <template v-else>
              <div class="get-btn" v-if="item.gold" @click="clickGet(item)">
                {{ $t('消耗') }}{{ item.gold }}{{ $t('金币') }}
              </div>
              <div class="get-btn" v-else @click="clickGet(item)">
                {{ $t('领取') }}
              </div>
            </template>
            <div class="desc" v-if="item.control">{{ item.control }}</div>
          </div>
        </div>
      </div>
      <div class="section coupon-section">
        <div class="section-title">{{ $t('财富会员专享区') }}</div>
        <div class="section-list">
          <div class="info-item" v-for="(item, index) in wealth" :key="index">
            <div class="info-icon">
              <img class="info-icon-img" :src="item.img" alt="" />
              <img class="frame" :src="formatFrame(item.card_type)" alt="" />
            </div>
            <div class="info-title">{{ item.title }}</div>
            <div class="get-btn had-get" v-if="item.is_receive">
              {{ $t('已领取') }}
            </div>
            <template v-else>
              <div class="get-btn" v-if="item.gold" @click="clickGet(item)">
                {{ $t('消耗') }}{{ item.gold }}{{ $t('金币') }}
              </div>
              <div class="get-btn" v-else @click="clickGet(item)">
                {{ $t('领取') }}
              </div>
            </template>
            <div class="desc" v-if="item.control">{{ item.control }}</div>
          </div>
        </div>
      </div>
      <div class="rule-section">
        <div class="rule-title">{{ $t('活动规则') }}</div>
        <div class="rule-content" v-html="info.explain"></div>
      </div>
    </rubber-band>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">{{ $t('礼包码') }}</div>
      </div>
      <div class="cardpass">{{ dialogInfo.cardpass }}</div>
      <div class="desc">{{ introduction }}</div>
      <div class="copy-btn btn" @click="copy(dialogInfo.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(info.game_id)"
    ></xh-create-tip-dialog>
    <!-- 小号金币提醒弹窗 -->
    <van-dialog
      v-model="useGoldPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="use-gold-popup"
    >
      <div class="popup-content">
        <div class="title">
          {{ $t('确认兑换') }}
        </div>
        <div class="info-item">
          <div class="info-icon">
            <img class="info-icon-img" :src="clickInfo.img" alt="" />
            <img
              class="frame"
              src="@/assets/images/game-activity/frame-icon.png"
              alt=""
            />
          </div>
          <div class="info-title">{{ clickInfo.title }}</div>
        </div>
        <div class="tips">
          {{ $t('确认消耗') }}{{ clickInfo.gold }}{{ $t('金币兑换') }}
        </div>
        <div class="confirm" @click="useGoldGet(clickInfo)">
          {{ $t('确定') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiIndexGetDownActivityInfo,
  ApiIndexReceiveRrize,
} from '@/api/views/home.js';
import { BOX_goToGame } from '@/utils/box.uni.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';
import { mapGetters, mapMutations } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog/index.vue';
import frameIcon1 from '@/assets/images/game-activity/frame-icon.png';
import frameIcon2 from '@/assets/images/game-activity/frame-icon-border-radius.png';
export default {
  components: {
    xhCreateTipDialog,
  },
  data() {
    return {
      id: 0,
      info: {},
      bgc: '',
      total: [],
      gold: [],
      wealth: [],
      clickInfo: {}, // 点击领取的项的信息
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      currentXiaohao: {}, //当前选择小号
      xiaohaoListShowItem: {},
      dialogInfo: {}, // 礼包码信息
      useGoldPopupShow: false, // 消耗金币提示弹窗
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoMap: 'gift/xiaohaoMap',
    }),
    topShadow() {
      return `linear-gradient(360deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    bottomShadow() {
      return `linear-gradient(180deg, ${this.hexToRgba} 0%, ${this.bgc} 100%)`;
    },
    hexToRgba() {
      return `rgba(${parseInt('0x' + this.bgc.slice(1, 3))},${parseInt(
        '0x' + this.bgc.slice(3, 5),
      )},${parseInt('0x' + this.bgc.slice(5, 7))},0)`;
    },
    introduction() {
      return `${this.$t('使用说明')}：${this.dialogInfo.cardtext}`;
    },
  },

  async activated() {
    this.id = this.$route.params.id;
    await this.getActivityInfo();
    await this.getCurrentXiaohaoId();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    ...mapMutations({
      setXiaohaoMap: 'gift/setXiaohaoMap',
    }),
    async getActivityInfo() {
      const res = await ApiIndexGetDownActivityInfo({ id: this.id });
      let { info, gold, total, wealth } = res.data.list;
      this.info = info;
      this.info.explain = this.info.explain.replace(/\n/g, '<br>');
      this.gold = gold;
      this.total = total;
      this.wealth = wealth;
      this.bgc = this.info.bg_color;
    },
    goToGame() {
      BOX_goToGame(
        {
          params: {
            id: this.info.game_id,
            gameInfo: this.info,
          },
        },
        { id: this.info.game_id },
      );
    },
    async clickGet(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      this.clickInfo = item;
      // card_type: 1->礼包 2->代金券
      if (!item.gold) {
        // 不需要消耗金币
        this.checkXiaohao();
      } else {
        // 需要消耗金币
        this.useGoldPopupShow = true;
        return false;
      }
    },
    checkXiaohao() {
      // 1.先判断是否存在小号
      if (!this.currentXiaohao.id) {
        this.createDialogShow = true;
        return false;
      }
      // 2.选择小号
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    async useGoldGet(item) {
      this.useGoldPopupShow = false;
      this.checkXiaohao();
    },
    async clickGoldGet(item) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
        return false;
      }
      // card_type: 1->礼包 2->代金券
      this.useGoldPopupShow = true;
      return false;
    },
    async handleGet(item, xh_id) {
      let params = { id: item.id };
      if (xh_id) {
        params.xh_id = xh_id;
      }
      const res = await ApiIndexReceiveRrize(params);
      if (item.card_type == 1) {
        // 礼包
        this.showCopyBar(res.data);
      } else {
        // 代金券
        this.$toast(res.msg);
      }
      this.getActivityInfo();
    },
    xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      this.xiaohaoListShow = false;
    },
    async getCurrentXiaohaoId() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.info.game_id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
        // 判断是否有已选择小号
        let flag = list.some(item => {
          return item.id == this.xiaohaoMap[this.info.game_id]?.id;
        });
        if (flag) {
          this.currentXiaohao = this.xiaohaoMap[this.info.game_id];
        } else {
          this.currentXiaohao = list[0];
          this.setXiaohaoMap([this.info.game_id, list[0]]);
        }
      } else {
        this.xiaohaoList = [];
        this.currentXiaohao = {};
        this.setXiaohaoMap([this.info.game_id, {}]);
        if (text) this.fixedText = text;
      }
    },
    changeXiaohao() {
      this.xiaohaoListShowItem = this.currentXiaohao;
      this.xhDialogShow = true;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      this.currentXiaohao = this.xiaohaoListShowItem;
      this.setXiaohaoMap([this.info.game_id, this.currentXiaohao]);
      this.handleGet(this.clickInfo, this.currentXiaohao.id);
      this.$toast.clear();
    },
    showCopyBar(info) {
      this.dialogInfo = info;
      this.copyDialogShow = true;
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    formatFrame(card_type) {
      return card_type == 1 ? frameIcon1 : frameIcon2;
    },
  },
};
</script>

<style lang="less" scoped>
.game-activity-page {
  min-height: 100vh;
  padding-bottom: 40 * @rem;
  .placeholder {
    position: relative;
    display: block;
    width: 100%;
    height: calc(56 * @rem + @safeAreaTop);
    height: calc(56 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .game-panel {
    position: relative;
    width: 100%;
    // height: 375 * @rem;
    // padding-bottom: 50 * @rem;
    .top-shadow {
      width: 100%;
      height: 150 * @rem;
      position: absolute;
      left: 0;
      top: -1 * @rem;
      z-index: 2;
    }
    .bottom-shadow {
      width: 100%;
      height: 90 * @rem;
      position: absolute;
      left: 0;
      bottom: 0 * @rem;
      z-index: 2;
    }
  }
  .download-section {
    margin-top: 10 * @rem;
    .download-btn {
      margin: 0 auto;
      width: 166 * @rem;
      height: 46 * @rem;
    }
  }
  .section {
    padding: 14 * @rem 0 16 * @rem;
    &.coupon-section {
      .section-list {
        .info-item {
          margin-top: 15 * @rem;
          &:not(:first-of-type) {
            margin-left: 9 * @rem;
          }
        }
      }
    }
    .section-title {
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 22 * @rem;
      text-align: center;
    }
    .section-list {
      display: flex;
      justify-content: center;
      margin-top: 12 * @rem;
      flex-wrap: wrap;
      .info-item {
        width: 80 * @rem;
        margin-top: 15 * @rem;
        flex-grow: 0;
        flex-shrink: 0;
        &:not(:nth-of-type(3n + 1)) {
          margin-left: 37 * @rem;
        }
        .info-icon {
          width: 80 * @rem;
          height: 80 * @rem;
          position: relative;
          .info-icon-img {
            object-fit: cover;
            position: absolute;
            width: 70 * @rem;
            height: 70 * @rem;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
          .frame {
            position: absolute;
            left: 0;
            top: 0;
          }
        }
        .info-title {
          text-align: center;
          font-size: 11 * @rem;
          line-height: 16 * @rem;
          color: #ffde6a;
          font-weight: 600;
          margin-top: 8 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .get-btn {
          text-align: center;
          width: 76 * @rem;
          height: 26 * @rem;
          .image-bg('~@/assets/images/game-activity/get-btn.png');
          color: #a25707;
          font-size: 10 * @rem;
          line-height: 23 * @rem;
          margin: 8 * @rem auto 0;
          font-weight: 600;
          &.had-get {
            .image-bg('~@/assets/images/game-activity/had-get-btn.png');
            color: #fff;
          }
        }
        .desc {
          text-align: center;
          font-size: 10 * @rem;
          line-height: 14 * @rem;
          color: #ffffff;
          font-weight: 600;
          margin-top: 6 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .rule-section {
    box-sizing: border-box;
    width: 348 * @rem;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 15 * @rem;
    margin: 12 * @rem auto;
    padding: 13 * @rem 24 * @rem 20 * @rem;
    .rule-title {
      text-align: center;
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 22 * @rem;
    }
    .rule-content {
      font-size: 12 * @rem;
      line-height: 20 * @rem;
      color: #ffffff;
      font-size: 12 * @rem;
      margin-top: 7 * @rem;
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;

        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
.use-gold-popup {
  box-sizing: border-box;
  width: 244 * @rem;
  background-color: #fff;
  border-radius: 20 * @rem;
  padding: 20 * @rem 10 * @rem 20 * @rem;
  .popup-content {
    .title {
      font-size: 16 * @rem;
      font-weight: 600;
      color: #000;
      text-align: center;
      line-height: 25 * @rem;
    }
    .info-item {
      width: 200 * @rem;
      margin: 20 * @rem auto 0;
      .info-icon {
        width: 80 * @rem;
        height: 80 * @rem;
        position: relative;
        margin: 0 auto;
        .info-icon-img {
          object-fit: cover;
          position: absolute;
          width: 70 * @rem;
          height: 70 * @rem;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        .frame {
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .info-title {
        text-align: center;
        font-size: 12 * @rem;
        line-height: 16 * @rem;
        color: @themeColor;
        font-weight: 600;
        margin-top: 8 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .tips {
      font-size: 12 * @rem;
      color: #333;
      text-align: center;
      margin-top: 10 * @rem;
    }
    .confirm {
      width: 102 * @rem;
      height: 35 * @rem;
      color: #ffffff;
      font-size: 13 * @rem;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      background: @themeBg;
      border-radius: 18 * @rem;
      margin: 18 * @rem auto 0;
    }
  }
}
.copy-dialog {
  box-sizing: border-box;
  width: 244 * @rem;
  border-radius: 12 * @rem;
  background-color: #fff;
  padding: 20 * @rem 16 * @rem 22 * @rem;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    .title-icon {
      width: 18 * @rem;
      height: 18 * @rem;
      .image-bg('~@/assets/images/games/gift-title-icon.png');
    }
    .title-text {
      font-size: 18 * @rem;
      color: #000000;
      font-weight: 500;
      margin-left: 4 * @rem;
    }
  }
  .cardpass {
    box-sizing: border-box;
    width: 209 * @rem;
    height: 39 * @rem;
    background-color: #f4f4f4;
    border-radius: 6 * @rem;
    display: flex;
    align-items: center;
    padding: 0 10 * @rem;
    margin-top: 13 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    font-weight: 400;
  }
  .desc {
    font-size: 12 * @rem;
    line-height: 17 * @rem;
    color: #757575;
    font-weight: 400;
    margin-top: 13 * @rem;
    padding: 0 5 * @rem;
  }
  .copy-btn {
    width: 186 * @rem;
    height: 36 * @rem;
    margin: 20 * @rem auto 0;
    background: @themeBg;
    border-radius: 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13 * @rem;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
