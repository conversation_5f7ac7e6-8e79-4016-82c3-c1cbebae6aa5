<template>
  <div class="game-recharge-order">
    <nav-bar-2 :border="true" :title="$t('订单中心')"></nav-bar-2>
    <main>
      <van-tabs
        v-model="active"
        swipeable
        animated
        :title-active-color="themeColorLess"
        title-inactive-color="#666666"
        :border="true"
        line-width="33.3%"
        :color="themeColorLess"
        line-height="2px"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :title="item.title"
        >
          <order-list :status="item.status"></order-list>
        </van-tab>
      </van-tabs>
    </main>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
import OrderList from './OrderList';
export default {
  name: 'GameRechargeOrder',
  components: {
    OrderList,
  },
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          status: 0,
          title: this.$t('全部'),
        },
        {
          status: 2,
          title: this.$t('已支付'),
        },
        {
          status: 1,
          title: this.$t('未支付'),
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.game-recharge-order {
  /deep/ .van-tab__pane {
    height: 100%;
  }
  main {
    // box-sizing: border-box;
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    left: 0;
    width: 100%;
    .fixed-center;
    /deep/ .van-tab {
      font-size: 15px;
    }
  }
  main /deep/ .van-tabs.van-tabs--line {
    height: 100%;
  }
  main /deep/ .van-tabs--line .van-tabs__wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #ffffff;
  }
  main /deep/ .van-tabs--line .van-tabs__content {
    box-sizing: border-box;
    height: 100%;
    padding-top: 44px;
    overflow: hidden;
  }
}
</style>
