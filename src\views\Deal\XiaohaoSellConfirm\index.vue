<template>
  <div class="page xiaohao-sell-confirm-page">
    <nav-bar-2 :title="$t('账号交易')" :border="true"></nav-bar-2>
    <div class="main">
      <div class="form">
        <div class="item">
          <div class="title">{{ $t('标题') }}</div>
          <div class="text">
            <input
              type="text"
              v-model="title"
              :placeholder="$t('说明核心卖点，5~20字')"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('详情描述（选填）') }}</div>
          <div class="text">
            <textarea
              :maxlength="100"
              :rows="2"
              v-model="desc"
              :placeholder="
                $t(
                  '可描述角色等级、装备、属性等，10~100字。完善的描述可快速有效促成交易哦~',
                )
              "
            ></textarea>
          </div>
        </div>
        <div class="item">
          <div class="title">{{ $t('二级密码（有则必填）') }}</div>
          <div class="text">
            <textarea
              :maxlength="100"
              :rows="2"
              v-model="secret"
              :placeholder="
                $t('填写示例：上回密码666666。该密码仅审核人员及最终买家可见。')
              "
            ></textarea>
          </div>
        </div>
        <div class="item">
          <div class="title">
            {{ $t('游戏截图') }}<span>（{{ $t('3-9张游戏内截图') }}）</span>
          </div>
          <div class="text">
            <van-uploader
              v-model="imageFileList"
              :after-read="afterRead"
              @delete="deletePic"
              :max-count="9"
              accept="image/*"
              :before-read="beforeRead"
              :multiple="true"
            >
              <div class="upload-btn">
                <img src="@/assets/images/deal/upload-icon.png" />
              </div>
            </van-uploader>
          </div>
        </div>
        <div class="item">
          <div class="title">
            {{ $t('游戏视频')
            }}<span>（{{ $t('仅可上传1个游戏视频,非必选') }}）</span>
          </div>
          <div class="text">
            <div class="upload-btn" @click="handleVideo">
              <img
                v-if="!videoUrl"
                src="@/assets/images/deal/upload-icon.png"
              />
              <div v-else class="had-video">{{ $t('已选') }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-bar fixed-center" v-if="!isRewrite">
      <div class="confirm-btn btn" @click="confirmSell">
        {{ $t('确认出售') }}
      </div>
      <!-- <div class="confirm-btn orange btn" @click="selectUser">
        {{ $t("指定玩家出售") }}
      </div> -->
    </div>
    <div class="bottom-bar fixed-center" v-else>
      <div class="rewrite-confirm-btn btn" @click="confirmSell">
        {{ $t('确认提交') }}
      </div>
    </div>
    <!-- 上传图片提示窗 -->
    <van-dialog
      v-model="uploadTipShow"
      :lock-scroll="false"
      :show-confirm-button="true"
      :confirmButtonText="$t('确定')"
      confirmButtonColor="#47A83A"
      @confirm="uploadTipFlag = false"
      :message="
        $t(
          '请如实上传含有游戏内容的图片,若提交的截图与账号实际内容不符，将会导致审核不通过。',
        )
      "
      messageAlign="left"
    >
    </van-dialog>
    <!-- 选择视频弹窗 -->
    <van-dialog
      class="video-van-dialog"
      v-model="videoDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="video-dialog">
        <div class="close-video-dialog btn" @click="closeVideoDialog"></div>
        <div class="title">{{ $t('选择小号视频') }}</div>
        <div class="video-tips">
          {{ $t('小号出售仅可上传1个当前游戏视频，请选择当前小号视频。')
          }}<span @click="toGameDetail"
            >{{ $t('前往游戏录制视频') }} &gt;&gt;</span
          >
        </div>
        <div class="video-container">
          <content-empty
            :tips="$t('暂无小号视频')"
            v-if="!videoList.length"
          ></content-empty>
          <div class="video-list" v-else>
            <div
              class="video-item"
              v-for="(item, index) in videoList"
              :key="index"
              @click="switchVideo(item)"
            >
              <div class="video-player">
                <video :src="item.video_url"></video>
              </div>
              <div class="video-right">
                <div
                  class="select"
                  :class="{ selected: selectedVideo == item.video_url }"
                ></div>
                <div class="date">{{ item.create_time | formatTime }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="use-video btn" @click="useVideo">{{ $t('使用视频') }}</div>
      </div>
    </van-dialog>
    <sell-tip-dialog
      v-model="sellTipShow"
      :isRewrite="isRewrite"
    ></sell-tip-dialog>
  </div>
</template>

<script>
import { ApiUploadImage } from '@/api/views/system';
import SellTipDialog from '../components/sell-tip-dialog';
import md5 from 'js-md5';
import { mapGetters, mapMutations } from 'vuex';
import { ApiXiaohaoGetXiaoHaoVideo } from '@/api/views/xiaohao.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'XiaohaoSellConfirm',
  components: {
    SellTipDialog,
  },
  data() {
    return {
      imageFileList: [],
      uploadTipShow: false, // 上传提示是否显示
      uploadTipFlag: true, // 上传提示是否可显示
      sellTipShow: false, // 确认出售弹窗
      videoDialogShow: false, // 选择视频弹窗
      title: '',
      desc: '',
      secret: '',
      images: [],
      videoList: [],
      videoUrl: '',
      selectedVideo: '', // 选择video
      xh_game: {},
      specifyUser: '', //指定出售玩家
      isRewrite: false,
    };
  },
  computed: {
    ...mapGetters({
      xiaohaoSellInfo: 'deal/xiaohaoSellInfo',
    }),
  },
  filters: {
    formatTime(val) {
      let { year, date, time } = handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
  },
  activated() {
    if (this.xiaohaoSellInfo.title) {
      this.title = this.xiaohaoSellInfo.title;
    } else {
      this.title = '';
    }
    if (this.xiaohaoSellInfo.desc) {
      this.desc = this.xiaohaoSellInfo.desc;
    } else {
      this.desc = '';
    }
    if (this.xiaohaoSellInfo.secret) {
      this.secret = this.xiaohaoSellInfo.secret;
    } else {
      this.secret = '';
    }
    if (this.xiaohaoSellInfo.videoUrl) {
      this.videoUrl = this.xiaohaoSellInfo.videoUrl;
    } else {
      this.videoUrl = '';
    }
    if (!this.xiaohaoSellInfo.images) {
      this.imageFileList = [];
      this.images = [];
    }
    if (this.xiaohaoSellInfo.mem_id) {
      this.isRewrite = true;
    } else {
      this.isRewrite = false;
    }
  },
  methods: {
    ...mapMutations({
      setXiaohaoSellInfo: 'deal/setXiaohaoSellInfo',
    }),
    beforeRead() {
      if (this.uploadTipFlag) {
        this.uploadTipShow = true;
        this.uploadTipFlag = false;
      }
      return true;
    },
    handleValidate() {
      if (!this.title) {
        this.$toast(this.$t('请输入标题!'));
        return false;
      }
      if (this.title.length < 5) {
        this.$toast(this.$t('标题不能少于5个字!'));
        return false;
      }
      if (this.images.length < 3) {
        this.$toast(this.$t('游戏截图不能少于3张!'));
        return false;
      }
      return true;
    },
    closeVideoDialog() {
      this.videoDialogShow = false;
      if (!this.selectedVideo) {
        this.videoUrl = '';
      }
    },
    async handleVideo() {
      this.videoDialogShow = true;
      await this.getVideo();
    },
    useVideo() {
      this.videoDialogShow = false;
      this.videoUrl = this.selectedVideo;
    },
    switchVideo(item) {
      if (this.selectedVideo == item.video_url) {
        this.selectedVideo = '';
      } else {
        this.selectedVideo = item.video_url;
      }
    },
    async getVideo() {
      const res = await ApiXiaohaoGetXiaoHaoVideo({
        xhId: this.xiaohaoSellInfo.xh_id,
      });
      this.xh_game = res.data.game;
      this.videoList = res.data.list;
      if (this.videoList.length && !this.videoUrl) {
        this.selectedVideo = this.videoList[0].video_url;
      } else if (this.videoUrl) {
        this.selectedVideo = this.videoUrl;
      }
    },
    toGameDetail() {
      this.videoDialogShow = false;
      this.toPage('GameDetail', {
        id: this.xh_game.id,
      });
    },
    selectUser() {
      if (!this.handleValidate()) {
        return false;
      }
      let info = {
        title: this.title,
        desc: this.desc,
        secret: this.secret,
        images: this.images,
        videoUrl: this.videoUrl,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...info });
      this.toPage('SelectUser');
    },
    confirmSell() {
      if (!this.handleValidate()) {
        return false;
      }
      let info = {
        title: this.title,
        desc: this.desc,
        secret: this.secret,
        images: this.images,
        videoUrl: this.videoUrl,
      };
      this.setXiaohaoSellInfo({ ...this.xiaohaoSellInfo, ...info });
      this.sellTipShow = true;
    },
    async handleUpload(file) {
      // 修改图片上传状态
      file.status = 'uploading';
      let uploadFile = file.file;
      let data = {};
      let time = Math.floor(new Date().getTime() / 1000);
      data.type = 'etc'; //写死
      data.image = uploadFile;
      data.time = time;
      data.name = uploadFile.name;
      data.auth_key = md5('FoFHDov5mTn3OriLeE9u3loOnIL6tp7q' + time);
      try {
        const res = await ApiUploadImage(data);
        this.images.push(res.data.url);
        file.status = 'done';
        file.message = this.$t('上传成功');
      } catch (err) {
        file.status = 'failed';
        file.message = this.$t('上传失败');
      }
    },
    async afterRead(file) {
      if (!file.length) {
        await this.handleUpload(file);
      } else {
        for (var item of file) {
         this.handleUpload(item);
        }
      }
    },
    deletePic(file, detail) {
      this.images.splice(detail.index, 1);
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-sell-confirm-page {
  .main {
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
    .form {
      padding: 0 14 * @rem;
      .item {
        padding: 10 * @rem 0;
        .title {
          font-size: 16 * @rem;
          color: #333333;
          font-weight: bold;
          display: flex;
          align-items: center;
          span {
            font-size: 14 * @rem;
            color: #666666;
          }
        }
        .text {
          font-size: 15 * @rem;
          color: #333333;
          padding: 10 * @rem 0;
          width: 100%;
          input {
            width: 100%;
          }
          textarea {
            border: 0;
            outline: none;
            display: block;
            width: 100%;
            line-height: 22 * @rem;
            padding: 0;
            margin: 0;
          }
          .upload-btn {
            width: 80px;
            height: 80px;
            .had-video {
              width: 100%;
              height: 100%;
              background-color: rgb(246, 246, 246);
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 16px;
              color: rgb(200, 200, 200);
              font-weight: bold;
            }
          }
        }
      }
    }
  }
  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(70 * @rem + @safeAreaBottom);
    height: calc(70 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .confirm-btn {
      width: 330 * @rem;
      // width: 165 * @rem;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
      background: @themeBg;
      border-radius: 8 * @rem;
      margin: 0 auto;
      &.orange {
        background: linear-gradient(90deg, #f7be45, #f5b51b);
      }
    }
    .rewrite-confirm-btn {
      flex: 1;
      height: 50 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
      background: @themeBg;
      border-radius: 8 * @rem;
      margin: 0 auto;
    }
  }
}
/deep/ .video-van-dialog {
  width: 310 * @rem;
}
.video-dialog {
  padding: 23 * @rem 17 * @rem;
  position: relative;
  .close-video-dialog {
    position: absolute;
    right: 0;
    top: 0;
    width: 53 * @rem;
    height: 53 * @rem;
    background: url(~@/assets/images/close-dialog.png) center center no-repeat;
    background-size: 17 * @rem 17 * @rem;
  }
  .title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: bold;
    text-align: center;
  }
  .video-tips {
    font-size: 13 * @rem;
    color: #333333;
    line-height: 18 * @rem;
    margin-top: 20 * @rem;
    span {
      color: #ffa200;
    }
  }
  .video-container {
    .video-list {
      .video-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20 * @rem;
        .video-player {
          width: 130 * @rem;
          height: 75 * @rem;
          border-radius: 5 * @rem;
          background-color: #000;
          overflow: hidden;
          video {
            width: 130 * @rem;
            height: 75 * @rem;
            object-fit: cover;
          }
        }
        .video-right {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          .select {
            width: 20 * @rem;
            height: 20 * @rem;
            background: url(~@/assets/images/deal/video-selected-icon.png)
              no-repeat;
            background-size: 20 * @rem 20 * @rem;
            opacity: 0;
            &.selected {
              opacity: 1;
            }
          }
          .date {
            margin-top: 30 * @rem;
            font-size: 12 * @rem;
            color: #999999;
          }
        }
      }
    }
  }
  .use-video {
    width: 230 * @rem;
    height: 40 * @rem;
    background: @themeBg;
    border-radius: 5 * @rem;
    font-size: 15 * @rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 23 * @rem auto 0;
  }
}

::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #b4b4b4;
  font-size: 15px;
}

::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #b4b4b4;
  font-size: 15px;
}

:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #b4b4b4;
  font-size: 15px;
}
</style>
