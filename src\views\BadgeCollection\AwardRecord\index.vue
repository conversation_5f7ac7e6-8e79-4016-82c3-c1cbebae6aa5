<template>
  <div
    class="award-record-page"
    :class="{
      centered: !isLoading,
    }"
  >
    <rubber-band :topColor="'#151412'" :bottomColor="'#151412'">
      <loading-indicator v-if="!isLoading" />
      <template v-else>
        <nav-bar-2
          class="nav-bar"
          :title="title"
          :placeholder="false"
          :bgStyle="bgStyle"
          :bgColor="`rgba(28, 23, 19, ${navbarOpacity})`"
          :azShow="true"
        >
          <template #right>
            <div class="share-btn" @click="shareBadge()"></div>
          </template>
        </nav-bar-2>
        <div class="top-bg"> </div>
        <div class="top-bar">
          <div class="user-info">
            <user-avatar class="user-avatar"></user-avatar>
            <div class="user-name">{{ userInfo.nickname }}</div>
          </div>
        </div>
        <div
          class="empty-box"
          v-if="awardRecordInfo && !awardRecordInfo?.length"
        >
          <default-not-found-page notFoundText="暂未获得徽章哦～" />
        </div>
        <div class="main" v-else>
          <yy-list
            class="award-record-container"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :empty="empty"
            :check="false"
            :tips="$t('暂无数据')"
          >
            <div
              class="award-record-box"
              v-for="yearData in awardRecordInfo"
              :key="yearData.year"
            >
              <div
                class="step-title-sticky-anchor"
                :ref="'anchor-' + yearData.year"
              ></div>
              <div
                class="step-title"
                :class="{ fixed: fixedYear === yearData.year }"
              >
                {{ yearData.year }}
              </div>
              <!-- 吸顶占位 -->
              <div
                class="step-title-placeholder"
                v-if="fixedYear === yearData.year"
              >
                {{ yearData.year }}
              </div>
              <div class="step-list">
                <template v-for="monthData in yearData.months">
                  <div class="step-sub-title" :key="monthData.month">{{
                    monthData.month
                  }}</div>
                  <div class="step-item" :key="`item-${monthData.month}`">
                    <div class="step-card">
                      <div class="badge-box">
                        <div
                          class="badge-content"
                          v-for="(item, index) in monthData.badges"
                          :key="index"
                        >
                          <div class="badge-item">
                            <div class="content" @click="showBadgeDetail(item)">
                              <div class="badge-image">
                                <img :src="item.icon_url" />
                              </div>
                              <div class="badge-name">{{ item.name }}</div>
                              <!-- <div class="badge-time"
                                >{{ formatDate(item.create_time) }} 获得</div
                              > -->
                              <div class="badge-text" v-if="item.arrive_text">
                                {{ item.arrive_text }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </yy-list>
        </div>
      </template>
    </rubber-band>
  </div>
</template>

<script>
import LoadingIndicator from '@/components/loading-Indicator';
import { ApiUserBadgeMyBadgeLog } from '@/api/views/badgeCollection.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import { throttle } from '@/utils/tools.js';
export default {
  name: 'AwardRecord',
  props: {},
  components: {
    LoadingIndicator,
  },
  data() {
    return {
      title: '',
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      awardRecordInfo: [],
      firstBadge: {},
      isLoading: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      page: 1,
      listRows: 30,
      fixedYear: '', // 当前吸顶的年份
      navBarHeight: 0, // nav-bar 高度（用来判断是否需要隐藏）
      // 节流函数
      shareBadgeThrottled: null,
    };
  },
  methods: {
    // 分享徽章
    async _shareBadge() {
      if (!Object.keys(this.firstBadge).length) {
        this.$toast('没有该徽章哦～');
        return;
      }
      const res = await ApiCommonShareInfo({
        type: 15,
        id: this.firstBadge.type_id,
        source_id: this.firstBadge.record_id,
      });
      this.$nextTick(() => {
        setTimeout(() => {
          this.$copyText(res.data.share_text + ' ' + res.data.url).then(
            res => {
              this.$toast('链接已复制');
            },
            err => {
              this.$dialog.alert({
                message: '复制失败，请稍后重试',
                lockScroll: false,
              });
            },
          );
        }, 100);
      });
    },

    // 分享徽章
    shareBadge() {
      if (!this.shareBadgeThrottled) {
        this.shareBadgeThrottled = throttle(this._shareBadge, 1500);
      }
      this.shareBadgeThrottled();
    },
    async getUserBadgeMyBadgeLog(action = 1) {
      try {
        if (action === 1) {
          this.finished = false;
          this.page = 1;
          this.firstBadge = [];
        } else {
          if (this.finished) {
            return;
          }
          this.page++;
        }
        const res = await ApiUserBadgeMyBadgeLog({
          page: this.page,
          listRows: this.listRows,
        });
        let { list } = res.data;
        let formatList = this.formatAwardData(list);

        if (action === 1 || this.page == 1) {
          this.awardRecordInfo = [];
          this.firstBadge = list[0];
          if (!list.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
          this.awardRecordInfo = formatList;
        } else {
          // 合并新的徽章数据到现有结构中
          this.mergeAwardData(formatList);
        }

        // console.log(list);
        // console.log(this.awardRecordInfo);
        this.loadingObj.loading = false;
        if (list.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished) {
            this.finished = false;
          }
        }
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
    // 合并新的徽章数据到现有结构
    mergeAwardData(newData) {
      if (!newData || !newData.length) return;

      newData.forEach(yearItem => {
        // 查找是否已有该年份
        let existingYearItem = this.awardRecordInfo.find(
          y => y.year === yearItem.year,
        );

        if (existingYearItem) {
          // 如果已有该年份 则遍历其中的月份
          yearItem.months.forEach(monthItem => {
            // 查找是否已有该月份
            let existingMonthItem = existingYearItem.months.find(
              m => m.month === monthItem.month,
            );

            if (existingMonthItem) {
              // 已有该月份 新的徽章添加到现有月份的徽章数组中
              existingMonthItem.badges.push(...monthItem.badges);
            } else {
              existingYearItem.months.push(monthItem);
            }
          });
        } else {
          this.awardRecordInfo.push(yearItem);
        }
      });

      // 按照月份排序
      this.awardRecordInfo.forEach(yearItem => {
        yearItem.months.sort((a, b) => {
          return parseInt(b.month) - parseInt(a.month);
        });
      });

      // 按照年份排序
      this.awardRecordInfo.sort((a, b) => {
        return parseInt(b.year) - parseInt(a.year);
      });
    },
    async onRefresh() {
      await this.getUserBadgeMyBadgeLog();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getUserBadgeMyBadgeLog(2);
    },
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
    // 处理徽章时间线
    formatAwardData(data) {
      const result = [];

      data.sort((a, b) => b.create_time - a.create_time);

      data.forEach(item => {
        const date = new Date(item.create_time * 1000);
        const year = date.getFullYear() + '年';
        const month = date.getMonth() + 1 + '月';

        // 构建 badge 对象
        const badge = item;

        // 查找年份
        let yearGroup = result.find(y => y.year == year);
        if (!yearGroup) {
          yearGroup = {
            year,
            months: [],
          };
          result.push(yearGroup);
        }

        // 查找月份
        let monthGroup = yearGroup.months.find(m => m.month == month);
        if (!monthGroup) {
          monthGroup = {
            month,
            badges: [],
          };
          yearGroup.months.push(monthGroup);
        }

        monthGroup.badges.push(badge);
      });

      return result;
    },
    // 点击徽章详情
    showBadgeDetail(item) {
      console.log(item);
      this.$router.push({
        name: 'BadgeDetail',
        params: {
          id: item.id,
          type_id: item.type_id,
          record_id: item.record_id ? item.record_id : 0,
          is_valid: 1, //成就徽章进入隐藏详情页已经过期的 0隐藏 1显示
        },
      });
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.title = '获取记录';
        // this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.title = '';
        // this.bgStyle = 'transparent-white';
      }
    },
    getNavBarHeight() {
      const navBar = document.querySelector('.nav-bar');
      this.navBarHeight = navBar ? navBar.offsetHeight : 50;
    },
    // 优化版：动态吸顶逻辑增强，支持新增数据后刷新吸顶判断
    handleYearSticky() {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const anchors = this.$refs;

      const visible = [];

      for (let i = 0; i < this.awardRecordInfo.length; i++) {
        const year = this.awardRecordInfo[i].year;
        const ref = anchors['anchor-' + year];
        const el = Array.isArray(ref) ? ref[0] : ref;
        if (el) {
          const offsetTop = el.getBoundingClientRect().top;
          visible.push({ year, offsetTop });
        }
      }

      const active = visible
        .filter(v => v.offsetTop <= this.navBarHeight + 2)
        .sort((a, b) => b.offsetTop - a.offsetTop)[0];

      // console.log(active);
      if (active) {
        this.fixedYear = active.year;
      } else {
        this.fixedYear = '';
      }
    },
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
    this.loadingObj.loading = true;
    this.getUserBadgeMyBadgeLog();
  },
  mounted() {
    window.addEventListener('scroll', this.handleYearSticky);
    this.getNavBarHeight();
  },

  activated() {
    this.fixedYear = '';
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('scroll', this.handleYearSticky);
  },
};
</script>

<style lang="less" scoped>
.award-record-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(180deg, #151412 0%, #151412 100%);
  overflow: hidden;
  &.centered {
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
  }
  .nav-bar {
    /deep/ .transparent-white {
      .nav-title {
        color: #fffcec;
        font-size: 17 * @rem;
        font-weight: normal;
      }
      .back {
        background-image: url(~@/assets/images/badge-collection/back-btn.png);
      }
    }
    .share-btn {
      width: 22 * @rem;
      height: 22 * @rem;
      background: url(~@/assets/images/badge-collection/badge-share-btn.png)
        no-repeat;
      background-size: 100% 100%;
    }
  }
  .top-bg {
    width: 100%;
    height: 200 * @rem;
    background: url(~@/assets/images/badge-collection/award-record-bg.png)
      no-repeat top right;
    background-size: 100% auto;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }
  .top-bar {
    width: 100%;
    padding-top: 44 * @rem;
    padding-top: calc(44 * @rem + @safeAreaTop);
    padding-top: calc(44 * @rem + @safeAreaTopEnv);
    box-sizing: border-box;
    padding-bottom: 43 * @rem;
    position: relative;
    z-index: 2;
    .user-info {
      margin-top: calc(63 * @rem - @safeAreaTop);
      margin-top: calc(63 * @rem - @safeAreaTopEnv);
      margin-left: 17 * @rem;
      display: flex;
      align-items: center;
      .user-avatar {
        width: 50 * @rem;
        height: 50 * @rem;
        overflow: hidden;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.4);
        box-sizing: border-box;
        background-color: #ccc;
      }
      .user-name {
        margin-left: 10 * @rem;
        font-weight: 600;
        font-size: 22 * @rem;
        color: #fffcec;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 118 * @rem;
      }
    }
  }
  .empty-box {
    height: calc(100vh - 200 * @rem - @safeAreaTop);
    height: calc(100vh - 200 * @rem - @safeAreaTopEnv);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .main {
    box-sizing: border-box;
    padding-bottom: 30 * @rem;
    .award-record-container {
      .award-record-box {
        padding: 0 11 * @rem;
        box-sizing: border-box;
        &:first-of-type {
          margin-top: 4 * @rem;
        }
        &:not(:first-of-type) {
          margin-top: 22 * @rem;
        }
        .step-title {
          margin-left: 6 * @rem;
          font-weight: 600;
          font-size: 20 * @rem;
          color: #fffcec;
          &.fixed {
            position: fixed;
            z-index: 9;
            top: calc(50 * @rem + @safeAreaTop);
            top: calc(50 * @rem + @safeAreaTopEnv);
            margin-left: 0;
            width: 355 * @rem;
            height: 38 * @rem;
            line-height: 38 * @rem;
            padding: 0 7 * @rem;
            background: linear-gradient(
              90deg,
              #6a635f 0%,
              rgba(126, 115, 100, 0) 98%
            );
            border-radius: 6 * @rem;
          }
        }
        .step-title-placeholder {
          height: 38 * @rem;
          visibility: hidden;
        }

        .step-list {
          position: relative;
          display: flex;
          flex-direction: column;
          &::before {
            content: '';
            position: absolute;
            left: 3 * @rem;
            top: 20 * @rem;
            width: 0;
            height: calc(100% - 20 * @rem);
            border-left: 1 * @rem solid #3d3b35;
          }
          .step-sub-title {
            position: relative;
            margin: 12 * @rem 0 12 * @rem 0;
            font-weight: 400;
            font-size: 16 * @rem;
            color: #fffcec;
            padding-left: 14 * @rem;
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 6 * @rem;
              height: 6 * @rem;
              background: #3d3b35;
              border-radius: 50%;
            }
          }
          .step-item {
            padding: 0 20 * @rem;
            .step-card {
              .badge-box {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                .badge-content {
                  margin-bottom: 16 * @rem;
                  &:nth-of-type(3n) {
                    .badge-item {
                      margin-right: 0;
                    }
                  }
                  .badge-item {
                    width: 88 * @rem;
                    margin-right: 24 * @rem;
                    .content {
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      .badge-image {
                        width: 75 * @rem;
                        height: 75 * @rem;
                      }
                      .badge-name {
                        height: 20 * @rem;
                        line-height: 20 * @rem;
                        font-weight: 400;
                        font-size: 14 * @rem;
                        color: #fffcec;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 100 * @rem;
                      }
                      .badge-time {
                        height: 14 * @rem;
                        line-height: 14 * @rem;
                        font-weight: 400;
                        font-size: 10 * @rem;
                        color: #6e6b64;
                        white-space: nowrap;
                      }
                      .badge-text {
                        height: 15 * @rem;
                        line-height: 15 * @rem;
                        font-weight: 400;
                        font-size: 11 * @rem;
                        color: #6e6b64;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 88 * @rem;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
