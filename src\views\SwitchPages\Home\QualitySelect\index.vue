<template>
  <div class="recommend-page">
    <div class="placeholder"></div>
    <div class="main">
      <van-pull-refresh
        @refresh="onRefresh"
        v-model="isLoading"
        :head-height="40"
        pulling-text=" "
        :loading-text="$t('刷新中...')"
        :success-text="$t('刷新成功')"
      >
        <template v-for="(fragment, index) in homeList">
          <div
            class="fragment-container"
            :key="index"
            v-if="fragmentComponentMap[fragment.view_type]"
          >
            <component
              :is="fragmentComponentMap[fragment.view_type]"
              :info="fragment"
            ></component>
          </div>
        </template>
      </van-pull-refresh>
    </div>
    <div
      class="float-ball"
      :class="{ 'need-guide': needGuide, 'hide': pageScrolling }"
      v-if="floatBallShow"
      @click="clickFloatBall"
    >
      <div class="float-ball-close" @click.stop="closeFloatBall"></div>
      <div class="ball-info">
        <img :src="floatBallInfo.icon_url" alt="" />
      </div>
    </div>
    <!-- <ad-popup
      :show.sync="adPopupShow"
      :data="adPopupData"
      @closed="currentPopupClose"
    ></ad-popup> -->
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import NavList from '../components/nav-list';
import { fragmentComponentMap } from '@/utils/componentMap.js';
import SectionTitleNew from '../components/section-title-new';
import FirstCouponPopup from '@/components/first-coupon-popup';
import footerToCategory from '../components/footer-to-category';
import AdPopup from '@/components/ad-popup';
import {
  ApiIndexExtraIndex,
  ApiIndexSpIndexV3,
  ApiIndexGetTopic,
  ApiIndexGetShowList,
  ApiStatisticsBanner,
} from '@/api/views/home.js';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { needGuide, isWebApp } from '@/utils/userAgent';
import {
  ApiIndexBannerList,
  ApiV2024IndexGetPopOrder,
  ApiV2024IndexGetFloatWindow,
  ApiV2024IndexPopcloseLog,
  ApiV2024IndexGetPopUp,
} from '@/api/views/system.js';
import { handleActionCode } from '@/utils/actionCode.js';
import { getAdPopupData } from '@/utils/adPopup.js';
import useCloudNoticePopup from '@/components/cloud-notice-popup/index.js';
let scrollTimeout = null;
export default {
  name: 'Recommend',
  components: {
    NavList,
    SectionTitleNew,
    FirstCouponPopup,
    footerToCategory,
    AdPopup,
  },
  data() {
    const that = this;
    return {
      fragmentComponentMap,
      isLoading: false,
      floatBallInfo: {},
      floatBallShow: false,
      pageScrolling: false,
      page: 1,
      // 当前弹窗索引
      currentPopupIndex: 0,
      // 弹窗列表
      popupList: [],

      // ad通用弹窗
      adPopupShow: false,
      adPopupData: {},
      remNumberLess,
      homeNav: [],
      bannerFragment: [], //合集碎片数组
      navList: [], //导航块列表
      homeList: [], //主页游戏模块
      isShowLCIcon: false, //是否显示限时福利图标
      needGuide: needGuide, //是否需要引导（用来判断icon-list是否需要往上移)
      isInitIndexPopup: false, //是否初始化过首页弹窗
    };
  },
  computed: {
    topShadow() {
      return `linear-gradient(360deg, ${this.bgc}00 0%, ${this.bgc}ff 100%)`;
    },
    // 是否显示回归奖励图标
    isShowRGIcon() {
      // 判断是否满足回归条件
      if (this.regressionData && this.regressionData.is_regression) {
        return true;
      } else {
        return false;
      }
    },
    // 是否显示svip引导图标
    isShowSvipIcon() {
      if (!this.userInfo.is_svip) {
        return true;
      } else {
        return false;
      }
    },
    showGoldToPlatform() {
      if (this.userInfo.token) {
        let temp = this.userInfo.gold > 0 ? true : false;
        return temp;
      }
    },
    ...mapGetters({
      showFirstCouponIcon: 'user/showFirstCouponIcon',
      showRegressionPopup: 'user/showRegressionPopup',
      regressionData: 'user/regressionData',
      initData: 'system/initData',
    }),
  },
  async created() {
    // await this.getPopupData();
    await this.getAdPopupData();
    await this.getFloatBall();
    await this.getPageNavList();
    await this.getHomeData();
  },
  async activated() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.addScrollEvent();
      }, 300);
    });
  },
  beforeRouteLeave(to, from, next) {
    this.removeScrollEvent();
    next();
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (vm.isInitIndexPopup && from.fullPath !== '/' && vm.userInfo.token) {
        vm.getIndexCloudNoticePopup();
      } else {
        vm.isInitIndexPopup = true;
      }
    });
    next();
  },
  methods: {
    async getAdPopupData() {
      await getAdPopupData({
        box_position: 1,
        userInfo: this.userInfo,
      });
    },
    async getIndexCloudNoticePopup() {
      const today = new Date().toDateString();
      const userId = this.userInfo.user_id;
      if (sessionStorage.getItem(userId) === today) {
        return;
      }

      const res = await ApiV2024IndexGetPopUp({
        box_position: 7,
        userInfo: this.userInfo,
      });
      sessionStorage.setItem(userId, today);
      if (Object.keys(res.data?.popup_config).length > 0) {
        useCloudNoticePopup({
          content: res.data.popup_config[0],
        });
      }
    },
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },

    handleScroll() {
      if (this.pageScrolling == false) {
        this.pageScrolling = true;
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(() => {
        if (this.pageScrolling == true) {
          this.pageScrolling = false;
        }
      }, 300);
    },
    async getFloatBall() {
      const res = await ApiV2024IndexGetFloatWindow();
      this.floatBallInfo = res.data?.info || {};
      if (this.floatBallInfo.action_code) {
        this.floatBallShow = true;
      }
    },
    clickFloatBall() {
      if (this.floatBallInfo.click_id) {
        this.CLICK_EVENT(this.floatBallInfo.click_id);
      }
      handleActionCode(this.floatBallInfo);
    },
    closeFloatBall() {
      this.floatBallShow = false;
      this.popupCloseLog(this.floatBallInfo);
    },
    async popupCloseLog(info) {
      const res = await ApiV2024IndexPopcloseLog({
        type: info.type,
        pop_id: info.id,
        pop_type: info.pop_type,
      });
    },

    // 首页新版弹窗系统
    async getPopupData() {
      const res = await ApiV2024IndexGetPopOrder();
      this.popupList = res.data;
      this.currentPopupIndex = 0;
      this.currentPopupOpen();
    },
    saveCurrentPopupIndex(index) {
      this.currentPopupIndex = index;
    },
    // 打开弹窗系统的当前项
    currentPopupOpen() {
      if (this.currentPopupIndex > this.popupList.length - 1) {
        return;
      }
      const { type } = this.popupList[this.currentPopupIndex];
      const info = this.popupList[this.currentPopupIndex];
      switch (type) {
        case 2:
          this.adPopupShow = true;
          this.adPopupData = info;
          break;
        default:
          break;
      }
    },
    // 关闭弹窗系统的当前项
    currentPopupClose() {
      this.currentPopupIndex++;
      if (this.currentPopupIndex <= this.popupList.length - 1) {
        this.currentPopupOpen();
      }
    },

    async getActivity() {
      const res = await ApiIndexBannerList({ showType: 1 });
      this.bannerList = res.data.list;
    },
    async getExtraIndex() {
      const res = await ApiIndexExtraIndex();
      this.navList = res.data.tab_action;
    },
    async getSpIndexNew() {
      const res = await ApiIndexGetTopic({
        topic_id: 1,
        listRows: 4,
        page: this.page,
      });
      if (res.data && res.data.length) {
        if (this.page == 1) {
          this.homeList = [];
        }
        this.homeList.push(...res.data);
        this.page++;
        await this.getSpIndexNew();
      } else {
        this.isLoading = false;
      }
    },
    async onRefresh() {
      await this.getHomeData();
    },
    async getHomeData() {
      this.page = 1;
      await this.getSpIndexNew();
    },

    goToCate(click_id = '') {
      if (click_id) {
        this.CLICK_EVENT(click_id);
      }
      this.toPage('Category');
    },
    goToRank(click_id = '') {
      if (click_id) {
        this.CLICK_EVENT(click_id);
      }
      this.$router.push({
        path: '/home/<USER>',
        query: {
          tab: 2,
        },
      });
    },
    goToNewGame(click_id = '') {
      if (click_id) {
        this.CLICK_EVENT(click_id);
      }
      this.toPage('HomeNewGame');
    },
    async couponPopup() {
      if (this.userInfo.username) {
        // 判断是否满足认证福利条件
        if (parseInt(this.userInfo.auth_status) !== 2) {
          this.isShowLCIcon = true;
        } else {
          this.isShowLCIcon = false;
        }
      } else {
        this.isShowLCIcon = false;
      }
    },
    toXiaohaoDetail(id, title, item) {
      this.CLICK_EVENT(item.click_id);
      this.toPage('XiaohaoDetail', { id: id });
    },
    async getPageNavList() {
      const res = await ApiIndexGetShowList();
      if (res.data && res.data.show_info && res.data.show_info.length) {
        this.homeNav = res.data.show_info;
        //top-bar组件要用到，所以存到store里
        this.setHomeNav(this.homeNav);
      } else {
        this.setHomeNav();
      }
    },
    ...mapMutations({
      setShowLCPopup: 'user/setShowLCPopup',
      setShowRegressionPopup: 'user/setShowRegressionPopup',
      setShowFirstCouponIcon: 'user/setShowFirstCouponIcon',
      setHomeNav: 'system/setHomeNav',
      setTabBarAngle: 'system/setTabBarAngle',
      setShowFCPopup: 'user/setShowFCPopup',
    }),
    ...mapActions({
      SET_FIRST_COUPON_ICON: 'user/SET_FIRST_COUPON_ICON',
      SET_ADANDCOUPON_POPUP: 'user/SET_ADANDCOUPON_POPUP',
      SET_CHANNEL_AD_POPUP: 'user/SET_CHANNEL_AD_POPUP',
      SET_BIRTHDAY_POPUP: 'user/SET_BIRTHDAY_POPUP',
    }),
  },
};
</script>

<style lang="less" scoped>
.recommend-page {
  background-color: #f7f8fa;
  .placeholder {
    position: relative;
    display: block;
    width: 100%;
    height: calc(80 * @rem + @safeAreaTop);
    height: calc(80 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .main {
    background: linear-gradient(
      180deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 483 * @rem
    );
  }

  .fragment-container {
    flex: 1;
    padding: 6 * @rem 0;
  }

  .game-container {
    border-top: 8 * @rem solid #f8f8f8;
    position: relative;
    &.border-0 {
      border: 0;
    }

    .game-list {
      padding: 0 18 * @rem;

      .game-item {
        padding: 15 * @rem 0;
        border-bottom: 0.5 * @rem solid #ebebeb;

        &:first-of-type {
          padding-top: 0;
        }

        &:last-of-type {
          border-bottom: none;
        }

        /deep/ .game-item-components {
          padding: 0;
        }
      }
    }

    .game-list1 {
      display: flex;
      padding: 0 18 * @rem 22 * @rem;
      overflow-x: auto;
      &::-webkit-scrollbar {
        display: none;
      }

      .game-item {
        flex-shrink: 0;
        width: 80 * @rem;
        margin-right: 5 * @rem;

        &:last-of-type {
          margin-right: 0;
        }

        .game-icon {
          width: 64 * @rem;
          height: 64 * @rem;
          margin: 0 auto;

          img {
            border-radius: 12 * @rem;
          }
        }

        .game-name {
          width: 100%;
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #222222;
          line-height: 17 * @rem;
          margin-top: 12 * @rem;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .tag-list {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 8 * @rem;
        }
        .tag {
          display: block;
          max-width: 100%;
          height: 17 * @rem;
          line-height: 17 * @rem;
          padding: 0 4 * @rem;
          border-radius: 4 * @rem;
          border: 1 * @rem solid #e0e0e0;
          overflow: hidden;
        }
      }
    }

    .more {
      display: flex;
      align-items: center;
      height: 25 * @rem;
      position: absolute;
      top: 24 * @rem;
      right: 18 * @rem;

      .txt {
        font-weight: 400;
        font-size: 12 * @rem;
        color: #9a9a9a;
        line-height: 17 * @rem;
      }

      .icon {
        content: '';
        width: 8 * @rem;
        height: 12 * @rem;
        margin-left: 5 * @rem;
        background: url(~@/assets/images/home/<USER>
        background-size: 8 * @rem 12 * @rem;
      }
    }
  }
}

.icon-list {
  position: fixed;
  bottom: calc(70 * @rem + @safeAreaBottom);
  bottom: calc(70 * @rem + @safeAreaBottomEnv);
  transform: translate(300 * @rem, 0);
  z-index: 20;
  &.need-guide {
    bottom: calc(137.8 * @rem + @safeAreaBottom);
    bottom: calc(137.8 * @rem + @safeAreaBottomEnv);
  }
  .svip-icon,
  .regression-icon,
  .first-coupon-icon,
  .gold-to-platform-icon,
  .limited-coupon-icon {
    width: 68 * @rem;
    height: 68 * @rem;
    margin: 5 * @rem auto 0;
    background-image: url(~@/assets/images/home/<USER>
    background-size: 100%;
  }
  .svip-icon {
    width: 68 * @rem;
    height: 68 * @rem;
    background-image: url(~@/assets/images/home/<USER>
  }
  .regression-icon {
    width: 65 * @rem;
    height: 65 * @rem;
    background-image: url(~@/assets/images/home/<USER>
  }
  .limited-coupon-icon {
    width: 64 * @rem;
    height: 68 * @rem;
    background-image: url(~@/assets/images/home/<USER>
  }
}
.float-ball {
  width: 56 * @rem;
  height: 56 * @rem;
  position: fixed;
  bottom: calc(70 * @rem + @safeAreaBottom);
  bottom: calc(70 * @rem + @safeAreaBottomEnv);
  transform: translate(300 * @rem, 0);
  z-index: 20;
  transition: all 0.5s ease;
  &.need-guide {
    bottom: calc(137.8 * @rem + @safeAreaBottom);
    bottom: calc(137.8 * @rem + @safeAreaBottomEnv);
  }
  &.hide {
    transform: translate(380 * @rem, 0);
  }
  .float-ball-close {
    width: 12 * @rem;
    height: 12 * @rem;
    background: url(~@/assets/images/input-clear.png) center center no-repeat;
    background-size: 12 * @rem 12 * @rem;
    position: absolute;
    right: -8 * @rem;
    top: -6 * @rem;
  }
}
.banner-fragment {
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
  /deep/ .swiper-slide {
    border-radius: 0 !important;
  }
}
.upgrade-item-container {
  background: #fff;
  display: flex;
  justify-content: center;
}
.broadcast-container {
  display: flex;
  align-items: center;
  padding: 0 14 * @rem;
  margin-top: 15 * @rem;
  .broadcast-icon {
    width: 21 * @rem;
    height: 17 * @rem;
    background: url(~@/assets/images/home/<USER>
    background-size: 21 * @rem 17 * @rem;
  }
  .broadcast-swipe {
    flex: 1;
    min-width: 0;
    height: 30 * @rem;
    .broadcast-text {
      height: 30 * @rem;
      line-height: 30 * @rem;
      font-size: 12 * @rem;
      color: #999999;
      padding-left: 15 * @rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.popup-dialog {
  width: 300 * @rem;
  height: 216 * @rem;
  background: #ffffff;
  border-radius: 16 * @rem;
  z-index: 3001 !important;
  .popup-container {
    height: 216 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .content-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        padding: 28 * @rem 0 0 0;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #191b1f;
      }
      .content {
        padding: 28 * @rem 0 0 0;
        width: 238 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #60666c;
        text-align: center;
        line-height: 22 * @rem;
        .red_color {
          color: red;
        }
      }
    }

    .btn-box {
      margin-top: 21 * @rem;
      border-top: 1px solid #f0f1f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48 * @rem;
      width: 100%;
      .close {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #93999f;
      }
      .confirm {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
    }
  }
}
</style>
