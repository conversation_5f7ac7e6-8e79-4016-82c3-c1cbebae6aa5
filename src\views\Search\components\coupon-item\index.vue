<template>
  <div class="coupon-item-component">
    <div class="coupon-model" @click.stop="clickGetCoupon(couponInfo)">
      <div class="left">
        <!-- <div
          v-if="couponInfo.type && couponInfo.type != 0 && subscript()"
          class="subscript"
        >
          {{ subscript() }}
        </div> -->
        <div class="left-top">
          <div class="amount-section">
            <div class="amount">
              ¥
              <span
                :class="{ 'big-size': parseInt(couponInfo.money) < 1000 }"
                >{{ couponInfo.money }}</span
              >
            </div>

            <div class="tip">
              {{ $t('满') }}{{ couponInfo.reach_money }}{{ $t('元可用') }}
            </div>
          </div>
          <div class="info">
            <div class="game-name">{{ couponInfo.title }}</div>
            <div class="date">
              {{ couponInfo.period_title }}
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="coupon-total">
            <div
              class="coupon-left"
              :style="{ width: couponInfo.remain_percent + '%' }"
            ></div>
          </div>
          <div class="left-num">
            {{ $t('剩余') }}
            <span>{{ couponInfo.remain_percent }}</span
            >%
          </div>
        </div>
      </div>
      <!-- <div class="right">
        <div
          class="get btn"
          :class="{
            had: couponInfo.take_status != 0 || couponInfo.remain_percent == 0,
          }"
          @click="clickGetCoupon"
        >
          {{
            couponInfo.take_status != 0
              ? $t("已领取")
              : couponInfo.remain_percent != 0
              ? $t("领取")
              : $t("已抢光")
          }}
        </div>
      </div> -->
      <div class="right">
        <div class="get btn">
          {{ $t('前往领取') }}
        </div>
      </div>
    </div>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span>{{ xiaohaoListShowItem.nickname }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div class="xiaohao-list" :class="{ on: xiaohaoListShow }">
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="handleGetCoupon">
            {{ $t('确定') }}
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(couponInfo.game_id)"
    ></xh-create-tip-dialog>
  </div>
</template>

<script>
import { ApiCouponTake, ApiCouponCheckCoupon } from '@/api/views/coupon.js';
import {
  ApiXiaohaoMyListByCard,
  ApiXiaohaoMyListByGameId,
} from '@/api/views/xiaohao.js';
import { mapActions } from 'vuex';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog';
export default {
  name: 'CouponItem',
  components: {
    xhCreateTipDialog,
  },
  props: ['couponItem'],
  data() {
    return {
      couponInfo: {},
      xhDialogShow: false, //小号选择弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      createDialogShow: false,
      xiaohaoListShowItem: {},
    };
  },
  computed: {},
  created() {
    this.couponInfo = this.couponItem;
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async clickGetCoupon(couponInfo) {
      this.$router.push({
        name: 'GameCoupon',
        params: { game_id: couponInfo.game_id },
      });

      // if (this.couponInfo.take_status != 0) {
      //   this.$toast(this.$t("亲，当前账号已经领取过该券了~"))
      //   return false
      // }
      // 先判断该代金券的游戏中时候有创建小号
      // this.$toast.loading()
      // await this.getXiaohaoList()
      // this.$toast.clear()
      // if (!this.xiaohaoList.length) {
      //   this.createDialogShow = true
      //   return false
      // }
      // 有小号就先选择小号再领取
      // this.changeXiaohao()
    },

    handleGetCoupon() {
      ApiCouponTake({
        couponId: this.couponInfo.id,
        xhId: this.xiaohaoListShowItem.id,
      }).then(
        res => {
          // this.$toast(res.msg)
          this.SET_USER_INFO();
          this.xhDialogShow = false;

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${this.couponInfo.id}`,
            voucher_name: `${this.couponInfo.title}`,
            voucher_amount: `${this.couponInfo.money}`,
          });
        },
        err => {
          // if (err.code == 0) {
          //   this.couponInfo.remain_percent = 0;
          // }
        },
      );
    },
    // 监测当前小号时候可领取代金券
    async checkCoupon() {
      try {
        const res = await ApiCouponCheckCoupon({
          couponId: this.couponInfo.id,
          xhId: this.xiaohaoListShowItem.id,
          gameId: this.couponInfo.game_id,
        });
      } catch (e) {}
    },
    async getXiaohaoList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.couponInfo.game_id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
    },
    async changeXiaohao() {
      this.xiaohaoListShowItem = this.xiaohaoList[0];
      await this.checkCoupon();
      this.xhDialogShow = true;
    },
    async xiaoHaoListClick(item) {
      this.xiaohaoListShowItem = item;
      await this.checkCoupon();
      this.xiaohaoListShow = false;
    },
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    subscript() {
      switch (parseInt(this.couponInfo.type)) {
        case 1:
          return this.$t('SVIP专属');
        case 2:
          return this.$t('无门槛');
        // case 5:
        //   return this.$t("零门槛");
        default:
          return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.coupon-item-component {
  width: 100%;
  .coupon-model {
    box-sizing: border-box;
    width: 339 * @rem;
    height: 91 * @rem;
    display: flex;
    align-items: center;
    position: relative;
    background-color: #f5f5f5;
    margin: 0 auto;
    border-radius: 12 * @rem;
    // &:before {
    //   content: "";
    //   width: 15 * @rem;
    //   height: 15 * @rem;
    //   border-radius: 50%;
    //   background-color: #fff;
    //   position: absolute;
    //   left: 260 * @rem;
    //   top: -9 * @rem;
    // }
    // &:after {
    //   content: "";
    //   width: 15 * @rem;
    //   height: 15 * @rem;
    //   border-radius: 50%;
    //   background-color: #fff;
    //   position: absolute;
    //   left: 260 * @rem;
    //   bottom: -9 * @rem;
    // }
    &.svip-bg {
      // background-color: #feecc4;
      .left {
        .subscript {
          background: linear-gradient(90deg, #fed184 0%, #ffc96c 100%);
          font-weight: 600;
          color: #754400;
        }
        .left-top {
          .info {
            .game-name {
              color: #75440d;
            }
            .date {
              color: #a16910;
            }
          }
        }
      }
      .right {
        .get {
          background: linear-gradient(90deg, #ffa06c 0%, #ff5050 100%);
          &.had {
            background: #c1c1c1;
          }
        }
      }
    }
    .left {
      box-sizing: border-box;
      width: 267 * @rem;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // padding-bottom: 10 * @rem;
      .subscript {
        position: absolute;
        top: 0;
        left: 0;
        height: 18 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 6 * @rem;
        background: linear-gradient(90deg, #ff9e84 0%, #fe654d 100%);
        color: #fff;
        font-size: 11 * @rem;
        border-radius: 9 * @rem 0 9 * @rem 0;
      }
      .left-top {
        flex: 1;
        min-height: 0;
        display: flex;
        align-items: center;
        .amount-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          min-width: 90 * @rem;
          flex-shrink: 0;
          overflow: hidden;
          .amount {
            text-align: center;
            font-size: 11 * @rem;
            color: #fe4a26;
            white-space: nowrap;
            span {
              font-size: 22 * @rem;
              font-weight: bold;
              &.big-size {
                font-size: 24 * @rem;
              }
            }
          }
          .tip {
            font-size: 10 * @rem;
            // color: #fe4a26;
            margin-top: 2 * @rem;
            white-space: nowrap;
          }
        }

        .info {
          margin-left: 5 * @rem;
          flex: 1;
          min-width: 0;
          .game-name {
            font-size: 15 * @rem;
            font-weight: bold;
            color: #000000;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 21 * @rem;
            max-height: 42 * @rem;
            overflow: hidden;
          }

          .date {
            color: #909090;
            font-size: 12 * @rem;
            margin-top: 7 * @rem;
          }
        }
      }
      .left-bottom {
        display: flex;
        align-items: center;
        margin-left: 15 * @rem;
        display: none;
        .coupon-total {
          background-color: #ebebeb;
          width: 170 * @rem;
          border-radius: 3 * @rem;
          height: 6 * @rem;
          overflow: hidden;
          .coupon-left {
            background: #ff7554
              linear-gradient(270deg, #fdba42 0%, #fe5b5b 100%);
            width: 50%;
            height: 6 * @rem;
            border-radius: 3 * @rem;
          }
        }
        .left-num {
          margin-left: 12 * @rem;
          font-size: 11 * @rem;
          color: #757575;
          span {
            color: @themeColor;
          }
        }
      }
    }
    .right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .get {
        font-size: 12 * @rem;
        color: #ffffff;
        width: 62 * @rem;
        height: 26 * @rem;
        background: #5abf73;
        border-radius: 29 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        &.had {
          background: #c1c1c1;
        }
      }
    }
  }
}
.xh-dialog {
  width: 244 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/games/dialog-logo.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    width: 244 * @rem;
    background-color: #fff;
    border-radius: 20 * @rem;
    margin-top: -4 * @rem;
    z-index: 2;
    padding: 16 * @rem 10 * @rem 19 * @rem;
    .title {
      font-size: 16 * @rem;
      color: #000000;
      font-weight: 600;
      text-align: center;
      line-height: 25 * @rem;
    }
    .center {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15 * @rem 0 0;
      padding: 0 18 * @rem;
      .left,
      .right {
        position: relative;
        line-height: 40 * @rem;
      }
      .left {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 400;
      }
      .right {
        width: 133 * @rem;
        text-align: right;
        border-bottom: 0.5 * @rem solid #a6a6a6;
        .text {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #000000;
          font-size: 13 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          span {
            display: block;
            flex-shrink: 0;
          }
        }
        .more-text-icon {
          width: 10 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/games/bottom-arrow.png) center center
            no-repeat;
          background-size: 10 * @rem 6 * @rem;
          margin-left: 6 * @rem;
          transition: 0.3s;
          &.on {
            transform: rotateZ(180deg);
          }
        }
      }
      .xiaohao-list {
        display: none;
        position: absolute;
        top: 40 * @rem;
        left: 0;
        z-index: 2000;
        width: 100%;
        max-height: 200 * @rem;
        overflow: auto;
        border-radius: 0 0 4 * @rem 4 * @rem;
        background: #fff;
        border: 1 * @rem solid #f2f2f2;
        &.on {
          display: block;
        }
        .xiaohao-item {
          box-sizing: border-box;
          text-align: center;
          line-height: 40 * @rem;
          text-align: right;
          padding: 0 15 * @rem;
          font-size: 13 * @rem;
          color: #000000;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f2f2f2;
          }
        }
      }
    }

    .dialog-bottom-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18 * @rem;
      padding: 0 5 * @rem;
      .cancel {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #7d7d7d;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        border-radius: 18 * @rem;
      }
      .confirm {
        width: 102 * @rem;
        height: 35 * @rem;
        color: #ffffff;
        font-size: 13 * @rem;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 18 * @rem;
      }
    }
  }
}
</style>
