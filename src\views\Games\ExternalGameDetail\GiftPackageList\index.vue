<template>
  <div class="gift-package-container">
    <yy-list
      class="game-list-box"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh()"
      @loadMore="loadMore()"
      :check="false"
      :empty="empty"
      :tips="tips"
    >
      <div class="gift-pack-list">
        <div
          class="gift-pack-item"
          v-for="(item, index) in resultList"
          :key="index"
        >
          <gift-pack-item
            :couponItem="item"
            @calledAgainGetGift="calledAgainGetGift"
          ></gift-pack-item>
        </div>
      </div>
    </yy-list>
  </div>
</template>

<script>
import { ApiCardIndex, ApiCardGet } from '@/api/views/gift.js';
import GiftPackItem from '../Components/gift-package-item/index.vue';
export default {
  name: 'giftPackageList',
  components: { GiftPackItem },
  props: {
    xh_id: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      use_total: 0,
      total: 0,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      resultList: [], // 结果列表
      tips: '暂无礼包',
    };
  },
  async created() {
    this.game_id = this.$route.params.id;
    await this.getGiftPackageList();
  },
  methods: {
    async getGiftPackageList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiCardIndex({
        gameId: Number(this.game_id),
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.resultList = [];
        if (!res.data.list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.resultList.push(...res.data.list);
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        this.finished = false;
        await this.getGiftPackageList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGiftPackageList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
    async calledAgainGetGift() {
      await this.getGiftPackageList();
    },
  },
};
</script>

<style lang="less" scoped>
.gift-package-container {
  padding: 0 18 * @rem;
  .game-list-box {
    .gift-pack-list {
      .gift-pack-item {
        margin-bottom: 20 * @rem;
        &:first-child {
          margin-top: 10 * @rem;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
