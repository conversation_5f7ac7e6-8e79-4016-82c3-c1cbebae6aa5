<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item" @click="toExternalGameDetail()">
      <div class="game-info">
        <div class="game-item-components">
          <div class="game-icon">
            <img :src="couponItem.titlepic" alt="" />
          </div>
          <div class="game-info-item">
            <div class="game-name">
              <div class="game-title">{{ couponItem.main_title }}</div>
              <div class="game-subtitle" v-if="couponItem.subtitle">{{
                couponItem.subtitle
              }}</div>
            </div>
            <div class="game-bottom">
              <div class="game-hot" v-if="couponItem.totaldown">
                <img
                  class="hot-icon"
                  src="@/assets/images/games/hot-icon.png"
                />
                <div class="hot-num">{{ couponItem.totaldown }}</div>
              </div>
              <div class="types">
                <template v-for="(type, typeIndex) in couponItem.type">
                  <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                    type
                  }}</span>
                </template>
              </div>
            </div>
            <div class="tags">
              <div
                class="tag"
                v-for="(tag, tagIndex) in couponItem.extra_tag"
                :key="tagIndex"
              >
                <div class="tag-name">{{ tag.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div
        class="game-quan-get"
        :class="{ loading: downloadLoading[couponItem.id] }"
      >
        云手机
      </div> -->
      <div class="game-quan-get">
        <!-- :class="{ loading: downloadLoading[couponItem.id] }"
        @click="downloadBtn(couponItem.down_a, couponItem.id)"
      > -->
        {{ $t('下载') }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameQuanItem',
  props: {
    couponItem: {
      type: Object,
      default: () => {},
    },
    isShowTitlePic: {
      type: Boolean,
      default: true,
    },
    showRight: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      item: {},
      downloadLoading: {}, //下载当前loading id
    };
  },
  mounted() {},
  methods: {
    toExternalGameDetail() {
      this.$router.push({
        // name: 'ExternalGameDetail',
        name: 'GameDetail',
        params: {
          id: this.couponItem.id,
        },
      });
    },
    downloadBtn(link, id) {
      if (!this.userInfo.token) {
        this.$router.push({
          name: 'PhoneLogin',
        });
      } else {
        if (this.downloadLoading[id]) {
          return;
        }
        // if (this.interstitial_ad_id) {
        //   (window.slotbydup = window.slotbydup || []).push({
        //     id: this.interstitial_ad_id,
        //     container: 'up-detail',
        //     async: true,
        //   });
        // }
        // loading动画
        if (!link) {
          this.$toast('暂无下载噢~');
          return;
        }
        this.$set(this.downloadLoading, id, true);
        setTimeout(() => {
          this.$set(this.downloadLoading, id, false);
        }, 2000);
        window.location.href = link;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  .game-quan-item {
    height: 72 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12 * @rem;
    .game-info {
      flex: 1;
      min-width: 0;

      .game-item-components {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        .game-icon {
          position: relative;
          flex: 0 0 72 * @rem;
          width: 72 * @rem;
          height: 72 * @rem;
          border-radius: 14 * @rem;
          background-color: #eeeeee;
        }
        .game-info-item {
          margin-left: 6 * @rem;
          flex: 1;
          flex-shrink: 0;
          min-width: 0;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: space-between;
          .game-name {
            width: 100%;
            display: flex;
            align-items: center;
            .game-title {
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
            .game-subtitle {
              box-sizing: border-box;
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
              border: 1 * @rem solid #e5e1ea;
              color: #888888;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          .game-m-title {
            width: 186 * @rem;
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-weight: 600;
            font-size: 12 * @rem;
            margin: 7 * @rem 0;
            color: #999999;
            text-align: left;
            font-style: normal;
            text-transform: none;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            align-items: center;
            .remain-info {
              margin-left: 5 * @rem;
              white-space: nowrap;
              min-width: 50 * @rem;
              width: auto;
              flex-shrink: 0;
            }
          }
          .game-bottom {
            font-size: 12 * @rem;
            color: #929292;
            display: flex;
            align-items: center;
            // flex-wrap: wrap;
            margin: 5 * @rem 0 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            height: 17 * @rem;
            .game-hot {
              color: #666666;
              display: flex;
              height: 15 * @rem;
              .hot-icon {
                background-size: 10 * @rem 10 * @rem;
                width: 10 * @rem;
                height: 10 * @rem;
                margin-right: 2 * @rem;
                margin-top: 2 * @rem;
              }
              .hot-num {
                font-size: 12 * @rem;
                color: #999999;
                line-height: 15 * @rem;
                height: 15 * @rem;
              }
            }
            .server-date {
              color: #ff962c;
              margin-left: 5 * @rem;
              height: 17 * @rem;
              display: flex;
              align-items: center;
            }
            .types {
              display: flex;
              align-items: center;
              margin-left: 5 * @rem;
              height: 17 * @rem;
              overflow: hidden;
              flex-wrap: wrap;
              .type {
                padding: 0 5 * @rem;
                position: relative;
                display: flex;
                align-items: center;
                color: #999999;
                line-height: 15 * @rem;
                font-size: 12 * @rem;
                &:not(:first-child) {
                  &:before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1 * @rem;
                    height: 10 * @rem;
                    background-color: #999999;
                  }
                }
              }
            }
            &.grq {
              display: block;
            }
          }
          .tags {
            display: flex;
            height: 20 * @rem;
            overflow: hidden;
            flex-wrap: wrap;
            margin-top: 7 * @rem;
            &.show-right {
              margin-right: 58 * @rem;
            }
            .tag {
              height: 20 * @rem;
              margin-right: 5 * @rem;
              display: flex;
              align-items: center;
              flex-wrap: nowrap;
              color: #34b768;
              background-color: rgb(223, 250, 240);
              border-radius: 2 * @rem;
              padding: 2 * @rem 5 * @rem;
              box-sizing: border-box;
              .tag-icon {
                width: 13 * @rem;
                height: 13 * @rem;
              }
              .tag-name {
                font-size: 11 * @rem;
                white-space: nowrap;
                margin-left: 2 * @rem;
              }
            }
            .modify-tag {
              background-color: #fff;
            }
          }
        }
      }
    }
    .game-quan-get {
      width: 58 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 13 * @rem;
      color: #ffffff;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 28 * @rem;
      background: @themeBg;
      border-radius: 29 * @rem;
      margin-top: 4 * @rem;
      &.loading {
        position: relative;
        font-size: 0;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          animation: rotate 1s infinite linear;
        }
      }
      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
    }
  }
}
</style>
