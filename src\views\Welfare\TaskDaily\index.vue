<template>
  <div class="task-daily-page">
    <nav-bar-2
      bgStyle="transparent-white"
      :placeholder="false"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bar"></div>
      <div class="svip-info" @click="toSvip">
        <img src="@/assets/images/welfare/daily-svip-ad-new.png" alt="" />
      </div>
      <pull-refresh @refresh="onRefresh" v-model="isLoading">
        <div class="task-list">
          <template v-for="(item, key) in taskList">
            <div class="task-item" :key="key">
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="task-info">
                <div class="title">{{ item.title }}</div>
                <div class="line">
                  <div class="gold" v-if="item.gold">+{{ item.gold }}</div>
                  <div class="coupon" v-if="item.coupon">
                    {{ item.coupon }}
                  </div>
                  <div class="exp" v-if="item.exp">+{{ item.exp }}</div>
                </div>
                <div class="extra" v-if="item.extra_exp">
                  会员加成：{{ item.extra_exp }}
                </div>
              </div>
              <div
                class="task-btn btn"
                v-if="item.is_finish == 0"
                @click.stop="handleGo(item, key)"
              >
                {{ $t('去完成') }}
              </div>
              <div
                class="task-btn btn get"
                v-if="item.is_finish == 1"
                @click.stop="handleGet(item, key)"
              >
                {{ $t('领取') }}
              </div>

              <div class="task-btn btn had" v-else-if="item.is_finish == 2">
                {{ $t('已完成') }}
              </div>
            </div>
          </template>
        </div>
      </pull-refresh>
    </div>
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      :tip="popupTip"
      :confirmText="popupConfirmText"
      @confirm="popupConfirm"
    ></task-popup>
  </div>
</template>
<script>
import TaskPopup from '../comonents/task-popup';
import {
  ApiMissionGetDaily,
  ApiMissionGetMissionReward,
} from '@/api/views/mission.js';
import {
  BOX_openInNewNavWindow,
  BOX_openInNewWindow,
  BOX_showActivity,
  BOX_showActivityByAction,
  platform,
} from '@/utils/box.uni.js';
export default {
  name: 'TaskDaily',
  components: {
    TaskPopup,
  },
  data() {
    return {
      taskPopup: false, // 任务弹窗是否显示
      popupContent: '', // 任务弹窗内容
      popupTip: '', // 任务弹窗提示
      popupConfirmText: '', // 任务弹窗确认按钮文案
      isSvip: false,
      taskList: {},
      ptbUrl: '',
      vipUrl: '',
      isLoading: false,
    };
  },
  async created() {
    if (platform == 'android') {
      document.title = this.$t('每日任务');
    }
    await this.getDailyData();
  },
  methods: {
    async onRefresh() {
      await this.getDailyData();
      this.isLoading = false;
    },
    toSvip() {
      BOX_openInNewWindow({ name: 'Svip' }, { url: this.vipUrl });
    },
    popupConfirm() {
      this.taskPopup = false;
      if (this.popupConfirmText == this.$t('我知道了')) {
        return false;
      }
      BOX_openInNewWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    async handleGet(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      this.$toast(res.msg);
      await this.getDailyData();
    },
    handleGo(item, key) {
      switch (key) {
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'pay':
          this.popupContent = this.$t(
            '每日在平台游戏内使用微信支付/支付宝支付一次，或充值一次平台币、开通svip会员，都可以完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('充值平台币');
          this.taskPopup = true;
          break;
        case 'pay100':
        case 'pay500':
          this.popupContent = `${this.$t(
            '每日在平台游戏内使用微信支付/支付宝支付累计充值满',
          )}${item.need_value}${this.$t(
            '元，或累计充值平台币、开通svip会员满',
          )}${item.need_value}${this.$t('元，都可以完成任务哦~')}`;
          this.popupTip = this.$t('注：每日累计充值时间为0点至24点');
          this.popupConfirmText = this.$t('充值平台币');
          this.taskPopup = true;
          break;
        case 'play_time':
          this.popupContent = this.$t(
            '成功登录并畅玩任意游戏30分钟即可完成当前任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'sign':
          try {
            BOX_showActivityByAction({
              action_code: 30,
              web_url: 'Welfare',
              type: 1,
            });
          } catch (e) {
            BOX_showActivity(
              { name: 'Welfare', params: { type: 1 } },
              { page: 'qd' },
            );
          }
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        default:
          break;
      }
    },
    async getDailyData() {
      const res = await ApiMissionGetDaily();
      let { isSvip, list, ptbUrl, vipUrl } = res.data;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      this.isSvip = isSvip;
      this.taskList = list;
    },
  },
};
</script>

<style lang="less" scoped>
.task-daily-page {
  background-color: #fff;
  font-size: 14 * @rem;
  min-height: 100vh;
  .main {
    padding-bottom: 40 * @rem;
  }
  .top-bar {
    width: 100%;
    height: 228 * @rem;
    .image-bg('~@/assets/images/welfare/task-daily-bg-new.png');
  }
  .svip-info {
    width: 100%;
    height: 76 * @rem;
    margin: -21 * @rem auto 0;
  }
  .task-list {
    .task-item {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 337 * @rem;
      height: 90 * @rem;
      background: #fafafa;
      border-radius: 8 * @rem;
      margin: 12 * @rem auto 0;
      padding: 0 12 * @rem;
      .icon {
        width: 50 * @rem;
        height: 50 * @rem;
        background-color: #ffffff;
        border-radius: 8 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 32 * @rem;
          height: 32 * @rem;
        }
      }
      .task-info {
        flex: 1;
        min-width: 0;
        margin-left: 10 * @rem;
        .title {
          font-size: 14 * @rem;
          color: #333333;
          line-height: 18 * @rem;
          font-weight: 500;
          text-align: left;
          word-break: break-all;
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 6 * @rem;
          .gold {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-gold.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .exp {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-exp.png) left center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
          .coupon {
            padding-left: 20 * @rem;
            background: url(~@/assets/images/recharge/task-coupon.png) left
              center no-repeat;
            background-size: 18 * @rem 18 * @rem;
            line-height: 18 * @rem;
            font-size: 12 * @rem;
            color: #f05f29;
            line-height: 15 * @rem;
            margin-right: 12 * @rem;
          }
        }
        .extra {
          font-size: 11 * @rem;
          color: #777777;
          line-height: 14 * @rem;
          margin-top: 6 * @rem;
        }
      }
      .task-btn {
        width: 72 * @rem;
        height: 32 * @rem;
        border-radius: 16 * @rem;
        background: @themeColor;
        font-size: 14 * @rem;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        &.had {
          background: #e4e4e4;
        }
        &.get {
          background: @themeColor;
        }
      }
    }
  }
}
</style>
