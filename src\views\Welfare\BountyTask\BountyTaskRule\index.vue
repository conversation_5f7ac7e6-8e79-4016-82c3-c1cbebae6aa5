<template>
  <div class="page">
    <nav-bar-2 title="赏金规则" :border="true" :azShow="true"> </nav-bar-2>
    <div class="main">
      <div v-html="rule"></div>
      <!-- <div class="rule-container">
        <div class="rule-title">一、如何试玩赚金币?</div>
        <div class="rule-content">
          1.在金币页面，领取游戏任务:<br />
          2.活动期间(在游戏创角后达到对应游戏角色等级)即可通过活动页面领取奖励，倒计时结束后达成相应任务没有领取奖励，将视为放弃领取资格，任务奖励有限先到先得。(单账号每天仅可完成三次任务)
        </div>
        <div class="rule-title">二、任务的获取</div>
        <div class="rule-content">
          1.每个任务都会同步对应任务区服的开服时间进行显示和开启任务领取，特殊情况会稍有延迟;<br />
          2.获得奖励后该任务不再显示。
        </div>
        <div class="rule-title">三.为什么完成任务却无法领取奖励?</div>
        <div class="rule-content">
          1.要求中规定了具体的任务区服，未在相应区服内进行试玩的话是无法领取奖励的;<br />
          2.检查一下是否在任务游戏中创建了多个小号，若存在多个小号，其他小号也要在任务区服中创建一个角色完成任务要求后，再尝试领取奖励;<br />
          3.请在有效期内领取，任务结束则无法领取:<br />
          4.若上述条件都满足，还是提示未达到任务要求的，请联系客服进行处理。
        </div>
        <div class="rule-content">
          <span class="red">注:</span>金币核实过程中，若发现有使用作弊手段(包括但不限于使用游戏加速、修改攻击数值等)或恶意刷任务等行为，将取消获奖资格。
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { ApiBountyTaskGetRuleInfo } from '@/api/views/bounty.js';
export default {
  data() {
    return {
      rule: '',
    };
  },
  async created() {
    const res = await ApiBountyTaskGetRuleInfo();
    this.rule = res.data.ruleDocument;
  },
};
</script>

<style lang="less" scoped>
.main {
  /deep/ .rule-container {
    padding: 20 * @rem 14 * @rem;
    font-size: 14 * @rem;
    color: #333333;
    line-height: 22 * @rem;
    .rule-title {
      font-size: 14 * @rem;
      color: #333333;
      font-weight: 600;
      line-height: 22 * @rem;
    }
    .rule-content {
      line-height: 22 * @rem;
      font-size: 13 * @rem;
      color: #666666;
      margin-bottom: 24 * @rem;
      margin-top: 12 * @rem;
    }
    .red {
      color: @themeColor;
      font-weight: 600;
    }
  }
}
</style>
