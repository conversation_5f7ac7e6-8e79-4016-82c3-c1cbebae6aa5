<template>
  <div>
    <div class="step-container-1" v-if="currentStep == 1">
      <div class="step-tip-1">
        <img src="@/assets/images/rookie-guide/step-tip-1.png" alt="" />
      </div>
    </div>
    <van-dialog
      v-model="popup"
      :showConfirmButton="false"
      :lockScroll="false"
      class="welfare-guide-popup"
      v-if="currentStep == 2 && $route.name == 'Welfare'"
    >
      <div class="step-container-2">
        <div class="next-step-btn-2" @click="closePopup">知道了</div>
        <div class="step-tip-2">
          <img src="@/assets/images/rookie-guide/step-tip-2.png" alt="" />
        </div>
        <div class="step-focus-2">
          <img src="@/assets/images/rookie-guide/step-focus-2.png" alt="" />
        </div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { mapMutations, mapGetters, mapActions } from 'vuex';
export default {
  data() {
    return {};
  },
  computed: {
    currentStep() {
      return this.showWelfareGuidePopup;
    },
    popup: {
      get() {
        return this.showWelfareGuidePopup == 2 ? true : false;
      },
      set(value) {
        if (value) {
          this.setShowWelfareGuidePopup(2);
          localStorage.setItem('welfareGuideStep', 2);
        } else {
          this.setShowWelfareGuidePopup(0);
          localStorage.setItem('welfareGuideStep', 0);
        }
      },
    },
    ...mapGetters({
      showWelfareGuidePopup: 'system/showWelfareGuidePopup',
    }),
  },
  async created() {
    await this.SET_SHOW_WELFARE_GUIDE_POPUP(1);
  },
  methods: {
    ...mapMutations({
      setShowWelfareGuidePopup: 'system/setShowWelfareGuidePopup',
    }),
    ...mapActions({
      SET_SHOW_WELFARE_GUIDE_POPUP: 'system/SET_SHOW_WELFARE_GUIDE_POPUP',
    }),
    closePopup() {
      this.popup = false;
    },
  },
};
</script>
<style lang="less" scoped>
.step-container-1 {
  position: fixed;
  left: 50%;
  transform: translateX(-80 * @rem);
  bottom: calc(48 * @rem + @safeAreaBottom);
  bottom: calc(48 * @rem + @safeAreaBottomEnv);
  .step-tip-1 {
    width: 200 * @rem;
    height: 60 * @rem;
  }
}
.welfare-guide-popup {
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - @safeAreaBottom);
  height: calc(100vh - @safeAreaBottomEnv);
  background: transparent;
  border-radius: 0;
  transform: translate(0, 0);
  top: 0;
  left: 0;
  .step-container-2 {
    box-sizing: border-box;
    width: 100%;
    height: 100vh;
    .next-step-btn-1 {
      box-sizing: border-box;
      width: 118 * @rem;
      height: 44 * @rem;
      font-size: 16 * @rem;
      color: #ffffff;
      border-radius: 22 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1.5 * @rem solid #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
      position: absolute;
      bottom: 169 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .step-tip-1 {
      position: absolute;
      width: 234 * @rem;
      height: 71 * @rem;
      bottom: 82 * @rem;
      left: 50%;
      transform: translateX(-105 * @rem);
    }
    .step-focus-1 {
      position: absolute;
      width: 99 * @rem;
      height: 79 * @rem;
      bottom: 0 * @rem;
      left: 50%;
      transform: translateX(30 * @rem);
    }

    .next-step-btn-2 {
      box-sizing: border-box;
      width: 118 * @rem;
      height: 44 * @rem;
      font-size: 16 * @rem;
      color: #ffffff;
      border-radius: 22 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1.5 * @rem solid #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
      position: absolute;
      top: 413 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .step-tip-2 {
      position: absolute;
      width: 224 * @rem;
      height: 72 * @rem;
      top: 322 * @rem;
      left: 50%;
      transform: translateX(-170 * @rem);
    }
    .step-focus-2 {
      position: absolute;
      width: 80 * @rem;
      height: 103 * @rem;
      top: 214 * @rem;
      left: 50%;
      transform: translateX(-177 * @rem);
      background-color: #fff;
      border-radius: 8 * @rem;
      overflow: hidden;
    }
  }
}
</style>
