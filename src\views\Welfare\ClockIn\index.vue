<template>
  <div class="clock-in-page">
    <nav-bar-2
      bgStyle="transparent"
      :border="false"
      :placeholder="false"
    ></nav-bar-2>
    <div class="main">
      <div class="float-banner"></div>
      <div class="clock-rule-btn" @click="openRulePopup">
        <img src="@/assets/images/clock-in/clock-rule-btn.png" alt="" />
      </div>
      <div class="section-container mt-95">
        <div class="top-bar-container">
          <div class="top-reward">
            <div class="reward-title"></div>
            <div class="reward-subtitle"
              >SVIP会员金币奖励翻倍，连续签到28天宝箱奖励3733金币</div
            >
            <div class="reward-list">
              <!-- state 0:不可领取 1:可领取 2:已领取 -->
              <div
                class="reward-item"
                :class="{ last: index === goldBoxList.length - 1 }"
                v-for="(item, index) in goldBoxList"
                :key="item.gold"
                @click="handleGet(item)"
              >
                <div class="days">{{ item.title }}</div>
                <div
                  class="reward-icon"
                  :class="{ svip: item.type == 'svip' }"
                ></div>

                <div class="coins can" v-if="item.state == 1">
                  {{ $t('可领取') }}
                </div>
                <div class="coins had" v-else-if="item.state == 2">
                  {{ $t('已领取') }}
                </div>
                <div class="coins" v-else>
                  {{ item.gold_text }}
                </div>
              </div>
            </div>
          </div>
          <div class="clock-in-info">
            <div class="operate-info">
              <div>
                <div class="total-days">
                  <span>{{ continousDay }}</span
                  >(本月已连续签到天数)
                </div>
                <div class="record-item">
                  {{ $t('剩余补签次数') }}：<span>{{ surplusRepairNum }}</span>
                </div>
              </div>
              <div
                class="clock-in-btn btn"
                :class="{ had: isSignIn(currentDay) }"
                @click="clockIn"
              >
                <span v-if="isSignIn(currentDay)"></span>
                {{ clockInBtnText }}
              </div>
            </div>
          </div>
          <!-- 日历 -->
          <div class="calendar-container">
            <div class="title">
              {{ currentYear }}{{ $t('年') }}{{ currentMonth + 1
              }}{{ $t('月') }}
            </div>
            <div class="content">
              <div class="calendar-top" v-if="calendarOpen">
                <div
                  class="top-item"
                  v-for="(item, index) in calendarTop"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
              <div class="day-list">
                <template v-for="item in prevLeftDays">
                  <div
                    class="day-item day-pre"
                    :key="'pre' + item"
                    v-if="
                      thisWeekList.some(
                        v =>
                          v.day == prevDays + item - prevLeftDays &&
                          v.month != currentMonth + 1,
                      ) || calendarOpen
                    "
                  >
                    <div class="day-btn">
                      <div class="num">
                        {{ prevDays + item - prevLeftDays }}
                      </div>
                    </div>
                  </div>
                </template>
                <template v-for="item in currentDays">
                  <div
                    class="day-item"
                    :key="'cur' + item"
                    v-if="
                      thisWeekList.some(
                        v => v.day == item && v.month == currentMonth + 1,
                      ) || calendarOpen
                    "
                  >
                    <div
                      class="day-btn btn"
                      @click="repairSign(item)"
                      :class="{
                        had: isSignIn(item),
                        can: canSignIn(item),
                        today: item == currentDay,
                      }"
                    >
                      <div class="num">
                        {{ item }}
                      </div>

                      <div
                        class="text"
                        v-if="canSignIn(item) || isSignIn(item)"
                      >
                        {{ isSignIn(item) ? '' : $t('补签') }}
                      </div>
                      <div class="text" v-else></div>
                    </div>
                  </div>
                </template>
                <template v-for="item in nextLeftDays">
                  <div
                    class="day-item day-next"
                    :key="'next' + item"
                    v-if="
                      thisWeekList.some(
                        v => v.day == item && v.month != currentMonth + 1,
                      ) || calendarOpen
                    "
                  >
                    <div class="day-btn">
                      <div class="num">{{ item }}</div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <div
              class="calendar-open"
              :class="{ close: calendarOpen }"
              @click="calendarOpen = !calendarOpen"
            ></div>
          </div>
        </div>
      </div>

      <!-- 碎片开始 -->
      <template v-for="(section, sectionIndex) in sectionList">
        <!-- 今日游戏福利 -->
        <div
          class="section-container mt-16"
          :key="sectionIndex"
          v-if="section.view_type == 58"
        >
          <div class="today-game-container">
            <div class="today-title">{{ section.header_title }}</div>
            <div class="game-swiper">
              <van-swipe
                class="my-swipe"
                :autoplay="3000"
                indicator-color="rgba(104, 183, 255, 1)"
                :loop="true"
              >
                <van-swipe-item
                  v-for="(item, index) in section.game_list.list"
                  :key="index"
                >
                  <div class="swiper-item">
                    <div class="game-line">
                      <game-item-2
                        :type="5"
                        :gameInfo="item"
                        class="game-item"
                        :showRight="false"
                      ></game-item-2>
                    </div>
                    <div
                      class="reward-line"
                      @click="goToGameCoupon(item)"
                      v-if="item.coupon"
                    >
                      <div class="reward-icon">
                        <img :src="section.game_list.icon.coupon" alt="" />
                      </div>
                      <div class="reward-content">
                        满<span
                          >{{ item.coupon.reach_money }}-{{
                            item.coupon.money
                          }}</span
                        >专属代金券
                      </div>
                      <div class="btn reward-btn">领取</div>
                    </div>
                  </div>
                </van-swipe-item>
              </van-swipe>
            </div>
          </div>
        </div>
        <!-- 签到页banner -->
        <div
          class="banner-container mt-16"
          :key="sectionIndex"
          v-if="section.view_type == 53"
        >
          <div
            class="banner-item"
            v-for="(item, index) in section.banner"
            :key="index"
            @click="clickWelfareBanner(item)"
          >
            <img :src="item.titleimg" alt="" />
          </div>
        </div>
        <!-- 金币兑换道具 -->
        <div
          class="section-container mt-16"
          :key="sectionIndex"
          v-if="section.view_type == 54"
        >
          <div class="exchange-container">
            <div class="section-title exchange-title">
              {{ section.header_title }}
              <div
                class="exchange-more"
                @click="toPage(section.web_page)"
              ></div>
            </div>

            <div class="exchange-content">
              <div class="more-game-btn" @click="clickMoreGame"></div>
              <div class="nav-list">
                <div
                  class="nav-item"
                  :class="{
                    active: game.game_info.id == currentGame.game_info.id,
                  }"
                  v-for="game in goldCardList"
                  :key="game.id"
                  @click="clickNavGame(game)"
                >
                  <div class="game-icon">
                    <img :src="game.game_info.titlepic" alt="" />
                  </div>
                  <div
                    class="game-info"
                    v-if="game.game_info.id == currentGame.game_info.id"
                  >
                    <div class="game-title">
                      <div class="title-text">
                        <div
                          class="text"
                          :class="{
                            'text-scroll': game.game_info.title.length > 3,
                          }"
                        >
                          {{ game.game_info.title }}
                        </div>
                      </div>
                      <div class="title-tag" v-if="game.game_info.subtitle">
                        {{ game.game_info.subtitle }}
                      </div>
                    </div>
                    <div
                      class="game-discount"
                      v-if="Number(game.game_info.pay_rebate / 10) < 10"
                    >
                      <div class="left">折扣</div>
                      <div class="right">
                        {{ Number(game.game_info.pay_rebate / 10) }}折直充
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="exchange-list">
                <template v-for="(item, index) in currentGame.card_info">
                  <div
                    class="exchange-item"
                    :key="item.id"
                    v-if="!(index > 2 && !isExpand)"
                  >
                    <div class="limit" v-if="item.redeem_num_text">
                      {{ item.redeem_num_text }}
                    </div>
                    <div class="exchange-icon">
                      <img :src="item.titlepic" alt="" />
                    </div>
                    <div class="exchange-info">
                      <marquee-text
                        class="title"
                        :text="item.title"
                      ></marquee-text>
                      <div class="cost">
                        消耗<span>{{ item.need_gold }}</span
                        >金币
                      </div>
                    </div>
                    <div class="exchange-btn" @click="handleExchange(item)">
                      立即兑换
                    </div>
                  </div>
                </template>

                <template v-if="currentGame.card_info.length > 3">
                  <div
                    class="exchange-down"
                    v-if="!isExpand"
                    @click="isExpand = !isExpand"
                  >
                    更多道具<i></i>
                  </div>
                  <div
                    class="exchange-down up"
                    v-else
                    @click="isExpand = !isExpand"
                  >
                    收起<i></i>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 金币福利 -->
        <div
          class="section-container mt-16"
          :key="sectionIndex"
          v-if="section.view_type == 36"
        >
          <div class="activity-container">
            <div class="section-title activity-title">
              {{ section.header_title }}
            </div>
            <div class="welfare-bar">
              <div
                class="welfare-banner"
                @click="clickGoldWelfare(item)"
                v-for="(item, index) in section.tab_action"
                :key="index"
              >
                <img :src="item.icon_url" alt="" />
              </div>
            </div>
          </div>
        </div>
        <!-- 玩家之声 -->
        <div
          class="section-container mt-16"
          :class="{ 'no-padding': section.view_type == 57 }"
          :key="sectionIndex"
          v-if="section.view_type == 57 && isPlayerVoiceShow"
        >
          <div class="close-btn" @click="isPlayerVoiceShow = false"></div>
          <div
            class="player-voice img"
            @click="toPage(section.web_page, { id: 0 })"
          >
            <img :src="section.img" alt="" />
          </div>
        </div>
      </template>
    </div>
    <div class="sign-popup-unset">
      <!-- 领取宝箱成功弹窗 ---旧 -->
      <!-- <van-dialog
        v-model="boxSuccessShow"
        :show-confirm-button="false"
        :lock-scroll="false"
        class="box-success-dialog"
      >
        <div class="box-success">
          <div class="box-icon"></div>
          <div class="box-success-text"></div>
          <div class="info-content">
            <div
              class="box-popup-text"
              v-for="item in boxPoints"
              :key="item.id"
            >
              {{ item.rule_name }}{{ item.text }}{{ item.num }}
            </div>
            <div class="box-popup-text blue small" v-if="!userInfo.is_svip">
              SVIP尊享每日签到双倍金币，宝箱奖励翻倍！
            </div>
          </div>
          <div class="btn-bar">
            <div class="success-conform" @click="boxSuccessShow = false">
              收下
            </div>
            <div class="vip-conform" v-if="!userInfo.is_svip" @click="toSvip">
              立即开通
            </div>
          </div>
        </div>
      </van-dialog> -->
    </div>

    <van-dialog
      v-model="signSuccessShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="sign-dialog"
      @closed="openYindaoPopup()"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="reward-box">
          <div class="reward-item" v-if="reward_info.exp">
            <div class="exp-icon"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.exp }}</span>
              <span>经验</span>
            </div>
          </div>
          <div class="reward-item" v-if="reward_info.gold">
            <div class="gold-icon" :class="{ svip: userInfo.is_svip }"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.gold }}</span>
              <span>金币</span>
            </div>
          </div>
          <div class="upgrade" v-if="!userInfo.is_svip">
            <div class="text">升级</div>
            <div class="up-icon"></div>
          </div>
          <div class="reward-item svip-more-gold" v-if="!userInfo.is_svip">
            <div class="gold-icon"></div>
            <div class="reward-msg">
              <span class="msg-number">
                {{ reward_info.gold + reward_info.svip_gold }}</span
              >
              <span>金币</span>
            </div>
          </div>
        </div>
        <div class="svip-tips" v-if="noSvipText && !userInfo.is_svip">
          <div class="tips-title">升级SVIP获得</div>
          <div
            class="small-tips"
            v-for="(item, index) in noSvipText"
            :key="index"
            >{{ item }}</div
          >
        </div>
        <div class="btn-list">
          <div class="btn" @click="signSuccessShow = false">开心收下</div>
          <div class="btn" v-if="!userInfo.is_svip" @click="toSvip"
            >升级领取
            <div class="svip-tip" v-if="svipTip">{{ svipTip }}</div></div
          >
        </div>
        <div class="tips" v-if="userInfo.is_svip && reward_info.extra_tip">{{
          reward_info.extra_tip
        }}</div>
        <div class="dialog-close-btn" @click="signSuccessShow = false"></div>
      </div>
    </van-dialog>

    <!-- <div class="sign-popup-unset">
      <van-dialog
        v-model="signSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        @closed="openYindaoPopup()"
      >
        <div class="sign-success">
          <div class="dialog-close" @click="signSuccessShow = false">
            <img src="@/assets/images/clock-in/sign-success-close.png" alt="" />
          </div>
          <div class="success-info">
            <div class="info-content">
              <div class="info-text" v-for="item in points" :key="item.id">
                {{ item.rule_name }}{{ item.text }}{{ item.num }}
              </div>
              <div class="info-text red" v-if="!userInfo.is_svip">
                {{ $t('开通SVIP【额外】金币') }}+{{ svip_extra_gold }}
              </div>
              <div class="info-text red small" v-if="!userInfo.is_svip">
                还可金币兑换平台币哦~
              </div>
            </div>
            <div
              class="success-conform"
              v-if="!userInfo.is_svip"
              @click="toSvip"
            >
              {{ $t('立即开通') }}
            </div>
            <div
              class="success-conform"
              v-else
              @click="signSuccessShow = false"
            >
              {{ $t('开心收下') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div> -->
    <!-- 引导添加到桌面弹窗 -->
    <van-dialog
      v-model="yindaoPopup"
      :showConfirmButton="false"
      :showCancelButton="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="yindao-popup"
    >
      <div class="close" @click="yindaoPopup = false"></div>
      <div class="text">{{ $t('为了更方便的连续签到领宝箱') }}</div>
      <div class="text">{{ $t('请添加到主屏幕哦') }}</div>
      <div class="down-arrow"></div>
    </van-dialog>

    <!-- 非svip点击签到弹窗 -->
    <van-dialog
      class="no-svip-sign-dislog"
      v-model="noSvipSignShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
    >
      <div class="top-icon"></div>
      <div class="no-svip-tip">
        <div class="line">{{ $t('开通SVIP') }}</div>
        <div class="small-tips" v-for="(item, index) in noSvipText">{{
          item
        }}</div>
      </div>
      <div class="operation">
        <div class="clock-btn btn" @click="clickNormalSign">
          {{ $t('普通签到') }}
          <span>{{ common_gold_text }}</span>
        </div>
        <div class="clock-btn svip btn" @click="clickSvipSign">
          {{ $t('SVIP签到') }}
          <span>{{ svip_gold_text }}</span>
          <div class="svip-tips" v-if="svipTip">{{ svipTip }}</div>
        </div>
      </div>
      <div class="no-notice" :class="{ remember: remember }">
        <div class="content" @click.stop="remember = !remember">
          <div class="gou"></div>
          {{ $t('不再提醒') }}
        </div>
      </div>
      <div class="close-btn" @click="noSvipSignShow = false"></div>
    </van-dialog>

    <!-- 非svip点击补签弹窗 -->
    <van-dialog
      v-model="repairSignTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="repair-sign-tip-popup"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="box-bg">
          <div class="title">{{ noSvipRepairSignTip }}</div>
        </div>
        <div class="btn-box">
          <div class="btn-cancel" @click="repairSignTipShow = false">
            {{ $t('取消') }}</div
          >
          <div class="btn-open" @click="toBuySvip"> {{ $t('立即开通') }}</div>
        </div>
      </div>
    </van-dialog>

    <!-- svip补签提示弹窗 -->
    <van-dialog
      v-model="svipRepairSignTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="svip-repair-sign-tip-popup"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="box-bg">
          <div class="title"> {{ repairSignTip }}</div>
          <div class="btn" @click="repairSigning"> {{ $t('补签') }}</div>
        </div>
      </div>
    </van-dialog>

    <div class="sign-popup-unset">
      <!-- 补签成功弹窗 -->
      <van-dialog
        v-model="repairSignSuccessShow"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
        :lock-scroll="false"
        class="repair-sign-dialog"
      >
        <div class="logo-icon"></div>
        <div class="dialog-content">
          <div class="title">{{ $t('补签成功') }}</div>
          <div class="msg">
            <span v-if="repairGoldNum">
              <span class="msg-number"> +{{ repairGoldNum }}</span>
              <span>{{ $t('金币') }}</span>
            </span>
          </div>
          <div class="btn" @click="repairSignSuccessShow = false">{{
            $t('开心收下')
          }}</div>
          <div class="dialog-close-btn" @click="repairSignSuccessShow = false">
          </div>
        </div>
      </van-dialog>
    </div>

    <!-- 连签奖励未完成 -->
    <van-dialog
      v-model="getAwardIncompletePopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-before-dialog"
    >
      <div class="dialog-content">
        <div class="title" :class="{ fzs: getAwardPopupInfo.gold > 999 }"
          >解锁{{
            getAwardPopupInfo.type == 'svip'
              ? getAwardPopupInfo.svip + '天SVIP权益'
              : getAwardPopupInfo.gold + '金币'
          }}仅剩{{ getAwardPopupInfo.diff_day }}天</div
        >
        <div class="detail">
          <div
            class="double"
            v-if="userInfo.is_svip && getAwardPopupInfo.type != 'svip'"
            >已翻倍</div
          >
          <img
            v-if="getAwardPopupInfo.type != 'svip'"
            src="@/assets/images/clock-in/coin-new-icon.png"
            alt=""
          />
          <img v-else src="@/assets/images/clock-in/svip-new-icon.png" alt="" />

          <div v-if="getAwardPopupInfo.type != 'svip'" class="gold-num"
            >+{{ getAwardPopupInfo.gold }}</div
          >
          <div v-else class="svip-num">+{{ getAwardPopupInfo.svip }}天SVIP</div>
        </div>
        <div class="msg">
          <div class="msg-number">{{ getAwardPopupInfo.next_box_text }}</div>
          <div
            class="msg-number"
            v-if="
              !userInfo.is_svip &&
              getAwardPopupInfo.svip_text &&
              getAwardPopupInfo.type != 'svip'
            "
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="getAwardIncompletePopupShow = false"
            >我知道了</div
          >
          <div class="btn" @click="toSvip" v-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div
          class="dialog-close-btn"
          @click="getAwardIncompletePopupShow = false"
        ></div>
      </div>
    </van-dialog>

    <!-- 连签奖励弹窗 -->
    <van-dialog
      v-model="getAwardPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-dialog"
    >
      <div
        class="logo-icon"
        :class="{ svip: getAwardPopupInfo.type == 'svip' }"
      ></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="msg">
          <div class="msg-number"
            >连续签到{{ getAwardPopupInfo.box_num }}天奖励</div
          >
          <div class="msg-number" v-if="getAwardPopupInfo.type == 'svip'"
            >SVIP {{ getAwardPopupInfo.svip }}天权益</div
          >
          <div class="msg-number" v-else>
            {{ userInfo.is_svip ? 'SVIP会员' : '普通用户' }} 金币 +{{
              getAwardPopupInfo.gold
            }}
          </div>
          <div
            class="tips"
            v-if="
              !userInfo.is_svip &&
              getAwardPopupInfo.svip_text &&
              getAwardPopupInfo.type != 'svip'
            "
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="getAwardPopupShow = false">开心收下</div>
          <div
            class="btn"
            @click="toSvip"
            v-if="getAwardPopupInfo.type == 'svip'"
            >查看SVIP权益</div
          >
          <div class="btn" @click="toSvip" v-else-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div class="dialog-close-btn" @click="getAwardPopupShow = false"></div>
      </div>
    </van-dialog>

    <!-- 连续签到领宝箱弹窗(全局弹窗了，这个弹窗样式暂时不用了) -->
    <!-- <van-dialog
      v-model="goldBoxSuccessShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="gold-box-success">
        <div class="success-text">
          {{ goldPoints.rule_name }}，{{ goldPoints.text
          }}<span>{{ goldPoints.num }}</span>
        </div>
        <div class="confirm" @click="goldBoxSuccessShow = false">确定</div>
      </div>
    </van-dialog> -->

    <div class="sign-popup-unset">
      <!-- 宝箱分享弹窗 -->
      <van-dialog
        v-model="goldBoxShareShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="title">
            {{ $t('即将获得') }}<span>{{ goldBoxShareData.gold }}</span
            >{{ $t('金币') }}
          </div>
          <div class="tip">{{ goldBoxShareData.showText }}</div>
          <div class="sub-tip">{{ $t('新老用户皆可') }}</div>
          <div class="avatar">
            <img src="@/assets/images/clock-in/no-invite.png" alt="" />
          </div>
          <div class="operate">
            <div class="common-invite btn" @click="handleShare">
              {{ $t('立即邀请') }}
            </div>
            <div class="svip-invite btn" @click="toPage('Svip')">
              {{ $t('SVIP免邀请') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <div class="sign-popup-unset">
      <!-- 宝箱分享成功弹窗 -->
      <!-- 2021年12月13日18:39:42 新需求：最后两个宝箱取消分享直接领取。 -->
      <van-dialog
        v-model="goldBoxShareSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareSuccessShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="get-gold-num">
            {{ $t('获得') }}{{ goldBoxShareSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="sub-tip help-man">{{ $t('助力好友') }}</div>
          <div class="avatar avatar-icon">
            <img :src="goldBoxShareSuccessData.avatar" alt="" />
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxShareSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <div class="sign-popup-unset">
      <!-- svip直接领取最后两个宝箱 -->
      <van-dialog
        v-model="goldBoxSvipSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxSvipSuccessShow = false"></div>
          <div class="top-text orange">{{ $t('恭喜你') }}</div>
          <div class="sub-tip help-svip">
            {{ $t('SVIP无需好友助力即可领取') }}
          </div>
          <div class="get-gold-num svip-num">
            {{ goldBoxSvipSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxSvipSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <!-- 签到规则弹窗 -->
    <van-action-sheet v-model="rulePopup" title="" class="rule-popup">
      <div class="popup-content">
        <div class="close" @click="rulePopup = false"></div>
        <div class="popup-title">
          <img src="@/assets/images/clock-in/clock-in-rule-title.png" alt="" />
        </div>
        <div class="rule-content" v-html="rules" @click="clickRules"></div>
      </div>
    </van-action-sheet>

    <exchange-game-search-popup
      :show.sync="moreGamePopup"
      @onSelectSuccess="onGameSelectSuccess"
    ></exchange-game-search-popup>

    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="xhGameId"
      @onSelectSuccess="onXhSelectSuccess"
    ></yy-xh-select>

    <exchange-copy-popup
      :show.sync="giftCopyPopupShow"
      :info="exchangeGift"
    ></exchange-copy-popup>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import rubberBand from '@/components/rubber-band';
import exchangeGameSearchPopup from '@/components/exchange-game-search-popup';
import {
  ApiUserClockIn,
  ApiUserActiveSign,
  ApiUserGetGoldBox,
  ApiUserRepairDeductGold,
  ApiUserRepairSign,
} from '@/api/views/users.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { needGuide } from '@/utils/userAgent';
import { ApiIndexGetSplndexData } from '@/api/views/welfare.js';

import { clickBanner } from '@/utils/function.js';

import { ApiGameGetGameCardList } from '@/api/views/gold.js';

import { ApiCardGet } from '@/api/views/gift.js';

import { PageName } from '@/utils/actionCode.js';

import {
  BOX_takeGift,
  platform,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';

import h5Page from '@/utils/h5Page';
import useCollectToast from '@/components/yy-collect-toast/index.js';
let defaultDate = new Date().getTime();
export default {
  name: 'ClockIn',
  components: {
    rubberBand,
    exchangeGameSearchPopup,
  },
  data() {
    return {
      themeColorLess,
      currentToken: 0,
      mainTitle: '',
      goldBoxList: [],
      noSvipText: [],
      svipTip: '',
      common_gold_text: '+10金币',
      svip_gold_text: '+20金币',
      clockInDate: [], // 已签到的日期
      rules: '',
      maxDays: 0, // 本月最长连续签到
      continousDay: 0, // 目前连续签到天数
      surplusRepairNum: 0, // 剩余补签次数
      repairDate: [], // 可补签的日期
      calendarTop: [
        this.$t('周日'),
        this.$t('周一'),
        this.$t('周二'),
        this.$t('周三'),
        this.$t('周四'),
        this.$t('周五'),
        this.$t('周六'),
      ],
      currentDay: new Date(defaultDate).getDate(),
      currentMonth: new Date(defaultDate).getMonth(),
      currentYear: new Date(defaultDate).getFullYear(),
      signSuccessShow: false, // 签到成功弹窗
      points: [], // 签到成功信息
      reward_info: {}, // 签到成功信息
      repairSignTipShow: false, // 补签提示弹窗
      svipRepairSignTipShow: false, // svip补签提示弹窗(svip才可补签了)
      noSvipRepairSignTip: '', // 非svip补签提示信息
      repairSignTip: '', // 补签提示信息
      repairSignSuccessShow: false, // 补签成功弹窗
      repairSignDay: '', // 要补签的是哪一天
      // goldPoints: [], // 领取宝箱成功信息
      goldBoxSuccessShow: false, // 连续签到领宝箱弹窗
      goldBoxShareShow: false, // 宝箱分享弹窗
      goldBoxShareData: {}, // 宝箱分享信息
      shareInfo: {}, // 分享给好友的内容
      goldBoxShareSuccessShow: false, // 宝箱分享成功弹窗
      goldBoxShareSuccessData: {}, // 宝箱分享成功弹窗
      repairGoldNum: 0,
      goldBoxSvipSuccessShow: false, // svip免邀请直接领取宝箱的弹窗
      goldBoxSvipSuccessData: {}, // svip免邀请直接领取宝箱的信息
      yindaoPopup: false, //引导到添加主屏幕的弹窗
      noSvipSignShow: false, // 非svip签到弹窗
      remember: false, // 是否记住不再提示
      popup_msg_show: true, //是否同意打开消息提示弹窗（用于规则弹窗只弹一次）
      is_open_new_role: false, //是否打开锁功能
      svip_extra_gold: 0,

      // boxSuccessShow: false, // 领取宝箱弹窗
      // boxPoints: [], // 领取宝箱信息

      calendarOpen: false, // 日历是否展开
      thisWeekList: [], // 今日这周的数组
      rulePopup: false,

      // 碎片列表
      sectionList: [],
      isPlayerVoiceShow: true,
      currentGame: {},
      moreGamePopup: false,
      xhDialogShow: false,
      xhGameId: 0,
      exchangeGift: {},
      giftCopyPopupShow: false,

      isExpand: false,

      getAwardIncompletePopupShow: false,
      getAwardPopupShow: false,
      getAwardPopupInfo: {},
    };
  },
  computed: {
    clockInBtnText() {
      let clockDate =
        this.currentDay < 10
          ? '0' + this.currentDay
          : this.currentDay.toString();
      return this.clockInDate.includes(clockDate)
        ? this.$t('已签到')
        : this.$t('立即签到');
    },
    // 这个月天数
    currentDays() {
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },
    // 上个月天数
    prevDays() {
      return new Date(this.currentYear, this.currentMonth, 0).getDate();
    },
    // 上个月剩余天数
    prevLeftDays() {
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },
    // 下个月显示的天数
    nextLeftDays() {
      return 7 - new Date(this.currentYear, this.currentMonth + 1, 1).getDay();
    },

    // 兑换道具的数据
    goldCardList() {
      return (
        this.sectionList.find(section => section.view_type == 54)
          ?.gold_card_list || []
      );
    },
  },
  async created() {
    this.getThisWeekList();
    this.SET_USER_INFO(true);
    this.currentToken = this.userInfo.token;
    this.currentDay = new Date(defaultDate).getDate();
    this.currentMonth = new Date(defaultDate).getMonth();
    this.currentYear = new Date(defaultDate).getFullYear();
    this.getSignInfo();
    this.getSpIndexData();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    if (to.name == 'PlayerVoice') {
      to.meta.keepAlive = false;
    }
    next(true);
  },
  methods: {
    clickNavGame(game) {
      this.isExpand = false;
      this.currentGame = game;
    },
    async getSpIndexData() {
      const res = await ApiIndexGetSplndexData({ id: 15 });
      this.sectionList = res.data;
      this.currentGame = this.goldCardList[0] ?? {};
    },
    clickWelfareBanner(item) {
      this.CLICK_EVENT(item.click_id);

      clickBanner(item);
    },
    clickGoldWelfare(item) {
      this.CLICK_EVENT(item.click_id);
      this.toPage(PageName[item.action_code]);
    },
    openRulePopup() {
      this.rulePopup = true;
    },
    goToGameCoupon(item) {
      this.toPage('GameCoupon', {
        game_id: item.id,
      });
    },
    getThisWeekList() {
      let date = new Date(defaultDate);
      let now = date.getTime();
      let day = date.getDay();
      let oneDayTime = 24 * 60 * 60 * 1000;
      let MondayTime = now - day * oneDayTime;
      let SundayTime = now + (7 - day - 1) * oneDayTime;
      let thisWeekList = [];
      for (let i = 0; i < 7; i++) {
        let d = new Date(MondayTime + i * oneDayTime);
        thisWeekList.push(d);
      }
      this.thisWeekList = thisWeekList.map(item => {
        return {
          day: item.getDate(),
          month: item.getMonth() + 1,
        };
      });
    },
    // 签到成功跳转svip
    toSvip() {
      this.signSuccessShow = false;
      this.noSvipSignShow = false;
      this.getAwardIncompletePopupShow = false;
      this.getAwardPopupShow = false;
      // this.boxSuccessShow = false;
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
    // 补签跳转svip
    toBuySvip() {
      this.repairSignTipShow = false;
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
    // 是否可补签
    async repairSign(item) {
      if (this.canSignIn(item)) {
        this.$toast.loading({
          message: this.$t('加载中'),
        });
        const res = await ApiUserRepairDeductGold();
        this.$toast.clear();
        if (res.code == -14) {
          // 非svip点击补签
          this.noSvipRepairSignTip = res.msg;
          this.repairSignTipShow = true;
        }
        if (res.code > 0) {
          this.repairSignTip = res.data.gold_num;
          this.repairSignDay = item;
          this.svipRepairSignTipShow = true;
        }
      }
    },
    // 补签中
    async repairSigning() {
      this.svipRepairSignTipShow = false;
      let date = `${this.currentYear}-${this.currentMonth + 1}-${
        this.repairSignDay
      }`;
      const res = await ApiUserRepairSign({
        signDate: date,
      });
      this.repairGoldNum = res.data.gold_num;
      this.repairSignSuccessShow = true;
      await this.getSignInfo();
    },
    // 是否是签到过的
    isSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.clockInDate.length > 0 && this.clockInDate.includes(str)) {
        return true;
      }
      return false;
    },
    // 是否是可以补签的
    canSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.repairDate.length > 0 && this.repairDate.includes(str)) {
        return true;
      }
      return false;
    },
    // 领取宝箱
    async handleGet(item) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      // 文案由后端返回，所以前端没有判断是否已领取过或者还不能领取，所有宝箱均可点击
      await this.fetchGetGoldBox(item);
    },
    async fetchGetGoldBox(item) {
      try {
        const res = await ApiUserGetGoldBox({
          box_num: item.box_num,
        });
        let {
          need_share,
          gold_box_list,
          clock_in_date,
          is_svip,
          get_box,
          show_text,
          share_info,
          list,
        } = res.data;
        if (res.code > 0) {
          this.$toast.clear();
          this.getAwardPopupInfo = {};
          if (res.data.pop) {
            this.getAwardPopupInfo = { ...res.data.pop, ...item };
            this.getAwardIncompletePopupShow = true;
            return false;
          }
          if (res.points) {
            this.getAwardPopupInfo = {
              ...res.points.num,
              svip_text: res.data.svip_text || '',
              ...item,
            };
            this.getAwardPopupShow = true;
          }
          await this.getSignInfo();
        }

        // 以下两个if为刷新宝箱数据
        if (gold_box_list && gold_box_list.length) {
          this.goldBoxList = gold_box_list;
        }
        if (clock_in_date && clock_in_date.length) {
          this.clockInDate = clock_in_date;
        }

        if (need_share == true) {
          // 需要分享的
          if (is_svip == false) {
            // 非svip才需要助力
            if (get_box == false) {
              // 还需要助力
              this.goldBoxShareData = {
                gold: item.gold,
                showText: show_text,
              };
              if (share_info) {
                this.shareInfo = share_info;
              }
              this.goldBoxShareShow = true;
            } else {
              // 助力成功
              this.goldBoxShareSuccessData = list[0];
              this.goldBoxShareSuccessData.gold = item.gold;
              this.goldBoxShareSuccessShow = true;
            }
          } else {
            // svip直接领取的弹窗
            this.goldBoxSvipSuccessData = {
              gold: item.gold,
            };
            this.goldBoxSvipSuccessShow = true;
          }
        }
      } catch (e) {}
    },
    async getSignInfo() {
      const res = await ApiUserClockIn();
      let {
        gold_box_list,
        clock_in_date,
        max_days,
        repair_date,
        surplus_repair_num,
        title,
        text1,
        continuous_day,
        is_open_new_role,
        svip_extra_gold,
        no_svip_text,
        btn_corner_text,
        common_gold_text,
        svip_gold_text,
      } = res.data;
      this.goldBoxList = gold_box_list;
      this.noSvipText = no_svip_text || [];
      this.svipTip = btn_corner_text;
      if (common_gold_text) {
        this.common_gold_text = common_gold_text;
      }
      if (svip_gold_text) {
        this.svip_gold_text = svip_gold_text;
      }
      this.mainTitle = title;
      this.rules = text1;
      this.clockInDate = clock_in_date;
      this.maxDays = max_days;
      this.continousDay = continuous_day;
      this.surplusRepairNum = surplus_repair_num;
      this.repairDate = repair_date;
      this.is_open_new_role = is_open_new_role;
      this.svip_extra_gold = svip_extra_gold;
      if (res.data.update_msg && this.popup_msg_show) {
        this.popup_msg_show = false;
        this.$dialog.alert({
          message: res.data.update_msg,
          lockScroll: false,
        });
      }
    },
    clickRules(event) {
      event.preventDefault();
      let target = event.target;
      if (target.tagName.toLowerCase() == 'a') {
        let routeName = target.getAttribute('href');
        this.$router.push({ name: routeName });
      }
    },
    // 点击普通签到
    clickSvipSign() {
      this.noSvipSignShow = false;
      this.$nextTick(() => {
        this.handlenotice();
        this.toPage('Svip');
      });
    },
    // 点击svip签到
    async clickNormalSign() {
      this.noSvipSignShow = false;
      this.$nextTick(async () => {
        this.handlenotice();
        await this.handleClockIn();
      });
    },
    handlenotice() {
      if (this.remember) {
        localStorage.setItem('NO_SVIP_SIGN_DIALOG_HIDE', true);
      }
    },
    // 点击签到
    async clockIn() {
      let temDay =
        this.currentDay < 10
          ? `0${this.currentDay}`
          : this.currentDay.toString();
      if (!this.clockInDate.includes(temDay)) {
        // // 还没签到的情况
        if (!this.userInfo.is_svip) {
          let no_svip_sign_dialog_hide = localStorage.getItem(
            'NO_SVIP_SIGN_DIALOG_HIDE',
          );
          if (no_svip_sign_dialog_hide) {
            // 不再提示svip直接签到
            await this.handleClockIn();
            return false;
          }
          this.remember = false;
          this.noSvipSignShow = true;
        } else {
          // 是svip的情况
          await this.handleClockIn();
        }
        return false;
      }
      this.$toast(this.$t('今日已签到'));
    },
    async handleClockIn() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiUserActiveSign();
      this.$toast.clear();
      if (res.data.reward_info) {
        // 弹出签到成功弹窗
        this.reward_info = res.data.reward_info;
        this.signSuccessShow = true;
      } else {
        this.$toast(res.msg);
      }
      await this.getSignInfo();
    },
    handleShare() {
      this.$copyText(this.shareInfo.title_url).then(res => {
        this.$toast(this.$t('复制成功，快去粘贴给好友助力吧~'));
      });
    },
    openYindaoPopup() {
      if (needGuide) {
        this.yindaoPopup = true;
      }
    },
    clickMoreGame() {
      this.moreGamePopup = true;
    },
    async onGameSelectSuccess(info) {
      this.isExpand = false;
      let scrollEl = document.querySelector('.nav-list');
      let findOne = this.goldCardList.find(
        item => item.game_info.id === info.id,
      );
      if (!findOne) {
        this.goldCardList.unshift({
          game_info: info,
          card_info: [],
        });
        this.currentGame = this.goldCardList[0] ?? {};
        this.$nextTick(() => {
          scrollEl.scrollTo({
            left: 0,
            behavior: 'smooth',
          });
        });
      } else {
        this.currentGame = findOne;
        this.$nextTick(() => {
          let index = this.goldCardList.findIndex(
            item => item.game_info.id === info.id,
          );
          scrollEl.scrollTo({
            left: index * 46,
            behavior: 'smooth',
          });
        });
      }

      await this.updatePropList();
      this.moreGamePopup = false;
    },
    async updatePropList() {
      const res = await ApiGameGetGameCardList({
        id: this.currentGame.game_info.id,
      });
      this.currentGame.card_info = res.data.list;
      this.goldCardList.find(
        item => item.game_info.id === this.currentGame.game_info.id,
      ).card_info = res.data.list;
    },
    async handleExchange(item) {
      if (!this.userInfo.is_svip) {
        this.$dialog
          .confirm({
            message: '抱歉，您当前尚未开通SVIP会员，\n请先开通SVIP会员',
            confirmButtonText: '前往开通',
            cancelButtonText: '取消',
            confirmButtonColor: this.themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            BOX_openInNewWindow({ name: 'Svip' }, { url: h5Page.svip_url });
          });
        return;
      }

      if (this.userInfo.gold < item.need_gold) {
        this.$toast.clear();
        this.$dialog
          .confirm({
            message: ' 抱歉，您当前金币不足，\n可以前往赚取更多金币再来哦～',
            confirmButtonText: '去赚金币',
            cancelButtonText: '取消',
            confirmButtonColor: themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            BOX_openInNewWindow(
              { name: 'GoldCoin' },
              { url: h5Page.renwudating },
            );
          });
        return;
      }

      if (platform == 'android') {
        BOX_takeGift(item, 0);
        return;
      }

      this.xhGameId = this.currentGame.game_info.id;
      this.exchangeGift = item;
      this.xhDialogShow = true;
    },
    async onXhSelectSuccess({ xhId }) {
      this.$toast.loading({ message: '加载中...' });
      this.xhDialogShow = false;
      try {
        const res = await ApiCardGet({
          cardId: this.exchangeGift.id,
          xhId,
        });
        this.exchangeGift = { ...this.exchangeGift, ...res.data };
        this.giftCopyPopupShow = true;

        // 神策埋点
        this.$sensorsTrack('game_rewards_claim', {
          game_id: `${this.exchangeGift.game_id}`,
          adv_id: '暂无',
          game_name: this.exchangeGift.titlegame,
          game_type: `${this.exchangeGift.classid}`,
          game_size: '暂无',
          reward_type: this.exchangeGift.title, // 传礼包名称
          data_source: this.$sensorsChainGet(),
        });
      } finally {
        setTimeout(() => {
          this.$toast.clear();
        }, 2000);
      }
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
};
</script>

<style lang="less" scoped>
.clock-in-page {
  box-sizing: border-box;
  width: 100%;
  background: #53cffd url(~@/assets/images/clock-in/clock-in-bg-2.png) center
    top no-repeat;
  background-size: 100% auto;
  overflow: hidden;
  padding-top: 1 * @rem;
  min-height: 100vh;
  .main {
    padding-bottom: 20 * @rem;
  }
  .float-banner {
    width: 100%;
    height: 137 * @rem;
    background: url('~@/assets/images/clock-in/clock-banner-new.png');
    background-size: 100% 137 * @rem;
    position: absolute;
    top: 44 * @rem;
    left: 0;
    z-index: 1;
  }
  .clock-rule-btn {
    position: absolute;
    width: 33 * @rem;
    height: 80 * @rem;
    right: 0;
    top: 51 * @rem;
    z-index: 2;
  }
  .section-container {
    box-sizing: border-box;
    width: 345 * @rem;
    background: rgba(255, 255, 255, 0.5);
    padding: 10 * @rem;
    margin: 0 auto;
    border-radius: 20 * @rem;
    position: relative;
    &.mt-95 {
      margin-top: 95 * @rem;
    }
    &.mt-16 {
      margin-top: 16 * @rem;
    }
    &.no-padding {
      padding: 0;
    }
    .close-btn {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/clock-in/close-btn.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
      position: absolute;
      top: -2 * @rem;
      right: -4 * @rem;
    }
  }
  .banner-container {
    width: 325 * @rem;
    margin: 16 * @rem auto 0;
    .banner-item {
      width: 325 * @rem;
      height: 86 * @rem;
      &:not(:first-of-type) {
        margin-top: 16 * @rem;
      }
    }
  }
  .top-bar-container {
    width: 325 * @rem;
    background: #fff;
    overflow: hidden;
    border-radius: 12 * @rem 12 * @rem;
    .top-reward {
      box-sizing: border-box;
      padding: 20 * @rem 0 0;
      .reward-title {
        width: 112 * @rem;
        height: 16 * @rem;
        background: url(~@/assets/images/clock-in/rewrad-title.png) left top
          no-repeat;
        background-size: 112 * @rem 16 * @rem;
        margin-left: 20 * @rem;
      }
      .reward-subtitle {
        font-size: 11 * @rem;
        line-height: 11 * @rem;
        color: rgba(36, 40, 64, 0.43);
        margin-top: 8 * @rem;
        margin-left: 20 * @rem;
      }
      .reward-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 14 * @rem 14 * @rem;
        padding: 0 * @rem 23 * @rem 0;
        margin-top: 13 * @rem;
        .reward-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 88 * @rem;
          background: rgba(245, 245, 246, 0.58);
          border-radius: 9 * @rem;
          &.last {
            grid-column: ~'2 / 4';
            padding: 0 18 * @rem;
            .days {
              margin-right: auto;
            }
            .reward-icon {
              margin-left: auto;
              transform: scale(75 / 42);
              transform-origin: right center;
            }
            .coins {
              margin-right: auto;
            }
          }
          .days {
            font-family:
              PingFangSC-Regular,
              PingFang SC;
            font-size: 14 * @rem;
            color: #242840;
            font-weight: bold;
            line-height: 14 * @rem;
            margin-top: 10 * @rem;
          }
          .reward-icon {
            width: 42 * @rem;
            height: 24 * @rem;
            .image-bg('~@/assets/images/clock-in/coin-new-2.png');
            background-size: auto 24 * @rem;
            margin-top: 7 * @rem;
            &.svip {
              background-image: url('~@/assets/images/clock-in/svip-new.png');
            }
          }
          .coins {
            padding: 0 5 * @rem;
            height: 18 * @rem;
            line-height: 18 * @rem;
            border-radius: 9 * @rem;
            background-color: #ffffff;
            text-align: center;
            font-size: 10 * @rem;
            font-weight: 500;
            color: #ff8819;
            white-space: nowrap;
            margin-top: 7 * @rem;
            &.can {
              color: rgba(255, 136, 25, 1);
              background-color: rgba(255, 239, 197, 0.7);
            }
            &.had {
              color: rgba(121, 121, 121, 1);
              background-color: rgba(238, 238, 238, 1);
            }
          }
        }
      }
    }

    .clock-in-info {
      padding: 5 * @rem 20 * @rem 0;
      .operate-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .total-days {
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-size: 12 * @rem;
          color: #797979;
          height: 48 * @rem;
          span {
            font-family: DINAlternate-Bold, DINAlternate;
            font-size: 42 * @rem;
            color: #686eff;
            font-weight: bold;
            line-height: 48 * @rem;
            margin-right: 2 * @rem;
          }
        }
        .record-item {
          font-size: 14 * @rem;
          font-weight: bold;
          color: #242840;
          line-height: 14 * @rem;
          margin-top: 10 * @rem;
          span {
            color: #686eff;
            font-weight: bold;
          }
        }
        .clock-in-btn {
          width: 87 * @rem;
          height: 42 * @rem;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
          font-size: 14 * @rem;
          font-weight: 500;
          color: #fff;
          display: flex;
          line-height: 38 * @rem;
          justify-content: center;
          .image-bg('~@/assets/images/clock-in/clock-in-btn-new.png');
          background-size: 87 * @rem 42 * @rem;
          margin-top: 20 * @rem;

          &.had {
            .image-bg('~@/assets/images/clock-in/clock-in-btn-had-new.png');
            position: relative;

            span {
              display: block;
              width: 78 * @rem;
              height: 25 * @rem;
              background: url('~@/assets/images/clock-in/tomorrow-sign-in.png')
                no-repeat;
              background-size: 78 * @rem 25 * @rem;
              position: absolute;
              left: 3 * @rem;
              top: -9 * @rem;
            }
          }
        }
      }
      .record-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15 * @rem;
      }
    }
  }

  .activity-container {
    box-sizing: border-box;
    width: 325 * @rem;
    background: #fff;
    overflow: hidden;
    border-radius: 12 * @rem 12 * @rem;
    padding: 14 * @rem 15 * @rem;
    .activity-title {
      height: 30 * @rem;
      line-height: 30 * @rem;
      background: url(~@/assets/images/clock-in/section-title-bg-1.png) left
        bottom no-repeat;
      background-size: 79 * @rem 20 * @rem;
      font-size: 16 * @rem;
      color: rgba(36, 40, 64, 1);
      font-weight: bold;
    }
    .welfare-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10 * @rem;
      .welfare-banner {
        width: 143 * @rem;
        height: 88 * @rem;
      }
    }
  }

  .exchange-container {
    box-sizing: border-box;
    width: 325 * @rem;
    background: #fff;
    overflow: hidden;
    border-radius: 12 * @rem 12 * @rem;
    padding: 14 * @rem 15 * @rem;
    .exchange-title {
      height: 30 * @rem;
      line-height: 30 * @rem;
      background: url(~@/assets/images/clock-in/section-title-bg-2.png) left
        bottom no-repeat;
      background-size: 107 * @rem 20 * @rem;
      font-size: 16 * @rem;
      color: rgba(36, 40, 64, 1);
      font-weight: bold;
      position: relative;
      .exchange-more {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 12 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/right-icon.png) center center no-repeat;
        background-size: 9 * @rem 15 * @rem;
      }
    }

    .exchange-content {
      margin-top: 10 * @rem;
      position: relative;
      .more-game-btn {
        width: 23 * @rem;
        height: 57 * @rem;
        background: url(~@/assets/images/clock-in/more-game-btn.png) center
          center no-repeat;
        background-size: 23 * @rem 57 * @rem;
        position: absolute;
        right: -15 * @rem;
        top: 6 * @rem;
        z-index: 10;
      }
      .nav-list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        overflow-x: auto;
        position: relative;
        &::-webkit-scrollbar {
          display: none;
        }
        .nav-item {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          height: 66 * @rem;
          margin-right: 10 * @rem;
          flex-shrink: 0;
          flex-grow: 0;
          .game-icon {
            width: 46 * @rem;
            height: 46 * @rem;
            border-radius: 10 * @rem;
            overflow: hidden;
          }
          .game-info {
            margin-left: 8 * @rem;
            min-width: 0;
            flex: 1;
            .game-title {
              display: flex;
              align-items: center;
              .title-text {
                font-size: 14 * @rem;
                color: #242840;
                font-weight: bold;
                overflow: hidden;
                .text {
                  white-space: nowrap;
                }
              }
              .title-tag {
                box-sizing: border-box;
                background: #dbedff;
                border-radius: 8 * @rem 8 * @rem 8 * @rem 0;
                height: 15 * @rem;
                margin-left: 4 * @rem;
                font-size: 10 * @rem;
                color: #686eff;
                font-weight: 400;
                white-space: nowrap;
                padding: 1 * @rem 4 * @rem;
                margin-right: -5 * @rem;
              }
            }
            .game-discount {
              display: inline-block;
              border: 0.5 * @rem solid #ff7171;
              border-radius: 2 * @rem;
              overflow: hidden;
              height: 17 * @rem;
              margin-top: 5 * @rem;
              .left {
                display: inline-block;
                background-color: #ffecec;
                height: 17 * @rem;
                line-height: 17 * @rem;
                font-size: 10 * @rem;
                color: #ff6b69;
                padding: 0 3 * @rem;
              }
              .right {
                display: inline-block;
                font-size: 10 * @rem;
                color: #ff6b69;
                height: 17 * @rem;
                line-height: 17 * @rem;
                padding: 0 4 * @rem;
                background: #fff;
              }
            }
          }
          &.active {
            background: #fff;
            width: 184 * @rem;
            height: 68 * @rem;
            margin-right: 12 * @rem;
            padding: 0 10 * @rem;
            border-radius: 12 * @rem;
            position: relative;
            z-index: 2;
            &::before {
              content: '';
              background: linear-gradient(
                180deg,
                rgba(203, 227, 255, 1),
                rgba(242, 247, 255, 1)
              );
              width: 184 * @rem;
              height: 68 * @rem;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              border-radius: 12 * @rem;
              z-index: -2;
            }
            &::after {
              content: '';
              background: linear-gradient(
                180deg,
                #ecf8ff 0%,
                rgba(255, 255, 255, 1) 100%
              );
              width: 182 * @rem;
              height: 66 * @rem;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              border-radius: 12 * @rem;
              z-index: -1;
            }
          }
        }
      }

      .exchange-list {
        padding-top: 7 * @rem;
        .exchange-item {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          position: relative;
          overflow: hidden;
          background: rgba(245, 245, 246, 0.58);
          border-radius: 9 * @rem;
          margin-top: 9 * @rem;
          width: 295 * @rem;
          height: 73 * @rem;
          padding: 0 10 * @rem;
          .limit {
            position: absolute;
            right: 0;
            top: 0;
            width: 58 * @rem;
            height: 21 * @rem;
            background: url(~@/assets/images/clock-in/exchange-limit-icon.png)
              center center no-repeat;
            background-size: 58 * @rem 21 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10 * @rem;
            color: #686eff;
          }
          .exchange-icon {
            width: 45 * @rem;
            height: 45 * @rem;
            border-radius: 11 * @rem;
            background: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 45 * @rem;
              height: 45 * @rem;
              object-fit: contain;
              border-radius: 8 * @rem;
            }
          }
          .exchange-info {
            flex: 1;
            min-width: 0;
            margin-left: 6 * @rem;
            .title {
              font-size: 14 * @rem;
              color: #242840;
              font-weight: bold;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .cost {
              font-size: 12 * @rem;
              color: #777777;
              line-height: 15 * @rem;
              margin-top: 8 * @rem;
              span {
                color: #ff6b69;
              }
            }
          }
          .exchange-btn {
            width: 64 * @rem;
            height: 25 * @rem;
            color: #fff;
            background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
            font-size: 11 * @rem;
            font-weight: 500;
            border-radius: 25 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10 * @rem;
          }
        }

        .exchange-down {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #777777;
          padding: 10 * @rem 0 0;
          i {
            display: block;
            width: 10 * @rem;
            height: 7 * @rem;
            margin-left: 4 * @rem;
            background: url(~@/assets/images/down-icon.png) center center
              no-repeat;
            background-size: 10 * @rem 7 * @rem;
          }
          &.up {
            i {
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }

  .calendar-container {
    width: 325 * @rem;
    overflow: hidden;
    background-color: #fff;
    padding-top: 23 * @rem;
    .title {
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-size: 14 * @rem;
      font-weight: bold;
      color: #000000;
      height: 20 * @rem;
      line-height: 20 * @rem;
      margin: 0 14 * @rem;
      text-align: center;
      position: relative;
      &:before {
        content: '';
        width: 100 * @rem;
        height: 0 * @rem;
        border-top: 0.5 * @rem dashed rgba(44, 67, 128, 0.26);
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      &:after {
        content: '';
        width: 100 * @rem;
        height: 0 * @rem;
        border-top: 0.5 * @rem dashed rgba(44, 67, 128, 0.26);
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .content {
      padding: 0 7 * @rem;
      .calendar-top {
        display: flex;
        flex-wrap: wrap;
        padding-top: 24 * @rem;
        .top-item {
          width: 14.28%;
          text-align: center;
          font-size: 12 * @rem;
          line-height: 17 * @rem;
          color: #797979;
        }
      }
      .day-list {
        display: flex;
        flex-wrap: wrap;
        padding: 10 * @rem 0 15 * @rem;
        .day-item {
          width: 14.28%;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 5 * @rem;
          &.day-pre,
          &.day-next {
            .day-btn {
              .num {
                color: #c1c1c1;
                font-size: 16 * @rem;
              }
            }
          }
          .day-btn {
            box-sizing: border-box;
            width: 34 * @rem;
            height: 42 * @rem;
            border-radius: 8 * @rem;
            .num {
              display: flex;
              align-items: center;
              justify-content: center;
              font-family:
                PingFangSC-Regular,
                PingFang SC;
              font-size: 16 * @rem;
              line-height: 20 * @rem;
              color: #000000;
              font-weight: 400;
              // width: 34 * @rem;
              margin-top: 4 * @rem;
            }
            .text {
              width: 34 * @rem;
              height: 14 * @rem;
              font-family:
                PingFangSC-Regular,
                PingFang SC;
              font-size: 12 * @rem;
              line-height: 14 * @rem;
              font-weight: 400;
              color: #686eff;
              transform: scale(0.8333);
              transform-origin: center top;
            }

            &.had {
              background-color: #f5f5f6;
              .num {
                color: #000000;
              }
              .text {
                transform: unset;
                background: url(~@/assets/images/clock-in/clock-had.png) center
                  center no-repeat;
                background-size: 15 * @rem 10 * @rem;
              }
            }
            &.can {
              background-color: #f5f5f6;
            }
            &.today {
              border: 1 * @rem solid #686eff;
              background: #e9f4ff;
              // &:after {
              //   content: "";
              //   width: 8 * @rem;
              //   height: 5 * @rem;
              //   background: url(~@/assets/images/clock-in/current-arrow.png);
              //   background-size: 8 * @rem 5 * @rem;
              //   position: absolute;
              //   bottom: -6 * @rem;
              //   left: 50%;
              //   transform: translateX(-50%);
              // }
              .num {
                color: #686eff;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    .calendar-open {
      width: 56 * @rem;
      height: 18 * @rem;
      background: url(~@/assets/images/clock-in/calendar-open.png) left center
        no-repeat;
      background-size: 56 * @rem 18 * @rem;
      margin: 0 auto;
      &.close {
        background-image: url(~@/assets/images/clock-in/calendar-close.png);
      }
    }
  }

  .today-game-container {
    box-sizing: border-box;
    height: 251 * @rem;
    border-radius: 12 * @rem;
    background-color: #ffffff;
    padding: 15 * @rem 15 * @rem 14 * @rem;
    .today-title {
      height: 30 * @rem;
      line-height: 30 * @rem;
      background: url(~@/assets/images/clock-in/section-title-bg-1.png) left
        bottom no-repeat;
      background-size: 79 * @rem 20 * @rem;
      font-size: 16 * @rem;
      color: rgba(36, 40, 64, 1);
      font-weight: bold;
    }
  }

  .sign-rule-container {
    box-sizing: border-box;
    padding: 20 * @rem 20 * @rem;
    background-color: #fff;
    border-radius: 12 * @rem;
    .rule-title {
      display: flex;
      align-items: center;
      justify-content: center;
      .rule-title-icon {
        width: 16 * @rem;
        height: 16 * @rem;
        background: url(~@/assets/images/clock-in/rule-title-icon.png) center
          center no-repeat;
        background-size: 16 * @rem 16 * @rem;
      }
      .rule-title-text {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: bold;
        margin-left: 6 * @rem;
        line-height: 22 * @rem;
      }
    }
  }
}
.sign-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 97 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-logo3.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -43 * @rem;
    z-index: 2;
    padding: 50 * @rem 23 * @rem 21 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
      no-repeat 0 0;
    background-size: 300 * @rem 270 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 25 * @rem;
      font-weight: normal;
      font-size: 18 * @rem;
      color: #191b1f;
      line-height: 25 * @rem;
      text-align: center;
      font-weight: bold;
    }
    .reward-box {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 21 * @rem;

      .reward-item {
        margin-left: 20 * @rem;

        &:first-of-type {
          margin-left: 0;
        }

        .exp-icon {
          width: 48 * @rem;
          height: 36 * @rem;
          background: url(~@/assets/images/clock-in/new-icon-exp.png) no-repeat;
          background-size: 48 * @rem 36 * @rem;
        }

        .gold-icon {
          width: 48 * @rem;
          height: 36 * @rem;
          background: url(~@/assets/images/clock-in/new-icon-coin.png) no-repeat;
          background-size: 48 * @rem 36 * @rem;
        }

        .reward-msg {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          margin-top: 2 * @rem;

          span {
            height: 11 * @rem;
            font-weight: bold;
            font-size: 9 * @rem;
            color: #191b1f;
            line-height: 11 * @rem;
          }

          .msg-number {
            height: 14 * @rem;
            font-weight: bold;
            font-size: 14 * @rem;
            color: #191b1f;
            line-height: 14 * @rem;
          }
        }

        &.svip-more-gold {
          margin-left: 7 * @rem;
          .gold-icon {
            background-image: url(~@/assets/images/clock-in/new-icon-coin2.png);
          }

          .reward-msg {
            span {
              color: #ff8819;
            }
            .msg-number {
              color: #ff8819;
            }
          }
        }
      }

      .upgrade {
        margin-left: 13 * @rem;

        .text {
          height: 14 * @rem;
          font-weight: bold;
          font-size: 10 * @rem;
          color: #ff8819;
          line-height: 14 * @rem;
          text-align: center;
        }

        .up-icon {
          width: 20 * @rem;
          height: 21 * @rem;
          background: url(~@/assets/images/clock-in/level-up-icon.png) no-repeat;
          background-size: 20 * @rem 21 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
    .svip-tips {
      width: 224 * @rem;
      padding: 8 * @rem 15 * @rem;
      margin: 13 * @rem auto 0;
      box-sizing: border-box;
      border-radius: 8 * @rem;
      background: linear-gradient(180deg, #eff3ff 0%, #ffffff 48 * @rem);

      .tips-title {
        height: 17 * @rem;
        font-weight: bold;
        font-size: 12 * @rem;
        color: #686eff;
        line-height: 17 * @rem;
        text-align: left;
        margin-bottom: 1 * @rem;
      }
      .small-tips {
        display: flex;
        align-items: center;
        height: 15 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #93999f;
        line-height: 15 * @rem;
        margin-top: 6 * @rem;

        &::before {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/welfare/tips-star-icon.png) no-repeat;
          background-size: 6 * @rem 6 * @rem;
          margin-right: 4 * @rem;
        }
      }
    }
    .btn-list {
      display: flex;
      align-content: center;
      justify-content: space-between;
      margin-top: 21 * @rem;
      .btn {
        flex: 1;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: #e9f4ff;
        border-radius: 30 * @rem;
        font-weight: bold;
        font-size: 15 * @rem;
        color: #686eff;
        text-align: center;
        margin-right: 14 * @rem;
        position: relative;

        &:last-of-type {
          background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
          color: #fff;
          margin-right: 0;
        }

        .svip-tip {
          width: unset;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 18 * @rem;
          padding: 0 8 * @rem;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 9 * @rem;
          color: #c05c2a;
          line-height: 11 * @rem;
          text-align: center;
          border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
          box-sizing: border-box;
          background: linear-gradient(
            90deg,
            #ffeaab 0%,
            #ffe0dd 54%,
            #ffbdcf 100%
          );
          border: 1 * @rem solid #ffffff;
          position: absolute;
          top: -13 * @rem;
          right: -8 * @rem;
        }
      }
    }
    .tips {
      margin-top: 12 * @rem;
      white-space: nowrap;
      height: 15 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 12 * @rem;
      color: #686eff;
      line-height: 15 * @rem;
      text-align: center;
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.repair-sign-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 134 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-logo1.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -83 * @rem;
    z-index: 2;
    padding: 84 * @rem 31 * @rem 25 * @rem;
    background: url('~@/assets/images/clock-in/sign-success-bg1.png') no-repeat
      0 0;
    background-size: 300 * @rem 270 * @rem;
    width: 300 * @rem;
    height: 270 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 34 * @rem;
      font-family:
        Dream Han Sans CN,
        Dream Han Sans CN;
      font-weight: normal;
      font-size: 24 * @rem;
      color: #333333;
      line-height: 34 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: bold;
    }
    .msg {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 25 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: bold;
      font-size: 15 * @rem;
      color: #8264ff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 22 * @rem 0 30 * @rem 0;
      .msg-number {
        font-weight: 500;
        font-size: 20 * @rem;
        &:not(:first-child) {
          margin-left: 10 * @rem;
        }
      }
    }
    .btn {
      width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      border-radius: 40 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .tips {
      margin-top: 8 * @rem;
      white-space: nowrap;
      height: 17 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #777777;
      line-height: 17 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.sign-popup-unset {
  /deep/ .van-dialog {
    background-color: unset;
    width: unset;
    border-radius: unset;
  }
}

.dialog-close {
  width: 50 * @rem;
  height: 50 * @rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10 * @rem auto 0;
  img {
    display: block;
    width: 30 * @rem;
    height: 30 * @rem;
  }
}
.box-success-dialog {
  width: 306 * @rem;
  overflow: unset;
}
.box-success {
  box-sizing: border-box;
  padding-top: 1 * @rem;
  position: relative;
  width: 306 * @rem;
  background: #fff3fc;
  border-radius: 12 * @rem;
  padding-bottom: 18 * @rem;
  overflow: unset;
  background: #fff url(~@/assets/images/exchange-bg.png) center top no-repeat;
  background-size: 306 * @rem 140 * @rem;
  .box-icon {
    width: 111 * @rem;
    height: 66 * @rem;
    background: url(~@/assets/images/clock-in/box-coin-new.png) center top
      no-repeat;
    background-size: 111 * @rem 66 * @rem;
    margin: -33 * @rem auto 0;
  }
  .box-success-text {
    width: 94 * @rem;
    height: 23 * @rem;
    background: url(~@/assets/images/clock-in/box-success-text.png) center
      center no-repeat;
    background-size: 94 * @rem 23 * @rem;
    margin: 8 * @rem auto 0;
  }
  .info-content {
    .info-text {
      font-size: 14 * @rem;
      color: #333333;
      text-align: center;
      margin-top: 0 * @rem;
      line-height: 18 * @rem;
      width: 220 * @rem;
      margin: 0 auto;
      &.red {
        color: @themeColor;
        font-weight: 400;
      }
      &.blue {
        color: #6b7eff;
      }
      &.small {
        width: unset;
        font-size: 11 * @rem;
        line-height: 14 * @rem;
        margin-top: 11 * @rem;
      }
    }
    .box-popup-text {
      font-size: 15 * @rem;
      color: #242840;
      text-align: center;
      margin-top: 0 * @rem;
      line-height: 20 * @rem;
      width: 220 * @rem;
      margin: 10 * @rem auto 0;
      &.blue {
        color: #6b7eff;
      }
      &.small {
        width: unset;
        font-size: 12 * @rem;
        line-height: 15 * @rem;
        margin-top: 20 * @rem;
      }
    }
  }
  .btn-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 18 * @rem auto 0;
    padding: 0 25 * @rem;
    .success-conform,
    .vip-conform {
      flex: 1;
      height: 36 * @rem;
      border-radius: 19 * @rem;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      font-weight: bold;
      color: #ffffff;
      margin: 0 5 * @rem;
    }
    .success-conform {
      background: #e9f4ff;
      color: #686eff;
    }
  }
}
/* 签到成功弹窗 */
.sign-success {
  width: 282 * @rem;
  height: 358 * @rem;
  .image-bg('~@/assets/images/clock-in/sign-success-bg.png');
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .dialog-close {
    width: 14 * @rem;
    height: 14 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    position: absolute;
    right: 12 * @rem;
    top: 74 * @rem;
    img {
      display: block;
      width: 14 * @rem;
      height: 14 * @rem;
    }
  }
  .success-info {
    box-sizing: border-box;
    padding-top: 17 * @rem;
    width: 252 * @rem;
    height: 184 * @rem;
    margin: 158 * @rem auto 0;
    border-radius: 8 * @rem;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
    .info-content {
      .info-text {
        font-size: 15 * @rem;
        color: #000000;
        text-align: center;
        margin-top: 8 * @rem;
        line-height: 21 * @rem;
        &.red {
          color: #ff7554;
          font-weight: bold;
        }
        &.small {
          font-size: 12 * @rem;
          line-height: 12 * @rem;
          margin-top: 2 * @rem;
        }
      }
    }
    .success-conform {
      width: 238 * @rem;
      height: 40 * @rem;
      border-radius: 40 * @rem;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15 * @rem;
      font-weight: 500;
      color: #ffffff;
      position: absolute;
      bottom: 16 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .success-conform-text {
      font-weight: 500;
      font-size: 12 * @rem;
      color: #777777;
      line-height: 17 * @rem;
      height: 17 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      white-space: nowrap;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
/*非svip点击补签弹窗*/
.repair-sign-tip-popup {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    position: relative;
    background: url('~@/assets/images/clock-in/repair-sign-tip-icon.png')
      no-repeat -23 * @rem -4 * @rem;
    background-size: 164 * @rem 96 * @rem;
    width: 117 * @rem;
    height: 92 * @rem;
    top: -35 * @rem;
    z-index: 3;
    margin: 0 auto;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -83 * @rem;
    z-index: 2;
    width: 300 * @rem;
    height: 192 * @rem;
    text-align: center;
    .box-bg {
      background: url(~@/assets/images/clock-in/repair-sign-tip-bg.png)
        no-repeat 0 0;
      background-size: 300 * @rem 160 * @rem;
      width: 300 * @rem;
      height: 139 * @rem;
      padding: 63 * @rem 31 * @rem 20 * @rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 187 * @rem;
        height: 40 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #242840;
        line-height: 20 * @rem;
        margin: 0 auto;
      }

      .btn {
        width: 238 * @rem;
        height: 40 * @rem;
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        border-radius: 40 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        line-height: 21 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .btn-box {
      height: 53 * @rem;
      line-height: 53 * @rem;
      display: flex;
      align-items: center;
      border: 0.5 * @rem solid rgba(161, 163, 173, 0.21);
      .btn-cancel {
        flex: 1;
        width: 51 * @rem;
        height: 20 * @rem;
        font-weight: 400;
        font-size: 16 * @rem;
        color: #a1a3ad;
        line-height: 20 * @rem;
        text-align: center;
      }
      .btn-open {
        border-left: 0.5 * @rem solid rgba(161, 163, 173, 0.21);
        flex: 1;
        height: 20 * @rem;
        font-weight: 500;
        font-size: 16 * @rem;
        color: rgba(30, 30, 18, 0.78);
        line-height: 20 * @rem;
        text-align: center;
      }
    }
  }
}
/* svip补签提示弹窗 */
.svip-repair-sign-tip-popup {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    position: relative;
    background: url('~@/assets/images/clock-in/svip-repair-sign-tip-icon.png')
      no-repeat -23 * @rem -4 * @rem;
    background-size: 164 * @rem 97 * @rem;
    width: 117 * @rem;
    height: 93 * @rem;
    top: -34 * @rem;
    z-index: 3;
    margin: 0 auto;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    background-color: #fff;
    border-radius: 16 * @rem;
    margin-top: -83 * @rem;
    z-index: 2;
    width: 300 * @rem;
    height: 192 * @rem;
    text-align: center;
    .box-bg {
      background: url(~@/assets/images/clock-in/repair-sign-tip-bg.png)
        no-repeat 0 0;
      background-size: 300 * @rem 160 * @rem;
      width: 300 * @rem;
      height: 192 * @rem;
      padding: 63 * @rem 31 * @rem 20 * @rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 160 * @rem;
        height: 40 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #242840;
        line-height: 20 * @rem;
        margin: 0 auto;
      }
      .btn {
        width: 238 * @rem;
        height: 40 * @rem;
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        border-radius: 40 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        line-height: 21 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
.repair-sign-tip {
  box-sizing: border-box;
  width: 290 * @rem;
  height: 170 * @rem;
  border-radius: 10 * @rem;
  background-color: #fff;
  padding: 28 * @rem 42 * @rem;
  .tip-text {
    font-size: 15 * @rem;
    color: #333333;
    line-height: 24 * @rem;
    text-align: center;
    margin-top: 6 * @rem;
    letter-spacing: 2 * @rem;
    span {
      font-weight: bold;
      color: #ffb400;
    }
  }
  .repair-btn {
    width: 180 * @rem;
    height: 38 * @rem;
    border-radius: 6 * @rem;
    background-color: #47a83a;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 15 * @rem;
    margin: 25 * @rem auto 0;
  }
}

/* 补签成功弹窗 */
.repair-sign-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  .repair-num {
    font-size: 20 * @rem;
    color: #fff;
    font-weight: bold;
    span {
      color: #ffc900;
    }
  }
  .coin-icon {
    width: 123 * @rem;
    height: 124 * @rem;
    margin-top: 18 * @rem;
  }
  .text {
    margin-top: 10 * @rem;
    text-align: center;
    font-size: 15 * @rem;
    color: #ffffff;
    line-height: 24 * @rem;
  }
}
/* 连续签到领宝箱弹窗 */
.gold-box-success {
  box-sizing: border-box;
  width: 290 * @rem;
  height: 150 * @rem;
  border-radius: 10 * @rem;
  background-color: #fff;
  padding: 28 * @rem 10 * @rem;
  overflow: hidden;
  .success-text {
    font-size: 15 * @rem;
    color: #333333;
    text-align: center;
    margin-top: 20 * @rem;
    span {
      color: #ffb400;
      font-weight: bold;
    }
  }
  .confirm {
    display: flex;
    justify-content: flex-end;
    font-size: 16 * @rem;
    color: #40b640;
    margin-top: 30 * @rem;
    padding: 10 * @rem;
    float: right;
    letter-spacing: 2 * @rem;
  }
}
/* 宝箱分享弹窗 */
.gold-box-share {
  box-sizing: border-box;
  position: relative;
  width: 300 * @rem;
  background-color: #fff;
  border-radius: 10 * @rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24 * @rem 13 * @rem 30 * @rem;
  .close {
    width: 40 * @rem;
    height: 40 * @rem;
    background: url(~@/assets/images/clock-in/close-share.png) center center
      no-repeat;
    background-size: 21 * @rem 21 * @rem;
    position: absolute;
    right: 0;
    top: 0;
  }
  .top-text {
    font-size: 20 * @rem;
    color: #333333;
    &.orange {
      color: #ff641d;
    }
  }
  .title {
    font-size: 25 * @rem;
    font-weight: bold;
    color: #000000;
    margin-top: 10 * @rem;
    span {
      color: #ff641d;
      font-weight: bold;
    }
  }
  .tip {
    font-size: 16 * @rem;
    color: #ff8549;
    margin-top: 10 * @rem;
  }
  .sub-tip {
    font-size: 14 * @rem;
    color: #999999;
    margin-top: 10 * @rem;
  }
  .help-man {
    margin-top: 40 * @rem;
  }
  .help-svip {
    font-size: 18 * @rem;
    color: #000000;
    margin-top: 20 * @rem;
  }
  .avatar {
    width: 50 * @rem;
    height: 50 * @rem;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 35 * @rem;
  }
  .avatar-icon {
    margin-top: 15 * @rem;
  }
  .operate {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 48 * @rem;
    .common-invite {
      width: 130 * @rem;
      height: 43 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16 * @rem;
      border-radius: 5 * @rem;
      border: 1 * @rem solid #bfbfbf;
      color: #666666;
    }
    .svip-invite {
      width: 130 * @rem;
      height: 43 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16 * @rem;
      border-radius: 5 * @rem;
      background-color: #ffa820;
      color: #fff;
    }
    .confirm {
      width: 200 * @rem;
      height: 43 * @rem;
      border-radius: 5 * @rem;
      background-color: #ffa820;
      color: #fff;
      font-size: 16 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
  }
  .get-gold-num {
    font-size: 28 * @rem;
    color: #ff8a00;
    font-weight: bold;
    margin-top: 15 * @rem;
  }
  .svip-num {
    font-size: 28 * @rem;
    color: #ff8a00;
    margin-top: 60 * @rem;
  }
}
.yindao-popup {
  width: 250 * @rem;
  padding: 40 * @rem 35 * @rem 20 * @rem;
  background-color: #fff;
  border-radius: 10 * @rem;
  line-height: 28 * @rem;
  .close {
    position: absolute;
    top: 15 * @rem;
    right: 15 * @rem;
    width: 16 * @rem;
    height: 16 * @rem;
    background-image: url(~@/assets/images/close-black.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .text {
    text-align: center;
    font-size: 15 * @rem;
  }
  .down-arrow {
    margin: 15 * @rem auto 0;
    width: 16 * @rem;
    height: 20 * @rem;
    background-image: url(~@/assets/images/clock-in/down-arrow.png);
    background-size: 100%;
    background-repeat: no-repeat;
    -webkit-animation: downward 0.8s ease-in-out infinite;
    animation: downward 0.8s ease-in-out infinite;
  }
}
.award-before-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    z-index: 2;
    padding: 32 * @rem 12 * @rem 20 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
      no-repeat 0 0;
    background-size: 300 * @rem 264 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 34 * @rem;
      font-family:
        Dream Han Sans CN,
        Dream Han Sans CN;
      font-weight: normal;
      font-size: 22 * @rem;
      color: #222222;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 34 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: bold;

      &.fzs {
        font-size: 22 * @rem;
      }
    }
    .detail {
      width: 80 * @rem;
      height: 80 * @rem;
      position: relative;
      margin: 8 * @rem auto;

      img {
        width: 100%;
        height: 100%;
      }
      .double {
        height: 16 * @rem;
        background: #ff674c;
        border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
        padding: 0 5 * @rem;
        font-weight: 500;
        font-size: 10 * @rem;
        color: #ffffff;
        line-height: 16 * @rem;
        text-align: center;
        position: absolute;
        top: 6 * @rem;
        right: -28 * @rem;
      }
      .gold-num {
        height: 20 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 16 * @rem;
        position: absolute;
        bottom: 4 * @rem;
        left: 53 * @rem;
      }

      .svip-num {
        height: 20 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 16 * @rem;
        position: absolute;
        bottom: 4 * @rem;
        left: 53 * @rem;
        white-space: nowrap;
      }
    }
    .msg {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #999999;
      text-align: center;
      line-height: 17 * @rem;
      margin-top: 4 * @rem;
      overflow: hidden;
      .msg-number {
        margin-top: 4 * @rem;
      }
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 20 * @rem 11 * @rem 0;
    }
    .btn {
      width: 100%;
      max-width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: #e9f4ff;
      border-radius: 30 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #686eff;
      text-align: center;
      margin: 0 auto;
      margin-right: 14 * @rem;

      &:last-of-type {
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        color: #fff;
        margin-right: auto;
      }
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.award-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    width: 164 * @rem;
    height: 97 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-logo3.png');
    margin: 0 auto;
    position: relative;
    z-index: 3;
    &.svip {
      background-image: url('~@/assets/images/clock-in/sign-success-logo5.png');
    }
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    z-index: 2;
    padding: 49 * @rem 12 * @rem 20 * @rem;
    margin-top: -48 * @rem;
    background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
      no-repeat 0 0;
    background-size: 300 * @rem 264 * @rem;
    width: 300 * @rem;
    text-align: center;
    .title {
      white-space: nowrap;
      height: 34 * @rem;
      font-family:
        Dream Han Sans CN,
        Dream Han Sans CN;
      font-weight: normal;
      font-size: 24 * @rem;
      color: #fe6600;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 34 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: bold;
    }
    .msg {
      font-family:
        PingFang SC,
        PingFang SC;
      font-size: 15 * @rem;
      color: #333333;
      text-align: center;
      line-height: 19 * @rem;
      margin-top: 7 * @rem;
      overflow: hidden;
      .msg-number {
        margin-top: 3 * @rem;
        font-weight: bold;
      }
    }
    .tips {
      height: 15 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #6b7eff;
      line-height: 15 * @rem;
      text-align: center;
      margin-top: 20 * @rem;
    }
    .btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 26 * @rem 11 * @rem 0;
    }
    .btn {
      width: 100%;
      max-width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: #e9f4ff;
      border-radius: 30 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #686eff;
      text-align: center;
      margin: 0 auto;
      margin-right: 14 * @rem;

      &:last-of-type {
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        color: #fff;
        margin-right: auto;
      }
    }
    .dialog-close-btn {
      position: absolute;
      bottom: -58 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
      background-size: 28 * @rem 28 * @rem;
      width: 28 * @rem;
      height: 28 * @rem;
    }
  }
}
.no-svip-sign-dislog {
  background: #fff url(~@/assets/images/welfare/welfare-popup-bg-blue.png)
    no-repeat top;
  background-size: 100% auto;
  width: 300 * @rem;
  border-radius: 16 * @rem;
  padding-top: 50 * @rem;
  overflow: unset;
  .top-icon {
    width: 164 * @rem;
    height: 105 * @rem;
    .image-bg('~@/assets/images/welfare/popup-top-svip-icon.png');
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -58 * @rem;
  }
  .no-svip-tip {
    .line {
      height: 25 * @rem;
      font-weight: bold;
      font-size: 18 * @rem;
      color: #191b1f;
      line-height: 25 * @rem;
      text-align: center;
      margin-bottom: 20 * @rem;
    }
    .small-tips {
      display: flex;
      align-items: center;
      height: 17 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #93999f;
      line-height: 17 * @rem;
      margin-top: 7 * @rem;
      padding-left: 62 * @rem;

      &::before {
        content: '';
        display: block;
        width: 6 * @rem;
        height: 6 * @rem;
        background: url(~@/assets/images/welfare/tips-star-icon.png) no-repeat;
        background-size: 6 * @rem 6 * @rem;
        margin-right: 4 * @rem;
      }
    }
  }
  .no-notice {
    text-align: center;
    color: #999;
    font-size: 12 * @rem;
    padding: 16 * @rem 0 21 * @rem;
    line-height: 12 * @rem;
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 12 * @rem;
      .gou {
        width: 12 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/welfare/select-no.png) center center
          no-repeat;
        background-size: 12 * @rem 12 * @rem;
        margin-right: 4 * @rem;
      }
    }
    &.remember {
      .content {
        .gou {
          background-image: url(~@/assets/images/welfare/select-yes.png);
        }
      }
    }
  }
  .operation {
    display: flex;
    align-items: center;
    padding: 0 22 * @rem;
    justify-content: space-between;
    margin: 29 * @rem auto 0;
    .clock-btn {
      width: 120 * @rem;
      height: 40 * @rem;
      background: #e9f4ff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 30 * @rem;
      font-size: 15 * @rem;
      line-height: 19 * @rem;
      color: #686eff;
      font-weight: bold;
      position: relative;

      span {
        font-weight: normal;
        color: rgba(104, 110, 255, 0.8);
        font-size: 9 * @rem;
        line-height: 12 * @rem;
      }
      &.svip {
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        color: #fff;
        span {
          color: #fff;
        }

        .svip-tips {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 18 * @rem;
          padding: 0 8 * @rem;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: bold;
          font-size: 9 * @rem;
          color: #c05c2a;
          line-height: 11 * @rem;
          text-align: center;
          border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
          box-sizing: border-box;
          background: linear-gradient(
            90deg,
            #ffeaab 0%,
            #ffe0dd 54%,
            #ffbdcf 100%
          );
          border: 1 * @rem solid #ffffff;
          position: absolute;
          top: -13 * @rem;
          right: -7 * @rem;
        }
      }
    }
  }
  .close-btn {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url(~@/assets/images/welfare/popup-bottom-close-btn.png)
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
    position: absolute;
    bottom: -48 * @rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
@keyframes downward {
  0% {
  }
  50% {
    transform: translate(0, 10 * @rem);
  }
  to {
    transform: translate(0, 0);
  }
}
.my-swipe {
  height: 197 * @rem;
  /deep/ .van-swipe__indicators {
    bottom: 8 * @rem;
    .van-swipe__indicator {
      width: 6 * @rem;
      height: 6 * @rem;
      border-radius: 3 * @rem;
      background: #d8d8d8;
      transition: 0.3;
    }
    .van-swipe__indicator--active {
      width: 16 * @rem;
      border-radius: 3 * @rem;
    }
  }
}
.swiper-item {
  width: 295 * @rem;
  height: 164 * @rem;
  border-radius: 8 * @rem;
  overflow: hidden;
  background-color: #f8f8fa;
  margin-top: 8 * @rem;
  .game-line {
    box-sizing: border-box;
    height: 108 * @rem;
    padding: 4 * @rem 10 * @rem;
  }
  .reward-line {
    box-sizing: border-box;
    height: 56 * @rem;
    padding: 7 * @rem 10 * @rem;
    background-color: #f1f2fa;
    display: flex;
    align-items: center;
    .reward-icon {
      width: 42 * @rem;
      height: 42 * @rem;
    }
    .reward-content {
      flex: 1;
      min-width: 0;
      margin: 0 8 * @rem;
      font-size: 12 * @rem;
      color: #242840;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        color: @themeColor;
      }
    }
    .reward-btn {
      width: 52 * @rem;
      height: 22 * @rem;
      text-align: center;
      line-height: 22 * @rem;
      font-size: 11 * @rem;
      color: #ffffff;
      border-radius: 11 * @rem;
      background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
      border: 2 * @rem solid rgba(255, 253, 239, 0.6);
    }
  }
  /deep/ .game-item-components {
    padding: 8 * @rem 0;
    .game-icon {
      width: 76 * @rem;
      height: 76 * @rem;
      flex: 0 0 76 * @rem;
    }
    .game-info {
      height: 70 * @rem;
      .game-name {
        font-size: 14 * @rem;
        font-weight: bold;
        line-height: 20 * @rem;
        .game-subtitle {
          border: none;
          background: #ffffff;
          font-size: 12 * @rem;
          color: #808080;
          padding: 3 * @rem 5 * @rem;
          border: 1 * @rem solid #f5f5f6;
          border-radius: 5 * @rem;
        }
      }
      .game-bottom {
        font-size: 11 * @rem;
        line-height: 16 * @rem;
        font-size: 12 * @rem;
        .score {
          font-weight: bold;
          color: @themeColor;
          &::before {
            content: '';
            display: block;
            width: 8 * @rem;
            height: 8 * @rem;
            margin-right: 4 * @rem;
            .image-bg('~@/assets/images/category/star.png');
          }
        }
      }
      .tags {
        height: unset;
        overflow: unset;
        .tag {
          background: #ffefee;
          border: 1 * @rem solid #ff471f;
          color: #ff471f;
          font-size: 12 * @rem;
          padding: 2 * @rem 4 * @rem;
        }
        .tag:nth-of-type(2n) {
          background: #fff8f4;
          color: #ff8a00;
          border: 1 * @rem solid #ff471f;
        }
        .tag:nth-of-type(n + 3) {
          display: none;
        }
      }
    }
  }
}
.rule-popup {
  background: #fff;
  .popup-content {
    padding: 24 * @rem 18 * @rem 0;
    max-height: 600 * @rem;
    .popup-title {
      width: 114 * @rem;
      height: 28 * @rem;
      margin: 0 auto;
    }
    /deep/ .rule-content {
      font-size: 14 * @rem;
      line-height: 22 * @rem;
      color: #000000;
      margin-top: 12 * @rem;
      padding-bottom: 10 * @rem;
      a {
        text-decoration: underline;
        color: #686eff !important;
      }
    }
  }
  .close {
    width: 20 * @rem;
    height: 20 * @rem;
    background: url('~@/assets/images/clock-in/clock-in-close-btn.png')
      no-repeat center center;
    background-size: 20 * @rem 20 * @rem;
    position: absolute;
    right: 18 * @rem;
    top: 16 * @rem;
  }
}

.text-scroll {
  flex-shrink: 0;
  flex-grow: 1;
  white-space: nowrap;
  animation: scroll-left 4s linear forwards infinite;
}
@keyframes scroll-left {
  50% {
    transform: translateX(0%);
  }
  99.99% {
    transform: translateX(-120%);
  }
  100% {
    transform: translateX(0%);
  }
}
</style>
