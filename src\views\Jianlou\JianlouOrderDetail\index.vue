<template>
  <div class="page jianlou-order-detail-page">
    <nav-bar-2 :border="true" :title="$t('捡漏订单详情')"></nav-bar-2>
    <div class="main">
      <div class="order-info">
        <div class="top-content">
          <div class="title">{{ $t('订单信息') }}</div>
          <div class="cur-status">
            <span :style="{ color: jianlouInfo.status_info.color }">{{
              jianlouInfo.status_info.str
            }}</span>
          </div>
        </div>
        <div class="order-content" v-if="jianlouInfo.order_id">
          <div class="content-title">{{ $t('订单号') }}：</div>
          <div class="content-text">{{ jianlouInfo.order_id }}</div>
        </div>
        <div class="order-content" v-if="jianlouInfo.trade_no">
          <div class="content-title">{{ $t('交易流水号') }}：</div>
          <div class="content-text">{{ jianlouInfo.trade_no }}</div>
        </div>
        <div class="order-content" v-if="jianlouInfo.create_time">
          <div class="content-title">{{ $t('创建时间') }}：</div>
          <div class="content-text">
            {{ jianlouInfo.create_time | formatTime }}
          </div>
        </div>
        <div class="order-content" v-if="jianlouInfo.pay_time">
          <div class="content-title">{{ $t('支付时间') }}：</div>
          <div class="content-text">
            {{ jianlouInfo.pay_time | formatTime }}
          </div>
        </div>
        <div class="order-content" v-if="jianlouInfo.pay_mode_str">
          <div class="content-title">{{ $t('支付方式') }}：</div>
          <div class="content-text">
            {{ jianlouInfo.pay_mode_str }}{{ $t('支付') }}
          </div>
        </div>
      </div>
      <div class="xiaohao-info">
        <div class="jianlou-item">
          <div class="left-info">
            <div class="game-icon">
              <img :src="jianlouInfo.game.titlepic" />
            </div>
          </div>
          <div class="center-info">
            <div class="game-name">
              {{ jianlouInfo.game.main_title
              }}<span class="game-subtitle" v-if="jianlouInfo.game.subtitle">{{
                jianlouInfo.game.subtitle
              }}</span>
            </div>
            <div class="game-area">
              {{ $t('区服') }}：{{ jianlouInfo.game_area }}
            </div>
            <div class="xh-id">{{ $t('小号ID') }}：{{ jianlouInfo.xh_id }}</div>
          </div>
          <div class="right-info">
            <div class="price">
              ¥<span>{{ Number(jianlouInfo.rmb).toFixed(1) }}</span>
            </div>
            <div class="platforms">
              <div
                class="plat"
                v-for="(item, index) in jianlouInfo.platforms"
                :key="index"
              >
                <img :src="item.icon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-bar center-fixed">
      <div
        class="operate-btn recharge-btn"
        v-if="statusIn([0])"
        @click="handlePay()"
      >
        {{ $t('支付') }}
      </div>
      <div
        class="operate-btn help-btn"
        v-if="statusIn([1])"
        @click="handleHelp()"
      >
        {{ $t('如何登录') }}
      </div>
      <div
        class="operate-btn cancel-btn"
        v-if="statusIn([0])"
        @click="handleCancel()"
      >
        {{ $t('取消') }}
      </div>
      <div
        class="operate-btn delete-btn"
        v-if="statusIn([1, 2])"
        @click="handleDelete()"
      >
        {{ $t('删除') }}
      </div>
    </div>
    <!-- 如何登录 -->
    <van-dialog
      v-model="isHelpShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      get-container="body"
    >
      <div class="help-container">
        <div class="title">{{ $t('登录说明') }}</div>
        <div class="help-content">
          <div class="help-p">
            {{ $t('1、直接使用您当前登录APP的账号进入游戏即可。') }}
          </div>
          <div class="help-p">{{ $t('2、登录游戏后选择您购买的小号。') }}</div>
          <img
            class="help-img"
            src="@/assets/images/deal/login-help.png"
            alt=""
          />
          <div class="help-p">
            {{ $t('3、如有任何疑问，可联系') }}<span>{{ $t('客服咨询') }}</span
            >！
          </div>
        </div>
        <div class="confirm-btn btn" @click="isHelpShow = false">
          {{ $t('我知道了') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiXiaohaoChangeOrderStatusJL } from '@/api/views/xiaohao.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'JianlouOrderDetail',
  data() {
    return {
      jianlouInfo: {},
      isHelpShow: false,
    };
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  created() {
    this.jianlouInfo = this.$route.params.info;
  },
  methods: {
    statusIn(statusArr) {
      let index = statusArr.findIndex(item => {
        return Number(this.jianlouInfo.status) == item;
      });
      return index == -1 ? false : true;
    },
    async changeOrderStatus(status) {
      try {
        this.$toast({
          type: 'loading',
          duration: 0,
          message: this.$t('加载中...'),
        });
        // status   2 = 已取消 100 删除
        const res = await ApiXiaohaoChangeOrderStatusJL({
          status: status,
          orderId: this.jianlouInfo.order_id,
        });
      } finally {
        this.$toast.clear();
      }
    },
    handleHelp() {
      this.isHelpShow = true;
    },
    handlePay() {
      this.toPage('XiaohaoOrderRecharge', {
        info: this.jianlouInfo,
        order_type: 202, // 小号捡漏
        xhInfo: this.jianlouInfo,
        back: 'MyJianlou',
      });
    },
    handleCancel() {
      this.$dialog
        .confirm({
          message: this.$t(
            '取消支付后，该角色可能会被抢走哦~是否确认取消支付?',
          ),
          confirmButtonText: this.$t('立即付款'),
          cancelButtonText: this.$t('取消支付'),
          closeOnClickOverlay: false,
          lockScroll: false,
        })
        .then(async () => {
          // 立即付款
          this.handlePay();
        })
        .catch(async () => {
          // 取消支付
          await this.changeOrderStatus(2);
          this.$router.go(-1);
        });
    },
    handleDelete() {
      this.$dialog
        .confirm({
          message: this.$t('删除后无法恢复，确认删除？'),
          lockScroll: false,
        })
        .then(async () => {
          await this.changeOrderStatus(100);
          this.$router.go(-1);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.jianlou-order-detail-page {
  background-color: #f6f6f6;
  .main {
    box-sizing: border-box;
    background-color: #f6f6f6;
    min-height: calc(100vh - 50 * @rem - @safeAreaTop);
    min-height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    padding-bottom: calc(70 * @rem + @safeAreaBottom);
    padding-bottom: calc(70 * @rem + @safeAreaBottomEnv);
    .order-info {
      box-sizing: border-box;
      padding: 0 12 * @rem 12 * @rem;
      width: 351 * @rem;
      border-radius: 10 * @rem;
      background-color: #fff;
      margin: 12 * @rem auto 0;
      .top-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 52 * @rem;
        border-bottom: 0.5 * @rem solid #ebebeb;
        .title {
          font-size: 18 * @rem;
          color: #000000;
          font-weight: 600;
        }
        .cur-status {
          font-size: 14 * @rem;
          color: #797979;
          span {
            color: @themeColor;
          }
        }
      }
      .order-content {
        display: flex;
        justify-content: space-between;
        font-size: 14 * @rem;
        color: #666666;
        margin-top: 16 * @rem;
        .content-title {
          color: #797979;
        }
        .content-text {
          color: #000000;
        }
      }
    }
    .xiaohao-info {
      box-sizing: border-box;
      width: 351 * @rem;
      margin: 10 * @rem auto 0;
      border-radius: 12 * @rem;
      background-color: #fff;
      padding: 12 * @rem;
      .jianlou-item {
        background: #ffffff;
        display: flex;
        align-items: center;
        .left-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .game-icon {
            width: 70 * @rem;
            height: 70 * @rem;
            border-radius: 8 * @rem;
            overflow: hidden;
          }
        }
        .center-info {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            color: #000000;
            font-weight: 600;
            line-height: 22 * @rem;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1 * @rem solid fade(@themeColor, 80);
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              color: @themeColor;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .game-area {
            font-size: 13 * @rem;
            color: @themeColor;
            line-height: 18 * @rem;
            margin-top: 4 * @rem;
          }
          .xh-id {
            font-size: 13 * @rem;
            color: #797979;
            line-height: 18 * @rem;
            margin-top: 8 * @rem;
          }
        }
        .right-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .price {
            font-size: 12 * @rem;
            font-weight: 600;
            color: @themeColor;
            margin-top: -5 * @rem;
            span {
              font-size: 20 * @rem;
              font-weight: 600;
              color: @themeColor;
            }
          }
          .platforms {
            display: flex;
            align-items: center;
            margin-top: 22 * @rem;
            .plat {
              width: 17 * @rem;
              height: 17 * @rem;
              margin-left: 7 * @rem;
            }
          }
        }
      }
    }
  }
  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 60 * @rem;
    left: 0;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 15 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .operate-btn {
      height: 30 * @rem;
      padding: 0 22 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #999999;
      border: 1px solid #d2d2d2;
      border-radius: 15 * @rem;
      margin-left: 10 * @rem;
    }
    .recharge-btn {
      border: 1px solid #ffa811;
      color: #ffa811;
    }
    .cancel-btn {
      border: 1px solid #bfbfbf;
      color: #bfbfbf;
    }
    .delete-btn {
      border: 1px solid #bfbfbf;
      color: #bfbfbf;
    }
    .help-btn {
      border: 1px solid #ffa811;
      color: #ffa811;
    }
  }
}
.help-container {
  box-sizing: border-box;
  width: 320 * @rem;
  padding: 20 * @rem 15 * @rem;
  .title {
    font-size: 20 * @rem;
    color: #000000;
    text-align: center;
  }
  .help-content {
    font-size: 15 * @rem;
    color: #000000;
    line-height: 20 * @rem;
    .help-p {
      margin-top: 15 * @rem;
      span {
        color: #1795ff;
      }
    }
    .help-img {
      width: 260 * @rem;
      height: 168 * @rem;
      margin: 10 * @rem auto 0;
    }
  }
  .confirm-btn {
    height: 45 * @rem;
    background: @themeBg;
    border-radius: 5 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16 * @rem;
    color: #ffffff;
    margin-top: 20 * @rem;
  }
}
</style>
