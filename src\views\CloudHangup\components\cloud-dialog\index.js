import Vue from 'vue';
import cloudDialog from './index.vue';
import router from '@/router';
import store from '@/store';

const CloudDialog = Vue.extend(cloudDialog);

// const options = {
//   // 配置项
//   title: '',
//   desc: '',
//   cancelText: '',
//   confirmText: '',
//   showCancel: false,
//   showConfirm: true,
//   onCancel: () => {},
//   onConfirm: () => {},
// }

function useCloudDialog(options) {
  return new Promise((resolve, reject) => {
    const dialog = new CloudDialog({
      router,
      store,
    });

    dialog.$mount(document.createElement('div'));
    document.body.appendChild(dialog.$el);

    dialog.$el.addEventListener(
      'animationend',
      () => {
        if (dialog.show == false) {
          dialog.$destroy();
          dialog.$el.parentNode.removeChild(dialog.$el);
        }
      },
      false,
    );

    dialog.title = options.title || '提示';
    dialog.desc = options.desc || '';
    dialog.cancelText = options.cancelText || '取消';
    dialog.confirmText = options.confirmText || '确定';
    dialog.showCancel = options.showCancel || false;
    dialog.showConfirm = options.showConfirm || true;
    dialog.isConfirm1 = options.isConfirm1 || false;
    if (options.onCancel) {
      dialog.onCancel = () => {
        options.onCancel();
        dialog.show = false;
      };
    }
    if (options.onConfirm) {
      dialog.onConfirm = () => {
        options.onConfirm();
        dialog.show = false;
      };
    }
  });
}

export default useCloudDialog;
