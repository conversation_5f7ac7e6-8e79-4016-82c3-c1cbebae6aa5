<template>
  <div class="wrap" v-if="showWrap" :class="showContent ? 'fadein' : 'fadeout'">
    <div class="logo">
      <img :src="logoIcon" alt="" />
    </div>
    <div class="msg">{{ msg }}</div>
  </div>
</template>
<script>
import taskSuccessImg from '@/assets/images/cloud-game/task-success-img.png';
export default {
  data() {
    return {
      logoIcon: taskSuccessImg,
      msg: '',
      showWrap: true,
      showContent: true,
    };
  },
};
</script>
<style scoped lang="less">
.wrap {
  position: fixed;
  top: 50%;
  left: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  box-sizing: content-box;
  min-width: 132 * @rem;
  height: 72 * @rem;
  max-width: 70%;
  min-height: 72 * @rem;
  color: #fff;
  font-size: 14 * @rem;
  white-space: pre-wrap;
  text-align: center;
  word-break: break-all;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 12 * @rem;
  opacity: 0.9;
  z-index: 999999;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  padding: 12 * @rem 15 * @rem;
  box-sizing: border-box;
  .logo {
    background-size: 24 * @rem 24 * @rem;
    width: 24 * @rem;
    height: 24 * @rem;
  }
  .msg {
    margin-top: 4 * @rem;
    white-space: nowrap;
    font-size: 14 * @rem;
    color: #ffffff;
    height: 16 * @rem;
    line-height: 16 * @rem;
  }
}
.fadein {
  animation: animate_in 0.25s;
}
.fadeout {
  animation: animate_out 0.25s;
  opacity: 0;
}
@keyframes animate_in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes animate_out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
