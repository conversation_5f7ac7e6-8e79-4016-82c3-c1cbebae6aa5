<template>
  <div class="page">
    <nav-bar-2 ref="navBar" title="开局号" :border="false" :azShow="true">
      <template #right>
        <div class="more-btn btn" @click="clickMore">更多</div>
      </template>
    </nav-bar-2>
    <div class="main">
      <van-sticky ref="container" :offset-top="navBarHeight">
        <div class="top-container">
          <van-dropdown-menu active-color="#32B768">
            <van-dropdown-item
              :title="selectedArea"
              v-model="selectedArea"
              :options="showArea"
              @change="onSelectedAreaChange"
            >
            </van-dropdown-item>
            <van-dropdown-item
              :title="`分类:${selectedCateTitle}`"
              ref="cateRef"
              v-model="selectedCateId"
            >
              <div
                class="cate-container"
                v-for="(item, index) in showCate"
                :key="index"
              >
                <div class="cate-title">{{ item.title }}</div>
                <div class="cate-list">
                  <div
                    class="cate-item"
                    :class="{
                      disable: cur.cate_count <= 0,
                      current: clickedCate.id == cur.id,
                    }"
                    v-for="(cur, curIndex) in item.children"
                    :key="curIndex"
                    @click="clickCate(cur)"
                  >
                    {{ cur.title }}
                  </div>
                </div>
              </div>

              <div class="cate-confirm" @click="confrimCate"> 确认 </div>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </van-sticky>

      <div class="xiaohao-container">
        <yy-list
          class="xiaohao-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="empty"
          :check="false"
        >
          <template v-for="(item, index) in xiaohaoList">
            <div
              class="xiaohao-item"
              :key="index"
              @click="clickXiaohaoItem(item)"
            >
              <div class="xiaohao-pic">
                <img :src="item.img" alt="" />
              </div>
              <div class="xiaohao-info">
                <div class="name">{{ item.game_name }}</div>
                <div class="area">{{ item.area_text }}</div>
                <div class="price">
                  <div class="now">
                    ¥<span>{{ item.current_price }}</span>
                  </div>
                  <div class="old">¥{{ item.price }}</div>
                </div>
              </div>
            </div>
          </template>
        </yy-list>
      </div>
    </div>
    <van-popup
      safe-area-inset-bottom
      v-model="morePopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      class="more-popup"
    >
      <div class="more-list">
        <div class="more-item" @click="toOpeningAccountExplain">
          <div class="more-icon">
            <img src="@/assets/images/deal/opening-explain-icon.png" alt="" />
          </div>
          <div class="more-title">规则说明</div>
        </div>
        <div class="more-item" @click="toOpeningAccountRecord">
          <div class="more-icon">
            <img src="@/assets/images/deal/opening-record-icon.png" alt="" />
          </div>
          <div class="more-title">交易记录</div>
        </div>
      </div>
      <div class="cancel" @click="morePopupShow = false">取消</div>
    </van-popup>

    <!-- 开局号详情弹窗 -->
    <van-popup
      safe-area-inset-bottom
      v-model="detailPopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      class="detail-popup"
    >
      <div class="popup-title">
        开局号详情
        <div class="close" @click="detailPopupShow = false"></div>
      </div>

      <div class="detail-bar">
        <div class="game-icon">
          <img :src="selectedXiaohao.game_icon" alt="" />
        </div>
        <div class="center">
          <div class="game-name">{{ selectedXiaohao.game_name }}</div>
          <div class="game-area">{{ selectedXiaohao.area_text }}</div>
        </div>
        <div class="right">
          <div class="tag" :class="{ hide: !selectedXiaohao.is_first }">
            新服首单免费
          </div>
          <div class="price">
            <div class="now">
              ¥<span>{{ selectedXiaohao.current_price }}</span>
            </div>
            <div class="old">¥{{ selectedXiaohao.price }}</div>
          </div>
        </div>
      </div>
      <div class="detail-info">
        <div class="title">角色名称</div>
        <div class="desc">{{ selectedXiaohao.role_name }}</div>
        <div class="pic">
          <img :src="selectedXiaohao.img" alt="" />
        </div>
        <div class="tips">
          具体描述以实际帐号信息为准<br />
          发布日期：{{ formatDate(selectedXiaohao.create_time) }}
        </div>
      </div>
      <div class="detail-btn" @click="clickDetailBtn">立即购买</div>
    </van-popup>

    <!-- 开局号购买须知弹窗 -->
    <van-popup
      safe-area-inset-bottom
      v-model="noticePopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      class="notice-popup"
    >
      <div class="popup-title">
        开局号购买须知
        <div class="close" @click="noticePopupShow = false"></div>
      </div>
      <div class="notice-content">
        1、未实名用户无法获取开局小号。<br />
        2、开局小号购买后直接在你的账号中新增一个小号，登录游戏即可查收。<br />
        3、买够开局号存在异常24小时内可找客服申诉，超过时间将按照正常问题处理。<br />
        4、购买前请先确保已阅读并明确《规则说明》。购买后开局号不可退货。<br />
      </div>
      <div class="buy-btn" @click="clickBuyBtn">立即购买</div>
      <div class="cancel-btn" @click="noticePopupShow = false">取消</div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiXiaohaoGetOpeningAccountCate,
  ApiXiaohaoGetOpeningAccount,
  ApiXiaohaoPurchaseOpeningAccount,
} from '@/api/views/xiaohao.js';
export default {
  data() {
    return {
      navBarHeight: 0,

      gameId: ********,
      area_text: [],
      cate: [],
      sub_cate: [],

      clickedCate: {}, // 点击的分类项
      selectedCateId: '', // 确认选中的分类id
      selectedArea: '', // 选中的区服

      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      xiaohaoList: [],
      page: 1,
      listRows: 10,
      empty: false,

      morePopupShow: false, // 更多弹窗
      detailPopupShow: false, //开局号详情弹窗
      noticePopupShow: false, // 开局号购买须知弹窗

      selectedXiaohao: {}, // 选中查看详情的小号
    };
  },
  computed: {
    showCate() {
      return this.cate.map(cate => {
        const subArr = this.sub_cate.filter(sub => cate.id == sub.pid);
        return {
          ...cate,
          children: subArr,
        };
      });
    },
    showArea() {
      let areaArr = [{ text: '全部区服', value: '' }, ...this.area_text];
      return areaArr.map(item => {
        return {
          text: item.text || item.area_text,
          value: item.value || item.area_text,
        };
      });
    },
    selectedCateTitle() {
      if (this.sub_cate.length) {
        let found = this.sub_cate.find(item => item.id == this.selectedCateId);
        if (found) {
          return found.title;
        }
      }
      return '全部';
    },
  },
  async created() {
    this.gameId = this.$route.params.id;
    await this.getCate();
    this.selectedArea = this.showArea[0].value;
    await this.getList();
  },
  mounted() {
    this.$nextTick(() => {
      this.navBarHeight = this.$refs.navBar.clientHeight;
    });
  },
  methods: {
    formatDate(val) {
      let { year, month, day, time } = this.$handleTimestamp(val);
      return `${year}年${month}月${day}日 ${time}`;
    },
    clickMore() {
      this.morePopupShow = true;
    },
    async getCate() {
      const res = await ApiXiaohaoGetOpeningAccountCate({
        game_id: this.gameId,
      });
      this.area_text = res.data.area_text;
      this.cate = res.data.cate;
      this.sub_cate = res.data.sub_cate;
    },
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }

      const res = await ApiXiaohaoGetOpeningAccount({
        game_id: this.gameId,
        sub_cate: this.selectedCateId,
        area: this.selectedArea,
        page: this.page,
        listRows: this.listRows,
      });

      if (action == 1 || this.page == 1) {
        this.xiaohaoList = [];
      }

      this.xiaohaoList.push(...res.data);
      if (!this.xiaohaoList.length) {
        this.empty = true;
      } else {
        this.empty = false;
      }
      if (res.data.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished == true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.xiaohaoList.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }

      this.loadingObj.loading = false;
    },
    async confrimCate() {
      this.selectedCateId = this.clickedCate.id ?? '';
      this.$refs.cateRef.toggle();
      await this.getList();
    },
    clickCate(item) {
      if (item.cate_count <= 0) {
        return false;
      } else if (this.clickedCate.id == item.id) {
        this.clickedCate = {};
      } else {
        this.clickedCate = item;
      }
    },
    async onSelectedAreaChange(e) {
      await this.getList();
    },
    toOpeningAccountExplain() {
      this.morePopupShow = false;
      this.toPage('OpeningAccountExplain');
    },
    toOpeningAccountRecord() {
      this.morePopupShow = false;
      this.toPage('OpeningAccountRecord', { id: this.gameId });
    },

    clickXiaohaoItem(item) {
      this.selectedXiaohao = item;
      console.log(this.selectedXiaohao);
      this.detailPopupShow = true;
    },
    clickDetailBtn() {
      this.detailPopupShow = false;
      this.noticePopupShow = true;
    },
    clickBuyBtn() {
      localStorage.setItem(
        'openingAccountXiaohaoInfo',
        JSON.stringify(this.selectedXiaohao),
      );
      this.toPage('OpeningAccountBuy', {
        id: this.selectedXiaohao.id,
        xiaohaoInfo: this.selectedXiaohao,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .more-btn {
    color: #32b768;
  }
  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    .top-container {
      /deep/ .van-dropdown-menu__bar {
        box-shadow: unset;
        padding: 16 * @rem 12 * @rem 10 * @rem;
        height: 35 * @rem;
        .van-ellipsis {
          font-size: 15 * @rem;
          color: #333333;
        }
        .van-dropdown-menu__title::after {
          border-color: transparent transparent #000000 #000000;
        }
        .van-dropdown-menu__item {
          width: 160 * @rem;
          height: 35 * @rem;
          background-color: #f3f4f8;
          border-radius: 20 * @rem;
          margin: 0 8 * @rem;
        }
        .van-dropdown-menu__title--active {
          .van-ellipsis {
            font-size: 15 * @rem;
            color: #32b768;
          }
          &::after {
            border-color: transparent transparent #32b768 #32b768;
          }
        }
      }

      /deep/ .van-overlay {
        height: 100vh;
      }
      .cate-container {
        padding: 20 * @rem 20 * @rem 0;

        .cate-title {
          font-size: 15 * @rem;
          line-height: 19 * @rem;
          color: #333438;
          font-weight: 600;
        }
        .cate-list {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20 * @rem;
          column-gap: 16 * @rem;
          margin-top: 16 * @rem;
          .cate-item {
            height: 36 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 18 * @rem;
            background-color: #f9f9f9;
            font-size: 14 * @rem;
            color: #333438;
            &.current {
              background-color: #ecfbf4;
              color: #5abf73;
            }
            &.disable {
              background-color: #f9f9f9;
              color: #55555540;
            }
          }
        }
      }
    }
    .cate-confirm {
      width: 295 * @rem;
      height: 40 * @rem;
      margin: 22 * @rem auto;
      background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      border-radius: 29 * @rem;
      font-size: 15 * @rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .xiaohao-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      .xiaohao-list {
        padding: 5 * @rem 20 * @rem;
        .xiaohao-item {
          display: flex;
          align-items: center;
          padding: 13 * @rem 0;
          .xiaohao-pic {
            width: 120 * @rem;
            height: 80 * @rem;
            border-radius: 8 * @rem;
            overflow: hidden;
          }
          .xiaohao-info {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .name {
              font-size: 14 * @rem;
              color: #333333;
              line-height: 18 * @rem;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .area {
              height: 17 * @rem;
              padding: 0 4 * @rem;
              display: inline-block;
              font-size: 10 * @rem;
              color: #888888;
              line-height: 17 * @rem;
              border-radius: 4 * @rem;
              background-color: #f5f5f6;
              margin-top: 8 * @rem;
            }
            .price {
              display: flex;
              align-items: center;
              margin-top: 4 * @rem;
              .now {
                font-size: 12 * @rem;
                color: @themeColor;
                font-weight: 600;
                display: flex;
                align-items: center;
                line-height: 31 * @rem;
                span {
                  font-size: 22 * @rem;
                  color: @themeColor;
                  font-weight: 600;
                  margin-left: 2 * @rem;
                  line-height: 31 * @rem;
                }
              }
              .old {
                text-decoration: line-through;
                font-size: 11 * @rem;
                color: #999999;
                margin-left: 6 * @rem;
                margin-top: 8 * @rem;
              }
            }
          }
        }
      }
    }
  }
}

.more-popup {
  height: 190 * @rem;
  width: 100%;
  .more-list {
    padding-top: 20 * @rem;
    .more-item {
      display: flex;
      align-items: center;
      padding: 14 * @rem 20 * @rem;
      .more-icon {
        width: 18 * @rem;
        height: 18 * @rem;
      }
      .more-title {
        font-size: 14 * @rem;
        color: #555555;
        margin-left: 6 * @rem;
      }
    }
  }
  .cancel {
    width: 295 * @rem;
    height: 40 * @rem;
    border-radius: 20 * @rem;
    background-color: #efefef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15 * @rem;
    color: #999999;
    font-weight: 500;
    margin: 14 * @rem auto 0;
  }
}

.popup-title {
  height: 62 * @rem;
  border-bottom: 1 * @rem solid #ebebeb;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 16 * @rem;
  color: #111111;
  font-weight: bold;
  .close {
    width: 26 * @rem;
    height: 62 * @rem;
    background: url(~@/assets/images/deal/opening-popup-close.png) center center
      no-repeat;
    background-size: 16 * @rem 16 * @rem;
    position: absolute;
    right: 0;
    top: 0;
  }
}
.detail-popup {
  box-sizing: border-box;
  padding: 0 18 * @rem 20 * @rem;
  .detail-bar {
    box-sizing: border-box;
    height: 96 * @rem;
    display: flex;
    align-items: center;
    .game-icon {
      width: 56 * @rem;
      height: 56 * @rem;
    }
    .center {
      flex: 1;
      min-width: 0;
      margin-left: 8 * @rem;

      .game-name {
        font-size: 14 * @rem;
        color: #333333;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .game-area {
        box-sizing: border-box;
        padding: 0 4 * @rem;
        font-size: 10 * @rem;
        color: #888888;
        height: 17 * @rem;
        line-height: 17 * @rem;
        border-radius: 4 * @rem;
        background: #f5f5f6;
        display: inline-block;
        margin-top: 12 * @rem;
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      margin-left: 4 * @rem;
      .tag {
        width: 81 * @rem;
        height: 31 * @rem;
        background: url(~@/assets/images/deal/opening-free-tag-bg.png) no-repeat;
        background-size: 81 * @rem 31 * @rem;
        line-height: 25 * @rem;
        font-size: 11 * @rem;
        color: #ffffff;
        text-align: center;
        &.hide {
          opacity: 0;
        }
      }
      .price {
        display: flex;
        align-items: center;
        .now {
          font-size: 12 * @rem;
          color: @themeColor;
          font-weight: 600;
          line-height: 31 * @rem;
          span {
            font-size: 22 * @rem;
            color: @themeColor;
            font-weight: 600;
            line-height: 31 * @rem;
          }
        }
        .old {
          font-size: 11 * @rem;
          color: #999999;
          line-height: 14 * @rem;
          min-width: 45 * @rem;
          text-align: right;
          text-decoration: line-through;
          margin-left: 6 * @rem;
        }
      }
    }
  }
  .detail-info {
    padding-top: 16 * @rem;
    border-top: 1 * @rem solid #ebebeb;
    .title {
      font-size: 14 * @rem;
      color: #111111;
      line-height: 20 * @rem;
    }
    .desc {
      font-size: 11 * @rem;
      color: #555555;
      line-height: 14 * @rem;
      margin-top: 6 * @rem;
    }
    .pic {
      width: 120 * @rem;
      height: auto;
      border-radius: 6 * @rem;
      margin-top: 12 * @rem;
    }
    .tips {
      font-size: 11 * @rem;
      color: #999999;
      line-height: 18 * @rem;
      margin-top: 12 * @rem;
    }
  }
  .detail-btn {
    width: 295 * @rem;
    height: 40 * @rem;
    border-radius: 20 * @rem;
    margin: 20 * @rem auto 0;
    background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.notice-popup {
  box-sizing: border-box;
  padding: 0 18 * @rem 20 * @rem;
  .notice-content {
    font-size: 12 * @rem;
    color: #555555;
    line-height: 20 * @rem;
    margin: 18 * @rem auto 0;
  }
  .buy-btn {
    width: 295 * @rem;
    height: 40 * @rem;
    border-radius: 20 * @rem;
    margin: 27 * @rem auto 0;
    background: linear-gradient(39deg, #32b768 0%, #56d76a 100%);
    font-size: 15 * @rem;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .cancel-btn {
    width: 295 * @rem;
    height: 40 * @rem;
    border-radius: 20 * @rem;
    margin: 12 * @rem auto 0;
    background: #efefef;
    font-size: 15 * @rem;
    color: #888888;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
