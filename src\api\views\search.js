import { request } from '../index';

/**
 * 搜索
 * @param keyword
 * @param fromAction 1-主动输入关键词搜索，2-点击热门搜索，3-点击历史搜索，4-刷新、重试，5-加载更多，6-切换tab触发
 * @param type 1-全部游戏(默认)，101-礼包，201-资讯, 301-视频 php说传401, 701-合集
 * @param orderType 1按推荐数倒排，2按排序时间倒排
 */
export function ApiSearchIndex(params = {}) {
  return request('/api/search/index', params);
}
// 搜索-其他
export function ApiCouponCouponList(params = {}) {
  return request('/api/coupon/couponList', params);
}

/**
 * 搜索 4.4 热门搜索分类
 */
export function ApiSearchGetHotKey(params = {}) {
  return request('/api/search/getHotKey', params);
}

/**
 * 猜你喜欢
 */
export function ApiCwbIndexSearch(params = {}) {
  return request('/cwb/index/search', params);
}
/**
 * 热门：1.热门游戏 2.热门分类 3.热门合集
 */
export function ApiCwbIndexGetSearchHotData(params = {}) {
  return request('/cwb/index/getSearchHotData', params);
}

/**
 * 
 * @function 首页 - 新版搜索V2
 * @param {string} keyword 搜索词
 * @param {int} type 类型 // 1-游戏，901-分类，701-合集,1000-综合
 * @param {int} page 
 * @param {int} listRows 
 * @param {int} search_ver 版本号 
 */
export function ApiV2024SearchIndex(params = {}) {
  return request('/v2024/search/index', params);
}

/**
 * 
 * @function 首页 - 搜索SUG
 * @param {string} keyword 搜索词
 */
export function ApiV2024SearchSug(params = {}) {
  return request('/v2024/search/sug', params);
}

/**
 * 
 * @function 首页 - 换一换（猜你喜欢）
 * @param {int} page 页码
 */
export function ApiV2024SearchYourLike(params = {}) {
  return request('/v2024/search/youLike', params);
}

/**
 * 
 * @function 首页 - 搜索热门列表
 */
export function ApiV2024SearchHotList(params = {}) {
  return request('/v2024/search/hotList', params);
}