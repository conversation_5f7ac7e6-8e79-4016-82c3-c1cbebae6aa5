import { h5PostMessage, h5HandleMessage, h5PostAdPopupConfigMessage } from '@/utils/function';
import router from '@/router';
import { ApiSimulatorInit, ApiCloudSdkInit } from '@/api/views/game.js';
import { platform } from '@/utils/box.uni.js';
import h5Page from '@/utils/h5Page.js';
import Vue from 'vue';

export default {
  state: {
    gameInfo: {},
    getUdidPopupShow: false, //是否显示UDID获取弹窗,
    downloadGrqDialogShow: false, //是否显示个人签下载弹窗
    grqStatus: 0, //签名状态
    grqLoading: false, //个人签loading
    downloadPopupShow: false, //IOS下载弹窗
    h5GameUrl: '', //当前h5游戏url
    h5gamePopup: 0, //当前h5游戏弹窗0不显示1显示2最小化
    simulatorInitLoading: {}, //模拟器游戏loading状态
    simulatorInitInfo: {}, //模拟器游戏初始化信息
    pcCloudGameInitLoading: {}, //PC云游戏loading状态
    pcCloudGameInitInfo: {}, //PC云游戏初始化信息
    durationOverShow: false, //是否显示游戏时长不足弹窗
    taskListPopupShow: false, //是否显示PC云游戏任务弹窗
    hereItIosBoxTaskShow: false, //是否来自iosBox马甲包云游戏任务展示
  },
  getters: {
    gameInfo(state) {
      return state.gameInfo;
    },
    getUdidPopupShow(state) {
      return state.getUdidPopupShow;
    },
    downloadGrqDialogShow(state) {
      return state.downloadGrqDialogShow;
    },
    grqStatus(state) {
      return state.grqStatus;
    },
    grqLoading(state) {
      return state.grqLoading;
    },
    downloadPopupShow(state) {
      return state.downloadPopupShow;
    },
    h5GameUrl(state) {
      return state.h5GameUrl;
    },
    h5gamePopup(state) {
      return state.h5gamePopup;
    },
    simulatorInitLoading(state) {
      return state.simulatorInitLoading;
    },
    simulatorInitInfo(state) {
      return state.simulatorInitInfo;
    },
    pcCloudGameInitLoading(state) {
      return state.pcCloudGameInitLoading;
    },
    pcCloudGameInitInfo(state) {
      return state.pcCloudGameInitInfo;
    },
    durationOverShow(state) {
      return state.durationOverShow;
    },
    taskListPopupShow(state) {
      return state.taskListPopupShow;
    },
    hereItIosBoxTaskShow(state) {
      return state.hereItIosBoxTaskShow;
    },
  },
  mutations: {
    setGameInfo(state, payload) {
      state.gameInfo = Object.assign({}, payload);
    },
    setGetUdidPopupShow(state, payload) {
      state.getUdidPopupShow = payload;
    },
    setDownloadGrqDialogShow(state, payload) {
      state.downloadGrqDialogShow = payload;
    },
    setGrqStatus(state, payload) {
      state.grqStatus = payload;
    },
    setGrqLoading(state, payload) {
      state.grqLoading = payload;
    },
    setDownloadPopupShow(state, payload) {
      state.downloadPopupShow = payload;
    },
    setInitDownloadStatus(state) {
      state.getUdidPopupShow = false; //是否显示UDID获取弹窗,
      state.downloadGrqDialogShow = false; //是否显示个人签下载弹窗
      state.grqStatus = 0; //签名状态
      state.grqLoading = false; //个人签loading
      state.downloadPopupShow = false; //IOS下载弹窗
    },
    setH5GameUrl(state, payload) {
      if (payload) {
        state.h5GameUrl = `${payload}&w=1`;
      } else {
        state.h5GameUrl = payload;
      }
    },
    setH5gamePopup(state, payload) {
      let oldvalue = state.h5gamePopup;
      state.h5gamePopup = payload;
      if (payload) {
        if (payload == 1) {
          if (oldvalue == 0) {
            window.addEventListener('message', h5HandleMessage, false);
            h5PostMessage();
            h5PostAdPopupConfigMessage();
          }
          let current_page = JSON.stringify({ name: router.history.current.name, params: router.history.current.params, query: router.history.current.query })
          setTimeout(() => {
            // 记录当前页面，跳转首页（等他点击最小化后再跳到这个页面，防止用户回退杀掉游戏进程)
            sessionStorage.setItem('current_page', current_page);
            router.push({ name: 'QualitySelect' });
          }, 1500);
        } else if (payload == 2) {
          // 跳回原有页面
          router.push(
            JSON.parse(
              sessionStorage.getItem('current_page', router.history.current),
            ),
          );
        }
      } else {
        window.removeEventListener('message', h5HandleMessage, false);
        state.h5GameUrl = '';
      }
    },
    setSimulatorInitLoading(state, payload) {
      Vue.set(state.simulatorInitLoading, payload.id, payload.loading);
    },
    setSimulatorInitLoadingEmpty(state, payload) {
      state.simulatorInitLoading = payload
    },
    setSimulatorInitInfo(state, payload) {
      state.simulatorInitInfo = payload;
    },
    setPcCloudGameInitLoading(state, payload) {
      Vue.set(state.pcCloudGameInitLoading, payload.id, payload.loading);
    },
    setPcCloudGameInitLoadingEmpty(state, payload) {
      state.pcCloudGameInitLoading = payload
    },
    setPcCloudGameInitInfo(state, payload) {
      state.pcCloudGameInitInfo = payload;
    },
    setDurationOverShow(state, payload) {
      state.durationOverShow = payload;
    },
    setTaskListPopupShow(state, payload) {
      state.taskListPopupShow = payload;
    },
    setHereItIosBoxTaskShow(state, payload) {
      state.hereItIosBoxTaskShow = payload;
    },
  },
  actions: {
    async OPEN_SIMULATOR_GAME({ commit, rootGetters }, detail) {
      try {
        if (!rootGetters['user/userInfo'].token) {
          router.push({ name: 'PhoneLogin' });
          return false;
        }
        if (parseInt(rootGetters['user/userInfo'].auth_status) !== 2) {
          router.push({ name: 'IdCard' });
          return false;
        }
        commit('setSimulatorInitLoading', { id: detail.id, loading: true });
        const res = await ApiSimulatorInit({ id: detail.id });
        if (res.code !== 3) {
          return
        }
        switch (platform) {
          case 'iosBox':
            const { down_i, title, titlepic } = detail
            const data = { ...res.data };
            delete data.simulator_info.other_info
            const simulatorInitInfo = { ...data, ...data.simulator_info };
            delete simulatorInitInfo.simulator_info;
            window.webkit.messageHandlers.goToSimulatorZone.postMessage({
              simulatorInitInfo: { down_i, title, titlepic, ...simulatorInitInfo },
              userInfo: rootGetters['user/userInfo'],
              svip_url: h5Page.svip_url,
            });
            break;
        }
        commit('setSimulatorInitInfo', res.data);
      } catch (error) {
        commit('setSimulatorInitLoadingEmpty', {})
      } finally {
        commit('setSimulatorInitLoadingEmpty', {})
      }
    },
    async OPEN_PC_CLOUD_GAME({ commit, rootGetters }, detail) {
      try {
        if (!rootGetters['user/userInfo'].token) {
          router.push({ name: 'PhoneLogin' });
          return false;
        }
        if (parseInt(rootGetters['user/userInfo'].auth_status) !== 2) {
          router.push({ name: 'IdCard' });
          return false;
        }
        commit('setPcCloudGameInitLoading', { id: detail.id, loading: true });
        const res = await ApiCloudSdkInit({ game_id: detail.id });

        if (res.code === -40) {
          // 时长不足
          commit('setDurationOverShow', true);
          return
        }

        if (res.code !== 3) {
          return
        }

        // console.log({
        //   cloudSdkInit: { game_id: detail.id, ...res.data },
        //   userInfo: rootGetters['user/userInfo'],
        //   cloud_member_url: h5Page.cloud_member_url,
        //   kefu_url: h5Page.kefu_url,
        // });
        switch (platform) {
          case 'iosBox':
            window.webkit.messageHandlers.goToPcCloudGame.postMessage({
              cloudSdkInit: { game_id: detail.id, ...res.data },
              userInfo: rootGetters['user/userInfo'],
              cloud_member_url: h5Page.cloud_member_url,
              cloud_game_use_url: h5Page.cloud_game_use_url,
              cloud_member_scb_url: h5Page.cloud_member_scb_url,
              kefu_url: h5Page.kefu_url,
            });
            break;
        }
        commit('setPcCloudGameInitInfo', res.data);
      } catch (error) {
        commit('setPcCloudGameInitLoadingEmpty', {})
      } finally {
        commit('setPcCloudGameInitLoadingEmpty', {})
      }
    }
  }
};
