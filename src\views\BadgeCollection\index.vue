<template>
  <div class="badge-collection-page">
    <rubber-band :topColor="'#151412'" :bottomColor="'#151412'">
      <nav-bar-2
        class="nav-bar"
        :title="title"
        :placeholder="false"
        :bgStyle="bgStyle"
        :bgColor="`rgba(28, 23, 19, ${navbarOpacity})`"
        :azShow="true"
      >
        <template #right>
          <div class="award-record" @click="goToAwardRecord()">
            <span>获取记录</span>
            <span class="right-icon"></span>
          </div>
        </template>
      </nav-bar-2>
      <div class="top-bg"> </div>
      <div class="top-bar">
        <div class="bar-container">
          <div class="badge-collection-icon"> </div>
          <div class="user-info">
            <user-avatar class="user-avatar"></user-avatar>
            <div class="user-name">{{ userInfo.nickname }}</div>
          </div>
        </div>
        <div class="badge-icon" @click="showAwardRecord(wearBadgeInfo)">
          <img
            v-if="!wearBadgeInfo?.icon_url"
            src="~@/assets/images/badge-collection/badge-default-icon.png"
            alt=""
          />
          <img v-else :src="wearBadgeInfo?.icon_url" alt="" />
        </div>
        <div class="award-record">
          <span v-if="!wearBadgeInfo?.icon_url">未佩戴</span>
          <span v-else>佩戴中</span>
        </div>
      </div>
      <loading-indicator v-if="!isLoading" />
      <template v-else>
        <div v-if="badgeSections && !badgeSections.length" class="empty-box">
          <default-not-found-page notFoundText="暂未获得徽章哦～" />
        </div>
        <div v-else class="main">
          <template v-for="(section, index) in badgeSections">
            <div
              class="badge-container"
              :key="section.key"
              v-if="section.list && section.list.length > 0"
            >
              <div class="badge-title">{{ section.title }}</div>
              <div
                class="badge-box"
                :class="{
                  'default-box':
                    section.list.length > 1 && section.key !== 'new',
                }"
              >
                <!-- 最近获得徽章 - 轮播模式 -->
                <template v-if="section.key == 'new'">
                  <swiper
                    :id="`badgeSwiper_${index}`"
                    :ref="`badgeSwiper_${index}`"
                    class="badge-content"
                    :options="swiperBannerOptions"
                    :auto-update="false"
                    @slideChange="() => onSlideBannerChange(index)"
                  >
                    <swiper-slide
                      class="badge-item swiper-slide"
                      v-for="(item, i) in section.list"
                      :key="`${item.type_id}_${i}`"
                    >
                      <div class="content" @click="showAwardRecord(item)">
                        <div class="badge-image">
                          <img :src="item.icon_url" />
                        </div>
                        <div class="badge-name">{{ item.name }}</div>
                        <div class="badge-time" v-if="item.create_time"
                          >{{ formatDate(item.create_time) }} 获得</div
                        >
                      </div>
                    </swiper-slide>
                  </swiper>
                </template>

                <!-- 单个展示样式 -->
                <template
                  v-else-if="section.key !== 'new' && section.list.length === 1"
                >
                  <div
                    class="memorial-badge"
                    v-for="(item, i) in section.list"
                    :key="`${item.type_id}_${i}`"
                    :class="{ 'not-obtained-badge': item.is_did == 0 }"
                    @click="showAwardRecord(item)"
                  >
                    <div class="memorial-badge-left">
                      <div class="memorial-badge-img">
                        <img :src="item.icon_url" alt="" />
                      </div>
                      <div class="badge-new" v-if="item.is_new == 1">
                        <img
                          src="~@/assets/images/badge-collection/badge-new-icon.png"
                          alt=""
                        />
                      </div>
                    </div>
                    <div class="memorial-badge-content">
                      <div class="memorial-badge-header">
                        <div class="memorial-badge-title">{{ item.name }}</div>
                        <div
                          class="memorial-badge-subtitle"
                          v-if="item.subtitle"
                        >
                          {{ item.subtitle }}
                        </div>
                      </div>
                      <div class="memorial-badge-count">
                        <span class="memorial-badge-text">已有</span>
                        <span class="memorial-badge-num">{{
                          item.have_num
                        }}</span>
                        <span class="memorial-badge-text">人获得</span>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- 多个展示样式 -->
                <template v-else>
                  <div
                    class="badge-content"
                    v-for="item in showBadges(section)"
                    :key="item.id"
                    :class="{ 'not-obtained-badge': item.is_did == 0 }"
                  >
                    <div class="badge-item">
                      <div class="content" @click.stop="showAwardRecord(item)">
                        <div class="badge-image">
                          <img :src="item.icon_url" />
                        </div>
                        <div class="badge-new" v-if="item.is_new == 1">
                          <img
                            src="~@/assets/images/badge-collection/badge-new-icon.png"
                            alt=""
                          />
                        </div>
                        <div class="badge-name">{{ item.name }}</div>
                        <div class="badge-arrive">{{ item.arrive_text }}</div>
                        <div class="badge-time"
                          >已有{{ item.have_num }}人获得</div
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </div>

              <!-- 展开收起按钮 -->
              <div
                class="put-away"
                v-if="section.key !== 'new' && section.list.length > 6"
              >
                <div
                  class="put-away-icon"
                  :class="{ rotate: section.isExpand }"
                  @click="toggleExpand(index)"
                >
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </rubber-band>
  </div>
</template>

<script>
import LoadingIndicator from '@/components/loading-Indicator';
import { ApiUserBadgeBadge } from '@/api/views/badgeCollection.js';
import { mapMutations, mapGetters } from 'vuex';
export default {
  name: 'BadgeCollection',
  props: {},
  components: {
    LoadingIndicator,
  },
  data() {
    return {
      title: '',
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      swiperBannerOptions: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        resistanceRatio: 0, // 惯性阻力
      },
      badgeSections: [], // 徽章列表
      isLoading: false,
    };
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  mounted() {},
  methods: {
    async getUserBadgeInfo() {
      try {
        const res = await ApiUserBadgeBadge();
        // 从本地存储中获取保存的展开状态
        let savedExpandStates = {};
        try {
          const savedStates = sessionStorage.getItem(
            'badgeSectionsExpandState' + this.userInfo?.user_id,
          );
          if (savedStates) {
            savedExpandStates = JSON.parse(savedStates);
          }
        } catch (e) {}
        this.badgeSections = res.data.list.map(item => {
          const isExpand =
            savedExpandStates[item.key] !== undefined
              ? savedExpandStates[item.key]
              : true;
          return {
            ...item,
            isExpand,
          };
        });
        this.setWearBadgeInfo(res.data?.wear);
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
    // 获取记录
    goToAwardRecord() {
      if (!this.isLoading) return false;
      this.$nextTick(() => {
        this.toPage('AwardRecord');
      });
    },
    // 获取点击徽章详情
    showAwardRecord(item) {
      if (!item) return;
      this.$nextTick(() => {
        this.$router.push({
          name: 'BadgeDetail',
          params: {
            id: item.id,
            type_id: item.type_id,
            record_id: item.record_id ? item.record_id : 0,
            is_valid: 0, //成就徽章进入隐藏详情页已经过期的 0隐藏 1显示
          },
        });
      });
    },
    // 滑动最近获得徽章
    onSlideBannerChange(index) {
      try {
        const swiperRef = this.$refs[`badgeSwiper_${index}`];
        if (!swiperRef || !swiperRef[0] || !swiperRef[0].$swiper) {
          return;
        }
        const swiper = swiperRef[0].$swiper;
        let startX = 0; // 记录起始位置

        // 监听触摸开始事件
        swiper.on('touchStart', e => {
          if (e.touches && e.touches.length > 0) {
            startX = e.touches[0].clientX;
          }
        });

        // 监听触摸结束事件
        swiper.on('touchEnd', e => {
          if (e.changedTouches && e.changedTouches.length > 0) {
            const endX = e.changedTouches[0].clientX;
            const deltaX = endX - startX; // 计算滑动方向

            if (swiper.isEnd && deltaX < -50) {
              //最后一页 用户左滑
              this.$nextTick(() => {
                this.toPage('AwardRecord');
              });
            } else if (deltaX > 100) {
              // 右滑
            } else if (deltaX < -100) {
              // 左滑
            }
          }
        });
      } catch (error) {}
    },
    // 点击最近获得徽章
    onSwiperTap(index) {
      try {
        const swiperRef = this.$refs[`badgeSwiper_${index}`];
        if (!swiperRef || !swiperRef[0] || !swiperRef[0].$swiper) {
          return;
        }

        const swiper = swiperRef[0].$swiper;
        const activeIndex = swiper.activeIndex;
        const currentBadge = this.badgeSections[index].list[activeIndex];
        if (currentBadge) {
          this.showAwardRecord(currentBadge);
        }
      } catch (error) {}
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.title = '成就徽章';
        // this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.title = '';
        // this.bgStyle = 'transparent-white';
      }
    },
    // 切换展开/收起状态
    toggleExpand(index) {
      this.badgeSections[index].isExpand = !this.badgeSections[index].isExpand;

      // 保存展开状态到本地存储
      try {
        const expandStates = {};
        this.badgeSections.forEach(section => {
          if (section.key) {
            expandStates[section.key] = section.isExpand;
          }
        });
        sessionStorage.setItem(
          'badgeSectionsExpandState' + this.userInfo?.user_id,
          JSON.stringify(expandStates),
        );
      } catch (e) {}
    },
    formatDate(val) {
      let { year, month, day } = this.$handleTimestamp(val);
      return `${year}.${month}.${day}`;
    },
    // 收起 展开徽章
    showBadges(section) {
      if (section.list.length <= 6 || section.isExpand) {
        return section.list;
      } else {
        return section.list.slice(0, 3);
      }
    },
    ...mapMutations({
      setWearBadgeInfo: 'badge_collection/setWearBadgeInfo',
    }),
  },
  activated() {
    this.getUserBadgeInfo();
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  computed: {
    ...mapGetters({
      wearBadgeInfo: 'badge_collection/wearBadgeInfo',
    }),
  },
};
</script>

<style lang="less" scoped>
.badge-collection-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #1e1916 0%, #161207 100%);
  .nav-bar {
    /deep/ .transparent-white {
      .nav-title {
        color: #fffcec;
        font-size: 17 * @rem;
        font-weight: normal;
      }
      .back {
        background-image: url(~@/assets/images/badge-collection/back-btn.png);
      }
    }
    /deep/.van-nav-bar__right {
      .award-record {
        display: flex;
        align-items: center;
        span {
          color: #fff;
        }
        .right-icon {
          margin-left: 2 * @rem;
          width: 6 * @rem;
          height: 7 * @rem;
          background: url(~@/assets/images/badge-collection/award-record-right.png)
            no-repeat center center;
          background-size: 6 * @rem 7 * @rem;
        }
      }
    }
  }
  .top-bg {
    width: 100%;
    height: 216 * @rem;
    background: url(~@/assets/images/badge-collection/badge-collection-bg.png)
      no-repeat top right;
    background-size: 100% auto;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
  }
  .top-bar {
    width: 100%;
    padding-top: 44 * @rem;
    padding-top: calc(44 * @rem + @safeAreaTop);
    padding-top: calc(44 * @rem + @safeAreaTopEnv);
    box-sizing: border-box;
    padding-bottom: 18 * @rem;
    position: relative;
    z-index: 2;
    .bar-container {
      box-sizing: border-box;
      margin: 0 auto;
      .badge-collection-icon {
        margin-top: calc(54 * @rem - @safeAreaTop);
        margin-top: calc(54 * @rem - @safeAreaTopEnv);
        margin-left: 30 * @rem;
        width: 136 * @rem;
        height: 34 * @rem;
        background: url(~@/assets/images/badge-collection/badge-collection-icon.png)
          no-repeat;
        background-size: 136 * @rem 34 * @rem;
      }
      .user-info {
        margin-top: 8 * @rem;
        margin-left: 30 * @rem;
        display: flex;
        align-items: center;
        .user-avatar {
          width: 30 * @rem;
          height: 30 * @rem;
          overflow: hidden;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.4);
          box-sizing: border-box;
          background-color: #ccc;
        }
        .user-name {
          margin-left: 6 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #fffcec;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100 * @rem;
        }
      }
    }
    .badge-icon {
      position: absolute;
      top: 71 * @rem;
      right: 28 * @rem;
      width: 100 * @rem;
      height: 100 * @rem;
    }
    .award-record {
      position: absolute;
      top: 154 * @rem;
      right: 52 * @rem;
      width: 52 * @rem;
      height: 17 * @rem;
      background: url(~@/assets/images/badge-collection/award-record-btn.png)
        no-repeat center center;
      background-size: 52 * @rem 17 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10 * @rem;
      color: #fffcec;
    }
  }
  .empty-box {
    height: calc(100vh - 216 * @rem - @safeAreaTop);
    height: calc(100vh - 216 * @rem - @safeAreaTopEnv);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .main {
    width: 100%;
    padding: 0 14 * @rem 34 * @rem 14 * @rem;
    box-sizing: border-box;
    .badge-container {
      width: 347 * @rem;
      background: #1d1c18;
      border-radius: 12 * @rem;
      border: 1px solid;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      &:not(:last-of-type) {
        margin-bottom: 14 * @rem;
      }
      .badge-title {
        margin-top: 16 * @rem;
        margin-bottom: 12 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        height: 22 * @rem;
        line-height: 22 * @rem;
        color: #fffcec;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          content: '';
          display: block;
          width: 23 * @rem;
          height: 10 * @rem;
          margin-right: 4 * @rem;
          background: url(~@/assets/images/badge-collection/left-badge-title-wings.png)
            no-repeat;
          background-size: 23 * @rem 10 * @rem;
        }
        &::after {
          content: '';
          display: block;
          width: 23 * @rem;
          height: 10 * @rem;
          margin-left: 4 * @rem;
          background: url(~@/assets/images/badge-collection/right-badge-title-wings.png)
            no-repeat;
          background-size: 23 * @rem 10 * @rem;
        }
      }
      .default-box {
        padding: 0 16 * @rem;
        width: 347 * @rem;
        .badge-content {
          &:nth-of-type(3n) {
            .badge-item {
              margin-right: 0;
            }
          }
          .badge-item {
            width: 91 * @rem;
            margin-right: 21 * @rem;
            overflow: hidden;
          }
        }
      }
      .badge-box {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        .badge-content {
          margin-bottom: 16 * @rem;
          .badge-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            .content {
              position: relative;
              display: flex;
              flex-direction: column;
              align-items: center;
              .badge-image {
                width: 75 * @rem;
                height: 75 * @rem;
              }
              .badge-new {
                position: absolute;
                right: 7 * @rem;
                top: 6 * @rem;
                img {
                  width: 24 * @rem;
                  height: 9 * @rem;
                }
              }
              .badge-name {
                height: 20 * @rem;
                line-height: 20 * @rem;
                font-weight: 400;
                font-size: 14 * @rem;
                color: #fffcec;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 73 * @rem;
              }
              .badge-arrive {
                font-weight: 400;
                font-size: 11 * @rem;
                color: #a59e91;
                margin-top: 2 * @rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 73 * @rem;
              }
              .badge-time {
                height: 14 * @rem;
                line-height: 14 * @rem;
                font-weight: 400;
                font-size: 10 * @rem;
                color: #6e6b64;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 91 * @rem;
              }
            }
          }
          .swiper-slide {
            width: 73 * @rem;
            &:first-of-type {
              margin-left: 16 * @rem;
            }
            &:last-of-type {
              margin-right: 16 * @rem;
            }
            &:not(:first-of-type) {
              margin-left: 26 * @rem;
            }
            .content {
              .badge-image {
                width: 70 * @rem;
                height: 70 * @rem;
              }
            }
          }
        }
        .memorial-badge {
          padding: 14 * @rem 0 16 * @rem 24 * @rem;
          display: flex;
          .memorial-badge-left {
            position: relative;
            .memorial-badge-img {
              width: 75 * @rem;
              height: 75 * @rem;
            }
            .badge-new {
              position: absolute;
              right: 7 * @rem;
              top: 6 * @rem;
              img {
                width: 24 * @rem;
                height: 9 * @rem;
              }
            }
          }

          .memorial-badge-content {
            flex: 1;
            margin-left: 10 * @rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .memorial-badge-header {
              display: flex;
              align-items: center;
              .memorial-badge-title {
                font-size: 16 * @rem;
                color: #fffcec;
                font-weight: bold;
              }
              .memorial-badge-subtitle {
                font-size: 12 * @rem;
                color: #a8a8a8;
              }
            }

            .memorial-badge-count {
              margin-top: 8 * @rem;
              font-size: 12 * @rem;
              .memorial-badge-text {
                color: #6e6b64;
              }

              .memorial-badge-num {
                color: #6e6b64;
              }
            }
          }
        }
        .not-obtained-badge {
          opacity: 0.15;
        }
      }
      .put-away {
        margin: 0 auto 16 * @rem;
        .put-away-icon {
          width: 20 * @rem;
          height: 16 * @rem;
          background: url(~@/assets/images/badge-collection/put-away-icon.png)
            no-repeat top center;
          background-size: 10 * @rem 8 * @rem;
          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
</style>
