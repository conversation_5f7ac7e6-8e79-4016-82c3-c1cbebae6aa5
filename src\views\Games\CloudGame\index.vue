<template>
  <div :class="{ xxx: heng }" class="cloud">
    <div id="cloud-game"></div>
    <Ball
      :qualityList="qualityList"
      :networkDelay="networkDelay"
      :gamePortrait="portrait"
      :selectedQuality="selectedQuality"
      :orientation="orientation"
      @closePopup="closePopup()"
      @changeQuality="changeQuality($event)"
    />
    <div
      v-if="openPicShow"
      :class="{
        portrait: !portrait,
        column: orientation === 90 || orientation === -90,
      }"
      class="open-pic"
    >
      <img id="gif" src="~@/assets/images/cloud-game/open-pic.gif" />
    </div>
    <van-dialog
      v-model="exitPopupShow"
      :message="$t('是否退出游戏')"
      show-cancel-button
      @confirm="back()"
      :class="{
        portrait: !portrait && (orientation === 0 || orientation === 180),
      }"
    >
    </van-dialog>
  </div>
</template>
<script>
import { ApiCloudGameInfo } from '@/api/views/game';
import Ball from './components/ball';
import BASEPARAMS from '@/utils/baseParams';

// Cloudplay.getCid

export default {
  name: 'CloudGame',
  data() {
    return {
      pkgName: '', //海马云参数 游戏包名
      appChannel: '', //海马云参数 游戏包渠道号
      accessKeyID: '', //海马云参数 BID
      userId: '', //海马云参数
      userToken: '', //海马云参数
      cToken: '', //海马云参数
      portrait: 1, // 0=横屏 1=竖屏
      mdInfo: '', //免登录信息
      channelId: '3733', //海马云参数
      networkDelay: 0, //当前延迟
      isRefresh: false, //是否需要重新刷新游戏
      openPicShow: false, //是否显示开屏图
      exitPopupShow: false, //退出弹窗显示
      qualityList: [], //画质数组
      selectedQuality: 0, //当前选择画质
      orientation: 0, //判断当前手机横竖屏状态
      fullScreen: '', //是否全面屏
      heng: '', //非全面屏iPhone 让游戏画面避开状态栏的样式 横屏下不显示此类名
      timer: null,
      waitShow: true, //是否开启排队的弹窗
    };
  },
  watch: {
    openPicShow() {
      if (this.openPicShow) {
        setTimeout(() => {
          document.getElementById('gif').src =
            document.getElementById('gif').src;
        }, 5);
        this.timer = setInterval(() => {
          document.getElementById('gif').src =
            document.getElementById('gif').src;
        }, 5000);
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('html')[0].style.background = '#fff';
    if (to.name !== 'GameRecharge') {
      this.closeGame();
      this.isRefresh = true;
      next(true);
    } else {
      this.isRefresh = false;
      next(true);
    }
  },
  activated() {
    if (this.isRefresh) {
      this.init();
    } else {
      document.getElementsByTagName('html')[0].style.background = '#000';
      let video = document.getElementsByTagName('video')[0];
      if (video) {
        // 支付接口返回开启画面和声音并传消息关闭游戏内部loading
        video.play();
        this.playAudio();
        Cloudplay.sendMessage(JSON.stringify({ code: 2001 }));
      }
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.judgeBigScreen();
  },

  methods: {
    //判断全面屏
    judgeBigScreen() {
      const rate = window.screen.height / window.screen.width;
      let limit =
        window.screen.height == window.screen.availHeight ? 1.8 : 1.65; // 临界判断值
      // window.screen.height为屏幕高度
      //  window.screen.availHeight 为浏览器 可用高度
      if (rate > limit) {
        //是全面屏
        this.fullScreen = true;
        this.heng = false;
      } else {
        this.fullScreen = false;
        this.heng = true;
      }
    },
    // 页面初始化
    async init() {
      window.addEventListener(
        'orientationchange' in window ? 'orientationchange' : 'resize',
        this.renderResize,
        false,
      );
      this.networkDelay = 0;
      try {
        const res = await ApiCloudGameInfo({
          gameId: this.$route.params.game_id,
        });
        this.$nextTick(() => {
          this.pkgName = res.data.pkg_name;
          this.appChannel = res.data.app_channel;
          this.accessKeyID = res.data.bid;
          this.userId = res.data.user_id;
          this.userToken = res.data.user_token;
          this.cToken = res.data.c_token;
          this.portrait = res.data.portrait;
          this.mdInfo = {
            channel: BASEPARAMS.channel,
            onceToken: res.data.once_token,
            userId: res.data.user_id,
            userName: this.userInfo.username,
            userIcon: this.userInfo.avatar,
            from: BASEPARAMS.from.toString(),
          };
          this.openPicShow = true;
          this.initGame();
        });
      } catch {
        this.isRefresh = true;
      }
    },
    // 初始化游戏
    initGame() {
      Cloudplay.initSDK({
        accessKeyID: this.accessKeyID,
        channelId: this.channelId,
        onSceneChanged: (sceneId, extraInfo) => {
          this.handleSceneId(sceneId, extraInfo);
        },
        MessageHandler: message => {
          this.handleMessage(message);
        },
      });

      this.startGame();
    },
    // 主动开启音频
    playAudio() {
      // 微信自动播放
      if (window.WeixinJSBridge) {
        WeixinJSBridge.invoke(
          'getNetworkType',
          {},
          function (e) {
            Cloudplay.enableAudio(true);
          },
          false,
        );
      } else {
        document.addEventListener(
          'WeixinJSBridgeReady',
          function () {
            WeixinJSBridge.invoke('getNetworkType', {}, function (e) {
              Cloudplay.enableAudio(true);
            });
          },
          false,
        );
      }

      // 点击画面播放
      let oPlayerWarp = document.querySelector('#HMW_cpPlayerDivId');
      let oTouch = oPlayerWarp.querySelector('#touch');
      let oCanvas = oPlayerWarp.querySelector('canvas');
      if (oTouch) {
        oTouch.addEventListener(
          'touchend',
          function () {
            Cloudplay.enableAudio(true);
          },
          true,
        );
      }
      if (oCanvas) {
        oCanvas.addEventListener(
          'touchend',
          function () {
            Cloudplay.enableAudio(true);
          },
          true,
        );
      }
      // 可以自动播放的设备自动播放
      Cloudplay.enableAudio(true);
    },
    // 主动关闭游戏
    closeGame() {
      this.openPicShow = false; //关闭游戏的时候开屏图要false 否则再进入游戏会影响gif
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      window.removeEventListener(
        'orientationchange' in window ? 'orientationchange' : 'resize',
        this.renderResize,
      );
      // 通知云游客户端这边关闭游戏了
      Cloudplay.sendMessage(JSON.stringify({ code: 2003 }));
      Cloudplay.stopGame(data => {
        if (data.code === 0) {
          this.networkDelay = 0;
        }
        this.isRefresh = true;
      });
    },
    // 主动开始游戏
    startGame() {
      Cloudplay.startGame('cloud-game', {
        pkgName: this.pkgName,
        userInfo: {
          uId: this.userId,
          uToken: this.userToken,
          uType: 0,
        },
        priority: 0,
        extraId: '',
        playingtime: 3600 * 1000,
        configInfo: this.mdInfo,
        cToken: this.cToken,
        isArchive: false,
        isPortrait: this.Portrait,
        appChannel: this.appChannel,
      });
    },
    // 打开关闭退出弹窗
    closePopup() {
      this.exitPopupShow = true;
    },
    // 改变画面清晰度
    changeQuality(id) {
      Cloudplay.switchResolution(id, () => {
        this.$toast.success(this.$t('切换清晰度成功'));
      });
    },
    // 重新计算是否手机横屏
    renderResize() {
      setTimeout(() => {
        // 通知游戏手机翻转了
        Cloudplay.sendMessage(JSON.stringify({ code: 2002 }));
        // 真机测试resize有一定延迟才能获取到最新的
        this.orientation =
          window.orientation || window.orientation === 0
            ? window.orientation
            : window.screen.orientation.angle;
        if (!this.fullScreen) {
          if (this.orientation == 90 || this.orientation == -90) {
            this.heng = false;
          } else {
            this.heng = true;
          }
        }
      }, 400);
    },
    // 处理云游戏各个状态
    handleSceneId(sceneId, extraInfo) {
      // 判断手机横竖屏弹窗
      let className =
        !this.portrait && (this.orientation === 0 || this.orientation === 180)
          ? 'portrait'
          : '';
      if (process.env.NODE_ENV === 'development') {
        console.log('sceneId:', sceneId, ' extraInfo:', extraInfo);
      }
      switch (sceneId) {
        // 停止游戏
        case 'stop':
          if (this.$route.name != 'CloudGame') {
            this.startGame();
          } else {
            // 停止游戏
            this.$dialog.alert({
              message: extraInfo.message,
              lockScroll: false,
            });
          }
          break;
        // 开始游戏
        case 'play':
          this.$dialog.close();
          document.getElementsByTagName('html')[0].style.background = '#000';
          this.openPicShow = false;
          clearInterval(this.timer);
          this.timer = null;
          //游戏加载成功后，控制其自动播放声音，如果不希望其自动播放声音请别调用
          setTimeout(() => {
            this.playAudio();
          }, 2000);
          break;
        // 网络延迟
        case 'networkDelay':
          this.networkDelay = extraInfo.networkDelay;
          break;
        // 清晰度列表
        case 'resolutionList':
          this.qualityList = extraInfo.list;
          this.selectedQuality = extraInfo.selected;
          break;
        // 排队
        case 'wait':
          switch (extraInfo.reason) {
            // 显示当前排队的信息
            case 'showQueueInfo':
              this.$dialog.confirm({
                message: extraInfo.message,
                showConfirmButton: false,
                className: className,
              });
              break;
            // 询问是否进入排队
            case 'whetherToQueue':
              this.$dialog
                .confirm({
                  message: `${this.$t('排队原因')}：${
                    extraInfo.message
                  }。${this.$t('当前排队人数')}：${
                    extraInfo.waitingPeople
                  }。${this.$t('是否排队')}?`,
                  cancelButtonText: this.$t('再想想'),
                  confirmButtonText: this.$t('去排队'),
                  className: className,
                })
                .then(() => {
                  Cloudplay.enterQueue();
                })
                .catch(() => {
                  Cloudplay.outQueue();
                });
              break;
            // 排队完成，正在进入游戏
            case 'applyGame':
              this.$dialog.close();
              this.$dialog.confirm({
                message: extraInfo.message,
                showConfirmButton: false,
                className: className,
              });
              break;
          }
          break;
        // 断网提示
        case 'offline':
          this.$dialog.confirm({
            message: extraInfo.message,
            showCancelButton: false,
            className: className,
          });
          break;
        // 重连状态提示
        case 'reconnectingStatus':
          if (this.$route.name === 'CloudGame') {
            this.$dialog.confirm({
              message: extraInfo.message,
              showCancelButton: false,
              className: className,
            });
          }
          break;
        // 出错提示
        case 'warning':
          this.$dialog.confirm({
            message: extraInfo.message,
            showCancelButton: false,
            className: className,
          });
          break;
        // 服务器维护
        case 'maintain':
          this.$dialog.confirm({
            message: this.$t('服务器维护中'),
            showCancelButton: false,
            className: className,
          });
          break;
      }
    },
    // 处理云游和盒子间通讯
    handleMessage(message) {
      const msg = JSON.parse(message.payload);
      if (process.env.NODE_ENV === 'development') {
        console.log('message:', message);
      }
      switch (msg.code) {
        // 退出游戏
        case 1001:
          this.back();
          break;
        // 如果是支付
        case 1002:
          setTimeout(() => {
            // 他们自己的支付会先跳然后做拦截，不这么做的话会在他们内部自己跳的时候切路由，造成回来死在他们自己的支付画面
            this.$router.push({
              name: 'GameRecharge',
              query: { orderId: msg.orderId },
            });
          }, 300);
          break;
        // 上报时长接口错误码-201：该用户打开新的云游，onceToken失效
        case 1014:
          // this.$dialog
          //   .confirm({
          //     message: "该用户打开新的云游，onceToken失效",
          //     showCancelButton: false,
          //   })
          //   .then(() => {
          //     this.back();
          //   });
          break;
      }
    },
  },
  components: {
    Ball,
  },
};
</script>
<style lang="less" scoped>
@import '~@/common/cloud_game/saas-sdk.css';
.xxx {
  //非全面屏 避免状态栏遮挡游戏画面
  /deep/ #HMW_cpPlayerDivId {
    width: calc(100vh - 20px) !important;
    margin-left: 20px !important;
  }
}

.open-pic {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  img {
    width: 300 * @rem;
    height: auto;
  }
  &.portrait {
    img {
      transform: rotate(90deg);
    }
  }
  &.column {
    transform: rotate(-90deg);
  }
}
/deep/ .van-dialog.portrait {
  top: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
}
</style>
