<template>
  <div class="verify-security-component">
    <div class="title">密保验证</div>
    <div class="content">
      <form class="form">
        <div class="field">
          <div class="field-icon question-icon"></div>
          <!-- <input
            type="text"
            v-model="question"
            placeholder="输出显示设置密保问题？"
          /> -->
          <security-question-bar
            class="field-content question-box"
            :selectedQuestion.sync="selectedQuestion"
          ></security-question-bar>
        </div>
        <div class="field">
          <div class="field-icon answer-icon"></div>
          <input type="text" v-model="answer" placeholder="请输入密保答案" />
        </div>
      </form>
    </div>
    <div
      class="confirm btn"
      :class="{ on: selectedQuestion.id && answer }"
      @click="confirm"
    >
      下一步
    </div>
    <div class="tips" @click="openKefu">忘记密保？联系在线客服</div>
  </div>
</template>

<script>
import { ApiUserBindEmail } from '@/api/views/users';
import securityQuestionBar from '@/components/security-question-bar';
export default {
  components: {
    securityQuestionBar,
  },
  data() {
    return {
      question: '',
      selectedQuestion: {
        title: '请选择密保问题',
      },
      answer: '',
    };
  },
  methods: {
    async confirm() {
      if (!this.selectedQuestion.id) {
        this.$toast('请选择密保问题');
        return false;
      }
      if (!this.answer) {
        this.$toast('请输入密保答案');
      }

      this.$toast.loading('加载中');
      const params = {
        question_1: this.selectedQuestion.id,
        answer_1: this.answer,
        step: 1,
      };
      const res = await ApiUserBindEmail(params);
      this.$emit('verifySuccess');
    },
  },
};
</script>

<style lang="less" scoped>
.verify-security-component {
  padding: 32 * @rem 16 * @rem;
  .title {
    font-size: 16 * @rem;
    color: #333438;
    line-height: 20 * @rem;
    font-weight: 600;
  }
  .content {
    box-sizing: border-box;
    width: 343 * @rem;
    margin: 12 * @rem auto 0;
    background: #ffffff;
    border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
    padding: 7 * @rem 20 * @rem;
    .form {
      .field {
        display: flex;
        align-items: center;
        width: 100%;
        height: 50 * @rem;
        position: relative;
        background: #ffffff;
        padding: 9 * @rem 0;
        &:not(:first-of-type) {
          border-top: 1 * @rem solid #efefef;
        }
        .field-icon {
          width: 24 * @rem;
          height: 24 * @rem;
          background-size: 24 * @rem 24 * @rem;
          background-repeat: no-repeat;
          background-position: center center;
          &.question-icon {
            background-image: url('~@/assets/images/users/email-question-icon.png');
          }
          &.answer-icon {
            background-image: url('~@/assets/images/users/email-answer-icon.png');
          }
        }
        .question-box {
          flex: 1;
          min-width: 0;
          margin-left: 16 * @rem;
          /deep/ .question-bar {
            width: 100%;
            .question-container {
              width: 100%;
            }
          }
        }
        input {
          flex: 1;
          min-width: 0;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          width: 100%;
          height: 50 * @rem;
          position: relative;
          background: #ffffff;
          padding: 0 16 * @rem;
          border-radius: 25 * @rem;
          font-size: 14 * @rem;
          color: #333333;
          font-weight: 600;
          &::placeholder {
            font-weight: 400;
          }
        }
      }
    }
  }
  .confirm {
    height: 44 * @rem;
    margin: 52 * @rem 0 0;
    text-align: center;
    line-height: 44 * @rem;
    background: @themeBg;
    border-radius: 25 * @rem;
    font-size: 16 * @rem;
    color: #ffffff;
    font-weight: 500;
    opacity: 0.5;
    transition: opacity 0.2s;
    &.on {
      opacity: 1;
    }
  }
  .tips {
    text-align: center;
    margin-top: 16 * @rem;
    font-size: 12 * @rem;
    color: @themeColor;
    line-height: 20 * @rem;
  }
}
</style>
