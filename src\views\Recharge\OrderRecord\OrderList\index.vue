<template>
  <div class="order-list-page">
    <yy-empty v-if="empty"></yy-empty>
    <load-more
      v-else
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
    >
      <div class="order-list">
        <div class="order-item" v-for="(item, index) in orderList" :key="index">
          <div class="top-content">
            <div class="top">
              <div class="date">{{ formatTime(item.create_time) }}</div>
              <div
                v-if="item.status == 1"
                :class="{ cancel: item.closed != 0 }"
                @click="handleCancel(item)"
                class="button btn"
              >
                {{ item.closed != 0 ? '已取消' : '取消订单' }}
              </div>
            </div>
            <div class="item-title">支付金额</div>
            <div class="money"><span>¥</span>{{ item.pay_amount }}</div>
          </div>
          <div class="bottom-content">
            <div class="bottom-item">
              <div class="left">支付方式:</div>
              <div class="right">{{ item.pw }}</div>
            </div>
            <div class="bottom-item">
              <div class="left">订单号:</div>
              <div class="right">{{ item.order_id }}</div>
            </div>
            <div class="bottom-item">
              <div class="left">支付情况:</div>
              <div :class="{ color: item.status == 1 }" class="right">
                {{ formatStatus(item.status) }}
              </div>
            </div>
            <div class="bottom-item">
              <div class="left">充值到:</div>
              <div class="right">{{ item.game_name }}</div>
            </div>
          </div>
        </div>
      </div>
    </load-more>
    <van-popup :lock-scroll="false" class="popup1" v-model="popup1Show" teleport="popup1">
      <div class="title">提示</div>
      <div class="content">
        <div class="text">确认取消当前订单？</div>
      </div>
      <div class="button-container">
        <div @click="popup1Show = false" class="button left-button btn">
          取消
        </div>
        <div
          @click="cancelOrder()"
          :class="{ ajaxing }"
          class="button right-button btn"
        >
          确定
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiGetPayRecordList, ApiCancelOrder } from '@/api/views/recharge.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'OrderList',
  props: {
    status: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      orderList: [],
      loading: false,
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      popup1Show: false, //取消弹窗
      cancelOrderId: 0, //当前要取消订单的ID
      ajaxing: false,
    };
  },
  async created() {
    await this.getOrderList();
  },
  methods: {
    async getOrderList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      try {
        let res = await ApiGetPayRecordList({
          page: this.page,
          listRows: this.listRows,
          status: this.status,
        });
        this.loading = false;
        if (action === 1 || this.page === 1) {
          this.orderList = [];
          if (!res.data.length) {
            this.empty = true;
          } else {
            this.empty = false;
          }
        }
        this.orderList.push(...res.data);
        if (res.data.length < this.listRows) {
          this.finished = true;
        } else {
          if (this.finished === true) {
            this.finished = false;
          }
        }
      } catch (e) {
        if (e.code == 0) {
          this.finished = true;
        }
      } finally {
      }
    },
    async loadMore() {
      await this.getOrderList(2);
    },
    handleCancel(item) {
      if (item.closed != 0) {
        return false;
      }
      this.cancelOrderId = item.order_id;
      this.popup1Show = true;
    },
    async cancelOrder() {
      if (this.ajaxing) {
        return false;
      }
      this.ajaxing = true;
      this.$toast.loading({
        message: '请稍等...',
        forbidClick: true,
        duration: 0,
      });
      try {
        const res = await ApiCancelOrder({ orderId: this.cancelOrderId });
        await this.getOrderList();
        this.popup1Show = false;
      } finally {
        this.ajaxing = false;
      }
    },
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
    formatStatus(val) {
      let result;
      switch (Number(val)) {
        case 1:
          result = '未支付';
          break;
        case 2:
          result = '支付成功';
          break;
        case 3:
          result = '支付失败';
          break;
        default:
          result = '';
          break;
      }
      return result;
    },
  },
};
</script>

<style lang="less" scoped>
.order-list-page {
  margin-top: 44 * @rem;
  position: relative;
  height: 100%;
  overflow-y: auto;
  .order-list {
    .order-item {
      box-sizing: border-box;
      padding: 0 16 * @rem;
      margin: 10 * @rem 15 * @rem;
      border-radius: 12 * @rem;
      background-color: #fff;
      .top-content {
        padding: 18 * @rem 0;
        .top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .date {
            font-size: 14 * @rem;
            color: #999;
          }
          .button {
            box-sizing: border-box;
            width: 72 * @rem;
            height: 24 * @rem;
            border-radius: 6 * @rem;
            border: 1 * @rem solid rgba(255, 146, 72, 1);
            font-size: 11 * @rem;
            color: rgba(255, 146, 72, 1);
            line-height: 22 * @rem;
            text-align: center;
            &.cancel {
              background: #b1b1b1;
              color: #fff;
              border-color: #b1b1b1;
            }
          }
        }
        .item-title {
          margin-top: 24 * @rem;
          font-size: 14 * @rem;
          color: #909090;
          line-height: 17 * @rem;
          text-align: center;
          margin-bottom: 8 * @rem;
        }
        .money {
          font-size: 36 * @rem;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
          font-weight: 600;
          color: #000000;
          line-height: 50 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            margin-right: 6 * @rem;
            font-size: 20 * @rem;
            line-height: 28 * @rem;
          }
        }
      }
      .bottom-content {
        padding: 10 * @rem 0 0;
        overflow: hidden;
        .bottom-item {
          display: flex;
          line-height: 24 * @rem;
          margin-bottom: 10 * @rem;
          .left {
            flex: 0 0 65 * @rem;
            margin-right: 10 * @rem;
            font-size: 14 * @rem;
            color: rgba(153, 153, 153, 1);
            text-align: right;
          }
          .right {
            flex: 1;
            font-size: 14 * @rem;
            color: #222;
            &.color {
              color: rgba(255, 18, 33, 1);
            }
          }
        }
      }
    }
  }
}
.popup1 {
  box-sizing: border-box;
  width: 244 * @rem;
  padding: 15 * @rem;
  background: #ffffff;
  border-radius: 11 * @rem;
  overflow: unset;
  &::before {
    position: absolute;
    top: -33 * @rem;
    left: 0;
    content: '';
    display: block;
    width: 244 * @rem;
    height: 37 * @rem;
    .image-bg('~@/assets/images/popup_bg2.png');
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 14 * @rem;
    line-height: 20 * @rem;
    text-align: center;
  }
  .title {
    width: 36 * @rem;
    height: 25 * @rem;
    font-size: 18 * @rem;
    font-family: PingFangHK-Medium, PingFangHK;
    font-weight: 500;
    color: #000000;
    line-height: 25 * @rem;
    text-align: center;
    margin: 12 * @rem auto;
  }
  .button-container {
    margin-top: 27 * @rem;
    display: flex;
    justify-content: space-between;
    .button {
      width: 96 * @rem;
      height: 32 * @rem;
      border-radius: 6 * @rem;
      font-size: 13 * @rem;
      font-family: PingFangHK-Medium, PingFangHK;
      font-weight: 500;
      line-height: 32 * @rem;
      text-align: center;
    }
    .left-button {
      background: #f2f2f2;
      color: #7d7d7d;
    }
    .right-button {
      background: @themeBg;
      color: #fff;
      &.ajaxing {
        background: #f2f2f2;
      }
    }
  }
}
</style>
