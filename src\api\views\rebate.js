import { request } from '../index';

/* 返利首页游戏列表 */
/**
 * @param keywords 搜索关键字
 *  */
export function ApiRebateGameList(params = {}) {
  return request('/api/rebate/gameList', params);
}

/* 我的返利 */
/**
 * res
 * @param status -1=全部，0-未处理，1-处理中，2-已发放，3-已驳回，4-已撤回
 *  */
export function ApiRebateMine(params = {}) {
  return request('/api/rebate/mine', params);
}

/* 查询某个用户某个游戏某天充值数，并计算返利内容 */
/**
 * @param appId 游戏id
 * @param date 游戏日期
 * @param xhId 小号id
 *  */
export function ApiRebateMyDayPay(params = {}) {
  return request('/api/rebate/myDayPay', params);
}

/* 提交 */
/**
 * @param gameId 游戏ID
 * @param date 日期
 * @param xhId 小号ID
 * @param gameArea 游戏区服
 * @param gameRoleName 角色名
 * @param gameRoleId 角色ID
 * @param contact qq号
 * @param remark 申请备注
 *  */
export function ApiRebateSubmit(params = {}) {
  return request('/api/rebate/submit', params);
}

/* 资讯活动页申请返利 */
/**
 * @param activityId 活动ID
 *  */
export function ApiRebateObtainAward(params = {}) {
  return request('/api/rebate/obtainAward', params);
}

/* 我的返利 */
/**
 * @param gameId 游戏id 可选
 * @param status 类型 全部0  待处理1 已发放2 驳回3
 *  */
export function ApiRebateGetList(params = {}) {
  return request('/api/rebate/getList', params);
}

/* 资讯活动页申请返利 */
// 2024年5月28日
/* 返利 - 提交返利申请 */
export function ApiRebateSubmitRebate(params = {}) {
  return request('/api/rebate/submitRebate', params);
}

/* 返利 - 获取用户游戏角色的充值金额 */
export function ApiRebateGetUserPaySum(params = {}) {
  return request('/api/rebate/getUserPaySum', params);
}

/* 返利 - 获取返利信息 */
export function ApiRebateGetRebateInfo(params = {}) {
  return request('/api/rebate/getRebateInfo', params);
}
