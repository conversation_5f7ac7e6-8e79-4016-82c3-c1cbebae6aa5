<template>
  <div class="welfare-page page">
    <nav-bar-2
      :title="'648福利'"
      :placeholder="false"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :bgStyle="bgStyle"
    >
    </nav-bar-2>
    <div class="ruler-btn" @click="rulerPopupShow = true">规则</div>
    <div
      class="main"
      :style="{
        backgroundImage: `url(${topInfo.configs.titleimg || ''})`,
        backgroundColor: topInfo.configs.bg_color,
      }"
    >
      <div
        class="top-info"
        :style="{ backgroundImage: `url(${topInfo.configs.get_img || ''})` }"
      >
        <div class="get-number">今日已有{{ topInfo.num }}人领</div>
        <div class="get-swiper">
          <swiper :options="getSwiperOption">
            <swiper-slide
              class="swiper-slide"
              v-for="(item, index) in topInfo.list"
              :key="index"
            >
              <div
                class="info"
                :style="{ color: topInfo.configs.get_time_color }"
              >
                <span :style="{ color: topInfo.configs.get_game_color }">{{
                  item.nickname
                }}</span>
                领取了
                <span :style="{ color: topInfo.configs.get_game_color }">{{
                  item.game_title
                }}</span>
                648充值卡
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="content">
        <div class="tabs">
          <div
            class="tab-btn"
            :class="{ active: tab == 0 }"
            @click="changeTab(0)"
          >
            <img
              src="~@/assets/images/welfare/welfare648/icon-game-list.png"
              alt=""
            />
            <img
              class="active"
              src="~@/assets/images/welfare/welfare648/icon-game-list-active.png"
              alt=""
            />
          </div>
          <div
            class="tab-btn"
            :class="{ active: tab == 1 }"
            @click="changeTab(1)"
          >
            <img
              src="~@/assets/images/welfare/welfare648/icon-my-get.png"
              alt=""
            />
            <img
              class="active"
              src="~@/assets/images/welfare/welfare648/icon-my-get-active.png"
              alt=""
            />
          </div>
        </div>
        <div class="cate-list" v-if="tab == 0 && cateList.length">
          <div
            class="cate-item"
            :class="{ active: cateItem.id == cate.id }"
            v-for="(cateItem, index) in cateList"
            :key="index"
            @click="changeCate(cateItem)"
          >
            {{ cateItem.title }}
          </div>
        </div>
        <div class="list">
          <yy-list
            class="game648-list"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :check="false"
            :empty="empty"
          >
            <div
              class="game-item"
              v-for="(card, index) in cardList"
              :key="index"
            >
              <game-item-4
                :gameInfo="card.game ? card.game : card"
                :showHot="true"
              ></game-item-4>
              <div class="right-box">
                <div
                  class="get-btn"
                  @click="toGameDetail(card.id)"
                  v-if="tab == 1"
                  >去使用</div
                >
                <template v-else>
                  <div class="get-btn" @click="chooseXh(card)">{{
                    card.benefit_num > 1 ? '一键领取' : '领取'
                  }}</div>
                  <span v-if="card.benefit_num > 1"
                    >{{ card.benefit_num }}个待领取</span
                  >
                </template>
              </div>
            </div>
          </yy-list>
        </div>
      </div>
    </div>
    <!-- 活动规则 -->
    <van-dialog
      v-model="rulerPopupShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      class="rule-popup-box"
    >
      <div class="content-box">
        <div class="title">活动规则</div>
        <div class="cont" v-html="topInfo.ruleText"></div>
        <div class="close-btn" @click="rulerPopupShow = false">关闭</div>
      </div>
    </van-dialog>

    <!-- 小号选择弹窗 -->
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="Number(selectCoupon.id)"
      :autoXh="1"
      @onSelectSuccess="getWelfare"
    ></yy-xh-select>

    <!-- 礼包领取结果弹窗 -->
    <cardpass-copy-popup
      :show.sync="cardCopyPopupShow"
      :info="cardSelected"
      :autoXh="1"
    ></cardpass-copy-popup>

    <!-- 领取结果 -->
    <van-dialog
      v-model="welfareResultPopupShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
      @closed="closePopup"
      class="result-popup-box"
    >
      <div class="content-box" v-if="welfareResultInfo.game_id">
        <div class="title"
          >成功领取{{
            welfareResultInfo.card.length + welfareResultInfo.coupon.length
          }}个648福利</div
        >
        <div class="tips">需登录游戏使用，已为您预下载游戏~</div>
        <div class="bg">
          <div class="welfare-content-box">
            <div class="card-list" v-if="welfareResultInfo.card.length">
              <div
                class="card-item"
                v-for="(card, cardIndex) in welfareResultInfo.card"
                :key="cardIndex"
              >
                <img :src="card.icon" alt="" />
                <div class="info">
                  <div class="name">{{ card.title }}</div>
                  <div class="cardpass">{{ card.cardpass }}</div>
                </div>
                <div class="get-btn" @click="copyCard(card)">复制</div>
              </div>
            </div>
            <div class="coupon-list" v-if="welfareResultInfo.coupon.length">
              <div
                class="coupon-item"
                v-for="(coupon, couponIndex) in welfareResultInfo.coupon"
                :key="couponIndex"
              >
                <div class="left">
                  <div class="money">
                    <div class="unit">¥</div>
                    <div class="money-num">{{ coupon.money }}</div>
                  </div>
                  <div class="reach-money">满{{ coupon.reach_money }}可用</div>
                </div>
                <div class="right">
                  <div class="name">{{ coupon.title }}</div>
                  <div class="time">{{ coupon.period_title }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="toDetail" @click="toGameDetail(welfareResultInfo.game_id)"
            >前往查看</div
          >
        </div>
        <div class="close-btn" @click="welfareResultPopupShow = false"></div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiCardGetMemGet648CardLog } from '@/api/views/gift.js';
import {
  ApiGetGame648List,
  ApiGetMy648GameList,
  ApiGetGame648,
} from '@/api/views/welfare.js';
import { set } from 'vue';
export default {
  data() {
    return {
      topInfo: {
        num: 0,
        list: {},
        ruleText: '',
        configs: {},
      },
      tab: 0,
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      rulerPopupShow: false,
      page: 1,
      listRows: 10,
      cate: {
        id: 0,
        title: '全部',
      },
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      empty: false,
      cateList: [],
      cardList: [],
      xhDialogShow: false,
      selectCoupon: {},
      getSwiperOption: {
        slidesPerView: 1,
        direction: 'vertical',
        loop: true,
        autoplay: true,
        allowTouchMove: false,
      },
      welfareResultInfo: {},
      welfareResultPopupShow: false,
      cardSelected: {},
      cardCopyPopupShow: false,
      getCouponShow: false, //领取成功代金券弹窗
      isReFresh: false,
      canGetGiftNumber: true,
    };
  },
  async created() {
    this.getMemberList();
    this.getGame648List();
    window.addEventListener('scroll', this.handleScroll);

    // 神策埋点
    this.$sensorsTrack('free_648benefits_pageView')
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    async getMemberList() {
      const res = await ApiCardGetMemGet648CardLog();
      if (res.data.num > 9999) {
        this.topInfo.num = res.data.num.tofixed(1) + 'W';
      } else {
        this.topInfo.num = res.data.num;
      }
      this.topInfo.list = res.data.list;
      this.topInfo.ruleText = res.data.rule_text.replaceAll('\n', '<br>');
      this.topInfo.configs = res.data.configs;
    },
    async changeTab(id) {
      this.tab = id;
      this.cardList = [];
      this.empty = false;
      this.finished = false;
      if (!this.tab) {
        await this.getGame648List();
      } else {
        await this.getMy648GameList();
      }
    },
    async getGame648List(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;

      let res = await ApiGetGame648List({
        cate: this.cate.id,
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      let { list, cate, status_card } = res.data;
      this.canGetGiftNumber = status_card;
      if (cate) {
        this.cateList = [
          {
            id: 0,
            title: '全部',
          },
          ...cate,
        ];
        if (!this.cate) {
          this.cate = this.cateList[0];
        }
      }
      if (action === 1 || this.page === 1) {
        this.cardList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.cardList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async getMy648GameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      let res = await ApiGetMy648GameList({
        page: this.page,
        listRows: this.listRows,
      });
      this.loadingObj.loading = false;
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.cardList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.cardList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      try {
        if (!this.tab) {
          await this.getGame648List(2);
        } else {
          await this.getMy648GameList(2);
        }
      } finally {
        this.loadingObj.loading = false;
      }
    },
    async onRefresh() {
      try {
        if (!this.tab) {
          await this.getGame648List();
        } else {
          await this.getMy648GameList();
        }
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async changeCate(cate) {
      this.cate = cate;
      this.cardList = [];
      try {
        if (!this.tab) {
          await this.getGame648List();
        } else {
          await this.getMy648GameList();
        }
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    chooseXh(item) {
      this.selectCoupon = item;
      this.xhDialogShow = true;
    },
    async getWelfare(xh) {
      const toastLoading = this.$toast.loading('领取中...');
      try {
        let res = await ApiGetGame648({
          game_id: this.selectCoupon.id,
          xh_id: xh.xhId,
        });

        this.welfareResultInfo = { game_id: this.selectCoupon.id, ...res.data };
        this.welfareResultPopupShow = true;
        if (this.canGetGiftNumber != res.data.status_card) {
          this.isReFresh = true;
        }
        this.canGetGiftNumber = res.data.status_card;
        this.cardList = this.cardList.filter(
          cardItem => this.selectCoupon !== cardItem,
        );
      } finally {
        toastLoading.clear();
      }
    },
    async closePopup() {
      if (this.isReFresh) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
        this.loadingObj.reloading = true;
        await this.onRefresh();
        this.isReFresh = false;
      }
    },
    toGameDetail(id) {
      this.$router.push({
        name: 'GameDetail',
        params: {
          id,
        },
      });
    },
    copyCard(card) {
      this.$copyText(card.cardpass).then(
        res => {
          this.$toast('复制成功');
          this.popupShow = false;
        },
        err => {
          this.$dialog.alert({
            message: '复制失败，请手动复制',
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.welfare-page {
  position: relative;
  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20 * @rem;
  }
  .ruler-btn {
    width: 26 * @rem;
    height: 56 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 13 * @rem;
    line-height: 18 * @rem;
    letter-spacing: 1 * @rem;
    text-align: center;
    color: #ffffff;
    background-color: #ff4661;
    border-radius: 5 * @rem 0 0 5 * @rem;
    position: absolute;
    top: calc(39 * @rem + @safeAreaTop);
    top: calc(39 * @rem + @safeAreaTopEnv);
    right: 0;
    cursor: pointer;
  }
  .main {
    flex: 1;
    background: #fbb068 url(~@/assets/images/welfare/welfare648/top-bg2.png)
      no-repeat top center;
    background-size: 100% auto;
    padding-bottom: 18 * @rem;
  }
  .top-info {
    display: flex;
    align-items: center;
    width: 339 * @rem;
    height: 32 * @rem;
    margin: 282 * @rem 18 * @rem 44 * @rem;
    background: url(~@/assets/images/welfare/welfare648/info-bg.png) no-repeat;
    background-size: 339 * @rem 32 * @rem;

    .get-number {
      flex-shrink: 0;
      width: 115 * @rem;
      height: 32 * @rem;
      line-height: 32 * @rem;
      font-size: 11 * @rem;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      background: url(~@/assets/images/welfare/welfare648/sp-info-bg.png)
        no-repeat;
      background-size: 115 * @rem 32 * @rem;
    }

    .get-swiper {
      flex: 1;
      min-width: 0;
      padding: 0 13 * @rem;
      height: 36 * @rem;
      line-height: 36 * @rem;
      .swiper-container {
        height: 100%;
      }
      .info {
        display: block;
        line-height: 36 * @rem;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 10 * @rem;
        font-weight: 500;
        color: #cb3344;

        span {
          color: #c106f0;
        }
      }
    }
  }
  .content {
    width: 355 * @rem;
    background-color: #fff;
    border-radius: 16 * @rem;
    padding: 12 * @rem;
    box-sizing: border-box;
    margin: 0 auto;

    .tabs {
      width: 100%;
      height: 46 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: linear-gradient(91deg, #ff91a2 0%, #ff455f 100%);
      border-radius: 16 * @rem;
      padding: 0 4 * @rem;
      box-sizing: border-box;

      .tab-btn {
        width: 156 * @rem;
        height: 46 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          background: url(~@/assets/images/welfare/welfare648/tab-active-bg.png)
            no-repeat center;
          background-size: 156 * @rem 46 * @rem;
          img {
            display: none;
          }
          .active {
            display: block;
          }
        }

        img {
          width: auto;
          height: 18 * @rem;
        }
        .active {
          display: none;
        }
      }
    }

    .cate-list {
      display: flex;
      align-items: center;
      margin-top: 14 * @rem;
      overflow-x: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .cate-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        height: 22 * @rem;
        background: #f7f8fa;
        font-family:
          PingFang SC,
          PingFang SC;
        font-size: 12 * @rem;
        color: #93999f;
        line-height: 12 * @rem;
        text-align: center;
        border-radius: 6 * @rem;
        padding: 0 10 * @rem;
        margin-right: 10 * @rem;

        &:last-of-type {
          margin-right: 0;
        }

        &.active {
          color: #333333;
        }
      }
    }

    .list {
      min-height: 250 * @rem;
    }

    .game648-list {
      .game-item {
        display: flex;
        align-items: center;
      }
      .right-box {
        margin-left: 10 * @rem;

        .get-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 65 * @rem;
          height: 29 * @rem;
          background: #ff577c;
          border-radius: 15 * @rem;
          font-weight: bold;
          font-size: 12 * @rem;
          color: #ffffff;
          line-height: 17 * @rem;
          text-align: center;
        }

        span {
          display: block;
          height: 14 * @rem;
          font-weight: bold;
          font-size: 10 * @rem;
          color: #ff5f77;
          line-height: 14 * @rem;
          text-align: center;
          overflow: hidden;
          margin-top: 4 * @rem;
        }
      }
    }
  }
  .rule-popup-box {
    .content-box {
      padding: 30 * @rem 20 * @rem 20 * @rem;

      .title {
        display: block;
        height: 20 * @rem;
        font-size: 16 * @rem;
        font-weight: 600;
        color: #333333;
        line-height: 20 * @rem;
        text-align: center;
      }

      .cont {
        font-size: 13 * @rem;
        font-weight: 400;
        color: #777777;
        line-height: 25 * @rem;
        margin-top: 23 * @rem;
      }

      .close-btn {
        width: 238 * @rem;
        height: 38 * @rem;
        line-height: 38 * @rem;
        background: #fe6600;
        border-radius: 22 * @rem;
        text-align: center;
        font-size: 14 * @rem;
        font-weight: 400;
        color: #ffffff;
        margin: 20 * @rem auto 0;
        cursor: pointer;
      }
    }
  }
  .result-popup-box {
    background-color: transparent;
    overflow: unset;
    &::before {
      content: '';
      display: block;
      width: 326 * @rem;
      height: 118 * @rem;
      background: url(~@/assets/images/welfare/welfare648/get-result-bg.png)
        no-repeat;
      background-size: 326 * @rem auto;
      position: absolute;
      top: -57 * @rem;
      left: 0;
      z-index: 1;
    }

    .content-box {
      width: 300 * @rem;
      box-sizing: border-box;
      position: relative;
      z-index: 2;

      .title {
        height: 24 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 19 * @rem;
        color: #191b1f;
        line-height: 24 * @rem;
        overflow: hidden;
        padding: 21 * @rem 16 * @rem 0;
      }
      .tips {
        height: 16 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-size: 13 * @rem;
        color: #93999f;
        line-height: 16 * @rem;
        padding: 0 16 * @rem;
      }

      .bg {
        background-color: #fff;
        padding: 15 * @rem 16 * @rem 18 * @rem;
        border-radius: 0 0 20 * @rem 20 * @rem;
        margin-top: -2 * @rem;
      }
      .welfare-content-box {
        max-height: 60vh;
        overflow-y: auto;
      }

      .card-item {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffeaef;
        padding: 10 * @rem;
        border-radius: 10 * @rem;
        margin-bottom: 10 * @rem;

        img {
          flex-shrink: 0;
          width: 38 * @rem;
          height: 38 * @rem;
          margin-right: 10 * @rem;
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            height: 16 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            font-size: 13 * @rem;
            color: #191b1f;
            line-height: 16 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .cardpass {
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #93999f;
            line-height: 14 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .get-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 56 * @rem;
          height: 27 * @rem;
          background: #ff577c;
          border-radius: 14 * @rem;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 13 * @rem;
          color: #ffffff;
          line-height: 16 * @rem;
          text-align: center;
          margin-left: 10 * @rem;
        }
      }

      .coupon-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 68 * @rem;
        background: url(~@/assets/images/welfare/welfare648/coupon-bg.png)
          no-repeat;
        background-size: 100% auto;
        margin-bottom: 10 * @rem;

        .left {
          flex: 0 0 77 * @rem;
          width: 77 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .money {
            display: flex;
            align-items: flex-end;
            justify-content: center;

            .unit {
              height: 13 * @rem;
              font-weight: bold;
              font-size: 9 * @rem;
              color: #fe4a26;
              line-height: 13 * @rem;
              margin-bottom: 3 * @rem;
            }

            .money-num {
              height: 25 * @rem;
              font-weight: bold;
              font-size: 18 * @rem;
              color: #fe4a26;
              line-height: 25 * @rem;
            }
          }
          .reach-money {
            height: 13 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 9 * @rem;
            color: #ff3000;
            line-height: 13 * @rem;
            text-align: center;
          }
        }

        .right {
          flex: 1;
          min-width: 0;
          padding-left: 16 * @rem;
          padding-right: 21 * @rem;

          .name {
            height: 17 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            font-size: 12 * @rem;
            color: #121212;
            line-height: 17 * @rem;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .time {
            height: 14 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 10 * @rem;
            color: #93999f;
            line-height: 14 * @rem;
            text-align: left;
            margin-top: 5 * @rem;
          }
        }
      }

      .toDetail {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 238 * @rem;
        height: 40 * @rem;
        background: #ff577c;
        border-radius: 29 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #ffffff;
        line-height: 20 * @rem;
        text-align: center;
        margin: 6 * @rem auto 0;
      }
    }
    .close-btn {
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/welfare/popup-bottom-close-btn.png)
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
      position: absolute;
      bottom: -48 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
