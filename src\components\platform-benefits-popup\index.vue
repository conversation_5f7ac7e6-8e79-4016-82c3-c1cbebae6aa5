<template>
  <div>
    <!-- 平台福利弹窗 -->
    <van-popup
      v-model="popupShow"
      :close-on-click-overlay="true"
      position="bottom"
      :lock-scroll="false"
      round
      class="platform-benefits-popup"
    >
      <div class="container">
        <div class="title">平台福利</div>
        <div class="content">
          <!-- 开通立享 -->
          <div class="open-lixiang-seciton" v-if="!userInfo.is_svip">
            <div class="section-title">
              <div class="title-text">开通立享 </div>
            </div>
            <div class="activity-bar">
              <div class="left-info">
                <div class="icon">
                  <img src="~@/assets/images/games/activity-icon.png" alt="" />
                </div>
                <div class="title"
                  >SVIP会员金币奖励翻倍，享金币兑换平台币等18+项特权</div
                >
              </div>
              <div class="right-arrow">
                <div class="activate-now" @click="goSvip()">立即开通</div>
              </div>
            </div>
          </div>
          <!-- 独家福利 -->
          <div
            class="benefits-container section"
            v-if="exclusive_benefits.length"
          >
            <div class="section-title">
              <div class="title-text">独家福利 </div>
            </div>
            <div class="benefits-list">
              <div
                class="benefits-item"
                @click="clickBenefitItem(item)"
                v-for="(item, index) in exclusive_benefits"
                :key="index"
              >
                <div class="icon">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="center">
                  <div class="title-line">
                    <div class="title">{{ item.title }}</div>
                    <div class="subtitle" v-if="item.subtitle">
                      {{ item.subtitle }}
                    </div>
                  </div>
                  <div class="desc">{{ item.desc }}</div>
                </div>
                <div class="right-icon"></div>
              </div>
            </div>
          </div>
          <!-- 上线福利 -->
          <div class="welfare-container section" v-if="features">
            <div class="section-title">
              <div class="title-text">上线福利 </div>
            </div>
            <div class="welfare-box">
              <div class="wel-section">
                <div
                  class="content-text"
                  ref="content1"
                  v-html="features"
                ></div>
              </div>
            </div>
          </div>
          <!-- 小提示 -->
          <div class="tips-container">
            <span class="tips"
              >由于福利活动存在变动，实际以领取的福利为准，
              如有疑问，可联系客服</span
            >
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'PlatformBenefitsPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    exclusive_benefits: {
      type: Array,
      default: [],
    },
    features: {
      type: String,
      default: '',
    },
    svip_info: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {},
  data() {
    return {};
  },
  async mounted() {},

  methods: {
    goSvip() {
      this.$emit('update:show', false);
      this.$nextTick(() => {
        this.toPage('Svip');
      });
    },
    clickBenefitItem(item) {
      this.$emit('update:show', false);
      this.$nextTick(() => {
        switch (item.action_code) {
          case 13: // web内页
            this.toPage(item.web_page);
            break;
          case 23: // 游戏签到
            this.toPage('GameSignInDetail', { id: this.id });
            break;
          case 21: // 开局号
            this.toPage('OpeningAccount', { id: this.id });
            break;
        }
      });
    },
  },
  computed: {
    popupShow: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.platform-benefits-popup {
  padding: 22 * @rem 12 * @rem 25 * @rem;
  background: #ffffff;
  overflow: hidden;
  box-sizing: border-box;
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      font-weight: 600;
      font-size: 16 * @rem;
      color: #191b1f;
    }
    .content {
      margin-top: 20 * @rem;
      min-width: 351 * @rem;
      max-height: 550 * @rem;
      overflow-y: auto;
      overflow-x: hidden;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      .open-lixiang-seciton {
        width: 351 * @rem;
        height: 86 * @rem;
        .activity-bar {
          margin-top: 12 * @rem;
          height: 56 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 10 * @rem;
          box-sizing: border-box;
          background: url('~@/assets/images/games/activity-bg.png') #fff5db
            no-repeat top center;
          background-size: 351 * @rem 36 * @rem;
          .left-info {
            display: flex;
            align-items: center;
            .icon {
              width: 36 * @rem;
              height: 36 * @rem;
            }
            .title {
              width: 196 * @rem;
              height: 36 * @rem;
              line-height: 18 * @rem;
              margin-left: 7 * @rem;
              font-weight: 600;
              font-size: 12 * @rem;
              color: #6c4537;
              overflow: hidden;
            }
          }
          .right-arrow {
            .activate-now {
              padding: 0 12 * @rem;
              height: 28 * @rem;
              background: linear-gradient(270deg, #ff7a66 0%, #ffc670 100%);
              border-radius: 48 * @rem;
              font-weight: 500;
              font-size: 10 * @rem;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
      .benefits-container {
        .benefits-title {
          height: 20 * @rem;
          line-height: 20 * @rem;
          // background: url(~@/assets/images/games/dujia-title-icon.png) left
          //   center no-repeat;
          // background-size: 18 * @rem 18 * @rem;
          // padding-left: 24 * @rem;
          font-size: 16 * @rem;
          color: #191b1f;
          font-weight: 600;
          display: flex;
          align-items: center;
        }
        .benefits-list {
          width: 351 * @rem;
          border-radius: 8 * @rem;
          // background-color: #f9f9f9;
          // padding: 14 * @rem 10 * @rem;
          .benefits-item {
            display: flex;
            align-items: center;
            height: 54 * @rem;
            position: relative;
            &:not(:first-of-type) {
              margin-top: 12 * @rem;
            }
            .icon {
              width: 54 * @rem;
              height: 54 * @rem;
              border-radius: 12 * @rem;
              overflow: hidden;
            }
            .center {
              flex: 1;
              min-width: 0;
              margin-left: 8 * @rem;
              .title-line {
                display: flex;
                align-items: center;
                .title {
                  font-size: 14 * @rem;
                  color: #303236;
                  font-weight: 600;
                  line-height: 18 * @rem;
                  flex-shrink: 0;
                }
                .subtitle {
                  box-sizing: border-box;
                  border: 0.5 * @rem solid #ff7171;
                  border-radius: 4 * @rem;
                  margin-left: 4 * @rem;
                  height: 16 * @rem;
                  line-height: 16 * @rem;
                  padding: 0 2 * @rem;
                  background-color: #ffecec;
                  font-size: 11 * @rem;
                  color: #ff5050;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
              }
              .desc {
                width: 263 * @rem;
                font-size: 11 * @rem;
                color: #93999f;
                line-height: 14 * @rem;
                margin-top: 8 * @rem;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
            .right-icon {
              width: 9 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/games/benefits-arrow.png) left
                center no-repeat;
              background-size: 9 * @rem 12 * @rem;
            }
          }
        }
      }
      .welfare-container {
        .welfare-box {
          background: #f6fefc;
          border-radius: 8 * @rem;
          border: 1 * @rem solid #e1f9f0;
          background: url('~@/assets/images/games/welfare-container-bg.png')
            #f6fefc no-repeat top center;
          width: 351 * @rem;
          background-size: 351 * @rem 108 * @rem;
          .welfare-title {
            padding: 10 * @rem 15 * @rem 0 12 * @rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
              width: 73 * @rem;
              height: 25 * @rem;
            }
            .arrow {
              width: 24 * @rem;
              height: 24 * @rem;
            }
          }
          .wel-section {
            box-sizing: border-box;
            padding: 16 * @rem 12 * @rem;
            // background: linear-gradient(360deg, #ffffff 0%, #fff5f3 100%);
            border-radius: 12 * @rem;
            width: 100%;
            position: relative;
          }
          .content-text {
            font-size: 14px;
            color: #60666c;
            line-height: 20px;
            display: -webkit-box;
            max-height: unset;
            height: auto;
            transition: 0.3s;
            display: flex;
            flex-wrap: wrap;
            &.on {
              max-height: 100px;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 5;
              overflow: hidden;
            }
          }
        }
      }
      .tips-container {
        margin: 16 * @rem auto 0;
        width: 242 * @rem;
        text-align: center;
        .tips {
          font-size: 11 * @rem;
          line-height: 17 * @rem;
          color: #93999f;
        }
      }
      .section {
        padding: 20 * @rem 12 * @rem 0;
        .section-title {
          margin-bottom: 10 * @rem;
        }
        .section-arrow {
          margin-left: 4 * @rem;
          width: 8 * @rem;
          height: 12 * @rem;
        }
      }
      .section-title {
        display: flex;
        justify-content: space-between;
        height: 25 * @rem;
        align-items: center;
        .title-icon {
          width: 22 * @rem;
          height: 22 * @rem;
          .image-bg('~@/assets/images/games/sun-icon.png');
          margin-right: 5 * @rem;
          &.icon-jieshao {
            .image-bg('~@/assets/images/games/icon-jieshao.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.icon-xinxi {
            .image-bg('~@/assets/images/games/icon-xinxi.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.icon-jiagebiao {
            .image-bg('~@/assets/images/games/icon-jiagebiao.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.icon-pinglun {
            .image-bg('~@/assets/images/games/icon-pinglun.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.icon-fuli {
            .image-bg('~@/assets/images/games/icon-fuli.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.icon-xiangguan {
            .image-bg('~@/assets/images/games/icon-xiangguan.png');
            background-position: center center;
            background-size: 18 * @rem 18 * @rem;
          }
        }
        .title-text {
          font-size: 16 * @rem;
          color: #191b1f;
          font-weight: 600;
          display: flex;
          align-items: center;
          .number {
            font-weight: 600;
            font-size: 13 * @rem;
            color: #303236;
          }
        }
        .title-right {
          display: flex;
          align-items: center;
          span {
            font-size: 15 * @rem;
            color: @themeColor;
          }
          .title-right-icon {
            width: 10 * @rem;
            height: 10 * @rem;
            background: url(~@/assets/images/games/right-arrow.png) center
              center no-repeat;
            background-size: 10 * @rem 10 * @rem;
            margin-left: 4 * @rem;
          }
        }
      }
    }
  }
}
</style>
