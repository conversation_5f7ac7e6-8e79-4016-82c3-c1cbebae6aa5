<template>
  <div class="page">
    <router-view class="switch-page" :class="{'pd-0': platform == 'android'}"></router-view>
    <tab-bar></tab-bar>
  </div>
</template>

<script>
import tabBar from '@/components/tab-bar';
import { mapActions } from 'vuex';
import {platform} from '@/utils/box.uni.js';
export default {
  name: 'SwitchPages',
  components: {
    tabBar,
  },
  data() {
    return {
      platform
    }
  },
  activated() {
    // 每次到首页时刷新小红点状态
    if(this.platform !== 'android') {
      this.SET_UNREAD_COUNT();
    }
  },
  methods: {
    ...mapActions({
      SET_UNREAD_COUNT: 'system/SET_UNREAD_COUNT',
    }),
  },
};
</script>

<style lang="less" scoped>
.switch-page {
  flex: 1;
  padding-bottom: 50 * @rem;
  &.pd-0 {
    padding-bottom: 0;
  }
 }
</style>
