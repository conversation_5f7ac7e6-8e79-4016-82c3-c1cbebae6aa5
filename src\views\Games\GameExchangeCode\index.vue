<template>
  <div class="game-exchange-code">
    <nav-bar-2
      bgStyle="transparent-white"
      :border="false"
      :placeholder="false"
    ></nav-bar-2>
    <img :src="bg" class="bg" />
    <main>
      <div class="game-info">
        <img :src="game_info.titlepic" class="game-left" />
        <div class="game-center">
          <div class="game-name">{{ game_info.title }}</div>
          <div class="tag-list">
            <div
              v-for="(item, index) in game_info.new_cate_list"
              :key="index"
              class="tag-item"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div class="game-right">
          <div class="hot-icon"></div>
          <div class="hot-text">{{ game_info.totaldown }}</div>
        </div>
      </div>
      <div class="explain">{{ exchange_text }}</div>
      <div class="big-title">兑换码大全</div>
      <div class="exchange-list">
        <div
          v-for="(item, index) in exchang_list"
          :key="index"
          :class="{ empty: item.is_ok == 0 }"
          class="exchange-item"
        >
          <div class="exchange-left">
            <div class="exchange-text">{{ item.content }}</div>
            <div class="exchange-code">
              兑换码：{{ item.card }}<span></span>
            </div>
          </div>
          <div class="exchange-right">
            <div @click="copy(item.card)" class="button">
              {{ item.is_ok == 1 ? '复制' : '已失效' }}
            </div>
          </div>
        </div>
      </div>
      <div class="explain2">
        <div class="explain-title">温馨提示</div>
        <div class="explain-text">1.发现兑换码后请尽快使用，防止过期</div>
        <div class="explain-text">
          2.同一账号只能使用一次兑换码，重复只用为无效
        </div>
        <div class="explain-text">
          3.游戏盒将实时同步更新最新兑换码，请持续关注
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { ApiGameGetExchangeCode } from '@/api/views/game.js';

export default {
  name: 'GameExchangeCode',
  data() {
    return {
      exchange_text: '',
      game_info: {},
      exchang_list: [],
      bg: '',
    };
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      const res = await ApiGameGetExchangeCode({
        game_id: this.$route.params.game_id,
      });
      this.exchange_text = res.data.info.info;
      this.game_info = res.data.info.game_info;
      this.exchang_list = res.data.info.data;
      this.bg = res.data.info.bg_img;
      document.title = res.data.info.title;
    },
    copy(text) {
      this.$copyText(text)
        .then(() => {
          this.$toast('复制成功');
        })
        .catch(() => {
          this.$toast('复制失败，请手动复制');
        });
    },
  },
};
</script>
<style lang="less" scoped>
.game-exchange-code {
  position: relative;
  overflow: hidden;
  .bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    display: block;
    width: 375 * @rem;
    height: auto;
  }
}
main {
  background: #fff;
  border-radius: 15 * @rem 15 * @rem 0 0;
  margin-top: 248 * @rem;
  overflow: hidden;
  .game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 28 * @rem 25 * @rem 0;
    .game-left {
      width: 64 * @rem;
      height: 64 * @rem;
      border-radius: 10 * @rem;
    }
    .game-center {
      flex: 1;
      margin-left: 10 * @rem;
      margin-right: 10 * @rem;
      .game-name {
        font-size: 14 * @rem;
        font-weight: bolder;
      }
      .tag-list {
        display: flex;
        margin-top: 10 * @rem;
        .tag-item {
          margin-right: 8 * @rem;
          border-radius: 4 * @rem;
          background: #f5f5f6;
          color: #808080;
          font-size: 10 * @rem;
          padding: 2 * @rem 4 * @rem;
        }
      }
    }
    .game-right {
      .hot-icon {
        width: 34 * @rem;
        height: 34 * @rem;
        .image-bg('~@/assets/images/game-exchange-code/hot_icon.png');
      }
      .hot-text {
        margin-top: 3 * @rem;
        text-align: center;
      }
    }
  }
}
.explain {
  background: #f8f8f8;
  border-radius: 19 * @rem;
  color: #333333;
  line-height: 15 * @rem;
  padding: 11 * @rem 24 * @rem;
  margin: 17 * @rem 25 * @rem 0;
}
.big-title {
  font-size: 16 * @rem;
  font-weight: 600;
  color: #333333;
  line-height: 20 * @rem;
  margin: 20 * @rem auto 13 * @rem;
  text-align: center;
}
.exchange-list {
  .exchange-item {
    width: 323 * @rem;
    height: 89 * @rem;
    margin: 0 auto 20 * @rem;
    .image-bg('~@/assets/images/game-exchange-code/exchange_bg.png');
    display: flex;
    align-items: center;
    &.empty {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.4);
      }
      .exchange-right {
        .button {
          background: rgba(138, 132, 127, 1);
        }
      }
    }
    .exchange-left {
      flex: 0 0 211 * @rem;
      padding: 0 13 * @rem;
      .exchange-text {
        height: 45 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        color: #555555;
      }
      .exchange-code {
        font-size: 11 * @rem;
        color: #b88250;
      }
    }
    .exchange-right {
      flex: 1;
      display: flex;
      justify-content: center;
      .button {
        width: 64 * @rem;
        height: 28 * @rem;
        background: linear-gradient(75deg, #ff7a00 0%, #ffb03a 100%);
        border-radius: 29 * @rem;
        color: #fff;
        font-size: 13 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.explain2 {
  margin: 0 25 * @rem 50 * @rem;
  .explain-title {
    font-size: 16 * @rem;
    font-weight: 600;
    color: #616161;
    line-height: 20 * @rem;
  }
  .explain-text {
    margin-top: 7 * @rem;
    color: #858585;
    line-height: 15 * @rem;
  }
}
</style>
