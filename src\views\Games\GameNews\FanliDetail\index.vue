<template>
  <div class="iframe">
    <nav-bar-2 :title="title" :border="true" ref="topNavBar"></nav-bar-2>
    <iframe id="main" :src="url" frameborder="0"></iframe>
    <div class="kefu-btn" @click="handleKefu" v-if="showKefu"></div>

    <div
      class="go-to-apply"
      @click="clickNewsBtn(info)"
      v-if="info && showApply"
    >
      {{ info.apply_type.type_str }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'Iframe',
  data() {
    return {
      title: '',
      url: '',
      info: null,
      showKefu: 0,
      showApply: false,
    };
  },
  created() {
    this.title = this.$route.params.title;
    this.url = this.$route.params.url;
    this.info = this.$route.params.info;
    this.showKefu = this.$route.params.showKefu ?? 0;
    if (this.info.apply_type.type == 4) {
      console.log(this.info);
      this.showApply = true;
    }
    this.$toast.loading({
      message: this.$t('加载中...'),
    });
  },
  mounted() {
    let iframe = document.querySelector('#main');
    iframe.onload = () => {
      this.$toast.clear();
    };
    // window.addEventListener(
    //   "message",
    //   (e) => {
    //     this.handleMessage(e);
    //   },
    //   false
    // );
    // // this.postMessage();
    // let iframe = document.getElementById("main");
    // iframe.onload = function () {
    // };
  },
  methods: {
    handleKefu() {
      this.openKefu({
        from: 'fanli',
        gameName: '',
        activityName: this.title,
      });
    },

    async clickNewsBtn(info) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        setTimeout(() => {
          this.toPage('PhoneLogin');
        }, 500);
        return;
      }
      if (info.apply_type.type == 4) {
        // 申请返利
        this.toPage(
          'FanliApply',
          {
            id: info.id,
            game_id: info.game_id,
            info: info,
          },
          2,
        );
      } else if (info.apply_type.type == 3) {
        // 联系客服
        this.openKefu({
          from: 'fanli',
          gameName: info.titlegame,
          activityName: info.title,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.iframe {
  height: 100vh;
  overflow: hidden;
  iframe {
    width: 100%;
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    // height: 100vh;
  }
}
.kefu-btn {
  position: fixed;
  bottom: 300 * @rem;
  right: 10 * @rem;
  width: 60 * @rem;
  height: 60 * @rem;
  background: #fff;
  z-index: 999;
  background: url('~@/assets/images/kefu-fixed.png') center center no-repeat;
  background-size: 60 * @rem 60 * @rem;
}
.go-to-apply {
  position: fixed;
  bottom: 20 * @rem;
  left: 50%;
  transform: translateX(-50%);
  width: 255 * @rem;
  height: 49 * @rem;
  background: #fff;
  z-index: 999;
  background: linear-gradient(222deg, #6ddc8c 0%, #21b98a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16 * @rem;
  color: #ffffff;
  font-weight: 500;
  border-radius: 25 * @rem;
  box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.2);
}
</style>
