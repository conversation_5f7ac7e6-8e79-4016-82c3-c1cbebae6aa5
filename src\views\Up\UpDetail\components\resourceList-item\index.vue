<template>
  <div class="game-item">
    <div class="container">
      <div class="left" @click="toDetail">
        <img :src="gameInfo.titlepic" class="game-img" />
        <div class="info">
          <div class="top">{{ gameInfo.title }}</div>
          <div class="center">
            <div class="version-info">
              <span>版本：</span>
              <span>{{ gameInfo.version }}</span>
            </div>
          </div>
          <div class="bottom">
            <div class="text">更新时间：</div>
            <div class="text" v-if="gameInfo.truetime">{{
              formatDate(gameInfo.truetime)
            }}</div>
          </div>
        </div>
      </div>
      <div
        class="right"
        :class="{ loading: downloadLoading1[gameInfo.id] }"
        @click="downloadBtn(gameInfo.down_a, gameInfo.id)"
        >下载</div
      >
    </div>
  </div>
</template>
<script>
import { BOX_goToGame } from '@/utils/box.uni.js';

export default {
  props: {
    gameInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      interstitial_ad_id: 0, //百度广告id
      downloadLoading1: {}, //下载当前loading id
    };
  },
  methods: {
    downloadBtn(link, id) {
      if (this.downloadLoading1[id]) {
        return;
      }
      if (this.interstitial_ad_id) {
        (window.slotbydup = window.slotbydup || []).push({
          id: this.interstitial_ad_id,
          container: 'up-detail',
          async: true,
        });
      }
      if (!link) {
        this.$toast('暂无下载噢~');
        return;
      }
      // loading动画
      this.$set(this.downloadLoading1, id, true);
      setTimeout(() => {
        this.$set(this.downloadLoading1, id, false);
      }, 2000);
      if (!link) return;
      window.location.href = link;
    },
    toDetail() {
      console.log(this.gameInfo.classid);
      if (this.gameInfo.classid == 41) {
        BOX_goToGame(
          {
            params: {
              id: this.gameInfo.id,
              gameInfo: this.gameInfo,
            },
          },
          { id: this.gameInfo.id },
        );
      } else {
        this.toPage('UpDetail', {
          id: this.gameInfo.id,
          gameInfo: this.gameInfo,
        });
      }
    },
    formatDate(val) {
      let { year, date, time } = this.$handleTimestamp(val);
      return `${year}-${date} ${time}`;
    },
  },
};
</script>
<style lang="less" scoped>
.game-item {
  height: 64 * @rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  .container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .game-img {
        width: 64 * @rem;
        height: 64 * @rem;
        flex: 0 0 64 * @rem;
        border-radius: 10 * @rem;
      }
      .info {
        flex: 1;
        height: 64 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: 0 10 * @rem;
        overflow: hidden;
        width: 172 * @rem;
        .top {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14 * @rem;
          font-family:
            PingFang SC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: #333333;
        }
        .center {
          display: flex;
          align-items: center;
          flex: 1;
          overflow: hidden;
          .text {
            white-space: nowrap;
            font-size: 10 * @rem;
            color: #999999;
            margin-right: 5 * @rem;
          }
        }
        .bottom {
          display: flex;
          overflow: hidden;
          .text {
            font-size: 11 * @rem;
            color: #444444;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            &:nth-of-type(n + 2) {
              margin-left: 5 * @rem;
            }
          }
        }
      }
    }

    .right {
      flex: 0 0 58 * @rem;
      width: 58 * @rem;
      height: 28 * @rem;
      background: @themeBg;
      border-radius: 25 * @rem 25 * @rem 25 * @rem 25 * @rem;
      font-size: 13 * @rem;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      &.loading {
        position: relative;
        font-size: 0;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          background-size: 16 * @rem 16 * @rem;
          background-image: url(~@/assets/images/downloadLoading.png);
          animation: rotate 1s infinite linear;
        }
      }
    }
  }
}
</style>
