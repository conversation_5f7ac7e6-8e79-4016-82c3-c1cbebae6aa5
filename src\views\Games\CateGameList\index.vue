<template>
  <div class="page">
    <nav-bar-2
      bgStyle="white"
      :placeholder="true"
      :azShow="true"
      :title="title"
    >
      <template #right>
        <div class="search-icon" @click="toPage('Search')"> </div>
      </template>
    </nav-bar-2>
    <div class="main">
      <template>
        <div
          class="cate-container"
          v-if="$route.params.cate_id == 0 && cateList.length"
        >
          <div class="cate-list">
            <div
              class="cate-item"
              :class="{ active: cate_id == item.id }"
              @click="clickCate(item)"
              v-for="item in cateList"
              :key="item.id"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div
          class="cate-game-container"
          :class="{ cate: $route.params.cate_id == 0 && cateList.length }"
        >
          <div
            class="nav-container"
            :class="{ cate: $route.params.cate_id == 0 && cateList.length }"
          >
            <div class="sort-bar">
              <div
                class="sort-item"
                v-for="item in sortList"
                :key="item.id"
                :class="{ on: sortId == item.id }"
                @click="clickSort(item)"
              >
                {{ item.title }}
              </div>
            </div>
            <div class="size-bar">
              <van-popover
                v-model="showPopover"
                trigger="click"
                placement="bottom-end"
                :actions="popoverActions"
                @select="onPopoverSelect"
              >
                <template #reference>
                  <div class="size-show"
                    >{{ !sizeId ? '全部大小' : sizeList[sizeId].title }}
                    <div class="arrow" :class="{ on: showPopover }"></div
                  ></div>
                </template>
              </van-popover>
            </div>
          </div>
          <van-loading class="loading-box" v-if="pageLoading">{{
            $t('加载中...')
          }}</van-loading>
          <yy-list
            v-else
            class="yy-list"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :check="false"
            :empty="empty"
          >
            <div class="game-list">
              <div class="game-item" v-for="item in gameList" :key="item.id">
                <game-item-4 :gameInfo="item" :showHot="true"></game-item-4>
                <yy-download-btn
                  v-if="!($route.params.cate_id == 0 && cateList.length)"
                  :gameInfo="item"
                  @click="goToGame(item)"
                ></yy-download-btn>
              </div>
            </div>
          </yy-list>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {
  ApiV2024CateGetCateList,
  ApiV2024CateGetGameList,
} from '@/api/views/game.js';
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'filterGameList',
  data() {
    return {
      basis_id: 0,
      cate_id: 0,

      title: '分类',
      defaultTitle: '全部分类',

      pageLoading: true,

      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,

      cateList: [],
      sortList: [],
      sizeList: [],
      sortId: 2,
      sizeId: 0,

      showPopover: false,
    };
  },
  computed: {
    popoverActions() {
      return this.sizeList.map(item => {
        return {
          text: item.title,
          id: item.id,
        };
      });
    },
    ids() {
      return [this.basis_id, this.cate_id].join(',');
    },
  },
  async created() {
    this.pageLoading = true;
    this.cate_id = this.$route.params.cate_id;
    this.basis_id = this.$route.params.basis_id;
    this.title =
      this.$route.params.cate_id == 0
        ? this.defaultTitle
        : this.$route.params.title;
    try {
      await this.getCateList();
      await this.getGameList();
    } finally {
      this.pageLoading = false;
    }
  },
  activated() {
    this.$sensorsPageSet(`分类-${this.title}`);
  },
  methods: {
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiV2024CateGetGameList({
        ids: this.ids,
        page: this.page,
        listRows: this.listRows,
        size: this.sizeId,
        sort: this.sortId,
      });
      const list = res.data.list;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async getCateList() {
      const res = await ApiV2024CateGetCateList({
        cate_id: this.cate_id,
        basis_id: this.basis_id,
      });
      this.cateList = [
        {
          id: 0,
          title: '全部',
          big_id: 0,
        },
        ...res.data.list,
      ];
      this.sortList = res.data.sort_list;
      this.sizeList = res.data.size_list;
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }
      this.loadingObj.loading = false;
    },

    async onPopoverSelect(e) {
      if (this.pageLoading) {
        return;
      }
      this.sizeId = e.id;
      this.pageLoading = true;
      await this.getGameList();
      this.pageLoading = false;
    },
    async clickSort(item) {
      if (this.pageLoading) {
        return;
      }
      this.sortId = item.id;
      this.pageLoading = true;
      await this.getGameList();
      this.pageLoading = false;
    },
    async clickCate(item) {
      if (this.pageLoading) {
        return;
      }
      this.cate_id = item.id;
      this.title = item.id == 0 ? this.defaultTitle : item.title;
    this.$sensorsPageSet(`分类-${this.title}`);
      this.pageLoading = true;
      await this.getGameList();
      this.pageLoading = false;
    },
    goToGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  .search-icon {
    width: 28 * @rem;
    height: 28 * @rem;
    background: url('~@/assets/images/games/search-icon.png') center center
      no-repeat;
    background-size: 28 * @rem 28 * @rem;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .main {
    flex: 1;
    .cate-container {
      position: fixed;
      width: 80 * @rem;
      background: #fafafa;
      height: 100%;
      top: calc(50 * @rem + @safeAreaTop);
      top: calc(50 * @rem + @safeAreaTopEnv);
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      &::scrollbar {
        display: none;
      }
      .cate-list {
        width: 100%;
        padding-bottom: 0 * @rem;
        .cate-item {
          position: relative;
          height: 48 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #111111;
          font-size: 14 * @rem;
          padding: 0 5 * @rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &.active {
            background: #fff;
            color: #32b768;
            font-weight: bold;
            &::before {
              content: '';
              width: 4 * @rem;
              height: 14 * @rem;
              border-radius: 2 * @rem;
              background: #32b768;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }
      }
    }
    .cate-game-container {
      height: 100%;
      position: relative;
      margin-left: 0 * @rem;
      &.cate {
        margin-left: 80 * @rem;
      }
    }
    .nav-container {
      box-sizing: border-box;
      width: 100%;
      position: fixed;
      z-index: 2000;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 18 * @rem;
      height: 40 * @rem;
      background: #ffffff;
      max-width: 450px;
      &.cate {
        width: calc(100% - 80 * @rem);
        max-width: calc(450px - 80 * @rem);
      }
      .sort-bar {
        display: flex;
        align-items: center;
        background: #f6f6f6;
        height: 26 * @rem;
        padding: 0 2 * @rem;
        border-radius: 5 * @rem;
        .sort-item {
          height: 22 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 8 * @rem;
          font-size: 12 * @rem;
          color: #979797;
          &.on {
            font-weight: bold;
            color: #333333;
            background: #ffffff;
            border-radius: 4 * @rem;
          }
        }
      }
      .size-bar {
        position: relative;
        .size-show {
          font-size: 12 * @rem;
          color: #333333;
          padding: 0 8 * @rem;
          background: #f6f6f6;
          height: 26 * @rem;
          border-radius: 5 * @rem;
          display: flex;
          align-items: center;
          font-weight: bold;
          .arrow {
            width: 8 * @rem;
            height: 26 * @rem;
            margin-left: 5 * @rem;
            background: url('~@/assets/images/arrow-to-top.png') center center
              no-repeat;
            background-size: 8 * @rem 5 * @rem;
            transform: rotateX(180deg);
            transition: 0.2s;
            &.on {
              transform: rotateX(0deg);
            }
          }
        }
      }
    }
    .yy-list {
      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 40 * @rem;
        background: #ffffff;
      }
      .game-list {
        padding: 0 18 * @rem;
        .game-item {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
