<template>
  <div class="collection-page">
    <nav-bar-2 :border="true" :title="$t('我的收藏')">
      <template #right>
        <div class="operate-btn btn" v-if="!isEditing" @click="clickEdit">
          {{ $t('编辑') }}
        </div>
        <div class="operate-btn btn" v-else @click="handleEdit">
          {{ $t('完成') }}
        </div>
      </template>
    </nav-bar-2>
    <main>
      <van-tabs
        v-model="active"
        :title-active-color="themeColorLess"
        title-inactive-color="#666666"
        :border="true"
        line-width="50%"
        :color="themeColorLess"
        line-height="2px"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :title="item.title"
          :to="{ name: item.name }"
          :replace="true"
          :name="item.name"
        >
        </van-tab>
      </van-tabs>
      <keep-alive
        ><router-view ref="collectionComponent"></router-view
      ></keep-alive>
    </main>
  </div>
</template>

<script>
import { themeColorLess } from '@/common/styles/_variable.less';
export default {
  name: 'Collection',
  data() {
    return {
      themeColorLess,
      active: 0,
      tabList: [
        {
          name: 'GameCollection',
          title: this.$t('游戏'),
        },
        {
          name: 'XiaohaoCollection',
          title: this.$t('交易角色'),
        },
      ],
      isEditing: false,
    };
  },
  activated() {
    this.getActive();
  },
  methods: {
    clickEdit() {
      this.isEditing = true;
      this.$refs['collectionComponent'].clickEdit(this.isEditing);
    },
    handleEdit() {
      this.isEditing = false;
      this.$refs['collectionComponent'].handleEdit(this.isEditing);
    },
    getActive() {
      let index = this.tabList.findIndex(item => {
        return item.name == this.$route.name;
      });
      this.active = this.tabList[index].name;
    },
  },
};
</script>

<style lang="less" scoped>
.collection-page {
  .operate-btn {
    color: #000;
    font-size: 14 * @rem;
  }
}
main {
  height: calc(100vh - 50 * @rem - @safeAreaTop);
  height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
  width: 100%;
  display: flex;
  flex-direction: column;
  /deep/ .van-tabs--line .van-tabs__wrap {
    width: 100%;
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    left: 50%;
    transform: translateX(-50%);
    max-width: @maxWidth;
    background-color: #fff;
    z-index: 1000;
  }
  /deep/ .van-tab {
    font-size: 15px;
  }
}
</style>
