<template>
  <div class="page exchange-page">
    <nav-bar-2
      :border="false"
      :title="info.title"
      :azShow="true"
      :placeholder="true"
    >
    </nav-bar-2>
    <div class="main">
      <div class="big-pic">
        <img :src="info.pic" alt="" v-if="info.pic" />
        <div class="gray-bg" v-else></div>
      </div>
      <div class="info">
        <div class="exchange-price">
          <div class="left">
            <span>{{ userInfo.is_svip ? info.svip_price : info.price }}</span>
            <em>金币</em>
          </div>
          <div class="subscript" v-if="userInfo.is_svip">SVIP尊享价</div>
        </div>
        <div class="svip-discount" v-if="!userInfo.is_svip">
          <div class="svip-text">开通SVIP尽享优选服务</div>
          <div class="svip-btn btn" @click="toPage('Svip')">开通会员</div>
        </div>

        <div class="info-title">{{ info.title }}</div>
        <div class="exchange-requirement">
          <div class="section-title">兑换条件</div>
          <div class="require-list">
            <div class="require-item">
              <div class="require-title">兑换限制</div>
              <div class="require-content">{{ info.restrictions_desc }}</div>
            </div>
            <div class="require-item">
              <div class="require-title">兑换次数</div>
              <div class="require-content">{{ info.limit_desc }}</div>
            </div>
          </div>
        </div>
        <div class="item-description" v-if="info.details">
          <div class="section-title">商品详情</div>
          <div class="description-content" v-html="info.details"></div>
        </div>
        <div class="bottom-bar">
          <div
            class="exchange-btn btn"
            v-if="info.status == 1"
            @click="handleExchange"
            >兑换</div
          >
          <div
            class="exchange-btn gold-not-enough btn"
            v-else-if="info.status == 3"
            @click="cantExchange"
            >金币不足</div
          >
          <div class="exchange-btn none btn" v-else>已抢完</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  ApiCoinCenterGetExchangeGoodsDetail,
  ApiCoinCenterExchange,
} from '@/api/views/gold.js';

export default {
  name: 'ExchangeDetail',
  data() {
    return {
      id: 0,
      info: {},
      popupShow: false,
      actType: '',
      startTime: '',
    };
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getExchangeInfo();
  },
  methods: {
    async getExchangeInfo() {
      const res = await ApiCoinCenterGetExchangeGoodsDetail({ id: this.id });
      this.info = { ...res.data.good, status: res.data.status };
      this.actType = res.data.type;
      this.startTime = res.data.start_time;
    },
    cantExchange() {
      this.$toast('金币不足');
    },
    handleExchange() {
      if (this.info.restrictions == 1 && !this.userInfo.is_svip) {
        this.$dialog
          .confirm({
            title: '你不满足兑换条件',
            message: '开通SVIP即可兑换商品，还可享受SVIP专属兑换价',
            confirmButtonText: '开通SVIP',
            className: 'svip-dialog', // 弹窗的class
          })
          .then(async () => {
            this.toPage('Svip');
          })
          .catch(e => {
            console.log(e);
          });
        return false;
      }
      if (this.actType == 2 && this.startTime > Date.parse(new Date()) / 1000) {
        this.$toast('兑换还未开始');
        return false;
      }
      this.$dialog
        .confirm({
          title: '确认兑换',
          message: '商品一经兑换，无法退回<br>是否确认兑换？',
          confirmButtonText: '确定',
        })
        .then(async () => {
          this.$toast.loading();
          await ApiCoinCenterExchange({ id: this.id });
          await this.getExchangeInfo();
        })
        .catch(e => {
          console.log(e);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.exchange-page {
  .main {
    padding-bottom: 74 * @rem;
  }
  .big-pic {
    width: 100%;
    position: relative;
    background: #fff;

    .gray-bg {
      width: 100%;
      height: 240 * @rem;
    }

    &::after {
      content: '';
      display: block;
      width: 100%;
      height: 64 * @rem;
      background: linear-gradient(
        360deg,
        rgba(0, 0, 0, 0.06) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .info {
    padding: 18 * @rem 18 * @rem 0;
    border-radius: 16 * @rem 16 * @rem 0 0;
    margin-top: -13 * @rem;
    position: relative;
    z-index: 1;
    background-color: #fff;

    .exchange-price {
      display: flex;
      align-items: center;
      height: 50 * @rem;
      background-color: #fff8e3;
      border-radius: 12 * @rem;
      padding: 0 12 * @rem;
      position: relative;
      z-index: 1;

      .left {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        span {
          font-size: 28 * @rem;
          line-height: 30 * @rem;
          color: #ff6649;
          font-weight: bolder;
        }

        em {
          display: block;
          font-size: 14 * @rem;
          line-height: 14 * @rem;
          color: #ff6649;
          margin-top: 7 * @rem;
          margin-left: 2 * @rem;
        }
      }

      .subscript {
        flex-shrink: 0;
        height: 14 * @rem;
        font-weight: 600;
        font-size: 14 * @rem;
        color: #e1af5d;
        line-height: 14 * @rem;
        margin-left: 4 * @rem;
      }
    }

    .svip-discount {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 37 * @rem;
      margin-top: -10 * @rem;
      padding: 10 * @rem 12 * @rem 0;
      background-color: #ffe6c0;
      border-radius: 0 0 12 * @rem 12 * @rem;

      .svip-text {
        flex: 1;
        min-width: 0;
        height: 13 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 13 * @rem;
        color: #663d00;
        line-height: 13 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .svip-btn {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 61 * @rem;
        height: 23 * @rem;
        border-radius: 16 * @rem;
        border: 1 * @rem solid #663d00;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 600;
        font-size: 11 * @rem;
        color: #663d00;
        line-height: 11 * @rem;
        text-align: center;
      }
    }
  }
  .info-title {
    font-weight: bold;
    font-size: 18 * @rem;
    color: #333333;
    line-height: 25 * @rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-top: 16 * @rem;
  }
  .section-title {
    color: #333;
    line-height: 23 * @rem;
    font-size: 16 * @rem;
    font-weight: bold;
  }
  .exchange-requirement {
    padding: 14 * @rem 16 * @rem 14 * @rem 12 * @rem;
    background-color: #fafafa;
    border-radius: 8 * @rem;
    margin-top: 16 * @rem;

    .require-list {
      .require-item {
        display: flex;
        margin-top: 14 * @rem;

        &:first-of-type {
          margin-top: 10 * @rem;
        }

        .require-title {
          width: 67 * @rem;
          flex-shrink: 0;
          margin-right: 10 * @rem;
          font-size: 13 * @rem;
          line-height: 18 * @rem;
          color: #333;
        }
        .require-content {
          flex: 1;
          min-width: 0;
          color: #979797;
          font-size: 13 * @rem;
          line-height: 18 * @rem;
        }
      }
    }
  }
  .item-description {
    margin-top: 25 * @rem;

    .section-title {
      font-size: 18 * @rem;
      line-height: 25 * @rem;
    }

    .description-content {
      margin-top: 12 * @rem;
      font-size: 14 * @rem;
      color: #979797;
      line-height: 24 * @rem;
      text-align: justify;
    }
  }
  .bottom-bar {
    width: 375 * @rem;
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    box-shadow: 0 -10 * @rem 10 * @rem rgba(0, 0, 0, 0.035);
    z-index: 9;

    .exchange-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 14 * @rem 28 * @rem;
      height: 38 * @rem;
      text-align: center;
      font-weight: 600;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 15 * @rem;
      border-radius: 20 * @rem;
      background: @themeBg;

      &.none {
        background: #cccccc;
      }
      &.gold-not-enough {
        background: #ecfbf4;
        color: @themeColor;
      }
    }
  }
  .exchange-popup {
    width: 300 * @rem;

    .exchange-popup-title {
      padding: 20 * @rem 15 * @rem;
      font-size: 18 * @rem;
      text-align: center;
      font-weight: bold;
      color: #000;
    }

    .exchange-popup-content {
      padding: 0 15 * @rem;
      color: #000;
      font-size: 16 * @rem;
      line-height: 20 * @rem;
      text-align: center;
      margin-top: 10 * @rem;
    }

    .btns {
      display: flex;
      margin-top: 30 * @rem;

      .cancel-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50%;
        height: 60 * @rem;
        text-align: center;
        color: #999;
        font-size: 16 * @rem;
      }
      .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50%;
        height: 60 * @rem;
        text-align: center;
        color: #000;
        font-weight: bold;
        font-size: 16 * @rem;
      }
    }
  }
}
</style>
