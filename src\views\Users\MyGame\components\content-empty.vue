<template>
  <div class="empty">
    <van-empty :description="tips" class="van-empty" :image="emptyImg">
      <div class="retry" @click="retry" v-if="showRetry">
        {{ $t('点击重试') }}
      </div>
    </van-empty>
  </div>
</template>

<script>
import emptyImg from '@/assets/images/game-list-empty.png';
export default {
  name: 'ContentEmpty',
  props: {
    tips: {
      type: String,
      default: '暂无数据',
    },
    showRetry: {
      type: Boolean,
      default: false,
    },
    emptyImg: {
      type: String,
      default: emptyImg,
    },
  },
  methods: {
    retry() {
      this.$emit('retry');
    },
  },
};
</script>

<style lang="less" scoped>
.empty {
  display: flex;
  justify-content: center;
  margin: 64 * @rem 0;
  .van-empty {
    align-self: center;
    padding: 0;
    /deep/ .van-empty__description {
      margin-top: 8 * @rem;
      color: #9B9B9B;
    }
    /deep/ .van-empty__image {
      width: 128 * @rem;
      height: 128 * @rem;
      img {
        object-fit: contain;
      }
    }
  }
  .retry {
    font-size: 14 * @rem;
    text-decoration: underline;
    color: @themeColor;
  }
}
</style>
