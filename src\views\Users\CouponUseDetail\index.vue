<template>
  <div class="page platform-coin-detail-page">
    <nav-bar-2
      :border="true"
      :title="$t('使用明细')"
      :azShow="true"
    ></nav-bar-2>
    <div class="main">
      <yy-list
        v-model="loadingObj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="empty"
      >
        <div class="record-list">
          <div
            class="record-item"
            v-for="(item, index) in couponUseDetailList"
            :key="index"
          >
            <div class="content">
              <div class="left">
                <div class="title">{{ item.pay_title }}</div>
                <div class="date">{{ item.create_time_text }}</div>
              </div>
              <div class="coin">{{ item.amount_text }}</div>
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </div>
</template>
<script>
import { ApiGetSplitCouponLog } from '@/api/views/coupon.js';
export default {
  name: 'PlatformCoinDetail',
  data() {
    return {
      id: 1,
      finished: false,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      page: 1,
      listRows: 10,
      couponUseDetailList: [],
      empty: false,
    };
  },
  created() {
    this.id = this.$route.params.id || 1;
  },
  methods: {
    async getList() {
      const res = await ApiGetSplitCouponLog({
        page: this.page,
        listRows: this.listRows,
        coupon_record_id: this.id,
      });
      if (this.page === 1) this.couponUseDetailList = [];
      this.couponUseDetailList.push(...res.data.list);
      if (this.couponUseDetailList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.page = 1;
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getList();
      this.loadingObj.loading = false;
      this.page++;
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-detail-page {
  .main {
    display: flex;
    flex-direction: column;
    flex: 1;

    .record-list {
      .record-item {
        border-bottom: 1 * @rem solid #f3f5f9;
        padding: 20 * @rem 18 * @rem;

        .content {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left {
            flex: 1;
            min-width: 0;
            .title {
              height: 21 * @rem;
              font-weight: 600;
              color: #222222;
              line-height: 21 * @rem;
              font-size: 15 * @rem;
              color: #333333;
            }

            .date {
              height: 17 * @rem;
              line-height: 17 * @rem;
              font-size: 12 * @rem;
              font-weight: 400;
              color: #999999;
              margin-top: 12 * @rem;
            }
          }

          .coin {
            flex-shrink: 0;
            height: 21 * @rem;
            line-height: 21 * @rem;
            font-size: 15 * @rem;
            font-weight: 500;
            color: @themeColor;
          }
        }
      }
    }
  }
}
</style>
