<template>
  <div class="not-found-page">
    <div class="page-box">
      <div class="not-found-image">
        <img :src="notFoundImage" alt="" />
      </div>
      <div class="not-found-text">{{ notFoundText }}</div>
    </div>
  </div>
</template>

<script>
import defaultNotFoundIcon from '@/assets/images/default-not-found-icon.png';
export default {
  name: 'defaultNotFoundPage',
  props: {
    notFoundImage: {
      type: String,
      default: defaultNotFoundIcon,
    },
    notFoundText: {
      type: String,
      default() {
        return this.$t('暂无搜索结果');
      },
    },
  },
};
</script>

<style lang="less" scoped>
.not-found-page {
  height: 100%;
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  .page-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .not-found-image {
      width: 128 * @rem;
      height: 128 * @rem;
    }
    .not-found-text {
      margin-top: 16 * @rem;
      font-weight: 400;
      font-size: 14 * @rem;
      color: #9b9b9b;
    }
  }
}
</style>
