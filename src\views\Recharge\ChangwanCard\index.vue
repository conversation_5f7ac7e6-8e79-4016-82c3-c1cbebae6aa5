<template>
  <div class="changwan-page">
    <nav-bar-2
      :title="$t('省钱畅玩卡')"
      :placeholder="false"
      bgStyle="transparent"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
    ></nav-bar-2>
    <div class="main">
      <div class="top-bar">
        <div class="user-bar" v-if="memberStatus.user_info">
          <div class="user-info">
            <user-avatar
              class="avatar"
              :src="memberStatus.user_info.avatar"
            ></user-avatar>
            <div class="info-right">
              <div class="nickname">{{ memberStatus.user_info.nickname }}</div>
              <div class="no-open-text" v-if="!(memberStatus.is_member != 0)">
                {{ $t('暂未开通畅玩卡，无法享受会员权益') }}
              </div>
              <div class="end-date" v-else>
                {{ $t('有效期至') }}：{{ overdue }}
              </div>
            </div>
          </div>
          <div class="no-open-tag" v-if="!(memberStatus.is_member != 0)"></div>
        </div>
      </div>

      <div class="container changwan-container">
        <div class="changwan-list-title">
          {{ $t('畅玩卡套餐') }}
          <span
            class="count-down"
            v-if="cardList.length && cardList[0].end_time != 0"
          >
            {{ $t('活动截止时间') }}：
            {{ cardList[0].end_time | formatTime }}</span
          >
        </div>
        <div class="card-list">
          <template v-for="(card, index) in cardList">
            <div class="card-item" :key="index">
              <img class="card-banner" :src="card.icon" alt="" />
              <div class="total" v-if="card.amount_desc">
                <span>{{ card.amount_desc.price }}</span
                >元x<span>{{ card.amount_desc.day }}</span
                >{{ $t('张') }}=<span>{{ card.amount_desc.total_price }}</span
                >元
              </div>
              <div class="every" v-if="card.amount_desc">
                {{ card.amount_desc.unit_price }}
              </div>
              <div class="open btn" @click="clickCard(card)">
                <div class="amount-container">
                  <div class="amount">
                    ¥ <span>{{ card.amount }}</span>
                  </div>
                  <div class="old-amount" v-if="card.old_amount > 0">
                    {{ $t('原价') }}¥{{ card.old_amount }}
                  </div>
                </div>
                <div class="discount" v-if="card.scale">
                  {{ $t('限时') }}{{ card.scale }}{{ $t('折') }}
                </div>
                <div class="open-now">{{ $t('立即开通') }}</div>
              </div>
            </div>
          </template>
        </div>
        <div class="search-more btn" @click="openGameDialog">
          <div class="search-text">{{ $t('查询可使用畅玩卡的游戏') }}</div>
          <div class="right-icon-light"></div>
        </div>
      </div>

      <div class="container introduction-container">
        <div class="title">{{ $t('购卡说明') }}</div>
        <div class="content">
          <p>
            1.开通畅玩卡后，立即可获得一张满6减6代金券。后续每天可领取一张代金券。
            <span>{{
              $t(
                '折扣游戏使用代金券规则：(原价-代金券金额)*折扣比例，如：八折游戏，原价100元，那么计算公式为(100-6)*0.8=75.2。',
              )
            }}</span>
          </p>
          <p>
            {{
              $t(
                '2.畅玩卡支持重复购买，代金券发放时间会自动累加（当前最大叠加时长为365天）。已持有的周卡的用户再购买月卡会自动升级为月卡用户。',
              )
            }}
          </p>
          <p>
            3.领取方式：每日点击我的-代金券，可自动触发领取。若当天忘记点击则您将错过领取，无法补发。
          </p>
          <p>
            {{ $t('4.畅玩卡所发放的代金券有效期为1天，请于当天及时使用。') }}
          </p>
          <p>
            <span>
              {{
                $t(
                  '5.此为虚拟商品，一经售出概不退换，请您购买前先查询以确保您玩的游戏可以使用。',
                )
              }}
            </span>
          </p>
        </div>
      </div>
      <!-- 查询可用游戏 -->
      <van-dialog
        v-model="gameDialogShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
      >
        <div class="search-container">
          <div class="close-search" @click="gameDialogShow = false"></div>
          <div class="search-bar">
            <div class="input-text">
              <form @submit.prevent="searchGame">
                <input
                  type="text"
                  v-model.trim="inputGame"
                  :placeholder="$t('输入游戏名')"
                />
              </form>
            </div>
            <div class="search-btn" @click="searchGame">{{ $t('搜索') }}</div>
          </div>
          <yy-list
            class="yy-list game-list"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :empty="!gameList.length"
            :tips="$t('请输入您想搜索的游戏')"
            :check="false"
          >
            <div
              class="game-item btn"
              v-for="item in gameList"
              :key="item.id"
              @click="toGame(item)"
            >
              <div class="game-icon">
                <img :src="item.titlepic" alt="" />
              </div>
              <div class="right">
                <div class="game-name">{{ item.title }}</div>
                <div class="use-card" :class="{ can: item.use_coupon == 1 }">
                  {{ item.use_coupon == 1 ? '' : $t('不')
                  }}{{ $t('支持使用畅玩卡') }}
                </div>
              </div>
            </div>
          </yy-list>
        </div>
      </van-dialog>
      <!-- 支付弹窗抽屉 -->
      <van-popup
        v-model="payPopupShow"
        position="bottom"
        :lock-scroll="false"
        round
        class="pay-container-popup"
        :close-on-popstate="true"
      >
        <div class="pay-container">
          <div class="pay-way-title">{{ $t('选择支付方式') }}</div>
          <div class="pay-list">
            <div
              class="pay-item"
              v-for="(item, index) in payList"
              :key="index"
              @click="selectedPayType = item.key"
            >
              <div
                class="icon"
                :style="{ backgroundImage: `url(${item.icon})` }"
              ></div>
              <div class="pay-name">{{ item.name }}</div>
              <div
                class="choose"
                :class="{ on: selectedPayType == item.key }"
              ></div>
            </div>
          </div>
          <div class="pay-btn btn" @click="handlePay">
            {{ $t('确认支付') }}¥{{ selectedCard.amount }}
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script>
import {
  ApiGameCardIndex,
  ApiGetUserCard,
  ApiGameCardSearchGame,
  ApiGameCardCreateOrder,
  ApiGetPayUrl,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
import { handleTimestamp } from '@/utils/datetime.js';
export default {
  name: 'ChangwanCard',
  data() {
    return {
      navbarOpacity: 0,
      memberStatus: {},
      cardList: [],
      gameDialogShow: false,
      payPopupShow: false,
      gameList: [],
      inputGame: '',
      selectedCard: {},
      selectedPayType: 'wx',
      payList: [],
      timer: null,
      countDownObj: {},
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
    };
  },
  computed: {
    overdue() {
      if (!this.memberStatus.remain_days) {
        return '0000-00-00';
      }
      let leftDay = this.memberStatus.remain_days;
      let now = new Date().getTime();
      let overdue = new Date(now + leftDay * 24 * 60 * 60 * 1000);
      let year = overdue.getFullYear();
      let month = overdue.getMonth() + 1;
      let day = overdue.getDate();
      return `${year}-${month}-${day}`;
    },
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  watch: {
    // memberStatus(val, oldVal) {
    //   if (val.remain_days != oldVal.remain_days) {
    //     this.getUserCard();
    //   }
    // },
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
    await this.getUserCard();
    await this.getChangwanList();
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    openGameDialog() {
      this.gameDialogShow = true;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    async getChangwanList() {
      const res = await ApiGameCardIndex();
      this.cardList = res.data.card_list;
      this.payList = res.data.payArr;
      this.selectedPayType = this.payList[0].key;
    },
    handlePay() {
      this.payPopupShow = false;
      ApiGameCardCreateOrder({
        type: this.selectedCard.type,
        payWay: this.selectedPayType,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 104,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 104,
          })
            .then(async res2 => {
              await this.getUserCard();
            })
            .catch(() => {
            });
        });
      });
    },
    clickCard(card) {
      this.selectedCard = card;
      this.payPopupShow = true;
    },
    toGame(item) {
      this.toPage('GameDetail', {
        id: item.id,
      });
    },
    async getUserCard() {
      const res = await ApiGetUserCard();
      this.memberStatus = res.data;
    },
    async searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      this.gameList = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
    toRecharge(id) {
      this.$router.push({
        name: 'ChangwanRecharge',
        params: {
          id: id,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.changwan-page {
  background-color: #ffdfbb;
  padding-bottom: 20 * @rem;
  .top-bar {
    box-sizing: border-box;
    width: 100%;
    height: 310 * @rem;
    .image-bg('~@/assets/images/recharge/changwan_card_bg.png');
    padding-top: 197 * @rem;
    .user-bar {
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      width: 355 * @rem;
      height: 70 * @rem;
      background-color: #fff;
      margin: 0 auto;
      border-radius: 16 * @rem;
      .no-open-tag {
        position: absolute;
        right: -1 * @rem;
        top: -1 * @rem;
        width: 60 * @rem;
        height: 26 * @rem;
        .image-bg('~@/assets/images/recharge/no-open-changwan-tag.png');
      }
      .user-info {
        height: 70 * @rem;
        display: flex;
        align-items: center;
        margin-left: 15 * @rem;
        .avatar {
          box-sizing: border-box;
          width: 40 * @rem;
          height: 40 * @rem;
          overflow: hidden;
          border-radius: 50%;
        }
        .info-right {
          margin-left: 10 * @rem;
          .nickname {
            font-size: 14 * @rem;
            color: #805e46;
            line-height: 18 * @rem;
            font-weight: bold;
          }
          .end-date {
            font-size: 12 * @rem;
            color: #805e46;
            line-height: 15 * @rem;
            font-weight: bold;
            margin-top: 4 * @rem;
          }
          .no-open-text {
            font-size: 12 * @rem;
            color: #d1b099;
            line-height: 15 * @rem;
            margin-top: 4 * @rem;
          }
        }
      }
      .svip-intro {
        height: 24 * @rem;
        display: flex;
        align-items: center;
        font-size: 12 * @rem;
        color: #ffffff;
        font-weight: bold;
        padding: 0 14 * @rem;
        background: rgba(81, 21, 21, 0.2);
        span {
          color: #ffe600;
          font-weight: bold;
        }
      }
    }
  }
  .main {
    .container {
      width: 355 * @rem;
      margin: 10 * @rem auto;
      background: #fff;
      border-radius: 16 * @rem;
      overflow: hidden;
      &.changwan-container {
        margin-top: -33 * @rem;
      }
      .changwan-list-title {
        display: flex;
        align-items: center;
        font-size: 18 * @rem;
        color: #805e46;
        line-height: 23 * @rem;
        font-weight: bold;
        margin-top: 19 * @rem;
        padding: 0 10 * @rem;
        .count-down {
          font-size: 12 * @rem;
          color: #ea4545;
          font-weight: bold;
          margin-left: 10 * @rem;
        }
      }
      .search-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20 * @rem 0;
        .search-text {
          font-size: 16 * @rem;
          color: #805e46;
          line-height: 20 * @rem;
          font-weight: bold;
        }
        .right-icon-light {
          margin-left: 4 * @rem;
          width: 16 * @rem;
          height: 16 * @rem;
          border-radius: 50 * @rem;
          .image-bg('~@/assets/images/recharge/round-right.png');
        }
      }
      .card-list {
        margin-top: 15 * @rem;
        .card-item {
          width: 335 * @rem;
          height: 168 * @rem;
          margin: 0 auto;
          position: relative;
          &:not(:first-of-type) {
            margin-top: 20 * @rem;
          }
          .card-banner {
            width: 335 * @rem;
            height: 168 * @rem;
          }
          .total {
            font-size: 10 * @rem;
            color: #af7f50;
            position: absolute;
            left: 36 * @rem;
            top: 68 * @rem;
            letter-spacing: 5 * @rem;
            span {
              font-size: 20 * @rem;
              color: #7e5935;
              font-weight: bold;
              letter-spacing: 0;
              margin-right: 2 * @rem;
            }
          }
          .every {
            font-size: 13 * @rem;
            line-height: 16 * @rem;
            color: #af7f50;
            position: absolute;
            right: 26 * @rem;
            top: 52 * @rem;
            font-weight: bold;
          }
          .open {
            width: 283 * @rem;
            height: 40 * @rem;
            .image-bg('~@/assets/images/recharge/changwan-open-btn.png');
            border-radius: 25 * @rem;
            position: absolute;
            left: 50%;
            margin-left: -141 * @rem;
            top: 115 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16 * @rem;
            color: #102f10;
            font-weight: 600;
            .amount-container {
              flex: 1;
              min-width: 0;
              display: flex;
              align-items: center;
              padding-left: 20 * @rem;
            }
            .amount {
              font-size: 12 * @rem;
              color: #ffebce;
              font-weight: 600;
              span {
                font-size: 28 * @rem;
                color: #ffebce;
                font-weight: bold;
              }
            }
            .old-amount {
              font-size: 12 * @rem;
              color: #ebb89b;
              text-decoration: line-through;
              margin-left: 12 * @rem;
            }
            .discount {
              box-sizing: border-box;
              position: absolute;
              right: 26 * @rem;
              top: -17 * @rem;
              width: 74 * @rem;
              height: 30 * @rem;
              background: url(~@/assets/images/recharge/discount-decoration.png)
                center center no-repeat;
              background-size: 74 * @rem 30 * @rem;
              font-size: 12 * @rem;
              font-weight: bold;
              line-height: 20 * @rem;
              color: #7b3615;
              text-align: center;
            }
            .open-now {
              width: 100 * @rem;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14 * @rem;
              color: #ffffff;
              font-weight: bold;
            }
          }
        }
      }
    }
    .introduction-container {
      .title {
        font-size: 16 * @rem;
        text-align: center;
        height: 22 * @rem;
        line-height: 22 * @rem;
        font-weight: bold;
        color: #7b3615;
        background: url(~@/assets/images/recharge/changwan-sub-bg.png) center
          center no-repeat;
        background-size: 195 * @rem 3 * @rem;
        margin: 15 * @rem 0 0;
      }
      .content {
        padding: 19 * @rem 15 * @rem 20 * @rem;
        font-size: 13 * @rem;
        color: #805e46;
        line-height: 20 * @rem;
        p {
          margin-bottom: 15 * @rem;
          span {
            color: #ff5934;
          }
        }
      }
    }
    /deep/ .van-dialog {
      overflow: unset;
      width: 320 * @rem;
    }
    .search-container {
      box-sizing: border-box;
      width: 320 * @rem;
      height: 450 * @rem;
      padding: 24 * @rem 19 * @rem 10 * @rem;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: unset;
      .close-search {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url(~@/assets/images/recharge/close-search.png) center
          center no-repeat;
        background-size: 24 * @rem 24 * @rem;
        position: absolute;
        right: -10 * @rem;
        top: -10 * @rem;
      }
      .search-bar {
        display: flex;
        align-items: center;
        .input-text {
          width: 240 * @rem;
          height: 35 * @rem;
          border: 1px solid #e5e5e5;
          border-radius: 18 * @rem;
          flex: 1;
          overflow: hidden;
          form {
            display: block;
            width: 100%;
            height: 100%;
          }
          input {
            box-sizing: border-box;
            display: block;
            width: 100%;
            height: 100%;
            padding: 0 18 * @rem;
            font-size: 15 * @rem;
            color: #333333;
            background-color: #f6f6f6;
          }
        }
        .search-btn {
          font-size: 15 * @rem;
          color: #666666;
          padding-left: 13 * @rem;
          height: 35 * @rem;
          line-height: 35 * @rem;
        }
      }
      .game-list {
        flex: 1;
        overflow: auto;
        margin-top: 10 * @rem;
        .game-item {
          display: flex;
          align-items: center;
          padding: 21 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .game-icon {
            width: 50 * @rem;
            height: 50 * @rem;
            border-radius: 10 * @rem;
            background-color: #b5b5b5;
          }
          .right {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .game-name {
              font-size: 16 * @rem;
              font-weight: bold;
              color: #000000;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .use-card {
              font-size: 12 * @rem;
              color: #f72e2e;
              margin-top: 10 * @rem;
              &.can {
                color: #36b150;
              }
            }
          }
        }
      }
    }
  }
  .pay-container-popup {
    .pay-container {
      padding: 10 * @rem 14 * @rem 0;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .pay-way-title {
        font-size: 16 * @rem;
        color: #333;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pay-list {
        .pay-item {
          display: flex;
          align-items: center;
          padding: 16 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .icon {
            width: 24 * @rem;
            height: 24 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 24 * @rem 24 * @rem;
            // &.wx {
            //   background-image: url(~@/assets/images/recharge/wx-icon.png);
            //   background-size: 24 * @rem 21 * @rem;
            // }
            // &.zfb_dmf {
            //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
            //   background-size: 24 * @rem 24 * @rem;
            // }
          }
          .pay-name {
            font-size: 15 * @rem;
            color: #666666;
            flex: 1;
            min-width: 0;
            margin-left: 8 * @rem;
          }
          .choose {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/recharge/n_radio.png) center center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            &.on {
              background-image: url(~@/assets/images/recharge/c_radio.png);
            }
          }
        }
      }
      .pay-btn {
        width: 290 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9d482;
        font-size: 18 * @rem;
        font-weight: bold;
        color: #7d561c;
        margin: 30 * @rem auto;
        border-radius: 23 * @rem;
        box-shadow: 0px 2px 13px 0px rgba(99, 68, 24, 0.24);
      }
    }
  }
}
</style>
