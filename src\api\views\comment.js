import { request } from '../index';

/**
 * 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论
 */

/**
 * 评论列表
 * @param {classId} 类型id 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论 105=合集评论
 * @param {sourceId} 1555
 * @param {order} 0=默认排序（置顶+热门+最新），1=时间正序，2=时间倒序(最新)，3=热门倒序(最热)，11=智能排序
 * @param {justSum} 1=只返回评论数
 * @param {is_show_playtime} 1显示游戏时长 0不显示游戏时长
 * @param {is_show_model} 1显示机型 0不显示机型
 */
export function ApiCommentComments(params = {}) {
  // return request('/api/comment/comments', params);
  return request('/api/comment/commentsNew', params);
}

/**
 * 我的评论
 * @param {classId} 102
 * @param {page} 1
 * @param {listRows} 2
 */
export function ApiCommentMine(params = {}) {
  return request('/api/comment/mine', params);
}

/**
 * 回复我的
 * @param {page} 1
 * @param {listRows} 10
 */
export function ApiCommentReplyMe(params = {}) {
  return request('/api/comment/replyMe', params);
}

/**
 * 获取评论详情
 * @param {classId} 102=动态评论
 * @param {sourceId} 1555
 * @param {commentId} 4291
 * @param {page} 1
 * @param {listRows} 2
 * @param {justSum} 0
 * @param {order} 0
 */
export function ApiCommentReplies(params = {}) {
  // return request('/api/comment/replies', params);
  return request('/api/comment/repliesNew', params);
}

/**
 * 回复动态
 * @param {classId} 1=资讯，2=动态，3=应用，4=推荐，5=交易小号，6=捡漏小号，101=资讯评论，102=动态评论，103=应用评论, 104=推荐评论 105=合集评论
 * @param {content} 什么内容
 * @param {images}
 * @param {model} 设备型号
 * @param {channel} 'none'
 * @param {sourceId} 动态的id
 * @param {replyCommentId} 回复子评论的id
 * @param {replyOuterId} 回复评论的id
 * @param {rating} 1-5
 * @param {comment_id} 评论ID 修改评论使用
 * @param {is_update} 是否修改 1-修改 0-获取数据
 */
export function ApiCommentSubmit(params = {}) {
  return request('/api/comment/submit', params);
}

/**
 * 消息中心消除小红点 阅读评论
 * @param {type} 1
 *
 * order 0=默认排序（置顶+热门+最新），1=时间正序，2=时间倒序(最新)，3=热门倒序(最热)
 */
export function ApiCommentCommentRead(params = {}) {
  return request('/api/comment/commentRead', params);
}

/**
 * 消息中心小红点状态
 * 
 * # "data": {
#    "feedback_read": false,//反馈通知是否已读： true=有小红点  false=没有小红点
#    "reply_read": false//评论回复是否已读：true=有小红点  false=没有小红点
#  }
 * 
*/
export function ApiCommentCommentReadStatus(params = {}) {
  return request('/api/comment/commentReadStatus', params);
}

/**
 * 获取用户未读消息数量
 */
export function ApiCommentGetUnreadCount(params = {}) {
  return request('/api/comment/getUnreadCount', params);
}

/**
 * 评论 - 一键已读
 */
export function ApiCommentSetCommentRead(params = {}) {
  return request('/api/comment/setCommentRead', params);
}

/**
 * 回复动态
 * @param {classId} 1=资讯，2=动态102=动态评论
 * @param {sourceId} 4288
 */
export function ApiResourceSupport(params = {}) {
  return request('/api/resource/support', params);
}

/**
 * 点击评论 用户是否可以评论
 * @param {classId} 103
 * @param {sourceId} 20016181
 */
export function ApiCommentClickComment(params = {}) {
  return request('/api/comment/clickComment', params);
}

/**
 * 评论 - 快速点评
 */
export function ApiCommentQuick(params = {}) {
  return request('/api/comment/getFastCommentList', params);
}
