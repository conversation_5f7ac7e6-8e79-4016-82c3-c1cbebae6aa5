<template>
  <div class="recycle-dialog">
    <van-dialog
      v-model="show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="recycle-container">
        <div class="recycle-title">{{ $t('小号回收') }}</div>
        <div class="xiaohao-info">
          <div class="item">
            <div class="title">{{ $t('游戏名') }}</div>
            <div class="game-info">
              <div class="game-icon">
                <img :src="info.game_icon" alt="" />
              </div>
              <div class="game-name">
                {{ info.game_name }}
              </div>
            </div>
          </div>
          <div class="item">
            <div class="title">{{ $t('小号') }}</div>
            <div class="xiaohao-name">
              {{ info.nickname }}
            </div>
          </div>
          <div class="item">
            <div class="title">{{ $t('回收所得金币') }}</div>
            <div class="gold">
              {{ payInfo.recycle_gold }}
            </div>
          </div>
          <div class="recharge-num">
            {{ $t('实际充值') }}:<span
              >{{ Number(payInfo.pay_sum).toFixed(1) }}元</span
            >{{ $t('（注：按实际充值金额') }}{{ userInfo.is_svip ? 8 : 5 }}%{{
              $t('回收金币）')
            }}
          </div>
        </div>
        <div class="edit-container">
          <form>
            <div class="input-item">
              <input
                type="text"
                v-model="gameArea"
                :placeholder="$t('请输入主要区服')"
              />
            </div>
            <div class="input-item">
              <input
                type="text"
                v-model="roleName"
                :placeholder="$t('请输入主要角色名称（选填）')"
              />
            </div>
            <div class="input-item">
              <input
                type="text"
                v-model="smsCode"
                :placeholder="$t('验证码')"
              />
              <div class="get-code btn" v-if="!ifCount" @click="captchaClick()">
                {{ $t('获取验证码') }}
              </div>
              <div class="get-code getting" v-else>
                {{ `${$t('已发送')}(${countdown}s)` }}
              </div>
            </div>
            <div class="secret">
              <div class="secret-title">{{ $t('二级密码（有则必填）') }}</div>
              <input
                type="text"
                v-model="secret"
                :placeholder="$t('该密码仅审核人员可见')"
              />
            </div>
            <div class="bottom-operate">
              <div class="cancel btn" @click="show = false">
                {{ $t('取消') }}
              </div>
              <div class="confirm btn" @click="handleConfirm">
                {{ $t('确认') }}
              </div>
            </div>
          </form>
        </div>
        <div class="tips-container">
          {{
            $t(
              '注:小号回收后,所有区服角色都将被冻结,无法登陆。在72小时内可将回收的小号购回,逾期不可购回。购回小号将收取回收价3%的金币手续费(最低10金币)。',
            )
          }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiAuthCode, ApiMailSend } from '@/api/views/users';
import { ApiXiaohaoRecycle } from '@/api/views/xiaohao.js';
import { mapGetters } from 'vuex';
export default {
  name: 'RecycleDialog',
  model: {
    prop: 'dialogShow',
    event: 'change',
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    payInfo: {
      type: Object,
      default: () => {},
    },
    dialogShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: this.dialogShow,
      smsCode: '', // 出售验证码
      countdown: 60,
      ifCount: false,
      gameArea: '',
      roleName: '',
      secret: '',
      captcha: null,
      type: 1, // 1=手机号，2=邮箱
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      isHw: 'system/isHw',
    }),
  },
  watch: {
    show(val) {
      this.$emit('change', val);
      setTimeout(() => {
        this.gameArea = '';
        this.roleName = '';
        this.smsCode = '';
        this.secret = '';
      }, 200);
    },
    dialogShow(val) {
      this.show = val;
    },
  },
  created() {
    try {
      // 生成一个验证码对象
      //callback：定义的回调函数
      this.captcha = new TencentCaptcha('192845611', this.captchaCallBack, {});
    } catch (error) {
      // 加载异常，调用验证码js加载错误处理函数
      this.loadErrorCallback();
    }
  },
  methods: {
    async handleConfirm() {
      if (!this.gameArea) {
        this.$toast(this.$t('请输入主要区服'));
        return false;
      }
      if (!this.smsCode) {
        this.$toast(this.$t('请输入验证码'));
        return false;
      }
      // 提交回收逻辑
      const res = await ApiXiaohaoRecycle({
        xhId: this.info.id,
        recyclePtb: this.payInfo.recycle_ptb,
        smsCode: this.smsCode,
        gameArea: this.gameArea,
        secret: this.secret,
        roleName: this.roleName,
        type: this.type,
      });

      this.show = false;
      if (res.code == 1) {
        this.$toast(this.$t('提交小号回收成功'));
      }
      this.$emit('refresh');
    },
    captchaCallBack(res) {
      if (res.ticket && res.randstr && res.errorCode != 1001) {
        this.getAuthCode(res);
      }
    },
    loadErrorCallback() {
      var appid = '';
      // 生成容灾票据或自行做其它处理
      var ticket =
        'terror_1001_' + appid + Math.floor(new Date().getTime() / 1000);
      this.captchaCallBack({
        ret: 0,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error',
      });
    },
    captchaClick() {
      if (!this.isHw) {
        if (this.userInfo.mobile === '') {
          this.$toast(this.$t('请输入手机号码'));
          return false;
        }
        if (this.initData.captcha_is_open) {
          this.captcha.show();
        } else {
          this.getAuthCode();
        }
      } else {
        if (!this.userInfo.email) {
          this.$toast('请输入邮箱');
          return;
        }
        this.getAuthCode();
      }
    },
    getAuthCode(captcha) {
      // 发送axios请求
      if (!this.isHw) {
        let params = {
          phone: this.userInfo.mobile,
          countryCode: this.userInfo.country_code,
          type: 7,
        };
        if (captcha) {
          params.randStr = captcha.randstr;
          params.ticket = captcha.ticket;
        }
        ApiAuthCode(params).then(
          res => {
            this.$toast(res.msg);
            // 出现倒计时，颜色变暗
            this.ifCount = !this.ifCount;
            this.type = 1;
            let fun = setInterval(() => {
              this.countdown--;
              if (this.countdown === -1) {
                clearInterval(fun);
                this.countdown = 60;
                this.ifCount = !this.ifCount;
              }
            }, 1000);
          },
          err => {},
        );
      } else {
        let params = {
          email: this.userInfo.email,
          type: 7,
        };
        ApiMailSend(params).then(
          res => {
            this.$toast(res.msg);
            // 出现倒计时，颜色变暗
            this.ifCount = !this.ifCount;
            this.type = 2;
            let fun = setInterval(() => {
              this.countdown--;
              if (this.countdown === -1) {
                clearInterval(fun);
                this.countdown = 60;
                this.ifCount = !this.ifCount;
              }
            }, 1000);
          },
          err => {},
        );
      }
    },
  },
  activated() {
    this.show = false;
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-dialog {
  width: 335 * @rem !important;
  top: 50%;
}
.recycle-container {
  box-sizing: border-box;
  width: 335 * @rem;
  padding: 20 * @rem 18 * @rem;
  .recycle-title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: 500;
    text-align: center;
    padding: 5 * @rem 0 15 * @rem;
    border-bottom: 0.5 * @rem solid #f3f3f8;
  }
  .xiaohao-info {
    margin-top: 10 * @rem;
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32 * @rem;
      .title {
        font-size: 13 * @rem;
        color: #909090;
      }
      .game-info {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .game-icon {
          width: 28 * @rem;
          height: 28 * @rem;
          background: #bfbfbf;
          border-radius: 6 * @rem;
          overflow: hidden;
        }
        .game-name {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: 600;
          margin-left: 8 * @rem;
        }
      }
      .xiaohao-name {
        font-size: 14 * @rem;
        color: #000000;
        font-weight: 600;
      }
      .gold {
        font-size: 14 * @rem;
        font-weight: 600;
        color: @themeColor;
      }
    }
    .recharge-num {
      font-size: 11 * @rem;
      color: #000;
      margin-top: 1 * @rem;
      font-weight: 500;
      span {
        font-size: 11 * @rem;
        color: @themeColor;
        font-weight: 500;
      }
    }
  }
  .edit-container {
    .input-item {
      height: 39 * @rem;
      display: flex;
      margin-top: 10 * @rem;
      input {
        box-sizing: border-box;
        flex: 1;
        min-width: 0;
        background-color: #f4f4f4;
        border-radius: 6 * @rem;
        padding: 0 12 * @rem;
        font-size: 13 * @rem;
        color: #333;
      }
      .get-code {
        box-sizing: border-box;
        width: 106 * @rem;
        height: 39 * @rem;
        background: #ffffff;
        border-radius: 6 * @rem;
        font-size: 15 * @rem;
        color: @themeColor;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10 * @rem;
        border: 1 * @rem solid @themeColor;
        &.getting {
          background: #ccc;
          color: #fff;
          border: 0;
        }
      }
    }
    .secret {
      margin-top: 15 * @rem;
      .secret-title {
        font-size: 14 * @rem;
        font-weight: 600;
        color: #000000;
      }
      input {
        box-sizing: border-box;
        width: 100%;
        height: 38 * @rem;
        display: flex;
        align-items: center;
        background-color: #f4f4f4;
        border-radius: 6 * @rem;
        padding: 0 12 * @rem;
        font-size: 13 * @rem;
        color: #333;
        margin-top: 10 * @rem;
      }
    }
    .bottom-operate {
      height: 45 * @rem;
      margin-top: 20 * @rem;
      display: flex;
      justify-content: space-between;
      .cancel {
        box-sizing: border-box;
        width: 140 * @rem;
        height: 42 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 21 * @rem;
        font-size: 14 * @rem;
        color: #909090;
        border: 1 * @rem solid #b1b1b1;
      }
      .confirm {
        box-sizing: border-box;
        width: 145 * @rem;
        height: 42 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @themeBg;
        border-radius: 21 * @rem;
        font-size: 14 * @rem;
        color: #ffffff;
        background: @themeBg;
      }
    }
  }
  .tips-container {
    margin-top: 10 * @rem;
    font-size: 12 * @rem;
    color: #b1b1b1;
    line-height: 17 * @rem;
  }
}
</style>
