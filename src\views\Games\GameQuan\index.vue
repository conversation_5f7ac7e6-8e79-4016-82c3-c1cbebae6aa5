<template>
  <div class="game-quan-page page">
    <nav-bar-2 :border="true" :title="$t('代金券')">
      <template #right>
        <div class="instruction" @click="toIntroduction">
          {{ $t('使用说明') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="quan-container">
      <content-empty v-if="empty"></content-empty>
      <div class="quan-list" v-else>
        <template v-for="(item, index) in couponList">
          <div class="quan-item" :key="index">
            <div v-if="item.type && item.type != 0" class="subscript">
              {{ subscript(item) }}
            </div>
            <img class="bg" src="@/assets/images/quan-bg.png" alt="" />
            <div class="quan-info">
              <div class="left">
                <div class="quan-num">
                  <span>{{ item.money }}</span
                  >元
                </div>
                <div class="total-num">
                  {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
                </div>
              </div>
              <div class="right">
                <div class="title">{{ item.remark }}</div>
                <div class="desc">
                  <div class="date">
                    {{ $t('有效期') }}：{{ item.period }}{{ $t('天') }}
                  </div>
                  <div
                    class="get btn"
                    :class="{
                      had: item.take_status != 0 || item.remain_percent == 0,
                    }"
                    @click="getCoupon(item)"
                  >
                    {{
                      item.take_status != 0
                        ? $t('已领取')
                        : item.remain_percent != 0
                          ? $t('领取')
                          : $t('已抢光')
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiCouponCoupon, ApiCouponTake } from '@/api/views/coupon.js';
import { mapActions } from 'vuex';
import h5Page from '@/utils/h5Page';

export default {
  name: 'GameQuan',
  data() {
    return {
      couponList: [],
      empty: false,
    };
  },
  async created() {
    const res = await ApiCouponCoupon({ gameId: this.$route.params.game_id });
    if (!res.data.list.length) {
      this.empty = true;
    } else {
      this.couponList = res.data.list;
    }
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    getCoupon(item) {
      ApiCouponTake({
        couponId: item.id,
      }).then(
        res => {
          this.$toast(res.msg);
          this.$set(this.couponList[this.findIndex(item)], 'take_status', true);
          this.SET_USER_INFO();

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${item.id}`,
            voucher_name: `${item.title}`,
            voucher_amount: `${item.money}`,
          });
        },
        err => {
          if (err.code == 0) {
            this.$set(
              this.couponList[this.findIndex(item)],
              'remain_percent',
              0,
            );
          }
        },
      );
    },
    findIndex(coupon) {
      return this.couponList.findIndex(item => {
        return item.id == coupon.id;
      });
    },
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: h5Page.daijinquanshuoming,
          title: this.$t('代金券使用说明'),
        },
      });
    },
    subscript(item) {
      switch (parseInt(item.type)) {
        case 1:
          return 'svip';
        case 2:
          return this.$t('无门槛');
        default:
          return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-page {
  padding: 13 * @rem 0;
  .instruction {
    font-size: 14 * @rem;
    color: #000;
  }
  .quan-container {
    .quan-list {
      .quan-item {
        position: relative;
        width: 360 * @rem;
        height: 100 * @rem;
        margin: 0 auto 6 * @rem;
        .subscript {
          position: absolute;
          top: 0 * @rem;
          left: 9 * @rem;
          padding: 3 * @rem 5 * @rem;
          background: rgba(255, 117, 84, 1);
          color: #fff;
          border-radius: 10 * @rem 0 10 * @rem 0;
          z-index: 0;
        }
        .bg {
          position: absolute;
          left: 0;
          top: 0;
          z-index: -1;
          display: block;
          width: 100%;
          height: 100%;
        }
        .quan-info {
          box-sizing: border-box;
          height: 100%;
          position: relative;
          padding: 8 * @rem 7 * @rem;
          display: flex;
          align-items: center;
          .left {
            width: 92 * @rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .quan-num {
              font-size: 14 * @rem;
              color: #ff3c3c;
              white-space: nowrap;
              span {
                font-size: 25 * @rem;
                font-weight: bold;
                margin-right: 2 * @rem;
              }
            }
            .total-num {
              font-size: 11 * @rem;
              color: #666666;
              margin-top: 10 * @rem;
            }
          }
          .right {
            flex: 1;
            min-width: 0;
            padding: 0 12 * @rem 0 16 * @rem;
            margin-top: 10 * @rem;
            .title {
              font-size: 15 * @rem;
              color: #010101;
              font-weight: bold;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .desc {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 5 * @rem;
              .date {
                font-size: 11 * @rem;
                color: #999999;
              }
              .get {
                width: 65 * @rem;
                height: 25 * @rem;
                border-radius: 13 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                font-size: 13 * @rem;
                background: linear-gradient(60deg, #ff4c39, #ff9c34);
                &.had {
                  background: #d6d6d6;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
