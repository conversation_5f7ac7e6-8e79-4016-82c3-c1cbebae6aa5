<template>
  <div class="select-xh">
    <nav-bar-2 :border="true" :title="$t('选择小号')"></nav-bar-2>
    <Empty :tips="$t('当前游戏无小号')" v-if="list.length === 0" />
    <template v-if="list.length !== 0">
      <div
        v-for="(item, index) in list"
        :key="index"
        @click="confirmXH(item)"
        class="xh-list"
      >
        <div class="top">
          <div class="left">{{ item.nickname }}</div>
          <div class="right">{{ item.status_info.str }}</div>
        </div>
        <div class="bottom">
          <img :src="item.game_icon" alt="" class="left" />
          <div class="center">{{ item.game_name }}</div>
          <div class="right"></div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { ApiXiaohaoAllPayerList } from '@/api/views/xiaohao';
import { mapGetters, mapMutations } from 'vuex';
import Empty from '@/components/content-empty';

export default {
  name: 'RebateSelectXH',
  data() {
    return {
      list: [],
      loading: false,
    };
  },
  computed: {
    ...mapGetters({
      gameInfo: 'rebate/gameInfo',
    }),
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const toast1 = this.$toast.loading({
        message: this.$t('加载中...'),
        forbidClick: true,
      });
      const res = await ApiXiaohaoAllPayerList({
        keyword: this.gameInfo.title,
        type: 1,
      });
      toast1.clear();
      this.loading = true;
      this.list = res.data.list;
    },
    confirmXH(item) {
      this.setSmallAccountName(item.nickname);
      this.setSmallAccountId(item.id);
      this.$router.replace({
        name: 'RebateFirst',
        params: { ...this.$route.params },
      });
    },
    ...mapMutations({
      setSmallAccountName: 'rebate/setSmallAccountName',
      setSmallAccountId: 'rebate/setSmallAccountId',
    }),
  },
  components: {
    Empty,
  },
};
</script>
<style lang="less" scoped>
.xh-list {
  margin: 20 * @rem 14 * @rem 0;
  padding: 14 * @rem;
  box-shadow: 0 0 16 * @rem 0 rgba(0, 0, 0, 0.12);
  border-radius: 20 * @rem;
  .top {
    display: flex;
    justify-content: space-between;
    font-size: 15 * @rem;
    .right {
      color: @themeColor;
    }
  }
  .bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15 * @rem;
    padding: 11 * @rem 12 * @rem;
    background: #f4f4f4;
    border-radius: 4 * @rem;
    font-size: 15 * @rem;
    .left {
      width: 28 * @rem;
      height: 28 * @rem;
    }
    .center {
      flex: 1;
      margin-left: 8 * @rem;
      text-align: left;
    }
    .right {
      width: 7 * @rem;
      height: 12 * @rem;
      background-image: url(~@/assets/images/right-icon.png);
      background-size: 100%;
    }
  }
}
</style>
