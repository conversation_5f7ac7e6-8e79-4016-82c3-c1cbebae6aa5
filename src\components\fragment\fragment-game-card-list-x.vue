<template>
  <div class="fragment-game-list">
    <div class="fragment-title">
      <div class="title-text">{{ info.header_title }}</div>
      <div class="right-icon" v-if="info.action_code" @click="clickMore"></div>
    </div>
    <div class="game-list">
      <div
        class="game-container"
        v-for="(game, index) in info.game_list"
        @click="goToGame(game, index)"
        :key="index"
        v-sensors-exposure="gameExposure(game, index)"
      >
        <div class="game-bg">
          <img :src="game.video_thumb" alt="" />
        </div>

        <div class="game-item">
          <game-item-4
            :gameInfo="game"
            :iconSize="56"
            :showHot="true"
          ></game-item-4>
          <yy-download-btn
            v-if="info.is_show_btn"
            :gameInfo="game"
            @click="goToGame(game, index)"
          ></yy-download-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'FragmentGameListX',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item,index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      }
    },
    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title || item.main_title,
        game_index: `${index}`,
      })
      this.$sensorsModuleSet(this.info.header_title)
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
    clickMore() {
      handleActionCode(this.info);
    },
  },
};
</script>

<style lang="less" scoped>
.fragment-game-list {
  padding: 12 * @rem 0;
  margin: 0 12 * @rem;
  background: #fff;
  border-radius: 12 * @rem;
  .fragment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7 * @rem 0;
    margin: 0 12 * @rem;
    .title-text {
      font-size: 16 * @rem;
      font-weight: bold;
      color: #191b1f;
      line-height: 16 * @rem;
    }
    .right-icon {
      width: 6 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/right-icon.png) right center no-repeat;
      background-size: 6 * @rem 10 * @rem;
      padding-left: 20 * @rem;
    }
  }
  .game-list {
    display: flex;
    overflow-x: auto;
    padding: 8 * @rem 12 * @rem 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .game-container {
      flex-shrink: 0;
      width: 312 * @rem;
      height: 256 * @rem;
      border-radius: 12 * @rem;
      margin-right: 16 * @rem;

      &:last-of-type {
        margin-right: 0;
      }
      .game-bg {
        width: 312 * @rem;
        height: 176 * @rem;
        border-radius: 12 * @rem 12 * @rem 0 0;
        overflow: hidden;
      }
      .game-item {
        background: #f7f8fa;
        border-radius: 0 0 12 * @rem 12 * @rem;
        padding: 0 12 * @rem;
        display: flex;
        align-items: center;
        /deep/ .game-item-components {
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>
