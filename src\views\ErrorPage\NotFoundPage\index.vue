<template>
  <div class="error-page">
    <nav-bar-2 title="" :azShow="true"></nav-bar-2>
    <content-empty
      :emptyImg="emptyImg"
      tips="抱歉，你访问的页面不存在"
      :showRetry="true"
      @retry="retry"
      class="empty-content"
    ></content-empty>
  </div>
</template>

<script>
import emptyImg from '@/assets/images/error/page-error.png';
export default {
  name: 'NotFoundPage',
  data() {
    return { emptyImg };
  },

  methods: {
    retry() {
      if (this.$route.params.name) {
        this.$router.replace(this.$route.params);
      } else {
        this.$toast.loading();
        setTimeout(() => {
          this.$toast.clear();
          this.$toast("你访问的页面不存在")
        }, 1000);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.error-page {
  display: flex;
  // align-items: center;
  justify-content: center;
  height: 100vh;
}
.empty-content {
  display: block !important;
  margin-top: 25vh;
  /deep/ .van-empty {
    .van-empty__description {
      margin-top: 8 * @rem;
    }

    .retry {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 108 * @rem;
      height: 40 * @rem;
      line-height: 1;
      text-align: center;
      color: #fff;
      font-size: 15 * @rem;
      background: @themeBg;
      border-radius: 29 * @rem;
      text-decoration: none;
    }
  }
}
</style>
