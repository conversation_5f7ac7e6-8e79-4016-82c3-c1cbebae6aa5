<template>
  <div class="game-quan-item-components">
    <div class="game-quan-item">
      <div class="game-info" @click="toExternalGameCollect">
        <div class="game-item-components">
          <div class="game-list">
            <div class="game-icon">
              <template v-if="couponItem.img_list.length == 3">
                <img
                  v-for="(item, index) in couponItem.img_list"
                  :key="index"
                  :src="item"
                  alt=""
                  :class="`image image${index}`"
                />
              </template>
              <template v-else>
                <img class="image" :src="couponItem.img_list[0]" />
              </template>
            </div>
          </div>

          <div class="game-info-item">
            <div class="game-name">
              <div class="game-title">{{ couponItem.title }}</div>
            </div>
            <div class="game-m-title"
              >UP主：{{ couponItem.up_info.nickname }}</div
            >
            <div class="game-clicks" v-if="couponItem.clicks">
              <div></div>
              <span> {{ couponItem.clicks }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="game-quan-get">
        <div
          class="collect-btn collect-btn-white btn"
          :class="{ had: couponItem.is_collect }"
          @click="setCollectStatus"
        ></div>
        <div>{{ couponItem.collect }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { ApiCollectCollectionGame } from '@/api/views/game.js';
export default {
  name: 'GameQuanItem',
  props: {
    couponItem: {
      type: Object,
      default: () => {},
    },
    isShowTitlePic: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    // 设置收藏
    setCollectStatus() {
      let status;
      if (!this.couponItem.is_collect) {
        status = 1;
      } else {
        status = 0;
      }
      ApiCollectCollectionGame({
        id: this.couponItem.id,
        type: status,
      }).then(res => {
        this.couponItem.is_collect = !this.couponItem.is_collect;
        this.couponItem.collect = this.couponItem.is_collect
          ? this.couponItem.collect + 1
          : this.couponItem.collect - 1;
        if (this.couponItem.collect < 0) {
          this.couponItem.collect = 0;
        }
      });
    },
    // 打开外部游戏合集页
    toExternalGameCollect() {
      this.toPage('ExternalGameCollect', {
        id: this.couponItem.id,
        info: this.couponItem,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.game-quan-item-components {
  width: 100%;
  .game-quan-item {
    height: 64 * @rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12 * @rem;
    .game-info {
      flex: 1;
      min-width: 0;

      .game-item-components {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        .game-list {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 84 * @rem;
          .game-icon {
            position: relative;
            flex: 0 0 64 * @rem;
            width: 64 * @rem;
            height: 64 * @rem;
            border: 1 * @rem solid #fff;
            background: linear-gradient(
              8deg,
              rgba(255, 255, 255, 1) 12.598425196850394%,
              #92f1dc 100%
            );
            border-radius: 10 * @rem;
            position: relative;
            .image {
              position: absolute;
              height: auto;
              top: 0;
              border-radius: 10 * @rem;
              overflow: hidden;
            }
            .image0 {
              left: -15 * @rem;
              z-index: 1;
              transform: scale(0.8);
            }

            .image1 {
              border-radius: 8 * @rem;
              z-index: 3;
            }

            .image2 {
              left: 15 * @rem;
              z-index: 2;
              transform: scale(0.8);
            }
          }
        }

        .game-info-item {
          margin-left: 6 * @rem;
          margin-right: 17 * @rem;
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .game-name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .game-title {
              width: 106 * @rem;
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 18 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
            .game-subtitle {
              box-sizing: border-box;
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
              border: 1 * @rem solid #e5e1ea;
              color: #888888;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          .game-m-title {
            width: 186 * @rem;
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-size: 11 * @rem;
            margin: 7 * @rem 0;
            color: #000;
            text-align: left;
            font-style: normal;
            text-transform: none;
            overflow: hidden;
            white-space: nowrap;
            display: flex;
            align-items: center;
          }
          .game-clicks {
            display: flex;
            align-items: center;
            div {
              background: url(~@/assets/images/games/xhm-logo.png) no-repeat 0 0;
              background-size: 14 * @rem 16 * @rem;
              width: 14 * @rem;
              height: 16 * @rem;
            }
            span {
              font-size: 11 * @rem;
              color: #fe4a55;
              padding: 0 5 * @rem;
              height: 16 * @rem;
              line-height: 16 * @rem;
              border-radius: 0 3 * @rem 3 * @rem 0;
              background: #fff1f1;
            }
          }
        }
      }
    }
    .game-quan-get {
      width: 58 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .total {
        font-size: 11 * @rem;
        color: #929292;
        margin-top: 2 * @rem;
      }
      .get {
        font-weight: 500;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 58 * @rem;
        height: 28 * @rem;
        background: #5abf73;
        border-radius: 29 * @rem;
        margin-top: 4 * @rem;
      }
      .collect-btn {
        padding: 3 * @rem 0;
        &.collect-btn-white {
          background: url(~@/assets/images/games/ic_up_collection_collect.png)
            no-repeat -3 * @rem -4 * @rem;
          background-size: 28 * @rem 28 * @rem;
          width: 22 * @rem;
          height: 20 * @rem;
        }
        &.had {
          background: url(~@/assets/images/games/ic_up_collection_collected.png)
            no-repeat -2 * @rem -4 * @rem;
          background-size: 28 * @rem 28 * @rem;
          width: 23 * @rem;
          height: 20 * @rem;
        }
      }
    }
  }
}
</style>
